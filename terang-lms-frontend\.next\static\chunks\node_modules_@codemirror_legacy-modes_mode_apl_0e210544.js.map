{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/apl.js"], "sourcesContent": ["var builtInFuncs = {\n  \"+\": [\"conjugate\", \"add\"],\n  \"−\": [\"negate\", \"subtract\"],\n  \"×\": [\"signOf\", \"multiply\"],\n  \"÷\": [\"reciprocal\", \"divide\"],\n  \"⌈\": [\"ceiling\", \"greaterOf\"],\n  \"⌊\": [\"floor\", \"lesserOf\"],\n  \"∣\": [\"absolute\", \"residue\"],\n  \"⍳\": [\"indexGenerate\", \"indexOf\"],\n  \"?\": [\"roll\", \"deal\"],\n  \"⋆\": [\"exponentiate\", \"toThePowerOf\"],\n  \"⍟\": [\"naturalLog\", \"logToTheBase\"],\n  \"○\": [\"piTimes\", \"circularFuncs\"],\n  \"!\": [\"factorial\", \"binomial\"],\n  \"⌹\": [\"matrixInverse\", \"matrixDivide\"],\n  \"<\": [null, \"lessThan\"],\n  \"≤\": [null, \"lessThanOrEqual\"],\n  \"=\": [null, \"equals\"],\n  \">\": [null, \"greaterThan\"],\n  \"≥\": [null, \"greaterThanOrEqual\"],\n  \"≠\": [null, \"notEqual\"],\n  \"≡\": [\"depth\", \"match\"],\n  \"≢\": [null, \"notMatch\"],\n  \"∈\": [\"enlist\", \"membership\"],\n  \"⍷\": [null, \"find\"],\n  \"∪\": [\"unique\", \"union\"],\n  \"∩\": [null, \"intersection\"],\n  \"∼\": [\"not\", \"without\"],\n  \"∨\": [null, \"or\"],\n  \"∧\": [null, \"and\"],\n  \"⍱\": [null, \"nor\"],\n  \"⍲\": [null, \"nand\"],\n  \"⍴\": [\"shapeOf\", \"reshape\"],\n  \",\": [\"ravel\", \"catenate\"],\n  \"⍪\": [null, \"firstAxisCatenate\"],\n  \"⌽\": [\"reverse\", \"rotate\"],\n  \"⊖\": [\"axis1Reverse\", \"axis1Rotate\"],\n  \"⍉\": [\"transpose\", null],\n  \"↑\": [\"first\", \"take\"],\n  \"↓\": [null, \"drop\"],\n  \"⊂\": [\"enclose\", \"partitionWithAxis\"],\n  \"⊃\": [\"diclose\", \"pick\"],\n  \"⌷\": [null, \"index\"],\n  \"⍋\": [\"gradeUp\", null],\n  \"⍒\": [\"gradeDown\", null],\n  \"⊤\": [\"encode\", null],\n  \"⊥\": [\"decode\", null],\n  \"⍕\": [\"format\", \"formatByExample\"],\n  \"⍎\": [\"execute\", null],\n  \"⊣\": [\"stop\", \"left\"],\n  \"⊢\": [\"pass\", \"right\"]\n};\n\nvar isOperator = /[\\.\\/⌿⍀¨⍣]/;\nvar isNiladic = /⍬/;\nvar isFunction = /[\\+−×÷⌈⌊∣⍳\\?⋆⍟○!⌹<≤=>≥≠≡≢∈⍷∪∩∼∨∧⍱⍲⍴,⍪⌽⊖⍉↑↓⊂⊃⌷⍋⍒⊤⊥⍕⍎⊣⊢]/;\nvar isArrow = /←/;\nvar isComment = /[⍝#].*$/;\n\nvar stringEater = function(type) {\n  var prev;\n  prev = false;\n  return function(c) {\n    prev = c;\n    if (c === type) {\n      return prev === \"\\\\\";\n    }\n    return true;\n  };\n};\n\nexport const apl = {\n  name: \"apl\",\n  startState: function() {\n    return {\n      prev: false,\n      func: false,\n      op: false,\n      string: false,\n      escape: false\n    };\n  },\n  token: function(stream, state) {\n    var ch;\n    if (stream.eatSpace()) {\n      return null;\n    }\n    ch = stream.next();\n    if (ch === '\"' || ch === \"'\") {\n      stream.eatWhile(stringEater(ch));\n      stream.next();\n      state.prev = true;\n      return \"string\";\n    }\n    if (/[\\[{\\(]/.test(ch)) {\n      state.prev = false;\n      return null;\n    }\n    if (/[\\]}\\)]/.test(ch)) {\n      state.prev = true;\n      return null;\n    }\n    if (isNiladic.test(ch)) {\n      state.prev = false;\n      return \"atom\";\n    }\n    if (/[¯\\d]/.test(ch)) {\n      if (state.func) {\n        state.func = false;\n        state.prev = false;\n      } else {\n        state.prev = true;\n      }\n      stream.eatWhile(/[\\w\\.]/);\n      return \"number\";\n    }\n    if (isOperator.test(ch)) {\n      return \"operator\"\n    }\n    if (isArrow.test(ch)) {\n      return \"operator\";\n    }\n    if (isFunction.test(ch)) {\n      state.func = true;\n      state.prev = false;\n      return builtInFuncs[ch] ? \"variableName.function.standard\" : \"variableName.function\"\n    }\n    if (isComment.test(ch)) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    if (ch === \"∘\" && stream.peek() === \".\") {\n      stream.next();\n      return \"variableName.function\";\n    }\n    stream.eatWhile(/[\\w\\$_]/);\n    state.prev = true;\n    return \"keyword\";\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,eAAe;IACjB,KAAK;QAAC;QAAa;KAAM;IACzB,KAAK;QAAC;QAAU;KAAW;IAC3B,KAAK;QAAC;QAAU;KAAW;IAC3B,KAAK;QAAC;QAAc;KAAS;IAC7B,KAAK;QAAC;QAAW;KAAY;IAC7B,KAAK;QAAC;QAAS;KAAW;IAC1B,KAAK;QAAC;QAAY;KAAU;IAC5B,KAAK;QAAC;QAAiB;KAAU;IACjC,KAAK;QAAC;QAAQ;KAAO;IACrB,KAAK;QAAC;QAAgB;KAAe;IACrC,KAAK;QAAC;QAAc;KAAe;IACnC,KAAK;QAAC;QAAW;KAAgB;IACjC,KAAK;QAAC;QAAa;KAAW;IAC9B,KAAK;QAAC;QAAiB;KAAe;IACtC,KAAK;QAAC;QAAM;KAAW;IACvB,KAAK;QAAC;QAAM;KAAkB;IAC9B,KAAK;QAAC;QAAM;KAAS;IACrB,KAAK;QAAC;QAAM;KAAc;IAC1B,KAAK;QAAC;QAAM;KAAqB;IACjC,KAAK;QAAC;QAAM;KAAW;IACvB,KAAK;QAAC;QAAS;KAAQ;IACvB,KAAK;QAAC;QAAM;KAAW;IACvB,KAAK;QAAC;QAAU;KAAa;IAC7B,KAAK;QAAC;QAAM;KAAO;IACnB,KAAK;QAAC;QAAU;KAAQ;IACxB,KAAK;QAAC;QAAM;KAAe;IAC3B,KAAK;QAAC;QAAO;KAAU;IACvB,KAAK;QAAC;QAAM;KAAK;IACjB,KAAK;QAAC;QAAM;KAAM;IAClB,KAAK;QAAC;QAAM;KAAM;IAClB,KAAK;QAAC;QAAM;KAAO;IACnB,KAAK;QAAC;QAAW;KAAU;IAC3B,KAAK;QAAC;QAAS;KAAW;IAC1B,KAAK;QAAC;QAAM;KAAoB;IAChC,KAAK;QAAC;QAAW;KAAS;IAC1B,KAAK;QAAC;QAAgB;KAAc;IACpC,KAAK;QAAC;QAAa;KAAK;IACxB,KAAK;QAAC;QAAS;KAAO;IACtB,KAAK;QAAC;QAAM;KAAO;IACnB,KAAK;QAAC;QAAW;KAAoB;IACrC,KAAK;QAAC;QAAW;KAAO;IACxB,KAAK;QAAC;QAAM;KAAQ;IACpB,KAAK;QAAC;QAAW;KAAK;IACtB,KAAK;QAAC;QAAa;KAAK;IACxB,KAAK;QAAC;QAAU;KAAK;IACrB,KAAK;QAAC;QAAU;KAAK;IACrB,KAAK;QAAC;QAAU;KAAkB;IAClC,KAAK;QAAC;QAAW;KAAK;IACtB,KAAK;QAAC;QAAQ;KAAO;IACrB,KAAK;QAAC;QAAQ;KAAQ;AACxB;AAEA,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,UAAU;AACd,IAAI,YAAY;AAEhB,IAAI,cAAc,SAAS,IAAI;IAC7B,IAAI;IACJ,OAAO;IACP,OAAO,SAAS,CAAC;QACf,OAAO;QACP,IAAI,MAAM,MAAM;YACd,OAAO,SAAS;QAClB;QACA,OAAO;IACT;AACF;AAEO,MAAM,MAAM;IACjB,MAAM;IACN,YAAY;QACV,OAAO;YACL,MAAM;YACN,MAAM;YACN,IAAI;YACJ,QAAQ;YACR,QAAQ;QACV;IACF;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI;QACJ,IAAI,OAAO,QAAQ,IAAI;YACrB,OAAO;QACT;QACA,KAAK,OAAO,IAAI;QAChB,IAAI,OAAO,OAAO,OAAO,KAAK;YAC5B,OAAO,QAAQ,CAAC,YAAY;YAC5B,OAAO,IAAI;YACX,MAAM,IAAI,GAAG;YACb,OAAO;QACT;QACA,IAAI,UAAU,IAAI,CAAC,KAAK;YACtB,MAAM,IAAI,GAAG;YACb,OAAO;QACT;QACA,IAAI,UAAU,IAAI,CAAC,KAAK;YACtB,MAAM,IAAI,GAAG;YACb,OAAO;QACT;QACA,IAAI,UAAU,IAAI,CAAC,KAAK;YACtB,MAAM,IAAI,GAAG;YACb,OAAO;QACT;QACA,IAAI,QAAQ,IAAI,CAAC,KAAK;YACpB,IAAI,MAAM,IAAI,EAAE;gBACd,MAAM,IAAI,GAAG;gBACb,MAAM,IAAI,GAAG;YACf,OAAO;gBACL,MAAM,IAAI,GAAG;YACf;YACA,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,IAAI,WAAW,IAAI,CAAC,KAAK;YACvB,OAAO;QACT;QACA,IAAI,QAAQ,IAAI,CAAC,KAAK;YACpB,OAAO;QACT;QACA,IAAI,WAAW,IAAI,CAAC,KAAK;YACvB,MAAM,IAAI,GAAG;YACb,MAAM,IAAI,GAAG;YACb,OAAO,YAAY,CAAC,GAAG,GAAG,mCAAmC;QAC/D;QACA,IAAI,UAAU,IAAI,CAAC,KAAK;YACtB,OAAO,SAAS;YAChB,OAAO;QACT;QACA,IAAI,OAAO,OAAO,OAAO,IAAI,OAAO,KAAK;YACvC,OAAO,IAAI;YACX,OAAO;QACT;QACA,OAAO,QAAQ,CAAC;QAChB,MAAM,IAAI,GAAG;QACb,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}