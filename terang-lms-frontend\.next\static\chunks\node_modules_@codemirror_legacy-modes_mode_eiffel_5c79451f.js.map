{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/eiffel.js"], "sourcesContent": ["function wordObj(words) {\n  var o = {};\n  for (var i = 0, e = words.length; i < e; ++i) o[words[i]] = true;\n  return o;\n}\nvar keywords = wordObj([\n  'note',\n  'across',\n  'when',\n  'variant',\n  'until',\n  'unique',\n  'undefine',\n  'then',\n  'strip',\n  'select',\n  'retry',\n  'rescue',\n  'require',\n  'rename',\n  'reference',\n  'redefine',\n  'prefix',\n  'once',\n  'old',\n  'obsolete',\n  'loop',\n  'local',\n  'like',\n  'is',\n  'inspect',\n  'infix',\n  'include',\n  'if',\n  'frozen',\n  'from',\n  'external',\n  'export',\n  'ensure',\n  'end',\n  'elseif',\n  'else',\n  'do',\n  'creation',\n  'create',\n  'check',\n  'alias',\n  'agent',\n  'separate',\n  'invariant',\n  'inherit',\n  'indexing',\n  'feature',\n  'expanded',\n  'deferred',\n  'class',\n  'Void',\n  'True',\n  'Result',\n  'Precursor',\n  'False',\n  'Current',\n  'create',\n  'attached',\n  'detachable',\n  'as',\n  'and',\n  'implies',\n  'not',\n  'or'\n]);\nvar operators = wordObj([\":=\", \"and then\",\"and\", \"or\",\"<<\",\">>\"]);\n\nfunction chain(newtok, stream, state) {\n  state.tokenize.push(newtok);\n  return newtok(stream, state);\n}\n\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) return null;\n  var ch = stream.next();\n  if (ch == '\"'||ch == \"'\") {\n    return chain(readQuoted(ch, \"string\"), stream, state);\n  } else if (ch == \"-\"&&stream.eat(\"-\")) {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == \":\"&&stream.eat(\"=\")) {\n    return \"operator\";\n  } else if (/[0-9]/.test(ch)) {\n    stream.eatWhile(/[xXbBCc0-9\\.]/);\n    stream.eat(/[\\?\\!]/);\n    return \"variable\";\n  } else if (/[a-zA-Z_0-9]/.test(ch)) {\n    stream.eatWhile(/[a-zA-Z_0-9]/);\n    stream.eat(/[\\?\\!]/);\n    return \"variable\";\n  } else if (/[=+\\-\\/*^%<>~]/.test(ch)) {\n    stream.eatWhile(/[=+\\-\\/*^%<>~]/);\n    return \"operator\";\n  } else {\n    return null;\n  }\n}\n\nfunction readQuoted(quote, style,  unescaped) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && (unescaped || !escaped)) {\n        state.tokenize.pop();\n        break;\n      }\n      escaped = !escaped && ch == \"%\";\n    }\n    return style;\n  };\n}\n\nexport const eiffel = {\n  name: \"eiffel\",\n  startState: function() {\n    return {tokenize: [tokenBase]};\n  },\n\n  token: function(stream, state) {\n    var style = state.tokenize[state.tokenize.length-1](stream, state);\n    if (style == \"variable\") {\n      var word = stream.current();\n      style = keywords.propertyIsEnumerable(stream.current()) ? \"keyword\"\n        : operators.propertyIsEnumerable(stream.current()) ? \"operator\"\n        : /^[A-Z][A-Z_0-9]*$/g.test(word) ? \"tag\"\n        : /^0[bB][0-1]+$/g.test(word) ? \"number\"\n        : /^0[cC][0-7]+$/g.test(word) ? \"number\"\n        : /^0[xX][a-fA-F0-9]+$/g.test(word) ? \"number\"\n        : /^([0-9]+\\.[0-9]*)|([0-9]*\\.[0-9]+)$/g.test(word) ? \"number\"\n        : /^[0-9]+$/g.test(word) ? \"number\"\n        : \"variable\";\n    }\n    return style;\n  },\n  languageData: {\n    commentTokens: {line: \"--\"}\n  }\n};\n\n"], "names": [], "mappings": ";;;AAAA,SAAS,QAAQ,KAAK;IACpB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IAC5D,OAAO;AACT;AACA,IAAI,WAAW,QAAQ;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,IAAI,YAAY,QAAQ;IAAC;IAAM;IAAW;IAAO;IAAK;IAAK;CAAK;AAEhE,SAAS,MAAM,MAAM,EAAE,MAAM,EAAE,KAAK;IAClC,MAAM,QAAQ,CAAC,IAAI,CAAC;IACpB,OAAO,OAAO,QAAQ;AACxB;AAEA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,OAAO,QAAQ,IAAI,OAAO;IAC9B,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,OAAK,MAAM,KAAK;QACxB,OAAO,MAAM,WAAW,IAAI,WAAW,QAAQ;IACjD,OAAO,IAAI,MAAM,OAAK,OAAO,GAAG,CAAC,MAAM;QACrC,OAAO,SAAS;QAChB,OAAO;IACT,OAAO,IAAI,MAAM,OAAK,OAAO,GAAG,CAAC,MAAM;QACrC,OAAO;IACT,OAAO,IAAI,QAAQ,IAAI,CAAC,KAAK;QAC3B,OAAO,QAAQ,CAAC;QAChB,OAAO,GAAG,CAAC;QACX,OAAO;IACT,OAAO,IAAI,eAAe,IAAI,CAAC,KAAK;QAClC,OAAO,QAAQ,CAAC;QAChB,OAAO,GAAG,CAAC;QACX,OAAO;IACT,OAAO,IAAI,iBAAiB,IAAI,CAAC,KAAK;QACpC,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEA,SAAS,WAAW,KAAK,EAAE,KAAK,EAAG,SAAS;IAC1C,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO;QACrB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;YACnC,IAAI,MAAM,SAAS,CAAC,aAAa,CAAC,OAAO,GAAG;gBAC1C,MAAM,QAAQ,CAAC,GAAG;gBAClB;YACF;YACA,UAAU,CAAC,WAAW,MAAM;QAC9B;QACA,OAAO;IACT;AACF;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,YAAY;QACV,OAAO;YAAC,UAAU;gBAAC;aAAU;QAAA;IAC/B;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,QAAQ,MAAM,QAAQ,CAAC,MAAM,QAAQ,CAAC,MAAM,GAAC,EAAE,CAAC,QAAQ;QAC5D,IAAI,SAAS,YAAY;YACvB,IAAI,OAAO,OAAO,OAAO;YACzB,QAAQ,SAAS,oBAAoB,CAAC,OAAO,OAAO,MAAM,YACtD,UAAU,oBAAoB,CAAC,OAAO,OAAO,MAAM,aACnD,qBAAqB,IAAI,CAAC,QAAQ,QAClC,iBAAiB,IAAI,CAAC,QAAQ,WAC9B,iBAAiB,IAAI,CAAC,QAAQ,WAC9B,uBAAuB,IAAI,CAAC,QAAQ,WACpC,uCAAuC,IAAI,CAAC,QAAQ,WACpD,YAAY,IAAI,CAAC,QAAQ,WACzB;QACN;QACA,OAAO;IACT;IACA,cAAc;QACZ,eAAe;YAAC,MAAM;QAAI;IAC5B;AACF", "ignoreList": [0], "debugId": null}}]}