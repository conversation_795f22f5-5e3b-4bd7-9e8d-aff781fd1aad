{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/properties.js"], "sourcesContent": ["export const properties = {\n  name: \"properties\",\n\n  token: function(stream, state) {\n    var sol = stream.sol() || state.afterSection;\n    var eol = stream.eol();\n\n    state.afterSection = false;\n\n    if (sol) {\n      if (state.nextMultiline) {\n        state.inMultiline = true;\n        state.nextMultiline = false;\n      } else {\n        state.position = \"def\";\n      }\n    }\n\n    if (eol && ! state.nextMultiline) {\n      state.inMultiline = false;\n      state.position = \"def\";\n    }\n\n    if (sol) {\n      while(stream.eatSpace()) {}\n    }\n\n    var ch = stream.next();\n\n    if (sol && (ch === \"#\" || ch === \"!\" || ch === \";\")) {\n      state.position = \"comment\";\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (sol && ch === \"[\") {\n      state.afterSection = true;\n      stream.skipTo(\"]\"); stream.eat(\"]\");\n      return \"header\";\n    } else if (ch === \"=\" || ch === \":\") {\n      state.position = \"quote\";\n      return null;\n    } else if (ch === \"\\\\\" && state.position === \"quote\") {\n      if (stream.eol()) {  // end of line?\n        // Multiline value\n        state.nextMultiline = true;\n      }\n    }\n\n    return state.position;\n  },\n\n  startState: function() {\n    return {\n      position : \"def\",       // Current position, \"def\", \"quote\" or \"comment\"\n      nextMultiline : false,  // Is the next line multiline value\n      inMultiline : false,    // Is the current line a multiline value\n      afterSection : false    // Did we just open a section\n    };\n  }\n\n};\n"], "names": [], "mappings": ";;;AAAO,MAAM,aAAa;IACxB,MAAM;IAEN,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,YAAY;QAC5C,IAAI,MAAM,OAAO,GAAG;QAEpB,MAAM,YAAY,GAAG;QAErB,IAAI,KAAK;YACP,IAAI,MAAM,aAAa,EAAE;gBACvB,MAAM,WAAW,GAAG;gBACpB,MAAM,aAAa,GAAG;YACxB,OAAO;gBACL,MAAM,QAAQ,GAAG;YACnB;QACF;QAEA,IAAI,OAAO,CAAE,MAAM,aAAa,EAAE;YAChC,MAAM,WAAW,GAAG;YACpB,MAAM,QAAQ,GAAG;QACnB;QAEA,IAAI,KAAK;YACP,MAAM,OAAO,QAAQ,GAAI,CAAC;QAC5B;QAEA,IAAI,KAAK,OAAO,IAAI;QAEpB,IAAI,OAAO,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,GAAG,GAAG;YACnD,MAAM,QAAQ,GAAG;YACjB,OAAO,SAAS;YAChB,OAAO;QACT,OAAO,IAAI,OAAO,OAAO,KAAK;YAC5B,MAAM,YAAY,GAAG;YACrB,OAAO,MAAM,CAAC;YAAM,OAAO,GAAG,CAAC;YAC/B,OAAO;QACT,OAAO,IAAI,OAAO,OAAO,OAAO,KAAK;YACnC,MAAM,QAAQ,GAAG;YACjB,OAAO;QACT,OAAO,IAAI,OAAO,QAAQ,MAAM,QAAQ,KAAK,SAAS;YACpD,IAAI,OAAO,GAAG,IAAI;gBAChB,kBAAkB;gBAClB,MAAM,aAAa,GAAG;YACxB;QACF;QAEA,OAAO,MAAM,QAAQ;IACvB;IAEA,YAAY;QACV,OAAO;YACL,UAAW;YACX,eAAgB;YAChB,aAAc;YACd,cAAe,MAAS,6BAA6B;QACvD;IACF;AAEF", "ignoreList": [0], "debugId": null}}]}