{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/cypher.js"], "sourcesContent": ["var wordRegexp = function(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n};\n\nvar tokenBase = function(stream/*, state*/) {\n  curPunc = null;\n  var ch = stream.next();\n  if (ch ==='\"') {\n    stream.match(/^.*?\"/);\n    return \"string\";\n  }\n  if (ch === \"'\") {\n    stream.match(/^.*?'/);\n    return \"string\";\n  }\n  if (/[{}\\(\\),\\.;\\[\\]]/.test(ch)) {\n    curPunc = ch;\n    return \"punctuation\";\n  } else if (ch === \"/\" && stream.eat(\"/\")) {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (operatorChars.test(ch)) {\n    stream.eatWhile(operatorChars);\n    return null;\n  } else {\n    stream.eatWhile(/[_\\w\\d]/);\n    if (stream.eat(\":\")) {\n      stream.eatWhile(/[\\w\\d_\\-]/);\n      return \"atom\";\n    }\n    var word = stream.current();\n    if (funcs.test(word)) return \"builtin\";\n    if (preds.test(word)) return \"def\";\n    if (keywords.test(word) || systemKeywords.test(word)) return \"keyword\";\n    return \"variable\";\n  }\n};\nvar pushContext = function(state, type, col) {\n  return state.context = {\n    prev: state.context,\n    indent: state.indent,\n    col: col,\n    type: type\n  };\n};\nvar popContext = function(state) {\n  state.indent = state.context.indent;\n  return state.context = state.context.prev;\n};\nvar curPunc;\nvar funcs = wordRegexp([\"abs\", \"acos\", \"allShortestPaths\", \"asin\", \"atan\", \"atan2\", \"avg\", \"ceil\", \"coalesce\", \"collect\", \"cos\", \"cot\", \"count\", \"degrees\", \"e\", \"endnode\", \"exp\", \"extract\", \"filter\", \"floor\", \"haversin\", \"head\", \"id\", \"keys\", \"labels\", \"last\", \"left\", \"length\", \"log\", \"log10\", \"lower\", \"ltrim\", \"max\", \"min\", \"node\", \"nodes\", \"percentileCont\", \"percentileDisc\", \"pi\", \"radians\", \"rand\", \"range\", \"reduce\", \"rel\", \"relationship\", \"relationships\", \"replace\", \"reverse\", \"right\", \"round\", \"rtrim\", \"shortestPath\", \"sign\", \"sin\", \"size\", \"split\", \"sqrt\", \"startnode\", \"stdev\", \"stdevp\", \"str\", \"substring\", \"sum\", \"tail\", \"tan\", \"timestamp\", \"toFloat\", \"toInt\", \"toString\", \"trim\", \"type\", \"upper\"]);\nvar preds = wordRegexp([\"all\", \"and\", \"any\", \"contains\", \"exists\", \"has\", \"in\", \"none\", \"not\", \"or\", \"single\", \"xor\"]);\nvar keywords = wordRegexp([\"as\", \"asc\", \"ascending\", \"assert\", \"by\", \"case\", \"commit\", \"constraint\", \"create\", \"csv\", \"cypher\", \"delete\", \"desc\", \"descending\", \"detach\", \"distinct\", \"drop\", \"else\", \"end\", \"ends\", \"explain\", \"false\", \"fieldterminator\", \"foreach\", \"from\", \"headers\", \"in\", \"index\", \"is\", \"join\", \"limit\", \"load\", \"match\", \"merge\", \"null\", \"on\", \"optional\", \"order\", \"periodic\", \"profile\", \"remove\", \"return\", \"scan\", \"set\", \"skip\", \"start\", \"starts\", \"then\", \"true\", \"union\", \"unique\", \"unwind\", \"using\", \"when\", \"where\", \"with\", \"call\", \"yield\"]);\nvar systemKeywords = wordRegexp([\"access\", \"active\", \"assign\", \"all\", \"alter\", \"as\", \"catalog\", \"change\", \"copy\", \"create\", \"constraint\", \"constraints\", \"current\", \"database\", \"databases\", \"dbms\", \"default\", \"deny\", \"drop\", \"element\", \"elements\", \"exists\", \"from\", \"grant\", \"graph\", \"graphs\", \"if\", \"index\", \"indexes\", \"label\", \"labels\", \"management\", \"match\", \"name\", \"names\", \"new\", \"node\", \"nodes\", \"not\", \"of\", \"on\", \"or\", \"password\", \"populated\", \"privileges\", \"property\", \"read\", \"relationship\", \"relationships\", \"remove\", \"replace\", \"required\", \"revoke\", \"role\", \"roles\", \"set\", \"show\", \"start\", \"status\", \"stop\", \"suspended\", \"to\", \"traverse\", \"type\", \"types\", \"user\", \"users\", \"with\", \"write\"]);\nvar operatorChars = /[*+\\-<>=&|~%^]/;\n\nexport const cypher = {\n  name: \"cypher\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      context: null,\n      indent: 0,\n      col: 0\n    };\n  },\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if (state.context && (state.context.align == null)) {\n        state.context.align = false;\n      }\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) {\n      return null;\n    }\n    var style = state.tokenize(stream, state);\n    if (style !== \"comment\" && state.context && (state.context.align == null) && state.context.type !== \"pattern\") {\n      state.context.align = true;\n    }\n    if (curPunc === \"(\") {\n      pushContext(state, \")\", stream.column());\n    } else if (curPunc === \"[\") {\n      pushContext(state, \"]\", stream.column());\n    } else if (curPunc === \"{\") {\n      pushContext(state, \"}\", stream.column());\n    } else if (/[\\]\\}\\)]/.test(curPunc)) {\n      while (state.context && state.context.type === \"pattern\") {\n        popContext(state);\n      }\n      if (state.context && curPunc === state.context.type) {\n        popContext(state);\n      }\n    } else if (curPunc === \".\" && state.context && state.context.type === \"pattern\") {\n      popContext(state);\n    } else if (/atom|string|variable/.test(style) && state.context) {\n      if (/[\\}\\]]/.test(state.context.type)) {\n        pushContext(state, \"pattern\", stream.column());\n      } else if (state.context.type === \"pattern\" && !state.context.align) {\n        state.context.align = true;\n        state.context.col = stream.column();\n      }\n    }\n    return style;\n  },\n  indent: function(state, textAfter, cx) {\n    var firstChar = textAfter && textAfter.charAt(0);\n    var context = state.context;\n    if (/[\\]\\}]/.test(firstChar)) {\n      while (context && context.type === \"pattern\") {\n        context = context.prev;\n      }\n    }\n    var closing = context && firstChar === context.type;\n    if (!context) return 0;\n    if (context.type === \"keywords\") return null\n    if (context.align) return context.col + (closing ? 0 : 1);\n    return context.indent + (closing ? 0 : cx.unit);\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,aAAa,SAAS,KAAK;IAC7B,OAAO,IAAI,OAAO,SAAS,MAAM,IAAI,CAAC,OAAO,MAAM;AACrD;AAEA,IAAI,YAAY,SAAS,OAAM,SAAS,GAAT;IAC7B,UAAU;IACV,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,OAAM,KAAK;QACb,OAAO,KAAK,CAAC;QACb,OAAO;IACT;IACA,IAAI,OAAO,KAAK;QACd,OAAO,KAAK,CAAC;QACb,OAAO;IACT;IACA,IAAI,mBAAmB,IAAI,CAAC,KAAK;QAC/B,UAAU;QACV,OAAO;IACT,OAAO,IAAI,OAAO,OAAO,OAAO,GAAG,CAAC,MAAM;QACxC,OAAO,SAAS;QAChB,OAAO;IACT,OAAO,IAAI,cAAc,IAAI,CAAC,KAAK;QACjC,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OAAO;QACL,OAAO,QAAQ,CAAC;QAChB,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,IAAI,OAAO,OAAO,OAAO;QACzB,IAAI,MAAM,IAAI,CAAC,OAAO,OAAO;QAC7B,IAAI,MAAM,IAAI,CAAC,OAAO,OAAO;QAC7B,IAAI,SAAS,IAAI,CAAC,SAAS,eAAe,IAAI,CAAC,OAAO,OAAO;QAC7D,OAAO;IACT;AACF;AACA,IAAI,cAAc,SAAS,KAAK,EAAE,IAAI,EAAE,GAAG;IACzC,OAAO,MAAM,OAAO,GAAG;QACrB,MAAM,MAAM,OAAO;QACnB,QAAQ,MAAM,MAAM;QACpB,KAAK;QACL,MAAM;IACR;AACF;AACA,IAAI,aAAa,SAAS,KAAK;IAC7B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM;IACnC,OAAO,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;AAC3C;AACA,IAAI;AACJ,IAAI,QAAQ,WAAW;IAAC;IAAO;IAAQ;IAAoB;IAAQ;IAAQ;IAAS;IAAO;IAAQ;IAAY;IAAW;IAAO;IAAO;IAAS;IAAW;IAAK;IAAW;IAAO;IAAW;IAAU;IAAS;IAAY;IAAQ;IAAM;IAAQ;IAAU;IAAQ;IAAQ;IAAU;IAAO;IAAS;IAAS;IAAS;IAAO;IAAO;IAAQ;IAAS;IAAkB;IAAkB;IAAM;IAAW;IAAQ;IAAS;IAAU;IAAO;IAAgB;IAAiB;IAAW;IAAW;IAAS;IAAS;IAAS;IAAgB;IAAQ;IAAO;IAAQ;IAAS;IAAQ;IAAa;IAAS;IAAU;IAAO;IAAa;IAAO;IAAQ;IAAO;IAAa;IAAW;IAAS;IAAY;IAAQ;IAAQ;CAAQ;AACxsB,IAAI,QAAQ,WAAW;IAAC;IAAO;IAAO;IAAO;IAAY;IAAU;IAAO;IAAM;IAAQ;IAAO;IAAM;IAAU;CAAM;AACrH,IAAI,WAAW,WAAW;IAAC;IAAM;IAAO;IAAa;IAAU;IAAM;IAAQ;IAAU;IAAc;IAAU;IAAO;IAAU;IAAU;IAAQ;IAAc;IAAU;IAAY;IAAQ;IAAQ;IAAO;IAAQ;IAAW;IAAS;IAAmB;IAAW;IAAQ;IAAW;IAAM;IAAS;IAAM;IAAQ;IAAS;IAAQ;IAAS;IAAS;IAAQ;IAAM;IAAY;IAAS;IAAY;IAAW;IAAU;IAAU;IAAQ;IAAO;IAAQ;IAAS;IAAU;IAAQ;IAAQ;IAAS;IAAU;IAAU;IAAS;IAAQ;IAAS;IAAQ;IAAQ;CAAQ;AACjjB,IAAI,iBAAiB,WAAW;IAAC;IAAU;IAAU;IAAU;IAAO;IAAS;IAAM;IAAW;IAAU;IAAQ;IAAU;IAAc;IAAe;IAAW;IAAY;IAAa;IAAQ;IAAW;IAAQ;IAAQ;IAAW;IAAY;IAAU;IAAQ;IAAS;IAAS;IAAU;IAAM;IAAS;IAAW;IAAS;IAAU;IAAc;IAAS;IAAQ;IAAS;IAAO;IAAQ;IAAS;IAAO;IAAM;IAAM;IAAM;IAAY;IAAa;IAAc;IAAY;IAAQ;IAAgB;IAAiB;IAAU;IAAW;IAAY;IAAU;IAAQ;IAAS;IAAO;IAAQ;IAAS;IAAU;IAAQ;IAAa;IAAM;IAAY;IAAQ;IAAS;IAAQ;IAAS;IAAQ;CAAQ;AAC9rB,IAAI,gBAAgB;AAEb,MAAM,SAAS;IACpB,MAAM;IACN,YAAY;QACV,OAAO;YACL,UAAU;YACV,SAAS;YACT,QAAQ;YACR,KAAK;QACP;IACF;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,MAAM,OAAO,IAAK,MAAM,OAAO,CAAC,KAAK,IAAI,MAAO;gBAClD,MAAM,OAAO,CAAC,KAAK,GAAG;YACxB;YACA,MAAM,MAAM,GAAG,OAAO,WAAW;QACnC;QACA,IAAI,OAAO,QAAQ,IAAI;YACrB,OAAO;QACT;QACA,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QACnC,IAAI,UAAU,aAAa,MAAM,OAAO,IAAK,MAAM,OAAO,CAAC,KAAK,IAAI,QAAS,MAAM,OAAO,CAAC,IAAI,KAAK,WAAW;YAC7G,MAAM,OAAO,CAAC,KAAK,GAAG;QACxB;QACA,IAAI,YAAY,KAAK;YACnB,YAAY,OAAO,KAAK,OAAO,MAAM;QACvC,OAAO,IAAI,YAAY,KAAK;YAC1B,YAAY,OAAO,KAAK,OAAO,MAAM;QACvC,OAAO,IAAI,YAAY,KAAK;YAC1B,YAAY,OAAO,KAAK,OAAO,MAAM;QACvC,OAAO,IAAI,WAAW,IAAI,CAAC,UAAU;YACnC,MAAO,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,KAAK,UAAW;gBACxD,WAAW;YACb;YACA,IAAI,MAAM,OAAO,IAAI,YAAY,MAAM,OAAO,CAAC,IAAI,EAAE;gBACnD,WAAW;YACb;QACF,OAAO,IAAI,YAAY,OAAO,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,KAAK,WAAW;YAC/E,WAAW;QACb,OAAO,IAAI,uBAAuB,IAAI,CAAC,UAAU,MAAM,OAAO,EAAE;YAC9D,IAAI,SAAS,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,GAAG;gBACrC,YAAY,OAAO,WAAW,OAAO,MAAM;YAC7C,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,KAAK,aAAa,CAAC,MAAM,OAAO,CAAC,KAAK,EAAE;gBACnE,MAAM,OAAO,CAAC,KAAK,GAAG;gBACtB,MAAM,OAAO,CAAC,GAAG,GAAG,OAAO,MAAM;YACnC;QACF;QACA,OAAO;IACT;IACA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,YAAY,aAAa,UAAU,MAAM,CAAC;QAC9C,IAAI,UAAU,MAAM,OAAO;QAC3B,IAAI,SAAS,IAAI,CAAC,YAAY;YAC5B,MAAO,WAAW,QAAQ,IAAI,KAAK,UAAW;gBAC5C,UAAU,QAAQ,IAAI;YACxB;QACF;QACA,IAAI,UAAU,WAAW,cAAc,QAAQ,IAAI;QACnD,IAAI,CAAC,SAAS,OAAO;QACrB,IAAI,QAAQ,IAAI,KAAK,YAAY,OAAO;QACxC,IAAI,QAAQ,KAAK,EAAE,OAAO,QAAQ,GAAG,GAAG,CAAC,UAAU,IAAI,CAAC;QACxD,OAAO,QAAQ,MAAM,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI;IAChD;AACF", "ignoreList": [0], "debugId": null}}]}