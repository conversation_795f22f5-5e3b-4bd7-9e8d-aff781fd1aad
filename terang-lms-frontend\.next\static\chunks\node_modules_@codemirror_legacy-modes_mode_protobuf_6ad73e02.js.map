{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/protobuf.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\", \"i\");\n};\n\nvar keywordArray = [\n  \"package\", \"message\", \"import\", \"syntax\",\n  \"required\", \"optional\", \"repeated\", \"reserved\", \"default\", \"extensions\", \"packed\",\n  \"bool\", \"bytes\", \"double\", \"enum\", \"float\", \"string\",\n  \"int32\", \"int64\", \"uint32\", \"uint64\", \"sint32\", \"sint64\", \"fixed32\", \"fixed64\", \"sfixed32\", \"sfixed64\",\n  \"option\", \"service\", \"rpc\", \"returns\"\n];\nvar keywords = wordRegexp(keywordArray);\n\nvar identifiers = new RegExp(\"^[_A-Za-z\\xa1-\\uffff][_A-Za-z0-9\\xa1-\\uffff]*\");\n\nfunction tokenBase(stream) {\n  // whitespaces\n  if (stream.eatSpace()) return null;\n\n  // Handle one line Comments\n  if (stream.match(\"//\")) {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  // Handle Number Literals\n  if (stream.match(/^[0-9\\.+-]/, false)) {\n    if (stream.match(/^[+-]?0x[0-9a-fA-F]+/))\n      return \"number\";\n    if (stream.match(/^[+-]?\\d*\\.\\d+([EeDd][+-]?\\d+)?/))\n      return \"number\";\n    if (stream.match(/^[+-]?\\d+([EeDd][+-]?\\d+)?/))\n      return \"number\";\n  }\n\n  // Handle Strings\n  if (stream.match(/^\"([^\"]|(\"\"))*\"/)) { return \"string\"; }\n  if (stream.match(/^'([^']|(''))*'/)) { return \"string\"; }\n\n  // Handle words\n  if (stream.match(keywords)) { return \"keyword\"; }\n  if (stream.match(identifiers)) { return \"variable\"; } ;\n\n  // Handle non-detected items\n  stream.next();\n  return null;\n};\n\nexport const protobuf = {\n  name: \"protobuf\",\n  token: tokenBase,\n  languageData: {\n    autocomplete: keywordArray\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,WAAW,KAAK;IACvB,OAAO,IAAI,OAAO,QAAQ,MAAM,IAAI,CAAC,SAAS,SAAS;AACzD;;AAEA,IAAI,eAAe;IACjB;IAAW;IAAW;IAAU;IAChC;IAAY;IAAY;IAAY;IAAY;IAAW;IAAc;IACzE;IAAQ;IAAS;IAAU;IAAQ;IAAS;IAC5C;IAAS;IAAS;IAAU;IAAU;IAAU;IAAU;IAAW;IAAW;IAAY;IAC5F;IAAU;IAAW;IAAO;CAC7B;AACD,IAAI,WAAW,WAAW;AAE1B,IAAI,cAAc,IAAI,OAAO;AAE7B,SAAS,UAAU,MAAM;IACvB,cAAc;IACd,IAAI,OAAO,QAAQ,IAAI,OAAO;IAE9B,2BAA2B;IAC3B,IAAI,OAAO,KAAK,CAAC,OAAO;QACtB,OAAO,SAAS;QAChB,OAAO;IACT;IAEA,yBAAyB;IACzB,IAAI,OAAO,KAAK,CAAC,cAAc,QAAQ;QACrC,IAAI,OAAO,KAAK,CAAC,yBACf,OAAO;QACT,IAAI,OAAO,KAAK,CAAC,oCACf,OAAO;QACT,IAAI,OAAO,KAAK,CAAC,+BACf,OAAO;IACX;IAEA,iBAAiB;IACjB,IAAI,OAAO,KAAK,CAAC,oBAAoB;QAAE,OAAO;IAAU;IACxD,IAAI,OAAO,KAAK,CAAC,oBAAoB;QAAE,OAAO;IAAU;IAExD,eAAe;IACf,IAAI,OAAO,KAAK,CAAC,WAAW;QAAE,OAAO;IAAW;IAChD,IAAI,OAAO,KAAK,CAAC,cAAc;QAAE,OAAO;IAAY;;IAEpD,4BAA4B;IAC5B,OAAO,IAAI;IACX,OAAO;AACT;;AAEO,MAAM,WAAW;IACtB,MAAM;IACN,OAAO;IACP,cAAc;QACZ,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}]}