{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/forth.js"], "sourcesContent": ["function toWordList(words) {\n  var ret = [];\n  words.split(' ').forEach(function(e){\n    ret.push({name: e});\n  });\n  return ret;\n}\n\nvar coreWordList = toWordList(\n  'INVERT AND OR XOR\\\n 2* 2/ LSHIFT RSHIFT\\\n 0= = 0< < > U< MIN MAX\\\n 2DROP 2DUP 2OVER 2SWAP ?DUP DEPTH DROP DUP OVER ROT SWAP\\\n >R R> R@\\\n + - 1+ 1- ABS NEGATE\\\n S>D * M* UM*\\\n FM/MOD SM/REM UM/MOD */ */MOD / /MOD MOD\\\n HERE , @ ! CELL+ CELLS C, C@ C! CHARS 2@ 2!\\\n ALIGN ALIGNED +! ALLOT\\\n CHAR [CHAR] [ ] BL\\\n FIND EXECUTE IMMEDIATE COUNT LITERAL STATE\\\n ; DOES> >BODY\\\n EVALUATE\\\n SOURCE >IN\\\n <# # #S #> HOLD SIGN BASE >NUMBER HEX DECIMAL\\\n FILL MOVE\\\n . CR EMIT SPACE SPACES TYPE U. .R U.R\\\n ACCEPT\\\n TRUE FALSE\\\n <> U> 0<> 0>\\\n NIP TUCK ROLL PICK\\\n 2>R 2R@ 2R>\\\n WITHIN UNUSED MARKER\\\n I J\\\n TO\\\n COMPILE, [COMPILE]\\\n SAVE-INPUT RESTORE-INPUT\\\n PAD ERASE\\\n 2LITERAL DNEGATE\\\n D- D+ D0< D0= D2* D2/ D< D= DMAX DMIN D>S DABS\\\n M+ M*/ D. D.R 2ROT DU<\\\n CATCH THROW\\\n FREE RESIZE ALLOCATE\\\n CS-PICK CS-ROLL\\\n GET-CURRENT SET-CURRENT FORTH-WORDLIST GET-ORDER SET-ORDER\\\n PREVIOUS SEARCH-WORDLIST WORDLIST FIND ALSO ONLY FORTH DEFINITIONS ORDER\\\n -TRAILING /STRING SEARCH COMPARE CMOVE CMOVE> BLANK SLITERAL');\n\nvar immediateWordList = toWordList('IF ELSE THEN BEGIN WHILE REPEAT UNTIL RECURSE [IF] [ELSE] [THEN] ?DO DO LOOP +LOOP UNLOOP LEAVE EXIT AGAIN CASE OF ENDOF ENDCASE');\n\nfunction searchWordList (wordList, word) {\n  var i;\n  for (i = wordList.length - 1; i >= 0; i--) {\n    if (wordList[i].name === word.toUpperCase()) {\n      return wordList[i];\n    }\n  }\n  return undefined;\n}\nexport const forth = {\n  name: \"forth\",\n  startState: function() {\n    return {\n      state: '',\n      base: 10,\n      coreWordList: coreWordList,\n      immediateWordList: immediateWordList,\n      wordList: []\n    };\n  },\n  token: function (stream, stt) {\n    var mat;\n    if (stream.eatSpace()) {\n      return null;\n    }\n    if (stt.state === '') { // interpretation\n      if (stream.match(/^(\\]|:NONAME)(\\s|$)/i)) {\n        stt.state = ' compilation';\n        return 'builtin';\n      }\n      mat = stream.match(/^(\\:)\\s+(\\S+)(\\s|$)+/);\n      if (mat) {\n        stt.wordList.push({name: mat[2].toUpperCase()});\n        stt.state = ' compilation';\n        return 'def';\n      }\n      mat = stream.match(/^(VARIABLE|2VARIABLE|CONSTANT|2CONSTANT|CREATE|POSTPONE|VALUE|WORD)\\s+(\\S+)(\\s|$)+/i);\n      if (mat) {\n        stt.wordList.push({name: mat[2].toUpperCase()});\n        return 'def';\n      }\n      mat = stream.match(/^(\\'|\\[\\'\\])\\s+(\\S+)(\\s|$)+/);\n      if (mat) {\n        return 'builtin'\n      }\n    } else { // compilation\n      // ; [\n      if (stream.match(/^(\\;|\\[)(\\s)/)) {\n        stt.state = '';\n        stream.backUp(1);\n        return 'builtin';\n      }\n      if (stream.match(/^(\\;|\\[)($)/)) {\n        stt.state = '';\n        return 'builtin';\n      }\n      if (stream.match(/^(POSTPONE)\\s+\\S+(\\s|$)+/)) {\n        return 'builtin';\n      }\n    }\n\n    // dynamic wordlist\n    mat = stream.match(/^(\\S+)(\\s+|$)/);\n    if (mat) {\n      if (searchWordList(stt.wordList, mat[1]) !== undefined) {\n        return 'variable';\n      }\n\n      // comments\n      if (mat[1] === '\\\\') {\n        stream.skipToEnd();\n        return 'comment';\n      }\n\n      // core words\n      if (searchWordList(stt.coreWordList, mat[1]) !== undefined) {\n        return 'builtin';\n      }\n      if (searchWordList(stt.immediateWordList, mat[1]) !== undefined) {\n        return 'keyword';\n      }\n\n      if (mat[1] === '(') {\n        stream.eatWhile(function (s) { return s !== ')'; });\n        stream.eat(')');\n        return 'comment';\n      }\n\n      // // strings\n      if (mat[1] === '.(') {\n        stream.eatWhile(function (s) { return s !== ')'; });\n        stream.eat(')');\n        return 'string';\n      }\n      if (mat[1] === 'S\"' || mat[1] === '.\"' || mat[1] === 'C\"') {\n        stream.eatWhile(function (s) { return s !== '\"'; });\n        stream.eat('\"');\n        return 'string';\n      }\n\n      // numbers\n      if (mat[1] - 0xfffffffff) {\n        return 'number';\n      }\n      // if (mat[1].match(/^[-+]?[0-9]+\\.[0-9]*/)) {\n      //     return 'number';\n      // }\n\n      return 'atom';\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,WAAW,KAAK;IACvB,IAAI,MAAM,EAAE;IACZ,MAAM,KAAK,CAAC,KAAK,OAAO,CAAC,SAAS,CAAC;QACjC,IAAI,IAAI,CAAC;YAAC,MAAM;QAAC;IACnB;IACA,OAAO;AACT;AAEA,IAAI,eAAe,WACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCF,IAAI,oBAAoB,WAAW;AAEnC,SAAS,eAAgB,QAAQ,EAAE,IAAI;IACrC,IAAI;IACJ,IAAK,IAAI,SAAS,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QACzC,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,WAAW,IAAI;YAC3C,OAAO,QAAQ,CAAC,EAAE;QACpB;IACF;IACA,OAAO;AACT;AACO,MAAM,QAAQ;IACnB,MAAM;IACN,YAAY;QACV,OAAO;YACL,OAAO;YACP,MAAM;YACN,cAAc;YACd,mBAAmB;YACnB,UAAU,EAAE;QACd;IACF;IACA,OAAO,SAAU,MAAM,EAAE,GAAG;QAC1B,IAAI;QACJ,IAAI,OAAO,QAAQ,IAAI;YACrB,OAAO;QACT;QACA,IAAI,IAAI,KAAK,KAAK,IAAI;YACpB,IAAI,OAAO,KAAK,CAAC,yBAAyB;gBACxC,IAAI,KAAK,GAAG;gBACZ,OAAO;YACT;YACA,MAAM,OAAO,KAAK,CAAC;YACnB,IAAI,KAAK;gBACP,IAAI,QAAQ,CAAC,IAAI,CAAC;oBAAC,MAAM,GAAG,CAAC,EAAE,CAAC,WAAW;gBAAE;gBAC7C,IAAI,KAAK,GAAG;gBACZ,OAAO;YACT;YACA,MAAM,OAAO,KAAK,CAAC;YACnB,IAAI,KAAK;gBACP,IAAI,QAAQ,CAAC,IAAI,CAAC;oBAAC,MAAM,GAAG,CAAC,EAAE,CAAC,WAAW;gBAAE;gBAC7C,OAAO;YACT;YACA,MAAM,OAAO,KAAK,CAAC;YACnB,IAAI,KAAK;gBACP,OAAO;YACT;QACF,OAAO;YACL,MAAM;YACN,IAAI,OAAO,KAAK,CAAC,iBAAiB;gBAChC,IAAI,KAAK,GAAG;gBACZ,OAAO,MAAM,CAAC;gBACd,OAAO;YACT;YACA,IAAI,OAAO,KAAK,CAAC,gBAAgB;gBAC/B,IAAI,KAAK,GAAG;gBACZ,OAAO;YACT;YACA,IAAI,OAAO,KAAK,CAAC,6BAA6B;gBAC5C,OAAO;YACT;QACF;QAEA,mBAAmB;QACnB,MAAM,OAAO,KAAK,CAAC;QACnB,IAAI,KAAK;YACP,IAAI,eAAe,IAAI,QAAQ,EAAE,GAAG,CAAC,EAAE,MAAM,WAAW;gBACtD,OAAO;YACT;YAEA,WAAW;YACX,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM;gBACnB,OAAO,SAAS;gBAChB,OAAO;YACT;YAEA,aAAa;YACb,IAAI,eAAe,IAAI,YAAY,EAAE,GAAG,CAAC,EAAE,MAAM,WAAW;gBAC1D,OAAO;YACT;YACA,IAAI,eAAe,IAAI,iBAAiB,EAAE,GAAG,CAAC,EAAE,MAAM,WAAW;gBAC/D,OAAO;YACT;YAEA,IAAI,GAAG,CAAC,EAAE,KAAK,KAAK;gBAClB,OAAO,QAAQ,CAAC,SAAU,CAAC;oBAAI,OAAO,MAAM;gBAAK;gBACjD,OAAO,GAAG,CAAC;gBACX,OAAO;YACT;YAEA,aAAa;YACb,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM;gBACnB,OAAO,QAAQ,CAAC,SAAU,CAAC;oBAAI,OAAO,MAAM;gBAAK;gBACjD,OAAO,GAAG,CAAC;gBACX,OAAO;YACT;YACA,IAAI,GAAG,CAAC,EAAE,KAAK,QAAQ,GAAG,CAAC,EAAE,KAAK,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM;gBACzD,OAAO,QAAQ,CAAC,SAAU,CAAC;oBAAI,OAAO,MAAM;gBAAK;gBACjD,OAAO,GAAG,CAAC;gBACX,OAAO;YACT;YAEA,UAAU;YACV,IAAI,GAAG,CAAC,EAAE,GAAG,aAAa;gBACxB,OAAO;YACT;YACA,8CAA8C;YAC9C,uBAAuB;YACvB,IAAI;YAEJ,OAAO;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}]}