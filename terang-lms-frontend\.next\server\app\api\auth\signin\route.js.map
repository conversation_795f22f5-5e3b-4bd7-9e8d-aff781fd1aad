{"version": 3, "file": "../app/api/auth/signin/route.js", "mappings": "2dAEA,GAAI,CAACA,QAAQC,GAAG,CAACC,YAAY,CAC3B,CAD6B,KACvB,MAAU,iDAGlB,IAAMC,EAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAACJ,QAAQC,GAAG,CAACC,YAAY,EAElC,eAAeG,EAAMC,CAA0B,CAAE,GAAGC,CAAa,EAEtE,OADe,MAAMJ,EAAIG,KAASC,EAEpC,yBCXA,6GCAA,oDCAA,qGCAA,mECAA,oDCAA,iDCAA,kDCAA,gDCAA,wGCAA,gECAA,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,2CCAA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,oCCRA,sGCAA,qDCAA,qECAA,qDCAA,kECAA,yDCAA,uDCAA,6GCAA,oDCAA,6DCAA,wDCAA,iECAA,uDCAA,sXCKO,eAAeC,EAAKC,CAAoB,EAC7C,GAAI,CAEF,GAAM,OAAEC,CAAK,CAAEC,UAAQ,CAAE,CADM,EACHC,IADSH,EAAQI,IAAI,CAAZJ,EAGrC,GAAI,CAACC,GAAS,CAACC,CAAVD,CACH,MADaC,CACNG,CADgB,CAChBA,YAAAA,CAAaD,IAAI,CACtB,CACEE,OAAAA,EAAS,EACTC,KAAAA,CAAO,kCACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,GAWlB,IAAMC,EAAOC,CANE,CAMTD,KANeb,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;;sBAIT,EAAEK,EAAMU,GAANV,QAAiB,GAAG;KACxC,CACmB,CAAC,EAAE,CAEtB,GAAI,CAACQ,GASD,CATCA,EAAM,IAQmBG,EAAAA,EAAAA,CAAAA,IACzBC,EAAiB,CADsB,CAACX,EAAUO,EAAKP,IAAfA,IAAuB,EAPlE,OAAOG,EAAAA,YAAAA,CAAaD,IAAI,CACtB,CAAEE,OAAAA,EAAS,EAAOC,KAAAA,CAAO,4BAA4B,CACrD,CAAEC,MAAAA,CAAQ,GAAI,GAclB,IAAMM,EAAqB,CACzBC,EAAAA,CAAIN,EAAKM,EAAE,CACXC,IAAAA,CAAMP,EAAKO,IAAI,CACff,KAAAA,CAAOQ,EAAKR,KAAK,CACjBgB,IAAAA,CAAMR,EAAKQ,IAAI,CACfC,aAAAA,CAAeT,EAAKU,EAALV,YAAmB,OAAIW,CACxC,EAEA,OAAOf,EAAAA,YAAAA,CAAaD,IAAI,CAAC,CACvBE,OAAAA,EAAS,EACTe,IAAAA,CAAM,CACJZ,IAAAA,CAAMK,EACNQ,MADMR,KACNQ,CAAab,EACT,CAAEM,CADON,CACPM,CAAIN,EAAKU,cAAc,CAAEH,IAAAA,CAAMP,EAAKc,gBAAAA,CAAiB,MACvDH,CACN,EACAI,OAAAA,CAAS,oBACX,EACF,CAAE,MAAOjB,EAAO,CAEd,EAFOA,KACPkB,OAAAA,CAAQlB,KAAK,CAAC,iBAAkBA,GACzBF,EADyBE,CAAAA,WACzBF,CAAaD,IAAI,CACtB,CAAEE,OAAAA,EAAS,EAAOC,KAAAA,CAAO,wBAAwB,CACjD,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CC9DA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CACrC,IAAI,EAGJ,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,KAAO,CAER,CAGA,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,kBAAkB,SACtC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,MAAekB,EAA4B,EAA7B,GAAkC,EAAR,EAEpC,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,CAAeC,OAA4B,EAA7B,GAAkC,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,8BACA,4BACA,iBACA,sCACA,CAAK,CACL,iJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,uCC5BA,iDCAA,2DCAA,2DCAA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://terang-lms-ui/./src/lib/db/raw.ts", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/src/app/api/auth/signin/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?7619", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["import { neon } from '@neondatabase/serverless';\r\n\r\nif (!process.env.DATABASE_URL) {\r\n  throw new Error('DATABASE_URL environment variable is required');\r\n}\r\n\r\nconst sql = neon(process.env.DATABASE_URL);\r\n\r\nexport async function query(text: TemplateStringsArray, ...params: any[]) {\r\n  const result = await sql(text, ...params);\r\n  return result;\r\n}\r\n", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 44725;\nmodule.exports = webpackEmptyContext;", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport bcrypt from 'bcryptjs';\r\nimport { query } from '@/lib/db/raw';\r\nimport { LoginCredentials, ApiResponse, AuthUser } from '@/types/database';\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body: LoginCredentials = await request.json();\r\n    const { email, password } = body;\r\n\r\n    if (!email || !password) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Email and password are required'\r\n        } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Find user by email\r\n    const result = await query`\r\n      SELECT u.*, i.name as institution_name\r\n      FROM users u\r\n      LEFT JOIN institutions i ON u.institution_id = i.id\r\n      WHERE u.email = ${email.toLowerCase()}\r\n    `;\r\n    const user = result[0];\r\n\r\n    if (!user) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Invalid email or password' } as ApiResponse,\r\n        { status: 401 }\r\n      );\r\n    }\r\n\r\n    // Verify password\r\n    const isValidPassword = await bcrypt.compare(password, user.password);\r\n    if (!isValidPassword) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Invalid email or password' } as ApiResponse,\r\n        { status: 401 }\r\n      );\r\n    }\r\n\r\n    // Create auth user object (without password)\r\n    const authUser: AuthUser = {\r\n      id: user.id,\r\n      name: user.name,\r\n      email: user.email,\r\n      role: user.role,\r\n      institutionId: user.institution_id || undefined\r\n    };\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: {\r\n        user: authUser,\r\n        institution: user\r\n          ? { id: user.institution_id, name: user.institution_name }\r\n          : undefined\r\n      },\r\n      message: 'Sign in successful'\r\n    } as ApiResponse);\r\n  } catch (error) {\r\n    console.error('Sign in error:', error);\r\n    return NextResponse.json(\r\n      { success: false, error: 'Internal server error' } as ApiResponse,\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/auth/signin',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\auth\\\\signin\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/auth/signin/route\",\n        pathname: \"/api/auth/signin\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/signin/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\auth\\\\signin\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["process", "env", "DATABASE_URL", "sql", "neon", "query", "text", "params", "POST", "request", "email", "password", "body", "json", "NextResponse", "success", "error", "status", "user", "result", "toLowerCase", "bcrypt", "isValidPassword", "authUser", "id", "name", "role", "institutionId", "institution_id", "undefined", "data", "institution", "institution_name", "message", "console", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}