{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/cmake.js"], "sourcesContent": ["var variable_regex = /({)?[a-zA-Z0-9_]+(})?/;\n\nfunction tokenString(stream, state) {\n  var current, prev, found_var = false;\n  while (!stream.eol() && (current = stream.next()) != state.pending) {\n    if (current === '$' && prev != '\\\\' && state.pending == '\"') {\n      found_var = true;\n      break;\n    }\n    prev = current;\n  }\n  if (found_var) {\n    stream.backUp(1);\n  }\n  if (current == state.pending) {\n    state.continueString = false;\n  } else {\n    state.continueString = true;\n  }\n  return \"string\";\n}\n\nfunction tokenize(stream, state) {\n  var ch = stream.next();\n\n  // Have we found a variable?\n  if (ch === '$') {\n    if (stream.match(variable_regex)) {\n      return 'variableName.special';\n    }\n    return 'variable';\n  }\n  // Should we still be looking for the end of a string?\n  if (state.continueString) {\n    // If so, go through the loop again\n    stream.backUp(1);\n    return tokenString(stream, state);\n  }\n  // Do we just have a function on our hands?\n  // In 'cmake_minimum_required (VERSION 2.8.8)', 'cmake_minimum_required' is matched\n  if (stream.match(/(\\s+)?\\w+\\(/) || stream.match(/(\\s+)?\\w+\\ \\(/)) {\n    stream.backUp(1);\n    return 'def';\n  }\n  if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  // Have we found a string?\n  if (ch == \"'\" || ch == '\"') {\n    // Store the type (single or double)\n    state.pending = ch;\n    // Perform the looping function to find the end\n    return tokenString(stream, state);\n  }\n  if (ch == '(' || ch == ')') {\n    return 'bracket';\n  }\n  if (ch.match(/[0-9]/)) {\n    return 'number';\n  }\n  stream.eatWhile(/[\\w-]/);\n  return null;\n}\nexport const cmake = {\n  name: \"cmake\",\n  startState: function () {\n    var state = {};\n    state.inDefinition = false;\n    state.inInclude = false;\n    state.continueString = false;\n    state.pending = false;\n    return state;\n  },\n  token: function (stream, state) {\n    if (stream.eatSpace()) return null;\n    return tokenize(stream, state);\n  }\n};\n\n"], "names": [], "mappings": ";;;AAAA,IAAI,iBAAiB;AAErB,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,IAAI,SAAS,MAAM,YAAY;IAC/B,MAAO,CAAC,OAAO,GAAG,MAAM,CAAC,UAAU,OAAO,IAAI,EAAE,KAAK,MAAM,OAAO,CAAE;QAClE,IAAI,YAAY,OAAO,QAAQ,QAAQ,MAAM,OAAO,IAAI,KAAK;YAC3D,YAAY;YACZ;QACF;QACA,OAAO;IACT;IACA,IAAI,WAAW;QACb,OAAO,MAAM,CAAC;IAChB;IACA,IAAI,WAAW,MAAM,OAAO,EAAE;QAC5B,MAAM,cAAc,GAAG;IACzB,OAAO;QACL,MAAM,cAAc,GAAG;IACzB;IACA,OAAO;AACT;AAEA,SAAS,SAAS,MAAM,EAAE,KAAK;IAC7B,IAAI,KAAK,OAAO,IAAI;IAEpB,4BAA4B;IAC5B,IAAI,OAAO,KAAK;QACd,IAAI,OAAO,KAAK,CAAC,iBAAiB;YAChC,OAAO;QACT;QACA,OAAO;IACT;IACA,sDAAsD;IACtD,IAAI,MAAM,cAAc,EAAE;QACxB,mCAAmC;QACnC,OAAO,MAAM,CAAC;QACd,OAAO,YAAY,QAAQ;IAC7B;IACA,2CAA2C;IAC3C,mFAAmF;IACnF,IAAI,OAAO,KAAK,CAAC,kBAAkB,OAAO,KAAK,CAAC,kBAAkB;QAChE,OAAO,MAAM,CAAC;QACd,OAAO;IACT;IACA,IAAI,MAAM,KAAK;QACb,OAAO,SAAS;QAChB,OAAO;IACT;IACA,0BAA0B;IAC1B,IAAI,MAAM,OAAO,MAAM,KAAK;QAC1B,oCAAoC;QACpC,MAAM,OAAO,GAAG;QAChB,+CAA+C;QAC/C,OAAO,YAAY,QAAQ;IAC7B;IACA,IAAI,MAAM,OAAO,MAAM,KAAK;QAC1B,OAAO;IACT;IACA,IAAI,GAAG,KAAK,CAAC,UAAU;QACrB,OAAO;IACT;IACA,OAAO,QAAQ,CAAC;IAChB,OAAO;AACT;AACO,MAAM,QAAQ;IACnB,MAAM;IACN,YAAY;QACV,IAAI,QAAQ,CAAC;QACb,MAAM,YAAY,GAAG;QACrB,MAAM,SAAS,GAAG;QAClB,MAAM,cAAc,GAAG;QACvB,MAAM,OAAO,GAAG;QAChB,OAAO;IACT;IACA,OAAO,SAAU,MAAM,EAAE,KAAK;QAC5B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,OAAO,SAAS,QAAQ;IAC1B;AACF", "ignoreList": [0], "debugId": null}}]}