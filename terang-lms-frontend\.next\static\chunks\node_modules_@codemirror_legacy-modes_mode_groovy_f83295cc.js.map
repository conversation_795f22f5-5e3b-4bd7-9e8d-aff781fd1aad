{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/groovy.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\nvar keywords = words(\n  \"abstract as assert boolean break byte case catch char class const continue def default \" +\n    \"do double else enum extends final finally float for goto if implements import in \" +\n    \"instanceof int interface long native new package private protected public return \" +\n    \"short static strictfp super switch synchronized threadsafe throw throws trait transient \" +\n    \"try void volatile while\");\nvar blockKeywords = words(\"catch class def do else enum finally for if interface switch trait try while\");\nvar standaloneKeywords = words(\"return break continue\");\nvar atoms = words(\"null true false this\");\n\nvar curPunc;\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"' || ch == \"'\") {\n    return startString(ch, stream, state);\n  }\n  if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    if (stream.eat(/eE/)) { stream.eat(/\\+\\-/); stream.eatWhile(/\\d/); }\n    return \"number\";\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize.push(tokenComment);\n      return tokenComment(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    if (expectExpression(state.lastToken, false)) {\n      return startString(ch, stream, state);\n    }\n  }\n  if (ch == \"-\" && stream.eat(\">\")) {\n    curPunc = \"->\";\n    return null;\n  }\n  if (/[+\\-*&%=<>!?|\\/~]/.test(ch)) {\n    stream.eatWhile(/[+\\-*&%=<>|~]/);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_]/);\n  if (ch == \"@\") { stream.eatWhile(/[\\w\\$_\\.]/); return \"meta\"; }\n  if (state.lastToken == \".\") return \"property\";\n  if (stream.eat(\":\")) { curPunc = \"proplabel\"; return \"property\"; }\n  var cur = stream.current();\n  if (atoms.propertyIsEnumerable(cur)) { return \"atom\"; }\n  if (keywords.propertyIsEnumerable(cur)) {\n    if (blockKeywords.propertyIsEnumerable(cur)) curPunc = \"newstatement\";\n    else if (standaloneKeywords.propertyIsEnumerable(cur)) curPunc = \"standalone\";\n    return \"keyword\";\n  }\n  return \"variable\";\n}\ntokenBase.isBase = true;\n\nfunction startString(quote, stream, state) {\n  var tripleQuoted = false;\n  if (quote != \"/\" && stream.eat(quote)) {\n    if (stream.eat(quote)) tripleQuoted = true;\n    else return \"string\";\n  }\n  function t(stream, state) {\n    var escaped = false, next, end = !tripleQuoted;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        if (!tripleQuoted) { break; }\n        if (stream.match(quote + quote)) { end = true; break; }\n      }\n      if (quote == '\"' && next == \"$\" && !escaped) {\n        if (stream.eat(\"{\")) {\n          state.tokenize.push(tokenBaseUntilBrace());\n          return \"string\";\n        } else if (stream.match(/^\\w/, false)) {\n          state.tokenize.push(tokenVariableDeref);\n          return \"string\";\n        }\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end) state.tokenize.pop();\n    return \"string\";\n  }\n  state.tokenize.push(t);\n  return t(stream, state);\n}\n\nfunction tokenBaseUntilBrace() {\n  var depth = 1;\n  function t(stream, state) {\n    if (stream.peek() == \"}\") {\n      depth--;\n      if (depth == 0) {\n        state.tokenize.pop();\n        return state.tokenize[state.tokenize.length-1](stream, state);\n      }\n    } else if (stream.peek() == \"{\") {\n      depth++;\n    }\n    return tokenBase(stream, state);\n  }\n  t.isBase = true;\n  return t;\n}\n\nfunction tokenVariableDeref(stream, state) {\n  var next = stream.match(/^(\\.|[\\w\\$_]+)/)\n  if (!next || !stream.match(next[0] == \".\" ? /^[\\w$_]/ : /^\\./)) state.tokenize.pop()\n  if (!next) return state.tokenize[state.tokenize.length-1](stream, state)\n  return next[0] == \".\" ? null : \"variable\"\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize.pop();\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction expectExpression(last, newline) {\n  return !last || last == \"operator\" || last == \"->\" || /[\\.\\[\\{\\(,;:]/.test(last) ||\n    last == \"newstatement\" || last == \"keyword\" || last == \"proplabel\" ||\n    (last == \"standalone\" && !newline);\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type) {\n  return state.context = new Context(state.indented, col, type, null, state.context);\n}\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n// Interface\n\nexport const groovy = {\n  name: \"groovy\",\n  startState: function(indentUnit) {\n    return {\n      tokenize: [tokenBase],\n      context: new Context(-indentUnit, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true,\n      lastToken: null\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n      // Automatic semicolon insertion\n      if (ctx.type == \"statement\" && !expectExpression(state.lastToken, true)) {\n        popContext(state); ctx = state.context;\n      }\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = state.tokenize[state.tokenize.length-1](stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\") && ctx.type == \"statement\") popContext(state);\n    // Handle indentation for {x -> \\n ... }\n    else if (curPunc == \"->\" && ctx.type == \"statement\" && ctx.prev.type == \"}\") {\n      popContext(state);\n      state.context.align = false;\n    }\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (ctx.type == \"}\" || ctx.type == \"top\" || (ctx.type == \"statement\" && curPunc == \"newstatement\"))\n      pushContext(state, stream.column(), \"statement\");\n    state.startOfLine = false;\n    state.lastToken = curPunc || style;\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (!state.tokenize[state.tokenize.length-1].isBase) return null;\n    var firstChar = textAfter && textAfter.charAt(0), ctx = state.context;\n    if (ctx.type == \"statement\" && !expectExpression(state.lastToken, true)) ctx = ctx.prev;\n    var closing = firstChar == ctx.type;\n    if (ctx.type == \"statement\") return ctx.indented + (firstChar == \"{\" ? 0 : cx.unit);\n    else if (ctx.align) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indented + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"'''\", '\"\"\"']}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AACA,IAAI,WAAW,MACb,4FACE,sFACA,sFACA,6FACA;AACJ,IAAI,gBAAgB,MAAM;AAC1B,IAAI,qBAAqB,MAAM;AAC/B,IAAI,QAAQ,MAAM;AAElB,IAAI;AACJ,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,OAAO,MAAM,KAAK;QAC1B,OAAO,YAAY,IAAI,QAAQ;IACjC;IACA,IAAI,qBAAqB,IAAI,CAAC,KAAK;QACjC,UAAU;QACV,OAAO;IACT;IACA,IAAI,KAAK,IAAI,CAAC,KAAK;QACjB,OAAO,QAAQ,CAAC;QAChB,IAAI,OAAO,GAAG,CAAC,OAAO;YAAE,OAAO,GAAG,CAAC;YAAS,OAAO,QAAQ,CAAC;QAAO;QACnE,OAAO;IACT;IACA,IAAI,MAAM,KAAK;QACb,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,MAAM,QAAQ,CAAC,IAAI,CAAC;YACpB,OAAO,aAAa,QAAQ;QAC9B;QACA,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO,SAAS;YAChB,OAAO;QACT;QACA,IAAI,iBAAiB,MAAM,SAAS,EAAE,QAAQ;YAC5C,OAAO,YAAY,IAAI,QAAQ;QACjC;IACF;IACA,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;QAChC,UAAU;QACV,OAAO;IACT;IACA,IAAI,oBAAoB,IAAI,CAAC,KAAK;QAChC,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,OAAO,QAAQ,CAAC;IAChB,IAAI,MAAM,KAAK;QAAE,OAAO,QAAQ,CAAC;QAAc,OAAO;IAAQ;IAC9D,IAAI,MAAM,SAAS,IAAI,KAAK,OAAO;IACnC,IAAI,OAAO,GAAG,CAAC,MAAM;QAAE,UAAU;QAAa,OAAO;IAAY;IACjE,IAAI,MAAM,OAAO,OAAO;IACxB,IAAI,MAAM,oBAAoB,CAAC,MAAM;QAAE,OAAO;IAAQ;IACtD,IAAI,SAAS,oBAAoB,CAAC,MAAM;QACtC,IAAI,cAAc,oBAAoB,CAAC,MAAM,UAAU;aAClD,IAAI,mBAAmB,oBAAoB,CAAC,MAAM,UAAU;QACjE,OAAO;IACT;IACA,OAAO;AACT;AACA,UAAU,MAAM,GAAG;AAEnB,SAAS,YAAY,KAAK,EAAE,MAAM,EAAE,KAAK;IACvC,IAAI,eAAe;IACnB,IAAI,SAAS,OAAO,OAAO,GAAG,CAAC,QAAQ;QACrC,IAAI,OAAO,GAAG,CAAC,QAAQ,eAAe;aACjC,OAAO;IACd;IACA,SAAS,EAAE,MAAM,EAAE,KAAK;QACtB,IAAI,UAAU,OAAO,MAAM,MAAM,CAAC;QAClC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,QAAQ,SAAS,CAAC,SAAS;gBAC7B,IAAI,CAAC,cAAc;oBAAE;gBAAO;gBAC5B,IAAI,OAAO,KAAK,CAAC,QAAQ,QAAQ;oBAAE,MAAM;oBAAM;gBAAO;YACxD;YACA,IAAI,SAAS,OAAO,QAAQ,OAAO,CAAC,SAAS;gBAC3C,IAAI,OAAO,GAAG,CAAC,MAAM;oBACnB,MAAM,QAAQ,CAAC,IAAI,CAAC;oBACpB,OAAO;gBACT,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO,QAAQ;oBACrC,MAAM,QAAQ,CAAC,IAAI,CAAC;oBACpB,OAAO;gBACT;YACF;YACA,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,KAAK,MAAM,QAAQ,CAAC,GAAG;QAC3B,OAAO;IACT;IACA,MAAM,QAAQ,CAAC,IAAI,CAAC;IACpB,OAAO,EAAE,QAAQ;AACnB;AAEA,SAAS;IACP,IAAI,QAAQ;IACZ,SAAS,EAAE,MAAM,EAAE,KAAK;QACtB,IAAI,OAAO,IAAI,MAAM,KAAK;YACxB;YACA,IAAI,SAAS,GAAG;gBACd,MAAM,QAAQ,CAAC,GAAG;gBAClB,OAAO,MAAM,QAAQ,CAAC,MAAM,QAAQ,CAAC,MAAM,GAAC,EAAE,CAAC,QAAQ;YACzD;QACF,OAAO,IAAI,OAAO,IAAI,MAAM,KAAK;YAC/B;QACF;QACA,OAAO,UAAU,QAAQ;IAC3B;IACA,EAAE,MAAM,GAAG;IACX,OAAO;AACT;AAEA,SAAS,mBAAmB,MAAM,EAAE,KAAK;IACvC,IAAI,OAAO,OAAO,KAAK,CAAC;IACxB,IAAI,CAAC,QAAQ,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,YAAY,QAAQ,MAAM,QAAQ,CAAC,GAAG;IAClF,IAAI,CAAC,MAAM,OAAO,MAAM,QAAQ,CAAC,MAAM,QAAQ,CAAC,MAAM,GAAC,EAAE,CAAC,QAAQ;IAClE,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,OAAO;AACjC;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,WAAW,OAAO;IACtB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,CAAC,GAAG;YAClB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,SAAS,iBAAiB,IAAI,EAAE,OAAO;IACrC,OAAO,CAAC,QAAQ,QAAQ,cAAc,QAAQ,QAAQ,gBAAgB,IAAI,CAAC,SACzE,QAAQ,kBAAkB,QAAQ,aAAa,QAAQ,eACtD,QAAQ,gBAAgB,CAAC;AAC9B;AAEA,SAAS,QAAQ,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;IAClD,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,IAAI,GAAG;AACd;AACA,SAAS,YAAY,KAAK,EAAE,GAAG,EAAE,IAAI;IACnC,OAAO,MAAM,OAAO,GAAG,IAAI,QAAQ,MAAM,QAAQ,EAAE,KAAK,MAAM,MAAM,MAAM,OAAO;AACnF;AACA,SAAS,WAAW,KAAK;IACvB,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI;IAC1B,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAC/B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ;IACzC,OAAO,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;AAC3C;AAIO,MAAM,SAAS;IACpB,MAAM;IACN,YAAY,SAAS,UAAU;QAC7B,OAAO;YACL,UAAU;gBAAC;aAAU;YACrB,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,OAAO;YAC5C,UAAU;YACV,aAAa;YACb,WAAW;QACb;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,MAAM,MAAM,OAAO;QACvB,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;YACnC,MAAM,QAAQ,GAAG,OAAO,WAAW;YACnC,MAAM,WAAW,GAAG;YACpB,gCAAgC;YAChC,IAAI,IAAI,IAAI,IAAI,eAAe,CAAC,iBAAiB,MAAM,SAAS,EAAE,OAAO;gBACvE,WAAW;gBAAQ,MAAM,MAAM,OAAO;YACxC;QACF;QACA,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,UAAU;QACV,IAAI,QAAQ,MAAM,QAAQ,CAAC,MAAM,QAAQ,CAAC,MAAM,GAAC,EAAE,CAAC,QAAQ;QAC5D,IAAI,SAAS,WAAW,OAAO;QAC/B,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;QAEnC,IAAI,CAAC,WAAW,OAAO,WAAW,GAAG,KAAK,IAAI,IAAI,IAAI,aAAa,WAAW;aAEzE,IAAI,WAAW,QAAQ,IAAI,IAAI,IAAI,eAAe,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK;YAC3E,WAAW;YACX,MAAM,OAAO,CAAC,KAAK,GAAG;QACxB,OACK,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK;YACvB,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;YACjD,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM,WAAW;YACtC,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;QACnD,OACK,IAAI,WAAW,IAAI,IAAI,EAAE,WAAW;aACpC,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,SAAU,IAAI,IAAI,IAAI,eAAe,WAAW,gBACtF,YAAY,OAAO,OAAO,MAAM,IAAI;QACtC,MAAM,WAAW,GAAG;QACpB,MAAM,SAAS,GAAG,WAAW;QAC7B,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,QAAQ,CAAC,MAAM,GAAC,EAAE,CAAC,MAAM,EAAE,OAAO;QAC5D,IAAI,YAAY,aAAa,UAAU,MAAM,CAAC,IAAI,MAAM,MAAM,OAAO;QACrE,IAAI,IAAI,IAAI,IAAI,eAAe,CAAC,iBAAiB,MAAM,SAAS,EAAE,OAAO,MAAM,IAAI,IAAI;QACvF,IAAI,UAAU,aAAa,IAAI,IAAI;QACnC,IAAI,IAAI,IAAI,IAAI,aAAa,OAAO,IAAI,QAAQ,GAAG,CAAC,aAAa,MAAM,IAAI,GAAG,IAAI;aAC7E,IAAI,IAAI,KAAK,EAAE,OAAO,IAAI,MAAM,GAAG,CAAC,UAAU,IAAI,CAAC;aACnD,OAAO,IAAI,QAAQ,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI;IACnD;IAEA,cAAc;QACZ,eAAe;QACf,eAAe;YAAC,MAAM;YAAM,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;QAC5D,eAAe;YAAC,UAAU;gBAAC;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAO;aAAM;QAAA;IACnE;AACF", "ignoreList": [0], "debugId": null}}]}