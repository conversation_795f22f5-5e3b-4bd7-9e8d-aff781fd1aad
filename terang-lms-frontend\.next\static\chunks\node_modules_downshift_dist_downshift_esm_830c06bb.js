(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/downshift/dist/downshift.esm.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Downshift$1),
    "resetIdCounter": (()=>resetIdCounter),
    "useCombobox": (()=>useCombobox),
    "useMultipleSelection": (()=>useMultipleSelection),
    "useSelect": (()=>useSelect)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$assertThisInitialized$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$inheritsLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prop-types/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$downshift$2f$node_modules$2f$react$2d$is$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/downshift/node_modules/react-is/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$downshift$2f$node_modules$2f$compute$2d$scroll$2d$into$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/downshift/node_modules/compute-scroll-into-view/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tslib/tslib.es6.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
var idCounter = 0;
/**
 * Accepts a parameter and returns it if it's a function
 * or a noop function if it's not. This allows us to
 * accept a callback, but not worry about it if it's not
 * passed.
 * @param {Function} cb the callback
 * @return {Function} a function
 */ function cbToCb(cb) {
    return typeof cb === 'function' ? cb : noop;
}
function noop() {}
/**
 * Scroll node into view if necessary
 * @param {HTMLElement} node the element that should scroll into view
 * @param {HTMLElement} menuNode the menu element of the component
 */ function scrollIntoView(node, menuNode) {
    if (!node) {
        return;
    }
    var actions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$downshift$2f$node_modules$2f$compute$2d$scroll$2d$into$2d$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(node, {
        boundary: menuNode,
        block: 'nearest',
        scrollMode: 'if-needed'
    });
    actions.forEach(function(_ref) {
        var el = _ref.el, top = _ref.top, left = _ref.left;
        el.scrollTop = top;
        el.scrollLeft = left;
    });
}
/**
 * @param {HTMLElement} parent the parent node
 * @param {HTMLElement} child the child node
 * @param {Window} environment The window context where downshift renders.
 * @return {Boolean} whether the parent is the child or the child is in the parent
 */ function isOrContainsNode(parent, child, environment) {
    var result = parent === child || child instanceof environment.Node && parent.contains && parent.contains(child);
    return result;
}
/**
 * Simple debounce implementation. Will call the given
 * function once after the time given has passed since
 * it was last called.
 * @param {Function} fn the function to call after the time
 * @param {Number} time the time to wait
 * @return {Function} the debounced function
 */ function debounce(fn, time) {
    var timeoutId;
    function cancel() {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
    }
    function wrapper() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        cancel();
        timeoutId = setTimeout(function() {
            timeoutId = null;
            fn.apply(void 0, args);
        }, time);
    }
    wrapper.cancel = cancel;
    return wrapper;
}
/**
 * This is intended to be used to compose event handlers.
 * They are executed in order until one of them sets
 * `event.preventDownshiftDefault = true`.
 * @param {...Function} fns the event handler functions
 * @return {Function} the event handler to add to an element
 */ function callAllEventHandlers() {
    for(var _len2 = arguments.length, fns = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){
        fns[_key2] = arguments[_key2];
    }
    return function(event) {
        for(var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++){
            args[_key3 - 1] = arguments[_key3];
        }
        return fns.some(function(fn) {
            if (fn) {
                fn.apply(void 0, [
                    event
                ].concat(args));
            }
            return event.preventDownshiftDefault || event.hasOwnProperty('nativeEvent') && event.nativeEvent.preventDownshiftDefault;
        });
    };
}
function handleRefs() {
    for(var _len4 = arguments.length, refs = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++){
        refs[_key4] = arguments[_key4];
    }
    return function(node) {
        refs.forEach(function(ref) {
            if (typeof ref === 'function') {
                ref(node);
            } else if (ref) {
                ref.current = node;
            }
        });
    };
}
/**
 * This generates a unique ID for an instance of Downshift
 * @return {String} the unique ID
 */ function generateId() {
    return String(idCounter++);
}
/**
 * Resets idCounter to 0. Used for SSR.
 */ function resetIdCounter() {
    idCounter = 0;
}
/**
 * Default implementation for status message. Only added when menu is open.
 * Will specify if there are results in the list, and if so, how many,
 * and what keys are relevant.
 *
 * @param {Object} param the downshift state and other relevant properties
 * @return {String} the a11y status message
 */ function getA11yStatusMessage$1(_ref2) {
    var isOpen = _ref2.isOpen, resultCount = _ref2.resultCount, previousResultCount = _ref2.previousResultCount;
    if (!isOpen) {
        return '';
    }
    if (!resultCount) {
        return 'No results are available.';
    }
    if (resultCount !== previousResultCount) {
        return resultCount + " result" + (resultCount === 1 ? ' is' : 's are') + " available, use up and down arrow keys to navigate. Press Enter key to select.";
    }
    return '';
}
/**
 * Takes an argument and if it's an array, returns the first item in the array
 * otherwise returns the argument
 * @param {*} arg the maybe-array
 * @param {*} defaultValue the value if arg is falsey not defined
 * @return {*} the arg or it's first item
 */ function unwrapArray(arg, defaultValue) {
    arg = Array.isArray(arg) ? /* istanbul ignore next (preact) */ arg[0] : arg;
    if (!arg && defaultValue) {
        return defaultValue;
    } else {
        return arg;
    }
}
/**
 * @param {Object} element (P)react element
 * @return {Boolean} whether it's a DOM element
 */ function isDOMElement(element) {
    // then we assume this is react
    return typeof element.type === 'string';
}
/**
 * @param {Object} element (P)react element
 * @return {Object} the props
 */ function getElementProps(element) {
    return element.props;
}
/**
 * Throws a helpful error message for required properties. Useful
 * to be used as a default in destructuring or object params.
 * @param {String} fnName the function name
 * @param {String} propName the prop name
 */ function requiredProp(fnName, propName) {
    // eslint-disable-next-line no-console
    console.error("The property \"" + propName + "\" is required in \"" + fnName + "\"");
}
var stateKeys = [
    'highlightedIndex',
    'inputValue',
    'isOpen',
    'selectedItem',
    'type'
];
/**
 * @param {Object} state the state object
 * @return {Object} state that is relevant to downshift
 */ function pickState(state) {
    if (state === void 0) {
        state = {};
    }
    var result = {};
    stateKeys.forEach(function(k) {
        if (state.hasOwnProperty(k)) {
            result[k] = state[k];
        }
    });
    return result;
}
/**
 * This will perform a shallow merge of the given state object
 * with the state coming from props
 * (for the controlled component scenario)
 * This is used in state updater functions so they're referencing
 * the right state regardless of where it comes from.
 *
 * @param {Object} state The state of the component/hook.
 * @param {Object} props The props that may contain controlled values.
 * @returns {Object} The merged controlled state.
 */ function getState(state, props) {
    return Object.keys(state).reduce(function(prevState, key) {
        prevState[key] = isControlledProp(props, key) ? props[key] : state[key];
        return prevState;
    }, {});
}
/**
 * This determines whether a prop is a "controlled prop" meaning it is
 * state which is controlled by the outside of this component rather
 * than within this component.
 *
 * @param {Object} props The props that may contain controlled values.
 * @param {String} key the key to check
 * @return {Boolean} whether it is a controlled controlled prop
 */ function isControlledProp(props, key) {
    return props[key] !== undefined;
}
/**
 * Normalizes the 'key' property of a KeyboardEvent in IE/Edge
 * @param {Object} event a keyboardEvent object
 * @return {String} keyboard key
 */ function normalizeArrowKey(event) {
    var key = event.key, keyCode = event.keyCode;
    /* istanbul ignore next (ie) */ if (keyCode >= 37 && keyCode <= 40 && key.indexOf('Arrow') !== 0) {
        return "Arrow" + key;
    }
    return key;
}
/**
 * Simple check if the value passed is object literal
 * @param {*} obj any things
 * @return {Boolean} whether it's object literal
 */ function isPlainObject(obj) {
    return Object.prototype.toString.call(obj) === '[object Object]';
}
/**
 * Returns the new index in the list, in a circular way. If next value is out of bonds from the total,
 * it will wrap to either 0 or itemCount - 1.
 *
 * @param {number} moveAmount Number of positions to move. Negative to move backwards, positive forwards.
 * @param {number} baseIndex The initial position to move from.
 * @param {number} itemCount The total number of items.
 * @param {Function} getItemNodeFromIndex Used to check if item is disabled.
 * @param {boolean} circular Specify if navigation is circular. Default is true.
 * @returns {number} The new index after the move.
 */ function getNextWrappingIndex(moveAmount, baseIndex, itemCount, getItemNodeFromIndex, circular) {
    if (circular === void 0) {
        circular = true;
    }
    if (itemCount === 0) {
        return -1;
    }
    var itemsLastIndex = itemCount - 1;
    if (typeof baseIndex !== 'number' || baseIndex < 0 || baseIndex >= itemCount) {
        baseIndex = moveAmount > 0 ? -1 : itemsLastIndex + 1;
    }
    var newIndex = baseIndex + moveAmount;
    if (newIndex < 0) {
        newIndex = circular ? itemsLastIndex : 0;
    } else if (newIndex > itemsLastIndex) {
        newIndex = circular ? 0 : itemsLastIndex;
    }
    var nonDisabledNewIndex = getNextNonDisabledIndex(moveAmount, newIndex, itemCount, getItemNodeFromIndex, circular);
    if (nonDisabledNewIndex === -1) {
        return baseIndex >= itemCount ? -1 : baseIndex;
    }
    return nonDisabledNewIndex;
}
/**
 * Returns the next index in the list of an item that is not disabled.
 *
 * @param {number} moveAmount Number of positions to move. Negative to move backwards, positive forwards.
 * @param {number} baseIndex The initial position to move from.
 * @param {number} itemCount The total number of items.
 * @param {Function} getItemNodeFromIndex Used to check if item is disabled.
 * @param {boolean} circular Specify if navigation is circular. Default is true.
 * @returns {number} The new index. Returns baseIndex if item is not disabled. Returns next non-disabled item otherwise. If no non-disabled found it will return -1.
 */ function getNextNonDisabledIndex(moveAmount, baseIndex, itemCount, getItemNodeFromIndex, circular) {
    var currentElementNode = getItemNodeFromIndex(baseIndex);
    if (!currentElementNode || !currentElementNode.hasAttribute('disabled')) {
        return baseIndex;
    }
    if (moveAmount > 0) {
        for(var index = baseIndex + 1; index < itemCount; index++){
            if (!getItemNodeFromIndex(index).hasAttribute('disabled')) {
                return index;
            }
        }
    } else {
        for(var _index = baseIndex - 1; _index >= 0; _index--){
            if (!getItemNodeFromIndex(_index).hasAttribute('disabled')) {
                return _index;
            }
        }
    }
    if (circular) {
        return moveAmount > 0 ? getNextNonDisabledIndex(1, 0, itemCount, getItemNodeFromIndex, false) : getNextNonDisabledIndex(-1, itemCount - 1, itemCount, getItemNodeFromIndex, false);
    }
    return -1;
}
/**
 * Checks if event target is within the downshift elements.
 *
 * @param {EventTarget} target Target to check.
 * @param {HTMLElement[]} downshiftElements The elements that form downshift (list, toggle button etc).
 * @param {Window} environment The window context where downshift renders.
 * @param {boolean} checkActiveElement Whether to also check activeElement.
 *
 * @returns {boolean} Whether or not the target is within downshift elements.
 */ function targetWithinDownshift(target, downshiftElements, environment, checkActiveElement) {
    if (checkActiveElement === void 0) {
        checkActiveElement = true;
    }
    return downshiftElements.some(function(contextNode) {
        return contextNode && (isOrContainsNode(contextNode, target, environment) || checkActiveElement && isOrContainsNode(contextNode, environment.document.activeElement, environment));
    });
}
// eslint-disable-next-line import/no-mutable-exports
var validateControlledUnchanged = noop;
/* istanbul ignore next */ if ("TURBOPACK compile-time truthy", 1) {
    validateControlledUnchanged = function validateControlledUnchanged(state, prevProps, nextProps) {
        var warningDescription = "This prop should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled Downshift element for the lifetime of the component. More info: https://github.com/downshift-js/downshift#control-props";
        Object.keys(state).forEach(function(propKey) {
            if (prevProps[propKey] !== undefined && nextProps[propKey] === undefined) {
                // eslint-disable-next-line no-console
                console.error("downshift: A component has changed the controlled prop \"" + propKey + "\" to be uncontrolled. " + warningDescription);
            } else if (prevProps[propKey] === undefined && nextProps[propKey] !== undefined) {
                // eslint-disable-next-line no-console
                console.error("downshift: A component has changed the uncontrolled prop \"" + propKey + "\" to be controlled. " + warningDescription);
            }
        });
    };
}
var cleanupStatus = debounce(function(documentProp) {
    getStatusDiv(documentProp).textContent = '';
}, 500);
/**
 * @param {String} status the status message
 * @param {Object} documentProp document passed by the user.
 */ function setStatus(status, documentProp) {
    var div = getStatusDiv(documentProp);
    if (!status) {
        return;
    }
    div.textContent = status;
    cleanupStatus(documentProp);
}
/**
 * Get the status node or create it if it does not already exist.
 * @param {Object} documentProp document passed by the user.
 * @return {HTMLElement} the status node.
 */ function getStatusDiv(documentProp) {
    if (documentProp === void 0) {
        documentProp = document;
    }
    var statusDiv = documentProp.getElementById('a11y-status-message');
    if (statusDiv) {
        return statusDiv;
    }
    statusDiv = documentProp.createElement('div');
    statusDiv.setAttribute('id', 'a11y-status-message');
    statusDiv.setAttribute('role', 'status');
    statusDiv.setAttribute('aria-live', 'polite');
    statusDiv.setAttribute('aria-relevant', 'additions text');
    Object.assign(statusDiv.style, {
        border: '0',
        clip: 'rect(0 0 0 0)',
        height: '1px',
        margin: '-1px',
        overflow: 'hidden',
        padding: '0',
        position: 'absolute',
        width: '1px'
    });
    documentProp.body.appendChild(statusDiv);
    return statusDiv;
}
var unknown = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_unknown__' : ("TURBOPACK unreachable", undefined);
var mouseUp = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_mouseup__' : ("TURBOPACK unreachable", undefined);
var itemMouseEnter = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_item_mouseenter__' : ("TURBOPACK unreachable", undefined);
var keyDownArrowUp = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_keydown_arrow_up__' : ("TURBOPACK unreachable", undefined);
var keyDownArrowDown = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_keydown_arrow_down__' : ("TURBOPACK unreachable", undefined);
var keyDownEscape = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_keydown_escape__' : ("TURBOPACK unreachable", undefined);
var keyDownEnter = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_keydown_enter__' : ("TURBOPACK unreachable", undefined);
var keyDownHome = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_keydown_home__' : ("TURBOPACK unreachable", undefined);
var keyDownEnd = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_keydown_end__' : ("TURBOPACK unreachable", undefined);
var clickItem = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_click_item__' : ("TURBOPACK unreachable", undefined);
var blurInput = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_blur_input__' : ("TURBOPACK unreachable", undefined);
var changeInput = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_change_input__' : ("TURBOPACK unreachable", undefined);
var keyDownSpaceButton = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_keydown_space_button__' : ("TURBOPACK unreachable", undefined);
var clickButton = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_click_button__' : ("TURBOPACK unreachable", undefined);
var blurButton = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_blur_button__' : ("TURBOPACK unreachable", undefined);
var controlledPropUpdatedSelectedItem = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_controlled_prop_updated_selected_item__' : ("TURBOPACK unreachable", undefined);
var touchEnd = ("TURBOPACK compile-time truthy", 1) ? '__autocomplete_touchend__' : ("TURBOPACK unreachable", undefined);
var stateChangeTypes$3 = /*#__PURE__*/ Object.freeze({
    __proto__: null,
    unknown: unknown,
    mouseUp: mouseUp,
    itemMouseEnter: itemMouseEnter,
    keyDownArrowUp: keyDownArrowUp,
    keyDownArrowDown: keyDownArrowDown,
    keyDownEscape: keyDownEscape,
    keyDownEnter: keyDownEnter,
    keyDownHome: keyDownHome,
    keyDownEnd: keyDownEnd,
    clickItem: clickItem,
    blurInput: blurInput,
    changeInput: changeInput,
    keyDownSpaceButton: keyDownSpaceButton,
    clickButton: clickButton,
    blurButton: blurButton,
    controlledPropUpdatedSelectedItem: controlledPropUpdatedSelectedItem,
    touchEnd: touchEnd
});
var _excluded$4 = [
    "refKey",
    "ref"
], _excluded2$3 = [
    "onClick",
    "onPress",
    "onKeyDown",
    "onKeyUp",
    "onBlur"
], _excluded3$2 = [
    "onKeyDown",
    "onBlur",
    "onChange",
    "onInput",
    "onChangeText"
], _excluded4$1 = [
    "refKey",
    "ref"
], _excluded5 = [
    "onMouseMove",
    "onMouseDown",
    "onClick",
    "onPress",
    "index",
    "item"
];
var Downshift = /*#__PURE__*/ function() {
    var Downshift = /*#__PURE__*/ function(_Component) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$inheritsLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Downshift, _Component);
        function Downshift(_props) {
            var _this;
            _this = _Component.call(this, _props) || this;
            // fancy destructuring + defaults + aliases
            // this basically says each value of state should either be set to
            // the initial value or the default value if the initial value is not provided
            _this.id = _this.props.id || "downshift-" + generateId();
            _this.menuId = _this.props.menuId || _this.id + "-menu";
            _this.labelId = _this.props.labelId || _this.id + "-label";
            _this.inputId = _this.props.inputId || _this.id + "-input";
            _this.getItemId = _this.props.getItemId || function(index) {
                return _this.id + "-item-" + index;
            };
            _this.input = null;
            _this.items = [];
            // itemCount can be changed asynchronously
            // from within downshift (so it can't come from a prop)
            // this is why we store it as an instance and use
            // getItemCount rather than just use items.length
            // (to support windowing + async)
            _this.itemCount = null;
            _this.previousResultCount = 0;
            _this.timeoutIds = [];
            /**
       * @param {Function} fn the function to call after the time
       * @param {Number} time the time to wait
       */ _this.internalSetTimeout = function(fn, time) {
                var id = setTimeout(function() {
                    _this.timeoutIds = _this.timeoutIds.filter(function(i) {
                        return i !== id;
                    });
                    fn();
                }, time);
                _this.timeoutIds.push(id);
            };
            _this.setItemCount = function(count) {
                _this.itemCount = count;
            };
            _this.unsetItemCount = function() {
                _this.itemCount = null;
            };
            _this.setHighlightedIndex = function(highlightedIndex, otherStateToSet) {
                if (highlightedIndex === void 0) {
                    highlightedIndex = _this.props.defaultHighlightedIndex;
                }
                if (otherStateToSet === void 0) {
                    otherStateToSet = {};
                }
                otherStateToSet = pickState(otherStateToSet);
                _this.internalSetState((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                    highlightedIndex: highlightedIndex
                }, otherStateToSet));
            };
            _this.clearSelection = function(cb) {
                _this.internalSetState({
                    selectedItem: null,
                    inputValue: '',
                    highlightedIndex: _this.props.defaultHighlightedIndex,
                    isOpen: _this.props.defaultIsOpen
                }, cb);
            };
            _this.selectItem = function(item, otherStateToSet, cb) {
                otherStateToSet = pickState(otherStateToSet);
                _this.internalSetState((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                    isOpen: _this.props.defaultIsOpen,
                    highlightedIndex: _this.props.defaultHighlightedIndex,
                    selectedItem: item,
                    inputValue: _this.props.itemToString(item)
                }, otherStateToSet), cb);
            };
            _this.selectItemAtIndex = function(itemIndex, otherStateToSet, cb) {
                var item = _this.items[itemIndex];
                if (item == null) {
                    return;
                }
                _this.selectItem(item, otherStateToSet, cb);
            };
            _this.selectHighlightedItem = function(otherStateToSet, cb) {
                return _this.selectItemAtIndex(_this.getState().highlightedIndex, otherStateToSet, cb);
            };
            // any piece of our state can live in two places:
            // 1. Uncontrolled: it's internal (this.state)
            //    We will call this.setState to update that state
            // 2. Controlled: it's external (this.props)
            //    We will call this.props.onStateChange to update that state
            //
            // In addition, we'll call this.props.onChange if the
            // selectedItem is changed.
            _this.internalSetState = function(stateToSet, cb) {
                var isItemSelected, onChangeArg;
                var onStateChangeArg = {};
                var isStateToSetFunction = typeof stateToSet === 'function';
                // we want to call `onInputValueChange` before the `setState` call
                // so someone controlling the `inputValue` state gets notified of
                // the input change as soon as possible. This avoids issues with
                // preserving the cursor position.
                // See https://github.com/downshift-js/downshift/issues/217 for more info.
                if (!isStateToSetFunction && stateToSet.hasOwnProperty('inputValue')) {
                    _this.props.onInputValueChange(stateToSet.inputValue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, _this.getStateAndHelpers(), stateToSet));
                }
                return _this.setState(function(state) {
                    state = _this.getState(state);
                    var newStateToSet = isStateToSetFunction ? stateToSet(state) : stateToSet;
                    // Your own function that could modify the state that will be set.
                    newStateToSet = _this.props.stateReducer(state, newStateToSet);
                    // checks if an item is selected, regardless of if it's different from
                    // what was selected before
                    // used to determine if onSelect and onChange callbacks should be called
                    isItemSelected = newStateToSet.hasOwnProperty('selectedItem');
                    // this keeps track of the object we want to call with setState
                    var nextState = {};
                    // we need to call on change if the outside world is controlling any of our state
                    // and we're trying to update that state. OR if the selection has changed and we're
                    // trying to update the selection
                    if (isItemSelected && newStateToSet.selectedItem !== state.selectedItem) {
                        onChangeArg = newStateToSet.selectedItem;
                    }
                    newStateToSet.type = newStateToSet.type || unknown;
                    Object.keys(newStateToSet).forEach(function(key) {
                        // onStateChangeArg should only have the state that is
                        // actually changing
                        if (state[key] !== newStateToSet[key]) {
                            onStateChangeArg[key] = newStateToSet[key];
                        }
                        // the type is useful for the onStateChangeArg
                        // but we don't actually want to set it in internal state.
                        // this is an undocumented feature for now... Not all internalSetState
                        // calls support it and I'm not certain we want them to yet.
                        // But it enables users controlling the isOpen state to know when
                        // the isOpen state changes due to mouseup events which is quite handy.
                        if (key === 'type') {
                            return;
                        }
                        newStateToSet[key];
                        // if it's coming from props, then we don't care to set it internally
                        if (!isControlledProp(_this.props, key)) {
                            nextState[key] = newStateToSet[key];
                        }
                    });
                    // if stateToSet is a function, then we weren't able to call onInputValueChange
                    // earlier, so we'll call it now that we know what the inputValue state will be.
                    if (isStateToSetFunction && newStateToSet.hasOwnProperty('inputValue')) {
                        _this.props.onInputValueChange(newStateToSet.inputValue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, _this.getStateAndHelpers(), newStateToSet));
                    }
                    return nextState;
                }, function() {
                    // call the provided callback if it's a function
                    cbToCb(cb)();
                    // only call the onStateChange and onChange callbacks if
                    // we have relevant information to pass them.
                    var hasMoreStateThanType = Object.keys(onStateChangeArg).length > 1;
                    if (hasMoreStateThanType) {
                        _this.props.onStateChange(onStateChangeArg, _this.getStateAndHelpers());
                    }
                    if (isItemSelected) {
                        _this.props.onSelect(stateToSet.selectedItem, _this.getStateAndHelpers());
                    }
                    if (onChangeArg !== undefined) {
                        _this.props.onChange(onChangeArg, _this.getStateAndHelpers());
                    }
                    // this is currently undocumented and therefore subject to change
                    // We'll try to not break it, but just be warned.
                    _this.props.onUserAction(onStateChangeArg, _this.getStateAndHelpers());
                });
            };
            //////////////////////////// ROOT
            _this.rootRef = function(node) {
                return _this._rootNode = node;
            };
            _this.getRootProps = function(_temp, _temp2) {
                var _extends2;
                var _ref = _temp === void 0 ? {} : _temp, _ref$refKey = _ref.refKey, refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey, ref = _ref.ref, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref, _excluded$4);
                var _ref2 = _temp2 === void 0 ? {} : _temp2, _ref2$suppressRefErro = _ref2.suppressRefError, suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;
                // this is used in the render to know whether the user has called getRootProps.
                // It uses that to know whether to apply the props automatically
                _this.getRootProps.called = true;
                _this.getRootProps.refKey = refKey;
                _this.getRootProps.suppressRefError = suppressRefError;
                var _this$getState = _this.getState(), isOpen = _this$getState.isOpen;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((_extends2 = {}, _extends2[refKey] = handleRefs(ref, _this.rootRef), _extends2.role = 'combobox', _extends2['aria-expanded'] = isOpen, _extends2['aria-haspopup'] = 'listbox', _extends2['aria-owns'] = isOpen ? _this.menuId : null, _extends2['aria-labelledby'] = _this.labelId, _extends2), rest);
            };
            //\\\\\\\\\\\\\\\\\\\\\\\\\\ ROOT
            _this.keyDownHandlers = {
                ArrowDown: function ArrowDown(event) {
                    var _this2 = this;
                    event.preventDefault();
                    if (this.getState().isOpen) {
                        var amount = event.shiftKey ? 5 : 1;
                        this.moveHighlightedIndex(amount, {
                            type: keyDownArrowDown
                        });
                    } else {
                        this.internalSetState({
                            isOpen: true,
                            type: keyDownArrowDown
                        }, function() {
                            var itemCount = _this2.getItemCount();
                            if (itemCount > 0) {
                                var _this2$getState = _this2.getState(), highlightedIndex = _this2$getState.highlightedIndex;
                                var nextHighlightedIndex = getNextWrappingIndex(1, highlightedIndex, itemCount, function(index) {
                                    return _this2.getItemNodeFromIndex(index);
                                });
                                _this2.setHighlightedIndex(nextHighlightedIndex, {
                                    type: keyDownArrowDown
                                });
                            }
                        });
                    }
                },
                ArrowUp: function ArrowUp(event) {
                    var _this3 = this;
                    event.preventDefault();
                    if (this.getState().isOpen) {
                        var amount = event.shiftKey ? -5 : -1;
                        this.moveHighlightedIndex(amount, {
                            type: keyDownArrowUp
                        });
                    } else {
                        this.internalSetState({
                            isOpen: true,
                            type: keyDownArrowUp
                        }, function() {
                            var itemCount = _this3.getItemCount();
                            if (itemCount > 0) {
                                var _this3$getState = _this3.getState(), highlightedIndex = _this3$getState.highlightedIndex;
                                var nextHighlightedIndex = getNextWrappingIndex(-1, highlightedIndex, itemCount, function(index) {
                                    return _this3.getItemNodeFromIndex(index);
                                });
                                _this3.setHighlightedIndex(nextHighlightedIndex, {
                                    type: keyDownArrowUp
                                });
                            }
                        });
                    }
                },
                Enter: function Enter(event) {
                    if (event.which === 229) {
                        return;
                    }
                    var _this$getState2 = this.getState(), isOpen = _this$getState2.isOpen, highlightedIndex = _this$getState2.highlightedIndex;
                    if (isOpen && highlightedIndex != null) {
                        event.preventDefault();
                        var item = this.items[highlightedIndex];
                        var itemNode = this.getItemNodeFromIndex(highlightedIndex);
                        if (item == null || itemNode && itemNode.hasAttribute('disabled')) {
                            return;
                        }
                        this.selectHighlightedItem({
                            type: keyDownEnter
                        });
                    }
                },
                Escape: function Escape(event) {
                    event.preventDefault();
                    this.reset((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                        type: keyDownEscape
                    }, !this.state.isOpen && {
                        selectedItem: null,
                        inputValue: ''
                    }));
                }
            };
            //////////////////////////// BUTTON
            _this.buttonKeyDownHandlers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, _this.keyDownHandlers, {
                ' ': function _(event) {
                    event.preventDefault();
                    this.toggleMenu({
                        type: keyDownSpaceButton
                    });
                }
            });
            _this.inputKeyDownHandlers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, _this.keyDownHandlers, {
                Home: function Home(event) {
                    var _this4 = this;
                    var _this$getState3 = this.getState(), isOpen = _this$getState3.isOpen;
                    if (!isOpen) {
                        return;
                    }
                    event.preventDefault();
                    var itemCount = this.getItemCount();
                    if (itemCount <= 0 || !isOpen) {
                        return;
                    }
                    // get next non-disabled starting downwards from 0 if that's disabled.
                    var newHighlightedIndex = getNextNonDisabledIndex(1, 0, itemCount, function(index) {
                        return _this4.getItemNodeFromIndex(index);
                    }, false);
                    this.setHighlightedIndex(newHighlightedIndex, {
                        type: keyDownHome
                    });
                },
                End: function End(event) {
                    var _this5 = this;
                    var _this$getState4 = this.getState(), isOpen = _this$getState4.isOpen;
                    if (!isOpen) {
                        return;
                    }
                    event.preventDefault();
                    var itemCount = this.getItemCount();
                    if (itemCount <= 0 || !isOpen) {
                        return;
                    }
                    // get next non-disabled starting upwards from last index if that's disabled.
                    var newHighlightedIndex = getNextNonDisabledIndex(-1, itemCount - 1, itemCount, function(index) {
                        return _this5.getItemNodeFromIndex(index);
                    }, false);
                    this.setHighlightedIndex(newHighlightedIndex, {
                        type: keyDownEnd
                    });
                }
            });
            _this.getToggleButtonProps = function(_temp3) {
                var _ref3 = _temp3 === void 0 ? {} : _temp3, onClick = _ref3.onClick;
                _ref3.onPress;
                var onKeyDown = _ref3.onKeyDown, onKeyUp = _ref3.onKeyUp, onBlur = _ref3.onBlur, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref3, _excluded2$3);
                var _this$getState5 = _this.getState(), isOpen = _this$getState5.isOpen;
                var enabledEventHandlers = {
                    onClick: callAllEventHandlers(onClick, _this.buttonHandleClick),
                    onKeyDown: callAllEventHandlers(onKeyDown, _this.buttonHandleKeyDown),
                    onKeyUp: callAllEventHandlers(onKeyUp, _this.buttonHandleKeyUp),
                    onBlur: callAllEventHandlers(onBlur, _this.buttonHandleBlur)
                };
                var eventHandlers = rest.disabled ? {} : enabledEventHandlers;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                    type: 'button',
                    role: 'button',
                    'aria-label': isOpen ? 'close menu' : 'open menu',
                    'aria-haspopup': true,
                    'data-toggle': true
                }, eventHandlers, rest);
            };
            _this.buttonHandleKeyUp = function(event) {
                // Prevent click event from emitting in Firefox
                event.preventDefault();
            };
            _this.buttonHandleKeyDown = function(event) {
                var key = normalizeArrowKey(event);
                if (_this.buttonKeyDownHandlers[key]) {
                    _this.buttonKeyDownHandlers[key].call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$assertThisInitialized$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_this), event);
                }
            };
            _this.buttonHandleClick = function(event) {
                event.preventDefault();
                // handle odd case for Safari and Firefox which
                // don't give the button the focus properly.
                /* istanbul ignore if (can't reasonably test this) */ if (_this.props.environment.document.activeElement === _this.props.environment.document.body) {
                    event.target.focus();
                }
                // to simplify testing components that use downshift, we'll not wrap this in a setTimeout
                // if the NODE_ENV is test. With the proper build system, this should be dead code eliminated
                // when building for production and should therefore have no impact on production code.
                if ("TURBOPACK compile-time falsy", 0) {
                    "TURBOPACK unreachable";
                } else {
                    // Ensure that toggle of menu occurs after the potential blur event in iOS
                    _this.internalSetTimeout(function() {
                        return _this.toggleMenu({
                            type: clickButton
                        });
                    });
                }
            };
            _this.buttonHandleBlur = function(event) {
                var blurTarget = event.target; // Save blur target for comparison with activeElement later
                // Need setTimeout, so that when the user presses Tab, the activeElement is the next focused element, not body element
                _this.internalSetTimeout(function() {
                    if (!_this.isMouseDown && (_this.props.environment.document.activeElement == null || _this.props.environment.document.activeElement.id !== _this.inputId) && _this.props.environment.document.activeElement !== blurTarget // Do nothing if we refocus the same element again (to solve issue in Safari on iOS)
                    ) {
                        _this.reset({
                            type: blurButton
                        });
                    }
                });
            };
            //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ BUTTON
            /////////////////////////////// LABEL
            _this.getLabelProps = function(props) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                    htmlFor: _this.inputId,
                    id: _this.labelId
                }, props);
            };
            //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ LABEL
            /////////////////////////////// INPUT
            _this.getInputProps = function(_temp4) {
                var _ref4 = _temp4 === void 0 ? {} : _temp4, onKeyDown = _ref4.onKeyDown, onBlur = _ref4.onBlur, onChange = _ref4.onChange, onInput = _ref4.onInput;
                _ref4.onChangeText;
                var rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref4, _excluded3$2);
                var onChangeKey;
                var eventHandlers = {};
                /* istanbul ignore next (preact) */ {
                    onChangeKey = 'onChange';
                }
                var _this$getState6 = _this.getState(), inputValue = _this$getState6.inputValue, isOpen = _this$getState6.isOpen, highlightedIndex = _this$getState6.highlightedIndex;
                if (!rest.disabled) {
                    var _eventHandlers;
                    eventHandlers = (_eventHandlers = {}, _eventHandlers[onChangeKey] = callAllEventHandlers(onChange, onInput, _this.inputHandleChange), _eventHandlers.onKeyDown = callAllEventHandlers(onKeyDown, _this.inputHandleKeyDown), _eventHandlers.onBlur = callAllEventHandlers(onBlur, _this.inputHandleBlur), _eventHandlers);
                }
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                    'aria-autocomplete': 'list',
                    'aria-activedescendant': isOpen && typeof highlightedIndex === 'number' && highlightedIndex >= 0 ? _this.getItemId(highlightedIndex) : null,
                    'aria-controls': isOpen ? _this.menuId : null,
                    'aria-labelledby': rest && rest['aria-label'] ? undefined : _this.labelId,
                    // https://developer.mozilla.org/en-US/docs/Web/Security/Securing_your_site/Turning_off_form_autocompletion
                    // revert back since autocomplete="nope" is ignored on latest Chrome and Opera
                    autoComplete: 'off',
                    value: inputValue,
                    id: _this.inputId
                }, eventHandlers, rest);
            };
            _this.inputHandleKeyDown = function(event) {
                var key = normalizeArrowKey(event);
                if (key && _this.inputKeyDownHandlers[key]) {
                    _this.inputKeyDownHandlers[key].call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$assertThisInitialized$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_this), event);
                }
            };
            _this.inputHandleChange = function(event) {
                _this.internalSetState({
                    type: changeInput,
                    isOpen: true,
                    inputValue: event.target.value,
                    highlightedIndex: _this.props.defaultHighlightedIndex
                });
            };
            _this.inputHandleBlur = function() {
                // Need setTimeout, so that when the user presses Tab, the activeElement is the next focused element, not the body element
                _this.internalSetTimeout(function() {
                    var downshiftButtonIsActive = _this.props.environment.document && !!_this.props.environment.document.activeElement && !!_this.props.environment.document.activeElement.dataset && _this.props.environment.document.activeElement.dataset.toggle && _this._rootNode && _this._rootNode.contains(_this.props.environment.document.activeElement);
                    if (!_this.isMouseDown && !downshiftButtonIsActive) {
                        _this.reset({
                            type: blurInput
                        });
                    }
                });
            };
            //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ INPUT
            /////////////////////////////// MENU
            _this.menuRef = function(node) {
                _this._menuNode = node;
            };
            _this.getMenuProps = function(_temp5, _temp6) {
                var _extends3;
                var _ref5 = _temp5 === void 0 ? {} : _temp5, _ref5$refKey = _ref5.refKey, refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey, ref = _ref5.ref, props = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref5, _excluded4$1);
                var _ref6 = _temp6 === void 0 ? {} : _temp6, _ref6$suppressRefErro = _ref6.suppressRefError, suppressRefError = _ref6$suppressRefErro === void 0 ? false : _ref6$suppressRefErro;
                _this.getMenuProps.called = true;
                _this.getMenuProps.refKey = refKey;
                _this.getMenuProps.suppressRefError = suppressRefError;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((_extends3 = {}, _extends3[refKey] = handleRefs(ref, _this.menuRef), _extends3.role = 'listbox', _extends3['aria-labelledby'] = props && props['aria-label'] ? null : _this.labelId, _extends3.id = _this.menuId, _extends3), props);
            };
            //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ MENU
            /////////////////////////////// ITEM
            _this.getItemProps = function(_temp7) {
                var _enabledEventHandlers;
                var _ref7 = _temp7 === void 0 ? {} : _temp7, onMouseMove = _ref7.onMouseMove, onMouseDown = _ref7.onMouseDown, onClick = _ref7.onClick;
                _ref7.onPress;
                var index = _ref7.index, _ref7$item = _ref7.item, item = _ref7$item === void 0 ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : requiredProp('getItemProps', 'item') : _ref7$item, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref7, _excluded5);
                if (index === undefined) {
                    _this.items.push(item);
                    index = _this.items.indexOf(item);
                } else {
                    _this.items[index] = item;
                }
                var onSelectKey = 'onClick';
                var customClickHandler = onClick;
                var enabledEventHandlers = (_enabledEventHandlers = {
                    // onMouseMove is used over onMouseEnter here. onMouseMove
                    // is only triggered on actual mouse movement while onMouseEnter
                    // can fire on DOM changes, interrupting keyboard navigation
                    onMouseMove: callAllEventHandlers(onMouseMove, function() {
                        if (index === _this.getState().highlightedIndex) {
                            return;
                        }
                        _this.setHighlightedIndex(index, {
                            type: itemMouseEnter
                        });
                        // We never want to manually scroll when changing state based
                        // on `onMouseMove` because we will be moving the element out
                        // from under the user which is currently scrolling/moving the
                        // cursor
                        _this.avoidScrolling = true;
                        _this.internalSetTimeout(function() {
                            return _this.avoidScrolling = false;
                        }, 250);
                    }),
                    onMouseDown: callAllEventHandlers(onMouseDown, function(event) {
                        // This prevents the activeElement from being changed
                        // to the item so it can remain with the current activeElement
                        // which is a more common use case.
                        event.preventDefault();
                    })
                }, _enabledEventHandlers[onSelectKey] = callAllEventHandlers(customClickHandler, function() {
                    _this.selectItemAtIndex(index, {
                        type: clickItem
                    });
                }), _enabledEventHandlers);
                // Passing down the onMouseDown handler to prevent redirect
                // of the activeElement if clicking on disabled items
                var eventHandlers = rest.disabled ? {
                    onMouseDown: enabledEventHandlers.onMouseDown
                } : enabledEventHandlers;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                    id: _this.getItemId(index),
                    role: 'option',
                    'aria-selected': _this.getState().highlightedIndex === index
                }, eventHandlers, rest);
            };
            //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ITEM
            _this.clearItems = function() {
                _this.items = [];
            };
            _this.reset = function(otherStateToSet, cb) {
                if (otherStateToSet === void 0) {
                    otherStateToSet = {};
                }
                otherStateToSet = pickState(otherStateToSet);
                _this.internalSetState(function(_ref8) {
                    var selectedItem = _ref8.selectedItem;
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                        isOpen: _this.props.defaultIsOpen,
                        highlightedIndex: _this.props.defaultHighlightedIndex,
                        inputValue: _this.props.itemToString(selectedItem)
                    }, otherStateToSet);
                }, cb);
            };
            _this.toggleMenu = function(otherStateToSet, cb) {
                if (otherStateToSet === void 0) {
                    otherStateToSet = {};
                }
                otherStateToSet = pickState(otherStateToSet);
                _this.internalSetState(function(_ref9) {
                    var isOpen = _ref9.isOpen;
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                        isOpen: !isOpen
                    }, isOpen && {
                        highlightedIndex: _this.props.defaultHighlightedIndex
                    }, otherStateToSet);
                }, function() {
                    var _this$getState7 = _this.getState(), isOpen = _this$getState7.isOpen, highlightedIndex = _this$getState7.highlightedIndex;
                    if (isOpen) {
                        if (_this.getItemCount() > 0 && typeof highlightedIndex === 'number') {
                            _this.setHighlightedIndex(highlightedIndex, otherStateToSet);
                        }
                    }
                    cbToCb(cb)();
                });
            };
            _this.openMenu = function(cb) {
                _this.internalSetState({
                    isOpen: true
                }, cb);
            };
            _this.closeMenu = function(cb) {
                _this.internalSetState({
                    isOpen: false
                }, cb);
            };
            _this.updateStatus = debounce(function() {
                var state = _this.getState();
                var item = _this.items[state.highlightedIndex];
                var resultCount = _this.getItemCount();
                var status = _this.props.getA11yStatusMessage((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                    itemToString: _this.props.itemToString,
                    previousResultCount: _this.previousResultCount,
                    resultCount: resultCount,
                    highlightedItem: item
                }, state));
                _this.previousResultCount = resultCount;
                setStatus(status, _this.props.environment.document);
            }, 200);
            var _this$props = _this.props, defaultHighlightedIndex = _this$props.defaultHighlightedIndex, _this$props$initialHi = _this$props.initialHighlightedIndex, _highlightedIndex = _this$props$initialHi === void 0 ? defaultHighlightedIndex : _this$props$initialHi, defaultIsOpen = _this$props.defaultIsOpen, _this$props$initialIs = _this$props.initialIsOpen, _isOpen = _this$props$initialIs === void 0 ? defaultIsOpen : _this$props$initialIs, _this$props$initialIn = _this$props.initialInputValue, _inputValue = _this$props$initialIn === void 0 ? '' : _this$props$initialIn, _this$props$initialSe = _this$props.initialSelectedItem, _selectedItem = _this$props$initialSe === void 0 ? null : _this$props$initialSe;
            var _state = _this.getState({
                highlightedIndex: _highlightedIndex,
                isOpen: _isOpen,
                inputValue: _inputValue,
                selectedItem: _selectedItem
            });
            if (_state.selectedItem != null && _this.props.initialInputValue === undefined) {
                _state.inputValue = _this.props.itemToString(_state.selectedItem);
            }
            _this.state = _state;
            return _this;
        }
        var _proto = Downshift.prototype;
        /**
     * Clear all running timeouts
     */ _proto.internalClearTimeouts = function internalClearTimeouts() {
            this.timeoutIds.forEach(function(id) {
                clearTimeout(id);
            });
            this.timeoutIds = [];
        };
        _proto.getState = function getState$1(stateToMerge) {
            if (stateToMerge === void 0) {
                stateToMerge = this.state;
            }
            return getState(stateToMerge, this.props);
        };
        _proto.getItemCount = function getItemCount() {
            // things read better this way. They're in priority order:
            // 1. `this.itemCount`
            // 2. `this.props.itemCount`
            // 3. `this.items.length`
            var itemCount = this.items.length;
            if (this.itemCount != null) {
                itemCount = this.itemCount;
            } else if (this.props.itemCount !== undefined) {
                itemCount = this.props.itemCount;
            }
            return itemCount;
        };
        _proto.getItemNodeFromIndex = function getItemNodeFromIndex(index) {
            return this.props.environment.document.getElementById(this.getItemId(index));
        };
        _proto.scrollHighlightedItemIntoView = function scrollHighlightedItemIntoView() {
            /* istanbul ignore else (react-native) */ {
                var node = this.getItemNodeFromIndex(this.getState().highlightedIndex);
                this.props.scrollIntoView(node, this._menuNode);
            }
        };
        _proto.moveHighlightedIndex = function moveHighlightedIndex(amount, otherStateToSet) {
            var _this6 = this;
            var itemCount = this.getItemCount();
            var _this$getState8 = this.getState(), highlightedIndex = _this$getState8.highlightedIndex;
            if (itemCount > 0) {
                var nextHighlightedIndex = getNextWrappingIndex(amount, highlightedIndex, itemCount, function(index) {
                    return _this6.getItemNodeFromIndex(index);
                });
                this.setHighlightedIndex(nextHighlightedIndex, otherStateToSet);
            }
        };
        _proto.getStateAndHelpers = function getStateAndHelpers() {
            var _this$getState9 = this.getState(), highlightedIndex = _this$getState9.highlightedIndex, inputValue = _this$getState9.inputValue, selectedItem = _this$getState9.selectedItem, isOpen = _this$getState9.isOpen;
            var itemToString = this.props.itemToString;
            var id = this.id;
            var getRootProps = this.getRootProps, getToggleButtonProps = this.getToggleButtonProps, getLabelProps = this.getLabelProps, getMenuProps = this.getMenuProps, getInputProps = this.getInputProps, getItemProps = this.getItemProps, openMenu = this.openMenu, closeMenu = this.closeMenu, toggleMenu = this.toggleMenu, selectItem = this.selectItem, selectItemAtIndex = this.selectItemAtIndex, selectHighlightedItem = this.selectHighlightedItem, setHighlightedIndex = this.setHighlightedIndex, clearSelection = this.clearSelection, clearItems = this.clearItems, reset = this.reset, setItemCount = this.setItemCount, unsetItemCount = this.unsetItemCount, setState = this.internalSetState;
            return {
                // prop getters
                getRootProps: getRootProps,
                getToggleButtonProps: getToggleButtonProps,
                getLabelProps: getLabelProps,
                getMenuProps: getMenuProps,
                getInputProps: getInputProps,
                getItemProps: getItemProps,
                // actions
                reset: reset,
                openMenu: openMenu,
                closeMenu: closeMenu,
                toggleMenu: toggleMenu,
                selectItem: selectItem,
                selectItemAtIndex: selectItemAtIndex,
                selectHighlightedItem: selectHighlightedItem,
                setHighlightedIndex: setHighlightedIndex,
                clearSelection: clearSelection,
                clearItems: clearItems,
                setItemCount: setItemCount,
                unsetItemCount: unsetItemCount,
                setState: setState,
                // props
                itemToString: itemToString,
                // derived
                id: id,
                // state
                highlightedIndex: highlightedIndex,
                inputValue: inputValue,
                isOpen: isOpen,
                selectedItem: selectedItem
            };
        };
        _proto.componentDidMount = function componentDidMount() {
            var _this7 = this;
            /* istanbul ignore if (react-native) */ if (("TURBOPACK compile-time value", "development") !== 'production' && !false && this.getMenuProps.called && !this.getMenuProps.suppressRefError) {
                validateGetMenuPropsCalledCorrectly(this._menuNode, this.getMenuProps);
            }
            /* istanbul ignore if (react-native) */ {
                // this.isMouseDown helps us track whether the mouse is currently held down.
                // This is useful when the user clicks on an item in the list, but holds the mouse
                // down long enough for the list to disappear (because the blur event fires on the input)
                // this.isMouseDown is used in the blur handler on the input to determine whether the blur event should
                // trigger hiding the menu.
                var onMouseDown = function onMouseDown() {
                    _this7.isMouseDown = true;
                };
                var onMouseUp = function onMouseUp(event) {
                    _this7.isMouseDown = false;
                    // if the target element or the activeElement is within a downshift node
                    // then we don't want to reset downshift
                    var contextWithinDownshift = targetWithinDownshift(event.target, [
                        _this7._rootNode,
                        _this7._menuNode
                    ], _this7.props.environment);
                    if (!contextWithinDownshift && _this7.getState().isOpen) {
                        _this7.reset({
                            type: mouseUp
                        }, function() {
                            return _this7.props.onOuterClick(_this7.getStateAndHelpers());
                        });
                    }
                };
                // Touching an element in iOS gives focus and hover states, but touching out of
                // the element will remove hover, and persist the focus state, resulting in the
                // blur event not being triggered.
                // this.isTouchMove helps us track whether the user is tapping or swiping on a touch screen.
                // If the user taps outside of Downshift, the component should be reset,
                // but not if the user is swiping
                var onTouchStart = function onTouchStart() {
                    _this7.isTouchMove = false;
                };
                var onTouchMove = function onTouchMove() {
                    _this7.isTouchMove = true;
                };
                var onTouchEnd = function onTouchEnd(event) {
                    var contextWithinDownshift = targetWithinDownshift(event.target, [
                        _this7._rootNode,
                        _this7._menuNode
                    ], _this7.props.environment, false);
                    if (!_this7.isTouchMove && !contextWithinDownshift && _this7.getState().isOpen) {
                        _this7.reset({
                            type: touchEnd
                        }, function() {
                            return _this7.props.onOuterClick(_this7.getStateAndHelpers());
                        });
                    }
                };
                var environment = this.props.environment;
                environment.addEventListener('mousedown', onMouseDown);
                environment.addEventListener('mouseup', onMouseUp);
                environment.addEventListener('touchstart', onTouchStart);
                environment.addEventListener('touchmove', onTouchMove);
                environment.addEventListener('touchend', onTouchEnd);
                this.cleanup = function() {
                    _this7.internalClearTimeouts();
                    _this7.updateStatus.cancel();
                    environment.removeEventListener('mousedown', onMouseDown);
                    environment.removeEventListener('mouseup', onMouseUp);
                    environment.removeEventListener('touchstart', onTouchStart);
                    environment.removeEventListener('touchmove', onTouchMove);
                    environment.removeEventListener('touchend', onTouchEnd);
                };
            }
        };
        _proto.shouldScroll = function shouldScroll(prevState, prevProps) {
            var _ref10 = this.props.highlightedIndex === undefined ? this.getState() : this.props, currentHighlightedIndex = _ref10.highlightedIndex;
            var _ref11 = prevProps.highlightedIndex === undefined ? prevState : prevProps, prevHighlightedIndex = _ref11.highlightedIndex;
            var scrollWhenOpen = currentHighlightedIndex && this.getState().isOpen && !prevState.isOpen;
            var scrollWhenNavigating = currentHighlightedIndex !== prevHighlightedIndex;
            return scrollWhenOpen || scrollWhenNavigating;
        };
        _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {
            if ("TURBOPACK compile-time truthy", 1) {
                validateControlledUnchanged(this.state, prevProps, this.props);
                /* istanbul ignore if (react-native) */ if (this.getMenuProps.called && !this.getMenuProps.suppressRefError) {
                    validateGetMenuPropsCalledCorrectly(this._menuNode, this.getMenuProps);
                }
            }
            if (isControlledProp(this.props, 'selectedItem') && this.props.selectedItemChanged(prevProps.selectedItem, this.props.selectedItem)) {
                this.internalSetState({
                    type: controlledPropUpdatedSelectedItem,
                    inputValue: this.props.itemToString(this.props.selectedItem)
                });
            }
            if (!this.avoidScrolling && this.shouldScroll(prevState, prevProps)) {
                this.scrollHighlightedItemIntoView();
            }
            /* istanbul ignore else (react-native) */ {
                this.updateStatus();
            }
        };
        _proto.componentWillUnmount = function componentWillUnmount() {
            this.cleanup(); // avoids memory leak
        };
        _proto.render = function render() {
            var children = unwrapArray(this.props.children, noop);
            // because the items are rerendered every time we call the children
            // we clear this out each render and it will be populated again as
            // getItemProps is called.
            this.clearItems();
            // we reset this so we know whether the user calls getRootProps during
            // this render. If they do then we don't need to do anything,
            // if they don't then we need to clone the element they return and
            // apply the props for them.
            this.getRootProps.called = false;
            this.getRootProps.refKey = undefined;
            this.getRootProps.suppressRefError = undefined;
            // we do something similar for getMenuProps
            this.getMenuProps.called = false;
            this.getMenuProps.refKey = undefined;
            this.getMenuProps.suppressRefError = undefined;
            // we do something similar for getLabelProps
            this.getLabelProps.called = false;
            // and something similar for getInputProps
            this.getInputProps.called = false;
            var element = unwrapArray(children(this.getStateAndHelpers()));
            if (!element) {
                return null;
            }
            if (this.getRootProps.called || this.props.suppressRefError) {
                if (("TURBOPACK compile-time value", "development") !== 'production' && !this.getRootProps.suppressRefError && !this.props.suppressRefError) {
                    validateGetRootPropsCalledCorrectly(element, this.getRootProps);
                }
                return element;
            } else if (isDOMElement(element)) {
                // they didn't apply the root props, but we can clone
                // this and apply the props ourselves
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"])(element, this.getRootProps(getElementProps(element)));
            }
            /* istanbul ignore else */ if ("TURBOPACK compile-time truthy", 1) {
                // they didn't apply the root props, but they need to
                // otherwise we can't query around the autocomplete
                throw new Error('downshift: If you return a non-DOM element, you must apply the getRootProps function');
            }
            /* istanbul ignore next */ return undefined;
        };
        return Downshift;
    }(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"]);
    Downshift.defaultProps = {
        defaultHighlightedIndex: null,
        defaultIsOpen: false,
        getA11yStatusMessage: getA11yStatusMessage$1,
        itemToString: function itemToString(i) {
            if (i == null) {
                return '';
            }
            if (("TURBOPACK compile-time value", "development") !== 'production' && isPlainObject(i) && !i.hasOwnProperty('toString')) {
                // eslint-disable-next-line no-console
                console.warn('downshift: An object was passed to the default implementation of `itemToString`. You should probably provide your own `itemToString` implementation. Please refer to the `itemToString` API documentation.', 'The object that was passed:', i);
            }
            return String(i);
        },
        onStateChange: noop,
        onInputValueChange: noop,
        onUserAction: noop,
        onChange: noop,
        onSelect: noop,
        onOuterClick: noop,
        selectedItemChanged: function selectedItemChanged(prevItem, item) {
            return prevItem !== item;
        },
        environment: /* istanbul ignore next (ssr) */ typeof window === 'undefined' ? {} : window,
        stateReducer: function stateReducer(state, stateToSet) {
            return stateToSet;
        },
        suppressRefError: false,
        scrollIntoView: scrollIntoView
    };
    Downshift.stateChangeTypes = stateChangeTypes$3;
    return Downshift;
}();
("TURBOPACK compile-time truthy", 1) ? Downshift.propTypes = {
    children: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    defaultHighlightedIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    defaultIsOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bool,
    initialHighlightedIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    initialSelectedItem: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
    initialInputValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    initialIsOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bool,
    getA11yStatusMessage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    itemToString: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onSelect: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onStateChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onInputValueChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onUserAction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onOuterClick: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    selectedItemChanged: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    stateReducer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    itemCount: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    environment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].shape({
        addEventListener: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
        removeEventListener: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
        document: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].shape({
            getElementById: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
            activeElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
            body: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any
        })
    }),
    suppressRefError: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bool,
    scrollIntoView: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    // things we keep in state for uncontrolled components
    // but can accept as props for controlled components
    /* eslint-disable react/no-unused-prop-types */ selectedItem: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
    isOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bool,
    inputValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    highlightedIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    labelId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    inputId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    menuId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    getItemId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func
} : ("TURBOPACK unreachable", undefined);
var Downshift$1 = Downshift;
function validateGetMenuPropsCalledCorrectly(node, _ref12) {
    var refKey = _ref12.refKey;
    if (!node) {
        // eslint-disable-next-line no-console
        console.error("downshift: The ref prop \"" + refKey + "\" from getMenuProps was not applied correctly on your menu element.");
    }
}
function validateGetRootPropsCalledCorrectly(element, _ref13) {
    var refKey = _ref13.refKey;
    var refKeySpecified = refKey !== 'ref';
    var isComposite = !isDOMElement(element);
    if (isComposite && !refKeySpecified && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$downshift$2f$node_modules$2f$react$2d$is$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isForwardRef"])(element)) {
        // eslint-disable-next-line no-console
        console.error('downshift: You returned a non-DOM element. You must specify a refKey in getRootProps');
    } else if (!isComposite && refKeySpecified) {
        // eslint-disable-next-line no-console
        console.error("downshift: You returned a DOM element. You should not specify a refKey in getRootProps. You specified \"" + refKey + "\"");
    }
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$downshift$2f$node_modules$2f$react$2d$is$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isForwardRef"])(element) && !getElementProps(element)[refKey]) {
        // eslint-disable-next-line no-console
        console.error("downshift: You must apply the ref prop \"" + refKey + "\" from getRootProps onto your root element.");
    }
}
var _excluded$3 = [
    "isInitialMount",
    "highlightedIndex",
    "items",
    "environment"
];
var dropdownDefaultStateValues = {
    highlightedIndex: -1,
    isOpen: false,
    selectedItem: null,
    inputValue: ''
};
function callOnChangeProps(action, state, newState) {
    var props = action.props, type = action.type;
    var changes = {};
    Object.keys(state).forEach(function(key) {
        invokeOnChangeHandler(key, action, state, newState);
        if (newState[key] !== state[key]) {
            changes[key] = newState[key];
        }
    });
    if (props.onStateChange && Object.keys(changes).length) {
        props.onStateChange((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            type: type
        }, changes));
    }
}
function invokeOnChangeHandler(key, action, state, newState) {
    var props = action.props, type = action.type;
    var handler = "on" + capitalizeString(key) + "Change";
    if (props[handler] && newState[key] !== undefined && newState[key] !== state[key]) {
        props[handler]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            type: type
        }, newState));
    }
}
/**
 * Default state reducer that returns the changes.
 *
 * @param {Object} s state.
 * @param {Object} a action with changes.
 * @returns {Object} changes.
 */ function stateReducer(s, a) {
    return a.changes;
}
/**
 * Returns a message to be added to aria-live region when item is selected.
 *
 * @param {Object} selectionParameters Parameters required to build the message.
 * @returns {string} The a11y message.
 */ function getA11ySelectionMessage(selectionParameters) {
    var selectedItem = selectionParameters.selectedItem, itemToStringLocal = selectionParameters.itemToString;
    return selectedItem ? itemToStringLocal(selectedItem) + " has been selected." : '';
}
/**
 * Debounced call for updating the a11y message.
 */ var updateA11yStatus = debounce(function(getA11yMessage, document1) {
    setStatus(getA11yMessage(), document1);
}, 200);
// istanbul ignore next
var useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"];
function useElementIds(_ref) {
    var _ref$id = _ref.id, id = _ref$id === void 0 ? "downshift-" + generateId() : _ref$id, labelId = _ref.labelId, menuId = _ref.menuId, getItemId = _ref.getItemId, toggleButtonId = _ref.toggleButtonId, inputId = _ref.inputId;
    var elementIdsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({
        labelId: labelId || id + "-label",
        menuId: menuId || id + "-menu",
        getItemId: getItemId || ({
            "useElementIds.useRef[elementIdsRef]": function(index) {
                return id + "-item-" + index;
            }
        })["useElementIds.useRef[elementIdsRef]"],
        toggleButtonId: toggleButtonId || id + "-toggle-button",
        inputId: inputId || id + "-input"
    });
    return elementIdsRef.current;
}
function getItemAndIndex(itemProp, indexProp, items, errorMessage) {
    var item, index;
    if (itemProp === undefined) {
        if (indexProp === undefined) {
            throw new Error(errorMessage);
        }
        item = items[indexProp];
        index = indexProp;
    } else {
        index = indexProp === undefined ? items.indexOf(itemProp) : indexProp;
        item = itemProp;
    }
    return [
        item,
        index
    ];
}
function itemToString(item) {
    return item ? String(item) : '';
}
function isAcceptedCharacterKey(key) {
    return /^\S{1}$/.test(key);
}
function capitalizeString(string) {
    return "" + string.slice(0, 1).toUpperCase() + string.slice(1);
}
function useLatestRef(val) {
    var ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(val);
    // technically this is not "concurrent mode safe" because we're manipulating
    // the value during render (so it's not idempotent). However, the places this
    // hook is used is to support memoizing callbacks which will be called
    // *during* render, so we need the latest values *during* render.
    // If not for this, then we'd probably want to use useLayoutEffect instead.
    ref.current = val;
    return ref;
}
/**
 * Computes the controlled state using a the previous state, props,
 * two reducers, one from downshift and an optional one from the user.
 * Also calls the onChange handlers for state values that have changed.
 *
 * @param {Function} reducer Reducer function from downshift.
 * @param {Object} initialState Initial state of the hook.
 * @param {Object} props The hook props.
 * @returns {Array} An array with the state and an action dispatcher.
 */ function useEnhancedReducer(reducer, initialState, props) {
    var prevStateRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    var actionRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    var enhancedReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useEnhancedReducer.useCallback[enhancedReducer]": function(state, action) {
            actionRef.current = action;
            state = getState(state, action.props);
            var changes = reducer(state, action);
            var newState = action.props.stateReducer(state, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, action, {
                changes: changes
            }));
            return newState;
        }
    }["useEnhancedReducer.useCallback[enhancedReducer]"], [
        reducer
    ]);
    var _useReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReducer"])(enhancedReducer, initialState), state = _useReducer[0], dispatch = _useReducer[1];
    var propsRef = useLatestRef(props);
    var dispatchWithProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useEnhancedReducer.useCallback[dispatchWithProps]": function(action) {
            return dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                props: propsRef.current
            }, action));
        }
    }["useEnhancedReducer.useCallback[dispatchWithProps]"], [
        propsRef
    ]);
    var action = actionRef.current;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useEnhancedReducer.useEffect": function() {
            if (action && prevStateRef.current && prevStateRef.current !== state) {
                callOnChangeProps(action, getState(prevStateRef.current, action.props), state);
            }
            prevStateRef.current = state;
        }
    }["useEnhancedReducer.useEffect"], [
        state,
        props,
        action
    ]);
    return [
        state,
        dispatchWithProps
    ];
}
/**
 * Wraps the useEnhancedReducer and applies the controlled prop values before
 * returning the new state.
 *
 * @param {Function} reducer Reducer function from downshift.
 * @param {Object} initialState Initial state of the hook.
 * @param {Object} props The hook props.
 * @returns {Array} An array with the state and an action dispatcher.
 */ function useControlledReducer$1(reducer, initialState, props) {
    var _useEnhancedReducer = useEnhancedReducer(reducer, initialState, props), state = _useEnhancedReducer[0], dispatch = _useEnhancedReducer[1];
    return [
        getState(state, props),
        dispatch
    ];
}
var defaultProps$3 = {
    itemToString: itemToString,
    stateReducer: stateReducer,
    getA11ySelectionMessage: getA11ySelectionMessage,
    scrollIntoView: scrollIntoView,
    environment: /* istanbul ignore next (ssr) */ typeof window === 'undefined' ? {} : window
};
function getDefaultValue$1(props, propKey, defaultStateValues) {
    if (defaultStateValues === void 0) {
        defaultStateValues = dropdownDefaultStateValues;
    }
    var defaultValue = props["default" + capitalizeString(propKey)];
    if (defaultValue !== undefined) {
        return defaultValue;
    }
    return defaultStateValues[propKey];
}
function getInitialValue$1(props, propKey, defaultStateValues) {
    if (defaultStateValues === void 0) {
        defaultStateValues = dropdownDefaultStateValues;
    }
    var value = props[propKey];
    if (value !== undefined) {
        return value;
    }
    var initialValue = props["initial" + capitalizeString(propKey)];
    if (initialValue !== undefined) {
        return initialValue;
    }
    return getDefaultValue$1(props, propKey, defaultStateValues);
}
function getInitialState$2(props) {
    var selectedItem = getInitialValue$1(props, 'selectedItem');
    var isOpen = getInitialValue$1(props, 'isOpen');
    var highlightedIndex = getInitialValue$1(props, 'highlightedIndex');
    var inputValue = getInitialValue$1(props, 'inputValue');
    return {
        highlightedIndex: highlightedIndex < 0 && selectedItem && isOpen ? props.items.indexOf(selectedItem) : highlightedIndex,
        isOpen: isOpen,
        selectedItem: selectedItem,
        inputValue: inputValue
    };
}
function getHighlightedIndexOnOpen(props, state, offset) {
    var items = props.items, initialHighlightedIndex = props.initialHighlightedIndex, defaultHighlightedIndex = props.defaultHighlightedIndex;
    var selectedItem = state.selectedItem, highlightedIndex = state.highlightedIndex;
    if (items.length === 0) {
        return -1;
    }
    // initialHighlightedIndex will give value to highlightedIndex on initial state only.
    if (initialHighlightedIndex !== undefined && highlightedIndex === initialHighlightedIndex) {
        return initialHighlightedIndex;
    }
    if (defaultHighlightedIndex !== undefined) {
        return defaultHighlightedIndex;
    }
    if (selectedItem) {
        return items.indexOf(selectedItem);
    }
    if (offset === 0) {
        return -1;
    }
    return offset < 0 ? items.length - 1 : 0;
}
/**
 * Reuse the movement tracking of mouse and touch events.
 *
 * @param {boolean} isOpen Whether the dropdown is open or not.
 * @param {Array<Object>} downshiftElementRefs Downshift element refs to track movement (toggleButton, menu etc.)
 * @param {Object} environment Environment where component/hook exists.
 * @param {Function} handleBlur Handler on blur from mouse or touch.
 * @returns {Object} Ref containing whether mouseDown or touchMove event is happening
 */ function useMouseAndTouchTracker(isOpen, downshiftElementRefs, environment, handleBlur) {
    var mouseAndTouchTrackersRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({
        isMouseDown: false,
        isTouchMove: false
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useMouseAndTouchTracker.useEffect": function() {
            if ((environment == null ? void 0 : environment.addEventListener) == null) {
                return;
            }
            // The same strategy for checking if a click occurred inside or outside downshift
            // as in downshift.js.
            var onMouseDown = function onMouseDown() {
                mouseAndTouchTrackersRef.current.isMouseDown = true;
            };
            var onMouseUp = function onMouseUp(event) {
                mouseAndTouchTrackersRef.current.isMouseDown = false;
                if (isOpen && !targetWithinDownshift(event.target, downshiftElementRefs.map({
                    "useMouseAndTouchTracker.useEffect.onMouseUp": function(ref) {
                        return ref.current;
                    }
                }["useMouseAndTouchTracker.useEffect.onMouseUp"]), environment)) {
                    handleBlur();
                }
            };
            var onTouchStart = function onTouchStart() {
                mouseAndTouchTrackersRef.current.isTouchMove = false;
            };
            var onTouchMove = function onTouchMove() {
                mouseAndTouchTrackersRef.current.isTouchMove = true;
            };
            var onTouchEnd = function onTouchEnd(event) {
                if (isOpen && !mouseAndTouchTrackersRef.current.isTouchMove && !targetWithinDownshift(event.target, downshiftElementRefs.map({
                    "useMouseAndTouchTracker.useEffect.onTouchEnd": function(ref) {
                        return ref.current;
                    }
                }["useMouseAndTouchTracker.useEffect.onTouchEnd"]), environment, false)) {
                    handleBlur();
                }
            };
            environment.addEventListener('mousedown', onMouseDown);
            environment.addEventListener('mouseup', onMouseUp);
            environment.addEventListener('touchstart', onTouchStart);
            environment.addEventListener('touchmove', onTouchMove);
            environment.addEventListener('touchend', onTouchEnd);
            // eslint-disable-next-line consistent-return
            return function cleanup() {
                environment.removeEventListener('mousedown', onMouseDown);
                environment.removeEventListener('mouseup', onMouseUp);
                environment.removeEventListener('touchstart', onTouchStart);
                environment.removeEventListener('touchmove', onTouchMove);
                environment.removeEventListener('touchend', onTouchEnd);
            };
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["useMouseAndTouchTracker.useEffect"], [
        isOpen,
        environment
    ]);
    return mouseAndTouchTrackersRef;
}
/* istanbul ignore next */ // eslint-disable-next-line import/no-mutable-exports
var useGetterPropsCalledChecker = function useGetterPropsCalledChecker() {
    return noop;
};
/**
 * Custom hook that checks if getter props are called correctly.
 *
 * @param  {...any} propKeys Getter prop names to be handled.
 * @returns {Function} Setter function called inside getter props to set call information.
 */ /* istanbul ignore next */ if ("TURBOPACK compile-time truthy", 1) {
    useGetterPropsCalledChecker = function useGetterPropsCalledChecker() {
        var isInitialMountRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(true);
        for(var _len = arguments.length, propKeys = new Array(_len), _key = 0; _key < _len; _key++){
            propKeys[_key] = arguments[_key];
        }
        var getterPropsCalledRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(propKeys.reduce({
            "useGetterPropsCalledChecker.useRef[getterPropsCalledRef]": function(acc, propKey) {
                acc[propKey] = {};
                return acc;
            }
        }["useGetterPropsCalledChecker.useRef[getterPropsCalledRef]"], {}));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
            "useGetterPropsCalledChecker.useEffect": function() {
                Object.keys(getterPropsCalledRef.current).forEach({
                    "useGetterPropsCalledChecker.useEffect": function(propKey) {
                        var propCallInfo = getterPropsCalledRef.current[propKey];
                        if (isInitialMountRef.current) {
                            if (!Object.keys(propCallInfo).length) {
                                // eslint-disable-next-line no-console
                                console.error("downshift: You forgot to call the " + propKey + " getter function on your component / element.");
                                return;
                            }
                        }
                        var suppressRefError = propCallInfo.suppressRefError, refKey = propCallInfo.refKey, elementRef = propCallInfo.elementRef;
                        if ((!elementRef || !elementRef.current) && !suppressRefError) {
                            // eslint-disable-next-line no-console
                            console.error("downshift: The ref prop \"" + refKey + "\" from " + propKey + " was not applied correctly on your element.");
                        }
                    }
                }["useGetterPropsCalledChecker.useEffect"]);
                isInitialMountRef.current = false;
            }
        }["useGetterPropsCalledChecker.useEffect"]);
        var setGetterPropCallInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
            "useGetterPropsCalledChecker.useCallback[setGetterPropCallInfo]": function(propKey, suppressRefError, refKey, elementRef) {
                getterPropsCalledRef.current[propKey] = {
                    suppressRefError: suppressRefError,
                    refKey: refKey,
                    elementRef: elementRef
                };
            }
        }["useGetterPropsCalledChecker.useCallback[setGetterPropCallInfo]"], []);
        return setGetterPropCallInfo;
    };
}
function useA11yMessageSetter(getA11yMessage, dependencyArray, _ref2) {
    var isInitialMount = _ref2.isInitialMount, highlightedIndex = _ref2.highlightedIndex, items = _ref2.items, environment = _ref2.environment, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref2, _excluded$3);
    // Sets a11y status message on changes in state.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useA11yMessageSetter.useEffect": function() {
            if (isInitialMount || false) {
                return;
            }
            updateA11yStatus({
                "useA11yMessageSetter.useEffect": function() {
                    return getA11yMessage((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                        highlightedIndex: highlightedIndex,
                        highlightedItem: items[highlightedIndex],
                        resultCount: items.length
                    }, rest));
                }
            }["useA11yMessageSetter.useEffect"], environment.document);
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["useA11yMessageSetter.useEffect"], dependencyArray);
}
function useScrollIntoView(_ref3) {
    var highlightedIndex = _ref3.highlightedIndex, isOpen = _ref3.isOpen, itemRefs = _ref3.itemRefs, getItemNodeFromIndex = _ref3.getItemNodeFromIndex, menuElement = _ref3.menuElement, scrollIntoViewProp = _ref3.scrollIntoView;
    // used not to scroll on highlight by mouse.
    var shouldScrollRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(true);
    // Scroll on highlighted item if change comes from keyboard.
    useIsomorphicLayoutEffect({
        "useScrollIntoView.useIsomorphicLayoutEffect": function() {
            if (highlightedIndex < 0 || !isOpen || !Object.keys(itemRefs.current).length) {
                return;
            }
            if (shouldScrollRef.current === false) {
                shouldScrollRef.current = true;
            } else {
                scrollIntoViewProp(getItemNodeFromIndex(highlightedIndex), menuElement);
            }
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["useScrollIntoView.useIsomorphicLayoutEffect"], [
        highlightedIndex
    ]);
    return shouldScrollRef;
}
// eslint-disable-next-line import/no-mutable-exports
var useControlPropsValidator = noop;
/* istanbul ignore next */ if ("TURBOPACK compile-time truthy", 1) {
    useControlPropsValidator = function useControlPropsValidator(_ref4) {
        var isInitialMount = _ref4.isInitialMount, props = _ref4.props, state = _ref4.state;
        // used for checking when props are moving from controlled to uncontrolled.
        var prevPropsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(props);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
            "useControlPropsValidator.useEffect": function() {
                if (isInitialMount) {
                    return;
                }
                validateControlledUnchanged(state, prevPropsRef.current, props);
                prevPropsRef.current = props;
            }
        }["useControlPropsValidator.useEffect"], [
            state,
            props,
            isInitialMount
        ]);
    };
}
/**
 * Handles selection on Enter / Alt + ArrowUp. Closes the menu and resets the highlighted index, unless there is a highlighted.
 * In that case, selects the item and resets to defaults for open state and highlighted idex.
 * @param {Object} props The useCombobox props.
 * @param {number} highlightedIndex The index from the state.
 * @param {boolean} inputValue Also return the input value for state.
 * @returns The changes for the state.
 */ function getChangesOnSelection(props, highlightedIndex, inputValue) {
    var _props$items;
    if (inputValue === void 0) {
        inputValue = true;
    }
    var shouldSelect = ((_props$items = props.items) == null ? void 0 : _props$items.length) && highlightedIndex >= 0;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        isOpen: false,
        highlightedIndex: -1
    }, shouldSelect && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        selectedItem: props.items[highlightedIndex],
        isOpen: getDefaultValue$1(props, 'isOpen'),
        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex')
    }, inputValue && {
        inputValue: props.itemToString(props.items[highlightedIndex])
    }));
}
function downshiftCommonReducer(state, action, stateChangeTypes) {
    var type = action.type, props = action.props;
    var changes;
    switch(type){
        case stateChangeTypes.ItemMouseMove:
            changes = {
                highlightedIndex: action.disabled ? -1 : action.index
            };
            break;
        case stateChangeTypes.MenuMouseLeave:
            changes = {
                highlightedIndex: -1
            };
            break;
        case stateChangeTypes.ToggleButtonClick:
        case stateChangeTypes.FunctionToggleMenu:
            changes = {
                isOpen: !state.isOpen,
                highlightedIndex: state.isOpen ? -1 : getHighlightedIndexOnOpen(props, state, 0)
            };
            break;
        case stateChangeTypes.FunctionOpenMenu:
            changes = {
                isOpen: true,
                highlightedIndex: getHighlightedIndexOnOpen(props, state, 0)
            };
            break;
        case stateChangeTypes.FunctionCloseMenu:
            changes = {
                isOpen: false
            };
            break;
        case stateChangeTypes.FunctionSetHighlightedIndex:
            changes = {
                highlightedIndex: action.highlightedIndex
            };
            break;
        case stateChangeTypes.FunctionSetInputValue:
            changes = {
                inputValue: action.inputValue
            };
            break;
        case stateChangeTypes.FunctionReset:
            changes = {
                highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),
                isOpen: getDefaultValue$1(props, 'isOpen'),
                selectedItem: getDefaultValue$1(props, 'selectedItem'),
                inputValue: getDefaultValue$1(props, 'inputValue')
            };
            break;
        default:
            throw new Error('Reducer called without proper action type.');
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, state, changes);
}
/* eslint-enable complexity */ function getItemIndexByCharacterKey(_a) {
    var keysSoFar = _a.keysSoFar, highlightedIndex = _a.highlightedIndex, items = _a.items, itemToString = _a.itemToString, getItemNodeFromIndex = _a.getItemNodeFromIndex;
    var lowerCasedKeysSoFar = keysSoFar.toLowerCase();
    for(var index = 0; index < items.length; index++){
        // if we already have a search query in progress, we also consider the current highlighted item.
        var offsetIndex = (index + highlightedIndex + (keysSoFar.length < 2 ? 1 : 0)) % items.length;
        var item = items[offsetIndex];
        if (item !== undefined && itemToString(item).toLowerCase().startsWith(lowerCasedKeysSoFar)) {
            var element = getItemNodeFromIndex(offsetIndex);
            if (!(element === null || element === void 0 ? void 0 : element.hasAttribute('disabled'))) {
                return offsetIndex;
            }
        }
    }
    return highlightedIndex;
}
var propTypes$2 = {
    items: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].array.isRequired,
    itemToString: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    getA11yStatusMessage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    getA11ySelectionMessage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    highlightedIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    defaultHighlightedIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    initialHighlightedIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    isOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bool,
    defaultIsOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bool,
    initialIsOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bool,
    selectedItem: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
    initialSelectedItem: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
    defaultSelectedItem: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    labelId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    menuId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    getItemId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    toggleButtonId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    stateReducer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onSelectedItemChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onHighlightedIndexChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onStateChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onIsOpenChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    environment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].shape({
        addEventListener: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
        removeEventListener: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
        document: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].shape({
            getElementById: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
            activeElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
            body: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any
        })
    })
};
/**
 * Default implementation for status message. Only added when menu is open.
 * Will specift if there are results in the list, and if so, how many,
 * and what keys are relevant.
 *
 * @param {Object} param the downshift state and other relevant properties
 * @return {String} the a11y status message
 */ function getA11yStatusMessage(_a) {
    var isOpen = _a.isOpen, resultCount = _a.resultCount, previousResultCount = _a.previousResultCount;
    if (!isOpen) {
        return '';
    }
    if (!resultCount) {
        return 'No results are available.';
    }
    if (resultCount !== previousResultCount) {
        return "".concat(resultCount, " result").concat(resultCount === 1 ? ' is' : 's are', " available, use up and down arrow keys to navigate. Press Enter or Space Bar keys to select.");
    }
    return '';
}
var defaultProps$2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__assign"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__assign"])({}, defaultProps$3), {
    getA11yStatusMessage: getA11yStatusMessage
});
// eslint-disable-next-line import/no-mutable-exports
var validatePropTypes$2 = noop;
/* istanbul ignore next */ if ("TURBOPACK compile-time truthy", 1) {
    validatePropTypes$2 = function(options, caller) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].checkPropTypes(propTypes$2, options, 'prop', caller.name);
    };
}
var ToggleButtonClick$1 = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_click__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonKeyDownArrowDown = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_keydown_arrow_down__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonKeyDownArrowUp = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_keydown_arrow_up__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonKeyDownCharacter = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_keydown_character__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonKeyDownEscape = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_keydown_escape__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonKeyDownHome = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_keydown_home__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonKeyDownEnd = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_keydown_end__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonKeyDownEnter = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_keydown_enter__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonKeyDownSpaceButton = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_keydown_space_button__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonKeyDownPageUp = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_keydown_page_up__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonKeyDownPageDown = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_keydown_page_down__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonBlur = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_blur__' : ("TURBOPACK unreachable", undefined);
var MenuMouseLeave$1 = ("TURBOPACK compile-time truthy", 1) ? '__menu_mouse_leave__' : ("TURBOPACK unreachable", undefined);
var ItemMouseMove$1 = ("TURBOPACK compile-time truthy", 1) ? '__item_mouse_move__' : ("TURBOPACK unreachable", undefined);
var ItemClick$1 = ("TURBOPACK compile-time truthy", 1) ? '__item_click__' : ("TURBOPACK unreachable", undefined);
var FunctionToggleMenu$1 = ("TURBOPACK compile-time truthy", 1) ? '__function_toggle_menu__' : ("TURBOPACK unreachable", undefined);
var FunctionOpenMenu$1 = ("TURBOPACK compile-time truthy", 1) ? '__function_open_menu__' : ("TURBOPACK unreachable", undefined);
var FunctionCloseMenu$1 = ("TURBOPACK compile-time truthy", 1) ? '__function_close_menu__' : ("TURBOPACK unreachable", undefined);
var FunctionSetHighlightedIndex$1 = ("TURBOPACK compile-time truthy", 1) ? '__function_set_highlighted_index__' : ("TURBOPACK unreachable", undefined);
var FunctionSelectItem$1 = ("TURBOPACK compile-time truthy", 1) ? '__function_select_item__' : ("TURBOPACK unreachable", undefined);
var FunctionSetInputValue$1 = ("TURBOPACK compile-time truthy", 1) ? '__function_set_input_value__' : ("TURBOPACK unreachable", undefined);
var FunctionReset$2 = ("TURBOPACK compile-time truthy", 1) ? '__function_reset__' : ("TURBOPACK unreachable", undefined);
var stateChangeTypes$2 = /*#__PURE__*/ Object.freeze({
    __proto__: null,
    ToggleButtonClick: ToggleButtonClick$1,
    ToggleButtonKeyDownArrowDown: ToggleButtonKeyDownArrowDown,
    ToggleButtonKeyDownArrowUp: ToggleButtonKeyDownArrowUp,
    ToggleButtonKeyDownCharacter: ToggleButtonKeyDownCharacter,
    ToggleButtonKeyDownEscape: ToggleButtonKeyDownEscape,
    ToggleButtonKeyDownHome: ToggleButtonKeyDownHome,
    ToggleButtonKeyDownEnd: ToggleButtonKeyDownEnd,
    ToggleButtonKeyDownEnter: ToggleButtonKeyDownEnter,
    ToggleButtonKeyDownSpaceButton: ToggleButtonKeyDownSpaceButton,
    ToggleButtonKeyDownPageUp: ToggleButtonKeyDownPageUp,
    ToggleButtonKeyDownPageDown: ToggleButtonKeyDownPageDown,
    ToggleButtonBlur: ToggleButtonBlur,
    MenuMouseLeave: MenuMouseLeave$1,
    ItemMouseMove: ItemMouseMove$1,
    ItemClick: ItemClick$1,
    FunctionToggleMenu: FunctionToggleMenu$1,
    FunctionOpenMenu: FunctionOpenMenu$1,
    FunctionCloseMenu: FunctionCloseMenu$1,
    FunctionSetHighlightedIndex: FunctionSetHighlightedIndex$1,
    FunctionSelectItem: FunctionSelectItem$1,
    FunctionSetInputValue: FunctionSetInputValue$1,
    FunctionReset: FunctionReset$2
});
/* eslint-disable complexity */ function downshiftSelectReducer(state, action) {
    var _props$items;
    var type = action.type, props = action.props, altKey = action.altKey;
    var changes;
    switch(type){
        case ItemClick$1:
            changes = {
                isOpen: getDefaultValue$1(props, 'isOpen'),
                highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),
                selectedItem: props.items[action.index]
            };
            break;
        case ToggleButtonKeyDownCharacter:
            {
                var lowercasedKey = action.key;
                var inputValue = "" + state.inputValue + lowercasedKey;
                var prevHighlightedIndex = !state.isOpen && state.selectedItem ? props.items.indexOf(state.selectedItem) : state.highlightedIndex;
                var highlightedIndex = getItemIndexByCharacterKey({
                    keysSoFar: inputValue,
                    highlightedIndex: prevHighlightedIndex,
                    items: props.items,
                    itemToString: props.itemToString,
                    getItemNodeFromIndex: action.getItemNodeFromIndex
                });
                changes = {
                    inputValue: inputValue,
                    highlightedIndex: highlightedIndex,
                    isOpen: true
                };
            }
            break;
        case ToggleButtonKeyDownArrowDown:
            {
                var _highlightedIndex = state.isOpen ? getNextWrappingIndex(1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false) : altKey && state.selectedItem == null ? -1 : getHighlightedIndexOnOpen(props, state, 1);
                changes = {
                    highlightedIndex: _highlightedIndex,
                    isOpen: true
                };
            }
            break;
        case ToggleButtonKeyDownArrowUp:
            if (state.isOpen && altKey) {
                changes = getChangesOnSelection(props, state.highlightedIndex, false);
            } else {
                var _highlightedIndex2 = state.isOpen ? getNextWrappingIndex(-1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false) : getHighlightedIndexOnOpen(props, state, -1);
                changes = {
                    highlightedIndex: _highlightedIndex2,
                    isOpen: true
                };
            }
            break;
        // only triggered when menu is open.
        case ToggleButtonKeyDownEnter:
        case ToggleButtonKeyDownSpaceButton:
            changes = getChangesOnSelection(props, state.highlightedIndex, false);
            break;
        case ToggleButtonKeyDownHome:
            changes = {
                highlightedIndex: getNextNonDisabledIndex(1, 0, props.items.length, action.getItemNodeFromIndex, false),
                isOpen: true
            };
            break;
        case ToggleButtonKeyDownEnd:
            changes = {
                highlightedIndex: getNextNonDisabledIndex(-1, props.items.length - 1, props.items.length, action.getItemNodeFromIndex, false),
                isOpen: true
            };
            break;
        case ToggleButtonKeyDownPageUp:
            changes = {
                highlightedIndex: getNextWrappingIndex(-10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)
            };
            break;
        case ToggleButtonKeyDownPageDown:
            changes = {
                highlightedIndex: getNextWrappingIndex(10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)
            };
            break;
        case ToggleButtonKeyDownEscape:
            changes = {
                isOpen: false,
                highlightedIndex: -1
            };
            break;
        case ToggleButtonBlur:
            changes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                isOpen: false,
                highlightedIndex: -1
            }, state.highlightedIndex >= 0 && ((_props$items = props.items) == null ? void 0 : _props$items.length) && {
                selectedItem: props.items[state.highlightedIndex]
            });
            break;
        case FunctionSelectItem$1:
            changes = {
                selectedItem: action.selectedItem
            };
            break;
        default:
            return downshiftCommonReducer(state, action, stateChangeTypes$2);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, state, changes);
}
/* eslint-enable complexity */ var _excluded$2 = [
    "onMouseLeave",
    "refKey",
    "onKeyDown",
    "onBlur",
    "ref"
], _excluded2$2 = [
    "onBlur",
    "onClick",
    "onPress",
    "onKeyDown",
    "refKey",
    "ref"
], _excluded3$1 = [
    "item",
    "index",
    "onMouseMove",
    "onClick",
    "onPress",
    "refKey",
    "ref",
    "disabled"
];
useSelect.stateChangeTypes = stateChangeTypes$2;
function useSelect(userProps) {
    if (userProps === void 0) {
        userProps = {};
    }
    validatePropTypes$2(userProps, useSelect);
    // Props defaults and destructuring.
    var props = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, defaultProps$2, userProps);
    var items = props.items, scrollIntoView = props.scrollIntoView, environment = props.environment, itemToString = props.itemToString, getA11ySelectionMessage = props.getA11ySelectionMessage, getA11yStatusMessage = props.getA11yStatusMessage;
    // Initial state depending on controlled props.
    var initialState = getInitialState$2(props);
    var _useControlledReducer = useControlledReducer$1(downshiftSelectReducer, initialState, props), state = _useControlledReducer[0], dispatch = _useControlledReducer[1];
    var isOpen = state.isOpen, highlightedIndex = state.highlightedIndex, selectedItem = state.selectedItem, inputValue = state.inputValue;
    // Element efs.
    var toggleButtonRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    var menuRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    var itemRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({});
    // used to keep the inputValue clearTimeout object between renders.
    var clearTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // prevent id re-generation between renders.
    var elementIds = useElementIds(props);
    // used to keep track of how many items we had on previous cycle.
    var previousResultCountRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    var isInitialMountRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(true);
    // utility callback to get item element.
    var latest = useLatestRef({
        state: state,
        props: props
    });
    // Some utils.
    var getItemNodeFromIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[getItemNodeFromIndex]": function(index) {
            return itemRefs.current[elementIds.getItemId(index)];
        }
    }["useSelect.useCallback[getItemNodeFromIndex]"], [
        elementIds
    ]);
    // Effects.
    // Sets a11y status message on changes in state.
    useA11yMessageSetter(getA11yStatusMessage, [
        isOpen,
        highlightedIndex,
        inputValue,
        items
    ], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        isInitialMount: isInitialMountRef.current,
        previousResultCount: previousResultCountRef.current,
        items: items,
        environment: environment,
        itemToString: itemToString
    }, state));
    // Sets a11y status message on changes in selectedItem.
    useA11yMessageSetter(getA11ySelectionMessage, [
        selectedItem
    ], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        isInitialMount: isInitialMountRef.current,
        previousResultCount: previousResultCountRef.current,
        items: items,
        environment: environment,
        itemToString: itemToString
    }, state));
    // Scroll on highlighted item if change comes from keyboard.
    var shouldScrollRef = useScrollIntoView({
        menuElement: menuRef.current,
        highlightedIndex: highlightedIndex,
        isOpen: isOpen,
        itemRefs: itemRefs,
        scrollIntoView: scrollIntoView,
        getItemNodeFromIndex: getItemNodeFromIndex
    });
    // Sets cleanup for the keysSoFar callback, debounded after 500ms.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSelect.useEffect": function() {
            // init the clean function here as we need access to dispatch.
            clearTimeoutRef.current = debounce({
                "useSelect.useEffect": function(outerDispatch) {
                    outerDispatch({
                        type: FunctionSetInputValue$1,
                        inputValue: ''
                    });
                }
            }["useSelect.useEffect"], 500);
            // Cancel any pending debounced calls on mount
            return ({
                "useSelect.useEffect": function() {
                    clearTimeoutRef.current.cancel();
                }
            })["useSelect.useEffect"];
        }
    }["useSelect.useEffect"], []);
    // Invokes the keysSoFar callback set up above.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSelect.useEffect": function() {
            if (!inputValue) {
                return;
            }
            clearTimeoutRef.current(dispatch);
        }
    }["useSelect.useEffect"], [
        dispatch,
        inputValue
    ]);
    useControlPropsValidator({
        isInitialMount: isInitialMountRef.current,
        props: props,
        state: state
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSelect.useEffect": function() {
            if (isInitialMountRef.current) {
                return;
            }
            previousResultCountRef.current = items.length;
        }
    }["useSelect.useEffect"]);
    // Add mouse/touch events to document.
    var mouseAndTouchTrackersRef = useMouseAndTouchTracker(isOpen, [
        menuRef,
        toggleButtonRef
    ], environment, {
        "useSelect.useMouseAndTouchTracker[mouseAndTouchTrackersRef]": function() {
            dispatch({
                type: ToggleButtonBlur
            });
        }
    }["useSelect.useMouseAndTouchTracker[mouseAndTouchTrackersRef]"]);
    var setGetterPropCallInfo = useGetterPropsCalledChecker('getMenuProps', 'getToggleButtonProps');
    // Make initial ref false.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSelect.useEffect": function() {
            isInitialMountRef.current = false;
            return ({
                "useSelect.useEffect": function() {
                    isInitialMountRef.current = true;
                }
            })["useSelect.useEffect"];
        }
    }["useSelect.useEffect"], []);
    // Reset itemRefs on close.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSelect.useEffect": function() {
            if (!isOpen) {
                itemRefs.current = {};
            }
        }
    }["useSelect.useEffect"], [
        isOpen
    ]);
    // Event handler functions.
    var toggleButtonKeyDownHandlers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useSelect.useMemo[toggleButtonKeyDownHandlers]": function() {
            return {
                ArrowDown: function ArrowDown(event) {
                    event.preventDefault();
                    dispatch({
                        type: ToggleButtonKeyDownArrowDown,
                        getItemNodeFromIndex: getItemNodeFromIndex,
                        altKey: event.altKey
                    });
                },
                ArrowUp: function ArrowUp(event) {
                    event.preventDefault();
                    dispatch({
                        type: ToggleButtonKeyDownArrowUp,
                        getItemNodeFromIndex: getItemNodeFromIndex,
                        altKey: event.altKey
                    });
                },
                Home: function Home(event) {
                    event.preventDefault();
                    dispatch({
                        type: ToggleButtonKeyDownHome,
                        getItemNodeFromIndex: getItemNodeFromIndex
                    });
                },
                End: function End(event) {
                    event.preventDefault();
                    dispatch({
                        type: ToggleButtonKeyDownEnd,
                        getItemNodeFromIndex: getItemNodeFromIndex
                    });
                },
                Escape: function Escape() {
                    if (latest.current.state.isOpen) {
                        dispatch({
                            type: ToggleButtonKeyDownEscape
                        });
                    }
                },
                Enter: function Enter(event) {
                    event.preventDefault();
                    dispatch({
                        type: latest.current.state.isOpen ? ToggleButtonKeyDownEnter : ToggleButtonClick$1
                    });
                },
                PageUp: function PageUp(event) {
                    if (latest.current.state.isOpen) {
                        event.preventDefault();
                        dispatch({
                            type: ToggleButtonKeyDownPageUp,
                            getItemNodeFromIndex: getItemNodeFromIndex
                        });
                    }
                },
                PageDown: function PageDown(event) {
                    if (latest.current.state.isOpen) {
                        event.preventDefault();
                        dispatch({
                            type: ToggleButtonKeyDownPageDown,
                            getItemNodeFromIndex: getItemNodeFromIndex
                        });
                    }
                },
                ' ': function _(event) {
                    event.preventDefault();
                    var currentState = latest.current.state;
                    if (!currentState.isOpen) {
                        dispatch({
                            type: ToggleButtonClick$1
                        });
                        return;
                    }
                    if (currentState.inputValue) {
                        dispatch({
                            type: ToggleButtonKeyDownCharacter,
                            key: ' ',
                            getItemNodeFromIndex: getItemNodeFromIndex
                        });
                    } else {
                        dispatch({
                            type: ToggleButtonKeyDownSpaceButton
                        });
                    }
                }
            };
        }
    }["useSelect.useMemo[toggleButtonKeyDownHandlers]"], [
        dispatch,
        getItemNodeFromIndex,
        latest
    ]);
    // Action functions.
    var toggleMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[toggleMenu]": function() {
            dispatch({
                type: FunctionToggleMenu$1
            });
        }
    }["useSelect.useCallback[toggleMenu]"], [
        dispatch
    ]);
    var closeMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[closeMenu]": function() {
            dispatch({
                type: FunctionCloseMenu$1
            });
        }
    }["useSelect.useCallback[closeMenu]"], [
        dispatch
    ]);
    var openMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[openMenu]": function() {
            dispatch({
                type: FunctionOpenMenu$1
            });
        }
    }["useSelect.useCallback[openMenu]"], [
        dispatch
    ]);
    var setHighlightedIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[setHighlightedIndex]": function(newHighlightedIndex) {
            dispatch({
                type: FunctionSetHighlightedIndex$1,
                highlightedIndex: newHighlightedIndex
            });
        }
    }["useSelect.useCallback[setHighlightedIndex]"], [
        dispatch
    ]);
    var selectItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[selectItem]": function(newSelectedItem) {
            dispatch({
                type: FunctionSelectItem$1,
                selectedItem: newSelectedItem
            });
        }
    }["useSelect.useCallback[selectItem]"], [
        dispatch
    ]);
    var reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[reset]": function() {
            dispatch({
                type: FunctionReset$2
            });
        }
    }["useSelect.useCallback[reset]"], [
        dispatch
    ]);
    var setInputValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[setInputValue]": function(newInputValue) {
            dispatch({
                type: FunctionSetInputValue$1,
                inputValue: newInputValue
            });
        }
    }["useSelect.useCallback[setInputValue]"], [
        dispatch
    ]);
    // Getter functions.
    var getLabelProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[getLabelProps]": function(labelProps) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                id: elementIds.labelId,
                htmlFor: elementIds.toggleButtonId
            }, labelProps);
        }
    }["useSelect.useCallback[getLabelProps]"], [
        elementIds
    ]);
    var getMenuProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[getMenuProps]": function(_temp, _temp2) {
            var _extends2;
            var _ref = _temp === void 0 ? {} : _temp, onMouseLeave = _ref.onMouseLeave, _ref$refKey = _ref.refKey, refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey;
            _ref.onKeyDown;
            _ref.onBlur;
            var ref = _ref.ref, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref, _excluded$2);
            var _ref2 = _temp2 === void 0 ? {} : _temp2, _ref2$suppressRefErro = _ref2.suppressRefError, suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;
            var menuHandleMouseLeave = function menuHandleMouseLeave() {
                dispatch({
                    type: MenuMouseLeave$1
                });
            };
            setGetterPropCallInfo('getMenuProps', suppressRefError, refKey, menuRef);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((_extends2 = {}, _extends2[refKey] = handleRefs(ref, {
                "useSelect.useCallback[getMenuProps]": function(menuNode) {
                    menuRef.current = menuNode;
                }
            }["useSelect.useCallback[getMenuProps]"]), _extends2.id = elementIds.menuId, _extends2.role = 'listbox', _extends2['aria-labelledby'] = rest && rest['aria-label'] ? undefined : "" + elementIds.labelId, _extends2.onMouseLeave = callAllEventHandlers(onMouseLeave, menuHandleMouseLeave), _extends2), rest);
        }
    }["useSelect.useCallback[getMenuProps]"], [
        dispatch,
        setGetterPropCallInfo,
        elementIds
    ]);
    var getToggleButtonProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[getToggleButtonProps]": function(_temp3, _temp4) {
            var _extends3;
            var _ref3 = _temp3 === void 0 ? {} : _temp3, onBlur = _ref3.onBlur, onClick = _ref3.onClick;
            _ref3.onPress;
            var onKeyDown = _ref3.onKeyDown, _ref3$refKey = _ref3.refKey, refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey, ref = _ref3.ref, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref3, _excluded2$2);
            var _ref4 = _temp4 === void 0 ? {} : _temp4, _ref4$suppressRefErro = _ref4.suppressRefError, suppressRefError = _ref4$suppressRefErro === void 0 ? false : _ref4$suppressRefErro;
            var latestState = latest.current.state;
            var toggleButtonHandleClick = function toggleButtonHandleClick() {
                dispatch({
                    type: ToggleButtonClick$1
                });
            };
            var toggleButtonHandleBlur = function toggleButtonHandleBlur() {
                if (latestState.isOpen && !mouseAndTouchTrackersRef.current.isMouseDown) {
                    dispatch({
                        type: ToggleButtonBlur
                    });
                }
            };
            var toggleButtonHandleKeyDown = function toggleButtonHandleKeyDown(event) {
                var key = normalizeArrowKey(event);
                if (key && toggleButtonKeyDownHandlers[key]) {
                    toggleButtonKeyDownHandlers[key](event);
                } else if (isAcceptedCharacterKey(key)) {
                    dispatch({
                        type: ToggleButtonKeyDownCharacter,
                        key: key,
                        getItemNodeFromIndex: getItemNodeFromIndex
                    });
                }
            };
            var toggleProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((_extends3 = {}, _extends3[refKey] = handleRefs(ref, {
                "useSelect.useCallback[getToggleButtonProps].toggleProps": function(toggleButtonNode) {
                    toggleButtonRef.current = toggleButtonNode;
                }
            }["useSelect.useCallback[getToggleButtonProps].toggleProps"]), _extends3['aria-activedescendant'] = latestState.isOpen && latestState.highlightedIndex > -1 ? elementIds.getItemId(latestState.highlightedIndex) : '', _extends3['aria-controls'] = elementIds.menuId, _extends3['aria-expanded'] = latest.current.state.isOpen, _extends3['aria-haspopup'] = 'listbox', _extends3['aria-labelledby'] = rest && rest['aria-label'] ? undefined : "" + elementIds.labelId, _extends3.id = elementIds.toggleButtonId, _extends3.role = 'combobox', _extends3.tabIndex = 0, _extends3.onBlur = callAllEventHandlers(onBlur, toggleButtonHandleBlur), _extends3), rest);
            if (!rest.disabled) {
                /* istanbul ignore if (react-native) */ {
                    toggleProps.onClick = callAllEventHandlers(onClick, toggleButtonHandleClick);
                    toggleProps.onKeyDown = callAllEventHandlers(onKeyDown, toggleButtonHandleKeyDown);
                }
            }
            setGetterPropCallInfo('getToggleButtonProps', suppressRefError, refKey, toggleButtonRef);
            return toggleProps;
        }
    }["useSelect.useCallback[getToggleButtonProps]"], [
        latest,
        elementIds,
        setGetterPropCallInfo,
        dispatch,
        mouseAndTouchTrackersRef,
        toggleButtonKeyDownHandlers,
        getItemNodeFromIndex
    ]);
    var getItemProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelect.useCallback[getItemProps]": function(_temp5) {
            var _extends4;
            var _ref5 = _temp5 === void 0 ? {} : _temp5, itemProp = _ref5.item, indexProp = _ref5.index, onMouseMove = _ref5.onMouseMove, onClick = _ref5.onClick;
            _ref5.onPress;
            var _ref5$refKey = _ref5.refKey, refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey, ref = _ref5.ref, disabled = _ref5.disabled, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref5, _excluded3$1);
            var _latest$current = latest.current, latestState = _latest$current.state, latestProps = _latest$current.props;
            var _getItemAndIndex = getItemAndIndex(itemProp, indexProp, latestProps.items, 'Pass either item or index to getItemProps!'), item = _getItemAndIndex[0], index = _getItemAndIndex[1];
            var itemHandleMouseMove = function itemHandleMouseMove() {
                if (index === latestState.highlightedIndex) {
                    return;
                }
                shouldScrollRef.current = false;
                dispatch({
                    type: ItemMouseMove$1,
                    index: index,
                    disabled: disabled
                });
            };
            var itemHandleClick = function itemHandleClick() {
                dispatch({
                    type: ItemClick$1,
                    index: index
                });
            };
            var itemProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((_extends4 = {
                disabled: disabled,
                role: 'option',
                'aria-selected': "" + (item === selectedItem),
                id: elementIds.getItemId(index)
            }, _extends4[refKey] = handleRefs(ref, {
                "useSelect.useCallback[getItemProps].itemProps": function(itemNode) {
                    if (itemNode) {
                        itemRefs.current[elementIds.getItemId(index)] = itemNode;
                    }
                }
            }["useSelect.useCallback[getItemProps].itemProps"]), _extends4), rest);
            if (!disabled) {
                /* istanbul ignore next (react-native) */ {
                    itemProps.onClick = callAllEventHandlers(onClick, itemHandleClick);
                }
            }
            itemProps.onMouseMove = callAllEventHandlers(onMouseMove, itemHandleMouseMove);
            return itemProps;
        }
    }["useSelect.useCallback[getItemProps]"], [
        latest,
        selectedItem,
        elementIds,
        shouldScrollRef,
        dispatch
    ]);
    return {
        // prop getters.
        getToggleButtonProps: getToggleButtonProps,
        getLabelProps: getLabelProps,
        getMenuProps: getMenuProps,
        getItemProps: getItemProps,
        // actions.
        toggleMenu: toggleMenu,
        openMenu: openMenu,
        closeMenu: closeMenu,
        setHighlightedIndex: setHighlightedIndex,
        selectItem: selectItem,
        reset: reset,
        setInputValue: setInputValue,
        // state.
        highlightedIndex: highlightedIndex,
        isOpen: isOpen,
        selectedItem: selectedItem,
        inputValue: inputValue
    };
}
var InputKeyDownArrowDown = ("TURBOPACK compile-time truthy", 1) ? '__input_keydown_arrow_down__' : ("TURBOPACK unreachable", undefined);
var InputKeyDownArrowUp = ("TURBOPACK compile-time truthy", 1) ? '__input_keydown_arrow_up__' : ("TURBOPACK unreachable", undefined);
var InputKeyDownEscape = ("TURBOPACK compile-time truthy", 1) ? '__input_keydown_escape__' : ("TURBOPACK unreachable", undefined);
var InputKeyDownHome = ("TURBOPACK compile-time truthy", 1) ? '__input_keydown_home__' : ("TURBOPACK unreachable", undefined);
var InputKeyDownEnd = ("TURBOPACK compile-time truthy", 1) ? '__input_keydown_end__' : ("TURBOPACK unreachable", undefined);
var InputKeyDownPageUp = ("TURBOPACK compile-time truthy", 1) ? '__input_keydown_page_up__' : ("TURBOPACK unreachable", undefined);
var InputKeyDownPageDown = ("TURBOPACK compile-time truthy", 1) ? '__input_keydown_page_down__' : ("TURBOPACK unreachable", undefined);
var InputKeyDownEnter = ("TURBOPACK compile-time truthy", 1) ? '__input_keydown_enter__' : ("TURBOPACK unreachable", undefined);
var InputChange = ("TURBOPACK compile-time truthy", 1) ? '__input_change__' : ("TURBOPACK unreachable", undefined);
var InputBlur = ("TURBOPACK compile-time truthy", 1) ? '__input_blur__' : ("TURBOPACK unreachable", undefined);
var InputFocus = ("TURBOPACK compile-time truthy", 1) ? '__input_focus__' : ("TURBOPACK unreachable", undefined);
var MenuMouseLeave = ("TURBOPACK compile-time truthy", 1) ? '__menu_mouse_leave__' : ("TURBOPACK unreachable", undefined);
var ItemMouseMove = ("TURBOPACK compile-time truthy", 1) ? '__item_mouse_move__' : ("TURBOPACK unreachable", undefined);
var ItemClick = ("TURBOPACK compile-time truthy", 1) ? '__item_click__' : ("TURBOPACK unreachable", undefined);
var ToggleButtonClick = ("TURBOPACK compile-time truthy", 1) ? '__togglebutton_click__' : ("TURBOPACK unreachable", undefined);
var FunctionToggleMenu = ("TURBOPACK compile-time truthy", 1) ? '__function_toggle_menu__' : ("TURBOPACK unreachable", undefined);
var FunctionOpenMenu = ("TURBOPACK compile-time truthy", 1) ? '__function_open_menu__' : ("TURBOPACK unreachable", undefined);
var FunctionCloseMenu = ("TURBOPACK compile-time truthy", 1) ? '__function_close_menu__' : ("TURBOPACK unreachable", undefined);
var FunctionSetHighlightedIndex = ("TURBOPACK compile-time truthy", 1) ? '__function_set_highlighted_index__' : ("TURBOPACK unreachable", undefined);
var FunctionSelectItem = ("TURBOPACK compile-time truthy", 1) ? '__function_select_item__' : ("TURBOPACK unreachable", undefined);
var FunctionSetInputValue = ("TURBOPACK compile-time truthy", 1) ? '__function_set_input_value__' : ("TURBOPACK unreachable", undefined);
var FunctionReset$1 = ("TURBOPACK compile-time truthy", 1) ? '__function_reset__' : ("TURBOPACK unreachable", undefined);
var ControlledPropUpdatedSelectedItem = ("TURBOPACK compile-time truthy", 1) ? '__controlled_prop_updated_selected_item__' : ("TURBOPACK unreachable", undefined);
var stateChangeTypes$1 = /*#__PURE__*/ Object.freeze({
    __proto__: null,
    InputKeyDownArrowDown: InputKeyDownArrowDown,
    InputKeyDownArrowUp: InputKeyDownArrowUp,
    InputKeyDownEscape: InputKeyDownEscape,
    InputKeyDownHome: InputKeyDownHome,
    InputKeyDownEnd: InputKeyDownEnd,
    InputKeyDownPageUp: InputKeyDownPageUp,
    InputKeyDownPageDown: InputKeyDownPageDown,
    InputKeyDownEnter: InputKeyDownEnter,
    InputChange: InputChange,
    InputBlur: InputBlur,
    InputFocus: InputFocus,
    MenuMouseLeave: MenuMouseLeave,
    ItemMouseMove: ItemMouseMove,
    ItemClick: ItemClick,
    ToggleButtonClick: ToggleButtonClick,
    FunctionToggleMenu: FunctionToggleMenu,
    FunctionOpenMenu: FunctionOpenMenu,
    FunctionCloseMenu: FunctionCloseMenu,
    FunctionSetHighlightedIndex: FunctionSetHighlightedIndex,
    FunctionSelectItem: FunctionSelectItem,
    FunctionSetInputValue: FunctionSetInputValue,
    FunctionReset: FunctionReset$1,
    ControlledPropUpdatedSelectedItem: ControlledPropUpdatedSelectedItem
});
function getInitialState$1(props) {
    var initialState = getInitialState$2(props);
    var selectedItem = initialState.selectedItem;
    var inputValue = initialState.inputValue;
    if (inputValue === '' && selectedItem && props.defaultInputValue === undefined && props.initialInputValue === undefined && props.inputValue === undefined) {
        inputValue = props.itemToString(selectedItem);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, initialState, {
        inputValue: inputValue
    });
}
var propTypes$1 = {
    items: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].array.isRequired,
    itemToString: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    selectedItemChanged: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    getA11yStatusMessage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    getA11ySelectionMessage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    highlightedIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    defaultHighlightedIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    initialHighlightedIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    isOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bool,
    defaultIsOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bool,
    initialIsOpen: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bool,
    selectedItem: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
    initialSelectedItem: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
    defaultSelectedItem: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
    inputValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    defaultInputValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    initialInputValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    labelId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    menuId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    getItemId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    inputId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    toggleButtonId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    stateReducer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onSelectedItemChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onHighlightedIndexChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onStateChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onIsOpenChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onInputValueChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    environment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].shape({
        addEventListener: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
        removeEventListener: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
        document: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].shape({
            getElementById: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
            activeElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
            body: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any
        })
    })
};
/**
 * The useCombobox version of useControlledReducer, which also
 * checks if the controlled prop selectedItem changed between
 * renders. If so, it will also update inputValue with its
 * string equivalent. It uses the common useEnhancedReducer to
 * compute the rest of the state.
 *
 * @param {Function} reducer Reducer function from downshift.
 * @param {Object} initialState Initial state of the hook.
 * @param {Object} props The hook props.
 * @returns {Array} An array with the state and an action dispatcher.
 */ function useControlledReducer(reducer, initialState, props) {
    var previousSelectedItemRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    var _useEnhancedReducer = useEnhancedReducer(reducer, initialState, props), state = _useEnhancedReducer[0], dispatch = _useEnhancedReducer[1];
    // ToDo: if needed, make same approach as selectedItemChanged from Downshift.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useControlledReducer.useEffect": function() {
            if (!isControlledProp(props, 'selectedItem')) {
                return;
            }
            if (props.selectedItemChanged(previousSelectedItemRef.current, props.selectedItem)) {
                dispatch({
                    type: ControlledPropUpdatedSelectedItem,
                    inputValue: props.itemToString(props.selectedItem)
                });
            }
            previousSelectedItemRef.current = state.selectedItem === previousSelectedItemRef.current ? props.selectedItem : state.selectedItem;
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["useControlledReducer.useEffect"], [
        state.selectedItem,
        props.selectedItem
    ]);
    return [
        getState(state, props),
        dispatch
    ];
}
// eslint-disable-next-line import/no-mutable-exports
var validatePropTypes$1 = noop;
/* istanbul ignore next */ if ("TURBOPACK compile-time truthy", 1) {
    validatePropTypes$1 = function validatePropTypes(options, caller) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].checkPropTypes(propTypes$1, options, 'prop', caller.name);
    };
}
var defaultProps$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, defaultProps$3, {
    selectedItemChanged: function selectedItemChanged(prevItem, item) {
        return prevItem !== item;
    },
    getA11yStatusMessage: getA11yStatusMessage$1
});
/* eslint-disable complexity */ function downshiftUseComboboxReducer(state, action) {
    var _props$items;
    var type = action.type, props = action.props, altKey = action.altKey;
    var changes;
    switch(type){
        case ItemClick:
            changes = {
                isOpen: getDefaultValue$1(props, 'isOpen'),
                highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),
                selectedItem: props.items[action.index],
                inputValue: props.itemToString(props.items[action.index])
            };
            break;
        case InputKeyDownArrowDown:
            if (state.isOpen) {
                changes = {
                    highlightedIndex: getNextWrappingIndex(1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, true)
                };
            } else {
                changes = {
                    highlightedIndex: altKey && state.selectedItem == null ? -1 : getHighlightedIndexOnOpen(props, state, 1, action.getItemNodeFromIndex),
                    isOpen: props.items.length >= 0
                };
            }
            break;
        case InputKeyDownArrowUp:
            if (state.isOpen) {
                if (altKey) {
                    changes = getChangesOnSelection(props, state.highlightedIndex);
                } else {
                    changes = {
                        highlightedIndex: getNextWrappingIndex(-1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, true)
                    };
                }
            } else {
                changes = {
                    highlightedIndex: getHighlightedIndexOnOpen(props, state, -1, action.getItemNodeFromIndex),
                    isOpen: props.items.length >= 0
                };
            }
            break;
        case InputKeyDownEnter:
            changes = getChangesOnSelection(props, state.highlightedIndex);
            break;
        case InputKeyDownEscape:
            changes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                isOpen: false,
                highlightedIndex: -1
            }, !state.isOpen && {
                selectedItem: null,
                inputValue: ''
            });
            break;
        case InputKeyDownPageUp:
            changes = {
                highlightedIndex: getNextWrappingIndex(-10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)
            };
            break;
        case InputKeyDownPageDown:
            changes = {
                highlightedIndex: getNextWrappingIndex(10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)
            };
            break;
        case InputKeyDownHome:
            changes = {
                highlightedIndex: getNextNonDisabledIndex(1, 0, props.items.length, action.getItemNodeFromIndex, false)
            };
            break;
        case InputKeyDownEnd:
            changes = {
                highlightedIndex: getNextNonDisabledIndex(-1, props.items.length - 1, props.items.length, action.getItemNodeFromIndex, false)
            };
            break;
        case InputBlur:
            changes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                isOpen: false,
                highlightedIndex: -1
            }, state.highlightedIndex >= 0 && ((_props$items = props.items) == null ? void 0 : _props$items.length) && action.selectItem && {
                selectedItem: props.items[state.highlightedIndex],
                inputValue: props.itemToString(props.items[state.highlightedIndex])
            });
            break;
        case InputChange:
            changes = {
                isOpen: true,
                highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),
                inputValue: action.inputValue
            };
            break;
        case InputFocus:
            changes = {
                isOpen: true,
                highlightedIndex: getHighlightedIndexOnOpen(props, state, 0)
            };
            break;
        case FunctionSelectItem:
            changes = {
                selectedItem: action.selectedItem,
                inputValue: props.itemToString(action.selectedItem)
            };
            break;
        case ControlledPropUpdatedSelectedItem:
            changes = {
                inputValue: action.inputValue
            };
            break;
        default:
            return downshiftCommonReducer(state, action, stateChangeTypes$1);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, state, changes);
}
/* eslint-enable complexity */ var _excluded$1 = [
    "onMouseLeave",
    "refKey",
    "ref"
], _excluded2$1 = [
    "item",
    "index",
    "refKey",
    "ref",
    "onMouseMove",
    "onMouseDown",
    "onClick",
    "onPress",
    "disabled"
], _excluded3 = [
    "onClick",
    "onPress",
    "refKey",
    "ref"
], _excluded4 = [
    "onKeyDown",
    "onChange",
    "onInput",
    "onFocus",
    "onBlur",
    "onChangeText",
    "refKey",
    "ref"
];
useCombobox.stateChangeTypes = stateChangeTypes$1;
function useCombobox(userProps) {
    if (userProps === void 0) {
        userProps = {};
    }
    validatePropTypes$1(userProps, useCombobox);
    // Props defaults and destructuring.
    var props = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, defaultProps$1, userProps);
    var initialIsOpen = props.initialIsOpen, defaultIsOpen = props.defaultIsOpen, items = props.items, scrollIntoView = props.scrollIntoView, environment = props.environment, getA11yStatusMessage = props.getA11yStatusMessage, getA11ySelectionMessage = props.getA11ySelectionMessage, itemToString = props.itemToString;
    // Initial state depending on controlled props.
    var initialState = getInitialState$1(props);
    var _useControlledReducer = useControlledReducer(downshiftUseComboboxReducer, initialState, props), state = _useControlledReducer[0], dispatch = _useControlledReducer[1];
    var isOpen = state.isOpen, highlightedIndex = state.highlightedIndex, selectedItem = state.selectedItem, inputValue = state.inputValue;
    // Element refs.
    var menuRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    var itemRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({});
    var inputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    var toggleButtonRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    var isInitialMountRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(true);
    // prevent id re-generation between renders.
    var elementIds = useElementIds(props);
    // used to keep track of how many items we had on previous cycle.
    var previousResultCountRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    // utility callback to get item element.
    var latest = useLatestRef({
        state: state,
        props: props
    });
    var getItemNodeFromIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[getItemNodeFromIndex]": function(index) {
            return itemRefs.current[elementIds.getItemId(index)];
        }
    }["useCombobox.useCallback[getItemNodeFromIndex]"], [
        elementIds
    ]);
    // Effects.
    // Sets a11y status message on changes in state.
    useA11yMessageSetter(getA11yStatusMessage, [
        isOpen,
        highlightedIndex,
        inputValue,
        items
    ], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        isInitialMount: isInitialMountRef.current,
        previousResultCount: previousResultCountRef.current,
        items: items,
        environment: environment,
        itemToString: itemToString
    }, state));
    // Sets a11y status message on changes in selectedItem.
    useA11yMessageSetter(getA11ySelectionMessage, [
        selectedItem
    ], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        isInitialMount: isInitialMountRef.current,
        previousResultCount: previousResultCountRef.current,
        items: items,
        environment: environment,
        itemToString: itemToString
    }, state));
    // Scroll on highlighted item if change comes from keyboard.
    var shouldScrollRef = useScrollIntoView({
        menuElement: menuRef.current,
        highlightedIndex: highlightedIndex,
        isOpen: isOpen,
        itemRefs: itemRefs,
        scrollIntoView: scrollIntoView,
        getItemNodeFromIndex: getItemNodeFromIndex
    });
    useControlPropsValidator({
        isInitialMount: isInitialMountRef.current,
        props: props,
        state: state
    });
    // Focus the input on first render if required.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCombobox.useEffect": function() {
            var focusOnOpen = initialIsOpen || defaultIsOpen || isOpen;
            if (focusOnOpen && inputRef.current) {
                inputRef.current.focus();
            }
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["useCombobox.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCombobox.useEffect": function() {
            if (isInitialMountRef.current) {
                return;
            }
            previousResultCountRef.current = items.length;
        }
    }["useCombobox.useEffect"]);
    // Add mouse/touch events to document.
    var mouseAndTouchTrackersRef = useMouseAndTouchTracker(isOpen, [
        inputRef,
        menuRef,
        toggleButtonRef
    ], environment, {
        "useCombobox.useMouseAndTouchTracker[mouseAndTouchTrackersRef]": function() {
            dispatch({
                type: InputBlur,
                selectItem: false
            });
        }
    }["useCombobox.useMouseAndTouchTracker[mouseAndTouchTrackersRef]"]);
    var setGetterPropCallInfo = useGetterPropsCalledChecker('getInputProps', 'getMenuProps');
    // Make initial ref false.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCombobox.useEffect": function() {
            isInitialMountRef.current = false;
            return ({
                "useCombobox.useEffect": function() {
                    isInitialMountRef.current = true;
                }
            })["useCombobox.useEffect"];
        }
    }["useCombobox.useEffect"], []);
    // Reset itemRefs on close.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCombobox.useEffect": function() {
            var _environment$document;
            if (!isOpen) {
                itemRefs.current = {};
            } else if (((_environment$document = environment.document) == null ? void 0 : _environment$document.activeElement) !== inputRef.current) {
                var _inputRef$current;
                inputRef == null || (_inputRef$current = inputRef.current) == null ? void 0 : _inputRef$current.focus();
            }
        }
    }["useCombobox.useEffect"], [
        isOpen,
        environment
    ]);
    /* Event handler functions */ var inputKeyDownHandlers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useCombobox.useMemo[inputKeyDownHandlers]": function() {
            return {
                ArrowDown: function ArrowDown(event) {
                    event.preventDefault();
                    dispatch({
                        type: InputKeyDownArrowDown,
                        altKey: event.altKey,
                        getItemNodeFromIndex: getItemNodeFromIndex
                    });
                },
                ArrowUp: function ArrowUp(event) {
                    event.preventDefault();
                    dispatch({
                        type: InputKeyDownArrowUp,
                        altKey: event.altKey,
                        getItemNodeFromIndex: getItemNodeFromIndex
                    });
                },
                Home: function Home(event) {
                    if (!latest.current.state.isOpen) {
                        return;
                    }
                    event.preventDefault();
                    dispatch({
                        type: InputKeyDownHome,
                        getItemNodeFromIndex: getItemNodeFromIndex
                    });
                },
                End: function End(event) {
                    if (!latest.current.state.isOpen) {
                        return;
                    }
                    event.preventDefault();
                    dispatch({
                        type: InputKeyDownEnd,
                        getItemNodeFromIndex: getItemNodeFromIndex
                    });
                },
                Escape: function Escape(event) {
                    var latestState = latest.current.state;
                    if (latestState.isOpen || latestState.inputValue || latestState.selectedItem || latestState.highlightedIndex > -1) {
                        event.preventDefault();
                        dispatch({
                            type: InputKeyDownEscape
                        });
                    }
                },
                Enter: function Enter(event) {
                    var latestState = latest.current.state;
                    // if closed or no highlighted index, do nothing.
                    if (!latestState.isOpen || event.which === 229 // if IME composing, wait for next Enter keydown event.
                    ) {
                        return;
                    }
                    event.preventDefault();
                    dispatch({
                        type: InputKeyDownEnter,
                        getItemNodeFromIndex: getItemNodeFromIndex
                    });
                },
                PageUp: function PageUp(event) {
                    if (latest.current.state.isOpen) {
                        event.preventDefault();
                        dispatch({
                            type: InputKeyDownPageUp,
                            getItemNodeFromIndex: getItemNodeFromIndex
                        });
                    }
                },
                PageDown: function PageDown(event) {
                    if (latest.current.state.isOpen) {
                        event.preventDefault();
                        dispatch({
                            type: InputKeyDownPageDown,
                            getItemNodeFromIndex: getItemNodeFromIndex
                        });
                    }
                }
            };
        }
    }["useCombobox.useMemo[inputKeyDownHandlers]"], [
        dispatch,
        latest,
        getItemNodeFromIndex
    ]);
    // Getter props.
    var getLabelProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[getLabelProps]": function(labelProps) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                id: elementIds.labelId,
                htmlFor: elementIds.inputId
            }, labelProps);
        }
    }["useCombobox.useCallback[getLabelProps]"], [
        elementIds
    ]);
    var getMenuProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[getMenuProps]": function(_temp, _temp2) {
            var _extends2;
            var _ref = _temp === void 0 ? {} : _temp, onMouseLeave = _ref.onMouseLeave, _ref$refKey = _ref.refKey, refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey, ref = _ref.ref, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref, _excluded$1);
            var _ref2 = _temp2 === void 0 ? {} : _temp2, _ref2$suppressRefErro = _ref2.suppressRefError, suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;
            setGetterPropCallInfo('getMenuProps', suppressRefError, refKey, menuRef);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((_extends2 = {}, _extends2[refKey] = handleRefs(ref, {
                "useCombobox.useCallback[getMenuProps]": function(menuNode) {
                    menuRef.current = menuNode;
                }
            }["useCombobox.useCallback[getMenuProps]"]), _extends2.id = elementIds.menuId, _extends2.role = 'listbox', _extends2['aria-labelledby'] = rest && rest['aria-label'] ? undefined : "" + elementIds.labelId, _extends2.onMouseLeave = callAllEventHandlers(onMouseLeave, {
                "useCombobox.useCallback[getMenuProps]": function() {
                    dispatch({
                        type: MenuMouseLeave
                    });
                }
            }["useCombobox.useCallback[getMenuProps]"]), _extends2), rest);
        }
    }["useCombobox.useCallback[getMenuProps]"], [
        dispatch,
        setGetterPropCallInfo,
        elementIds
    ]);
    var getItemProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[getItemProps]": function(_temp3) {
            var _extends3, _ref4;
            var _ref3 = _temp3 === void 0 ? {} : _temp3, itemProp = _ref3.item, indexProp = _ref3.index, _ref3$refKey = _ref3.refKey, refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey, ref = _ref3.ref, onMouseMove = _ref3.onMouseMove, onMouseDown = _ref3.onMouseDown, onClick = _ref3.onClick;
            _ref3.onPress;
            var disabled = _ref3.disabled, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref3, _excluded2$1);
            var _latest$current = latest.current, latestProps = _latest$current.props, latestState = _latest$current.state;
            var _getItemAndIndex = getItemAndIndex(itemProp, indexProp, latestProps.items, 'Pass either item or index to getItemProps!'), index = _getItemAndIndex[1];
            var onSelectKey = 'onClick';
            var customClickHandler = onClick;
            var itemHandleMouseMove = function itemHandleMouseMove() {
                if (index === latestState.highlightedIndex) {
                    return;
                }
                shouldScrollRef.current = false;
                dispatch({
                    type: ItemMouseMove,
                    index: index,
                    disabled: disabled
                });
            };
            var itemHandleClick = function itemHandleClick() {
                dispatch({
                    type: ItemClick,
                    index: index
                });
            };
            var itemHandleMouseDown = function itemHandleMouseDown(e) {
                return e.preventDefault();
            };
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((_extends3 = {}, _extends3[refKey] = handleRefs(ref, {
                "useCombobox.useCallback[getItemProps]": function(itemNode) {
                    if (itemNode) {
                        itemRefs.current[elementIds.getItemId(index)] = itemNode;
                    }
                }
            }["useCombobox.useCallback[getItemProps]"]), _extends3.disabled = disabled, _extends3.role = 'option', _extends3['aria-selected'] = "" + (index === latestState.highlightedIndex), _extends3.id = elementIds.getItemId(index), _extends3), !disabled && (_ref4 = {}, _ref4[onSelectKey] = callAllEventHandlers(customClickHandler, itemHandleClick), _ref4), {
                onMouseMove: callAllEventHandlers(onMouseMove, itemHandleMouseMove),
                onMouseDown: callAllEventHandlers(onMouseDown, itemHandleMouseDown)
            }, rest);
        }
    }["useCombobox.useCallback[getItemProps]"], [
        dispatch,
        latest,
        shouldScrollRef,
        elementIds
    ]);
    var getToggleButtonProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[getToggleButtonProps]": function(_temp4) {
            var _extends4;
            var _ref5 = _temp4 === void 0 ? {} : _temp4, onClick = _ref5.onClick;
            _ref5.onPress;
            var _ref5$refKey = _ref5.refKey, refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey, ref = _ref5.ref, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref5, _excluded3);
            var latestState = latest.current.state;
            var toggleButtonHandleClick = function toggleButtonHandleClick() {
                dispatch({
                    type: ToggleButtonClick
                });
            };
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((_extends4 = {}, _extends4[refKey] = handleRefs(ref, {
                "useCombobox.useCallback[getToggleButtonProps]": function(toggleButtonNode) {
                    toggleButtonRef.current = toggleButtonNode;
                }
            }["useCombobox.useCallback[getToggleButtonProps]"]), _extends4['aria-controls'] = elementIds.menuId, _extends4['aria-expanded'] = latestState.isOpen, _extends4.id = elementIds.toggleButtonId, _extends4.tabIndex = -1, _extends4), !rest.disabled && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, {
                onClick: callAllEventHandlers(onClick, toggleButtonHandleClick)
            }), rest);
        }
    }["useCombobox.useCallback[getToggleButtonProps]"], [
        dispatch,
        latest,
        elementIds
    ]);
    var getInputProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[getInputProps]": function(_temp5, _temp6) {
            var _extends5;
            var _ref6 = _temp5 === void 0 ? {} : _temp5, onKeyDown = _ref6.onKeyDown, onChange = _ref6.onChange, onInput = _ref6.onInput, onFocus = _ref6.onFocus, onBlur = _ref6.onBlur;
            _ref6.onChangeText;
            var _ref6$refKey = _ref6.refKey, refKey = _ref6$refKey === void 0 ? 'ref' : _ref6$refKey, ref = _ref6.ref, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref6, _excluded4);
            var _ref7 = _temp6 === void 0 ? {} : _temp6, _ref7$suppressRefErro = _ref7.suppressRefError, suppressRefError = _ref7$suppressRefErro === void 0 ? false : _ref7$suppressRefErro;
            setGetterPropCallInfo('getInputProps', suppressRefError, refKey, inputRef);
            var latestState = latest.current.state;
            var inputHandleKeyDown = function inputHandleKeyDown(event) {
                var key = normalizeArrowKey(event);
                if (key && inputKeyDownHandlers[key]) {
                    inputKeyDownHandlers[key](event);
                }
            };
            var inputHandleChange = function inputHandleChange(event) {
                dispatch({
                    type: InputChange,
                    inputValue: event.target.value
                });
            };
            var inputHandleBlur = function inputHandleBlur(event) {
                /* istanbul ignore else */ if (latestState.isOpen && !mouseAndTouchTrackersRef.current.isMouseDown) {
                    var isBlurByTabChange = event.relatedTarget === null && environment.document.activeElement !== environment.document.body;
                    dispatch({
                        type: InputBlur,
                        selectItem: !isBlurByTabChange
                    });
                }
            };
            var inputHandleFocus = function inputHandleFocus() {
                if (!latestState.isOpen) {
                    dispatch({
                        type: InputFocus
                    });
                }
            };
            /* istanbul ignore next (preact) */ var onChangeKey = 'onChange';
            var eventHandlers = {};
            if (!rest.disabled) {
                var _eventHandlers;
                eventHandlers = (_eventHandlers = {}, _eventHandlers[onChangeKey] = callAllEventHandlers(onChange, onInput, inputHandleChange), _eventHandlers.onKeyDown = callAllEventHandlers(onKeyDown, inputHandleKeyDown), _eventHandlers.onBlur = callAllEventHandlers(onBlur, inputHandleBlur), _eventHandlers.onFocus = callAllEventHandlers(onFocus, inputHandleFocus), _eventHandlers);
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((_extends5 = {}, _extends5[refKey] = handleRefs(ref, {
                "useCombobox.useCallback[getInputProps]": function(inputNode) {
                    inputRef.current = inputNode;
                }
            }["useCombobox.useCallback[getInputProps]"]), _extends5['aria-activedescendant'] = latestState.isOpen && latestState.highlightedIndex > -1 ? elementIds.getItemId(latestState.highlightedIndex) : '', _extends5['aria-autocomplete'] = 'list', _extends5['aria-controls'] = elementIds.menuId, _extends5['aria-expanded'] = latestState.isOpen, _extends5['aria-labelledby'] = rest && rest['aria-label'] ? undefined : "" + elementIds.labelId, _extends5.autoComplete = 'off', _extends5.id = elementIds.inputId, _extends5.role = 'combobox', _extends5.value = latestState.inputValue, _extends5), eventHandlers, rest);
        }
    }["useCombobox.useCallback[getInputProps]"], [
        setGetterPropCallInfo,
        latest,
        elementIds,
        inputKeyDownHandlers,
        dispatch,
        mouseAndTouchTrackersRef,
        environment
    ]);
    // returns
    var toggleMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[toggleMenu]": function() {
            dispatch({
                type: FunctionToggleMenu
            });
        }
    }["useCombobox.useCallback[toggleMenu]"], [
        dispatch
    ]);
    var closeMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[closeMenu]": function() {
            dispatch({
                type: FunctionCloseMenu
            });
        }
    }["useCombobox.useCallback[closeMenu]"], [
        dispatch
    ]);
    var openMenu = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[openMenu]": function() {
            dispatch({
                type: FunctionOpenMenu
            });
        }
    }["useCombobox.useCallback[openMenu]"], [
        dispatch
    ]);
    var setHighlightedIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[setHighlightedIndex]": function(newHighlightedIndex) {
            dispatch({
                type: FunctionSetHighlightedIndex,
                highlightedIndex: newHighlightedIndex
            });
        }
    }["useCombobox.useCallback[setHighlightedIndex]"], [
        dispatch
    ]);
    var selectItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[selectItem]": function(newSelectedItem) {
            dispatch({
                type: FunctionSelectItem,
                selectedItem: newSelectedItem
            });
        }
    }["useCombobox.useCallback[selectItem]"], [
        dispatch
    ]);
    var setInputValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[setInputValue]": function(newInputValue) {
            dispatch({
                type: FunctionSetInputValue,
                inputValue: newInputValue
            });
        }
    }["useCombobox.useCallback[setInputValue]"], [
        dispatch
    ]);
    var reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCombobox.useCallback[reset]": function() {
            dispatch({
                type: FunctionReset$1
            });
        }
    }["useCombobox.useCallback[reset]"], [
        dispatch
    ]);
    return {
        // prop getters.
        getItemProps: getItemProps,
        getLabelProps: getLabelProps,
        getMenuProps: getMenuProps,
        getInputProps: getInputProps,
        getToggleButtonProps: getToggleButtonProps,
        // actions.
        toggleMenu: toggleMenu,
        openMenu: openMenu,
        closeMenu: closeMenu,
        setHighlightedIndex: setHighlightedIndex,
        setInputValue: setInputValue,
        selectItem: selectItem,
        reset: reset,
        // state.
        highlightedIndex: highlightedIndex,
        isOpen: isOpen,
        selectedItem: selectedItem,
        inputValue: inputValue
    };
}
var defaultStateValues = {
    activeIndex: -1,
    selectedItems: []
};
/**
 * Returns the initial value for a state key in the following order:
 * 1. controlled prop, 2. initial prop, 3. default prop, 4. default
 * value from Downshift.
 *
 * @param {Object} props Props passed to the hook.
 * @param {string} propKey Props key to generate the value for.
 * @returns {any} The initial value for that prop.
 */ function getInitialValue(props, propKey) {
    return getInitialValue$1(props, propKey, defaultStateValues);
}
/**
 * Returns the default value for a state key in the following order:
 * 1. controlled prop, 2. default prop, 3. default value from Downshift.
 *
 * @param {Object} props Props passed to the hook.
 * @param {string} propKey Props key to generate the value for.
 * @returns {any} The initial value for that prop.
 */ function getDefaultValue(props, propKey) {
    return getDefaultValue$1(props, propKey, defaultStateValues);
}
/**
 * Gets the initial state based on the provided props. It uses initial, default
 * and controlled props related to state in order to compute the initial value.
 *
 * @param {Object} props Props passed to the hook.
 * @returns {Object} The initial state.
 */ function getInitialState(props) {
    var activeIndex = getInitialValue(props, 'activeIndex');
    var selectedItems = getInitialValue(props, 'selectedItems');
    return {
        activeIndex: activeIndex,
        selectedItems: selectedItems
    };
}
/**
 * Returns true if dropdown keydown operation is permitted. Should not be
 * allowed on keydown with modifier keys (ctrl, alt, shift, meta), on
 * input element with text content that is either highlighted or selection
 * cursor is not at the starting position.
 *
 * @param {KeyboardEvent} event The event from keydown.
 * @returns {boolean} Whether the operation is allowed.
 */ function isKeyDownOperationPermitted(event) {
    if (event.shiftKey || event.metaKey || event.ctrlKey || event.altKey) {
        return false;
    }
    var element = event.target;
    if (element instanceof HTMLInputElement && // if element is a text input
    element.value !== '' && (// and we have text in it
    // and cursor is either not at the start or is currently highlighting text.
    element.selectionStart !== 0 || element.selectionEnd !== 0)) {
        return false;
    }
    return true;
}
/**
 * Returns a message to be added to aria-live region when item is removed.
 *
 * @param {Object} selectionParameters Parameters required to build the message.
 * @returns {string} The a11y message.
 */ function getA11yRemovalMessage(selectionParameters) {
    var removedSelectedItem = selectionParameters.removedSelectedItem, itemToStringLocal = selectionParameters.itemToString;
    return itemToStringLocal(removedSelectedItem) + " has been removed.";
}
var propTypes = {
    selectedItems: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].array,
    initialSelectedItems: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].array,
    defaultSelectedItems: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].array,
    itemToString: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    getA11yRemovalMessage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    stateReducer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    activeIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    initialActiveIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    defaultActiveIndex: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].number,
    onActiveIndexChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    onSelectedItemsChange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
    keyNavigationNext: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    keyNavigationPrevious: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].string,
    environment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].shape({
        addEventListener: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
        removeEventListener: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
        document: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].shape({
            getElementById: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].func,
            activeElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any,
            body: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].any
        })
    })
};
var defaultProps = {
    itemToString: defaultProps$3.itemToString,
    stateReducer: defaultProps$3.stateReducer,
    environment: defaultProps$3.environment,
    getA11yRemovalMessage: getA11yRemovalMessage,
    keyNavigationNext: 'ArrowRight',
    keyNavigationPrevious: 'ArrowLeft'
};
// eslint-disable-next-line import/no-mutable-exports
var validatePropTypes = noop;
/* istanbul ignore next */ if ("TURBOPACK compile-time truthy", 1) {
    validatePropTypes = function validatePropTypes(options, caller) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prop$2d$types$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].checkPropTypes(propTypes, options, 'prop', caller.name);
    };
}
var SelectedItemClick = ("TURBOPACK compile-time truthy", 1) ? '__selected_item_click__' : ("TURBOPACK unreachable", undefined);
var SelectedItemKeyDownDelete = ("TURBOPACK compile-time truthy", 1) ? '__selected_item_keydown_delete__' : ("TURBOPACK unreachable", undefined);
var SelectedItemKeyDownBackspace = ("TURBOPACK compile-time truthy", 1) ? '__selected_item_keydown_backspace__' : ("TURBOPACK unreachable", undefined);
var SelectedItemKeyDownNavigationNext = ("TURBOPACK compile-time truthy", 1) ? '__selected_item_keydown_navigation_next__' : ("TURBOPACK unreachable", undefined);
var SelectedItemKeyDownNavigationPrevious = ("TURBOPACK compile-time truthy", 1) ? '__selected_item_keydown_navigation_previous__' : ("TURBOPACK unreachable", undefined);
var DropdownKeyDownNavigationPrevious = ("TURBOPACK compile-time truthy", 1) ? '__dropdown_keydown_navigation_previous__' : ("TURBOPACK unreachable", undefined);
var DropdownKeyDownBackspace = ("TURBOPACK compile-time truthy", 1) ? '__dropdown_keydown_backspace__' : ("TURBOPACK unreachable", undefined);
var DropdownClick = ("TURBOPACK compile-time truthy", 1) ? '__dropdown_click__' : ("TURBOPACK unreachable", undefined);
var FunctionAddSelectedItem = ("TURBOPACK compile-time truthy", 1) ? '__function_add_selected_item__' : ("TURBOPACK unreachable", undefined);
var FunctionRemoveSelectedItem = ("TURBOPACK compile-time truthy", 1) ? '__function_remove_selected_item__' : ("TURBOPACK unreachable", undefined);
var FunctionSetSelectedItems = ("TURBOPACK compile-time truthy", 1) ? '__function_set_selected_items__' : ("TURBOPACK unreachable", undefined);
var FunctionSetActiveIndex = ("TURBOPACK compile-time truthy", 1) ? '__function_set_active_index__' : ("TURBOPACK unreachable", undefined);
var FunctionReset = ("TURBOPACK compile-time truthy", 1) ? '__function_reset__' : ("TURBOPACK unreachable", undefined);
var stateChangeTypes = /*#__PURE__*/ Object.freeze({
    __proto__: null,
    SelectedItemClick: SelectedItemClick,
    SelectedItemKeyDownDelete: SelectedItemKeyDownDelete,
    SelectedItemKeyDownBackspace: SelectedItemKeyDownBackspace,
    SelectedItemKeyDownNavigationNext: SelectedItemKeyDownNavigationNext,
    SelectedItemKeyDownNavigationPrevious: SelectedItemKeyDownNavigationPrevious,
    DropdownKeyDownNavigationPrevious: DropdownKeyDownNavigationPrevious,
    DropdownKeyDownBackspace: DropdownKeyDownBackspace,
    DropdownClick: DropdownClick,
    FunctionAddSelectedItem: FunctionAddSelectedItem,
    FunctionRemoveSelectedItem: FunctionRemoveSelectedItem,
    FunctionSetSelectedItems: FunctionSetSelectedItems,
    FunctionSetActiveIndex: FunctionSetActiveIndex,
    FunctionReset: FunctionReset
});
/* eslint-disable complexity */ function downshiftMultipleSelectionReducer(state, action) {
    var type = action.type, index = action.index, props = action.props, selectedItem = action.selectedItem;
    var activeIndex = state.activeIndex, selectedItems = state.selectedItems;
    var changes;
    switch(type){
        case SelectedItemClick:
            changes = {
                activeIndex: index
            };
            break;
        case SelectedItemKeyDownNavigationPrevious:
            changes = {
                activeIndex: activeIndex - 1 < 0 ? 0 : activeIndex - 1
            };
            break;
        case SelectedItemKeyDownNavigationNext:
            changes = {
                activeIndex: activeIndex + 1 >= selectedItems.length ? -1 : activeIndex + 1
            };
            break;
        case SelectedItemKeyDownBackspace:
        case SelectedItemKeyDownDelete:
            {
                if (activeIndex < 0) {
                    break;
                }
                var newActiveIndex = activeIndex;
                if (selectedItems.length === 1) {
                    newActiveIndex = -1;
                } else if (activeIndex === selectedItems.length - 1) {
                    newActiveIndex = selectedItems.length - 2;
                }
                changes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                    selectedItems: [].concat(selectedItems.slice(0, activeIndex), selectedItems.slice(activeIndex + 1))
                }, {
                    activeIndex: newActiveIndex
                });
                break;
            }
        case DropdownKeyDownNavigationPrevious:
            changes = {
                activeIndex: selectedItems.length - 1
            };
            break;
        case DropdownKeyDownBackspace:
            changes = {
                selectedItems: selectedItems.slice(0, selectedItems.length - 1)
            };
            break;
        case FunctionAddSelectedItem:
            changes = {
                selectedItems: [].concat(selectedItems, [
                    selectedItem
                ])
            };
            break;
        case DropdownClick:
            changes = {
                activeIndex: -1
            };
            break;
        case FunctionRemoveSelectedItem:
            {
                var _newActiveIndex = activeIndex;
                var selectedItemIndex = selectedItems.indexOf(selectedItem);
                if (selectedItemIndex < 0) {
                    break;
                }
                if (selectedItems.length === 1) {
                    _newActiveIndex = -1;
                } else if (selectedItemIndex === selectedItems.length - 1) {
                    _newActiveIndex = selectedItems.length - 2;
                }
                changes = {
                    selectedItems: [].concat(selectedItems.slice(0, selectedItemIndex), selectedItems.slice(selectedItemIndex + 1)),
                    activeIndex: _newActiveIndex
                };
                break;
            }
        case FunctionSetSelectedItems:
            {
                var newSelectedItems = action.selectedItems;
                changes = {
                    selectedItems: newSelectedItems
                };
                break;
            }
        case FunctionSetActiveIndex:
            {
                var _newActiveIndex2 = action.activeIndex;
                changes = {
                    activeIndex: _newActiveIndex2
                };
                break;
            }
        case FunctionReset:
            changes = {
                activeIndex: getDefaultValue(props, 'activeIndex'),
                selectedItems: getDefaultValue(props, 'selectedItems')
            };
            break;
        default:
            throw new Error('Reducer called without proper action type.');
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, state, changes);
}
var _excluded = [
    "refKey",
    "ref",
    "onClick",
    "onKeyDown",
    "selectedItem",
    "index"
], _excluded2 = [
    "refKey",
    "ref",
    "onKeyDown",
    "onClick",
    "preventKeyAction"
];
useMultipleSelection.stateChangeTypes = stateChangeTypes;
function useMultipleSelection(userProps) {
    if (userProps === void 0) {
        userProps = {};
    }
    validatePropTypes(userProps, useMultipleSelection);
    // Props defaults and destructuring.
    var props = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, defaultProps, userProps);
    var getA11yRemovalMessage = props.getA11yRemovalMessage, itemToString = props.itemToString, environment = props.environment, keyNavigationNext = props.keyNavigationNext, keyNavigationPrevious = props.keyNavigationPrevious;
    // Reducer init.
    var _useControlledReducer = useControlledReducer$1(downshiftMultipleSelectionReducer, getInitialState(props), props), state = _useControlledReducer[0], dispatch = _useControlledReducer[1];
    var activeIndex = state.activeIndex, selectedItems = state.selectedItems;
    // Refs.
    var isInitialMountRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(true);
    var dropdownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    var previousSelectedItemsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(selectedItems);
    var selectedItemRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    selectedItemRefs.current = [];
    var latest = useLatestRef({
        state: state,
        props: props
    });
    // Effects.
    /* Sets a11y status message on changes in selectedItem. */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useMultipleSelection.useEffect": function() {
            if (isInitialMountRef.current || false) {
                return;
            }
            if (selectedItems.length < previousSelectedItemsRef.current.length) {
                var removedSelectedItem = previousSelectedItemsRef.current.find({
                    "useMultipleSelection.useEffect.removedSelectedItem": function(item) {
                        return selectedItems.indexOf(item) < 0;
                    }
                }["useMultipleSelection.useEffect.removedSelectedItem"]);
                setStatus(getA11yRemovalMessage({
                    itemToString: itemToString,
                    resultCount: selectedItems.length,
                    removedSelectedItem: removedSelectedItem,
                    activeIndex: activeIndex,
                    activeSelectedItem: selectedItems[activeIndex]
                }), environment.document);
            }
            previousSelectedItemsRef.current = selectedItems;
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["useMultipleSelection.useEffect"], [
        selectedItems.length
    ]);
    // Sets focus on active item.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useMultipleSelection.useEffect": function() {
            if (isInitialMountRef.current) {
                return;
            }
            if (activeIndex === -1 && dropdownRef.current) {
                dropdownRef.current.focus();
            } else if (selectedItemRefs.current[activeIndex]) {
                selectedItemRefs.current[activeIndex].focus();
            }
        }
    }["useMultipleSelection.useEffect"], [
        activeIndex
    ]);
    useControlPropsValidator({
        isInitialMount: isInitialMountRef.current,
        props: props,
        state: state
    });
    var setGetterPropCallInfo = useGetterPropsCalledChecker('getDropdownProps');
    // Make initial ref false.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useMultipleSelection.useEffect": function() {
            isInitialMountRef.current = false;
            return ({
                "useMultipleSelection.useEffect": function() {
                    isInitialMountRef.current = true;
                }
            })["useMultipleSelection.useEffect"];
        }
    }["useMultipleSelection.useEffect"], []);
    // Event handler functions.
    var selectedItemKeyDownHandlers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useMultipleSelection.useMemo[selectedItemKeyDownHandlers]": function() {
            var _ref;
            return _ref = {}, _ref[keyNavigationPrevious] = ({
                "useMultipleSelection.useMemo[selectedItemKeyDownHandlers]": function() {
                    dispatch({
                        type: SelectedItemKeyDownNavigationPrevious
                    });
                }
            })["useMultipleSelection.useMemo[selectedItemKeyDownHandlers]"], _ref[keyNavigationNext] = ({
                "useMultipleSelection.useMemo[selectedItemKeyDownHandlers]": function() {
                    dispatch({
                        type: SelectedItemKeyDownNavigationNext
                    });
                }
            })["useMultipleSelection.useMemo[selectedItemKeyDownHandlers]"], _ref.Delete = function Delete() {
                dispatch({
                    type: SelectedItemKeyDownDelete
                });
            }, _ref.Backspace = function Backspace() {
                dispatch({
                    type: SelectedItemKeyDownBackspace
                });
            }, _ref;
        }
    }["useMultipleSelection.useMemo[selectedItemKeyDownHandlers]"], [
        dispatch,
        keyNavigationNext,
        keyNavigationPrevious
    ]);
    var dropdownKeyDownHandlers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useMultipleSelection.useMemo[dropdownKeyDownHandlers]": function() {
            var _ref2;
            return _ref2 = {}, _ref2[keyNavigationPrevious] = ({
                "useMultipleSelection.useMemo[dropdownKeyDownHandlers]": function(event) {
                    if (isKeyDownOperationPermitted(event)) {
                        dispatch({
                            type: DropdownKeyDownNavigationPrevious
                        });
                    }
                }
            })["useMultipleSelection.useMemo[dropdownKeyDownHandlers]"], _ref2.Backspace = function Backspace(event) {
                if (isKeyDownOperationPermitted(event)) {
                    dispatch({
                        type: DropdownKeyDownBackspace
                    });
                }
            }, _ref2;
        }
    }["useMultipleSelection.useMemo[dropdownKeyDownHandlers]"], [
        dispatch,
        keyNavigationPrevious
    ]);
    // Getter props.
    var getSelectedItemProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMultipleSelection.useCallback[getSelectedItemProps]": function(_temp) {
            var _extends2;
            var _ref3 = _temp === void 0 ? {} : _temp, _ref3$refKey = _ref3.refKey, refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey, ref = _ref3.ref, onClick = _ref3.onClick, onKeyDown = _ref3.onKeyDown, selectedItemProp = _ref3.selectedItem, indexProp = _ref3.index, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref3, _excluded);
            var latestState = latest.current.state;
            var _getItemAndIndex = getItemAndIndex(selectedItemProp, indexProp, latestState.selectedItems, 'Pass either item or index to getSelectedItemProps!'), index = _getItemAndIndex[1];
            var isFocusable = index > -1 && index === latestState.activeIndex;
            var selectedItemHandleClick = function selectedItemHandleClick() {
                dispatch({
                    type: SelectedItemClick,
                    index: index
                });
            };
            var selectedItemHandleKeyDown = function selectedItemHandleKeyDown(event) {
                var key = normalizeArrowKey(event);
                if (key && selectedItemKeyDownHandlers[key]) {
                    selectedItemKeyDownHandlers[key](event);
                }
            };
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((_extends2 = {}, _extends2[refKey] = handleRefs(ref, {
                "useMultipleSelection.useCallback[getSelectedItemProps]": function(selectedItemNode) {
                    if (selectedItemNode) {
                        selectedItemRefs.current.push(selectedItemNode);
                    }
                }
            }["useMultipleSelection.useCallback[getSelectedItemProps]"]), _extends2.tabIndex = isFocusable ? 0 : -1, _extends2.onClick = callAllEventHandlers(onClick, selectedItemHandleClick), _extends2.onKeyDown = callAllEventHandlers(onKeyDown, selectedItemHandleKeyDown), _extends2), rest);
        }
    }["useMultipleSelection.useCallback[getSelectedItemProps]"], [
        dispatch,
        latest,
        selectedItemKeyDownHandlers
    ]);
    var getDropdownProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMultipleSelection.useCallback[getDropdownProps]": function(_temp2, _temp3) {
            var _extends3;
            var _ref4 = _temp2 === void 0 ? {} : _temp2, _ref4$refKey = _ref4.refKey, refKey = _ref4$refKey === void 0 ? 'ref' : _ref4$refKey, ref = _ref4.ref, onKeyDown = _ref4.onKeyDown, onClick = _ref4.onClick, _ref4$preventKeyActio = _ref4.preventKeyAction, preventKeyAction = _ref4$preventKeyActio === void 0 ? false : _ref4$preventKeyActio, rest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref4, _excluded2);
            var _ref5 = _temp3 === void 0 ? {} : _temp3, _ref5$suppressRefErro = _ref5.suppressRefError, suppressRefError = _ref5$suppressRefErro === void 0 ? false : _ref5$suppressRefErro;
            setGetterPropCallInfo('getDropdownProps', suppressRefError, refKey, dropdownRef);
            var dropdownHandleKeyDown = function dropdownHandleKeyDown(event) {
                var key = normalizeArrowKey(event);
                if (key && dropdownKeyDownHandlers[key]) {
                    dropdownKeyDownHandlers[key](event);
                }
            };
            var dropdownHandleClick = function dropdownHandleClick() {
                dispatch({
                    type: DropdownClick
                });
            };
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((_extends3 = {}, _extends3[refKey] = handleRefs(ref, {
                "useMultipleSelection.useCallback[getDropdownProps]": function(dropdownNode) {
                    if (dropdownNode) {
                        dropdownRef.current = dropdownNode;
                    }
                }
            }["useMultipleSelection.useCallback[getDropdownProps]"]), _extends3), !preventKeyAction && {
                onKeyDown: callAllEventHandlers(onKeyDown, dropdownHandleKeyDown),
                onClick: callAllEventHandlers(onClick, dropdownHandleClick)
            }, rest);
        }
    }["useMultipleSelection.useCallback[getDropdownProps]"], [
        dispatch,
        dropdownKeyDownHandlers,
        setGetterPropCallInfo
    ]);
    // returns
    var addSelectedItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMultipleSelection.useCallback[addSelectedItem]": function(selectedItem) {
            dispatch({
                type: FunctionAddSelectedItem,
                selectedItem: selectedItem
            });
        }
    }["useMultipleSelection.useCallback[addSelectedItem]"], [
        dispatch
    ]);
    var removeSelectedItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMultipleSelection.useCallback[removeSelectedItem]": function(selectedItem) {
            dispatch({
                type: FunctionRemoveSelectedItem,
                selectedItem: selectedItem
            });
        }
    }["useMultipleSelection.useCallback[removeSelectedItem]"], [
        dispatch
    ]);
    var setSelectedItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMultipleSelection.useCallback[setSelectedItems]": function(newSelectedItems) {
            dispatch({
                type: FunctionSetSelectedItems,
                selectedItems: newSelectedItems
            });
        }
    }["useMultipleSelection.useCallback[setSelectedItems]"], [
        dispatch
    ]);
    var setActiveIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMultipleSelection.useCallback[setActiveIndex]": function(newActiveIndex) {
            dispatch({
                type: FunctionSetActiveIndex,
                activeIndex: newActiveIndex
            });
        }
    }["useMultipleSelection.useCallback[setActiveIndex]"], [
        dispatch
    ]);
    var reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMultipleSelection.useCallback[reset]": function() {
            dispatch({
                type: FunctionReset
            });
        }
    }["useMultipleSelection.useCallback[reset]"], [
        dispatch
    ]);
    return {
        getSelectedItemProps: getSelectedItemProps,
        getDropdownProps: getDropdownProps,
        addSelectedItem: addSelectedItem,
        removeSelectedItem: removeSelectedItem,
        setSelectedItems: setSelectedItems,
        setActiveIndex: setActiveIndex,
        reset: reset,
        selectedItems: selectedItems,
        activeIndex: activeIndex
    };
}
;
}}),
}]);

//# sourceMappingURL=node_modules_downshift_dist_downshift_esm_830c06bb.js.map