const CHUNK_PUBLIC_PATH = "server/instrumentation.js";
const runtime = require("./chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/[externals]_node:inspector_d34f3da2._.js");
runtime.loadChunk("server/chunks/node_modules_@sentry_core_build_cjs_6351fe99._.js");
runtime.loadChunk("server/chunks/node_modules_@opentelemetry_core_build_esm_5278fd5f._.js");
runtime.loadChunk("server/chunks/996e2_@opentelemetry_semantic-conventions_build_esm_65581ec9._.js");
runtime.loadChunk("server/chunks/node_modules_@opentelemetry_semantic-conventions_build_esm_05b3d850._.js");
runtime.loadChunk("server/chunks/node_modules_@opentelemetry_bbd2b1f3._.js");
runtime.loadChunk("server/chunks/node_modules_@sentry_node-core_build_cjs_f4f6af12._.js");
runtime.loadChunk("server/chunks/node_modules_@opentelemetry_sdk-trace-base_build_esm_f015b30e._.js");
runtime.loadChunk("server/chunks/node_modules_@opentelemetry_resources_build_esm_5994052a._.js");
runtime.loadChunk("server/chunks/node_modules_@sentry_node_build_cjs_d8bfe000._.js");
runtime.loadChunk("server/chunks/node_modules_@sentry_nextjs_build_cjs_9dcb352e._.js");
runtime.loadChunk("server/chunks/node_modules_569ea5f3._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__e7f8aa70._.js");
runtime.getOrInstantiateRuntimeModule("[project]/src/instrumentation.ts [instrumentation] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/src/instrumentation.ts [instrumentation] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
