/* MDX Editor Styles */
@import '@mdxeditor/editor/style.css';

.mdx-editor-wrapper {
  border: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius) - 2px);
  overflow: hidden;
}

.mdx-editor-wrapper .mdxeditor {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: inherit;
}

.mdx-editor-wrapper .mdxeditor-toolbar {
  background: hsl(var(--muted));
  border-bottom: 1px solid hsl(var(--border));
  padding: 8px;
}

.mdx-editor-wrapper .mdxeditor-toolbar button {
  background: transparent;
  border: 1px solid transparent;
  border-radius: calc(var(--radius) - 2px);
  color: hsl(var(--foreground));
  padding: 6px 8px;
  margin: 0 2px;
  transition: all 0.2s;
}

.mdx-editor-wrapper .mdxeditor-toolbar button:hover {
  background: hsl(var(--accent));
  border-color: hsl(var(--border));
}

.mdx-editor-wrapper .mdxeditor-toolbar button[data-state="on"] {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.mdx-editor-wrapper .mdxeditor-content-editable {
  padding: 16px;
  min-height: 200px;
  font-size: 14px;
  line-height: 1.6;
}

.mdx-editor-wrapper .mdxeditor-content-editable:focus {
  outline: none;
}

.mdx-editor-wrapper .mdxeditor-content-editable h1 {
  font-size: 2em;
  font-weight: bold;
  margin: 1em 0 0.5em 0;
  line-height: 1.2;
}

.mdx-editor-wrapper .mdxeditor-content-editable h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 0.8em 0 0.4em 0;
  line-height: 1.3;
}

.mdx-editor-wrapper .mdxeditor-content-editable h3 {
  font-size: 1.25em;
  font-weight: bold;
  margin: 0.6em 0 0.3em 0;
  line-height: 1.4;
}

.mdx-editor-wrapper .mdxeditor-content-editable p {
  margin: 0.5em 0;
}

.mdx-editor-wrapper .mdxeditor-content-editable ul,
.mdx-editor-wrapper .mdxeditor-content-editable ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.mdx-editor-wrapper .mdxeditor-content-editable li {
  margin: 0.25em 0;
}

.mdx-editor-wrapper .mdxeditor-content-editable blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.mdx-editor-wrapper .mdxeditor-content-editable code {
  background: hsl(var(--muted));
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.mdx-editor-wrapper .mdxeditor-content-editable pre {
  background: hsl(var(--muted));
  padding: 1em;
  border-radius: calc(var(--radius) - 2px);
  overflow-x: auto;
  margin: 1em 0;
}

.mdx-editor-wrapper .mdxeditor-content-editable pre code {
  background: transparent;
  padding: 0;
}

.mdx-editor-wrapper .mdxeditor-content-editable table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.mdx-editor-wrapper .mdxeditor-content-editable th,
.mdx-editor-wrapper .mdxeditor-content-editable td {
  border: 1px solid hsl(var(--border));
  padding: 8px 12px;
  text-align: left;
}

.mdx-editor-wrapper .mdxeditor-content-editable th {
  background: hsl(var(--muted));
  font-weight: bold;
}

.mdx-editor-wrapper .mdxeditor-content-editable img {
  max-width: 100%;
  height: auto;
  border-radius: calc(var(--radius) - 2px);
  margin: 0.5em 0;
}

.mdx-editor-wrapper .mdxeditor-content-editable a {
  color: hsl(var(--primary));
  text-decoration: underline;
}

.mdx-editor-wrapper .mdxeditor-content-editable a:hover {
  color: hsl(var(--primary) / 0.8);
}

/* Dark mode adjustments */
.dark .mdx-editor-wrapper .mdxeditor-toolbar {
  background: hsl(var(--muted));
}

.dark .mdx-editor-wrapper .mdxeditor-toolbar button:hover {
  background: hsl(var(--accent));
}

/* Focus styles */
.mdx-editor-wrapper:focus-within {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Placeholder styles */
.mdx-editor-wrapper .mdxeditor-content-editable[data-placeholder]:empty::before {
  content: attr(data-placeholder);
  color: hsl(var(--muted-foreground));
  font-style: italic;
}
