{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/ttcn-cfg.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i)\n    obj[words[i]] = true;\n  return obj;\n}\n\nconst parserConfig = {\n  name: \"ttcn-cfg\",\n  keywords: words(\"Yes No LogFile FileMask ConsoleMask AppendFile\" +\n                  \" TimeStampFormat LogEventTypes SourceInfoFormat\" +\n                  \" LogEntityName LogSourceInfo DiskFullAction\" +\n                  \" LogFileNumber LogFileSize MatchingHints Detailed\" +\n                  \" Compact SubCategories Stack Single None Seconds\" +\n                  \" DateTime Time Stop Error Retry Delete TCPPort KillTimer\" +\n                  \" NumHCs UnixSocketsEnabled LocalAddress\"),\n  fileNCtrlMaskOptions: words(\"TTCN_EXECUTOR TTCN_ERROR TTCN_WARNING\" +\n                              \" TTCN_PORTEVENT TTCN_TIMEROP TTCN_VERDICTOP\" +\n                              \" TTCN_DEFAULTOP TTCN_TESTCASE TTCN_ACTION\" +\n                              \" TTCN_USER TTCN_FUNCTION TTCN_STATISTICS\" +\n                              \" TTCN_PARALLEL TTCN_MATCHING TTCN_DEBUG\" +\n                              \" EXECUTOR ERROR WARNING PORTEVENT TIMEROP\" +\n                              \" VERDICTOP DEFAULTOP TESTCASE ACTION USER\" +\n                              \" FUNCTION STATISTICS PARALLEL MATCHING DEBUG\" +\n                              \" LOG_ALL LOG_NOTHING ACTION_UNQUALIFIED\" +\n                              \" DEBUG_ENCDEC DEBUG_TESTPORT\" +\n                              \" DEBUG_UNQUALIFIED DEFAULTOP_ACTIVATE\" +\n                              \" DEFAULTOP_DEACTIVATE DEFAULTOP_EXIT\" +\n                              \" DEFAULTOP_UNQUALIFIED ERROR_UNQUALIFIED\" +\n                              \" EXECUTOR_COMPONENT EXECUTOR_CONFIGDATA\" +\n                              \" EXECUTOR_EXTCOMMAND EXECUTOR_LOGOPTIONS\" +\n                              \" EXECUTOR_RUNTIME EXECUTOR_UNQUALIFIED\" +\n                              \" FUNCTION_RND FUNCTION_UNQUALIFIED\" +\n                              \" MATCHING_DONE MATCHING_MCSUCCESS\" +\n                              \" MATCHING_MCUNSUCC MATCHING_MMSUCCESS\" +\n                              \" MATCHING_MMUNSUCC MATCHING_PCSUCCESS\" +\n                              \" MATCHING_PCUNSUCC MATCHING_PMSUCCESS\" +\n                              \" MATCHING_PMUNSUCC MATCHING_PROBLEM\" +\n                              \" MATCHING_TIMEOUT MATCHING_UNQUALIFIED\" +\n                              \" PARALLEL_PORTCONN PARALLEL_PORTMAP\" +\n                              \" PARALLEL_PTC PARALLEL_UNQUALIFIED\" +\n                              \" PORTEVENT_DUALRECV PORTEVENT_DUALSEND\" +\n                              \" PORTEVENT_MCRECV PORTEVENT_MCSEND\" +\n                              \" PORTEVENT_MMRECV PORTEVENT_MMSEND\" +\n                              \" PORTEVENT_MQUEUE PORTEVENT_PCIN\" +\n                              \" PORTEVENT_PCOUT PORTEVENT_PMIN\" +\n                              \" PORTEVENT_PMOUT PORTEVENT_PQUEUE\" +\n                              \" PORTEVENT_STATE PORTEVENT_UNQUALIFIED\" +\n                              \" STATISTICS_UNQUALIFIED STATISTICS_VERDICT\" +\n                              \" TESTCASE_FINISH TESTCASE_START\" +\n                              \" TESTCASE_UNQUALIFIED TIMEROP_GUARD\" +\n                              \" TIMEROP_READ TIMEROP_START TIMEROP_STOP\" +\n                              \" TIMEROP_TIMEOUT TIMEROP_UNQUALIFIED\" +\n                              \" USER_UNQUALIFIED VERDICTOP_FINAL\" +\n                              \" VERDICTOP_GETVERDICT VERDICTOP_SETVERDICT\" +\n                              \" VERDICTOP_UNQUALIFIED WARNING_UNQUALIFIED\"),\n  externalCommands: words(\"BeginControlPart EndControlPart BeginTestCase\" +\n                          \" EndTestCase\"),\n  multiLineStrings: true\n}\n\nvar keywords = parserConfig.keywords,\n    fileNCtrlMaskOptions = parserConfig.fileNCtrlMaskOptions,\n    externalCommands = parserConfig.externalCommands,\n    multiLineStrings = parserConfig.multiLineStrings,\n    indentStatements = parserConfig.indentStatements !== false;\nvar isOperatorChar = /[\\|]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[:=]/.test(ch)) {\n    curPunc = ch;\n    return \"punctuation\";\n  }\n  if (ch == \"#\"){\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  if (ch == \"[\"){\n    stream.eatWhile(/[\\w_\\]]/);\n    return \"number\";\n  }\n\n  stream.eatWhile(/[\\w\\$_]/);\n  var cur = stream.current();\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  if (fileNCtrlMaskOptions.propertyIsEnumerable(cur))\n    return \"atom\";\n  if (externalCommands.propertyIsEnumerable(cur)) return \"deleted\";\n\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped){\n        var afterNext = stream.peek();\n        //look if the character if the quote is like the B in '10100010'B\n        if (afterNext){\n          afterNext = afterNext.toLowerCase();\n          if(afterNext == \"b\" || afterNext == \"h\" || afterNext == \"o\")\n            stream.next();\n        }\n        end = true; break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !(escaped || multiLineStrings))\n      state.tokenize = null;\n    return \"string\";\n  };\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type) {\n  var indent = state.indented;\n  if (state.context && state.context.type == \"statement\")\n    indent = state.context.indented;\n  return state.context = new Context(indent, col, type, null, state.context);\n}\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n//Interface\nexport const ttcnCfg = {\n  name: \"ttcn\",\n  startState: function() {\n    return {\n      tokenize: null,\n      context: new Context(0, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\" || curPunc == \",\")\n        && ctx.type == \"statement\"){\n      popContext(state);\n    }\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (indentStatements && (((ctx.type == \"}\" || ctx.type == \"top\")\n                                   && curPunc != ';') || (ctx.type == \"statement\"\n                                                          && curPunc == \"newstatement\")))\n      pushContext(state, stream.column(), \"statement\");\n    state.startOfLine = false;\n    return style;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAClC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IAClB,OAAO;AACT;AAEA,MAAM,eAAe;IACnB,MAAM;IACN,UAAU,MAAM,mDACA,oDACA,gDACA,sDACA,qDACA,6DACA;IAChB,sBAAsB,MAAM,0CACA,gDACA,8CACA,6CACA,4CACA,8CACA,8CACA,iDACA,4CACA,iCACA,0CACA,yCACA,6CACA,4CACA,6CACA,2CACA,uCACA,sCACA,0CACA,0CACA,0CACA,wCACA,2CACA,wCACA,uCACA,2CACA,uCACA,uCACA,qCACA,oCACA,sCACA,2CACA,+CACA,oCACA,wCACA,6CACA,yCACA,sCACA,+CACA;IAC5B,kBAAkB,MAAM,kDACA;IACxB,kBAAkB;AACpB;AAEA,IAAI,WAAW,aAAa,QAAQ,EAChC,uBAAuB,aAAa,oBAAoB,EACxD,mBAAmB,aAAa,gBAAgB,EAChD,mBAAmB,aAAa,gBAAgB,EAChD,mBAAmB,aAAa,gBAAgB,KAAK;AACzD,IAAI,iBAAiB;AACrB,IAAI;AAEJ,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,OAAO,MAAM,KAAK;QAC1B,MAAM,QAAQ,GAAG,YAAY;QAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IACA,IAAI,OAAO,IAAI,CAAC,KAAK;QACnB,UAAU;QACV,OAAO;IACT;IACA,IAAI,MAAM,KAAI;QACZ,OAAO,SAAS;QAChB,OAAO;IACT;IACA,IAAI,KAAK,IAAI,CAAC,KAAK;QACjB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,IAAI,eAAe,IAAI,CAAC,KAAK;QAC3B,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,IAAI,MAAM,KAAI;QACZ,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IAEA,OAAO,QAAQ,CAAC;IAChB,IAAI,MAAM,OAAO,OAAO;IACxB,IAAI,SAAS,oBAAoB,CAAC,MAAM,OAAO;IAC/C,IAAI,qBAAqB,oBAAoB,CAAC,MAC5C,OAAO;IACT,IAAI,iBAAiB,oBAAoB,CAAC,MAAM,OAAO;IAEvD,OAAO;AACT;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;QACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,QAAQ,SAAS,CAAC,SAAQ;gBAC5B,IAAI,YAAY,OAAO,IAAI;gBAC3B,iEAAiE;gBACjE,IAAI,WAAU;oBACZ,YAAY,UAAU,WAAW;oBACjC,IAAG,aAAa,OAAO,aAAa,OAAO,aAAa,KACtD,OAAO,IAAI;gBACf;gBACA,MAAM;gBAAM;YACd;YACA,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,OAAO,CAAC,CAAC,WAAW,gBAAgB,GACtC,MAAM,QAAQ,GAAG;QACnB,OAAO;IACT;AACF;AAEA,SAAS,QAAQ,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;IAClD,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,IAAI,GAAG;AACd;AACA,SAAS,YAAY,KAAK,EAAE,GAAG,EAAE,IAAI;IACnC,IAAI,SAAS,MAAM,QAAQ;IAC3B,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,aACzC,SAAS,MAAM,OAAO,CAAC,QAAQ;IACjC,OAAO,MAAM,OAAO,GAAG,IAAI,QAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,OAAO;AAC3E;AACA,SAAS,WAAW,KAAK;IACvB,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI;IAC1B,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAC/B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ;IACzC,OAAO,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;AAC3C;AAGO,MAAM,UAAU;IACrB,MAAM;IACN,YAAY;QACV,OAAO;YACL,UAAU;YACV,SAAS,IAAI,QAAQ,GAAG,GAAG,OAAO;YAClC,UAAU;YACV,aAAa;QACf;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,MAAM,MAAM,OAAO;QACvB,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;YACnC,MAAM,QAAQ,GAAG,OAAO,WAAW;YACnC,MAAM,WAAW,GAAG;QACtB;QACA,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,UAAU;QACV,IAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,SAAS,EAAE,QAAQ;QAClD,IAAI,SAAS,WAAW,OAAO;QAC/B,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;QAEnC,IAAI,CAAC,WAAW,OAAO,WAAW,OAAO,WAAW,GAAG,KAChD,IAAI,IAAI,IAAI,aAAY;YAC7B,WAAW;QACb,OACK,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK;YACvB,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;YACjD,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM,WAAW;YACtC,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;QACnD,OACK,IAAI,WAAW,IAAI,IAAI,EAAE,WAAW;aACpC,IAAI,oBAAoB,CAAC,AAAC,CAAC,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,KAClC,WAAW,OAAS,IAAI,IAAI,IAAI,eACT,WAAW,cAAe,GACjF,YAAY,OAAO,OAAO,MAAM,IAAI;QACtC,MAAM,WAAW,GAAG;QACpB,OAAO;IACT;IAEA,cAAc;QACZ,eAAe;QACf,eAAe;YAAC,MAAM;QAAG;IAC3B;AACF", "ignoreList": [0], "debugId": null}}]}