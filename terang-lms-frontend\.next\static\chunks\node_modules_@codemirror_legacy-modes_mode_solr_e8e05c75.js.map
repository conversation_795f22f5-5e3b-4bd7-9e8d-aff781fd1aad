{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/solr.js"], "sourcesContent": ["var isStringChar = /[^\\s\\|\\!\\+\\-\\*\\?\\~\\^\\&\\:\\(\\)\\[\\]\\{\\}\\\"\\\\]/;\nvar isOperatorChar = /[\\|\\!\\+\\-\\*\\?\\~\\^\\&]/;\nvar isOperatorString = /^(OR|AND|NOT|TO)$/;\n\nfunction isNumber(word) {\n  return parseFloat(word).toString() === word;\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) break;\n      escaped = !escaped && next == \"\\\\\";\n    }\n\n    if (!escaped) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction tokenOperator(operator) {\n  return function(stream, state) {\n    if (operator == \"|\")\n      stream.eat(/\\|/);\n    else if (operator == \"&\")\n      stream.eat(/\\&/);\n\n    state.tokenize = tokenBase;\n    return \"operator\";\n  };\n}\n\nfunction tokenWord(ch) {\n  return function(stream, state) {\n    var word = ch;\n    while ((ch = stream.peek()) && ch.match(isStringChar) != null) {\n      word += stream.next();\n    }\n\n    state.tokenize = tokenBase;\n    if (isOperatorString.test(word))\n      return \"operator\";\n    else if (isNumber(word))\n      return \"number\";\n    else if (stream.peek() == \":\")\n      return \"propertyName\";\n    else\n      return \"string\";\n  };\n}\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"')\n    state.tokenize = tokenString(ch);\n  else if (isOperatorChar.test(ch))\n    state.tokenize = tokenOperator(ch);\n  else if (isStringChar.test(ch))\n    state.tokenize = tokenWord(ch);\n\n  return (state.tokenize != tokenBase) ? state.tokenize(stream, state) : null;\n}\n\nexport const solr = {\n  name: \"solr\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase\n    };\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,mBAAmB;AAEvB,SAAS,SAAS,IAAI;IACpB,OAAO,WAAW,MAAM,QAAQ,OAAO;AACzC;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO;QACrB,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,QAAQ,SAAS,CAAC,SAAS;YAC/B,UAAU,CAAC,WAAW,QAAQ;QAChC;QAEA,IAAI,CAAC,SAAS,MAAM,QAAQ,GAAG;QAC/B,OAAO;IACT;AACF;AAEA,SAAS,cAAc,QAAQ;IAC7B,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,YAAY,KACd,OAAO,GAAG,CAAC;aACR,IAAI,YAAY,KACnB,OAAO,GAAG,CAAC;QAEb,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT;AACF;AAEA,SAAS,UAAU,EAAE;IACnB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO;QACX,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,GAAG,KAAK,CAAC,iBAAiB,KAAM;YAC7D,QAAQ,OAAO,IAAI;QACrB;QAEA,MAAM,QAAQ,GAAG;QACjB,IAAI,iBAAiB,IAAI,CAAC,OACxB,OAAO;aACJ,IAAI,SAAS,OAChB,OAAO;aACJ,IAAI,OAAO,IAAI,MAAM,KACxB,OAAO;aAEP,OAAO;IACX;AACF;AAEA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,KACR,MAAM,QAAQ,GAAG,YAAY;SAC1B,IAAI,eAAe,IAAI,CAAC,KAC3B,MAAM,QAAQ,GAAG,cAAc;SAC5B,IAAI,aAAa,IAAI,CAAC,KACzB,MAAM,QAAQ,GAAG,UAAU;IAE7B,OAAO,AAAC,MAAM,QAAQ,IAAI,YAAa,MAAM,QAAQ,CAAC,QAAQ,SAAS;AACzE;AAEO,MAAM,OAAO;IAClB,MAAM;IAEN,YAAY;QACV,OAAO;YACL,UAAU;QACZ;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;AACF", "ignoreList": [0], "debugId": null}}]}