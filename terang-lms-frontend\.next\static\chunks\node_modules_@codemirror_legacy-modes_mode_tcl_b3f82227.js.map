{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/tcl.js"], "sourcesContent": ["function parseWords(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\nvar keywords = parseWords(\"Tcl safe after append array auto_execok auto_import auto_load \" +\n                          \"auto_mkindex auto_mkindex_old auto_qualify auto_reset bgerror \" +\n                          \"binary break catch cd close concat continue dde eof encoding error \" +\n                          \"eval exec exit expr fblocked fconfigure fcopy file fileevent filename \" +\n                          \"filename flush for foreach format gets glob global history http if \" +\n                          \"incr info interp join lappend lindex linsert list llength load lrange \" +\n                          \"lreplace lsearch lset lsort memory msgcat namespace open package parray \" +\n                          \"pid pkg::create pkg_mkIndex proc puts pwd re_syntax read regex regexp \" +\n                          \"registry regsub rename resource return scan seek set socket source split \" +\n                          \"string subst switch tcl_endOfWord tcl_findLibrary tcl_startOfNextWord \" +\n                          \"tcl_wordBreakAfter tcl_startOfPreviousWord tcl_wordBreakBefore tcltest \" +\n                          \"tclvars tell time trace unknown unset update uplevel upvar variable \" +\n                          \"vwait\");\nvar functions = parseWords(\"if elseif else and not or eq ne in ni for foreach while switch\");\nvar isOperatorChar = /[+\\-*&%=<>!?^\\/\\|]/;\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\nfunction tokenBase(stream, state) {\n  var beforeParams = state.beforeParams;\n  state.beforeParams = false;\n  var ch = stream.next();\n  if ((ch == '\"' || ch == \"'\") && state.inParams) {\n    return chain(stream, state, tokenString(ch));\n  } else if (/[\\[\\]{}\\(\\),;\\.]/.test(ch)) {\n    if (ch == \"(\" && beforeParams) state.inParams = true;\n    else if (ch == \")\") state.inParams = false;\n    return null;\n  } else if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  } else if (ch == \"#\") {\n    if (stream.eat(\"*\"))\n      return chain(stream, state, tokenComment);\n    if (ch == \"#\" && stream.match(/ *\\[ *\\[/))\n      return chain(stream, state, tokenUnparsed);\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == '\"') {\n    stream.skipTo(/\"/);\n    return \"comment\";\n  } else if (ch == \"$\") {\n    stream.eatWhile(/[$_a-z0-9A-Z\\.{:]/);\n    stream.eatWhile(/}/);\n    state.beforeParams = true;\n    return \"builtin\";\n  } else if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"comment\";\n  } else {\n    stream.eatWhile(/[\\w\\$_{}\\xa1-\\uffff]/);\n    var word = stream.current().toLowerCase();\n    if (keywords && keywords.propertyIsEnumerable(word))\n      return \"keyword\";\n    if (functions && functions.propertyIsEnumerable(word)) {\n      state.beforeParams = true;\n      return \"keyword\";\n    }\n    return null;\n  }\n}\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\nfunction tokenUnparsed(stream, state) {\n  var maybeEnd = 0, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd == 2) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    if (ch == \"]\")\n      maybeEnd++;\n    else if (ch != \" \")\n      maybeEnd = 0;\n  }\n  return \"meta\";\n}\nexport const tcl = {\n  name: \"tcl\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      beforeParams: false,\n      inParams: false\n    };\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  },\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,WAAW,GAAG;IACrB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AACA,IAAI,WAAW,WAAW,mEACA,mEACA,wEACA,2EACA,wEACA,2EACA,6EACA,2EACA,8EACA,2EACA,4EACA,yEACA;AAC1B,IAAI,YAAY,WAAW;AAC3B,IAAI,iBAAiB;AACrB,SAAS,MAAM,MAAM,EAAE,KAAK,EAAE,CAAC;IAC7B,MAAM,QAAQ,GAAG;IACjB,OAAO,EAAE,QAAQ;AACnB;AACA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,eAAe,MAAM,YAAY;IACrC,MAAM,YAAY,GAAG;IACrB,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,CAAC,MAAM,OAAO,MAAM,GAAG,KAAK,MAAM,QAAQ,EAAE;QAC9C,OAAO,MAAM,QAAQ,OAAO,YAAY;IAC1C,OAAO,IAAI,mBAAmB,IAAI,CAAC,KAAK;QACtC,IAAI,MAAM,OAAO,cAAc,MAAM,QAAQ,GAAG;aAC3C,IAAI,MAAM,KAAK,MAAM,QAAQ,GAAG;QACrC,OAAO;IACT,OAAO,IAAI,KAAK,IAAI,CAAC,KAAK;QACxB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OAAO,IAAI,MAAM,KAAK;QACpB,IAAI,OAAO,GAAG,CAAC,MACb,OAAO,MAAM,QAAQ,OAAO;QAC9B,IAAI,MAAM,OAAO,OAAO,KAAK,CAAC,aAC5B,OAAO,MAAM,QAAQ,OAAO;QAC9B,OAAO,SAAS;QAChB,OAAO;IACT,OAAO,IAAI,MAAM,KAAK;QACpB,OAAO,MAAM,CAAC;QACd,OAAO;IACT,OAAO,IAAI,MAAM,KAAK;QACpB,OAAO,QAAQ,CAAC;QAChB,OAAO,QAAQ,CAAC;QAChB,MAAM,YAAY,GAAG;QACrB,OAAO;IACT,OAAO,IAAI,eAAe,IAAI,CAAC,KAAK;QAClC,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OAAO;QACL,OAAO,QAAQ,CAAC;QAChB,IAAI,OAAO,OAAO,OAAO,GAAG,WAAW;QACvC,IAAI,YAAY,SAAS,oBAAoB,CAAC,OAC5C,OAAO;QACT,IAAI,aAAa,UAAU,oBAAoB,CAAC,OAAO;YACrD,MAAM,YAAY,GAAG;YACrB,OAAO;QACT;QACA,OAAO;IACT;AACF;AACA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;QACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,QAAQ,SAAS,CAAC,SAAS;gBAC7B,MAAM;gBACN;YACF;YACA,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,KAAK,MAAM,QAAQ,GAAG;QAC1B,OAAO;IACT;AACF;AACA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,WAAW,OAAO;IACtB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AACA,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,IAAI,WAAW,GAAG;IAClB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,YAAY,GAAG;YAC9B,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,IAAI,MAAM,KACR;aACG,IAAI,MAAM,KACb,WAAW;IACf;IACA,OAAO;AACT;AACO,MAAM,MAAM;IACjB,MAAM;IACN,YAAY;QACV,OAAO;YACL,UAAU;YACV,cAAc;YACd,UAAU;QACZ;IACF;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IACA,cAAc;QACZ,eAAe;YAAC,MAAM;QAAG;IAC3B;AACF", "ignoreList": [0], "debugId": null}}]}