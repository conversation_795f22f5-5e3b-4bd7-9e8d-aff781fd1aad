{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/pascal.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\nvar keywords = words(\n  \"absolute and array asm begin case const constructor destructor div do \" +\n    \"downto else end file for function goto if implementation in inherited \" +\n    \"inline interface label mod nil not object of operator or packed procedure \" +\n    \"program record reintroduce repeat self set shl shr string then to type \" +\n    \"unit until uses var while with xor as class dispinterface except exports \" +\n    \"finalization finally initialization inline is library on out packed \" +\n    \"property raise resourcestring threadvar try absolute abstract alias \" +\n    \"assembler bitpacked break cdecl continue cppdecl cvar default deprecated \" +\n    \"dynamic enumerator experimental export external far far16 forward generic \" +\n    \"helper implements index interrupt iocheck local message name near \" +\n    \"nodefault noreturn nostackframe oldfpccall otherwise overload override \" +\n    \"pascal platform private protected public published read register \" +\n    \"reintroduce result safecall saveregisters softfloat specialize static \" +\n    \"stdcall stored strict unaligned unimplemented varargs virtual write\");\nvar atoms = {\"null\": true};\n\nvar isOperatorChar = /[+\\-*&%=<>!?|\\/]/;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == \"#\" && state.startOfLine) {\n    stream.skipToEnd();\n    return \"meta\";\n  }\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (ch == \"(\" && stream.eat(\"*\")) {\n    state.tokenize = tokenComment;\n    return tokenComment(stream, state);\n  }\n  if (ch == \"{\") {\n    state.tokenize = tokenCommentBraces;\n    return tokenCommentBraces(stream, state);\n  }\n  if (/[\\[\\]\\(\\),;\\:\\.]/.test(ch)) {\n    return null;\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_]/);\n  var cur = stream.current().toLowerCase();\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {end = true; break;}\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped) state.tokenize = null;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \")\" && maybeEnd) {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenCommentBraces(stream, state) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"}\") {\n      state.tokenize = null;\n      break;\n    }\n  }\n  return \"comment\";\n}\n\n// Interface\n\nexport const pascal = {\n  name: \"pascal\",\n\n  startState: function() {\n    return {tokenize: null};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\" || style == \"meta\") return style;\n    return style;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {block: {open: \"(*\", close: \"*)\"}}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AACA,IAAI,WAAW,MACb,2EACE,2EACA,+EACA,4EACA,8EACA,yEACA,yEACA,8EACA,+EACA,uEACA,4EACA,sEACA,2EACA;AACJ,IAAI,QAAQ;IAAC,QAAQ;AAAI;AAEzB,IAAI,iBAAiB;AAErB,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,OAAO,MAAM,WAAW,EAAE;QAClC,OAAO,SAAS;QAChB,OAAO;IACT;IACA,IAAI,MAAM,OAAO,MAAM,KAAK;QAC1B,MAAM,QAAQ,GAAG,YAAY;QAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IACA,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;QAChC,MAAM,QAAQ,GAAG;QACjB,OAAO,aAAa,QAAQ;IAC9B;IACA,IAAI,MAAM,KAAK;QACb,MAAM,QAAQ,GAAG;QACjB,OAAO,mBAAmB,QAAQ;IACpC;IACA,IAAI,mBAAmB,IAAI,CAAC,KAAK;QAC/B,OAAO;IACT;IACA,IAAI,KAAK,IAAI,CAAC,KAAK;QACjB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,IAAI,MAAM,KAAK;QACb,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO,SAAS;YAChB,OAAO;QACT;IACF;IACA,IAAI,eAAe,IAAI,CAAC,KAAK;QAC3B,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,OAAO,QAAQ,CAAC;IAChB,IAAI,MAAM,OAAO,OAAO,GAAG,WAAW;IACtC,IAAI,SAAS,oBAAoB,CAAC,MAAM,OAAO;IAC/C,IAAI,MAAM,oBAAoB,CAAC,MAAM,OAAO;IAC5C,OAAO;AACT;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;QACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,QAAQ,SAAS,CAAC,SAAS;gBAAC,MAAM;gBAAM;YAAM;YAClD,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,OAAO,CAAC,SAAS,MAAM,QAAQ,GAAG;QACtC,OAAO;IACT;AACF;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,WAAW,OAAO;IACtB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,MAAM,EAAE,KAAK;IACvC,IAAI;IACJ,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,KAAK;YACb,MAAM,QAAQ,GAAG;YACjB;QACF;IACF;IACA,OAAO;AACT;AAIO,MAAM,SAAS;IACpB,MAAM;IAEN,YAAY;QACV,OAAO;YAAC,UAAU;QAAI;IACxB;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,IAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,SAAS,EAAE,QAAQ;QAClD,IAAI,SAAS,aAAa,SAAS,QAAQ,OAAO;QAClD,OAAO;IACT;IAEA,cAAc;QACZ,eAAe;QACf,eAAe;YAAC,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;IAClD;AACF", "ignoreList": [0], "debugId": null}}]}