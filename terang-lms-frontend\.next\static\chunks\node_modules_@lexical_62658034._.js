(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@lexical/selection/LexicalSelection.dev.mjs [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "$addNodeStyle": (()=>$addNodeStyle),
    "$copyBlockFormatIndent": (()=>$copyBlockFormatIndent),
    "$ensureForwardRangeSelection": (()=>$ensureForwardRangeSelection),
    "$forEachSelectedTextNode": (()=>$forEachSelectedTextNode),
    "$getComputedStyleForElement": (()=>$getComputedStyleForElement),
    "$getComputedStyleForParent": (()=>$getComputedStyleForParent),
    "$getSelectionStyleValueForProperty": (()=>$getSelectionStyleValueForProperty),
    "$isAtNodeEnd": (()=>$isAtNodeEnd),
    "$isParentElementRTL": (()=>$isParentElementRTL),
    "$isParentRTL": (()=>$isParentRTL),
    "$moveCaretSelection": (()=>$moveCaretSelection),
    "$moveCharacter": (()=>$moveCharacter),
    "$patchStyleText": (()=>$patchStyleText),
    "$setBlocksType": (()=>$setBlocksType),
    "$shouldOverrideDefaultCharacterSelection": (()=>$shouldOverrideDefaultCharacterSelection),
    "$sliceSelectedTextNodeContent": (()=>$sliceSelectedTextNodeContent),
    "$trimTextContentFromAnchor": (()=>$trimTextContentFromAnchor),
    "$wrapNodes": (()=>$wrapNodes),
    "createDOMRange": (()=>createDOMRange),
    "createRectsFromDOMRange": (()=>createRectsFromDOMRange),
    "getCSSFromStyleObject": (()=>getCSSFromStyleObject),
    "getStyleObjectFromCSS": (()=>getStyleObjectFromCSS),
    "trimTextContentFromAnchor": (()=>trimTextContentFromAnchor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Do not require this module directly! Use normal `invariant` calls.
function formatDevErrorMessage(message) {
    throw new Error(message);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const CSS_TO_STYLES = new Map();
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function getDOMTextNode(element) {
    let node = element;
    while(node != null){
        if (node.nodeType === Node.TEXT_NODE) {
            return node;
        }
        node = node.firstChild;
    }
    return null;
}
function getDOMIndexWithinParent(node) {
    const parent = node.parentNode;
    if (parent == null) {
        throw new Error('Should never happen');
    }
    return [
        parent,
        Array.from(parent.childNodes).indexOf(node)
    ];
}
/**
 * Creates a selection range for the DOM.
 * @param editor - The lexical editor.
 * @param anchorNode - The anchor node of a selection.
 * @param _anchorOffset - The amount of space offset from the anchor to the focus.
 * @param focusNode - The current focus.
 * @param _focusOffset - The amount of space offset from the focus to the anchor.
 * @returns The range of selection for the DOM that was created.
 */ function createDOMRange(editor, anchorNode, _anchorOffset, focusNode, _focusOffset) {
    const anchorKey = anchorNode.getKey();
    const focusKey = focusNode.getKey();
    const range = document.createRange();
    let anchorDOM = editor.getElementByKey(anchorKey);
    let focusDOM = editor.getElementByKey(focusKey);
    let anchorOffset = _anchorOffset;
    let focusOffset = _focusOffset;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(anchorNode)) {
        anchorDOM = getDOMTextNode(anchorDOM);
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(focusNode)) {
        focusDOM = getDOMTextNode(focusDOM);
    }
    if (anchorNode === undefined || focusNode === undefined || anchorDOM === null || focusDOM === null) {
        return null;
    }
    if (anchorDOM.nodeName === 'BR') {
        [anchorDOM, anchorOffset] = getDOMIndexWithinParent(anchorDOM);
    }
    if (focusDOM.nodeName === 'BR') {
        [focusDOM, focusOffset] = getDOMIndexWithinParent(focusDOM);
    }
    const firstChild = anchorDOM.firstChild;
    if (anchorDOM === focusDOM && firstChild != null && firstChild.nodeName === 'BR' && anchorOffset === 0 && focusOffset === 0) {
        focusOffset = 1;
    }
    try {
        range.setStart(anchorDOM, anchorOffset);
        range.setEnd(focusDOM, focusOffset);
    } catch (e) {
        return null;
    }
    if (range.collapsed && (anchorOffset !== focusOffset || anchorKey !== focusKey)) {
        // Range is backwards, we need to reverse it
        range.setStart(focusDOM, focusOffset);
        range.setEnd(anchorDOM, anchorOffset);
    }
    return range;
}
/**
 * Creates DOMRects, generally used to help the editor find a specific location on the screen.
 * @param editor - The lexical editor
 * @param range - A fragment of a document that can contain nodes and parts of text nodes.
 * @returns The selectionRects as an array.
 */ function createRectsFromDOMRange(editor, range) {
    const rootElement = editor.getRootElement();
    if (rootElement === null) {
        return [];
    }
    const rootRect = rootElement.getBoundingClientRect();
    const computedStyle = getComputedStyle(rootElement);
    const rootPadding = parseFloat(computedStyle.paddingLeft) + parseFloat(computedStyle.paddingRight);
    const selectionRects = Array.from(range.getClientRects());
    let selectionRectsLength = selectionRects.length;
    //sort rects from top left to bottom right.
    selectionRects.sort((a, b)=>{
        const top = a.top - b.top;
        // Some rects match position closely, but not perfectly,
        // so we give a 3px tolerance.
        if (Math.abs(top) <= 3) {
            return a.left - b.left;
        }
        return top;
    });
    let prevRect;
    for(let i = 0; i < selectionRectsLength; i++){
        const selectionRect = selectionRects[i];
        // Exclude rects that overlap preceding Rects in the sorted list.
        const isOverlappingRect = prevRect && prevRect.top <= selectionRect.top && prevRect.top + prevRect.height > selectionRect.top && prevRect.left + prevRect.width > selectionRect.left;
        // Exclude selections that span the entire element
        const selectionSpansElement = selectionRect.width + rootPadding === rootRect.width;
        if (isOverlappingRect || selectionSpansElement) {
            selectionRects.splice(i--, 1);
            selectionRectsLength--;
            continue;
        }
        prevRect = selectionRect;
    }
    return selectionRects;
}
/**
 * Creates an object containing all the styles and their values provided in the CSS string.
 * @param css - The CSS string of styles and their values.
 * @returns The styleObject containing all the styles and their values.
 */ function getStyleObjectFromRawCSS(css) {
    const styleObject = {};
    if (!css) {
        return styleObject;
    }
    const styles = css.split(';');
    for (const style of styles){
        if (style !== '') {
            const [key, value] = style.split(/:([^]+)/); // split on first colon
            if (key && value) {
                styleObject[key.trim()] = value.trim();
            }
        }
    }
    return styleObject;
}
/**
 * Given a CSS string, returns an object from the style cache.
 * @param css - The CSS property as a string.
 * @returns The value of the given CSS property.
 */ function getStyleObjectFromCSS(css) {
    let value = CSS_TO_STYLES.get(css);
    if (value === undefined) {
        value = getStyleObjectFromRawCSS(css);
        CSS_TO_STYLES.set(css, value);
    }
    {
        // Freeze the value in DEV to prevent accidental mutations
        Object.freeze(value);
    }
    return value;
}
/**
 * Gets the CSS styles from the style object.
 * @param styles - The style object containing the styles to get.
 * @returns A string containing the CSS styles and their values.
 */ function getCSSFromStyleObject(styles) {
    let css = '';
    for(const style in styles){
        if (style) {
            css += `${style}: ${styles[style]};`;
        }
    }
    return css;
}
/**
 * Gets the computed DOM styles of the element.
 * @param node - The node to check the styles for.
 * @returns the computed styles of the element or null if there is no DOM element or no default view for the document.
 */ function $getComputedStyleForElement(element) {
    const editor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getEditor"])();
    const domElement = editor.getElementByKey(element.getKey());
    if (domElement === null) {
        return null;
    }
    const view = domElement.ownerDocument.defaultView;
    if (view === null) {
        return null;
    }
    return view.getComputedStyle(domElement);
}
/**
 * Gets the computed DOM styles of the parent of the node.
 * @param node - The node to check its parent's styles for.
 * @returns the computed styles of the node or null if there is no DOM element or no default view for the document.
 */ function $getComputedStyleForParent(node) {
    const parent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootNode"])(node) ? node : node.getParentOrThrow();
    return $getComputedStyleForElement(parent);
}
/**
 * Determines whether a node's parent is RTL.
 * @param node - The node to check whether it is RTL.
 * @returns whether the node is RTL.
 */ function $isParentRTL(node) {
    const styles = $getComputedStyleForParent(node);
    return styles !== null && styles.direction === 'rtl';
}
/**
 * Generally used to append text content to HTML and JSON. Grabs the text content and "slices"
 * it to be generated into the new TextNode.
 * @param selection - The selection containing the node whose TextNode is to be edited.
 * @param textNode - The TextNode to be edited.
 * @returns The updated TextNode.
 */ function $sliceSelectedTextNodeContent(selection, textNode) {
    const anchorAndFocus = selection.getStartEndPoints();
    if (textNode.isSelected(selection) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTokenOrSegmented"])(textNode) && anchorAndFocus !== null) {
        const [anchor, focus] = anchorAndFocus;
        const isBackward = selection.isBackward();
        const anchorNode = anchor.getNode();
        const focusNode = focus.getNode();
        const isAnchor = textNode.is(anchorNode);
        const isFocus = textNode.is(focusNode);
        if (isAnchor || isFocus) {
            const [anchorOffset, focusOffset] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getCharacterOffsets"])(selection);
            const isSame = anchorNode.is(focusNode);
            const isFirst = textNode.is(isBackward ? focusNode : anchorNode);
            const isLast = textNode.is(isBackward ? anchorNode : focusNode);
            let startOffset = 0;
            let endOffset = undefined;
            if (isSame) {
                startOffset = anchorOffset > focusOffset ? focusOffset : anchorOffset;
                endOffset = anchorOffset > focusOffset ? anchorOffset : focusOffset;
            } else if (isFirst) {
                const offset = isBackward ? focusOffset : anchorOffset;
                startOffset = offset;
                endOffset = undefined;
            } else if (isLast) {
                const offset = isBackward ? anchorOffset : focusOffset;
                startOffset = 0;
                endOffset = offset;
            }
            // NOTE: This mutates __text directly because the primary use case is to
            // modify a $cloneWithProperties node that should never be added
            // to the EditorState so we must not call getWritable via setTextContent
            textNode.__text = textNode.__text.slice(startOffset, endOffset);
        }
    }
    return textNode;
}
/**
 * Determines if the current selection is at the end of the node.
 * @param point - The point of the selection to test.
 * @returns true if the provided point offset is in the last possible position, false otherwise.
 */ function $isAtNodeEnd(point) {
    if (point.type === 'text') {
        return point.offset === point.getNode().getTextContentSize();
    }
    const node = point.getNode();
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node)) {
        formatDevErrorMessage(`isAtNodeEnd: node must be a TextNode or ElementNode`);
    }
    return point.offset === node.getChildrenSize();
}
/**
 * Trims text from a node in order to shorten it, eg. to enforce a text's max length. If it deletes text
 * that is an ancestor of the anchor then it will leave 2 indents, otherwise, if no text content exists, it deletes
 * the TextNode. It will move the focus to either the end of any left over text or beginning of a new TextNode.
 * @param editor - The lexical editor.
 * @param anchor - The anchor of the current selection, where the selection should be pointing.
 * @param delCount - The amount of characters to delete. Useful as a dynamic variable eg. textContentSize - maxLength;
 */ function $trimTextContentFromAnchor(editor, anchor, delCount) {
    // Work from the current selection anchor point
    let currentNode = anchor.getNode();
    let remaining = delCount;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(currentNode)) {
        const descendantNode = currentNode.getDescendantByIndex(anchor.offset);
        if (descendantNode !== null) {
            currentNode = descendantNode;
        }
    }
    while(remaining > 0 && currentNode !== null){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(currentNode)) {
            const lastDescendant = currentNode.getLastDescendant();
            if (lastDescendant !== null) {
                currentNode = lastDescendant;
            }
        }
        let nextNode = currentNode.getPreviousSibling();
        let additionalElementWhitespace = 0;
        if (nextNode === null) {
            let parent = currentNode.getParentOrThrow();
            let parentSibling = parent.getPreviousSibling();
            while(parentSibling === null){
                parent = parent.getParent();
                if (parent === null) {
                    nextNode = null;
                    break;
                }
                parentSibling = parent.getPreviousSibling();
            }
            if (parent !== null) {
                additionalElementWhitespace = parent.isInline() ? 0 : 2;
                nextNode = parentSibling;
            }
        }
        let text = currentNode.getTextContent();
        // If the text is empty, we need to consider adding in two line breaks to match
        // the content if we were to get it from its parent.
        if (text === '' && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(currentNode) && !currentNode.isInline()) {
            // TODO: should this be handled in core?
            text = '\n\n';
        }
        const currentNodeSize = text.length;
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(currentNode) || remaining >= currentNodeSize) {
            const parent = currentNode.getParent();
            currentNode.remove();
            if (parent != null && parent.getChildrenSize() === 0 && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootNode"])(parent)) {
                parent.remove();
            }
            remaining -= currentNodeSize + additionalElementWhitespace;
            currentNode = nextNode;
        } else {
            const key = currentNode.getKey();
            // See if we can just revert it to what was in the last editor state
            const prevTextContent = editor.getEditorState().read(()=>{
                const prevNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNodeByKey"])(key);
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(prevNode) && prevNode.isSimpleText()) {
                    return prevNode.getTextContent();
                }
                return null;
            });
            const offset = currentNodeSize - remaining;
            const slicedText = text.slice(0, offset);
            if (prevTextContent !== null && prevTextContent !== text) {
                const prevSelection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getPreviousSelection"])();
                let target = currentNode;
                if (!currentNode.isSimpleText()) {
                    const textNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(prevTextContent);
                    currentNode.replace(textNode);
                    target = textNode;
                } else {
                    currentNode.setTextContent(prevTextContent);
                }
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(prevSelection) && prevSelection.isCollapsed()) {
                    const prevOffset = prevSelection.anchor.offset;
                    target.select(prevOffset, prevOffset);
                }
            } else if (currentNode.isSimpleText()) {
                // Split text
                const isSelected = anchor.key === key;
                let anchorOffset = anchor.offset;
                // Move offset to end if it's less than the remaining number, otherwise
                // we'll have a negative splitStart.
                if (anchorOffset < remaining) {
                    anchorOffset = currentNodeSize;
                }
                const splitStart = isSelected ? anchorOffset - remaining : 0;
                const splitEnd = isSelected ? anchorOffset : offset;
                if (isSelected && splitStart === 0) {
                    const [excessNode] = currentNode.splitText(splitStart, splitEnd);
                    excessNode.remove();
                } else {
                    const [, excessNode] = currentNode.splitText(splitStart, splitEnd);
                    excessNode.remove();
                }
            } else {
                const textNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(slicedText);
                currentNode.replace(textNode);
            }
            remaining = 0;
        }
    }
}
/**
 * Gets the TextNode's style object and adds the styles to the CSS.
 * @param node - The TextNode to add styles to.
 */ function $addNodeStyle(node) {
    const CSSText = node.getStyle();
    const styles = getStyleObjectFromRawCSS(CSSText);
    CSS_TO_STYLES.set(CSSText, styles);
}
/**
 * Applies the provided styles to the given TextNode, ElementNode, or
 * collapsed RangeSelection.
 *
 * @param target - The TextNode, ElementNode, or collapsed RangeSelection to apply the styles to
 * @param patch - The patch to apply, which can include multiple styles. \\{CSSProperty: value\\} . Can also accept a function that returns the new property value.
 */ function $patchStyle(target, patch) {
    if (!((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(target) ? target.isCollapsed() : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(target) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(target))) {
        formatDevErrorMessage(`$patchStyle must only be called with a TextNode, ElementNode, or collapsed RangeSelection`);
    }
    const prevStyles = getStyleObjectFromCSS((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(target) ? target.style : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(target) ? target.getStyle() : target.getTextStyle());
    const newStyles = Object.entries(patch).reduce((styles, [key, value])=>{
        if (typeof value === 'function') {
            styles[key] = value(prevStyles[key], target);
        } else if (value === null) {
            delete styles[key];
        } else {
            styles[key] = value;
        }
        return styles;
    }, {
        ...prevStyles
    });
    const newCSSText = getCSSFromStyleObject(newStyles);
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(target) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(target)) {
        target.setStyle(newCSSText);
    } else {
        target.setTextStyle(newCSSText);
    }
    CSS_TO_STYLES.set(newCSSText, newStyles);
}
/**
 * Applies the provided styles to the TextNodes in the provided Selection.
 * Will update partially selected TextNodes by splitting the TextNode and applying
 * the styles to the appropriate one.
 * @param selection - The selected node(s) to update.
 * @param patch - The patch to apply, which can include multiple styles. \\{CSSProperty: value\\} . Can also accept a function that returns the new property value.
 */ function $patchStyleText(selection, patch) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) && selection.isCollapsed()) {
        $patchStyle(selection, patch);
        const emptyNode = selection.anchor.getNode();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(emptyNode) && emptyNode.isEmpty()) {
            $patchStyle(emptyNode, patch);
        }
    }
    $forEachSelectedTextNode((textNode)=>{
        $patchStyle(textNode, patch);
    });
}
function $forEachSelectedTextNode(fn) {
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    if (!selection) {
        return;
    }
    const slicedTextNodes = new Map();
    const getSliceIndices = (node)=>slicedTextNodes.get(node.getKey()) || [
            0,
            node.getTextContentSize()
        ];
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
        for (const slice of (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$caretRangeFromSelection"])(selection).getTextSlices()){
            if (slice) {
                slicedTextNodes.set(slice.caret.origin.getKey(), slice.getSliceIndices());
            }
        }
    }
    const selectedNodes = selection.getNodes();
    for (const selectedNode of selectedNodes){
        if (!((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(selectedNode) && selectedNode.canHaveFormat())) {
            continue;
        }
        const [startOffset, endOffset] = getSliceIndices(selectedNode);
        // No actual text is selected, so do nothing.
        if (endOffset === startOffset) {
            continue;
        }
        // The entire node is selected or a token/segment, so just format it
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTokenOrSegmented"])(selectedNode) || startOffset === 0 && endOffset === selectedNode.getTextContentSize()) {
            fn(selectedNode);
        } else {
            // The node is partially selected, so split it into two or three nodes
            // and style the selected one.
            const splitNodes = selectedNode.splitText(startOffset, endOffset);
            const replacement = splitNodes[startOffset === 0 ? 0 : 1];
            fn(replacement);
        }
    }
    // Prior to NodeCaret #7046 this would have been a side-effect
    // so we do this for test compatibility.
    // TODO: we may want to consider simplifying by removing this
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) && selection.anchor.type === 'text' && selection.focus.type === 'text' && selection.anchor.key === selection.focus.key) {
        $ensureForwardRangeSelection(selection);
    }
}
/**
 * Ensure that the given RangeSelection is not backwards. If it
 * is backwards, then the anchor and focus points will be swapped
 * in-place. Ensuring that the selection is a writable RangeSelection
 * is the responsibility of the caller (e.g. in a read-only context
 * you will want to clone $getSelection() before using this).
 *
 * @param selection a writable RangeSelection
 */ function $ensureForwardRangeSelection(selection) {
    if (selection.isBackward()) {
        const { anchor, focus } = selection;
        // stash for the in-place swap
        const { key, offset, type } = anchor;
        anchor.set(focus.key, focus.offset, focus.type);
        focus.set(key, offset, type);
    }
}
function $copyBlockFormatIndent(srcNode, destNode) {
    const format = srcNode.getFormatType();
    const indent = srcNode.getIndent();
    if (format !== destNode.getFormatType()) {
        destNode.setFormat(format);
    }
    if (indent !== destNode.getIndent()) {
        destNode.setIndent(indent);
    }
}
/**
 * Converts all nodes in the selection that are of one block type to another.
 * @param selection - The selected blocks to be converted.
 * @param $createElement - The function that creates the node. eg. $createParagraphNode.
 * @param $afterCreateElement - The function that updates the new node based on the previous one ($copyBlockFormatIndent by default)
 */ function $setBlocksType(selection, $createElement, $afterCreateElement = $copyBlockFormatIndent) {
    if (selection === null) {
        return;
    }
    // Selections tend to not include their containing blocks so we effectively
    // expand it here
    const anchorAndFocus = selection.getStartEndPoints();
    const blockMap = new Map();
    let newSelection = null;
    if (anchorAndFocus) {
        const [anchor, focus] = anchorAndFocus;
        newSelection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createRangeSelection"])();
        newSelection.anchor.set(anchor.key, anchor.offset, anchor.type);
        newSelection.focus.set(focus.key, focus.offset, focus.type);
        const anchorBlock = $getAncestor(anchor.getNode(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INTERNAL_$isBlock"]);
        const focusBlock = $getAncestor(focus.getNode(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INTERNAL_$isBlock"]);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(anchorBlock)) {
            blockMap.set(anchorBlock.getKey(), anchorBlock);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(focusBlock)) {
            blockMap.set(focusBlock.getKey(), focusBlock);
        }
    }
    for (const node of selection.getNodes()){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INTERNAL_$isBlock"])(node)) {
            blockMap.set(node.getKey(), node);
        } else if (anchorAndFocus === null) {
            const ancestorBlock = $getAncestor(node, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INTERNAL_$isBlock"]);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(ancestorBlock)) {
                blockMap.set(ancestorBlock.getKey(), ancestorBlock);
            }
        }
    }
    for (const [key, prevNode] of blockMap){
        const element = $createElement();
        $afterCreateElement(prevNode, element);
        prevNode.replace(element, true);
        if (newSelection) {
            if (key === newSelection.anchor.key) {
                newSelection.anchor.set(element.getKey(), newSelection.anchor.offset, newSelection.anchor.type);
            }
            if (key === newSelection.focus.key) {
                newSelection.focus.set(element.getKey(), newSelection.focus.offset, newSelection.focus.type);
            }
        }
    }
    if (newSelection && selection.is((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])())) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setSelection"])(newSelection);
    }
}
function isPointAttached(point) {
    return point.getNode().isAttached();
}
function $removeParentEmptyElements(startingNode) {
    let node = startingNode;
    while(node !== null && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootOrShadowRoot"])(node)){
        const latest = node.getLatest();
        const parentNode = node.getParent();
        if (latest.getChildrenSize() === 0) {
            node.remove(true);
        }
        node = parentNode;
    }
}
/**
 * @deprecated In favor of $setBlockTypes
 * Wraps all nodes in the selection into another node of the type returned by createElement.
 * @param selection - The selection of nodes to be wrapped.
 * @param createElement - A function that creates the wrapping ElementNode. eg. $createParagraphNode.
 * @param wrappingElement - An element to append the wrapped selection and its children to.
 */ function $wrapNodes(selection, createElement, wrappingElement = null) {
    const anchorAndFocus = selection.getStartEndPoints();
    const anchor = anchorAndFocus ? anchorAndFocus[0] : null;
    const nodes = selection.getNodes();
    const nodesLength = nodes.length;
    if (anchor !== null && (nodesLength === 0 || nodesLength === 1 && anchor.type === 'element' && anchor.getNode().getChildrenSize() === 0)) {
        const target = anchor.type === 'text' ? anchor.getNode().getParentOrThrow() : anchor.getNode();
        const children = target.getChildren();
        let element = createElement();
        element.setFormat(target.getFormatType());
        element.setIndent(target.getIndent());
        children.forEach((child)=>element.append(child));
        if (wrappingElement) {
            element = wrappingElement.append(element);
        }
        target.replace(element);
        return;
    }
    let topLevelNode = null;
    let descendants = [];
    for(let i = 0; i < nodesLength; i++){
        const node = nodes[i];
        // Determine whether wrapping has to be broken down into multiple chunks. This can happen if the
        // user selected multiple Root-like nodes that have to be treated separately as if they are
        // their own branch. I.e. you don't want to wrap a whole table, but rather the contents of each
        // of each of the cell nodes.
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootOrShadowRoot"])(node)) {
            $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);
            descendants = [];
            topLevelNode = node;
        } else if (topLevelNode === null || topLevelNode !== null && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$hasAncestor"])(node, topLevelNode)) {
            descendants.push(node);
        } else {
            $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);
            descendants = [
                node
            ];
        }
    }
    $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);
}
/**
 * Wraps each node into a new ElementNode.
 * @param selection - The selection of nodes to wrap.
 * @param nodes - An array of nodes, generally the descendants of the selection.
 * @param nodesLength - The length of nodes.
 * @param createElement - A function that creates the wrapping ElementNode. eg. $createParagraphNode.
 * @param wrappingElement - An element to wrap all the nodes into.
 * @returns
 */ function $wrapNodesImpl(selection, nodes, nodesLength, createElement, wrappingElement = null) {
    if (nodes.length === 0) {
        return;
    }
    const firstNode = nodes[0];
    const elementMapping = new Map();
    const elements = [];
    // The below logic is to find the right target for us to
    // either insertAfter/insertBefore/append the corresponding
    // elements to. This is made more complicated due to nested
    // structures.
    let target = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(firstNode) ? firstNode : firstNode.getParentOrThrow();
    if (target.isInline()) {
        target = target.getParentOrThrow();
    }
    let targetIsPrevSibling = false;
    while(target !== null){
        const prevSibling = target.getPreviousSibling();
        if (prevSibling !== null) {
            target = prevSibling;
            targetIsPrevSibling = true;
            break;
        }
        target = target.getParentOrThrow();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootOrShadowRoot"])(target)) {
            break;
        }
    }
    const emptyElements = new Set();
    // Find any top level empty elements
    for(let i = 0; i < nodesLength; i++){
        const node = nodes[i];
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node) && node.getChildrenSize() === 0) {
            emptyElements.add(node.getKey());
        }
    }
    const movedNodes = new Set();
    // Move out all leaf nodes into our elements array.
    // If we find a top level empty element, also move make
    // an element for that.
    for(let i = 0; i < nodesLength; i++){
        const node = nodes[i];
        let parent = node.getParent();
        if (parent !== null && parent.isInline()) {
            parent = parent.getParent();
        }
        if (parent !== null && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLeafNode"])(node) && !movedNodes.has(node.getKey())) {
            const parentKey = parent.getKey();
            if (elementMapping.get(parentKey) === undefined) {
                const targetElement = createElement();
                targetElement.setFormat(parent.getFormatType());
                targetElement.setIndent(parent.getIndent());
                elements.push(targetElement);
                elementMapping.set(parentKey, targetElement);
                // Move node and its siblings to the new
                // element.
                parent.getChildren().forEach((child)=>{
                    targetElement.append(child);
                    movedNodes.add(child.getKey());
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(child)) {
                        // Skip nested leaf nodes if the parent has already been moved
                        child.getChildrenKeys().forEach((key)=>movedNodes.add(key));
                    }
                });
                $removeParentEmptyElements(parent);
            }
        } else if (emptyElements.has(node.getKey())) {
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node)) {
                formatDevErrorMessage(`Expected node in emptyElements to be an ElementNode`);
            }
            const targetElement = createElement();
            targetElement.setFormat(node.getFormatType());
            targetElement.setIndent(node.getIndent());
            elements.push(targetElement);
            node.remove(true);
        }
    }
    if (wrappingElement !== null) {
        for(let i = 0; i < elements.length; i++){
            const element = elements[i];
            wrappingElement.append(element);
        }
    }
    let lastElement = null;
    // If our target is Root-like, let's see if we can re-adjust
    // so that the target is the first child instead.
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootOrShadowRoot"])(target)) {
        if (targetIsPrevSibling) {
            if (wrappingElement !== null) {
                target.insertAfter(wrappingElement);
            } else {
                for(let i = elements.length - 1; i >= 0; i--){
                    const element = elements[i];
                    target.insertAfter(element);
                }
            }
        } else {
            const firstChild = target.getFirstChild();
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(firstChild)) {
                target = firstChild;
            }
            if (firstChild === null) {
                if (wrappingElement) {
                    target.append(wrappingElement);
                } else {
                    for(let i = 0; i < elements.length; i++){
                        const element = elements[i];
                        target.append(element);
                        lastElement = element;
                    }
                }
            } else {
                if (wrappingElement !== null) {
                    firstChild.insertBefore(wrappingElement);
                } else {
                    for(let i = 0; i < elements.length; i++){
                        const element = elements[i];
                        firstChild.insertBefore(element);
                        lastElement = element;
                    }
                }
            }
        }
    } else {
        if (wrappingElement) {
            target.insertAfter(wrappingElement);
        } else {
            for(let i = elements.length - 1; i >= 0; i--){
                const element = elements[i];
                target.insertAfter(element);
                lastElement = element;
            }
        }
    }
    const prevSelection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getPreviousSelection"])();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(prevSelection) && isPointAttached(prevSelection.anchor) && isPointAttached(prevSelection.focus)) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setSelection"])(prevSelection.clone());
    } else if (lastElement !== null) {
        lastElement.selectEnd();
    } else {
        selection.dirty = true;
    }
}
/**
 * Tests if the selection's parent element has vertical writing mode.
 * @param selection - The selection whose parent to test.
 * @returns true if the selection's parent has vertical writing mode (writing-mode: vertical-rl), false otherwise.
 */ function $isEditorVerticalOrientation(selection) {
    const computedStyle = $getComputedStyle(selection);
    return computedStyle !== null && computedStyle.writingMode === 'vertical-rl';
}
/**
 * Gets the computed DOM styles of the parent of the selection's anchor node.
 * @param selection - The selection to check the styles for.
 * @returns the computed styles of the node or null if there is no DOM element or no default view for the document.
 */ function $getComputedStyle(selection) {
    const anchorNode = selection.anchor.getNode();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(anchorNode)) {
        return $getComputedStyleForElement(anchorNode);
    }
    return $getComputedStyleForParent(anchorNode);
}
/**
 * Determines if the default character selection should be overridden. Used with DecoratorNodes
 * @param selection - The selection whose default character selection may need to be overridden.
 * @param isBackward - Is the selection backwards (the focus comes before the anchor)?
 * @returns true if it should be overridden, false if not.
 */ function $shouldOverrideDefaultCharacterSelection(selection, isBackward) {
    const isVertical = $isEditorVerticalOrientation(selection);
    // In vertical writing mode, we adjust the direction for correct caret movement
    let adjustedIsBackward = isVertical ? !isBackward : isBackward;
    // In right-to-left writing mode, we invert the direction for correct caret movement
    if ($isParentElementRTL(selection)) {
        adjustedIsBackward = !adjustedIsBackward;
    }
    const focusCaret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$caretFromPoint"])(selection.focus, adjustedIsBackward ? 'previous' : 'next');
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isExtendableTextPointCaret"])(focusCaret)) {
        return false;
    }
    for (const nextCaret of (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$extendCaretToRange"])(focusCaret)){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isChildCaret"])(nextCaret)) {
            return !nextCaret.origin.isInline();
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(nextCaret.origin)) {
            continue;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isDecoratorNode"])(nextCaret.origin)) {
            return true;
        }
        break;
    }
    return false;
}
/**
 * Moves the selection according to the arguments.
 * @param selection - The selected text or nodes.
 * @param isHoldingShift - Is the shift key being held down during the operation.
 * @param isBackward - Is the selection selected backwards (the focus comes before the anchor)?
 * @param granularity - The distance to adjust the current selection.
 */ function $moveCaretSelection(selection, isHoldingShift, isBackward, granularity) {
    selection.modify(isHoldingShift ? 'extend' : 'move', isBackward, granularity);
}
/**
 * Tests a parent element for right to left direction.
 * @param selection - The selection whose parent is to be tested.
 * @returns true if the selections' parent element has a direction of 'rtl' (right to left), false otherwise.
 */ function $isParentElementRTL(selection) {
    const computedStyle = $getComputedStyle(selection);
    return computedStyle !== null && computedStyle.direction === 'rtl';
}
/**
 * Moves selection by character according to arguments.
 * @param selection - The selection of the characters to move.
 * @param isHoldingShift - Is the shift key being held down during the operation.
 * @param isBackward - Is the selection backward (the focus comes before the anchor)?
 */ function $moveCharacter(selection, isHoldingShift, isBackward) {
    const isRTL = $isParentElementRTL(selection);
    const isVertical = $isEditorVerticalOrientation(selection);
    // In vertical-rl writing mode, arrow key directions need to be flipped
    // to match the visual flow of text (top to bottom, right to left)
    let adjustedIsBackward;
    if (isVertical) {
        // In vertical-rl mode, we need to completely invert the direction
        // Left arrow (backward) should move down (forward)
        // Right arrow (forward) should move up (backward)
        adjustedIsBackward = !isBackward;
    } else if (isRTL) {
        // In horizontal RTL mode, use the standard RTL behavior
        adjustedIsBackward = !isBackward;
    } else {
        // Standard LTR horizontal text
        adjustedIsBackward = isBackward;
    }
    // Apply the direction adjustment to move the caret
    $moveCaretSelection(selection, isHoldingShift, adjustedIsBackward, 'character');
}
/**
 * Returns the current value of a CSS property for Nodes, if set. If not set, it returns the defaultValue.
 * @param node - The node whose style value to get.
 * @param styleProperty - The CSS style property.
 * @param defaultValue - The default value for the property.
 * @returns The value of the property for node.
 */ function $getNodeStyleValueForProperty(node, styleProperty, defaultValue) {
    const css = node.getStyle();
    const styleObject = getStyleObjectFromCSS(css);
    if (styleObject !== null) {
        return styleObject[styleProperty] || defaultValue;
    }
    return defaultValue;
}
/**
 * Returns the current value of a CSS property for TextNodes in the Selection, if set. If not set, it returns the defaultValue.
 * If all TextNodes do not have the same value, it returns an empty string.
 * @param selection - The selection of TextNodes whose value to find.
 * @param styleProperty - The CSS style property.
 * @param defaultValue - The default value for the property, defaults to an empty string.
 * @returns The value of the property for the selected TextNodes.
 */ function $getSelectionStyleValueForProperty(selection, styleProperty, defaultValue = '') {
    let styleValue = null;
    const nodes = selection.getNodes();
    const anchor = selection.anchor;
    const focus = selection.focus;
    const isBackward = selection.isBackward();
    const endOffset = isBackward ? focus.offset : anchor.offset;
    const endNode = isBackward ? focus.getNode() : anchor.getNode();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) && selection.isCollapsed() && selection.style !== '') {
        const css = selection.style;
        const styleObject = getStyleObjectFromCSS(css);
        if (styleObject !== null && styleProperty in styleObject) {
            return styleObject[styleProperty];
        }
    }
    for(let i = 0; i < nodes.length; i++){
        const node = nodes[i];
        // if no actual characters in the end node are selected, we don't
        // include it in the selection for purposes of determining style
        // value
        if (i !== 0 && endOffset === 0 && node.is(endNode)) {
            continue;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(node)) {
            const nodeStyleValue = $getNodeStyleValueForProperty(node, styleProperty, defaultValue);
            if (styleValue === null) {
                styleValue = nodeStyleValue;
            } else if (styleValue !== nodeStyleValue) {
                // multiple text nodes are in the selection and they don't all
                // have the same style.
                styleValue = '';
                break;
            }
        }
    }
    return styleValue === null ? defaultValue : styleValue;
}
function $getAncestor(node, predicate) {
    let parent = node;
    while(parent !== null && parent.getParent() !== null && !predicate(parent)){
        parent = parent.getParentOrThrow();
    }
    return predicate(parent) ? parent : null;
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /** @deprecated renamed to {@link $trimTextContentFromAnchor} by @lexical/eslint-plugin rules-of-lexical */ const trimTextContentFromAnchor = $trimTextContentFromAnchor;
;
}}),
"[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "$descendantsMatching": (()=>$descendantsMatching),
    "$dfs": (()=>$dfs),
    "$dfsIterator": (()=>$dfsIterator),
    "$filter": (()=>$filter),
    "$findMatchingParent": (()=>$findMatchingParent),
    "$firstToLastIterator": (()=>$firstToLastIterator),
    "$getAdjacentCaret": (()=>$getAdjacentCaret),
    "$getAdjacentSiblingOrParentSiblingCaret": (()=>$getAdjacentSiblingOrParentSiblingCaret),
    "$getDepth": (()=>$getDepth),
    "$getNearestBlockElementAncestorOrThrow": (()=>$getNearestBlockElementAncestorOrThrow),
    "$getNearestNodeOfType": (()=>$getNearestNodeOfType),
    "$getNextRightPreorderNode": (()=>$getNextRightPreorderNode),
    "$getNextSiblingOrParentSibling": (()=>$getNextSiblingOrParentSibling),
    "$insertFirst": (()=>$insertFirst),
    "$insertNodeToNearestRoot": (()=>$insertNodeToNearestRoot),
    "$insertNodeToNearestRootAtCaret": (()=>$insertNodeToNearestRootAtCaret),
    "$isEditorIsNestedEditor": (()=>$isEditorIsNestedEditor),
    "$lastToFirstIterator": (()=>$lastToFirstIterator),
    "$restoreEditorState": (()=>$restoreEditorState),
    "$reverseDfs": (()=>$reverseDfs),
    "$reverseDfsIterator": (()=>$reverseDfsIterator),
    "$unwrapAndFilterDescendants": (()=>$unwrapAndFilterDescendants),
    "$unwrapNode": (()=>$unwrapNode),
    "$wrapNodeInElement": (()=>$wrapNodeInElement),
    "CAN_USE_BEFORE_INPUT": (()=>CAN_USE_BEFORE_INPUT),
    "CAN_USE_DOM": (()=>CAN_USE_DOM),
    "IS_ANDROID": (()=>IS_ANDROID),
    "IS_ANDROID_CHROME": (()=>IS_ANDROID_CHROME),
    "IS_APPLE": (()=>IS_APPLE),
    "IS_APPLE_WEBKIT": (()=>IS_APPLE_WEBKIT),
    "IS_CHROME": (()=>IS_CHROME),
    "IS_FIREFOX": (()=>IS_FIREFOX),
    "IS_IOS": (()=>IS_IOS),
    "IS_SAFARI": (()=>IS_SAFARI),
    "addClassNamesToElement": (()=>addClassNamesToElement),
    "calculateZoomLevel": (()=>calculateZoomLevel),
    "isMimeType": (()=>isMimeType),
    "makeStateWrapper": (()=>makeStateWrapper),
    "markSelection": (()=>markSelection),
    "mediaFileReader": (()=>mediaFileReader),
    "mergeRegister": (()=>mergeRegister),
    "objectKlassEquals": (()=>objectKlassEquals),
    "positionNodeOnRange": (()=>mlcPositionNodeOnRange),
    "registerNestedElementResolver": (()=>registerNestedElementResolver),
    "removeClassNamesFromElement": (()=>removeClassNamesFromElement),
    "selectionAlwaysOnDisplay": (()=>selectionAlwaysOnDisplay)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/selection/LexicalSelection.dev.mjs [app-client] (ecmascript) <locals>");
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Do not require this module directly! Use normal `invariant` calls.
function formatDevErrorMessage(message) {
    throw new Error(message);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const CAN_USE_DOM$1 = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const documentMode = CAN_USE_DOM$1 && 'documentMode' in document ? document.documentMode : null;
const IS_APPLE$1 = CAN_USE_DOM$1 && /Mac|iPod|iPhone|iPad/.test(navigator.platform);
const IS_FIREFOX$1 = CAN_USE_DOM$1 && /^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent);
const CAN_USE_BEFORE_INPUT$1 = CAN_USE_DOM$1 && 'InputEvent' in window && !documentMode ? 'getTargetRanges' in new window.InputEvent('input') : false;
const IS_SAFARI$1 = CAN_USE_DOM$1 && /Version\/[\d.]+.*Safari/.test(navigator.userAgent);
const IS_IOS$1 = CAN_USE_DOM$1 && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
const IS_ANDROID$1 = CAN_USE_DOM$1 && /Android/.test(navigator.userAgent);
// Keep these in case we need to use them in the future.
// export const IS_WINDOWS: boolean = CAN_USE_DOM && /Win/.test(navigator.platform);
const IS_CHROME$1 = CAN_USE_DOM$1 && /^(?=.*Chrome).*/i.test(navigator.userAgent);
// export const canUseTextInputEvent: boolean = CAN_USE_DOM && 'TextEvent' in window && !documentMode;
const IS_ANDROID_CHROME$1 = CAN_USE_DOM$1 && IS_ANDROID$1 && IS_CHROME$1;
const IS_APPLE_WEBKIT$1 = CAN_USE_DOM$1 && /AppleWebKit\/[\d.]+/.test(navigator.userAgent) && !IS_CHROME$1;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function normalizeClassNames(...classNames) {
    const rval = [];
    for (const className of classNames){
        if (className && typeof className === 'string') {
            for (const [s] of className.matchAll(/\S+/g)){
                rval.push(s);
            }
        }
    }
    return rval;
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * Returns a function that will execute all functions passed when called. It is generally used
 * to register multiple lexical listeners and then tear them down with a single function call, such
 * as React's useEffect hook.
 * @example
 * ```ts
 * useEffect(() => {
 *   return mergeRegister(
 *     editor.registerCommand(...registerCommand1 logic),
 *     editor.registerCommand(...registerCommand2 logic),
 *     editor.registerCommand(...registerCommand3 logic)
 *   )
 * }, [editor])
 * ```
 * In this case, useEffect is returning the function returned by mergeRegister as a cleanup
 * function to be executed after either the useEffect runs again (due to one of its dependencies
 * updating) or the component it resides in unmounts.
 * Note the functions don't necessarily need to be in an array as all arguments
 * are considered to be the func argument and spread from there.
 * The order of cleanup is the reverse of the argument order. Generally it is
 * expected that the first "acquire" will be "released" last (LIFO order),
 * because a later step may have some dependency on an earlier one.
 * @param func - An array of cleanup functions meant to be executed by the returned function.
 * @returns the function which executes all the passed cleanup functions.
 */ function mergeRegister(...func) {
    return ()=>{
        for(let i = func.length - 1; i >= 0; i--){
            func[i]();
        }
        // Clean up the references and make future calls a no-op
        func.length = 0;
    };
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function px(value) {
    return `${value}px`;
}
const mutationObserverConfig = {
    attributes: true,
    characterData: true,
    childList: true,
    subtree: true
};
function prependDOMNode(parent, node) {
    parent.insertBefore(node, parent.firstChild);
}
/**
 * Place one or multiple newly created Nodes at the passed Range's position.
 * Multiple nodes will only be created when the Range spans multiple lines (aka
 * client rects).
 *
 * This function can come particularly useful to highlight particular parts of
 * the text without interfering with the EditorState, that will often replicate
 * the state across collab and clipboard.
 *
 * This function accounts for DOM updates which can modify the passed Range.
 * Hence, the function return to remove the listener.
 */ function mlcPositionNodeOnRange(editor, range, onReposition) {
    let rootDOMNode = null;
    let parentDOMNode = null;
    let observer = null;
    let lastNodes = [];
    const wrapperNode = document.createElement('div');
    wrapperNode.style.position = 'relative';
    function position() {
        if (!(rootDOMNode !== null)) {
            formatDevErrorMessage(`Unexpected null rootDOMNode`);
        }
        if (!(parentDOMNode !== null)) {
            formatDevErrorMessage(`Unexpected null parentDOMNode`);
        }
        const { left: parentLeft, top: parentTop } = parentDOMNode.getBoundingClientRect();
        const rects = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createRectsFromDOMRange"])(editor, range);
        if (!wrapperNode.isConnected) {
            prependDOMNode(parentDOMNode, wrapperNode);
        }
        let hasRepositioned = false;
        for(let i = 0; i < rects.length; i++){
            const rect = rects[i];
            // Try to reuse the previously created Node when possible, no need to
            // remove/create on the most common case reposition case
            const rectNode = lastNodes[i] || document.createElement('div');
            const rectNodeStyle = rectNode.style;
            if (rectNodeStyle.position !== 'absolute') {
                rectNodeStyle.position = 'absolute';
                hasRepositioned = true;
            }
            const left = px(rect.left - parentLeft);
            if (rectNodeStyle.left !== left) {
                rectNodeStyle.left = left;
                hasRepositioned = true;
            }
            const top = px(rect.top - parentTop);
            if (rectNodeStyle.top !== top) {
                rectNode.style.top = top;
                hasRepositioned = true;
            }
            const width = px(rect.width);
            if (rectNodeStyle.width !== width) {
                rectNode.style.width = width;
                hasRepositioned = true;
            }
            const height = px(rect.height);
            if (rectNodeStyle.height !== height) {
                rectNode.style.height = height;
                hasRepositioned = true;
            }
            if (rectNode.parentNode !== wrapperNode) {
                wrapperNode.append(rectNode);
                hasRepositioned = true;
            }
            lastNodes[i] = rectNode;
        }
        while(lastNodes.length > rects.length){
            lastNodes.pop();
        }
        if (hasRepositioned) {
            onReposition(lastNodes);
        }
    }
    function stop() {
        parentDOMNode = null;
        rootDOMNode = null;
        if (observer !== null) {
            observer.disconnect();
        }
        observer = null;
        wrapperNode.remove();
        for (const node of lastNodes){
            node.remove();
        }
        lastNodes = [];
    }
    function restart() {
        const currentRootDOMNode = editor.getRootElement();
        if (currentRootDOMNode === null) {
            return stop();
        }
        const currentParentDOMNode = currentRootDOMNode.parentElement;
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLElement"])(currentParentDOMNode)) {
            return stop();
        }
        stop();
        rootDOMNode = currentRootDOMNode;
        parentDOMNode = currentParentDOMNode;
        observer = new MutationObserver((mutations)=>{
            const nextRootDOMNode = editor.getRootElement();
            const nextParentDOMNode = nextRootDOMNode && nextRootDOMNode.parentElement;
            if (nextRootDOMNode !== rootDOMNode || nextParentDOMNode !== parentDOMNode) {
                return restart();
            }
            for (const mutation of mutations){
                if (!wrapperNode.contains(mutation.target)) {
                    // TODO throttle
                    return position();
                }
            }
        });
        observer.observe(currentParentDOMNode, mutationObserverConfig);
        position();
    }
    const removeRootListener = editor.registerRootListener(restart);
    return ()=>{
        removeRootListener();
        stop();
    };
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function $getOrderedSelectionPoints(selection) {
    const points = selection.getStartEndPoints();
    return selection.isBackward() ? [
        points[1],
        points[0]
    ] : points;
}
function rangeTargetFromPoint(point, node, dom) {
    if (point.type === 'text' || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node)) {
        const textDOM = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDOMTextNode"])(dom) || dom;
        return [
            textDOM,
            point.offset
        ];
    } else {
        const slot = node.getDOMSlot(dom);
        return [
            slot.element,
            slot.getFirstChildOffset() + point.offset
        ];
    }
}
function rangeFromPoints(editor, start, startNode, startDOM, end, endNode, endDOM) {
    const editorDocument = editor._window ? editor._window.document : document;
    const range = editorDocument.createRange();
    range.setStart(...rangeTargetFromPoint(start, startNode, startDOM));
    range.setEnd(...rangeTargetFromPoint(end, endNode, endDOM));
    return range;
}
/**
 * Place one or multiple newly created Nodes at the current selection. Multiple
 * nodes will only be created when the selection spans multiple lines (aka
 * client rects).
 *
 * This function can come useful when you want to show the selection but the
 * editor has been focused away.
 */ function markSelection(editor, onReposition) {
    let previousAnchorNode = null;
    let previousAnchorNodeDOM = null;
    let previousAnchorOffset = null;
    let previousFocusNode = null;
    let previousFocusNodeDOM = null;
    let previousFocusOffset = null;
    let removeRangeListener = ()=>{};
    function compute(editorState) {
        editorState.read(()=>{
            const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
                // TODO
                previousAnchorNode = null;
                previousAnchorOffset = null;
                previousFocusNode = null;
                previousFocusOffset = null;
                removeRangeListener();
                removeRangeListener = ()=>{};
                return;
            }
            const [start, end] = $getOrderedSelectionPoints(selection);
            const currentStartNode = start.getNode();
            const currentStartNodeKey = currentStartNode.getKey();
            const currentStartOffset = start.offset;
            const currentEndNode = end.getNode();
            const currentEndNodeKey = currentEndNode.getKey();
            const currentEndOffset = end.offset;
            const currentStartNodeDOM = editor.getElementByKey(currentStartNodeKey);
            const currentEndNodeDOM = editor.getElementByKey(currentEndNodeKey);
            const differentStartDOM = previousAnchorNode === null || currentStartNodeDOM !== previousAnchorNodeDOM || currentStartOffset !== previousAnchorOffset || currentStartNodeKey !== previousAnchorNode.getKey();
            const differentEndDOM = previousFocusNode === null || currentEndNodeDOM !== previousFocusNodeDOM || currentEndOffset !== previousFocusOffset || currentEndNodeKey !== previousFocusNode.getKey();
            if ((differentStartDOM || differentEndDOM) && currentStartNodeDOM !== null && currentEndNodeDOM !== null) {
                const range = rangeFromPoints(editor, start, currentStartNode, currentStartNodeDOM, end, currentEndNode, currentEndNodeDOM);
                removeRangeListener();
                removeRangeListener = mlcPositionNodeOnRange(editor, range, (domNodes)=>{
                    if (onReposition === undefined) {
                        for (const domNode of domNodes){
                            const domNodeStyle = domNode.style;
                            if (domNodeStyle.background !== 'Highlight') {
                                domNodeStyle.background = 'Highlight';
                            }
                            if (domNodeStyle.color !== 'HighlightText') {
                                domNodeStyle.color = 'HighlightText';
                            }
                            if (domNodeStyle.marginTop !== px(-1.5)) {
                                domNodeStyle.marginTop = px(-1.5);
                            }
                            if (domNodeStyle.paddingTop !== px(4)) {
                                domNodeStyle.paddingTop = px(4);
                            }
                            if (domNodeStyle.paddingBottom !== px(0)) {
                                domNodeStyle.paddingBottom = px(0);
                            }
                        }
                    } else {
                        onReposition(domNodes);
                    }
                });
            }
            previousAnchorNode = currentStartNode;
            previousAnchorNodeDOM = currentStartNodeDOM;
            previousAnchorOffset = currentStartOffset;
            previousFocusNode = currentEndNode;
            previousFocusNodeDOM = currentEndNodeDOM;
            previousFocusOffset = currentEndOffset;
        });
    }
    compute(editor.getEditorState());
    return mergeRegister(editor.registerUpdateListener(({ editorState })=>compute(editorState)), ()=>{
        removeRangeListener();
    });
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function selectionAlwaysOnDisplay(editor) {
    let removeSelectionMark = null;
    const onSelectionChange = ()=>{
        const domSelection = getSelection();
        const domAnchorNode = domSelection && domSelection.anchorNode;
        const editorRootElement = editor.getRootElement();
        const isSelectionInsideEditor = domAnchorNode !== null && editorRootElement !== null && editorRootElement.contains(domAnchorNode);
        if (isSelectionInsideEditor) {
            if (removeSelectionMark !== null) {
                removeSelectionMark();
                removeSelectionMark = null;
            }
        } else {
            if (removeSelectionMark === null) {
                removeSelectionMark = markSelection(editor);
            }
        }
    };
    document.addEventListener('selectionchange', onSelectionChange);
    return ()=>{
        if (removeSelectionMark !== null) {
            removeSelectionMark();
        }
        document.removeEventListener('selectionchange', onSelectionChange);
    };
}
// Hotfix to export these with inlined types #5918
const CAN_USE_BEFORE_INPUT = CAN_USE_BEFORE_INPUT$1;
const CAN_USE_DOM = CAN_USE_DOM$1;
const IS_ANDROID = IS_ANDROID$1;
const IS_ANDROID_CHROME = IS_ANDROID_CHROME$1;
const IS_APPLE = IS_APPLE$1;
const IS_APPLE_WEBKIT = IS_APPLE_WEBKIT$1;
const IS_CHROME = IS_CHROME$1;
const IS_FIREFOX = IS_FIREFOX$1;
const IS_IOS = IS_IOS$1;
const IS_SAFARI = IS_SAFARI$1;
/**
 * Takes an HTML element and adds the classNames passed within an array,
 * ignoring any non-string types. A space can be used to add multiple classes
 * eg. addClassNamesToElement(element, ['element-inner active', true, null])
 * will add both 'element-inner' and 'active' as classes to that element.
 * @param element - The element in which the classes are added
 * @param classNames - An array defining the class names to add to the element
 */ function addClassNamesToElement(element, ...classNames) {
    const classesToAdd = normalizeClassNames(...classNames);
    if (classesToAdd.length > 0) {
        element.classList.add(...classesToAdd);
    }
}
/**
 * Takes an HTML element and removes the classNames passed within an array,
 * ignoring any non-string types. A space can be used to remove multiple classes
 * eg. removeClassNamesFromElement(element, ['active small', true, null])
 * will remove both the 'active' and 'small' classes from that element.
 * @param element - The element in which the classes are removed
 * @param classNames - An array defining the class names to remove from the element
 */ function removeClassNamesFromElement(element, ...classNames) {
    const classesToRemove = normalizeClassNames(...classNames);
    if (classesToRemove.length > 0) {
        element.classList.remove(...classesToRemove);
    }
}
/**
 * Returns true if the file type matches the types passed within the acceptableMimeTypes array, false otherwise.
 * The types passed must be strings and are CASE-SENSITIVE.
 * eg. if file is of type 'text' and acceptableMimeTypes = ['TEXT', 'IMAGE'] the function will return false.
 * @param file - The file you want to type check.
 * @param acceptableMimeTypes - An array of strings of types which the file is checked against.
 * @returns true if the file is an acceptable mime type, false otherwise.
 */ function isMimeType(file, acceptableMimeTypes) {
    for (const acceptableType of acceptableMimeTypes){
        if (file.type.startsWith(acceptableType)) {
            return true;
        }
    }
    return false;
}
/**
 * Lexical File Reader with:
 *  1. MIME type support
 *  2. batched results (HistoryPlugin compatibility)
 *  3. Order aware (respects the order when multiple Files are passed)
 *
 * const filesResult = await mediaFileReader(files, ['image/']);
 * filesResult.forEach(file => editor.dispatchCommand('INSERT_IMAGE', \\{
 *   src: file.result,
 * \\}));
 */ function mediaFileReader(files, acceptableMimeTypes) {
    const filesIterator = files[Symbol.iterator]();
    return new Promise((resolve, reject)=>{
        const processed = [];
        const handleNextFile = ()=>{
            const { done, value: file } = filesIterator.next();
            if (done) {
                return resolve(processed);
            }
            const fileReader = new FileReader();
            fileReader.addEventListener('error', reject);
            fileReader.addEventListener('load', ()=>{
                const result = fileReader.result;
                if (typeof result === 'string') {
                    processed.push({
                        file,
                        result
                    });
                }
                handleNextFile();
            });
            if (isMimeType(file, acceptableMimeTypes)) {
                fileReader.readAsDataURL(file);
            } else {
                handleNextFile();
            }
        };
        handleNextFile();
    });
}
/**
 * "Depth-First Search" starts at the root/top node of a tree and goes as far as it can down a branch end
 * before backtracking and finding a new path. Consider solving a maze by hugging either wall, moving down a
 * branch until you hit a dead-end (leaf) and backtracking to find the nearest branching path and repeat.
 * It will then return all the nodes found in the search in an array of objects.
 * @param startNode - The node to start the search, if omitted, it will start at the root node.
 * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.
 * @returns An array of objects of all the nodes found by the search, including their depth into the tree.
 * \\{depth: number, node: LexicalNode\\} It will always return at least 1 node (the start node).
 */ function $dfs(startNode, endNode) {
    return Array.from($dfsIterator(startNode, endNode));
}
/**
 * Get the adjacent caret in the same direction
 *
 * @param caret A caret or null
 * @returns `caret.getAdjacentCaret()` or `null`
 */ function $getAdjacentCaret(caret) {
    return caret ? caret.getAdjacentCaret() : null;
}
/**
 * $dfs iterator (right to left). Tree traversal is done on the fly as new values are requested with O(1) memory.
 * @param startNode - The node to start the search, if omitted, it will start at the root node.
 * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.
 * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).
 */ function $reverseDfs(startNode, endNode) {
    return Array.from($reverseDfsIterator(startNode, endNode));
}
/**
 * $dfs iterator (left to right). Tree traversal is done on the fly as new values are requested with O(1) memory.
 * @param startNode - The node to start the search, if omitted, it will start at the root node.
 * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.
 * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).
 */ function $dfsIterator(startNode, endNode) {
    return $dfsCaretIterator('next', startNode, endNode);
}
function $getEndCaret(startNode, direction) {
    const rval = $getAdjacentSiblingOrParentSiblingCaret((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSiblingCaret"])(startNode, direction));
    return rval && rval[0];
}
function $dfsCaretIterator(direction, startNode, endNode) {
    const root = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])();
    const start = startNode || root;
    const startCaret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(start) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getChildCaret"])(start, direction) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSiblingCaret"])(start, direction);
    const startDepth = $getDepth(start);
    const endCaret = endNode ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getAdjacentChildCaret"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getChildCaretOrSelf"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSiblingCaret"])(endNode, direction))) : $getEndCaret(start, direction);
    let depth = startDepth;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["makeStepwiseIterator"])({
        hasNext: (state)=>state !== null,
        initial: startCaret,
        map: (state)=>({
                depth,
                node: state.origin
            }),
        step: (state)=>{
            if (state.isSameNodeCaret(endCaret)) {
                return null;
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isChildCaret"])(state)) {
                depth++;
            }
            const rval = $getAdjacentSiblingOrParentSiblingCaret(state);
            if (!rval || rval[0].isSameNodeCaret(endCaret)) {
                return null;
            }
            depth += rval[1];
            return rval[0];
        }
    });
}
/**
 * Returns the Node sibling when this exists, otherwise the closest parent sibling. For example
 * R -> P -> T1, T2
 *   -> P2
 * returns T2 for node T1, P2 for node T2, and null for node P2.
 * @param node LexicalNode.
 * @returns An array (tuple) containing the found Lexical node and the depth difference, or null, if this node doesn't exist.
 */ function $getNextSiblingOrParentSibling(node) {
    const rval = $getAdjacentSiblingOrParentSiblingCaret((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSiblingCaret"])(node, 'next'));
    return rval && [
        rval[0].origin,
        rval[1]
    ];
}
function $getDepth(node) {
    let depth = -1;
    for(let innerNode = node; innerNode !== null; innerNode = innerNode.getParent()){
        depth++;
    }
    return depth;
}
/**
 * Performs a right-to-left preorder tree traversal.
 * From the starting node it goes to the rightmost child, than backtracks to parent and finds new rightmost path.
 * It will return the next node in traversal sequence after the startingNode.
 * The traversal is similar to $dfs functions above, but the nodes are visited right-to-left, not left-to-right.
 * @param startingNode - The node to start the search.
 * @returns The next node in pre-order right to left traversal sequence or `null`, if the node does not exist
 */ function $getNextRightPreorderNode(startingNode) {
    const startCaret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getChildCaretOrSelf"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSiblingCaret"])(startingNode, 'previous'));
    const next = $getAdjacentSiblingOrParentSiblingCaret(startCaret, 'root');
    return next && next[0].origin;
}
/**
 * $dfs iterator (right to left). Tree traversal is done on the fly as new values are requested with O(1) memory.
 * @param startNode - The node to start the search, if omitted, it will start at the root node.
 * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.
 * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).
 */ function $reverseDfsIterator(startNode, endNode) {
    return $dfsCaretIterator('previous', startNode, endNode);
}
/**
 * Takes a node and traverses up its ancestors (toward the root node)
 * in order to find a specific type of node.
 * @param node - the node to begin searching.
 * @param klass - an instance of the type of node to look for.
 * @returns the node of type klass that was passed, or null if none exist.
 */ function $getNearestNodeOfType(node, klass) {
    let parent = node;
    while(parent != null){
        if (parent instanceof klass) {
            return parent;
        }
        parent = parent.getParent();
    }
    return null;
}
/**
 * Returns the element node of the nearest ancestor, otherwise throws an error.
 * @param startNode - The starting node of the search
 * @returns The ancestor node found
 */ function $getNearestBlockElementAncestorOrThrow(startNode) {
    const blockNode = $findMatchingParent(startNode, (node)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node) && !node.isInline());
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(blockNode)) {
        {
            formatDevErrorMessage(`Expected node ${startNode.__key} to have closest block element node.`);
        }
    }
    return blockNode;
}
/**
 * Starts with a node and moves up the tree (toward the root node) to find a matching node based on
 * the search parameters of the findFn. (Consider JavaScripts' .find() function where a testing function must be
 * passed as an argument. eg. if( (node) => node.__type === 'div') ) return true; otherwise return false
 * @param startingNode - The node where the search starts.
 * @param findFn - A testing function that returns true if the current node satisfies the testing parameters.
 * @returns A parent node that matches the findFn parameters, or null if one wasn't found.
 */ const $findMatchingParent = (startingNode, findFn)=>{
    let curr = startingNode;
    while(curr !== (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])() && curr != null){
        if (findFn(curr)) {
            return curr;
        }
        curr = curr.getParent();
    }
    return null;
};
/**
 * Attempts to resolve nested element nodes of the same type into a single node of that type.
 * It is generally used for marks/commenting
 * @param editor - The lexical editor
 * @param targetNode - The target for the nested element to be extracted from.
 * @param cloneNode - See {@link $createMarkNode}
 * @param handleOverlap - Handles any overlap between the node to extract and the targetNode
 * @returns The lexical editor
 */ function registerNestedElementResolver(editor, targetNode, cloneNode, handleOverlap) {
    const $isTargetNode = (node)=>{
        return node instanceof targetNode;
    };
    const $findMatch = (node)=>{
        // First validate we don't have any children that are of the target,
        // as we need to handle them first.
        const children = node.getChildren();
        for(let i = 0; i < children.length; i++){
            const child = children[i];
            if ($isTargetNode(child)) {
                return null;
            }
        }
        let parentNode = node;
        let childNode = node;
        while(parentNode !== null){
            childNode = parentNode;
            parentNode = parentNode.getParent();
            if ($isTargetNode(parentNode)) {
                return {
                    child: childNode,
                    parent: parentNode
                };
            }
        }
        return null;
    };
    const $elementNodeTransform = (node)=>{
        const match = $findMatch(node);
        if (match !== null) {
            const { child, parent } = match;
            // Simple path, we can move child out and siblings into a new parent.
            if (child.is(node)) {
                handleOverlap(parent, node);
                const nextSiblings = child.getNextSiblings();
                const nextSiblingsLength = nextSiblings.length;
                parent.insertAfter(child);
                if (nextSiblingsLength !== 0) {
                    const newParent = cloneNode(parent);
                    child.insertAfter(newParent);
                    for(let i = 0; i < nextSiblingsLength; i++){
                        newParent.append(nextSiblings[i]);
                    }
                }
                if (!parent.canBeEmpty() && parent.getChildrenSize() === 0) {
                    parent.remove();
                }
            }
        }
    };
    return editor.registerNodeTransform(targetNode, $elementNodeTransform);
}
/**
 * Clones the editor and marks it as dirty to be reconciled. If there was a selection,
 * it would be set back to its previous state, or null otherwise.
 * @param editor - The lexical editor
 * @param editorState - The editor's state
 */ function $restoreEditorState(editor, editorState) {
    const FULL_RECONCILE = 2;
    const nodeMap = new Map();
    const activeEditorState = editor._pendingEditorState;
    for (const [key, node] of editorState._nodeMap){
        nodeMap.set(key, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$cloneWithProperties"])(node));
    }
    if (activeEditorState) {
        activeEditorState._nodeMap = nodeMap;
    }
    editor._dirtyType = FULL_RECONCILE;
    const selection = editorState._selection;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setSelection"])(selection === null ? null : selection.clone());
}
/**
 * If the selected insertion area is the root/shadow root node (see {@link lexical!$isRootOrShadowRoot}),
 * the node will be appended there, otherwise, it will be inserted before the insertion area.
 * If there is no selection where the node is to be inserted, it will be appended after any current nodes
 * within the tree, as a child of the root node. A paragraph will then be added after the inserted node and selected.
 * @param node - The node to be inserted
 * @returns The node after its insertion
 */ function $insertNodeToNearestRoot(node) {
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])() || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getPreviousSelection"])();
    let initialCaret;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
        initialCaret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$caretFromPoint"])(selection.focus, 'next');
    } else {
        if (selection != null) {
            const nodes = selection.getNodes();
            const lastNode = nodes[nodes.length - 1];
            if (lastNode) {
                initialCaret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSiblingCaret"])(lastNode, 'next');
            }
        }
        initialCaret = initialCaret || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getChildCaret"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])(), 'previous').getFlipped().insert((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])());
    }
    const insertCaret = $insertNodeToNearestRootAtCaret(node, initialCaret);
    const adjacent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getAdjacentChildCaret"])(insertCaret);
    const selectionCaret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isChildCaret"])(adjacent) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$normalizeCaret"])(adjacent) : insertCaret;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setSelectionFromCaretRange"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getCollapsedCaretRange"])(selectionCaret));
    return node.getLatest();
}
/**
 * If the insertion caret is the root/shadow root node (see {@link lexical!$isRootOrShadowRoot}),
 * the node will be inserted there, otherwise the parent nodes will be split according to the
 * given options.
 * @param node - The node to be inserted
 * @param caret - The location to insert or split from
 * @returns The node after its insertion
 */ function $insertNodeToNearestRootAtCaret(node, caret, options) {
    let insertCaret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getCaretInDirection"])(caret, 'next');
    for(let nextCaret = insertCaret; nextCaret; nextCaret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$splitAtPointCaretNext"])(nextCaret, options)){
        insertCaret = nextCaret;
    }
    if (!!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextPointCaret"])(insertCaret)) {
        formatDevErrorMessage(`$insertNodeToNearestRootAtCaret: An unattached TextNode can not be split`);
    }
    insertCaret.insert(node.isInline() ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])().append(node) : node);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getCaretInDirection"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSiblingCaret"])(node.getLatest(), 'next'), caret.direction);
}
/**
 * Wraps the node into another node created from a createElementNode function, eg. $createParagraphNode
 * @param node - Node to be wrapped.
 * @param createElementNode - Creates a new lexical element to wrap the to-be-wrapped node and returns it.
 * @returns A new lexical element with the previous node appended within (as a child, including its children).
 */ function $wrapNodeInElement(node, createElementNode) {
    const elementNode = createElementNode();
    node.replace(elementNode);
    elementNode.append(node);
    return elementNode;
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
/**
 * @param object = The instance of the type
 * @param objectClass = The class of the type
 * @returns Whether the object is has the same Klass of the objectClass, ignoring the difference across window (e.g. different iframes)
 */ function objectKlassEquals(object, objectClass) {
    return object !== null ? Object.getPrototypeOf(object).constructor.name === objectClass.name : false;
}
/**
 * Filter the nodes
 * @param nodes Array of nodes that needs to be filtered
 * @param filterFn A filter function that returns node if the current node satisfies the condition otherwise null
 * @returns Array of filtered nodes
 */ function $filter(nodes, filterFn) {
    const result = [];
    for(let i = 0; i < nodes.length; i++){
        const node = filterFn(nodes[i]);
        if (node !== null) {
            result.push(node);
        }
    }
    return result;
}
/**
 * Appends the node before the first child of the parent node
 * @param parent A parent node
 * @param node Node that needs to be appended
 */ function $insertFirst(parent, node) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getChildCaret"])(parent, 'next').insert(node);
}
let NEEDS_MANUAL_ZOOM = IS_FIREFOX || !CAN_USE_DOM ? false : undefined;
function needsManualZoom() {
    if (NEEDS_MANUAL_ZOOM === undefined) {
        // If the browser implements standardized CSS zoom, then the client rect
        // will be wider after zoom is applied
        // https://chromestatus.com/feature/5198254868529152
        // https://github.com/facebook/lexical/issues/6863
        const div = document.createElement('div');
        div.style.cssText = 'position: absolute; opacity: 0; width: 100px; left: -1000px;';
        document.body.appendChild(div);
        const noZoom = div.getBoundingClientRect();
        div.style.setProperty('zoom', '2');
        NEEDS_MANUAL_ZOOM = div.getBoundingClientRect().width === noZoom.width;
        document.body.removeChild(div);
    }
    return NEEDS_MANUAL_ZOOM;
}
/**
 * Calculates the zoom level of an element as a result of using
 * css zoom property. For browsers that implement standardized CSS
 * zoom (Firefox, Chrome >= 128), this will always return 1.
 * @param element
 */ function calculateZoomLevel(element) {
    let zoom = 1;
    if (needsManualZoom()) {
        while(element){
            zoom *= Number(window.getComputedStyle(element).getPropertyValue('zoom'));
            element = element.parentElement;
        }
    }
    return zoom;
}
/**
 * Checks if the editor is a nested editor created by LexicalNestedComposer
 */ function $isEditorIsNestedEditor(editor) {
    return editor._parentEditor !== null;
}
/**
 * A depth first last-to-first traversal of root that stops at each node that matches
 * $predicate and ensures that its parent is root. This is typically used to discard
 * invalid or unsupported wrapping nodes. For example, a TableNode must only have
 * TableRowNode as children, but an importer might add invalid nodes based on
 * caption, tbody, thead, etc. and this will unwrap and discard those.
 *
 * @param root The root to start the traversal
 * @param $predicate Should return true for nodes that are permitted to be children of root
 * @returns true if this unwrapped or removed any nodes
 */ function $unwrapAndFilterDescendants(root, $predicate) {
    return $unwrapAndFilterDescendantsImpl(root, $predicate, null);
}
function $unwrapAndFilterDescendantsImpl(root, $predicate, $onSuccess) {
    let didMutate = false;
    for (const node of $lastToFirstIterator(root)){
        if ($predicate(node)) {
            if ($onSuccess !== null) {
                $onSuccess(node);
            }
            continue;
        }
        didMutate = true;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node)) {
            $unwrapAndFilterDescendantsImpl(node, $predicate, $onSuccess || ((child)=>node.insertAfter(child)));
        }
        node.remove();
    }
    return didMutate;
}
/**
 * A depth first traversal of the children array that stops at and collects
 * each node that `$predicate` matches. This is typically used to discard
 * invalid or unsupported wrapping nodes on a children array in the `after`
 * of an {@link lexical!DOMConversionOutput}. For example, a TableNode must only have
 * TableRowNode as children, but an importer might add invalid nodes based on
 * caption, tbody, thead, etc. and this will unwrap and discard those.
 *
 * This function is read-only and performs no mutation operations, which makes
 * it suitable for import and export purposes but likely not for any in-place
 * mutation. You should use {@link $unwrapAndFilterDescendants} for in-place
 * mutations such as node transforms.
 *
 * @param children The children to traverse
 * @param $predicate Should return true for nodes that are permitted to be children of root
 * @returns The children or their descendants that match $predicate
 */ function $descendantsMatching(children, $predicate) {
    const result = [];
    const stack = Array.from(children).reverse();
    for(let child = stack.pop(); child !== undefined; child = stack.pop()){
        if ($predicate(child)) {
            result.push(child);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(child)) {
            for (const grandchild of $lastToFirstIterator(child)){
                stack.push(grandchild);
            }
        }
    }
    return result;
}
/**
 * Return an iterator that yields each child of node from first to last, taking
 * care to preserve the next sibling before yielding the value in case the caller
 * removes the yielded node.
 *
 * @param node The node whose children to iterate
 * @returns An iterator of the node's children
 */ function $firstToLastIterator(node) {
    return $childIterator((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getChildCaret"])(node, 'next'));
}
/**
 * Return an iterator that yields each child of node from last to first, taking
 * care to preserve the previous sibling before yielding the value in case the caller
 * removes the yielded node.
 *
 * @param node The node whose children to iterate
 * @returns An iterator of the node's children
 */ function $lastToFirstIterator(node) {
    return $childIterator((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getChildCaret"])(node, 'previous'));
}
function $childIterator(startCaret) {
    const seen = new Set();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["makeStepwiseIterator"])({
        hasNext: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isSiblingCaret"],
        initial: startCaret.getAdjacentCaret(),
        map: (caret)=>{
            const origin = caret.origin.getLatest();
            if (seen !== null) {
                const key = origin.getKey();
                if (!!seen.has(key)) {
                    formatDevErrorMessage(`$childIterator: Cycle detected, node with key ${String(key)} has already been traversed`);
                }
                seen.add(key);
            }
            return origin;
        },
        step: (caret)=>caret.getAdjacentCaret()
    });
}
/**
 * Replace this node with its children
 *
 * @param node The ElementNode to unwrap and remove
 */ function $unwrapNode(node) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$rewindSiblingCaret"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSiblingCaret"])(node, 'next')).splice(1, node.getChildren());
}
/**
 * Returns the Node sibling when this exists, otherwise the closest parent sibling. For example
 * R -> P -> T1, T2
 *   -> P2
 * returns T2 for node T1, P2 for node T2, and null for node P2.
 * @param node LexicalNode.
 * @returns An array (tuple) containing the found Lexical node and the depth difference, or null, if this node doesn't exist.
 */ function $getAdjacentSiblingOrParentSiblingCaret(startCaret, rootMode = 'root') {
    let depthDiff = 0;
    let caret = startCaret;
    let nextCaret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getAdjacentChildCaret"])(caret);
    while(nextCaret === null){
        depthDiff--;
        nextCaret = caret.getParentCaret(rootMode);
        if (!nextCaret) {
            return null;
        }
        caret = nextCaret;
        nextCaret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getAdjacentChildCaret"])(caret);
    }
    return nextCaret && [
        nextCaret,
        depthDiff
    ];
}
/**
 * A wrapper that creates bound functions and methods for the
 * StateConfig to save some boilerplate when defining methods
 * or exporting only the accessors from your modules rather
 * than exposing the StateConfig directly.
 */ /**
 * EXPERIMENTAL
 *
 * A convenience interface for working with {@link $getState} and
 * {@link $setState}.
 *
 * @param stateConfig The stateConfig to wrap with convenience functionality
 * @returns a StateWrapper
 */ function makeStateWrapper(stateConfig) {
    const $get = (node)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getState"])(node, stateConfig);
    const $set = (node, valueOrUpdater)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setState"])(node, stateConfig, valueOrUpdater);
    return {
        $get,
        $set,
        accessors: [
            $get,
            $set
        ],
        makeGetterMethod: ()=>function $getter() {
                return $get(this);
            },
        makeSetterMethod: ()=>function $setter(valueOrUpdater) {
                return $set(this, valueOrUpdater);
            },
        stateConfig
    };
}
;
}}),
"[project]/node_modules/@lexical/history/LexicalHistory.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "createEmptyHistoryState": (()=>createEmptyHistoryState),
    "registerHistory": (()=>registerHistory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const HISTORY_MERGE = 0;
const HISTORY_PUSH = 1;
const DISCARD_HISTORY_CANDIDATE = 2;
const OTHER = 0;
const COMPOSING_CHARACTER = 1;
const INSERT_CHARACTER_AFTER_SELECTION = 2;
const DELETE_CHARACTER_BEFORE_SELECTION = 3;
const DELETE_CHARACTER_AFTER_SELECTION = 4;
function getDirtyNodes(editorState, dirtyLeaves, dirtyElements) {
    const nodeMap = editorState._nodeMap;
    const nodes = [];
    for (const dirtyLeafKey of dirtyLeaves){
        const dirtyLeaf = nodeMap.get(dirtyLeafKey);
        if (dirtyLeaf !== undefined) {
            nodes.push(dirtyLeaf);
        }
    }
    for (const [dirtyElementKey, intentionallyMarkedAsDirty] of dirtyElements){
        if (!intentionallyMarkedAsDirty) {
            continue;
        }
        const dirtyElement = nodeMap.get(dirtyElementKey);
        if (dirtyElement !== undefined && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootNode"])(dirtyElement)) {
            nodes.push(dirtyElement);
        }
    }
    return nodes;
}
function getChangeType(prevEditorState, nextEditorState, dirtyLeavesSet, dirtyElementsSet, isComposing) {
    if (prevEditorState === null || dirtyLeavesSet.size === 0 && dirtyElementsSet.size === 0 && !isComposing) {
        return OTHER;
    }
    const nextSelection = nextEditorState._selection;
    const prevSelection = prevEditorState._selection;
    if (isComposing) {
        return COMPOSING_CHARACTER;
    }
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(nextSelection) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(prevSelection) || !prevSelection.isCollapsed() || !nextSelection.isCollapsed()) {
        return OTHER;
    }
    const dirtyNodes = getDirtyNodes(nextEditorState, dirtyLeavesSet, dirtyElementsSet);
    if (dirtyNodes.length === 0) {
        return OTHER;
    }
    // Catching the case when inserting new text node into an element (e.g. first char in paragraph/list),
    // or after existing node.
    if (dirtyNodes.length > 1) {
        const nextNodeMap = nextEditorState._nodeMap;
        const nextAnchorNode = nextNodeMap.get(nextSelection.anchor.key);
        const prevAnchorNode = nextNodeMap.get(prevSelection.anchor.key);
        if (nextAnchorNode && prevAnchorNode && !prevEditorState._nodeMap.has(nextAnchorNode.__key) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(nextAnchorNode) && nextAnchorNode.__text.length === 1 && nextSelection.anchor.offset === 1) {
            return INSERT_CHARACTER_AFTER_SELECTION;
        }
        return OTHER;
    }
    const nextDirtyNode = dirtyNodes[0];
    const prevDirtyNode = prevEditorState._nodeMap.get(nextDirtyNode.__key);
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(prevDirtyNode) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(nextDirtyNode) || prevDirtyNode.__mode !== nextDirtyNode.__mode) {
        return OTHER;
    }
    const prevText = prevDirtyNode.__text;
    const nextText = nextDirtyNode.__text;
    if (prevText === nextText) {
        return OTHER;
    }
    const nextAnchor = nextSelection.anchor;
    const prevAnchor = prevSelection.anchor;
    if (nextAnchor.key !== prevAnchor.key || nextAnchor.type !== 'text') {
        return OTHER;
    }
    const nextAnchorOffset = nextAnchor.offset;
    const prevAnchorOffset = prevAnchor.offset;
    const textDiff = nextText.length - prevText.length;
    if (textDiff === 1 && prevAnchorOffset === nextAnchorOffset - 1) {
        return INSERT_CHARACTER_AFTER_SELECTION;
    }
    if (textDiff === -1 && prevAnchorOffset === nextAnchorOffset + 1) {
        return DELETE_CHARACTER_BEFORE_SELECTION;
    }
    if (textDiff === -1 && prevAnchorOffset === nextAnchorOffset) {
        return DELETE_CHARACTER_AFTER_SELECTION;
    }
    return OTHER;
}
function isTextNodeUnchanged(key, prevEditorState, nextEditorState) {
    const prevNode = prevEditorState._nodeMap.get(key);
    const nextNode = nextEditorState._nodeMap.get(key);
    const prevSelection = prevEditorState._selection;
    const nextSelection = nextEditorState._selection;
    const isDeletingLine = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(prevSelection) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(nextSelection) && prevSelection.anchor.type === 'element' && prevSelection.focus.type === 'element' && nextSelection.anchor.type === 'text' && nextSelection.focus.type === 'text';
    if (!isDeletingLine && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(prevNode) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(nextNode) && prevNode.__parent === nextNode.__parent) {
        // This has the assumption that object key order won't change if the
        // content did not change, which should normally be safe given
        // the manner in which nodes and exportJSON are typically implemented.
        return JSON.stringify(prevEditorState.read(()=>prevNode.exportJSON())) === JSON.stringify(nextEditorState.read(()=>nextNode.exportJSON()));
    }
    return false;
}
function createMergeActionGetter(editor, delay) {
    let prevChangeTime = Date.now();
    let prevChangeType = OTHER;
    return (prevEditorState, nextEditorState, currentHistoryEntry, dirtyLeaves, dirtyElements, tags)=>{
        const changeTime = Date.now();
        // If applying changes from history stack there's no need
        // to run history logic again, as history entries already calculated
        if (tags.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HISTORIC_TAG"])) {
            prevChangeType = OTHER;
            prevChangeTime = changeTime;
            return DISCARD_HISTORY_CANDIDATE;
        }
        const changeType = getChangeType(prevEditorState, nextEditorState, dirtyLeaves, dirtyElements, editor.isComposing());
        const mergeAction = (()=>{
            const isSameEditor = currentHistoryEntry === null || currentHistoryEntry.editor === editor;
            const shouldPushHistory = tags.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HISTORY_PUSH_TAG"]);
            const shouldMergeHistory = !shouldPushHistory && isSameEditor && tags.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HISTORY_MERGE_TAG"]);
            if (shouldMergeHistory) {
                return HISTORY_MERGE;
            }
            if (prevEditorState === null) {
                return HISTORY_PUSH;
            }
            const selection = nextEditorState._selection;
            const hasDirtyNodes = dirtyLeaves.size > 0 || dirtyElements.size > 0;
            if (!hasDirtyNodes) {
                if (selection !== null) {
                    return HISTORY_MERGE;
                }
                return DISCARD_HISTORY_CANDIDATE;
            }
            if (shouldPushHistory === false && changeType !== OTHER && changeType === prevChangeType && changeTime < prevChangeTime + delay && isSameEditor) {
                return HISTORY_MERGE;
            }
            // A single node might have been marked as dirty, but not have changed
            // due to some node transform reverting the change.
            if (dirtyLeaves.size === 1) {
                const dirtyLeafKey = Array.from(dirtyLeaves)[0];
                if (isTextNodeUnchanged(dirtyLeafKey, prevEditorState, nextEditorState)) {
                    return HISTORY_MERGE;
                }
            }
            return HISTORY_PUSH;
        })();
        prevChangeTime = changeTime;
        prevChangeType = changeType;
        return mergeAction;
    };
}
function redo(editor, historyState) {
    const redoStack = historyState.redoStack;
    const undoStack = historyState.undoStack;
    if (redoStack.length !== 0) {
        const current = historyState.current;
        if (current !== null) {
            undoStack.push(current);
            editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CAN_UNDO_COMMAND"], true);
        }
        const historyStateEntry = redoStack.pop();
        if (redoStack.length === 0) {
            editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CAN_REDO_COMMAND"], false);
        }
        historyState.current = historyStateEntry || null;
        if (historyStateEntry) {
            historyStateEntry.editor.setEditorState(historyStateEntry.editorState, {
                tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HISTORIC_TAG"]
            });
        }
    }
}
function undo(editor, historyState) {
    const redoStack = historyState.redoStack;
    const undoStack = historyState.undoStack;
    const undoStackLength = undoStack.length;
    if (undoStackLength !== 0) {
        const current = historyState.current;
        const historyStateEntry = undoStack.pop();
        if (current !== null) {
            redoStack.push(current);
            editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CAN_REDO_COMMAND"], true);
        }
        if (undoStack.length === 0) {
            editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CAN_UNDO_COMMAND"], false);
        }
        historyState.current = historyStateEntry || null;
        if (historyStateEntry) {
            historyStateEntry.editor.setEditorState(historyStateEntry.editorState, {
                tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HISTORIC_TAG"]
            });
        }
    }
}
function clearHistory(historyState) {
    historyState.undoStack = [];
    historyState.redoStack = [];
    historyState.current = null;
}
/**
 * Registers necessary listeners to manage undo/redo history stack and related editor commands.
 * It returns `unregister` callback that cleans up all listeners and should be called on editor unmount.
 * @param editor - The lexical editor.
 * @param historyState - The history state, containing the current state and the undo/redo stack.
 * @param delay - The time (in milliseconds) the editor should delay generating a new history stack,
 * instead of merging the current changes with the current stack.
 * @returns The listeners cleanup callback function.
 */ function registerHistory(editor, historyState, delay) {
    const getMergeAction = createMergeActionGetter(editor, delay);
    const applyChange = ({ editorState, prevEditorState, dirtyLeaves, dirtyElements, tags })=>{
        const current = historyState.current;
        const redoStack = historyState.redoStack;
        const undoStack = historyState.undoStack;
        const currentEditorState = current === null ? null : current.editorState;
        if (current !== null && editorState === currentEditorState) {
            return;
        }
        const mergeAction = getMergeAction(prevEditorState, editorState, current, dirtyLeaves, dirtyElements, tags);
        if (mergeAction === HISTORY_PUSH) {
            if (redoStack.length !== 0) {
                historyState.redoStack = [];
                editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CAN_REDO_COMMAND"], false);
            }
            if (current !== null) {
                undoStack.push({
                    ...current
                });
                editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CAN_UNDO_COMMAND"], true);
            }
        } else if (mergeAction === DISCARD_HISTORY_CANDIDATE) {
            return;
        }
        // Else we merge
        historyState.current = {
            editor,
            editorState
        };
    };
    const unregister = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])(editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UNDO_COMMAND"], ()=>{
        undo(editor, historyState);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REDO_COMMAND"], ()=>{
        redo(editor, historyState);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLEAR_EDITOR_COMMAND"], ()=>{
        clearHistory(historyState);
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLEAR_HISTORY_COMMAND"], ()=>{
        clearHistory(historyState);
        editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CAN_REDO_COMMAND"], false);
        editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CAN_UNDO_COMMAND"], false);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerUpdateListener(applyChange));
    return unregister;
}
/**
 * Creates an empty history state.
 * @returns - The empty history state, as an object.
 */ function createEmptyHistoryState() {
    return {
        current: null,
        redoStack: [],
        undoStack: []
    };
}
;
}}),
"[project]/node_modules/@lexical/html/LexicalHtml.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "$generateHtmlFromNodes": (()=>$generateHtmlFromNodes),
    "$generateNodesFromDOM": (()=>$generateNodesFromDOM)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/selection/LexicalSelection.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * How you parse your html string to get a document is left up to you. In the browser you can use the native
 * DOMParser API to generate a document (see clipboard.ts), but to use in a headless environment you can use JSDom
 * or an equivalent library and pass in the document here.
 */ function $generateNodesFromDOM(editor, dom) {
    const elements = dom.body ? dom.body.childNodes : [];
    let lexicalNodes = [];
    const allArtificialNodes = [];
    for(let i = 0; i < elements.length; i++){
        const element = elements[i];
        if (!IGNORE_TAGS.has(element.nodeName)) {
            const lexicalNode = $createNodesFromDOM(element, editor, allArtificialNodes, false);
            if (lexicalNode !== null) {
                lexicalNodes = lexicalNodes.concat(lexicalNode);
            }
        }
    }
    $unwrapArtificialNodes(allArtificialNodes);
    return lexicalNodes;
}
function $generateHtmlFromNodes(editor, selection) {
    if (typeof document === 'undefined' || typeof window === 'undefined' && typeof global.window === 'undefined') {
        throw new Error('To use $generateHtmlFromNodes in headless mode please initialize a headless browser implementation such as JSDom before calling this function.');
    }
    const container = document.createElement('div');
    const root = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])();
    const topLevelChildren = root.getChildren();
    for(let i = 0; i < topLevelChildren.length; i++){
        const topLevelNode = topLevelChildren[i];
        $appendNodesToHTML(editor, topLevelNode, container, selection);
    }
    return container.innerHTML;
}
function $appendNodesToHTML(editor, currentNode, parentElement, selection = null) {
    let shouldInclude = selection !== null ? currentNode.isSelected(selection) : true;
    const shouldExclude = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(currentNode) && currentNode.excludeFromCopy('html');
    let target = currentNode;
    if (selection !== null) {
        let clone = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$cloneWithProperties"])(currentNode);
        clone = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(clone) && selection !== null ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$sliceSelectedTextNodeContent"])(selection, clone) : clone;
        target = clone;
    }
    const children = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(target) ? target.getChildren() : [];
    const registeredNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRegisteredNode"])(editor, target.getType());
    let exportOutput;
    // Use HTMLConfig overrides, if available.
    if (registeredNode && registeredNode.exportDOM !== undefined) {
        exportOutput = registeredNode.exportDOM(editor, target);
    } else {
        exportOutput = target.exportDOM(editor);
    }
    const { element, after } = exportOutput;
    if (!element) {
        return false;
    }
    const fragment = document.createDocumentFragment();
    for(let i = 0; i < children.length; i++){
        const childNode = children[i];
        const shouldIncludeChild = $appendNodesToHTML(editor, childNode, fragment, selection);
        if (!shouldInclude && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(currentNode) && shouldIncludeChild && currentNode.extractWithChild(childNode, selection, 'html')) {
            shouldInclude = true;
        }
    }
    if (shouldInclude && !shouldExclude) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLElement"])(element) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDocumentFragment"])(element)) {
            element.append(fragment);
        }
        parentElement.append(element);
        if (after) {
            const newElement = after.call(target, element);
            if (newElement) {
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDocumentFragment"])(element)) {
                    element.replaceChildren(newElement);
                } else {
                    element.replaceWith(newElement);
                }
            }
        }
    } else {
        parentElement.append(fragment);
    }
    return shouldInclude;
}
function getConversionFunction(domNode, editor) {
    const { nodeName } = domNode;
    const cachedConversions = editor._htmlConversions.get(nodeName.toLowerCase());
    let currentConversion = null;
    if (cachedConversions !== undefined) {
        for (const cachedConversion of cachedConversions){
            const domConversion = cachedConversion(domNode);
            if (domConversion !== null && (currentConversion === null || // Given equal priority, prefer the last registered importer
            // which is typically an application custom node or HTMLConfig['import']
            (currentConversion.priority || 0) <= (domConversion.priority || 0))) {
                currentConversion = domConversion;
            }
        }
    }
    return currentConversion !== null ? currentConversion.conversion : null;
}
const IGNORE_TAGS = new Set([
    'STYLE',
    'SCRIPT'
]);
function $createNodesFromDOM(node, editor, allArtificialNodes, hasBlockAncestorLexicalNode, forChildMap = new Map(), parentLexicalNode) {
    let lexicalNodes = [];
    if (IGNORE_TAGS.has(node.nodeName)) {
        return lexicalNodes;
    }
    let currentLexicalNode = null;
    const transformFunction = getConversionFunction(node, editor);
    const transformOutput = transformFunction ? transformFunction(node) : null;
    let postTransform = null;
    if (transformOutput !== null) {
        postTransform = transformOutput.after;
        const transformNodes = transformOutput.node;
        currentLexicalNode = Array.isArray(transformNodes) ? transformNodes[transformNodes.length - 1] : transformNodes;
        if (currentLexicalNode !== null) {
            for (const [, forChildFunction] of forChildMap){
                currentLexicalNode = forChildFunction(currentLexicalNode, parentLexicalNode);
                if (!currentLexicalNode) {
                    break;
                }
            }
            if (currentLexicalNode) {
                lexicalNodes.push(...Array.isArray(transformNodes) ? transformNodes : [
                    currentLexicalNode
                ]);
            }
        }
        if (transformOutput.forChild != null) {
            forChildMap.set(node.nodeName, transformOutput.forChild);
        }
    }
    // If the DOM node doesn't have a transformer, we don't know what
    // to do with it but we still need to process any childNodes.
    const children = node.childNodes;
    let childLexicalNodes = [];
    const hasBlockAncestorLexicalNodeForChildren = currentLexicalNode != null && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootOrShadowRoot"])(currentLexicalNode) ? false : currentLexicalNode != null && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isBlockElementNode"])(currentLexicalNode) || hasBlockAncestorLexicalNode;
    for(let i = 0; i < children.length; i++){
        childLexicalNodes.push(...$createNodesFromDOM(children[i], editor, allArtificialNodes, hasBlockAncestorLexicalNodeForChildren, new Map(forChildMap), currentLexicalNode));
    }
    if (postTransform != null) {
        childLexicalNodes = postTransform(childLexicalNodes);
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isBlockDomNode"])(node)) {
        if (!hasBlockAncestorLexicalNodeForChildren) {
            childLexicalNodes = wrapContinuousInlines(node, childLexicalNodes, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"]);
        } else {
            childLexicalNodes = wrapContinuousInlines(node, childLexicalNodes, ()=>{
                const artificialNode = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ArtificialNode__DO_NOT_USE"]();
                allArtificialNodes.push(artificialNode);
                return artificialNode;
            });
        }
    }
    if (currentLexicalNode == null) {
        if (childLexicalNodes.length > 0) {
            // If it hasn't been converted to a LexicalNode, we hoist its children
            // up to the same level as it.
            lexicalNodes = lexicalNodes.concat(childLexicalNodes);
        } else {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isBlockDomNode"])(node) && isDomNodeBetweenTwoInlineNodes(node)) {
                // Empty block dom node that hasnt been converted, we replace it with a linebreak if its between inline nodes
                lexicalNodes = lexicalNodes.concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createLineBreakNode"])());
            }
        }
    } else {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(currentLexicalNode)) {
            // If the current node is a ElementNode after conversion,
            // we can append all the children to it.
            currentLexicalNode.append(...childLexicalNodes);
        }
    }
    return lexicalNodes;
}
function wrapContinuousInlines(domNode, nodes, createWrapperFn) {
    const textAlign = domNode.style.textAlign;
    const out = [];
    let continuousInlines = [];
    // wrap contiguous inline child nodes in para
    for(let i = 0; i < nodes.length; i++){
        const node = nodes[i];
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isBlockElementNode"])(node)) {
            if (textAlign && !node.getFormat()) {
                node.setFormat(textAlign);
            }
            out.push(node);
        } else {
            continuousInlines.push(node);
            if (i === nodes.length - 1 || i < nodes.length - 1 && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isBlockElementNode"])(nodes[i + 1])) {
                const wrapper = createWrapperFn();
                wrapper.setFormat(textAlign);
                wrapper.append(...continuousInlines);
                out.push(wrapper);
                continuousInlines = [];
            }
        }
    }
    return out;
}
function $unwrapArtificialNodes(allArtificialNodes) {
    for (const node of allArtificialNodes){
        if (node.getNextSibling() instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ArtificialNode__DO_NOT_USE"]) {
            node.insertAfter((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createLineBreakNode"])());
        }
    }
    // Replace artificial node with it's children
    for (const node of allArtificialNodes){
        const children = node.getChildren();
        for (const child of children){
            node.insertBefore(child);
        }
        node.remove();
    }
}
function isDomNodeBetweenTwoInlineNodes(node) {
    if (node.nextSibling == null || node.previousSibling == null) {
        return false;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isInlineDomNode"])(node.nextSibling) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isInlineDomNode"])(node.previousSibling);
}
;
}}),
"[project]/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "$generateJSONFromSelectedNodes": (()=>$generateJSONFromSelectedNodes),
    "$generateNodesFromSerializedNodes": (()=>$generateNodesFromSerializedNodes),
    "$getClipboardDataFromSelection": (()=>$getClipboardDataFromSelection),
    "$getHtmlContent": (()=>$getHtmlContent),
    "$getLexicalContent": (()=>$getLexicalContent),
    "$insertDataTransferForPlainText": (()=>$insertDataTransferForPlainText),
    "$insertDataTransferForRichText": (()=>$insertDataTransferForRichText),
    "$insertGeneratedNodes": (()=>$insertGeneratedNodes),
    "copyToClipboard": (()=>copyToClipboard),
    "setLexicalClipboardDataTransfer": (()=>setLexicalClipboardDataTransfer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$html$2f$LexicalHtml$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/html/LexicalHtml.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/selection/LexicalSelection.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Do not require this module directly! Use normal `invariant` calls.
function formatDevErrorMessage(message) {
    throw new Error(message);
}
/**
 * Returns the *currently selected* Lexical content as an HTML string, relying on the
 * logic defined in the exportDOM methods on the LexicalNode classes. Note that
 * this will not return the HTML content of the entire editor (unless all the content is included
 * in the current selection).
 *
 * @param editor - LexicalEditor instance to get HTML content from
 * @param selection - The selection to use (default is $getSelection())
 * @returns a string of HTML content
 */ function $getHtmlContent(editor, selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])()) {
    if (selection == null) {
        {
            formatDevErrorMessage(`Expected valid LexicalSelection`);
        }
    }
    // If we haven't selected anything
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) && selection.isCollapsed() || selection.getNodes().length === 0) {
        return '';
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$html$2f$LexicalHtml$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$generateHtmlFromNodes"])(editor, selection);
}
/**
 * Returns the *currently selected* Lexical content as a JSON string, relying on the
 * logic defined in the exportJSON methods on the LexicalNode classes. Note that
 * this will not return the JSON content of the entire editor (unless all the content is included
 * in the current selection).
 *
 * @param editor  - LexicalEditor instance to get the JSON content from
 * @param selection - The selection to use (default is $getSelection())
 * @returns
 */ function $getLexicalContent(editor, selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])()) {
    if (selection == null) {
        {
            formatDevErrorMessage(`Expected valid LexicalSelection`);
        }
    }
    // If we haven't selected anything
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) && selection.isCollapsed() || selection.getNodes().length === 0) {
        return null;
    }
    return JSON.stringify($generateJSONFromSelectedNodes(editor, selection));
}
/**
 * Attempts to insert content of the mime-types text/plain or text/uri-list from
 * the provided DataTransfer object into the editor at the provided selection.
 * text/uri-list is only used if text/plain is not also provided.
 *
 * @param dataTransfer an object conforming to the [DataTransfer interface] (https://html.spec.whatwg.org/multipage/dnd.html#the-datatransfer-interface)
 * @param selection the selection to use as the insertion point for the content in the DataTransfer object
 */ function $insertDataTransferForPlainText(dataTransfer, selection) {
    const text = dataTransfer.getData('text/plain') || dataTransfer.getData('text/uri-list');
    if (text != null) {
        selection.insertRawText(text);
    }
}
/**
 * Attempts to insert content of the mime-types application/x-lexical-editor, text/html,
 * text/plain, or text/uri-list (in descending order of priority) from the provided DataTransfer
 * object into the editor at the provided selection.
 *
 * @param dataTransfer an object conforming to the [DataTransfer interface] (https://html.spec.whatwg.org/multipage/dnd.html#the-datatransfer-interface)
 * @param selection the selection to use as the insertion point for the content in the DataTransfer object
 * @param editor the LexicalEditor the content is being inserted into.
 */ function $insertDataTransferForRichText(dataTransfer, selection, editor) {
    const lexicalString = dataTransfer.getData('application/x-lexical-editor');
    if (lexicalString) {
        try {
            const payload = JSON.parse(lexicalString);
            if (payload.namespace === editor._config.namespace && Array.isArray(payload.nodes)) {
                const nodes = $generateNodesFromSerializedNodes(payload.nodes);
                return $insertGeneratedNodes(editor, nodes, selection);
            }
        } catch (_unused) {
        // Fail silently.
        }
    }
    const htmlString = dataTransfer.getData('text/html');
    const plainString = dataTransfer.getData('text/plain');
    // Skip HTML handling if it matches the plain text representation.
    // This avoids unnecessary processing for plain text strings created by
    // iOS Safari autocorrect, which incorrectly includes a `text/html` type.
    if (htmlString && plainString !== htmlString) {
        try {
            const parser = new DOMParser();
            const dom = parser.parseFromString(trustHTML(htmlString), 'text/html');
            const nodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$html$2f$LexicalHtml$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$generateNodesFromDOM"])(editor, dom);
            return $insertGeneratedNodes(editor, nodes, selection);
        } catch (_unused2) {
        // Fail silently.
        }
    }
    // Multi-line plain text in rich text mode pasted as separate paragraphs
    // instead of single paragraph with linebreaks.
    // Webkit-specific: Supports read 'text/uri-list' in clipboard.
    const text = plainString || dataTransfer.getData('text/uri-list');
    if (text != null) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            const parts = text.split(/(\r?\n|\t)/);
            if (parts[parts.length - 1] === '') {
                parts.pop();
            }
            for(let i = 0; i < parts.length; i++){
                const currentSelection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(currentSelection)) {
                    const part = parts[i];
                    if (part === '\n' || part === '\r\n') {
                        currentSelection.insertParagraph();
                    } else if (part === '\t') {
                        currentSelection.insertNodes([
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTabNode"])()
                        ]);
                    } else {
                        currentSelection.insertText(part);
                    }
                }
            }
        } else {
            selection.insertRawText(text);
        }
    }
}
function trustHTML(html) {
    if (window.trustedTypes && window.trustedTypes.createPolicy) {
        const policy = window.trustedTypes.createPolicy('lexical', {
            createHTML: (input)=>input
        });
        return policy.createHTML(html);
    }
    return html;
}
/**
 * Inserts Lexical nodes into the editor using different strategies depending on
 * some simple selection-based heuristics. If you're looking for a generic way to
 * to insert nodes into the editor at a specific selection point, you probably want
 * {@link lexical.$insertNodes}
 *
 * @param editor LexicalEditor instance to insert the nodes into.
 * @param nodes The nodes to insert.
 * @param selection The selection to insert the nodes into.
 */ function $insertGeneratedNodes(editor, nodes, selection) {
    if (!editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SELECTION_INSERT_CLIPBOARD_NODES_COMMAND"], {
        nodes,
        selection
    })) {
        selection.insertNodes(nodes);
        $updateSelectionOnInsert(selection);
    }
    return;
}
function $updateSelectionOnInsert(selection) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) && selection.isCollapsed()) {
        const anchor = selection.anchor;
        let nodeToInspect = null;
        const anchorCaret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$caretFromPoint"])(anchor, 'previous');
        if (anchorCaret) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextPointCaret"])(anchorCaret)) {
                nodeToInspect = anchorCaret.origin;
            } else {
                const range = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getCaretRange"])(anchorCaret, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getChildCaret"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])(), 'next').getFlipped());
                for (const caret of range){
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(caret.origin)) {
                        nodeToInspect = caret.origin;
                        break;
                    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(caret.origin) && !caret.origin.isInline()) {
                        break;
                    }
                }
            }
        }
        if (nodeToInspect && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(nodeToInspect)) {
            const newFormat = nodeToInspect.getFormat();
            const newStyle = nodeToInspect.getStyle();
            if (selection.format !== newFormat || selection.style !== newStyle) {
                selection.format = newFormat;
                selection.style = newStyle;
                selection.dirty = true;
            }
        }
    }
}
function exportNodeToJSON(node) {
    const serializedNode = node.exportJSON();
    const nodeClass = node.constructor;
    if (serializedNode.type !== nodeClass.getType()) {
        {
            formatDevErrorMessage(`LexicalNode: Node ${nodeClass.name} does not implement .exportJSON().`);
        }
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node)) {
        const serializedChildren = serializedNode.children;
        if (!Array.isArray(serializedChildren)) {
            {
                formatDevErrorMessage(`LexicalNode: Node ${nodeClass.name} is an element but .exportJSON() does not have a children array.`);
            }
        }
    }
    return serializedNode;
}
function $appendNodesToJSON(editor, selection, currentNode, targetArray = []) {
    let shouldInclude = selection !== null ? currentNode.isSelected(selection) : true;
    const shouldExclude = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(currentNode) && currentNode.excludeFromCopy('html');
    let target = currentNode;
    if (selection !== null) {
        let clone = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$cloneWithProperties"])(currentNode);
        clone = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(clone) && selection !== null ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$sliceSelectedTextNodeContent"])(selection, clone) : clone;
        target = clone;
    }
    const children = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(target) ? target.getChildren() : [];
    const serializedNode = exportNodeToJSON(target);
    // TODO: TextNode calls getTextContent() (NOT node.__text) within its exportJSON method
    // which uses getLatest() to get the text from the original node with the same key.
    // This is a deeper issue with the word "clone" here, it's still a reference to the
    // same node as far as the LexicalEditor is concerned since it shares a key.
    // We need a way to create a clone of a Node in memory with its own key, but
    // until then this hack will work for the selected text extract use case.
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(target)) {
        const text = target.__text;
        // If an uncollapsed selection ends or starts at the end of a line of specialized,
        // TextNodes, such as code tokens, we will get a 'blank' TextNode here, i.e., one
        // with text of length 0. We don't want this, it makes a confusing mess. Reset!
        if (text.length > 0) {
            serializedNode.text = text;
        } else {
            shouldInclude = false;
        }
    }
    for(let i = 0; i < children.length; i++){
        const childNode = children[i];
        const shouldIncludeChild = $appendNodesToJSON(editor, selection, childNode, serializedNode.children);
        if (!shouldInclude && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(currentNode) && shouldIncludeChild && currentNode.extractWithChild(childNode, selection, 'clone')) {
            shouldInclude = true;
        }
    }
    if (shouldInclude && !shouldExclude) {
        targetArray.push(serializedNode);
    } else if (Array.isArray(serializedNode.children)) {
        for(let i = 0; i < serializedNode.children.length; i++){
            const serializedChildNode = serializedNode.children[i];
            targetArray.push(serializedChildNode);
        }
    }
    return shouldInclude;
}
// TODO why $ function with Editor instance?
/**
 * Gets the Lexical JSON of the nodes inside the provided Selection.
 *
 * @param editor LexicalEditor to get the JSON content from.
 * @param selection Selection to get the JSON content from.
 * @returns an object with the editor namespace and a list of serializable nodes as JavaScript objects.
 */ function $generateJSONFromSelectedNodes(editor, selection) {
    const nodes = [];
    const root = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])();
    const topLevelChildren = root.getChildren();
    for(let i = 0; i < topLevelChildren.length; i++){
        const topLevelNode = topLevelChildren[i];
        $appendNodesToJSON(editor, selection, topLevelNode, nodes);
    }
    return {
        namespace: editor._config.namespace,
        nodes
    };
}
/**
 * This method takes an array of objects conforming to the BaseSerializedNode interface and returns
 * an Array containing instances of the corresponding LexicalNode classes registered on the editor.
 * Normally, you'd get an Array of BaseSerialized nodes from {@link $generateJSONFromSelectedNodes}
 *
 * @param serializedNodes an Array of objects conforming to the BaseSerializedNode interface.
 * @returns an Array of Lexical Node objects.
 */ function $generateNodesFromSerializedNodes(serializedNodes) {
    const nodes = [];
    for(let i = 0; i < serializedNodes.length; i++){
        const serializedNode = serializedNodes[i];
        const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$parseSerializedNode"])(serializedNode);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(node)) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$addNodeStyle"])(node);
        }
        nodes.push(node);
    }
    return nodes;
}
const EVENT_LATENCY = 50;
let clipboardEventTimeout = null;
// TODO custom selection
// TODO potentially have a node customizable version for plain text
/**
 * Copies the content of the current selection to the clipboard in
 * text/plain, text/html, and application/x-lexical-editor (Lexical JSON)
 * formats.
 *
 * @param editor the LexicalEditor instance to copy content from
 * @param event the native browser ClipboardEvent to add the content to.
 * @returns
 */ async function copyToClipboard(editor, event, data) {
    if (clipboardEventTimeout !== null) {
        // Prevent weird race conditions that can happen when this function is run multiple times
        // synchronously. In the future, we can do better, we can cancel/override the previously running job.
        return false;
    }
    if (event !== null) {
        return new Promise((resolve, reject)=>{
            editor.update(()=>{
                resolve($copyToClipboardEvent(editor, event, data));
            });
        });
    }
    const rootElement = editor.getRootElement();
    const editorWindow = editor._window || window;
    const windowDocument = window.document;
    const domSelection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDOMSelection"])(editorWindow);
    if (rootElement === null || domSelection === null) {
        return false;
    }
    const element = windowDocument.createElement('span');
    element.style.cssText = 'position: fixed; top: -1000px;';
    element.append(windowDocument.createTextNode('#'));
    rootElement.append(element);
    const range = new Range();
    range.setStart(element, 0);
    range.setEnd(element, 1);
    domSelection.removeAllRanges();
    domSelection.addRange(range);
    return new Promise((resolve, reject)=>{
        const removeListener = editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COPY_COMMAND"], (secondEvent)=>{
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["objectKlassEquals"])(secondEvent, ClipboardEvent)) {
                removeListener();
                if (clipboardEventTimeout !== null) {
                    window.clearTimeout(clipboardEventTimeout);
                    clipboardEventTimeout = null;
                }
                resolve($copyToClipboardEvent(editor, secondEvent, data));
            }
            // Block the entire copy flow while we wait for the next ClipboardEvent
            return true;
        }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_CRITICAL"]);
        // If the above hack execCommand hack works, this timeout code should never fire. Otherwise,
        // the listener will be quickly freed so that the user can reuse it again
        clipboardEventTimeout = window.setTimeout(()=>{
            removeListener();
            clipboardEventTimeout = null;
            resolve(false);
        }, EVENT_LATENCY);
        windowDocument.execCommand('copy');
        element.remove();
    });
}
// TODO shouldn't pass editor (pass namespace directly)
function $copyToClipboardEvent(editor, event, data) {
    if (data === undefined) {
        const domSelection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDOMSelection"])(editor._window);
        if (!domSelection) {
            return false;
        }
        const anchorDOM = domSelection.anchorNode;
        const focusDOM = domSelection.focusNode;
        if (anchorDOM !== null && focusDOM !== null && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSelectionWithinEditor"])(editor, anchorDOM, focusDOM)) {
            return false;
        }
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (selection === null) {
            return false;
        }
        data = $getClipboardDataFromSelection(selection);
    }
    event.preventDefault();
    const clipboardData = event.clipboardData;
    if (clipboardData === null) {
        return false;
    }
    setLexicalClipboardDataTransfer(clipboardData, data);
    return true;
}
const clipboardDataFunctions = [
    [
        'text/html',
        $getHtmlContent
    ],
    [
        'application/x-lexical-editor',
        $getLexicalContent
    ]
];
/**
 * Serialize the content of the current selection to strings in
 * text/plain, text/html, and application/x-lexical-editor (Lexical JSON)
 * formats (as available).
 *
 * @param selection the selection to serialize (defaults to $getSelection())
 * @returns LexicalClipboardData
 */ function $getClipboardDataFromSelection(selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])()) {
    const clipboardData = {
        'text/plain': selection ? selection.getTextContent() : ''
    };
    if (selection) {
        const editor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getEditor"])();
        for (const [mimeType, $editorFn] of clipboardDataFunctions){
            const v = $editorFn(editor, selection);
            if (v !== null) {
                clipboardData[mimeType] = v;
            }
        }
    }
    return clipboardData;
}
/**
 * Call setData on the given clipboardData for each MIME type present
 * in the given data (from {@link $getClipboardDataFromSelection})
 *
 * @param clipboardData the event.clipboardData to populate from data
 * @param data The lexical data
 */ function setLexicalClipboardDataTransfer(clipboardData, data) {
    for(const k in data){
        const v = data[k];
        if (v !== undefined) {
            clipboardData.setData(k, v);
        }
    }
}
;
}}),
"[project]/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "$createHeadingNode": (()=>$createHeadingNode),
    "$createQuoteNode": (()=>$createQuoteNode),
    "$isHeadingNode": (()=>$isHeadingNode),
    "$isQuoteNode": (()=>$isQuoteNode),
    "DRAG_DROP_PASTE": (()=>DRAG_DROP_PASTE),
    "HeadingNode": (()=>HeadingNode),
    "QuoteNode": (()=>QuoteNode),
    "eventFiles": (()=>eventFiles),
    "registerRichText": (()=>registerRichText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$clipboard$2f$LexicalClipboard$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/selection/LexicalSelection.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function caretFromPoint(x, y) {
    if (typeof document.caretRangeFromPoint !== 'undefined') {
        const range = document.caretRangeFromPoint(x, y);
        if (range === null) {
            return null;
        }
        return {
            node: range.startContainer,
            offset: range.startOffset
        };
    // @ts-ignore
    } else if (document.caretPositionFromPoint !== 'undefined') {
        // @ts-ignore FF - no types
        const range = document.caretPositionFromPoint(x, y);
        if (range === null) {
            return null;
        }
        return {
            node: range.offsetNode,
            offset: range.offset
        };
    } else {
        // Gracefully handle IE
        return null;
    }
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const documentMode = CAN_USE_DOM && 'documentMode' in document ? document.documentMode : null;
const CAN_USE_BEFORE_INPUT = CAN_USE_DOM && 'InputEvent' in window && !documentMode ? 'getTargetRanges' in new window.InputEvent('input') : false;
const IS_SAFARI = CAN_USE_DOM && /Version\/[\d.]+.*Safari/.test(navigator.userAgent);
const IS_IOS = CAN_USE_DOM && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
// Keep these in case we need to use them in the future.
// export const IS_WINDOWS: boolean = CAN_USE_DOM && /Win/.test(navigator.platform);
const IS_CHROME = CAN_USE_DOM && /^(?=.*Chrome).*/i.test(navigator.userAgent);
const IS_APPLE_WEBKIT = CAN_USE_DOM && /AppleWebKit\/[\d.]+/.test(navigator.userAgent) && !IS_CHROME;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const DRAG_DROP_PASTE = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createCommand"])('DRAG_DROP_PASTE_FILE');
/** @noInheritDoc */ class QuoteNode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementNode"] {
    static getType() {
        return 'quote';
    }
    static clone(node) {
        return new QuoteNode(node.__key);
    }
    // View
    createDOM(config) {
        const element = document.createElement('blockquote');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addClassNamesToElement"])(element, config.theme.quote);
        return element;
    }
    updateDOM(prevNode, dom) {
        return false;
    }
    static importDOM() {
        return {
            blockquote: (node)=>({
                    conversion: $convertBlockquoteElement,
                    priority: 0
                })
        };
    }
    exportDOM(editor) {
        const { element } = super.exportDOM(editor);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLElement"])(element)) {
            if (this.isEmpty()) {
                element.append(document.createElement('br'));
            }
            const formatType = this.getFormatType();
            if (formatType) {
                element.style.textAlign = formatType;
            }
            const direction = this.getDirection();
            if (direction) {
                element.dir = direction;
            }
        }
        return {
            element
        };
    }
    static importJSON(serializedNode) {
        return $createQuoteNode().updateFromJSON(serializedNode);
    }
    // Mutation
    insertNewAfter(_, restoreSelection) {
        const newBlock = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])();
        const direction = this.getDirection();
        newBlock.setDirection(direction);
        this.insertAfter(newBlock, restoreSelection);
        return newBlock;
    }
    collapseAtStart() {
        const paragraph = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])();
        const children = this.getChildren();
        children.forEach((child)=>paragraph.append(child));
        this.replace(paragraph);
        return true;
    }
    canMergeWhenEmpty() {
        return true;
    }
}
function $createQuoteNode() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$applyNodeReplacement"])(new QuoteNode());
}
function $isQuoteNode(node) {
    return node instanceof QuoteNode;
}
/** @noInheritDoc */ class HeadingNode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementNode"] {
    /** @internal */ static getType() {
        return 'heading';
    }
    static clone(node) {
        return new HeadingNode(node.__tag, node.__key);
    }
    constructor(tag, key){
        super(key);
        this.__tag = tag;
    }
    getTag() {
        return this.__tag;
    }
    setTag(tag) {
        const self = this.getWritable();
        this.__tag = tag;
        return self;
    }
    // View
    createDOM(config) {
        const tag = this.__tag;
        const element = document.createElement(tag);
        const theme = config.theme;
        const classNames = theme.heading;
        if (classNames !== undefined) {
            const className = classNames[tag];
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addClassNamesToElement"])(element, className);
        }
        return element;
    }
    updateDOM(prevNode, dom, config) {
        return prevNode.__tag !== this.__tag;
    }
    static importDOM() {
        return {
            h1: (node)=>({
                    conversion: $convertHeadingElement,
                    priority: 0
                }),
            h2: (node)=>({
                    conversion: $convertHeadingElement,
                    priority: 0
                }),
            h3: (node)=>({
                    conversion: $convertHeadingElement,
                    priority: 0
                }),
            h4: (node)=>({
                    conversion: $convertHeadingElement,
                    priority: 0
                }),
            h5: (node)=>({
                    conversion: $convertHeadingElement,
                    priority: 0
                }),
            h6: (node)=>({
                    conversion: $convertHeadingElement,
                    priority: 0
                }),
            p: (node)=>{
                // domNode is a <p> since we matched it by nodeName
                const paragraph = node;
                const firstChild = paragraph.firstChild;
                if (firstChild !== null && isGoogleDocsTitle(firstChild)) {
                    return {
                        conversion: ()=>({
                                node: null
                            }),
                        priority: 3
                    };
                }
                return null;
            },
            span: (node)=>{
                if (isGoogleDocsTitle(node)) {
                    return {
                        conversion: (domNode)=>{
                            return {
                                node: $createHeadingNode('h1')
                            };
                        },
                        priority: 3
                    };
                }
                return null;
            }
        };
    }
    exportDOM(editor) {
        const { element } = super.exportDOM(editor);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLElement"])(element)) {
            if (this.isEmpty()) {
                element.append(document.createElement('br'));
            }
            const formatType = this.getFormatType();
            if (formatType) {
                element.style.textAlign = formatType;
            }
            const direction = this.getDirection();
            if (direction) {
                element.dir = direction;
            }
        }
        return {
            element
        };
    }
    static importJSON(serializedNode) {
        return $createHeadingNode(serializedNode.tag).updateFromJSON(serializedNode);
    }
    updateFromJSON(serializedNode) {
        return super.updateFromJSON(serializedNode).setTag(serializedNode.tag);
    }
    exportJSON() {
        return {
            ...super.exportJSON(),
            tag: this.getTag()
        };
    }
    // Mutation
    insertNewAfter(selection, restoreSelection = true) {
        const anchorOffet = selection ? selection.anchor.offset : 0;
        const lastDesc = this.getLastDescendant();
        const isAtEnd = !lastDesc || selection && selection.anchor.key === lastDesc.getKey() && anchorOffet === lastDesc.getTextContentSize();
        const newElement = isAtEnd || !selection ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])() : $createHeadingNode(this.getTag());
        const direction = this.getDirection();
        newElement.setDirection(direction);
        this.insertAfter(newElement, restoreSelection);
        if (anchorOffet === 0 && !this.isEmpty() && selection) {
            const paragraph = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])();
            paragraph.select();
            this.replace(paragraph, true);
        }
        return newElement;
    }
    collapseAtStart() {
        const newElement = !this.isEmpty() ? $createHeadingNode(this.getTag()) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])();
        const children = this.getChildren();
        children.forEach((child)=>newElement.append(child));
        this.replace(newElement);
        return true;
    }
    extractWithChild() {
        return true;
    }
}
function isGoogleDocsTitle(domNode) {
    if (domNode.nodeName.toLowerCase() === 'span') {
        return domNode.style.fontSize === '26pt';
    }
    return false;
}
function $convertHeadingElement(element) {
    const nodeName = element.nodeName.toLowerCase();
    let node = null;
    if (nodeName === 'h1' || nodeName === 'h2' || nodeName === 'h3' || nodeName === 'h4' || nodeName === 'h5' || nodeName === 'h6') {
        node = $createHeadingNode(nodeName);
        if (element.style !== null) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setNodeIndentFromDOM"])(element, node);
            node.setFormat(element.style.textAlign);
        }
    }
    return {
        node
    };
}
function $convertBlockquoteElement(element) {
    const node = $createQuoteNode();
    if (element.style !== null) {
        node.setFormat(element.style.textAlign);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setNodeIndentFromDOM"])(element, node);
    }
    return {
        node
    };
}
function $createHeadingNode(headingTag = 'h1') {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$applyNodeReplacement"])(new HeadingNode(headingTag));
}
function $isHeadingNode(node) {
    return node instanceof HeadingNode;
}
function onPasteForRichText(event, editor) {
    event.preventDefault();
    editor.update(()=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        const clipboardData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["objectKlassEquals"])(event, InputEvent) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["objectKlassEquals"])(event, KeyboardEvent) ? null : event.clipboardData;
        if (clipboardData != null && selection !== null) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$clipboard$2f$LexicalClipboard$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$insertDataTransferForRichText"])(clipboardData, selection, editor);
        }
    }, {
        tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PASTE_TAG"]
    });
}
async function onCutForRichText(event, editor) {
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$clipboard$2f$LexicalClipboard$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["copyToClipboard"])(editor, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["objectKlassEquals"])(event, ClipboardEvent) ? event : null);
    editor.update(()=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            selection.removeText();
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
            selection.getNodes().forEach((node)=>node.remove());
        }
    });
}
// Clipboard may contain files that we aren't allowed to read. While the event is arguably useless,
// in certain occasions, we want to know whether it was a file transfer, as opposed to text. We
// control this with the first boolean flag.
function eventFiles(event) {
    let dataTransfer = null;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["objectKlassEquals"])(event, DragEvent)) {
        dataTransfer = event.dataTransfer;
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["objectKlassEquals"])(event, ClipboardEvent)) {
        dataTransfer = event.clipboardData;
    }
    if (dataTransfer === null) {
        return [
            false,
            [],
            false
        ];
    }
    const types = dataTransfer.types;
    const hasFiles = types.includes('Files');
    const hasContent = types.includes('text/html') || types.includes('text/plain');
    return [
        hasFiles,
        Array.from(dataTransfer.files),
        hasContent
    ];
}
function $handleIndentAndOutdent(indentOrOutdent) {
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
        return false;
    }
    const alreadyHandled = new Set();
    const nodes = selection.getNodes();
    for(let i = 0; i < nodes.length; i++){
        const node = nodes[i];
        const key = node.getKey();
        if (alreadyHandled.has(key)) {
            continue;
        }
        const parentBlock = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$findMatchingParent"])(node, (parentNode)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(parentNode) && !parentNode.isInline());
        if (parentBlock === null) {
            continue;
        }
        const parentKey = parentBlock.getKey();
        if (parentBlock.canIndent() && !alreadyHandled.has(parentKey)) {
            alreadyHandled.add(parentKey);
            indentOrOutdent(parentBlock);
        }
    }
    return alreadyHandled.size > 0;
}
function $isTargetWithinDecorator(target) {
    const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNearestNodeFromDOMNode"])(target);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isDecoratorNode"])(node);
}
function $isSelectionAtEndOfRoot(selection) {
    const focus = selection.focus;
    return focus.key === 'root' && focus.offset === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])().getChildrenSize();
}
function $isSelectionCollapsedAtFrontOfIndentedBlock(selection) {
    if (!selection.isCollapsed()) {
        return false;
    }
    const { anchor } = selection;
    if (anchor.offset !== 0) {
        return false;
    }
    const anchorNode = anchor.getNode();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootNode"])(anchorNode)) {
        return false;
    }
    const element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$getNearestBlockElementAncestorOrThrow"])(anchorNode);
    return element.getIndent() > 0 && (element.is(anchorNode) || anchorNode.is(element.getFirstDescendant()));
}
/**
 * Resets the capitalization of the selection to default.
 * Called when the user presses space, tab, or enter key.
 * @param selection The selection to reset the capitalization of.
 */ function $resetCapitalization(selection) {
    for (const format of [
        'lowercase',
        'uppercase',
        'capitalize'
    ]){
        if (selection.hasFormat(format)) {
            selection.toggleFormat(format);
        }
    }
}
function registerRichText(editor) {
    const removeListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])(editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLICK_COMMAND"], (payload)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
            selection.clear();
            return true;
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DELETE_CHARACTER_COMMAND"], (isBackward)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            selection.deleteCharacter(isBackward);
            return true;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
            selection.deleteNodes();
            return true;
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DELETE_WORD_COMMAND"], (isBackward)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        selection.deleteWord(isBackward);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DELETE_LINE_COMMAND"], (isBackward)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        selection.deleteLine(isBackward);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTROLLED_TEXT_INSERTION_COMMAND"], (eventOrText)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (typeof eventOrText === 'string') {
            if (selection !== null) {
                selection.insertText(eventOrText);
            }
        } else {
            if (selection === null) {
                return false;
            }
            const dataTransfer = eventOrText.dataTransfer;
            if (dataTransfer != null) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$clipboard$2f$LexicalClipboard$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$insertDataTransferForRichText"])(dataTransfer, selection, editor);
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
                const data = eventOrText.data;
                if (data) {
                    selection.insertText(data);
                }
                return true;
            }
        }
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REMOVE_TEXT_COMMAND"], ()=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        selection.removeText();
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FORMAT_TEXT_COMMAND"], (format)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        selection.formatText(format);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FORMAT_ELEMENT_COMMAND"], (format)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
            return false;
        }
        const nodes = selection.getNodes();
        for (const node of nodes){
            const element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$findMatchingParent"])(node, (parentNode)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(parentNode) && !parentNode.isInline());
            if (element !== null) {
                element.setFormat(format);
            }
        }
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_LINE_BREAK_COMMAND"], (selectStart)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        selection.insertLineBreak(selectStart);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_PARAGRAPH_COMMAND"], ()=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        selection.insertParagraph();
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_TAB_COMMAND"], ()=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$insertNodes"])([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTabNode"])()
        ]);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INDENT_CONTENT_COMMAND"], ()=>{
        return $handleIndentAndOutdent((block)=>{
            const indent = block.getIndent();
            block.setIndent(indent + 1);
        });
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OUTDENT_CONTENT_COMMAND"], ()=>{
        return $handleIndentAndOutdent((block)=>{
            const indent = block.getIndent();
            if (indent > 0) {
                block.setIndent(Math.max(0, indent - 1));
            }
        });
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_UP_COMMAND"], (event)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
            // If selection is on a node, let's try and move selection
            // back to being a range selection.
            const nodes = selection.getNodes();
            if (nodes.length > 0) {
                nodes[0].selectPrevious();
                return true;
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            const possibleNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getAdjacentNode"])(selection.focus, true);
            if (!event.shiftKey && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isDecoratorNode"])(possibleNode) && !possibleNode.isIsolated() && !possibleNode.isInline()) {
                possibleNode.selectPrevious();
                event.preventDefault();
                return true;
            }
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_DOWN_COMMAND"], (event)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
            // If selection is on a node, let's try and move selection
            // back to being a range selection.
            const nodes = selection.getNodes();
            if (nodes.length > 0) {
                nodes[0].selectNext(0, 0);
                return true;
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            if ($isSelectionAtEndOfRoot(selection)) {
                event.preventDefault();
                return true;
            }
            const possibleNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getAdjacentNode"])(selection.focus, false);
            if (!event.shiftKey && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isDecoratorNode"])(possibleNode) && !possibleNode.isIsolated() && !possibleNode.isInline()) {
                possibleNode.selectNext();
                event.preventDefault();
                return true;
            }
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_LEFT_COMMAND"], (event)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
            // If selection is on a node, let's try and move selection
            // back to being a range selection.
            const nodes = selection.getNodes();
            if (nodes.length > 0) {
                event.preventDefault();
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$isParentRTL"])(nodes[0])) {
                    nodes[0].selectNext(0, 0);
                } else {
                    nodes[0].selectPrevious();
                }
                return true;
            }
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$shouldOverrideDefaultCharacterSelection"])(selection, true)) {
            const isHoldingShift = event.shiftKey;
            event.preventDefault();
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$moveCharacter"])(selection, isHoldingShift, true);
            return true;
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_RIGHT_COMMAND"], (event)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
            // If selection is on a node, let's try and move selection
            // back to being a range selection.
            const nodes = selection.getNodes();
            if (nodes.length > 0) {
                event.preventDefault();
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$isParentRTL"])(nodes[0])) {
                    nodes[0].selectPrevious();
                } else {
                    nodes[0].selectNext(0, 0);
                }
                return true;
            }
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        const isHoldingShift = event.shiftKey;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$shouldOverrideDefaultCharacterSelection"])(selection, false)) {
            event.preventDefault();
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$moveCharacter"])(selection, isHoldingShift, false);
            return true;
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_BACKSPACE_COMMAND"], (event)=>{
        if ($isTargetWithinDecorator(event.target)) {
            return false;
        }
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            if ($isSelectionCollapsedAtFrontOfIndentedBlock(selection)) {
                event.preventDefault();
                return editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OUTDENT_CONTENT_COMMAND"], undefined);
            }
            // Exception handling for iOS native behavior instead of Lexical's behavior when using Korean on iOS devices.
            // more details - https://github.com/facebook/lexical/issues/5841
            if (IS_IOS && navigator.language === 'ko-KR') {
                return false;
            }
        } else if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
            return false;
        }
        event.preventDefault();
        return editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DELETE_CHARACTER_COMMAND"], true);
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_DELETE_COMMAND"], (event)=>{
        if ($isTargetWithinDecorator(event.target)) {
            return false;
        }
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection))) {
            return false;
        }
        event.preventDefault();
        return editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DELETE_CHARACTER_COMMAND"], false);
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ENTER_COMMAND"], (event)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        $resetCapitalization(selection);
        if (event !== null) {
            // If we have beforeinput, then we can avoid blocking
            // the default behavior. This ensures that the iOS can
            // intercept that we're actually inserting a paragraph,
            // and autocomplete, autocapitalize etc work as intended.
            // This can also cause a strange performance issue in
            // Safari, where there is a noticeable pause due to
            // preventing the key down of enter.
            if ((IS_IOS || IS_SAFARI || IS_APPLE_WEBKIT) && CAN_USE_BEFORE_INPUT) {
                return false;
            }
            event.preventDefault();
            if (event.shiftKey) {
                return editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_LINE_BREAK_COMMAND"], false);
            }
        }
        return editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_PARAGRAPH_COMMAND"], undefined);
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ESCAPE_COMMAND"], ()=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        editor.blur();
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DROP_COMMAND"], (event)=>{
        const [, files] = eventFiles(event);
        if (files.length > 0) {
            const x = event.clientX;
            const y = event.clientY;
            const eventRange = caretFromPoint(x, y);
            if (eventRange !== null) {
                const { offset: domOffset, node: domNode } = eventRange;
                const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNearestNodeFromDOMNode"])(domNode);
                if (node !== null) {
                    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createRangeSelection"])();
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(node)) {
                        selection.anchor.set(node.getKey(), domOffset, 'text');
                        selection.focus.set(node.getKey(), domOffset, 'text');
                    } else {
                        const parentKey = node.getParentOrThrow().getKey();
                        const offset = node.getIndexWithinParent() + 1;
                        selection.anchor.set(parentKey, offset, 'element');
                        selection.focus.set(parentKey, offset, 'element');
                    }
                    const normalizedSelection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$normalizeSelection__EXPERIMENTAL"])(selection);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setSelection"])(normalizedSelection);
                }
                editor.dispatchCommand(DRAG_DROP_PASTE, files);
            }
            event.preventDefault();
            return true;
        }
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return true;
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DRAGSTART_COMMAND"], (event)=>{
        const [isFileTransfer] = eventFiles(event);
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (isFileTransfer && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DRAGOVER_COMMAND"], (event)=>{
        const [isFileTransfer] = eventFiles(event);
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (isFileTransfer && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        const x = event.clientX;
        const y = event.clientY;
        const eventRange = caretFromPoint(x, y);
        if (eventRange !== null) {
            const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNearestNodeFromDOMNode"])(eventRange.node);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isDecoratorNode"])(node)) {
                // Show browser caret as the user is dragging the media across the screen. Won't work
                // for DecoratorNode nor it's relevant.
                event.preventDefault();
            }
        }
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SELECT_ALL_COMMAND"], ()=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$selectAll"])();
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COPY_COMMAND"], (event)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$clipboard$2f$LexicalClipboard$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["copyToClipboard"])(editor, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["objectKlassEquals"])(event, ClipboardEvent) ? event : null);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CUT_COMMAND"], (event)=>{
        onCutForRichText(event, editor);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PASTE_COMMAND"], (event)=>{
        const [, files, hasTextContent] = eventFiles(event);
        if (files.length > 0 && !hasTextContent) {
            editor.dispatchCommand(DRAG_DROP_PASTE, files);
            return true;
        }
        // if inputs then paste within the input ignore creating a new node on paste event
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDOMNode"])(event.target) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSelectionCapturedInDecoratorInput"])(event.target)) {
            return false;
        }
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (selection !== null) {
            onPasteForRichText(event, editor);
            return true;
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_SPACE_COMMAND"], (_)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            $resetCapitalization(selection);
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_TAB_COMMAND"], (_)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            $resetCapitalization(selection);
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]));
    return removeListener;
}
;
}}),
"[project]/node_modules/@lexical/text/LexicalText.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "$canShowPlaceholder": (()=>$canShowPlaceholder),
    "$canShowPlaceholderCurry": (()=>$canShowPlaceholderCurry),
    "$findTextIntersectionFromCharacters": (()=>$findTextIntersectionFromCharacters),
    "$isRootTextContentEmpty": (()=>$isRootTextContentEmpty),
    "$isRootTextContentEmptyCurry": (()=>$isRootTextContentEmptyCurry),
    "$rootTextContent": (()=>$rootTextContent),
    "registerLexicalTextEntity": (()=>registerLexicalTextEntity)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * Returns the root's text content.
 * @returns The root's text content.
 */ function $rootTextContent() {
    const root = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])();
    return root.getTextContent();
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * Determines if the root has any text content and can trim any whitespace if it does.
 * @param isEditorComposing - Is the editor in composition mode due to an active Input Method Editor?
 * @param trim - Should the root text have its whitespaced trimmed? Defaults to true.
 * @returns true if text content is empty, false if there is text or isEditorComposing is true.
 */ function $isRootTextContentEmpty(isEditorComposing, trim = true) {
    if (isEditorComposing) {
        return false;
    }
    let text = $rootTextContent();
    if (trim) {
        text = text.trim();
    }
    return text === '';
}
/**
 * Returns a function that executes {@link $isRootTextContentEmpty}
 * @param isEditorComposing - Is the editor in composition mode due to an active Input Method Editor?
 * @param trim - Should the root text have its whitespaced trimmed? Defaults to true.
 * @returns A function that executes $isRootTextContentEmpty based on arguments.
 */ function $isRootTextContentEmptyCurry(isEditorComposing, trim) {
    return ()=>$isRootTextContentEmpty(isEditorComposing, trim);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * Determines if the input should show the placeholder. If anything is in
 * in the root the placeholder should not be shown.
 * @param isComposing - Is the editor in composition mode due to an active Input Method Editor?
 * @returns true if the input should show the placeholder, false otherwise.
 */ function $canShowPlaceholder(isComposing) {
    if (!$isRootTextContentEmpty(isComposing, false)) {
        return false;
    }
    const root = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])();
    const children = root.getChildren();
    const childrenLength = children.length;
    if (childrenLength > 1) {
        return false;
    }
    for(let i = 0; i < childrenLength; i++){
        const topBlock = children[i];
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isDecoratorNode"])(topBlock)) {
            return false;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(topBlock)) {
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isParagraphNode"])(topBlock)) {
                return false;
            }
            if (topBlock.__indent !== 0) {
                return false;
            }
            const topBlockChildren = topBlock.getChildren();
            const topBlockChildrenLength = topBlockChildren.length;
            for(let s = 0; s < topBlockChildrenLength; s++){
                const child = topBlockChildren[i];
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(child)) {
                    return false;
                }
            }
        }
    }
    return true;
}
/**
 * Returns a function that executes {@link $canShowPlaceholder}
 * @param isEditorComposing - Is the editor in composition mode due to an active Input Method Editor?
 * @returns A function that executes $canShowPlaceholder with arguments.
 */ function $canShowPlaceholderCurry(isEditorComposing) {
    return ()=>$canShowPlaceholder(isEditorComposing);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * Finds a TextNode with a size larger than targetCharacters and returns
 * the node along with the remaining length of the text.
 * @param root - The RootNode.
 * @param targetCharacters - The number of characters whose TextNode must be larger than.
 * @returns The TextNode and the intersections offset, or null if no TextNode is found.
 */ function $findTextIntersectionFromCharacters(root, targetCharacters) {
    let node = root.getFirstChild();
    let currentCharacters = 0;
    mainLoop: while(node !== null){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node)) {
            const child = node.getFirstChild();
            if (child !== null) {
                node = child;
                continue;
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(node)) {
            const characters = node.getTextContentSize();
            if (currentCharacters + characters > targetCharacters) {
                return {
                    node,
                    offset: targetCharacters - currentCharacters
                };
            }
            currentCharacters += characters;
        }
        const sibling = node.getNextSibling();
        if (sibling !== null) {
            node = sibling;
            continue;
        }
        let parent = node.getParent();
        while(parent !== null){
            const parentSibling = parent.getNextSibling();
            if (parentSibling !== null) {
                node = parentSibling;
                continue mainLoop;
            }
            parent = parent.getParent();
        }
        break;
    }
    return null;
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Do not require this module directly! Use normal `invariant` calls.
function formatDevErrorMessage(message) {
    throw new Error(message);
}
/**
 * Returns a tuple that can be rested (...) into mergeRegister to clean up
 * node transforms listeners that transforms text into another node, eg. a HashtagNode.
 * @example
 * ```ts
 *   useEffect(() => {
    return mergeRegister(
      ...registerLexicalTextEntity(editor, getMatch, targetNode, createNode),
    );
  }, [createNode, editor, getMatch, targetNode]);
 * ```
 * Where targetNode is the type of node containing the text you want to transform (like a text input),
 * then getMatch uses a regex to find a matching text and creates the proper node to include the matching text.
 * @param editor - The lexical editor.
 * @param getMatch - Finds a matching string that satisfies a regex expression.
 * @param targetNode - The node type that contains text to match with. eg. HashtagNode
 * @param createNode - A function that creates a new node to contain the matched text. eg createHashtagNode
 * @returns An array containing the plain text and reverse node transform listeners.
 */ function registerLexicalTextEntity(editor, getMatch, targetNode, createNode) {
    const isTargetNode = (node)=>{
        return node instanceof targetNode;
    };
    const $replaceWithSimpleText = (node)=>{
        const textNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(node.getTextContent());
        textNode.setFormat(node.getFormat());
        node.replace(textNode);
    };
    const getMode = (node)=>{
        return node.getLatest().__mode;
    };
    const $textNodeTransform = (node)=>{
        if (!node.isSimpleText()) {
            return;
        }
        let prevSibling = node.getPreviousSibling();
        let text = node.getTextContent();
        let currentNode = node;
        let match;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(prevSibling)) {
            const previousText = prevSibling.getTextContent();
            const combinedText = previousText + text;
            const prevMatch = getMatch(combinedText);
            if (isTargetNode(prevSibling)) {
                if (prevMatch === null || getMode(prevSibling) !== 0) {
                    $replaceWithSimpleText(prevSibling);
                    return;
                } else {
                    const diff = prevMatch.end - previousText.length;
                    if (diff > 0) {
                        const concatText = text.slice(0, diff);
                        const newTextContent = previousText + concatText;
                        prevSibling.select();
                        prevSibling.setTextContent(newTextContent);
                        if (diff === text.length) {
                            node.remove();
                        } else {
                            const remainingText = text.slice(diff);
                            node.setTextContent(remainingText);
                        }
                        return;
                    }
                }
            } else if (prevMatch === null || prevMatch.start < previousText.length) {
                return;
            }
        }
        let prevMatchLengthToSkip = 0;
        // eslint-disable-next-line no-constant-condition
        while(true){
            match = getMatch(text);
            let nextText = match === null ? '' : text.slice(match.end);
            text = nextText;
            if (nextText === '') {
                const nextSibling = currentNode.getNextSibling();
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(nextSibling)) {
                    nextText = currentNode.getTextContent() + nextSibling.getTextContent();
                    const nextMatch = getMatch(nextText);
                    if (nextMatch === null) {
                        if (isTargetNode(nextSibling)) {
                            $replaceWithSimpleText(nextSibling);
                        } else {
                            nextSibling.markDirty();
                        }
                        return;
                    } else if (nextMatch.start !== 0) {
                        return;
                    }
                }
            }
            if (match === null) {
                return;
            }
            if (match.start === 0 && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(prevSibling) && prevSibling.isTextEntity()) {
                prevMatchLengthToSkip += match.end;
                continue;
            }
            let nodeToReplace;
            if (match.start === 0) {
                [nodeToReplace, currentNode] = currentNode.splitText(match.end);
            } else {
                [, nodeToReplace, currentNode] = currentNode.splitText(match.start + prevMatchLengthToSkip, match.end + prevMatchLengthToSkip);
            }
            if (!(nodeToReplace !== undefined)) {
                formatDevErrorMessage(`${'nodeToReplace'} should not be undefined. You may want to check splitOffsets passed to the splitText.`);
            }
            const replacementNode = createNode(nodeToReplace);
            replacementNode.setFormat(nodeToReplace.getFormat());
            nodeToReplace.replace(replacementNode);
            if (currentNode == null) {
                return;
            }
            prevMatchLengthToSkip = 0;
            prevSibling = replacementNode;
        }
    };
    const $reverseNodeTransform = (node)=>{
        const text = node.getTextContent();
        const match = getMatch(text);
        if (match === null || match.start !== 0) {
            $replaceWithSimpleText(node);
            return;
        }
        if (text.length > match.end) {
            // This will split out the rest of the text as simple text
            node.splitText(match.end);
            return;
        }
        const prevSibling = node.getPreviousSibling();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(prevSibling) && prevSibling.isTextEntity()) {
            $replaceWithSimpleText(prevSibling);
            $replaceWithSimpleText(node);
        }
        const nextSibling = node.getNextSibling();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(nextSibling) && nextSibling.isTextEntity()) {
            $replaceWithSimpleText(nextSibling);
            // This may have already been converted in the previous block
            if (isTargetNode(node)) {
                $replaceWithSimpleText(node);
            }
        }
    };
    const removePlainTextTransform = editor.registerNodeTransform(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextNode"], $textNodeTransform);
    const removeReverseNodeTransform = editor.registerNodeTransform(targetNode, $reverseNodeTransform);
    return [
        removePlainTextTransform,
        removeReverseNodeTransform
    ];
}
;
}}),
"[project]/node_modules/@lexical/dragon/LexicalDragon.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "registerDragonSupport": (()=>registerDragonSupport)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function registerDragonSupport(editor) {
    const origin = window.location.origin;
    const handler = (event)=>{
        if (event.origin !== origin) {
            return;
        }
        const rootElement = editor.getRootElement();
        if (document.activeElement !== rootElement) {
            return;
        }
        const data = event.data;
        if (typeof data === 'string') {
            let parsedData;
            try {
                parsedData = JSON.parse(data);
            } catch (e) {
                return;
            }
            if (parsedData && parsedData.protocol === 'nuanria_messaging' && parsedData.type === 'request') {
                const payload = parsedData.payload;
                if (payload && payload.functionId === 'makeChanges') {
                    const args = payload.args;
                    if (args) {
                        const [elementStart, elementLength, text, selStart, selLength, formatCommand] = args;
                        editor.update(()=>{
                            const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
                            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
                                const anchor = selection.anchor;
                                let anchorNode = anchor.getNode();
                                let setSelStart = 0;
                                let setSelEnd = 0;
                                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(anchorNode)) {
                                    // set initial selection
                                    if (elementStart >= 0 && elementLength >= 0) {
                                        setSelStart = elementStart;
                                        setSelEnd = elementStart + elementLength;
                                        // If the offset is more than the end, make it the end
                                        selection.setTextNodeRange(anchorNode, setSelStart, anchorNode, setSelEnd);
                                    }
                                }
                                if (setSelStart !== setSelEnd || text !== '') {
                                    selection.insertRawText(text);
                                    anchorNode = anchor.getNode();
                                }
                                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(anchorNode)) {
                                    // set final selection
                                    setSelStart = selStart;
                                    setSelEnd = selStart + selLength;
                                    const anchorNodeTextLength = anchorNode.getTextContentSize();
                                    // If the offset is more than the end, make it the end
                                    setSelStart = setSelStart > anchorNodeTextLength ? anchorNodeTextLength : setSelStart;
                                    setSelEnd = setSelEnd > anchorNodeTextLength ? anchorNodeTextLength : setSelEnd;
                                    selection.setTextNodeRange(anchorNode, setSelStart, anchorNode, setSelEnd);
                                }
                                // block the chrome extension from handling this event
                                event.stopImmediatePropagation();
                            }
                        });
                    }
                }
            }
        }
    };
    window.addEventListener('message', handler, true);
    return ()=>{
        window.removeEventListener('message', handler, true);
    };
}
;
}}),
"[project]/node_modules/@lexical/list/LexicalList.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "$createListItemNode": (()=>$createListItemNode),
    "$createListNode": (()=>$createListNode),
    "$getListDepth": (()=>$getListDepth),
    "$handleListInsertParagraph": (()=>$handleListInsertParagraph),
    "$insertList": (()=>$insertList),
    "$isListItemNode": (()=>$isListItemNode),
    "$isListNode": (()=>$isListNode),
    "$removeList": (()=>$removeList),
    "INSERT_CHECK_LIST_COMMAND": (()=>INSERT_CHECK_LIST_COMMAND),
    "INSERT_ORDERED_LIST_COMMAND": (()=>INSERT_ORDERED_LIST_COMMAND),
    "INSERT_UNORDERED_LIST_COMMAND": (()=>INSERT_UNORDERED_LIST_COMMAND),
    "ListItemNode": (()=>ListItemNode),
    "ListNode": (()=>ListNode),
    "REMOVE_LIST_COMMAND": (()=>REMOVE_LIST_COMMAND),
    "UPDATE_LIST_START_COMMAND": (()=>UPDATE_LIST_START_COMMAND),
    "insertList": (()=>insertList),
    "registerCheckList": (()=>registerCheckList),
    "registerList": (()=>registerList),
    "registerListStrictIndentTransform": (()=>registerListStrictIndentTransform),
    "removeList": (()=>removeList)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/selection/LexicalSelection.dev.mjs [app-client] (ecmascript) <locals>");
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Do not require this module directly! Use normal `invariant` calls.
function formatDevErrorMessage(message) {
    throw new Error(message);
}
/**
 * Checks the depth of listNode from the root node.
 * @param listNode - The ListNode to be checked.
 * @returns The depth of the ListNode.
 */ function $getListDepth(listNode) {
    let depth = 1;
    let parent = listNode.getParent();
    while(parent != null){
        if ($isListItemNode(parent)) {
            const parentList = parent.getParent();
            if ($isListNode(parentList)) {
                depth++;
                parent = parentList.getParent();
                continue;
            }
            {
                formatDevErrorMessage(`A ListItemNode must have a ListNode for a parent.`);
            }
        }
        return depth;
    }
    return depth;
}
/**
 * Finds the nearest ancestral ListNode and returns it, throws an invariant if listItem is not a ListItemNode.
 * @param listItem - The node to be checked.
 * @returns The ListNode found.
 */ function $getTopListNode(listItem) {
    let list = listItem.getParent();
    if (!$isListNode(list)) {
        {
            formatDevErrorMessage(`A ListItemNode must have a ListNode for a parent.`);
        }
    }
    let parent = list;
    while(parent !== null){
        parent = parent.getParent();
        if ($isListNode(parent)) {
            list = parent;
        }
    }
    return list;
}
/**
 * A recursive Depth-First Search (Postorder Traversal) that finds all of a node's children
 * that are of type ListItemNode and returns them in an array.
 * @param node - The ListNode to start the search.
 * @returns An array containing all nodes of type ListItemNode found.
 */ // This should probably be $getAllChildrenOfType
function $getAllListItems(node) {
    let listItemNodes = [];
    const listChildren = node.getChildren().filter($isListItemNode);
    for(let i = 0; i < listChildren.length; i++){
        const listItemNode = listChildren[i];
        const firstChild = listItemNode.getFirstChild();
        if ($isListNode(firstChild)) {
            listItemNodes = listItemNodes.concat($getAllListItems(firstChild));
        } else {
            listItemNodes.push(listItemNode);
        }
    }
    return listItemNodes;
}
/**
 * Checks to see if the passed node is a ListItemNode and has a ListNode as a child.
 * @param node - The node to be checked.
 * @returns true if the node is a ListItemNode and has a ListNode child, false otherwise.
 */ function isNestedListNode(node) {
    return $isListItemNode(node) && $isListNode(node.getFirstChild());
}
/**
 * Takes a deeply nested ListNode or ListItemNode and traverses up the branch to delete the first
 * ancestral ListNode (which could be the root ListNode) or ListItemNode with siblings, essentially
 * bringing the deeply nested node up the branch once. Would remove sublist if it has siblings.
 * Should not break ListItem -> List -> ListItem chain as empty List/ItemNodes should be removed on .remove().
 * @param sublist - The nested ListNode or ListItemNode to be brought up the branch.
 */ function $removeHighestEmptyListParent(sublist) {
    // Nodes may be repeatedly indented, to create deeply nested lists that each
    // contain just one bullet.
    // Our goal is to remove these (empty) deeply nested lists. The easiest
    // way to do that is crawl back up the tree until we find a node that has siblings
    // (e.g. is actually part of the list contents) and delete that, or delete
    // the root of the list (if no list nodes have siblings.)
    let emptyListPtr = sublist;
    while(emptyListPtr.getNextSibling() == null && emptyListPtr.getPreviousSibling() == null){
        const parent = emptyListPtr.getParent();
        if (parent == null || !($isListItemNode(parent) || $isListNode(parent))) {
            break;
        }
        emptyListPtr = parent;
    }
    emptyListPtr.remove();
}
/**
 * Wraps a node into a ListItemNode.
 * @param node - The node to be wrapped into a ListItemNode
 * @returns The ListItemNode which the passed node is wrapped in.
 */ function $wrapInListItem(node) {
    const listItemWrapper = $createListItemNode();
    return listItemWrapper.append(node);
}
function $isSelectingEmptyListItem(anchorNode, nodes) {
    return $isListItemNode(anchorNode) && (nodes.length === 0 || nodes.length === 1 && anchorNode.is(nodes[0]) && anchorNode.getChildrenSize() === 0);
}
/**
 * Inserts a new ListNode. If the selection's anchor node is an empty ListItemNode and is a child of
 * the root/shadow root, it will replace the ListItemNode with a ListNode and the old ListItemNode.
 * Otherwise it will replace its parent with a new ListNode and re-insert the ListItemNode and any previous children.
 * If the selection's anchor node is not an empty ListItemNode, it will add a new ListNode or merge an existing ListNode,
 * unless the the node is a leaf node, in which case it will attempt to find a ListNode up the branch and replace it with
 * a new ListNode, or create a new ListNode at the nearest root/shadow root.
 * @param listType - The type of list, "number" | "bullet" | "check".
 */ function $insertList(listType) {
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    if (selection !== null) {
        let nodes = selection.getNodes();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            const anchorAndFocus = selection.getStartEndPoints();
            if (!(anchorAndFocus !== null)) {
                formatDevErrorMessage(`insertList: anchor should be defined`);
            }
            const [anchor] = anchorAndFocus;
            const anchorNode = anchor.getNode();
            const anchorNodeParent = anchorNode.getParent();
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootOrShadowRoot"])(anchorNode)) {
                const firstChild = anchorNode.getFirstChild();
                if (firstChild) {
                    nodes = firstChild.selectStart().getNodes();
                } else {
                    const paragraph = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])();
                    anchorNode.append(paragraph);
                    nodes = paragraph.select().getNodes();
                }
            } else if ($isSelectingEmptyListItem(anchorNode, nodes)) {
                const list = $createListNode(listType);
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootOrShadowRoot"])(anchorNodeParent)) {
                    anchorNode.replace(list);
                    const listItem = $createListItemNode();
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(anchorNode)) {
                        listItem.setFormat(anchorNode.getFormatType());
                        listItem.setIndent(anchorNode.getIndent());
                    }
                    list.append(listItem);
                } else if ($isListItemNode(anchorNode)) {
                    const parent = anchorNode.getParentOrThrow();
                    append(list, parent.getChildren());
                    parent.replace(list);
                }
                return;
            }
        }
        const handled = new Set();
        for(let i = 0; i < nodes.length; i++){
            const node = nodes[i];
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node) && node.isEmpty() && !$isListItemNode(node) && !handled.has(node.getKey())) {
                $createListOrMerge(node, listType);
                continue;
            }
            let parent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLeafNode"])(node) ? node.getParent() : $isListItemNode(node) && node.isEmpty() ? node : null;
            while(parent != null){
                const parentKey = parent.getKey();
                if ($isListNode(parent)) {
                    if (!handled.has(parentKey)) {
                        const newListNode = $createListNode(listType);
                        append(newListNode, parent.getChildren());
                        parent.replace(newListNode);
                        handled.add(parentKey);
                    }
                    break;
                } else {
                    const nextParent = parent.getParent();
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootOrShadowRoot"])(nextParent) && !handled.has(parentKey)) {
                        handled.add(parentKey);
                        $createListOrMerge(parent, listType);
                        break;
                    }
                    parent = nextParent;
                }
            }
        }
    }
}
function append(node, nodesToAppend) {
    node.splice(node.getChildrenSize(), 0, nodesToAppend);
}
function $createListOrMerge(node, listType) {
    if ($isListNode(node)) {
        return node;
    }
    const previousSibling = node.getPreviousSibling();
    const nextSibling = node.getNextSibling();
    const listItem = $createListItemNode();
    append(listItem, node.getChildren());
    let targetList;
    if ($isListNode(previousSibling) && listType === previousSibling.getListType()) {
        previousSibling.append(listItem);
        // if the same type of list is on both sides, merge them.
        if ($isListNode(nextSibling) && listType === nextSibling.getListType()) {
            append(previousSibling, nextSibling.getChildren());
            nextSibling.remove();
        }
        targetList = previousSibling;
    } else if ($isListNode(nextSibling) && listType === nextSibling.getListType()) {
        nextSibling.getFirstChildOrThrow().insertBefore(listItem);
        targetList = nextSibling;
    } else {
        const list = $createListNode(listType);
        list.append(listItem);
        node.replace(list);
        targetList = list;
    }
    // listItem needs to be attached to root prior to setting indent
    listItem.setFormat(node.getFormatType());
    listItem.setIndent(node.getIndent());
    node.remove();
    return targetList;
}
/**
 * A recursive function that goes through each list and their children, including nested lists,
 * appending list2 children after list1 children and updating ListItemNode values.
 * @param list1 - The first list to be merged.
 * @param list2 - The second list to be merged.
 */ function mergeLists(list1, list2) {
    const listItem1 = list1.getLastChild();
    const listItem2 = list2.getFirstChild();
    if (listItem1 && listItem2 && isNestedListNode(listItem1) && isNestedListNode(listItem2)) {
        mergeLists(listItem1.getFirstChild(), listItem2.getFirstChild());
        listItem2.remove();
    }
    const toMerge = list2.getChildren();
    if (toMerge.length > 0) {
        list1.append(...toMerge);
    }
    list2.remove();
}
/**
 * Searches for the nearest ancestral ListNode and removes it. If selection is an empty ListItemNode
 * it will remove the whole list, including the ListItemNode. For each ListItemNode in the ListNode,
 * removeList will also generate new ParagraphNodes in the removed ListNode's place. Any child node
 * inside a ListItemNode will be appended to the new ParagraphNodes.
 */ function $removeList() {
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
        const listNodes = new Set();
        const nodes = selection.getNodes();
        const anchorNode = selection.anchor.getNode();
        if ($isSelectingEmptyListItem(anchorNode, nodes)) {
            listNodes.add($getTopListNode(anchorNode));
        } else {
            for(let i = 0; i < nodes.length; i++){
                const node = nodes[i];
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLeafNode"])(node)) {
                    const listItemNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$getNearestNodeOfType"])(node, ListItemNode);
                    if (listItemNode != null) {
                        listNodes.add($getTopListNode(listItemNode));
                    }
                }
            }
        }
        for (const listNode of listNodes){
            let insertionPoint = listNode;
            const listItems = $getAllListItems(listNode);
            for (const listItemNode of listItems){
                const paragraph = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])().setTextStyle(selection.style).setTextFormat(selection.format);
                append(paragraph, listItemNode.getChildren());
                insertionPoint.insertAfter(paragraph);
                insertionPoint = paragraph;
                // When the anchor and focus fall on the textNode
                // we don't have to change the selection because the textNode will be appended to
                // the newly generated paragraph.
                // When selection is in empty nested list item, selection is actually on the listItemNode.
                // When the corresponding listItemNode is deleted and replaced by the newly generated paragraph
                // we should manually set the selection's focus and anchor to the newly generated paragraph.
                if (listItemNode.__key === selection.anchor.key) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setPointFromCaret"])(selection.anchor, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$normalizeCaret"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getChildCaret"])(paragraph, 'next')));
                }
                if (listItemNode.__key === selection.focus.key) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setPointFromCaret"])(selection.focus, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$normalizeCaret"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getChildCaret"])(paragraph, 'next')));
                }
                listItemNode.remove();
            }
            listNode.remove();
        }
    }
}
/**
 * Takes the value of a child ListItemNode and makes it the value the ListItemNode
 * should be if it isn't already. Also ensures that checked is undefined if the
 * parent does not have a list type of 'check'.
 * @param list - The list whose children are updated.
 */ function updateChildrenListItemValue(list) {
    const isNotChecklist = list.getListType() !== 'check';
    let value = list.getStart();
    for (const child of list.getChildren()){
        if ($isListItemNode(child)) {
            if (child.getValue() !== value) {
                child.setValue(value);
            }
            if (isNotChecklist && child.getLatest().__checked != null) {
                child.setChecked(undefined);
            }
            if (!$isListNode(child.getFirstChild())) {
                value++;
            }
        }
    }
}
/**
 * Merge the next sibling list if same type.
 * <ul> will merge with <ul>, but NOT <ul> with <ol>.
 * @param list - The list whose next sibling should be potentially merged
 */ function mergeNextSiblingListIfSameType(list) {
    const nextSibling = list.getNextSibling();
    if ($isListNode(nextSibling) && list.getListType() === nextSibling.getListType()) {
        mergeLists(list, nextSibling);
    }
}
/**
 * Adds an empty ListNode/ListItemNode chain at listItemNode, so as to
 * create an indent effect. Won't indent ListItemNodes that have a ListNode as
 * a child, but does merge sibling ListItemNodes if one has a nested ListNode.
 * @param listItemNode - The ListItemNode to be indented.
 */ function $handleIndent(listItemNode) {
    // go through each node and decide where to move it.
    const removed = new Set();
    if (isNestedListNode(listItemNode) || removed.has(listItemNode.getKey())) {
        return;
    }
    const parent = listItemNode.getParent();
    // We can cast both of the below `isNestedListNode` only returns a boolean type instead of a user-defined type guards
    const nextSibling = listItemNode.getNextSibling();
    const previousSibling = listItemNode.getPreviousSibling();
    // if there are nested lists on either side, merge them all together.
    if (isNestedListNode(nextSibling) && isNestedListNode(previousSibling)) {
        const innerList = previousSibling.getFirstChild();
        if ($isListNode(innerList)) {
            innerList.append(listItemNode);
            const nextInnerList = nextSibling.getFirstChild();
            if ($isListNode(nextInnerList)) {
                const children = nextInnerList.getChildren();
                append(innerList, children);
                nextSibling.remove();
                removed.add(nextSibling.getKey());
            }
        }
    } else if (isNestedListNode(nextSibling)) {
        // if the ListItemNode is next to a nested ListNode, merge them
        const innerList = nextSibling.getFirstChild();
        if ($isListNode(innerList)) {
            const firstChild = innerList.getFirstChild();
            if (firstChild !== null) {
                firstChild.insertBefore(listItemNode);
            }
        }
    } else if (isNestedListNode(previousSibling)) {
        const innerList = previousSibling.getFirstChild();
        if ($isListNode(innerList)) {
            innerList.append(listItemNode);
        }
    } else {
        // otherwise, we need to create a new nested ListNode
        if ($isListNode(parent)) {
            const newListItem = $createListItemNode().setTextFormat(listItemNode.getTextFormat()).setTextStyle(listItemNode.getTextStyle());
            const newList = $createListNode(parent.getListType()).setTextFormat(parent.getTextFormat()).setTextStyle(parent.getTextStyle());
            newListItem.append(newList);
            newList.append(listItemNode);
            if (previousSibling) {
                previousSibling.insertAfter(newListItem);
            } else if (nextSibling) {
                nextSibling.insertBefore(newListItem);
            } else {
                parent.append(newListItem);
            }
        }
    }
}
/**
 * Removes an indent by removing an empty ListNode/ListItemNode chain. An indented ListItemNode
 * has a great grandparent node of type ListNode, which is where the ListItemNode will reside
 * within as a child.
 * @param listItemNode - The ListItemNode to remove the indent (outdent).
 */ function $handleOutdent(listItemNode) {
    // go through each node and decide where to move it.
    if (isNestedListNode(listItemNode)) {
        return;
    }
    const parentList = listItemNode.getParent();
    const grandparentListItem = parentList ? parentList.getParent() : undefined;
    const greatGrandparentList = grandparentListItem ? grandparentListItem.getParent() : undefined;
    // If it doesn't have these ancestors, it's not indented.
    if ($isListNode(greatGrandparentList) && $isListItemNode(grandparentListItem) && $isListNode(parentList)) {
        // if it's the first child in it's parent list, insert it into the
        // great grandparent list before the grandparent
        const firstChild = parentList ? parentList.getFirstChild() : undefined;
        const lastChild = parentList ? parentList.getLastChild() : undefined;
        if (listItemNode.is(firstChild)) {
            grandparentListItem.insertBefore(listItemNode);
            if (parentList.isEmpty()) {
                grandparentListItem.remove();
            }
        // if it's the last child in it's parent list, insert it into the
        // great grandparent list after the grandparent.
        } else if (listItemNode.is(lastChild)) {
            grandparentListItem.insertAfter(listItemNode);
            if (parentList.isEmpty()) {
                grandparentListItem.remove();
            }
        } else {
            // otherwise, we need to split the siblings into two new nested lists
            const listType = parentList.getListType();
            const previousSiblingsListItem = $createListItemNode();
            const previousSiblingsList = $createListNode(listType);
            previousSiblingsListItem.append(previousSiblingsList);
            listItemNode.getPreviousSiblings().forEach((sibling)=>previousSiblingsList.append(sibling));
            const nextSiblingsListItem = $createListItemNode();
            const nextSiblingsList = $createListNode(listType);
            nextSiblingsListItem.append(nextSiblingsList);
            append(nextSiblingsList, listItemNode.getNextSiblings());
            // put the sibling nested lists on either side of the grandparent list item in the great grandparent.
            grandparentListItem.insertBefore(previousSiblingsListItem);
            grandparentListItem.insertAfter(nextSiblingsListItem);
            // replace the grandparent list item (now between the siblings) with the outdented list item.
            grandparentListItem.replace(listItemNode);
        }
    }
}
/**
 * Attempts to insert a ParagraphNode at selection and selects the new node. The selection must contain a ListItemNode
 * or a node that does not already contain text. If its grandparent is the root/shadow root, it will get the ListNode
 * (which should be the parent node) and insert the ParagraphNode as a sibling to the ListNode. If the ListNode is
 * nested in a ListItemNode instead, it will add the ParagraphNode after the grandparent ListItemNode.
 * Throws an invariant if the selection is not a child of a ListNode.
 * @returns true if a ParagraphNode was inserted successfully, false if there is no selection
 * or the selection does not contain a ListItemNode or the node already holds text.
 */ function $handleListInsertParagraph() {
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) || !selection.isCollapsed()) {
        return false;
    }
    // Only run this code on empty list items
    const anchor = selection.anchor.getNode();
    if (!$isListItemNode(anchor) || anchor.getChildrenSize() !== 0) {
        return false;
    }
    const topListNode = $getTopListNode(anchor);
    const parent = anchor.getParent();
    if (!$isListNode(parent)) {
        formatDevErrorMessage(`A ListItemNode must have a ListNode for a parent.`);
    }
    const grandparent = parent.getParent();
    let replacementNode;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootOrShadowRoot"])(grandparent)) {
        replacementNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])();
        topListNode.insertAfter(replacementNode);
    } else if ($isListItemNode(grandparent)) {
        replacementNode = $createListItemNode();
        grandparent.insertAfter(replacementNode);
    } else {
        return false;
    }
    replacementNode.setTextStyle(selection.style).setTextFormat(selection.format).select();
    const nextSiblings = anchor.getNextSiblings();
    if (nextSiblings.length > 0) {
        const newList = $createListNode(parent.getListType());
        if ($isListItemNode(replacementNode)) {
            const newListItem = $createListItemNode();
            newListItem.append(newList);
            replacementNode.insertAfter(newListItem);
        } else {
            replacementNode.insertAfter(newList);
        }
        newList.append(...nextSiblings);
    }
    // Don't leave hanging nested empty lists
    $removeHighestEmptyListParent(anchor);
    return true;
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function normalizeClassNames(...classNames) {
    const rval = [];
    for (const className of classNames){
        if (className && typeof className === 'string') {
            for (const [s] of className.matchAll(/\S+/g)){
                rval.push(s);
            }
        }
    }
    return rval;
}
function applyMarkerStyles(dom, node, prevNode) {
    const styles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getStyleObjectFromCSS"])(node.__textStyle);
    for(const k in styles){
        dom.style.setProperty(`--listitem-marker-${k}`, styles[k]);
    }
    if (prevNode) {
        for(const k in (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$selection$2f$LexicalSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getStyleObjectFromCSS"])(prevNode.__textStyle)){
            if (!(k in styles)) {
                dom.style.removeProperty(`--listitem-marker-${k}`);
            }
        }
    }
}
/** @noInheritDoc */ class ListItemNode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementNode"] {
    /** @internal */ /** @internal */ /** @internal */ $config() {
        return this.config('listitem', {
            $transform: (node)=>{
                if (node.__checked == null) {
                    return;
                }
                const parent = node.getParent();
                if ($isListNode(parent)) {
                    if (parent.getListType() !== 'check' && node.getChecked() != null) {
                        node.setChecked(undefined);
                    }
                }
            },
            extends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementNode"],
            importDOM: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildImportMap"])({
                li: ()=>({
                        conversion: $convertListItemElement,
                        priority: 0
                    })
            })
        });
    }
    constructor(value = 1, checked = undefined, key){
        super(key);
        this.__value = value === undefined ? 1 : value;
        this.__checked = checked;
    }
    afterCloneFrom(prevNode) {
        super.afterCloneFrom(prevNode);
        this.__value = prevNode.__value;
        this.__checked = prevNode.__checked;
    }
    createDOM(config) {
        const element = document.createElement('li');
        this.updateListItemDOM(null, element, config);
        return element;
    }
    updateListItemDOM(prevNode, dom, config) {
        const parent = this.getParent();
        if ($isListNode(parent) && parent.getListType() === 'check') {
            updateListItemChecked(dom, this, prevNode);
        }
        dom.value = this.__value;
        $setListItemThemeClassNames(dom, config.theme, this);
        const prevStyle = prevNode ? prevNode.__style : '';
        const nextStyle = this.__style;
        if (prevStyle !== nextStyle) {
            if (nextStyle === '') {
                dom.removeAttribute('style');
            } else {
                dom.style.cssText = nextStyle;
            }
        }
        applyMarkerStyles(dom, this, prevNode);
    }
    updateDOM(prevNode, dom, config) {
        // @ts-expect-error - this is always HTMLListItemElement
        const element = dom;
        this.updateListItemDOM(prevNode, element, config);
        return false;
    }
    updateFromJSON(serializedNode) {
        return super.updateFromJSON(serializedNode).setValue(serializedNode.value).setChecked(serializedNode.checked);
    }
    exportDOM(editor) {
        const element = this.createDOM(editor._config);
        const formatType = this.getFormatType();
        if (formatType) {
            element.style.textAlign = formatType;
        }
        const direction = this.getDirection();
        if (direction) {
            element.dir = direction;
        }
        return {
            element
        };
    }
    exportJSON() {
        return {
            ...super.exportJSON(),
            checked: this.getChecked(),
            value: this.getValue()
        };
    }
    append(...nodes) {
        for(let i = 0; i < nodes.length; i++){
            const node = nodes[i];
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node) && this.canMergeWith(node)) {
                const children = node.getChildren();
                this.append(...children);
                node.remove();
            } else {
                super.append(node);
            }
        }
        return this;
    }
    replace(replaceWithNode, includeChildren) {
        if ($isListItemNode(replaceWithNode)) {
            return super.replace(replaceWithNode);
        }
        this.setIndent(0);
        const list = this.getParentOrThrow();
        if (!$isListNode(list)) {
            return replaceWithNode;
        }
        if (list.__first === this.getKey()) {
            list.insertBefore(replaceWithNode);
        } else if (list.__last === this.getKey()) {
            list.insertAfter(replaceWithNode);
        } else {
            // Split the list
            const newList = $createListNode(list.getListType());
            let nextSibling = this.getNextSibling();
            while(nextSibling){
                const nodeToAppend = nextSibling;
                nextSibling = nextSibling.getNextSibling();
                newList.append(nodeToAppend);
            }
            list.insertAfter(replaceWithNode);
            replaceWithNode.insertAfter(newList);
        }
        if (includeChildren) {
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(replaceWithNode)) {
                formatDevErrorMessage(`includeChildren should only be true for ElementNodes`);
            }
            this.getChildren().forEach((child)=>{
                replaceWithNode.append(child);
            });
        }
        this.remove();
        if (list.getChildrenSize() === 0) {
            list.remove();
        }
        return replaceWithNode;
    }
    insertAfter(node, restoreSelection = true) {
        const listNode = this.getParentOrThrow();
        if (!$isListNode(listNode)) {
            {
                formatDevErrorMessage(`insertAfter: list node is not parent of list item node`);
            }
        }
        if ($isListItemNode(node)) {
            return super.insertAfter(node, restoreSelection);
        }
        const siblings = this.getNextSiblings();
        // Split the lists and insert the node in between them
        listNode.insertAfter(node, restoreSelection);
        if (siblings.length !== 0) {
            const newListNode = $createListNode(listNode.getListType());
            siblings.forEach((sibling)=>newListNode.append(sibling));
            node.insertAfter(newListNode, restoreSelection);
        }
        return node;
    }
    remove(preserveEmptyParent) {
        const prevSibling = this.getPreviousSibling();
        const nextSibling = this.getNextSibling();
        super.remove(preserveEmptyParent);
        if (prevSibling && nextSibling && isNestedListNode(prevSibling) && isNestedListNode(nextSibling)) {
            mergeLists(prevSibling.getFirstChild(), nextSibling.getFirstChild());
            nextSibling.remove();
        }
    }
    insertNewAfter(_, restoreSelection = true) {
        const newElement = $createListItemNode().updateFromJSON(this.exportJSON()).setChecked(this.getChecked() ? false : undefined);
        this.insertAfter(newElement, restoreSelection);
        return newElement;
    }
    collapseAtStart(selection) {
        const paragraph = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])();
        const children = this.getChildren();
        children.forEach((child)=>paragraph.append(child));
        const listNode = this.getParentOrThrow();
        const listNodeParent = listNode.getParentOrThrow();
        const isIndented = $isListItemNode(listNodeParent);
        if (listNode.getChildrenSize() === 1) {
            if (isIndented) {
                // if the list node is nested, we just want to remove it,
                // effectively unindenting it.
                listNode.remove();
                listNodeParent.select();
            } else {
                listNode.insertBefore(paragraph);
                listNode.remove();
                // If we have selection on the list item, we'll need to move it
                // to the paragraph
                const anchor = selection.anchor;
                const focus = selection.focus;
                const key = paragraph.getKey();
                if (anchor.type === 'element' && anchor.getNode().is(this)) {
                    anchor.set(key, anchor.offset, 'element');
                }
                if (focus.type === 'element' && focus.getNode().is(this)) {
                    focus.set(key, focus.offset, 'element');
                }
            }
        } else {
            listNode.insertBefore(paragraph);
            this.remove();
        }
        return true;
    }
    getValue() {
        const self = this.getLatest();
        return self.__value;
    }
    setValue(value) {
        const self = this.getWritable();
        self.__value = value;
        return self;
    }
    getChecked() {
        const self = this.getLatest();
        let listType;
        const parent = this.getParent();
        if ($isListNode(parent)) {
            listType = parent.getListType();
        }
        return listType === 'check' ? Boolean(self.__checked) : undefined;
    }
    setChecked(checked) {
        const self = this.getWritable();
        self.__checked = checked;
        return self;
    }
    toggleChecked() {
        const self = this.getWritable();
        return self.setChecked(!self.__checked);
    }
    getIndent() {
        // If we don't have a parent, we are likely serializing
        const parent = this.getParent();
        if (parent === null || !this.isAttached()) {
            return this.getLatest().__indent;
        }
        // ListItemNode should always have a ListNode for a parent.
        let listNodeParent = parent.getParentOrThrow();
        let indentLevel = 0;
        while($isListItemNode(listNodeParent)){
            listNodeParent = listNodeParent.getParentOrThrow().getParentOrThrow();
            indentLevel++;
        }
        return indentLevel;
    }
    setIndent(indent) {
        if (!(typeof indent === 'number')) {
            formatDevErrorMessage(`Invalid indent value.`);
        }
        indent = Math.floor(indent);
        if (!(indent >= 0)) {
            formatDevErrorMessage(`Indent value must be non-negative.`);
        }
        let currentIndent = this.getIndent();
        while(currentIndent !== indent){
            if (currentIndent < indent) {
                $handleIndent(this);
                currentIndent++;
            } else {
                $handleOutdent(this);
                currentIndent--;
            }
        }
        return this;
    }
    /** @deprecated @internal */ canInsertAfter(node) {
        return $isListItemNode(node);
    }
    /** @deprecated @internal */ canReplaceWith(replacement) {
        return $isListItemNode(replacement);
    }
    canMergeWith(node) {
        return $isListItemNode(node) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isParagraphNode"])(node);
    }
    extractWithChild(child, selection) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        const anchorNode = selection.anchor.getNode();
        const focusNode = selection.focus.getNode();
        return this.isParentOf(anchorNode) && this.isParentOf(focusNode) && this.getTextContent().length === selection.getTextContent().length;
    }
    isParentRequired() {
        return true;
    }
    createParentElementNode() {
        return $createListNode('bullet');
    }
    canMergeWhenEmpty() {
        return true;
    }
}
function $setListItemThemeClassNames(dom, editorThemeClasses, node) {
    const classesToAdd = [];
    const classesToRemove = [];
    const listTheme = editorThemeClasses.list;
    const listItemClassName = listTheme ? listTheme.listitem : undefined;
    let nestedListItemClassName;
    if (listTheme && listTheme.nested) {
        nestedListItemClassName = listTheme.nested.listitem;
    }
    if (listItemClassName !== undefined) {
        classesToAdd.push(...normalizeClassNames(listItemClassName));
    }
    if (listTheme) {
        const parentNode = node.getParent();
        const isCheckList = $isListNode(parentNode) && parentNode.getListType() === 'check';
        const checked = node.getChecked();
        if (!isCheckList || checked) {
            classesToRemove.push(listTheme.listitemUnchecked);
        }
        if (!isCheckList || !checked) {
            classesToRemove.push(listTheme.listitemChecked);
        }
        if (isCheckList) {
            classesToAdd.push(checked ? listTheme.listitemChecked : listTheme.listitemUnchecked);
        }
    }
    if (nestedListItemClassName !== undefined) {
        const nestedListItemClasses = normalizeClassNames(nestedListItemClassName);
        if (node.getChildren().some((child)=>$isListNode(child))) {
            classesToAdd.push(...nestedListItemClasses);
        } else {
            classesToRemove.push(...nestedListItemClasses);
        }
    }
    if (classesToRemove.length > 0) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["removeClassNamesFromElement"])(dom, ...classesToRemove);
    }
    if (classesToAdd.length > 0) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addClassNamesToElement"])(dom, ...classesToAdd);
    }
}
function updateListItemChecked(dom, listItemNode, prevListItemNode, listNode) {
    // Only add attributes for leaf list items
    if ($isListNode(listItemNode.getFirstChild())) {
        dom.removeAttribute('role');
        dom.removeAttribute('tabIndex');
        dom.removeAttribute('aria-checked');
    } else {
        dom.setAttribute('role', 'checkbox');
        dom.setAttribute('tabIndex', '-1');
        if (!prevListItemNode || listItemNode.__checked !== prevListItemNode.__checked) {
            dom.setAttribute('aria-checked', listItemNode.getChecked() ? 'true' : 'false');
        }
    }
}
function $convertListItemElement(domNode) {
    const isGitHubCheckList = domNode.classList.contains('task-list-item');
    if (isGitHubCheckList) {
        for (const child of domNode.children){
            if (child.tagName === 'INPUT') {
                return $convertCheckboxInput(child);
            }
        }
    }
    const ariaCheckedAttr = domNode.getAttribute('aria-checked');
    const checked = ariaCheckedAttr === 'true' ? true : ariaCheckedAttr === 'false' ? false : undefined;
    return {
        node: $createListItemNode(checked)
    };
}
function $convertCheckboxInput(domNode) {
    const isCheckboxInput = domNode.getAttribute('type') === 'checkbox';
    if (!isCheckboxInput) {
        return {
            node: null
        };
    }
    const checked = domNode.hasAttribute('checked');
    return {
        node: $createListItemNode(checked)
    };
}
/**
 * Creates a new List Item node, passing true/false will convert it to a checkbox input.
 * @param checked - Is the List Item a checkbox and, if so, is it checked? undefined/null: not a checkbox, true/false is a checkbox and checked/unchecked, respectively.
 * @returns The new List Item.
 */ function $createListItemNode(checked) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$applyNodeReplacement"])(new ListItemNode(undefined, checked));
}
/**
 * Checks to see if the node is a ListItemNode.
 * @param node - The node to be checked.
 * @returns true if the node is a ListItemNode, false otherwise.
 */ function $isListItemNode(node) {
    return node instanceof ListItemNode;
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /** @noInheritDoc */ class ListNode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementNode"] {
    /** @internal */ /** @internal */ /** @internal */ /** @internal */ $config() {
        return this.config('list', {
            $transform: (node)=>{
                mergeNextSiblingListIfSameType(node);
                updateChildrenListItemValue(node);
            },
            extends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementNode"],
            importDOM: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildImportMap"])({
                ol: ()=>({
                        conversion: $convertListNode,
                        priority: 0
                    }),
                ul: ()=>({
                        conversion: $convertListNode,
                        priority: 0
                    })
            })
        });
    }
    constructor(listType = 'number', start = 1, key){
        super(key);
        const _listType = TAG_TO_LIST_TYPE[listType] || listType;
        this.__listType = _listType;
        this.__tag = _listType === 'number' ? 'ol' : 'ul';
        this.__start = start;
    }
    afterCloneFrom(prevNode) {
        super.afterCloneFrom(prevNode);
        this.__listType = prevNode.__listType;
        this.__tag = prevNode.__tag;
        this.__start = prevNode.__start;
    }
    getTag() {
        return this.getLatest().__tag;
    }
    setListType(type) {
        const writable = this.getWritable();
        writable.__listType = type;
        writable.__tag = type === 'number' ? 'ol' : 'ul';
        return writable;
    }
    getListType() {
        return this.getLatest().__listType;
    }
    getStart() {
        return this.getLatest().__start;
    }
    setStart(start) {
        const self = this.getWritable();
        self.__start = start;
        return self;
    }
    // View
    createDOM(config, _editor) {
        const tag = this.__tag;
        const dom = document.createElement(tag);
        if (this.__start !== 1) {
            dom.setAttribute('start', String(this.__start));
        }
        // @ts-expect-error Internal field.
        dom.__lexicalListType = this.__listType;
        $setListThemeClassNames(dom, config.theme, this);
        return dom;
    }
    updateDOM(prevNode, dom, config) {
        if (prevNode.__tag !== this.__tag) {
            return true;
        }
        $setListThemeClassNames(dom, config.theme, this);
        return false;
    }
    updateFromJSON(serializedNode) {
        return super.updateFromJSON(serializedNode).setListType(serializedNode.listType).setStart(serializedNode.start);
    }
    exportDOM(editor) {
        const element = this.createDOM(editor._config, editor);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLElement"])(element)) {
            if (this.__start !== 1) {
                element.setAttribute('start', String(this.__start));
            }
            if (this.__listType === 'check') {
                element.setAttribute('__lexicalListType', 'check');
            }
        }
        return {
            element
        };
    }
    exportJSON() {
        return {
            ...super.exportJSON(),
            listType: this.getListType(),
            start: this.getStart(),
            tag: this.getTag()
        };
    }
    canBeEmpty() {
        return false;
    }
    canIndent() {
        return false;
    }
    splice(start, deleteCount, nodesToInsert) {
        let listItemNodesToInsert = nodesToInsert;
        for(let i = 0; i < nodesToInsert.length; i++){
            const node = nodesToInsert[i];
            if (!$isListItemNode(node)) {
                if (listItemNodesToInsert === nodesToInsert) {
                    listItemNodesToInsert = [
                        ...nodesToInsert
                    ];
                }
                listItemNodesToInsert[i] = $createListItemNode().append((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node) && !($isListNode(node) || node.isInline()) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(node.getTextContent()) : node);
            }
        }
        return super.splice(start, deleteCount, listItemNodesToInsert);
    }
    extractWithChild(child) {
        return $isListItemNode(child);
    }
}
function $setListThemeClassNames(dom, editorThemeClasses, node) {
    const classesToAdd = [];
    const classesToRemove = [];
    const listTheme = editorThemeClasses.list;
    if (listTheme !== undefined) {
        const listLevelsClassNames = listTheme[`${node.__tag}Depth`] || [];
        const listDepth = $getListDepth(node) - 1;
        const normalizedListDepth = listDepth % listLevelsClassNames.length;
        const listLevelClassName = listLevelsClassNames[normalizedListDepth];
        const listClassName = listTheme[node.__tag];
        let nestedListClassName;
        const nestedListTheme = listTheme.nested;
        const checklistClassName = listTheme.checklist;
        if (nestedListTheme !== undefined && nestedListTheme.list) {
            nestedListClassName = nestedListTheme.list;
        }
        if (listClassName !== undefined) {
            classesToAdd.push(listClassName);
        }
        if (checklistClassName !== undefined && node.__listType === 'check') {
            classesToAdd.push(checklistClassName);
        }
        if (listLevelClassName !== undefined) {
            classesToAdd.push(...normalizeClassNames(listLevelClassName));
            for(let i = 0; i < listLevelsClassNames.length; i++){
                if (i !== normalizedListDepth) {
                    classesToRemove.push(node.__tag + i);
                }
            }
        }
        if (nestedListClassName !== undefined) {
            const nestedListItemClasses = normalizeClassNames(nestedListClassName);
            if (listDepth > 1) {
                classesToAdd.push(...nestedListItemClasses);
            } else {
                classesToRemove.push(...nestedListItemClasses);
            }
        }
    }
    if (classesToRemove.length > 0) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["removeClassNamesFromElement"])(dom, ...classesToRemove);
    }
    if (classesToAdd.length > 0) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addClassNamesToElement"])(dom, ...classesToAdd);
    }
}
/*
 * This function normalizes the children of a ListNode after the conversion from HTML,
 * ensuring that they are all ListItemNodes and contain either a single nested ListNode
 * or some other inline content.
 */ function $normalizeChildren(nodes) {
    const normalizedListItems = [];
    for(let i = 0; i < nodes.length; i++){
        const node = nodes[i];
        if ($isListItemNode(node)) {
            normalizedListItems.push(node);
            const children = node.getChildren();
            if (children.length > 1) {
                children.forEach((child)=>{
                    if ($isListNode(child)) {
                        normalizedListItems.push($wrapInListItem(child));
                    }
                });
            }
        } else {
            normalizedListItems.push($wrapInListItem(node));
        }
    }
    return normalizedListItems;
}
function isDomChecklist(domNode) {
    if (domNode.getAttribute('__lexicallisttype') === 'check' || // is github checklist
    domNode.classList.contains('contains-task-list')) {
        return true;
    }
    // if children are checklist items, the node is a checklist ul. Applicable for googledoc checklist pasting.
    for (const child of domNode.childNodes){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLElement"])(child) && child.hasAttribute('aria-checked')) {
            return true;
        }
    }
    return false;
}
function $convertListNode(domNode) {
    const nodeName = domNode.nodeName.toLowerCase();
    let node = null;
    if (nodeName === 'ol') {
        // @ts-ignore
        const start = domNode.start;
        node = $createListNode('number', start);
    } else if (nodeName === 'ul') {
        if (isDomChecklist(domNode)) {
            node = $createListNode('check');
        } else {
            node = $createListNode('bullet');
        }
    }
    return {
        after: $normalizeChildren,
        node
    };
}
const TAG_TO_LIST_TYPE = {
    ol: 'number',
    ul: 'bullet'
};
/**
 * Creates a ListNode of listType.
 * @param listType - The type of list to be created. Can be 'number', 'bullet', or 'check'.
 * @param start - Where an ordered list starts its count, start = 1 if left undefined.
 * @returns The new ListNode
 */ function $createListNode(listType = 'number', start = 1) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$applyNodeReplacement"])(new ListNode(listType, start));
}
/**
 * Checks to see if the node is a ListNode.
 * @param node - The node to be checked.
 * @returns true if the node is a ListNode, false otherwise.
 */ function $isListNode(node) {
    return node instanceof ListNode;
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const INSERT_CHECK_LIST_COMMAND = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createCommand"])('INSERT_CHECK_LIST_COMMAND');
function registerCheckList(editor) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])(editor.registerCommand(INSERT_CHECK_LIST_COMMAND, ()=>{
        $insertList('check');
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_DOWN_COMMAND"], (event)=>{
        return handleArrowUpOrDown(event, editor, false);
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_UP_COMMAND"], (event)=>{
        return handleArrowUpOrDown(event, editor, true);
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ESCAPE_COMMAND"], ()=>{
        const activeItem = getActiveCheckListItem();
        if (activeItem != null) {
            const rootElement = editor.getRootElement();
            if (rootElement != null) {
                rootElement.focus();
            }
            return true;
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_SPACE_COMMAND"], (event)=>{
        const activeItem = getActiveCheckListItem();
        if (activeItem != null && editor.isEditable()) {
            editor.update(()=>{
                const listItemNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNearestNodeFromDOMNode"])(activeItem);
                if ($isListItemNode(listItemNode)) {
                    event.preventDefault();
                    listItemNode.toggleChecked();
                }
            });
            return true;
        }
        return false;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_LEFT_COMMAND"], (event)=>{
        return editor.getEditorState().read(()=>{
            const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) && selection.isCollapsed()) {
                const { anchor } = selection;
                const isElement = anchor.type === 'element';
                if (isElement || anchor.offset === 0) {
                    const anchorNode = anchor.getNode();
                    const elementNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$findMatchingParent"])(anchorNode, (node)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node) && !node.isInline());
                    if ($isListItemNode(elementNode)) {
                        const parent = elementNode.getParent();
                        if ($isListNode(parent) && parent.getListType() === 'check' && (isElement || elementNode.getFirstDescendant() === anchorNode)) {
                            const domNode = editor.getElementByKey(elementNode.__key);
                            if (domNode != null && document.activeElement !== domNode) {
                                domNode.focus();
                                event.preventDefault();
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        });
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerRootListener((rootElement, prevElement)=>{
        if (rootElement !== null) {
            rootElement.addEventListener('click', handleClick);
            rootElement.addEventListener('pointerdown', handlePointerDown);
        }
        if (prevElement !== null) {
            prevElement.removeEventListener('click', handleClick);
            prevElement.removeEventListener('pointerdown', handlePointerDown);
        }
    }));
}
function handleCheckItemEvent(event, callback) {
    const target = event.target;
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLElement"])(target)) {
        return;
    }
    // Ignore clicks on LI that have nested lists
    const firstChild = target.firstChild;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLElement"])(firstChild) && (firstChild.tagName === 'UL' || firstChild.tagName === 'OL')) {
        return;
    }
    const parentNode = target.parentNode;
    // @ts-ignore internal field
    if (!parentNode || parentNode.__lexicalListType !== 'check') {
        return;
    }
    const rect = target.getBoundingClientRect();
    const zoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["calculateZoomLevel"])(target);
    const clientX = event.clientX / zoom;
    // Use getComputedStyle if available, otherwise fallback to 0px width
    const beforeStyles = window.getComputedStyle ? window.getComputedStyle(target, '::before') : {
        width: '0px'
    };
    const beforeWidthInPixels = parseFloat(beforeStyles.width);
    // Make click area slightly larger for touch devices to improve accessibility
    const isTouchEvent = event.pointerType === 'touch';
    const clickAreaPadding = isTouchEvent ? 32 : 0; // Add 32px padding for touch events
    if (target.dir === 'rtl' ? clientX < rect.right + clickAreaPadding && clientX > rect.right - beforeWidthInPixels - clickAreaPadding : clientX > rect.left - clickAreaPadding && clientX < rect.left + beforeWidthInPixels + clickAreaPadding) {
        callback();
    }
}
function handleClick(event) {
    handleCheckItemEvent(event, ()=>{
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLElement"])(event.target)) {
            const domNode = event.target;
            const editor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNearestEditorFromDOMNode"])(domNode);
            if (editor != null && editor.isEditable()) {
                editor.update(()=>{
                    const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNearestNodeFromDOMNode"])(domNode);
                    if ($isListItemNode(node)) {
                        domNode.focus();
                        node.toggleChecked();
                    }
                });
            }
        }
    });
}
function handlePointerDown(event) {
    handleCheckItemEvent(event, ()=>{
        // Prevents caret moving when clicking on check mark
        event.preventDefault();
    });
}
function getActiveCheckListItem() {
    const activeElement = document.activeElement;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLElement"])(activeElement) && activeElement.tagName === 'LI' && activeElement.parentNode != null && // @ts-ignore internal field
    activeElement.parentNode.__lexicalListType === 'check' ? activeElement : null;
}
function findCheckListItemSibling(node, backward) {
    let sibling = backward ? node.getPreviousSibling() : node.getNextSibling();
    let parent = node;
    // Going up in a tree to get non-null sibling
    while(sibling == null && $isListItemNode(parent)){
        // Get li -> parent ul/ol -> parent li
        parent = parent.getParentOrThrow().getParent();
        if (parent != null) {
            sibling = backward ? parent.getPreviousSibling() : parent.getNextSibling();
        }
    }
    // Going down in a tree to get first non-nested list item
    while($isListItemNode(sibling)){
        const firstChild = backward ? sibling.getLastChild() : sibling.getFirstChild();
        if (!$isListNode(firstChild)) {
            return sibling;
        }
        sibling = backward ? firstChild.getLastChild() : firstChild.getFirstChild();
    }
    return null;
}
function handleArrowUpOrDown(event, editor, backward) {
    const activeItem = getActiveCheckListItem();
    if (activeItem != null) {
        editor.update(()=>{
            const listItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNearestNodeFromDOMNode"])(activeItem);
            if (!$isListItemNode(listItem)) {
                return;
            }
            const nextListItem = findCheckListItemSibling(listItem, backward);
            if (nextListItem != null) {
                nextListItem.selectStart();
                const dom = editor.getElementByKey(nextListItem.__key);
                if (dom != null) {
                    event.preventDefault();
                    setTimeout(()=>{
                        dom.focus();
                    }, 0);
                }
            }
        });
    }
    return false;
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const UPDATE_LIST_START_COMMAND = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createCommand"])('UPDATE_LIST_START_COMMAND');
const INSERT_UNORDERED_LIST_COMMAND = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createCommand"])('INSERT_UNORDERED_LIST_COMMAND');
const INSERT_ORDERED_LIST_COMMAND = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createCommand"])('INSERT_ORDERED_LIST_COMMAND');
const REMOVE_LIST_COMMAND = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createCommand"])('REMOVE_LIST_COMMAND');
function registerList(editor) {
    const removeListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])(editor.registerCommand(INSERT_ORDERED_LIST_COMMAND, ()=>{
        $insertList('number');
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(UPDATE_LIST_START_COMMAND, (payload)=>{
        const { listNodeKey, newStart } = payload;
        const listNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNodeByKey"])(listNodeKey);
        if (!$isListNode(listNode)) {
            return false;
        }
        if (listNode.getListType() === 'number') {
            listNode.setStart(newStart);
            updateChildrenListItemValue(listNode);
        }
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(INSERT_UNORDERED_LIST_COMMAND, ()=>{
        $insertList('bullet');
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(REMOVE_LIST_COMMAND, ()=>{
        $removeList();
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_PARAGRAPH_COMMAND"], ()=>$handleListInsertParagraph(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerNodeTransform(ListItemNode, (node)=>{
        const firstChild = node.getFirstChild();
        if (firstChild) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(firstChild)) {
                const style = firstChild.getStyle();
                const format = firstChild.getFormat();
                if (node.getTextStyle() !== style) {
                    node.setTextStyle(style);
                }
                if (node.getTextFormat() !== format) {
                    node.setTextFormat(format);
                }
            }
        } else {
            // If it's empty, check the selection
            const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) && (selection.style !== node.getTextStyle() || selection.format !== node.getTextFormat()) && selection.isCollapsed() && node.is(selection.anchor.getNode())) {
                node.setTextStyle(selection.style).setTextFormat(selection.format);
            }
        }
    }), editor.registerNodeTransform(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextNode"], (node)=>{
        const listItemParentNode = node.getParent();
        if ($isListItemNode(listItemParentNode) && node.is(listItemParentNode.getFirstChild())) {
            const style = node.getStyle();
            const format = node.getFormat();
            if (style !== listItemParentNode.getTextStyle() || format !== listItemParentNode.getTextFormat()) {
                listItemParentNode.setTextStyle(style).setTextFormat(format);
            }
        }
    }));
    return removeListener;
}
function registerListStrictIndentTransform(editor) {
    const $formatListIndentStrict = (listItemNode)=>{
        const listNode = listItemNode.getParent();
        if ($isListNode(listItemNode.getFirstChild()) || !$isListNode(listNode)) {
            return;
        }
        const startingListItemNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$findMatchingParent"])(listItemNode, (node)=>$isListItemNode(node) && $isListNode(node.getParent()) && $isListItemNode(node.getPreviousSibling()));
        if (startingListItemNode === null && listItemNode.getIndent() > 0) {
            listItemNode.setIndent(0);
        } else if ($isListItemNode(startingListItemNode)) {
            const prevListItemNode = startingListItemNode.getPreviousSibling();
            if ($isListItemNode(prevListItemNode)) {
                const endListItemNode = $findChildrenEndListItemNode(prevListItemNode);
                const endListNode = endListItemNode.getParent();
                if ($isListNode(endListNode)) {
                    const prevDepth = $getListDepth(endListNode);
                    const depth = $getListDepth(listNode);
                    if (prevDepth + 1 < depth) {
                        listItemNode.setIndent(prevDepth);
                    }
                }
            }
        }
    };
    const $processListWithStrictIndent = (listNode)=>{
        const queue = [
            listNode
        ];
        while(queue.length > 0){
            const node = queue.shift();
            if (!$isListNode(node)) {
                continue;
            }
            for (const child of node.getChildren()){
                if ($isListItemNode(child)) {
                    $formatListIndentStrict(child);
                    const firstChild = child.getFirstChild();
                    if ($isListNode(firstChild)) {
                        queue.push(firstChild);
                    }
                }
            }
        }
    };
    return editor.registerNodeTransform(ListNode, $processListWithStrictIndent);
}
function $findChildrenEndListItemNode(listItemNode) {
    let current = listItemNode;
    let firstChild = current.getFirstChild();
    while($isListNode(firstChild)){
        const lastChild = firstChild.getLastChild();
        if ($isListItemNode(lastChild)) {
            current = lastChild;
            firstChild = current.getFirstChild();
        } else {
            break;
        }
    }
    return current;
}
/**
 * @deprecated use {@link $insertList} from an update or command listener.
 *
 * Inserts a new ListNode. If the selection's anchor node is an empty ListItemNode and is a child of
 * the root/shadow root, it will replace the ListItemNode with a ListNode and the old ListItemNode.
 * Otherwise it will replace its parent with a new ListNode and re-insert the ListItemNode and any previous children.
 * If the selection's anchor node is not an empty ListItemNode, it will add a new ListNode or merge an existing ListNode,
 * unless the the node is a leaf node, in which case it will attempt to find a ListNode up the branch and replace it with
 * a new ListNode, or create a new ListNode at the nearest root/shadow root.
 * @param editor - The lexical editor.
 * @param listType - The type of list, "number" | "bullet" | "check".
 */ function insertList(editor, listType) {
    editor.update(()=>$insertList(listType));
}
/**
 * @deprecated use {@link $removeList} from an update or command listener.
 *
 * Searches for the nearest ancestral ListNode and removes it. If selection is an empty ListItemNode
 * it will remove the whole list, including the ListItemNode. For each ListItemNode in the ListNode,
 * removeList will also generate new ParagraphNodes in the removed ListNode's place. Any child node
 * inside a ListItemNode will be appended to the new ParagraphNodes.
 * @param editor - The lexical editor.
 */ function removeList(editor) {
    editor.update(()=>$removeList());
}
;
}}),
"[project]/node_modules/@lexical/code/LexicalCode.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "$createCodeHighlightNode": (()=>$createCodeHighlightNode),
    "$createCodeNode": (()=>$createCodeNode),
    "$getEndOfCodeInLine": (()=>$getEndOfCodeInLine),
    "$getFirstCodeNodeOfLine": (()=>$getFirstCodeNodeOfLine),
    "$getLastCodeNodeOfLine": (()=>$getLastCodeNodeOfLine),
    "$getStartOfCodeInLine": (()=>$getStartOfCodeInLine),
    "$isCodeHighlightNode": (()=>$isCodeHighlightNode),
    "$isCodeNode": (()=>$isCodeNode),
    "CODE_LANGUAGE_FRIENDLY_NAME_MAP": (()=>CODE_LANGUAGE_FRIENDLY_NAME_MAP),
    "CODE_LANGUAGE_MAP": (()=>CODE_LANGUAGE_MAP),
    "CodeHighlightNode": (()=>CodeHighlightNode),
    "CodeNode": (()=>CodeNode),
    "DEFAULT_CODE_LANGUAGE": (()=>DEFAULT_CODE_LANGUAGE),
    "PrismTokenizer": (()=>PrismTokenizer),
    "getCodeLanguageOptions": (()=>getCodeLanguageOptions),
    "getCodeLanguages": (()=>getCodeLanguages),
    "getCodeThemeOptions": (()=>getCodeThemeOptions),
    "getDefaultCodeLanguage": (()=>getDefaultCodeLanguage),
    "getEndOfCodeInLine": (()=>getEndOfCodeInLine),
    "getFirstCodeNodeOfLine": (()=>getFirstCodeNodeOfLine),
    "getLanguageFriendlyName": (()=>getLanguageFriendlyName),
    "getLastCodeNodeOfLine": (()=>getLastCodeNodeOfLine),
    "getStartOfCodeInLine": (()=>getStartOfCodeInLine),
    "normalizeCodeLang": (()=>normalizeCodeLang),
    "normalizeCodeLanguage": (()=>normalizeCodeLang),
    "registerCodeHighlighting": (()=>registerCodeHighlighting)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$prism$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/prism.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$clike$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-clike.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$javascript$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-javascript.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$markup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-markup.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$markdown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-markdown.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$c$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-c.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$css$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-css.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$objectivec$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-objectivec.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$sql$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-sql.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$powershell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-powershell.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$python$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-python.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$rust$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-rust.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$swift$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-swift.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$typescript$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-typescript.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$java$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-java.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$prismjs$2f$components$2f$prism$2d$cpp$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/prismjs/components/prism-cpp.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Do not require this module directly! Use normal `invariant` calls.
function formatDevErrorMessage(message) {
    throw new Error(message);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // invariant(condition, message) will refine types based on "condition", and
// if "condition" is false will throw an error. This function is special-cased
// in flow itself, so we can't name it anything else.
function invariant(cond, message, ...args) {
    if (cond) {
        return;
    }
    throw new Error('Internal Lexical error: invariant() is meant to be replaced at compile ' + 'time. There is no runtime version. Error: ' + message);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const DEFAULT_CODE_LANGUAGE = 'javascript';
const getDefaultCodeLanguage = ()=>DEFAULT_CODE_LANGUAGE;
function hasChildDOMNodeTag(node, tagName) {
    for (const child of node.childNodes){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLElement"])(child) && child.tagName === tagName) {
            return true;
        }
        hasChildDOMNodeTag(child, tagName);
    }
    return false;
}
const LANGUAGE_DATA_ATTRIBUTE = 'data-language';
const HIGHLIGHT_LANGUAGE_DATA_ATTRIBUTE = 'data-highlight-language';
const THEME_DATA_ATTRIBUTE = 'data-theme';
/** @noInheritDoc */ class CodeNode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementNode"] {
    /** @internal */ /** @internal */ /** @internal */ static getType() {
        return 'code';
    }
    static clone(node) {
        return new CodeNode(node.__language, node.__key);
    }
    constructor(language, key){
        super(key);
        this.__language = language || undefined;
        this.__isSyntaxHighlightSupported = false;
        this.__theme = undefined;
    }
    afterCloneFrom(prevNode) {
        super.afterCloneFrom(prevNode);
        this.__language = prevNode.__language;
        this.__theme = prevNode.__theme;
        this.__isSyntaxHighlightSupported = prevNode.__isSyntaxHighlightSupported;
    }
    // View
    createDOM(config) {
        const element = document.createElement('code');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addClassNamesToElement"])(element, config.theme.code);
        element.setAttribute('spellcheck', 'false');
        const language = this.getLanguage();
        if (language) {
            element.setAttribute(LANGUAGE_DATA_ATTRIBUTE, language);
            if (this.getIsSyntaxHighlightSupported()) {
                element.setAttribute(HIGHLIGHT_LANGUAGE_DATA_ATTRIBUTE, language);
            }
        }
        const theme = this.getTheme();
        if (theme) {
            element.setAttribute(THEME_DATA_ATTRIBUTE, theme);
        }
        const style = this.getStyle();
        if (style) {
            element.setAttribute('style', style);
        }
        return element;
    }
    updateDOM(prevNode, dom, config) {
        const language = this.__language;
        const prevLanguage = prevNode.__language;
        if (language) {
            if (language !== prevLanguage) {
                dom.setAttribute(LANGUAGE_DATA_ATTRIBUTE, language);
            }
        } else if (prevLanguage) {
            dom.removeAttribute(LANGUAGE_DATA_ATTRIBUTE);
        }
        const isSyntaxHighlightSupported = this.__isSyntaxHighlightSupported;
        const prevIsSyntaxHighlightSupported = prevNode.__isSyntaxHighlightSupported;
        if (prevIsSyntaxHighlightSupported && prevLanguage) {
            if (isSyntaxHighlightSupported && language) {
                if (language !== prevLanguage) {
                    dom.setAttribute(HIGHLIGHT_LANGUAGE_DATA_ATTRIBUTE, language);
                }
            } else {
                dom.removeAttribute(HIGHLIGHT_LANGUAGE_DATA_ATTRIBUTE);
            }
        } else if (isSyntaxHighlightSupported && language) {
            dom.setAttribute(HIGHLIGHT_LANGUAGE_DATA_ATTRIBUTE, language);
        }
        const theme = this.__theme;
        const prevTheme = prevNode.__theme;
        if (theme) {
            if (theme !== prevTheme) {
                dom.setAttribute(THEME_DATA_ATTRIBUTE, theme);
            }
        } else if (prevTheme) {
            dom.removeAttribute(THEME_DATA_ATTRIBUTE);
        }
        const style = this.__style;
        const prevStyle = prevNode.__style;
        if (style) {
            if (style !== prevStyle) {
                dom.setAttribute('style', style);
            }
        } else if (prevStyle) {
            dom.removeAttribute('style');
        }
        return false;
    }
    exportDOM(editor) {
        const element = document.createElement('pre');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addClassNamesToElement"])(element, editor._config.theme.code);
        element.setAttribute('spellcheck', 'false');
        const language = this.getLanguage();
        if (language) {
            element.setAttribute(LANGUAGE_DATA_ATTRIBUTE, language);
            if (this.getIsSyntaxHighlightSupported()) {
                element.setAttribute(HIGHLIGHT_LANGUAGE_DATA_ATTRIBUTE, language);
            }
        }
        const theme = this.getTheme();
        if (theme) {
            element.setAttribute(THEME_DATA_ATTRIBUTE, theme);
        }
        const style = this.getStyle();
        if (style) {
            element.setAttribute('style', style);
        }
        return {
            element
        };
    }
    static importDOM() {
        return {
            // Typically <pre> is used for code blocks, and <code> for inline code styles
            // but if it's a multi line <code> we'll create a block. Pass through to
            // inline format handled by TextNode otherwise.
            code: (node)=>{
                const isMultiLine = node.textContent != null && (/\r?\n/.test(node.textContent) || hasChildDOMNodeTag(node, 'BR'));
                return isMultiLine ? {
                    conversion: $convertPreElement,
                    priority: 1
                } : null;
            },
            div: ()=>({
                    conversion: $convertDivElement,
                    priority: 1
                }),
            pre: ()=>({
                    conversion: $convertPreElement,
                    priority: 0
                }),
            table: (node)=>{
                const table = node;
                // domNode is a <table> since we matched it by nodeName
                if (isGitHubCodeTable(table)) {
                    return {
                        conversion: $convertTableElement,
                        priority: 3
                    };
                }
                return null;
            },
            td: (node)=>{
                // element is a <td> since we matched it by nodeName
                const td = node;
                const table = td.closest('table');
                if (isGitHubCodeCell(td) || table && isGitHubCodeTable(table)) {
                    // Return a no-op if it's a table cell in a code table, but not a code line.
                    // Otherwise it'll fall back to the T
                    return {
                        conversion: convertCodeNoop,
                        priority: 3
                    };
                }
                return null;
            },
            tr: (node)=>{
                // element is a <tr> since we matched it by nodeName
                const tr = node;
                const table = tr.closest('table');
                if (table && isGitHubCodeTable(table)) {
                    return {
                        conversion: convertCodeNoop,
                        priority: 3
                    };
                }
                return null;
            }
        };
    }
    static importJSON(serializedNode) {
        return $createCodeNode().updateFromJSON(serializedNode);
    }
    updateFromJSON(serializedNode) {
        return super.updateFromJSON(serializedNode).setLanguage(serializedNode.language).setTheme(serializedNode.theme);
    }
    exportJSON() {
        return {
            ...super.exportJSON(),
            language: this.getLanguage(),
            theme: this.getTheme()
        };
    }
    // Mutation
    insertNewAfter(selection, restoreSelection = true) {
        const children = this.getChildren();
        const childrenLength = children.length;
        if (childrenLength >= 2 && children[childrenLength - 1].getTextContent() === '\n' && children[childrenLength - 2].getTextContent() === '\n' && selection.isCollapsed() && selection.anchor.key === this.__key && selection.anchor.offset === childrenLength) {
            children[childrenLength - 1].remove();
            children[childrenLength - 2].remove();
            const newElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])();
            this.insertAfter(newElement, restoreSelection);
            return newElement;
        }
        // If the selection is within the codeblock, find all leading tabs and
        // spaces of the current line. Create a new line that has all those
        // tabs and spaces, such that leading indentation is preserved.
        const { anchor, focus } = selection;
        const firstPoint = anchor.isBefore(focus) ? anchor : focus;
        const firstSelectionNode = firstPoint.getNode();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(firstSelectionNode)) {
            let node = $getFirstCodeNodeOfLine(firstSelectionNode);
            const insertNodes = [];
            // eslint-disable-next-line no-constant-condition
            while(true){
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(node)) {
                    insertNodes.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTabNode"])());
                    node = node.getNextSibling();
                } else if ($isCodeHighlightNode(node)) {
                    let spaces = 0;
                    const text = node.getTextContent();
                    const textSize = node.getTextContentSize();
                    while(spaces < textSize && text[spaces] === ' '){
                        spaces++;
                    }
                    if (spaces !== 0) {
                        insertNodes.push($createCodeHighlightNode(' '.repeat(spaces)));
                    }
                    if (spaces !== textSize) {
                        break;
                    }
                    node = node.getNextSibling();
                } else {
                    break;
                }
            }
            const split = firstSelectionNode.splitText(anchor.offset)[0];
            const x = anchor.offset === 0 ? 0 : 1;
            const index = split.getIndexWithinParent() + x;
            const codeNode = firstSelectionNode.getParentOrThrow();
            const nodesToInsert = [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createLineBreakNode"])(),
                ...insertNodes
            ];
            codeNode.splice(index, 0, nodesToInsert);
            const last = insertNodes[insertNodes.length - 1];
            if (last) {
                last.select();
            } else if (anchor.offset === 0) {
                split.selectPrevious();
            } else {
                split.getNextSibling().selectNext(0, 0);
            }
        }
        if ($isCodeNode(firstSelectionNode)) {
            const { offset } = selection.anchor;
            firstSelectionNode.splice(offset, 0, [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createLineBreakNode"])()
            ]);
            firstSelectionNode.select(offset + 1, offset + 1);
        }
        return null;
    }
    canIndent() {
        return false;
    }
    collapseAtStart() {
        const paragraph = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])();
        const children = this.getChildren();
        children.forEach((child)=>paragraph.append(child));
        this.replace(paragraph);
        return true;
    }
    setLanguage(language) {
        const writable = this.getWritable();
        writable.__language = language || undefined;
        return writable;
    }
    getLanguage() {
        return this.getLatest().__language;
    }
    setIsSyntaxHighlightSupported(isSupported) {
        const writable = this.getWritable();
        writable.__isSyntaxHighlightSupported = isSupported;
        return writable;
    }
    getIsSyntaxHighlightSupported() {
        return this.getLatest().__isSyntaxHighlightSupported;
    }
    setTheme(theme) {
        const writable = this.getWritable();
        writable.__theme = theme || undefined;
        return writable;
    }
    getTheme() {
        return this.getLatest().__theme;
    }
}
function $createCodeNode(language, theme) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$create"])(CodeNode).setLanguage(language).setTheme(theme);
}
function $isCodeNode(node) {
    return node instanceof CodeNode;
}
function $convertPreElement(domNode) {
    const language = domNode.getAttribute(LANGUAGE_DATA_ATTRIBUTE);
    return {
        node: $createCodeNode(language)
    };
}
function $convertDivElement(domNode) {
    // domNode is a <div> since we matched it by nodeName
    const div = domNode;
    const isCode = isCodeElement(div);
    if (!isCode && !isCodeChildElement(div)) {
        return {
            node: null
        };
    }
    return {
        node: isCode ? $createCodeNode() : null
    };
}
function $convertTableElement() {
    return {
        node: $createCodeNode()
    };
}
function convertCodeNoop() {
    return {
        node: null
    };
}
function isCodeElement(div) {
    return div.style.fontFamily.match('monospace') !== null;
}
function isCodeChildElement(node) {
    let parent = node.parentElement;
    while(parent !== null){
        if (isCodeElement(parent)) {
            return true;
        }
        parent = parent.parentElement;
    }
    return false;
}
function isGitHubCodeCell(cell) {
    return cell.classList.contains('js-file-line');
}
function isGitHubCodeTable(table) {
    return table.classList.contains('js-file-line-container');
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /** @noInheritDoc */ class CodeHighlightNode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextNode"] {
    /** @internal */ constructor(text = '', highlightType, key){
        super(text, key);
        this.__highlightType = highlightType;
    }
    static getType() {
        return 'code-highlight';
    }
    static clone(node) {
        return new CodeHighlightNode(node.__text, node.__highlightType || undefined, node.__key);
    }
    getHighlightType() {
        const self = this.getLatest();
        return self.__highlightType;
    }
    setHighlightType(highlightType) {
        const self = this.getWritable();
        self.__highlightType = highlightType || undefined;
        return self;
    }
    canHaveFormat() {
        return false;
    }
    createDOM(config) {
        const element = super.createDOM(config);
        const className = getHighlightThemeClass(config.theme, this.__highlightType);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addClassNamesToElement"])(element, className);
        return element;
    }
    updateDOM(prevNode, dom, config) {
        const update = super.updateDOM(prevNode, dom, config);
        const prevClassName = getHighlightThemeClass(config.theme, prevNode.__highlightType);
        const nextClassName = getHighlightThemeClass(config.theme, this.__highlightType);
        if (prevClassName !== nextClassName) {
            if (prevClassName) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["removeClassNamesFromElement"])(dom, prevClassName);
            }
            if (nextClassName) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addClassNamesToElement"])(dom, nextClassName);
            }
        }
        return update;
    }
    static importJSON(serializedNode) {
        return $createCodeHighlightNode().updateFromJSON(serializedNode);
    }
    updateFromJSON(serializedNode) {
        return super.updateFromJSON(serializedNode).setHighlightType(serializedNode.highlightType);
    }
    exportJSON() {
        return {
            ...super.exportJSON(),
            highlightType: this.getHighlightType()
        };
    }
    // Prevent formatting (bold, underline, etc)
    setFormat(format) {
        return this;
    }
    isParentRequired() {
        return true;
    }
    createParentElementNode() {
        return $createCodeNode();
    }
}
function getHighlightThemeClass(theme, highlightType) {
    return highlightType && theme && theme.codeHighlight && theme.codeHighlight[highlightType];
}
function $createCodeHighlightNode(text = '', highlightType) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$applyNodeReplacement"])(new CodeHighlightNode(text, highlightType));
}
function $isCodeHighlightNode(node) {
    return node instanceof CodeHighlightNode;
}
function $getLastMatchingCodeNode(anchor, direction) {
    let matchingNode = anchor;
    for(let caret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSiblingCaret"])(anchor, direction); caret && ($isCodeHighlightNode(caret.origin) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(caret.origin)); caret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$getAdjacentCaret"])(caret)){
        matchingNode = caret.origin;
    }
    return matchingNode;
}
function $getFirstCodeNodeOfLine(anchor) {
    return $getLastMatchingCodeNode(anchor, 'previous');
}
function $getLastCodeNodeOfLine(anchor) {
    return $getLastMatchingCodeNode(anchor, 'next');
}
function $getStartOfCodeInLine(anchor, offset) {
    let last = null;
    let lastNonBlank = null;
    let node = anchor;
    let nodeOffset = offset;
    let nodeTextContent = anchor.getTextContent();
    // eslint-disable-next-line no-constant-condition
    while(true){
        if (nodeOffset === 0) {
            node = node.getPreviousSibling();
            if (node === null) {
                break;
            }
            if (!($isCodeHighlightNode(node) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(node) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(node))) {
                formatDevErrorMessage(`Expected a valid Code Node: CodeHighlightNode, TabNode, LineBreakNode`);
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(node)) {
                last = {
                    node,
                    offset: 1
                };
                break;
            }
            nodeOffset = Math.max(0, node.getTextContentSize() - 1);
            nodeTextContent = node.getTextContent();
        } else {
            nodeOffset--;
        }
        const character = nodeTextContent[nodeOffset];
        if ($isCodeHighlightNode(node) && character !== ' ') {
            lastNonBlank = {
                node,
                offset: nodeOffset
            };
        }
    }
    // lastNonBlank !== null: anchor in the middle of code; move to line beginning
    if (lastNonBlank !== null) {
        return lastNonBlank;
    }
    // Spaces, tabs or nothing ahead of anchor
    let codeCharacterAtAnchorOffset = null;
    if (offset < anchor.getTextContentSize()) {
        if ($isCodeHighlightNode(anchor)) {
            codeCharacterAtAnchorOffset = anchor.getTextContent()[offset];
        }
    } else {
        const nextSibling = anchor.getNextSibling();
        if ($isCodeHighlightNode(nextSibling)) {
            codeCharacterAtAnchorOffset = nextSibling.getTextContent()[0];
        }
    }
    if (codeCharacterAtAnchorOffset !== null && codeCharacterAtAnchorOffset !== ' ') {
        // Borderline whitespace and code, move to line beginning
        return last;
    } else {
        const nextNonBlank = findNextNonBlankInLine(anchor, offset);
        if (nextNonBlank !== null) {
            return nextNonBlank;
        } else {
            return last;
        }
    }
}
function findNextNonBlankInLine(anchor, offset) {
    let node = anchor;
    let nodeOffset = offset;
    let nodeTextContent = anchor.getTextContent();
    let nodeTextContentSize = anchor.getTextContentSize();
    // eslint-disable-next-line no-constant-condition
    while(true){
        if (!$isCodeHighlightNode(node) || nodeOffset === nodeTextContentSize) {
            node = node.getNextSibling();
            if (node === null || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(node)) {
                return null;
            }
            if ($isCodeHighlightNode(node)) {
                nodeOffset = 0;
                nodeTextContent = node.getTextContent();
                nodeTextContentSize = node.getTextContentSize();
            }
        }
        if ($isCodeHighlightNode(node)) {
            if (nodeTextContent[nodeOffset] !== ' ') {
                return {
                    node,
                    offset: nodeOffset
                };
            }
            nodeOffset++;
        }
    }
}
function $getEndOfCodeInLine(anchor) {
    const lastNode = $getLastCodeNodeOfLine(anchor);
    if (!!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(lastNode)) {
        formatDevErrorMessage(`Unexpected lineBreakNode in getEndOfCodeInLine`);
    }
    return lastNode;
}
(function(Prism1) {
    Prism1.languages.diff = {
        'coord': [
            // Match all kinds of coord lines (prefixed by "+++", "---" or "***").
            /^(?:\*{3}|-{3}|\+{3}).*$/m,
            // Match "@@ ... @@" coord lines in unified diff.
            /^@@.*@@$/m,
            // Match coord lines in normal diff (starts with a number).
            /^\d.*$/m
        ]
    };
    /**
	 * A map from the name of a block to its line prefix.
	 *
	 * @type {Object<string, string>}
	 */ var PREFIXES = {
        'deleted-sign': '-',
        'deleted-arrow': '<',
        'inserted-sign': '+',
        'inserted-arrow': '>',
        'unchanged': ' ',
        'diff': '!'
    };
    // add a token for each prefix
    Object.keys(PREFIXES).forEach(function(name) {
        var prefix = PREFIXES[name];
        var alias = [];
        if (!/^\w+$/.test(name)) {
            alias.push(/\w+/.exec(name)[0]);
        }
        if (name === 'diff') {
            alias.push('bold');
        }
        Prism1.languages.diff[name] = {
            pattern: RegExp('^(?:[' + prefix + '].*(?:\r\n?|\n|(?![\\s\\S])))+', 'm'),
            alias: alias,
            inside: {
                'line': {
                    pattern: /(.)(?=[\s\S]).*(?:\r\n?|\n)?/,
                    lookbehind: true
                },
                'prefix': {
                    pattern: /[\s\S]/,
                    alias: /\w+/.exec(name)[0]
                }
            }
        };
    });
    // make prefixes available to Diff plugin
    Object.defineProperty(Prism1.languages.diff, 'PREFIXES', {
        value: PREFIXES
    });
})(Prism);
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const Prism$1 = globalThis.Prism || window.Prism;
const CODE_LANGUAGE_FRIENDLY_NAME_MAP = {
    c: 'C',
    clike: 'C-like',
    cpp: 'C++',
    css: 'CSS',
    html: 'HTML',
    java: 'Java',
    js: 'JavaScript',
    markdown: 'Markdown',
    objc: 'Objective-C',
    plain: 'Plain Text',
    powershell: 'PowerShell',
    py: 'Python',
    rust: 'Rust',
    sql: 'SQL',
    swift: 'Swift',
    typescript: 'TypeScript',
    xml: 'XML'
};
const CODE_LANGUAGE_MAP = {
    cpp: 'cpp',
    java: 'java',
    javascript: 'js',
    md: 'markdown',
    plaintext: 'plain',
    python: 'py',
    text: 'plain',
    ts: 'typescript'
};
function normalizeCodeLang(lang) {
    return CODE_LANGUAGE_MAP[lang] || lang;
}
function getLanguageFriendlyName(lang) {
    const _lang = normalizeCodeLang(lang);
    return CODE_LANGUAGE_FRIENDLY_NAME_MAP[_lang] || _lang;
}
const getCodeLanguages = ()=>Object.keys(Prism$1.languages).filter(// Prism has several language helpers mixed into languages object
    // so filtering them out here to get langs list
    (language)=>typeof Prism$1.languages[language] !== 'function').sort();
function getCodeLanguageOptions() {
    const options = [];
    for (const [lang, friendlyName] of Object.entries(CODE_LANGUAGE_FRIENDLY_NAME_MAP)){
        options.push([
            lang,
            friendlyName
        ]);
    }
    return options;
}
// Prism has no theme support
function getCodeThemeOptions() {
    const options = [];
    return options;
}
function getDiffedLanguage(language) {
    const DIFF_LANGUAGE_REGEX = /^diff-([\w-]+)/i;
    const diffLanguageMatch = DIFF_LANGUAGE_REGEX.exec(language);
    return diffLanguageMatch ? diffLanguageMatch[1] : null;
}
function isCodeLanguageLoaded(language) {
    const diffedLanguage = getDiffedLanguage(language);
    const langId = diffedLanguage ? diffedLanguage : language;
    try {
        // eslint-disable-next-line no-prototype-builtins
        return langId ? Prism$1.languages.hasOwnProperty(langId) : false;
    } catch (_unused) {
        return false;
    }
}
async function loadCodeLanguage(language, editor, codeNodeKey) {
// NOT IMPLEMENTED
}
function getTextContent(token) {
    if (typeof token === 'string') {
        return token;
    } else if (Array.isArray(token)) {
        return token.map(getTextContent).join('');
    } else {
        return getTextContent(token.content);
    }
}
// The following code is extracted/adapted from prismjs v2
// It will probably be possible to use it directly from prism v2
// in the future when prismjs v2 is published and Lexical upgrades
// the prismsjs dependency
function tokenizeDiffHighlight(tokens, language) {
    const diffLanguage = language;
    const diffGrammar = Prism$1.languages[diffLanguage];
    const env = {
        tokens
    };
    const PREFIXES = Prism$1.languages.diff.PREFIXES;
    for (const token of env.tokens){
        if (typeof token === 'string' || !(token.type in PREFIXES) || !Array.isArray(token.content)) {
            continue;
        }
        const type = token.type;
        let insertedPrefixes = 0;
        const getPrefixToken = ()=>{
            insertedPrefixes++;
            return new Prism$1.Token('prefix', PREFIXES[type], type.replace(/^(\w+).*/, '$1'));
        };
        const withoutPrefixes = token.content.filter((t)=>typeof t === 'string' || t.type !== 'prefix');
        const prefixCount = token.content.length - withoutPrefixes.length;
        const diffTokens = Prism$1.tokenize(getTextContent(withoutPrefixes), diffGrammar);
        // re-insert prefixes
        // always add a prefix at the start
        diffTokens.unshift(getPrefixToken());
        const LINE_BREAK = /\r\n|\n/g;
        const insertAfterLineBreakString = (text)=>{
            const result = [];
            LINE_BREAK.lastIndex = 0;
            let last = 0;
            let m;
            while(insertedPrefixes < prefixCount && (m = LINE_BREAK.exec(text))){
                const end = m.index + m[0].length;
                result.push(text.slice(last, end));
                last = end;
                result.push(getPrefixToken());
            }
            if (result.length === 0) {
                return undefined;
            }
            if (last < text.length) {
                result.push(text.slice(last));
            }
            return result;
        };
        const insertAfterLineBreak = (toks)=>{
            for(let i = 0; i < toks.length && insertedPrefixes < prefixCount; i++){
                const tok = toks[i];
                if (typeof tok === 'string') {
                    const inserted = insertAfterLineBreakString(tok);
                    if (inserted) {
                        toks.splice(i, 1, ...inserted);
                        i += inserted.length - 1;
                    }
                } else if (typeof tok.content === 'string') {
                    const inserted = insertAfterLineBreakString(tok.content);
                    if (inserted) {
                        tok.content = inserted;
                    }
                } else if (Array.isArray(tok.content)) {
                    insertAfterLineBreak(tok.content);
                } else {
                    insertAfterLineBreak([
                        tok.content
                    ]);
                }
            }
        };
        insertAfterLineBreak(diffTokens);
        if (insertedPrefixes < prefixCount) {
            // we are missing the last prefix
            diffTokens.push(getPrefixToken());
        }
        token.content = diffTokens;
    }
    return env.tokens;
}
function $getHighlightNodes(codeNode, language) {
    const DIFF_LANGUAGE_REGEX = /^diff-([\w-]+)/i;
    const diffLanguageMatch = DIFF_LANGUAGE_REGEX.exec(language);
    const code = codeNode.getTextContent();
    let tokens = Prism$1.tokenize(code, Prism$1.languages[diffLanguageMatch ? 'diff' : language]);
    if (diffLanguageMatch) {
        tokens = tokenizeDiffHighlight(tokens, diffLanguageMatch[1]);
    }
    return $mapTokensToLexicalStructure(tokens);
}
function $mapTokensToLexicalStructure(tokens, type) {
    const nodes = [];
    for (const token of tokens){
        if (typeof token === 'string') {
            const partials = token.split(/(\n|\t)/);
            const partialsLength = partials.length;
            for(let i = 0; i < partialsLength; i++){
                const part = partials[i];
                if (part === '\n' || part === '\r\n') {
                    nodes.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createLineBreakNode"])());
                } else if (part === '\t') {
                    nodes.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTabNode"])());
                } else if (part.length > 0) {
                    nodes.push($createCodeHighlightNode(part, type));
                }
            }
        } else {
            const { content, alias } = token;
            if (typeof content === 'string') {
                nodes.push(...$mapTokensToLexicalStructure([
                    content
                ], token.type === 'prefix' && typeof alias === 'string' ? alias : token.type));
            } else if (Array.isArray(content)) {
                nodes.push(...$mapTokensToLexicalStructure(content, token.type === 'unchanged' ? undefined : token.type));
            }
        }
    }
    return nodes;
}
const PrismTokenizer = {
    $tokenize (codeNode, language) {
        return $getHighlightNodes(codeNode, language || this.defaultLanguage);
    },
    defaultLanguage: DEFAULT_CODE_LANGUAGE,
    tokenize (code, language) {
        return Prism$1.tokenize(code, Prism$1.languages[language || ''] || Prism$1.languages[this.defaultLanguage]);
    }
};
function $textNodeTransform(node, editor, tokenizer) {
    // Since CodeNode has flat children structure we only need to check
    // if node's parent is a code node and run highlighting if so
    const parentNode = node.getParent();
    if ($isCodeNode(parentNode)) {
        codeNodeTransform(parentNode, editor, tokenizer);
    } else if ($isCodeHighlightNode(node)) {
        // When code block converted into paragraph or other element
        // code highlight nodes converted back to normal text
        node.replace((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(node.__text));
    }
}
function updateCodeGutter(node, editor) {
    const codeElement = editor.getElementByKey(node.getKey());
    if (codeElement === null) {
        return;
    }
    const children = node.getChildren();
    const childrenLength = children.length;
    // @ts-ignore: internal field
    if (childrenLength === codeElement.__cachedChildrenLength) {
        // Avoid updating the attribute if the children length hasn't changed.
        return;
    }
    // @ts-ignore:: internal field
    codeElement.__cachedChildrenLength = childrenLength;
    let gutter = '1';
    let count = 1;
    for(let i = 0; i < childrenLength; i++){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(children[i])) {
            gutter += '\n' + ++count;
        }
    }
    codeElement.setAttribute('data-gutter', gutter);
}
// Using `skipTransforms` to prevent extra transforms since reformatting the code
// will not affect code block content itself.
//
// Using extra cache (`nodesCurrentlyHighlighting`) since both CodeNode and CodeHighlightNode
// transforms might be called at the same time (e.g. new CodeHighlight node inserted) and
// in both cases we'll rerun whole reformatting over CodeNode, which is redundant.
// Especially when pasting code into CodeBlock.
const nodesCurrentlyHighlighting = new Set();
function codeNodeTransform(node, editor, tokenizer) {
    const nodeKey = node.getKey();
    // When new code block inserted it might not have language selected
    if (node.getLanguage() === undefined) {
        node.setLanguage(tokenizer.defaultLanguage);
    }
    const language = node.getLanguage() || tokenizer.defaultLanguage;
    if (isCodeLanguageLoaded(language)) {
        if (!node.getIsSyntaxHighlightSupported()) {
            node.setIsSyntaxHighlightSupported(true);
        }
    } else {
        if (node.getIsSyntaxHighlightSupported()) {
            node.setIsSyntaxHighlightSupported(false);
        }
        loadCodeLanguage(language, editor, nodeKey);
        return;
    }
    if (nodesCurrentlyHighlighting.has(nodeKey)) {
        return;
    }
    nodesCurrentlyHighlighting.add(nodeKey);
    // Using nested update call to pass `skipTransforms` since we don't want
    // each individual CodeHighlightNode to be transformed again as it's already
    // in its final state
    editor.update(()=>{
        $updateAndRetainSelection(nodeKey, ()=>{
            const currentNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNodeByKey"])(nodeKey);
            if (!$isCodeNode(currentNode) || !currentNode.isAttached()) {
                return false;
            }
            //const DIFF_LANGUAGE_REGEX = /^diff-([\w-]+)/i;
            const currentLanguage = currentNode.getLanguage() || tokenizer.defaultLanguage;
            //const diffLanguageMatch = DIFF_LANGUAGE_REGEX.exec(currentLanguage);
            const highlightNodes = tokenizer.$tokenize(currentNode, currentLanguage);
            const diffRange = getDiffRange(currentNode.getChildren(), highlightNodes);
            const { from, to, nodesForReplacement } = diffRange;
            if (from !== to || nodesForReplacement.length) {
                node.splice(from, to - from, nodesForReplacement);
                return true;
            }
            return false;
        });
    }, {
        onUpdate: ()=>{
            nodesCurrentlyHighlighting.delete(nodeKey);
        },
        skipTransforms: true
    });
}
// Wrapping update function into selection retainer, that tries to keep cursor at the same
// position as before.
function $updateAndRetainSelection(nodeKey, updateFn) {
    const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNodeByKey"])(nodeKey);
    if (!$isCodeNode(node) || !node.isAttached()) {
        return;
    }
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    // If it's not range selection (or null selection) there's no need to change it,
    // but we can still run highlighting logic
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
        updateFn();
        return;
    }
    const anchor = selection.anchor;
    const anchorOffset = anchor.offset;
    const isNewLineAnchor = anchor.type === 'element' && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(node.getChildAtIndex(anchor.offset - 1));
    let textOffset = 0;
    // Calculating previous text offset (all text node prior to anchor + anchor own text offset)
    if (!isNewLineAnchor) {
        const anchorNode = anchor.getNode();
        textOffset = anchorOffset + anchorNode.getPreviousSiblings().reduce((offset, _node)=>{
            return offset + _node.getTextContentSize();
        }, 0);
    }
    const hasChanges = updateFn();
    if (!hasChanges) {
        return;
    }
    // Non-text anchors only happen for line breaks, otherwise
    // selection will be within text node (code highlight node)
    if (isNewLineAnchor) {
        anchor.getNode().select(anchorOffset, anchorOffset);
        return;
    }
    // If it was non-element anchor then we walk through child nodes
    // and looking for a position of original text offset
    node.getChildren().some((_node)=>{
        const isText = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(_node);
        if (isText || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(_node)) {
            const textContentSize = _node.getTextContentSize();
            if (isText && textContentSize >= textOffset) {
                _node.select(textOffset, textOffset);
                return true;
            }
            textOffset -= textContentSize;
        }
        return false;
    });
}
// Finds minimal diff range between two nodes lists. It returns from/to range boundaries of prevNodes
// that needs to be replaced with `nodes` (subset of nextNodes) to make prevNodes equal to nextNodes.
function getDiffRange(prevNodes, nextNodes) {
    let leadingMatch = 0;
    while(leadingMatch < prevNodes.length){
        if (!isEqual(prevNodes[leadingMatch], nextNodes[leadingMatch])) {
            break;
        }
        leadingMatch++;
    }
    const prevNodesLength = prevNodes.length;
    const nextNodesLength = nextNodes.length;
    const maxTrailingMatch = Math.min(prevNodesLength, nextNodesLength) - leadingMatch;
    let trailingMatch = 0;
    while(trailingMatch < maxTrailingMatch){
        trailingMatch++;
        if (!isEqual(prevNodes[prevNodesLength - trailingMatch], nextNodes[nextNodesLength - trailingMatch])) {
            trailingMatch--;
            break;
        }
    }
    const from = leadingMatch;
    const to = prevNodesLength - trailingMatch;
    const nodesForReplacement = nextNodes.slice(leadingMatch, nextNodesLength - trailingMatch);
    return {
        from,
        nodesForReplacement,
        to
    };
}
function isEqual(nodeA, nodeB) {
    // Only checking for code highlight nodes, tabs and linebreaks. If it's regular text node
    // returning false so that it's transformed into code highlight node
    return $isCodeHighlightNode(nodeA) && $isCodeHighlightNode(nodeB) && nodeA.__text === nodeB.__text && nodeA.__highlightType === nodeB.__highlightType || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(nodeA) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(nodeB) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(nodeA) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(nodeB);
}
/**
 * Returns a boolean.
 * Check that the selection span is within a single CodeNode.
 * This is used to guard against executing handlers that can only be
 * applied in a single CodeNode context
 */ function $isSelectionInCode(selection) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
        return false;
    }
    const anchorNode = selection.anchor.getNode();
    const maybeAnchorCodeNode = $isCodeNode(anchorNode) ? anchorNode : anchorNode.getParent();
    const focusNode = selection.focus.getNode();
    const maybeFocusCodeNode = $isCodeNode(focusNode) ? focusNode : focusNode.getParent();
    return $isCodeNode(maybeAnchorCodeNode) && maybeAnchorCodeNode.is(maybeFocusCodeNode);
}
/**
 * Returns an Array of code lines
 * Take the sequence of LineBreakNode | TabNode | CodeHighlightNode forming
 * the selection and split it by LineBreakNode.
 * If the selection ends at the start of the last line, it is considered empty.
 * Empty lines are discarded.
 */ function $getCodeLines(selection) {
    const nodes = selection.getNodes();
    const lines = [];
    if (nodes.length === 1 && $isCodeNode(nodes[0])) {
        return lines;
    }
    let lastLine = [];
    for(let i = 0; i < nodes.length; i++){
        const node = nodes[i];
        if (!($isCodeHighlightNode(node) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(node) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(node))) {
            formatDevErrorMessage(`Expected selection to be inside CodeBlock and consisting of CodeHighlightNode, TabNode and LineBreakNode`);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(node)) {
            if (lastLine.length > 0) {
                lines.push(lastLine);
                lastLine = [];
            }
        } else {
            lastLine.push(node);
        }
    }
    if (lastLine.length > 0) {
        const selectionEnd = selection.isBackward() ? selection.anchor : selection.focus;
        // Discard the last line if the selection ends exactly at the
        // start of the line (no real selection)
        const lastPoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createPoint"])(lastLine[0].getKey(), 0, 'text');
        if (!selectionEnd.is(lastPoint)) {
            lines.push(lastLine);
        }
    }
    return lines;
}
function $handleTab(shiftKey) {
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) || !$isSelectionInCode(selection)) {
        return null;
    }
    const indentOrOutdent = !shiftKey ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INDENT_CONTENT_COMMAND"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OUTDENT_CONTENT_COMMAND"];
    const tabOrOutdent = !shiftKey ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_TAB_COMMAND"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OUTDENT_CONTENT_COMMAND"];
    const anchor = selection.anchor;
    const focus = selection.focus;
    // 1. early decision when there is no real selection
    if (anchor.is(focus)) {
        return tabOrOutdent;
    }
    // 2. If only empty lines or multiple non-empty lines are selected: indent/outdent
    const codeLines = $getCodeLines(selection);
    if (codeLines.length !== 1) {
        return indentOrOutdent;
    }
    const codeLine = codeLines[0];
    const codeLineLength = codeLine.length;
    if (!(codeLineLength !== 0)) {
        formatDevErrorMessage(`$getCodeLines only extracts non-empty lines`);
    } // Take into account the direction of the selection
    let selectionFirst;
    let selectionLast;
    if (selection.isBackward()) {
        selectionFirst = focus;
        selectionLast = anchor;
    } else {
        selectionFirst = anchor;
        selectionLast = focus;
    }
    // find boundary elements of the line
    // since codeLine only contains TabNode | CodeHighlightNode
    // the result of these functions should is of Type TabNode | CodeHighlightNode
    const firstOfLine = $getFirstCodeNodeOfLine(codeLine[0]);
    const lastOfLine = $getLastCodeNodeOfLine(codeLine[0]);
    const anchorOfLine = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createPoint"])(firstOfLine.getKey(), 0, 'text');
    const focusOfLine = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createPoint"])(lastOfLine.getKey(), lastOfLine.getTextContentSize(), 'text');
    // 3. multiline because selection started strictly before the line
    if (selectionFirst.isBefore(anchorOfLine)) {
        return indentOrOutdent;
    }
    // 4. multiline because the selection stops strictly after the line
    if (focusOfLine.isBefore(selectionLast)) {
        return indentOrOutdent;
    }
    // The selection if within the line.
    // 4. If it does not touch both borders, it needs a tab
    if (anchorOfLine.isBefore(selectionFirst) || selectionLast.isBefore(focusOfLine)) {
        return tabOrOutdent;
    }
    // 5. Selection is matching a full line on non-empty code
    return indentOrOutdent;
}
function $handleMultilineIndent(type) {
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) || !$isSelectionInCode(selection)) {
        return false;
    }
    const codeLines = $getCodeLines(selection);
    const codeLinesLength = codeLines.length;
    // Special Indent case
    // Selection is collapsed at the beginning of a line
    if (codeLinesLength === 0 && selection.isCollapsed()) {
        if (type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INDENT_CONTENT_COMMAND"]) {
            selection.insertNodes([
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTabNode"])()
            ]);
        }
        return true;
    }
    // Special Indent case
    // Selection is matching only one LineBreak
    if (codeLinesLength === 0 && type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INDENT_CONTENT_COMMAND"] && selection.getTextContent() === '\n') {
        const tabNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTabNode"])();
        const lineBreakNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createLineBreakNode"])();
        const direction = selection.isBackward() ? 'previous' : 'next';
        selection.insertNodes([
            tabNode,
            lineBreakNode
        ]);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setSelectionFromCaretRange"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getCaretRangeInDirection"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getCaretRange"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getTextPointCaret"])(tabNode, 'next', 0), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$normalizeCaret"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSiblingCaret"])(lineBreakNode, 'next'))), direction));
        return true;
    }
    // Indent Non Empty Lines
    for(let i = 0; i < codeLinesLength; i++){
        const line = codeLines[i];
        // a line here is never empty
        if (line.length > 0) {
            let firstOfLine = line[0];
            // make sure to consider the first node on the first line
            // because the line might not be fully selected
            if (i === 0) {
                firstOfLine = $getFirstCodeNodeOfLine(firstOfLine);
            }
            if (type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INDENT_CONTENT_COMMAND"]) {
                const tabNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTabNode"])();
                firstOfLine.insertBefore(tabNode);
                // First real code line may need selection adjustment
                // when firstOfLine is at the selection boundary
                if (i === 0) {
                    const anchorKey = selection.isBackward() ? 'focus' : 'anchor';
                    const anchorLine = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createPoint"])(firstOfLine.getKey(), 0, 'text');
                    if (selection[anchorKey].is(anchorLine)) {
                        selection[anchorKey].set(tabNode.getKey(), 0, 'text');
                    }
                }
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(firstOfLine)) {
                firstOfLine.remove();
            }
        }
    }
    return true;
}
function $handleShiftLines(type, event) {
    // We only care about the alt+arrow keys
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
        return false;
    }
    // I'm not quite sure why, but it seems like calling anchor.getNode() collapses the selection here
    // So first, get the anchor and the focus, then get their nodes
    const { anchor, focus } = selection;
    const anchorOffset = anchor.offset;
    const focusOffset = focus.offset;
    const anchorNode = anchor.getNode();
    const focusNode = focus.getNode();
    const arrowIsUp = type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_UP_COMMAND"];
    // Ensure the selection is within the codeblock
    if (!$isSelectionInCode(selection) || !($isCodeHighlightNode(anchorNode) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(anchorNode)) || !($isCodeHighlightNode(focusNode) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(focusNode))) {
        return false;
    }
    if (!event.altKey) {
        // Handle moving selection out of the code block, given there are no
        // siblings that can natively take the selection.
        if (selection.isCollapsed()) {
            const codeNode = anchorNode.getParentOrThrow();
            if (arrowIsUp && anchorOffset === 0 && anchorNode.getPreviousSibling() === null) {
                const codeNodeSibling = codeNode.getPreviousSibling();
                if (codeNodeSibling === null) {
                    codeNode.selectPrevious();
                    event.preventDefault();
                    return true;
                }
            } else if (!arrowIsUp && anchorOffset === anchorNode.getTextContentSize() && anchorNode.getNextSibling() === null) {
                const codeNodeSibling = codeNode.getNextSibling();
                if (codeNodeSibling === null) {
                    codeNode.selectNext();
                    event.preventDefault();
                    return true;
                }
            }
        }
        return false;
    }
    let start;
    let end;
    if (anchorNode.isBefore(focusNode)) {
        start = $getFirstCodeNodeOfLine(anchorNode);
        end = $getLastCodeNodeOfLine(focusNode);
    } else {
        start = $getFirstCodeNodeOfLine(focusNode);
        end = $getLastCodeNodeOfLine(anchorNode);
    }
    if (start == null || end == null) {
        return false;
    }
    const range = start.getNodesBetween(end);
    for(let i = 0; i < range.length; i++){
        const node = range[i];
        if (!$isCodeHighlightNode(node) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(node) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(node)) {
            return false;
        }
    }
    // After this point, we know the selection is within the codeblock. We may not be able to
    // actually move the lines around, but we want to return true either way to prevent
    // the event's default behavior
    event.preventDefault();
    event.stopPropagation(); // required to stop cursor movement under Firefox
    const linebreak = arrowIsUp ? start.getPreviousSibling() : end.getNextSibling();
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(linebreak)) {
        return true;
    }
    const sibling = arrowIsUp ? linebreak.getPreviousSibling() : linebreak.getNextSibling();
    if (sibling == null) {
        return true;
    }
    const maybeInsertionPoint = $isCodeHighlightNode(sibling) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(sibling) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(sibling) ? arrowIsUp ? $getFirstCodeNodeOfLine(sibling) : $getLastCodeNodeOfLine(sibling) : null;
    let insertionPoint = maybeInsertionPoint != null ? maybeInsertionPoint : sibling;
    linebreak.remove();
    range.forEach((node)=>node.remove());
    if (type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_UP_COMMAND"]) {
        range.forEach((node)=>insertionPoint.insertBefore(node));
        insertionPoint.insertBefore(linebreak);
    } else {
        insertionPoint.insertAfter(linebreak);
        insertionPoint = linebreak;
        range.forEach((node)=>{
            insertionPoint.insertAfter(node);
            insertionPoint = node;
        });
    }
    selection.setTextNodeRange(anchorNode, anchorOffset, focusNode, focusOffset);
    return true;
}
function $handleMoveTo(type, event) {
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
        return false;
    }
    const { anchor, focus } = selection;
    const anchorNode = anchor.getNode();
    const focusNode = focus.getNode();
    const isMoveToStart = type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MOVE_TO_START"];
    // Ensure the selection is within the codeblock
    if (!$isSelectionInCode(selection) || !($isCodeHighlightNode(anchorNode) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(anchorNode)) || !($isCodeHighlightNode(focusNode) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTabNode"])(focusNode))) {
        return false;
    }
    if (isMoveToStart) {
        const start = $getStartOfCodeInLine(focusNode, focus.offset);
        if (start !== null) {
            const { node, offset } = start;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(node)) {
                node.selectNext(0, 0);
            } else {
                selection.setTextNodeRange(node, offset, node, offset);
            }
        } else {
            focusNode.getParentOrThrow().selectStart();
        }
    } else {
        const node = $getEndOfCodeInLine(focusNode);
        node.select();
    }
    event.preventDefault();
    event.stopPropagation();
    return true;
}
function registerCodeHighlighting(editor, tokenizer) {
    if (!editor.hasNodes([
        CodeNode,
        CodeHighlightNode
    ])) {
        throw new Error('CodeHighlightPlugin: CodeNode or CodeHighlightNode not registered on editor');
    }
    if (tokenizer == null) {
        tokenizer = PrismTokenizer;
    }
    const registrations = [];
    // Only register the mutation listener if not in headless mode
    if (editor._headless !== true) {
        registrations.push(editor.registerMutationListener(CodeNode, (mutations)=>{
            editor.update(()=>{
                for (const [key, type] of mutations){
                    if (type !== 'destroyed') {
                        const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNodeByKey"])(key);
                        if (node !== null) {
                            updateCodeGutter(node, editor);
                        }
                    }
                }
            });
        }, {
            skipInitialization: false
        }));
    }
    // Add the rest of the registrations
    registrations.push(editor.registerNodeTransform(CodeNode, (node)=>codeNodeTransform(node, editor, tokenizer)), editor.registerNodeTransform(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextNode"], (node)=>$textNodeTransform(node, editor, tokenizer)), editor.registerNodeTransform(CodeHighlightNode, (node)=>$textNodeTransform(node, editor, tokenizer)), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_TAB_COMMAND"], (event)=>{
        const command = $handleTab(event.shiftKey);
        if (command === null) {
            return false;
        }
        event.preventDefault();
        editor.dispatchCommand(command, undefined);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_TAB_COMMAND"], ()=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!$isSelectionInCode(selection)) {
            return false;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$insertNodes"])([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTabNode"])()
        ]);
        return true;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INDENT_CONTENT_COMMAND"], (payload)=>$handleMultilineIndent(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INDENT_CONTENT_COMMAND"]), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OUTDENT_CONTENT_COMMAND"], (payload)=>$handleMultilineIndent(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OUTDENT_CONTENT_COMMAND"]), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_UP_COMMAND"], (event)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        const { anchor } = selection;
        const anchorNode = anchor.getNode();
        if (!$isSelectionInCode(selection)) {
            return false;
        }
        // If at the start of a code block, prevent selection from moving out
        if (selection.isCollapsed() && anchor.offset === 0 && anchorNode.getPreviousSibling() === null && $isCodeNode(anchorNode.getParentOrThrow())) {
            event.preventDefault();
            return true;
        }
        return $handleShiftLines(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_UP_COMMAND"], event);
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_DOWN_COMMAND"], (event)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        const { anchor } = selection;
        const anchorNode = anchor.getNode();
        if (!$isSelectionInCode(selection)) {
            return false;
        }
        // If at the end of a code block, prevent selection from moving out
        if (selection.isCollapsed() && anchor.offset === anchorNode.getTextContentSize() && anchorNode.getNextSibling() === null && $isCodeNode(anchorNode.getParentOrThrow())) {
            event.preventDefault();
            return true;
        }
        return $handleShiftLines(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_ARROW_DOWN_COMMAND"], event);
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MOVE_TO_START"], (event)=>$handleMoveTo(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MOVE_TO_START"], event), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MOVE_TO_END"], (event)=>$handleMoveTo(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MOVE_TO_END"], event), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]));
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])(...registrations);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /** @deprecated renamed to {@link $getFirstCodeNodeOfLine} by @lexical/eslint-plugin rules-of-lexical */ const getFirstCodeNodeOfLine = $getFirstCodeNodeOfLine;
/** @deprecated renamed to {@link $getLastCodeNodeOfLine} by @lexical/eslint-plugin rules-of-lexical */ const getLastCodeNodeOfLine = $getLastCodeNodeOfLine;
/** @deprecated renamed to {@link $getEndOfCodeInLine} by @lexical/eslint-plugin rules-of-lexical */ const getEndOfCodeInLine = $getEndOfCodeInLine;
/** @deprecated renamed to {@link $getStartOfCodeInLine} by @lexical/eslint-plugin rules-of-lexical */ const getStartOfCodeInLine = $getStartOfCodeInLine;
;
}}),
"[project]/node_modules/@lexical/link/LexicalLink.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "$createAutoLinkNode": (()=>$createAutoLinkNode),
    "$createLinkNode": (()=>$createLinkNode),
    "$isAutoLinkNode": (()=>$isAutoLinkNode),
    "$isLinkNode": (()=>$isLinkNode),
    "$toggleLink": (()=>$toggleLink),
    "AutoLinkNode": (()=>AutoLinkNode),
    "LinkNode": (()=>LinkNode),
    "TOGGLE_LINK_COMMAND": (()=>TOGGLE_LINK_COMMAND),
    "formatUrl": (()=>formatUrl),
    "toggleLink": (()=>toggleLink)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Do not require this module directly! Use normal `invariant` calls.
function formatDevErrorMessage(message) {
    throw new Error(message);
}
const SUPPORTED_URL_PROTOCOLS = new Set([
    'http:',
    'https:',
    'mailto:',
    'sms:',
    'tel:'
]);
/** @noInheritDoc */ class LinkNode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementNode"] {
    /** @internal */ /** @internal */ /** @internal */ /** @internal */ static getType() {
        return 'link';
    }
    static clone(node) {
        return new LinkNode(node.__url, {
            rel: node.__rel,
            target: node.__target,
            title: node.__title
        }, node.__key);
    }
    constructor(url = '', attributes = {}, key){
        super(key);
        const { target = null, rel = null, title = null } = attributes;
        this.__url = url;
        this.__target = target;
        this.__rel = rel;
        this.__title = title;
    }
    createDOM(config) {
        const element = document.createElement('a');
        this.updateLinkDOM(null, element, config);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addClassNamesToElement"])(element, config.theme.link);
        return element;
    }
    updateLinkDOM(prevNode, anchor, config) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLAnchorElement"])(anchor)) {
            if (!prevNode || prevNode.__url !== this.__url) {
                anchor.href = this.sanitizeUrl(this.__url);
            }
            for (const attr of [
                'target',
                'rel',
                'title'
            ]){
                const key = `__${attr}`;
                const value = this[key];
                if (!prevNode || prevNode[key] !== value) {
                    if (value) {
                        anchor[attr] = value;
                    } else {
                        anchor.removeAttribute(attr);
                    }
                }
            }
        }
    }
    updateDOM(prevNode, anchor, config) {
        this.updateLinkDOM(prevNode, anchor, config);
        return false;
    }
    static importDOM() {
        return {
            a: (node)=>({
                    conversion: $convertAnchorElement,
                    priority: 1
                })
        };
    }
    static importJSON(serializedNode) {
        return $createLinkNode().updateFromJSON(serializedNode);
    }
    updateFromJSON(serializedNode) {
        return super.updateFromJSON(serializedNode).setURL(serializedNode.url).setRel(serializedNode.rel || null).setTarget(serializedNode.target || null).setTitle(serializedNode.title || null);
    }
    sanitizeUrl(url) {
        url = formatUrl(url);
        try {
            const parsedUrl = new URL(formatUrl(url));
            // eslint-disable-next-line no-script-url
            if (!SUPPORTED_URL_PROTOCOLS.has(parsedUrl.protocol)) {
                return 'about:blank';
            }
        } catch (_unused) {
            return url;
        }
        return url;
    }
    exportJSON() {
        return {
            ...super.exportJSON(),
            rel: this.getRel(),
            target: this.getTarget(),
            title: this.getTitle(),
            url: this.getURL()
        };
    }
    getURL() {
        return this.getLatest().__url;
    }
    setURL(url) {
        const writable = this.getWritable();
        writable.__url = url;
        return writable;
    }
    getTarget() {
        return this.getLatest().__target;
    }
    setTarget(target) {
        const writable = this.getWritable();
        writable.__target = target;
        return writable;
    }
    getRel() {
        return this.getLatest().__rel;
    }
    setRel(rel) {
        const writable = this.getWritable();
        writable.__rel = rel;
        return writable;
    }
    getTitle() {
        return this.getLatest().__title;
    }
    setTitle(title) {
        const writable = this.getWritable();
        writable.__title = title;
        return writable;
    }
    insertNewAfter(_, restoreSelection = true) {
        const linkNode = $createLinkNode(this.__url, {
            rel: this.__rel,
            target: this.__target,
            title: this.__title
        });
        this.insertAfter(linkNode, restoreSelection);
        return linkNode;
    }
    canInsertTextBefore() {
        return false;
    }
    canInsertTextAfter() {
        return false;
    }
    canBeEmpty() {
        return false;
    }
    isInline() {
        return true;
    }
    extractWithChild(child, selection, destination) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        const anchorNode = selection.anchor.getNode();
        const focusNode = selection.focus.getNode();
        return this.isParentOf(anchorNode) && this.isParentOf(focusNode) && selection.getTextContent().length > 0;
    }
    isEmailURI() {
        return this.__url.startsWith('mailto:');
    }
    isWebSiteURI() {
        return this.__url.startsWith('https://') || this.__url.startsWith('http://');
    }
}
function $convertAnchorElement(domNode) {
    let node = null;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isHTMLAnchorElement"])(domNode)) {
        const content = domNode.textContent;
        if (content !== null && content !== '' || domNode.children.length > 0) {
            node = $createLinkNode(domNode.getAttribute('href') || '', {
                rel: domNode.getAttribute('rel'),
                target: domNode.getAttribute('target'),
                title: domNode.getAttribute('title')
            });
        }
    }
    return {
        node
    };
}
/**
 * Takes a URL and creates a LinkNode.
 * @param url - The URL the LinkNode should direct to.
 * @param attributes - Optional HTML a tag attributes \\{ target, rel, title \\}
 * @returns The LinkNode.
 */ function $createLinkNode(url = '', attributes) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$applyNodeReplacement"])(new LinkNode(url, attributes));
}
/**
 * Determines if node is a LinkNode.
 * @param node - The node to be checked.
 * @returns true if node is a LinkNode, false otherwise.
 */ function $isLinkNode(node) {
    return node instanceof LinkNode;
}
// Custom node type to override `canInsertTextAfter` that will
// allow typing within the link
class AutoLinkNode extends LinkNode {
    /** @internal */ /** Indicates whether the autolink was ever unlinked. **/ constructor(url = '', attributes = {}, key){
        super(url, attributes, key);
        this.__isUnlinked = attributes.isUnlinked !== undefined && attributes.isUnlinked !== null ? attributes.isUnlinked : false;
    }
    static getType() {
        return 'autolink';
    }
    static clone(node) {
        return new AutoLinkNode(node.__url, {
            isUnlinked: node.__isUnlinked,
            rel: node.__rel,
            target: node.__target,
            title: node.__title
        }, node.__key);
    }
    getIsUnlinked() {
        return this.__isUnlinked;
    }
    setIsUnlinked(value) {
        const self = this.getWritable();
        self.__isUnlinked = value;
        return self;
    }
    createDOM(config) {
        if (this.__isUnlinked) {
            return document.createElement('span');
        } else {
            return super.createDOM(config);
        }
    }
    updateDOM(prevNode, anchor, config) {
        return super.updateDOM(prevNode, anchor, config) || prevNode.__isUnlinked !== this.__isUnlinked;
    }
    static importJSON(serializedNode) {
        return $createAutoLinkNode().updateFromJSON(serializedNode);
    }
    updateFromJSON(serializedNode) {
        return super.updateFromJSON(serializedNode).setIsUnlinked(serializedNode.isUnlinked || false);
    }
    static importDOM() {
        // TODO: Should link node should handle the import over autolink?
        return null;
    }
    exportJSON() {
        return {
            ...super.exportJSON(),
            isUnlinked: this.__isUnlinked
        };
    }
    insertNewAfter(selection, restoreSelection = true) {
        const element = this.getParentOrThrow().insertNewAfter(selection, restoreSelection);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(element)) {
            const linkNode = $createAutoLinkNode(this.__url, {
                isUnlinked: this.__isUnlinked,
                rel: this.__rel,
                target: this.__target,
                title: this.__title
            });
            element.append(linkNode);
            return linkNode;
        }
        return null;
    }
}
/**
 * Takes a URL and creates an AutoLinkNode. AutoLinkNodes are generally automatically generated
 * during typing, which is especially useful when a button to generate a LinkNode is not practical.
 * @param url - The URL the LinkNode should direct to.
 * @param attributes - Optional HTML a tag attributes. \\{ target, rel, title \\}
 * @returns The LinkNode.
 */ function $createAutoLinkNode(url = '', attributes) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$applyNodeReplacement"])(new AutoLinkNode(url, attributes));
}
/**
 * Determines if node is an AutoLinkNode.
 * @param node - The node to be checked.
 * @returns true if node is an AutoLinkNode, false otherwise.
 */ function $isAutoLinkNode(node) {
    return node instanceof AutoLinkNode;
}
const TOGGLE_LINK_COMMAND = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createCommand"])('TOGGLE_LINK_COMMAND');
function $getPointNode(point, offset) {
    if (point.type === 'element') {
        const node = point.getNode();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node)) {
            formatDevErrorMessage(`$getPointNode: element point is not an ElementNode`);
        }
        const childNode = node.getChildren()[point.offset + offset];
        return childNode || null;
    }
    return null;
}
/**
 * Preserve the logical start/end of a RangeSelection in situations where
 * the point is an element that may be reparented in the callback.
 *
 * @param $fn The function to run
 * @returns The result of the callback
 */ function $withSelectedNodes($fn) {
    const initialSelection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(initialSelection)) {
        return $fn();
    }
    const normalized = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$normalizeSelection__EXPERIMENTAL"])(initialSelection);
    const isBackwards = normalized.isBackward();
    const anchorNode = $getPointNode(normalized.anchor, isBackwards ? -1 : 0);
    const focusNode = $getPointNode(normalized.focus, isBackwards ? 0 : -1);
    const rval = $fn();
    if (anchorNode || focusNode) {
        const updatedSelection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(updatedSelection)) {
            const finalSelection = updatedSelection.clone();
            if (anchorNode) {
                const anchorParent = anchorNode.getParent();
                if (anchorParent) {
                    finalSelection.anchor.set(anchorParent.getKey(), anchorNode.getIndexWithinParent() + (isBackwards ? 1 : 0), 'element');
                }
            }
            if (focusNode) {
                const focusParent = focusNode.getParent();
                if (focusParent) {
                    finalSelection.focus.set(focusParent.getKey(), focusNode.getIndexWithinParent() + (isBackwards ? 0 : 1), 'element');
                }
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setSelection"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$normalizeSelection__EXPERIMENTAL"])(finalSelection));
        }
    }
    return rval;
}
/**
 * Generates or updates a LinkNode. It can also delete a LinkNode if the URL is null,
 * but saves any children and brings them up to the parent node.
 * @param url - The URL the link directs to.
 * @param attributes - Optional HTML a tag attributes. \\{ target, rel, title \\}
 */ function $toggleLink(url, attributes = {}) {
    const { target, title } = attributes;
    const rel = attributes.rel === undefined ? 'noreferrer' : attributes.rel;
    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
    if (selection === null || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
        return;
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
        const nodes = selection.getNodes();
        if (nodes.length === 0) {
            return;
        }
        // Handle all selected nodes
        nodes.forEach((node)=>{
            if (url === null) {
                // Remove link
                const linkParent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$findMatchingParent"])(node, (parent)=>!$isAutoLinkNode(parent) && $isLinkNode(parent));
                if (linkParent) {
                    linkParent.insertBefore(node);
                    if (linkParent.getChildren().length === 0) {
                        linkParent.remove();
                    }
                }
            } else {
                // Add/Update link
                const existingLink = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$findMatchingParent"])(node, (parent)=>!$isAutoLinkNode(parent) && $isLinkNode(parent));
                if (existingLink) {
                    existingLink.setURL(url);
                    if (target !== undefined) {
                        existingLink.setTarget(target);
                    }
                    if (rel !== undefined) {
                        existingLink.setRel(rel);
                    }
                } else {
                    const linkNode = $createLinkNode(url, {
                        rel,
                        target
                    });
                    node.insertBefore(linkNode);
                    linkNode.append(node);
                }
            }
        });
        return;
    }
    // Handle RangeSelection
    const nodes = selection.extract();
    if (url === null) {
        // Remove LinkNodes
        nodes.forEach((node)=>{
            const parentLink = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$findMatchingParent"])(node, (parent)=>!$isAutoLinkNode(parent) && $isLinkNode(parent));
            if (parentLink) {
                const children = parentLink.getChildren();
                for(let i = 0; i < children.length; i++){
                    parentLink.insertBefore(children[i]);
                }
                parentLink.remove();
            }
        });
        return;
    }
    const updatedNodes = new Set();
    const updateLinkNode = (linkNode)=>{
        if (updatedNodes.has(linkNode.getKey())) {
            return;
        }
        updatedNodes.add(linkNode.getKey());
        linkNode.setURL(url);
        if (target !== undefined) {
            linkNode.setTarget(target);
        }
        if (rel !== undefined) {
            linkNode.setRel(rel);
        }
        if (title !== undefined) {
            linkNode.setTitle(title);
        }
    };
    // Add or merge LinkNodes
    if (nodes.length === 1) {
        const firstNode = nodes[0];
        // if the first node is a LinkNode or if its
        // parent is a LinkNode, we update the URL, target and rel.
        const linkNode = $getAncestor(firstNode, $isLinkNode);
        if (linkNode !== null) {
            return updateLinkNode(linkNode);
        }
    }
    $withSelectedNodes(()=>{
        let linkNode = null;
        for (const node of nodes){
            if (!node.isAttached()) {
                continue;
            }
            const parentLinkNode = $getAncestor(node, $isLinkNode);
            if (parentLinkNode) {
                updateLinkNode(parentLinkNode);
                continue;
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node)) {
                if (!node.isInline()) {
                    continue;
                }
                if ($isLinkNode(node)) {
                    // If it's not an autolink node and we don't already have a LinkNode
                    // in this block then we can update it and re-use it
                    if (!$isAutoLinkNode(node) && (linkNode === null || !linkNode.getParentOrThrow().isParentOf(node))) {
                        updateLinkNode(node);
                        linkNode = node;
                        continue;
                    }
                    // Unwrap LinkNode, we already have one or it's an AutoLinkNode
                    for (const child of node.getChildren()){
                        node.insertBefore(child);
                    }
                    node.remove();
                    continue;
                }
            }
            const prevLinkNode = node.getPreviousSibling();
            if ($isLinkNode(prevLinkNode) && prevLinkNode.is(linkNode)) {
                prevLinkNode.append(node);
                continue;
            }
            linkNode = $createLinkNode(url, {
                rel,
                target,
                title
            });
            node.insertAfter(linkNode);
            linkNode.append(node);
        }
    });
}
/** @deprecated renamed to {@link $toggleLink} by @lexical/eslint-plugin rules-of-lexical */ const toggleLink = $toggleLink;
function $getAncestor(node, predicate) {
    let parent = node;
    while(parent !== null && parent.getParent() !== null && !predicate(parent)){
        parent = parent.getParentOrThrow();
    }
    return predicate(parent) ? parent : null;
}
const PHONE_NUMBER_REGEX = /^\+?[0-9\s()-]{5,}$/;
/**
 * Formats a URL string by adding appropriate protocol if missing
 *
 * @param url - URL to format
 * @returns Formatted URL with appropriate protocol
 */ function formatUrl(url) {
    // Check if URL already has a protocol
    if (url.match(/^[a-z][a-z0-9+.-]*:/i)) {
        // URL already has a protocol, leave it as is
        return url;
    } else if (url.match(/^[/#.]/)) {
        // Relative path, leave it as is
        return url;
    } else if (url.includes('@')) {
        return `mailto:${url}`;
    } else if (PHONE_NUMBER_REGEX.test(url)) {
        return `tel:${url}`;
    }
    // For everything else, return with https:// prefix
    return `https://${url}`;
}
;
}}),
"[project]/node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "$convertFromMarkdownString": (()=>$convertFromMarkdownString),
    "$convertToMarkdownString": (()=>$convertToMarkdownString),
    "BOLD_ITALIC_STAR": (()=>BOLD_ITALIC_STAR),
    "BOLD_ITALIC_UNDERSCORE": (()=>BOLD_ITALIC_UNDERSCORE),
    "BOLD_STAR": (()=>BOLD_STAR),
    "BOLD_UNDERSCORE": (()=>BOLD_UNDERSCORE),
    "CHECK_LIST": (()=>CHECK_LIST),
    "CODE": (()=>CODE),
    "ELEMENT_TRANSFORMERS": (()=>ELEMENT_TRANSFORMERS),
    "HEADING": (()=>HEADING),
    "HIGHLIGHT": (()=>HIGHLIGHT),
    "INLINE_CODE": (()=>INLINE_CODE),
    "ITALIC_STAR": (()=>ITALIC_STAR),
    "ITALIC_UNDERSCORE": (()=>ITALIC_UNDERSCORE),
    "LINK": (()=>LINK),
    "MULTILINE_ELEMENT_TRANSFORMERS": (()=>MULTILINE_ELEMENT_TRANSFORMERS),
    "ORDERED_LIST": (()=>ORDERED_LIST),
    "QUOTE": (()=>QUOTE),
    "STRIKETHROUGH": (()=>STRIKETHROUGH),
    "TEXT_FORMAT_TRANSFORMERS": (()=>TEXT_FORMAT_TRANSFORMERS),
    "TEXT_MATCH_TRANSFORMERS": (()=>TEXT_MATCH_TRANSFORMERS),
    "TRANSFORMERS": (()=>TRANSFORMERS),
    "UNORDERED_LIST": (()=>UNORDERED_LIST),
    "registerMarkdownShortcuts": (()=>registerMarkdownShortcuts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/list/LexicalList.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$code$2f$LexicalCode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/code/LexicalCode.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/link/LexicalLink.dev.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function indexBy(list, callback) {
    const index = {};
    for (const item of list){
        const key = callback(item);
        if (!key) {
            continue;
        }
        if (index[key]) {
            index[key].push(item);
        } else {
            index[key] = [
                item
            ];
        }
    }
    return index;
}
function transformersByType(transformers) {
    const byType = indexBy(transformers, (t)=>t.type);
    return {
        element: byType.element || [],
        multilineElement: byType['multiline-element'] || [],
        textFormat: byType['text-format'] || [],
        textMatch: byType['text-match'] || []
    };
}
const PUNCTUATION_OR_SPACE = /[!-/:-@[-`{-~\s]/;
const MARKDOWN_EMPTY_LINE_REG_EXP = /^\s{0,3}$/;
function isEmptyParagraph(node) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isParagraphNode"])(node)) {
        return false;
    }
    const firstChild = node.getFirstChild();
    return firstChild == null || node.getChildrenSize() === 1 && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(firstChild) && MARKDOWN_EMPTY_LINE_REG_EXP.test(firstChild.getTextContent());
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * Renders string from markdown. The selection is moved to the start after the operation.
 */ function createMarkdownExport(transformers, shouldPreserveNewLines = false) {
    const byType = transformersByType(transformers);
    const elementTransformers = [
        ...byType.multilineElement,
        ...byType.element
    ];
    const isNewlineDelimited = !shouldPreserveNewLines;
    // Export only uses text formats that are responsible for single format
    // e.g. it will filter out *** (bold, italic) and instead use separate ** and *
    const textFormatTransformers = byType.textFormat.filter((transformer)=>transformer.format.length === 1)// Make sure all text transformers that contain 'code' in their format are at the end of the array. Otherwise, formatted code like
    // <strong><code>code</code></strong> will be exported as `**Bold Code**`, as the code format will be applied first, and the bold format
    // will be applied second and thus skipped entirely, as the code format will prevent any further formatting.
    .sort((a, b)=>{
        return Number(a.format.includes('code')) - Number(b.format.includes('code'));
    });
    return (node)=>{
        const output = [];
        const children = (node || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])()).getChildren();
        for(let i = 0; i < children.length; i++){
            const child = children[i];
            const result = exportTopLevelElements(child, elementTransformers, textFormatTransformers, byType.textMatch);
            if (result != null) {
                output.push(// separate consecutive group of texts with a line break: eg. ["hello", "world"] -> ["hello", "/nworld"]
                isNewlineDelimited && i > 0 && !isEmptyParagraph(child) && !isEmptyParagraph(children[i - 1]) ? '\n'.concat(result) : result);
            }
        }
        // Ensure consecutive groups of texts are at least \n\n apart while each empty paragraph render as a newline.
        // Eg. ["hello", "", "", "hi", "\nworld"] -> "hello\n\n\nhi\n\nworld"
        return output.join('\n');
    };
}
function exportTopLevelElements(node, elementTransformers, textTransformersIndex, textMatchTransformers) {
    for (const transformer of elementTransformers){
        if (!transformer.export) {
            continue;
        }
        const result = transformer.export(node, (_node)=>exportChildren(_node, textTransformersIndex, textMatchTransformers));
        if (result != null) {
            return result;
        }
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node)) {
        return exportChildren(node, textTransformersIndex, textMatchTransformers);
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isDecoratorNode"])(node)) {
        return node.getTextContent();
    } else {
        return null;
    }
}
function exportChildren(node, textTransformersIndex, textMatchTransformers, unclosedTags, unclosableTags) {
    const output = [];
    const children = node.getChildren();
    // keep track of unclosed tags from the very beginning
    if (!unclosedTags) {
        unclosedTags = [];
    }
    if (!unclosableTags) {
        unclosableTags = [];
    }
    mainLoop: for (const child of children){
        for (const transformer of textMatchTransformers){
            if (!transformer.export) {
                continue;
            }
            const result = transformer.export(child, (parentNode)=>exportChildren(parentNode, textTransformersIndex, textMatchTransformers, unclosedTags, // Add current unclosed tags to the list of unclosable tags - we don't want nested tags from
                // textmatch transformers to close the outer ones, as that may result in invalid markdown.
                // E.g. **text [text**](https://lexical.io)
                // is invalid markdown, as the closing ** is inside the link.
                //
                [
                    ...unclosableTags,
                    ...unclosedTags
                ]), (textNode, textContent)=>exportTextFormat(textNode, textContent, textTransformersIndex, unclosedTags, unclosableTags));
            if (result != null) {
                output.push(result);
                continue mainLoop;
            }
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(child)) {
            output.push('\n');
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(child)) {
            output.push(exportTextFormat(child, child.getTextContent(), textTransformersIndex, unclosedTags, unclosableTags));
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(child)) {
            // empty paragraph returns ""
            output.push(exportChildren(child, textTransformersIndex, textMatchTransformers, unclosedTags, unclosableTags));
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isDecoratorNode"])(child)) {
            output.push(child.getTextContent());
        }
    }
    return output.join('');
}
function exportTextFormat(node, textContent, textTransformers, // unclosed tags include the markdown tags that haven't been closed yet, and their associated formats
unclosedTags, unclosableTags) {
    // This function handles the case of a string looking like this: "   foo   "
    // Where it would be invalid markdown to generate: "**   foo   **"
    // If the node has no format, we use the original text.
    // Otherwise, we escape leading and trailing whitespaces to their corresponding code points,
    // ensuring the returned string maintains its original formatting, e.g., "**&#32;&#32;&#32;foo&#32;&#32;&#32;**".
    let output = node.getFormat() === 0 ? textContent : escapeLeadingAndTrailingWhitespaces(textContent);
    if (!node.hasFormat('code')) {
        // Escape any markdown characters in the text content
        output = output.replace(/([*_`~\\])/g, '\\$1');
    }
    // the opening tags to be added to the result
    let openingTags = '';
    // the closing tags to be added to the result
    let closingTagsBefore = '';
    let closingTagsAfter = '';
    const prevNode = getTextSibling(node, true);
    const nextNode = getTextSibling(node, false);
    const applied = new Set();
    for (const transformer of textTransformers){
        const format = transformer.format[0];
        const tag = transformer.tag;
        // dedup applied formats
        if (hasFormat(node, format) && !applied.has(format)) {
            // Multiple tags might be used for the same format (*, _)
            applied.add(format);
            // append the tag to openingTags, if it's not applied to the previous nodes,
            // or the nodes before that (which would result in an unclosed tag)
            if (!hasFormat(prevNode, format) || !unclosedTags.find((element)=>element.tag === tag)) {
                unclosedTags.push({
                    format,
                    tag
                });
                openingTags += tag;
            }
        }
    }
    // close any tags in the same order they were applied, if necessary
    for(let i = 0; i < unclosedTags.length; i++){
        const nodeHasFormat = hasFormat(node, unclosedTags[i].format);
        const nextNodeHasFormat = hasFormat(nextNode, unclosedTags[i].format);
        // prevent adding closing tag if next sibling will do it
        if (nodeHasFormat && nextNodeHasFormat) {
            continue;
        }
        const unhandledUnclosedTags = [
            ...unclosedTags
        ]; // Shallow copy to avoid modifying the original array
        while(unhandledUnclosedTags.length > i){
            const unclosedTag = unhandledUnclosedTags.pop();
            // If tag is unclosable, don't close it and leave it in the original array,
            // So that it can be closed when it's no longer unclosable
            if (unclosableTags && unclosedTag && unclosableTags.find((element)=>element.tag === unclosedTag.tag)) {
                continue;
            }
            if (unclosedTag && typeof unclosedTag.tag === 'string') {
                if (!nodeHasFormat) {
                    // Handles cases where the tag has not been closed before, e.g. if the previous node
                    // was a text match transformer that did not account for closing tags of the next node (e.g. a link)
                    closingTagsBefore += unclosedTag.tag;
                } else if (!nextNodeHasFormat) {
                    closingTagsAfter += unclosedTag.tag;
                }
            }
            // Mutate the original array to remove the closed tag
            unclosedTags.pop();
        }
        break;
    }
    output = openingTags + output + closingTagsAfter;
    // Replace trimmed version of textContent ensuring surrounding whitespace is not modified
    return closingTagsBefore + output;
}
// Get next or previous text sibling a text node, including cases
// when it's a child of inline element (e.g. link)
function getTextSibling(node, backward) {
    let sibling = backward ? node.getPreviousSibling() : node.getNextSibling();
    if (!sibling) {
        const parent = node.getParentOrThrow();
        if (parent.isInline()) {
            sibling = backward ? parent.getPreviousSibling() : parent.getNextSibling();
        }
    }
    while(sibling){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(sibling)) {
            if (!sibling.isInline()) {
                break;
            }
            const descendant = backward ? sibling.getLastDescendant() : sibling.getFirstDescendant();
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(descendant)) {
                return descendant;
            } else {
                sibling = backward ? sibling.getPreviousSibling() : sibling.getNextSibling();
            }
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(sibling)) {
            return sibling;
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(sibling)) {
            return null;
        }
    }
    return null;
}
function hasFormat(node, format) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(node) && node.hasFormat(format);
}
function escapeLeadingAndTrailingWhitespaces(textContent) {
    return textContent.replace(/^\s+|\s+$/g, (match)=>{
        return [
            ...match
        ].map((char)=>'&#' + char.codePointAt(0) + ';').join('');
    });
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function findOutermostTextFormatTransformer(textNode, textFormatTransformersIndex) {
    const textContent = textNode.getTextContent();
    const match = findOutermostMatch(textContent, textFormatTransformersIndex);
    if (!match) {
        return null;
    }
    const textFormatMatchStart = match.index || 0;
    const textFormatMatchEnd = textFormatMatchStart + match[0].length;
    const transformer = textFormatTransformersIndex.transformersByTag[match[1]];
    return {
        endIndex: textFormatMatchEnd,
        match,
        startIndex: textFormatMatchStart,
        transformer
    };
}
// Finds first "<tag>content<tag>" match that is not nested into another tag
function findOutermostMatch(textContent, textTransformersIndex) {
    const openTagsMatch = textContent.match(textTransformersIndex.openTagsRegExp);
    if (openTagsMatch == null) {
        return null;
    }
    for (const match of openTagsMatch){
        // Open tags reg exp might capture leading space so removing it
        // before using match to find transformer
        const tag = match.replace(/^\s/, '');
        const fullMatchRegExp = textTransformersIndex.fullMatchRegExpByTag[tag];
        if (fullMatchRegExp == null) {
            continue;
        }
        const fullMatch = textContent.match(fullMatchRegExp);
        const transformer = textTransformersIndex.transformersByTag[tag];
        if (fullMatch != null && transformer != null) {
            if (transformer.intraword !== false) {
                return fullMatch;
            }
            // For non-intraword transformers checking if it's within a word
            // or surrounded with space/punctuation/newline
            const { index = 0 } = fullMatch;
            const beforeChar = textContent[index - 1];
            const afterChar = textContent[index + fullMatch[0].length];
            if ((!beforeChar || PUNCTUATION_OR_SPACE.test(beforeChar)) && (!afterChar || PUNCTUATION_OR_SPACE.test(afterChar))) {
                return fullMatch;
            }
        }
    }
    return null;
}
function importTextFormatTransformer(textNode, startIndex, endIndex, transformer, match) {
    const textContent = textNode.getTextContent();
    // No text matches - we can safely process the text format match
    let transformedNode, nodeAfter, nodeBefore;
    // If matching full content there's no need to run splitText and can reuse existing textNode
    // to update its content and apply format. E.g. for **_Hello_** string after applying bold
    // format (**) it will reuse the same text node to apply italic (_)
    if (match[0] === textContent) {
        transformedNode = textNode;
    } else {
        if (startIndex === 0) {
            [transformedNode, nodeAfter] = textNode.splitText(endIndex);
        } else {
            [nodeBefore, transformedNode, nodeAfter] = textNode.splitText(startIndex, endIndex);
        }
    }
    transformedNode.setTextContent(match[2]);
    if (transformer) {
        for (const format of transformer.format){
            if (!transformedNode.hasFormat(format)) {
                transformedNode.toggleFormat(format);
            }
        }
    }
    return {
        nodeAfter: nodeAfter,
        nodeBefore: nodeBefore,
        transformedNode: transformedNode
    };
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function findOutermostTextMatchTransformer(textNode_, textMatchTransformers) {
    const textNode = textNode_;
    let foundMatchStartIndex = undefined;
    let foundMatchEndIndex = undefined;
    let foundMatchTransformer = undefined;
    let foundMatch = undefined;
    for (const transformer of textMatchTransformers){
        if (!transformer.replace || !transformer.importRegExp) {
            continue;
        }
        const match = textNode.getTextContent().match(transformer.importRegExp);
        if (!match) {
            continue;
        }
        const startIndex = match.index || 0;
        const endIndex = transformer.getEndIndex ? transformer.getEndIndex(textNode, match) : startIndex + match[0].length;
        if (endIndex === false) {
            continue;
        }
        if (foundMatchStartIndex === undefined || foundMatchEndIndex === undefined || startIndex < foundMatchStartIndex && endIndex > foundMatchEndIndex) {
            foundMatchStartIndex = startIndex;
            foundMatchEndIndex = endIndex;
            foundMatchTransformer = transformer;
            foundMatch = match;
        }
    }
    if (foundMatchStartIndex === undefined || foundMatchEndIndex === undefined || foundMatchTransformer === undefined || foundMatch === undefined) {
        return null;
    }
    return {
        endIndex: foundMatchEndIndex,
        match: foundMatch,
        startIndex: foundMatchStartIndex,
        transformer: foundMatchTransformer
    };
}
function importFoundTextMatchTransformer(textNode, startIndex, endIndex, transformer, match) {
    let transformedNode, nodeAfter, nodeBefore;
    if (startIndex === 0) {
        [transformedNode, nodeAfter] = textNode.splitText(endIndex);
    } else {
        [nodeBefore, transformedNode, nodeAfter] = textNode.splitText(startIndex, endIndex);
    }
    if (!transformer.replace) {
        return null;
    }
    const potentialTransformedNode = transformer.replace(transformedNode, match);
    return {
        nodeAfter,
        nodeBefore,
        transformedNode: potentialTransformedNode || undefined
    };
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * Returns true if the node can contain transformable markdown.
 * Code nodes cannot contain transformable markdown.
 * For example, `code **bold**` should not be transformed to
 * <code>code <strong>bold</strong></code>.
 */ function canContainTransformableMarkdown(node) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(node) && !node.hasFormat('code');
}
/**
 * Handles applying both text format and text match transformers.
 * It finds the outermost text format or text match and applies it,
 * then recursively calls itself to apply the next outermost transformer,
 * until there are no more transformers to apply.
 */ function importTextTransformers(textNode, textFormatTransformersIndex, textMatchTransformers) {
    let foundTextFormat = findOutermostTextFormatTransformer(textNode, textFormatTransformersIndex);
    let foundTextMatch = findOutermostTextMatchTransformer(textNode, textMatchTransformers);
    if (foundTextFormat && foundTextMatch) {
        // Find the outermost transformer
        if (foundTextFormat.startIndex <= foundTextMatch.startIndex && foundTextFormat.endIndex >= foundTextMatch.endIndex || // foundTextMatch is not contained within foundTextFormat
        foundTextMatch.startIndex > foundTextFormat.endIndex) {
            // foundTextFormat wraps foundTextMatch - apply foundTextFormat by setting foundTextMatch to null
            foundTextMatch = null;
        } else {
            // foundTextMatch wraps foundTextFormat - apply foundTextMatch by setting foundTextFormat to null
            foundTextFormat = null;
        }
    }
    if (foundTextFormat) {
        const result = importTextFormatTransformer(textNode, foundTextFormat.startIndex, foundTextFormat.endIndex, foundTextFormat.transformer, foundTextFormat.match);
        if (canContainTransformableMarkdown(result.nodeAfter)) {
            importTextTransformers(result.nodeAfter, textFormatTransformersIndex, textMatchTransformers);
        }
        if (canContainTransformableMarkdown(result.nodeBefore)) {
            importTextTransformers(result.nodeBefore, textFormatTransformersIndex, textMatchTransformers);
        }
        if (canContainTransformableMarkdown(result.transformedNode)) {
            importTextTransformers(result.transformedNode, textFormatTransformersIndex, textMatchTransformers);
        }
    } else if (foundTextMatch) {
        const result = importFoundTextMatchTransformer(textNode, foundTextMatch.startIndex, foundTextMatch.endIndex, foundTextMatch.transformer, foundTextMatch.match);
        if (!result) {
            return;
        }
        if (canContainTransformableMarkdown(result.nodeAfter)) {
            importTextTransformers(result.nodeAfter, textFormatTransformersIndex, textMatchTransformers);
        }
        if (canContainTransformableMarkdown(result.nodeBefore)) {
            importTextTransformers(result.nodeBefore, textFormatTransformersIndex, textMatchTransformers);
        }
        if (canContainTransformableMarkdown(result.transformedNode)) {
            importTextTransformers(result.transformedNode, textFormatTransformersIndex, textMatchTransformers);
        }
    }
    // Handle escape characters
    const textContent = textNode.getTextContent();
    const escapedText = textContent.replace(/\\([*_`~\\])/g, '$1').replace(/&#(\d+);/g, (_, codePoint)=>{
        return String.fromCodePoint(codePoint);
    });
    textNode.setTextContent(escapedText);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * Renders markdown from a string. The selection is moved to the start after the operation.
 */ function createMarkdownImport(transformers, shouldPreserveNewLines = false) {
    const byType = transformersByType(transformers);
    const textFormatTransformersIndex = createTextFormatTransformersIndex(byType.textFormat);
    return (markdownString, node)=>{
        const lines = markdownString.split('\n');
        const linesLength = lines.length;
        const root = node || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])();
        root.clear();
        for(let i = 0; i < linesLength; i++){
            const lineText = lines[i];
            const [imported, shiftedIndex] = $importMultiline(lines, i, byType.multilineElement, root);
            if (imported) {
                // If a multiline markdown element was imported, we don't want to process the lines that were part of it anymore.
                // There could be other sub-markdown elements (both multiline and normal ones) matching within this matched multiline element's children.
                // However, it would be the responsibility of the matched multiline transformer to decide how it wants to handle them.
                // We cannot handle those, as there is no way for us to know how to maintain the correct order of generated lexical nodes for possible children.
                i = shiftedIndex; // Next loop will start from the line after the last line of the multiline element
                continue;
            }
            $importBlocks(lineText, root, byType.element, textFormatTransformersIndex, byType.textMatch, shouldPreserveNewLines);
        }
        // By default, removing empty paragraphs as md does not really
        // allow empty lines and uses them as delimiter.
        // If you need empty lines set shouldPreserveNewLines = true.
        const children = root.getChildren();
        for (const child of children){
            if (!shouldPreserveNewLines && isEmptyParagraph(child) && root.getChildrenSize() > 1) {
                child.remove();
            }
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])() !== null) {
            root.selectStart();
        }
    };
}
/**
 *
 * @returns first element of the returned tuple is a boolean indicating if a multiline element was imported. The second element is the index of the last line that was processed.
 */ function $importMultiline(lines, startLineIndex, multilineElementTransformers, rootNode) {
    for (const transformer of multilineElementTransformers){
        const { handleImportAfterStartMatch, regExpEnd, regExpStart, replace } = transformer;
        const startMatch = lines[startLineIndex].match(regExpStart);
        if (!startMatch) {
            continue; // Try next transformer
        }
        if (handleImportAfterStartMatch) {
            const result = handleImportAfterStartMatch({
                lines,
                rootNode,
                startLineIndex,
                startMatch,
                transformer
            });
            if (result === null) {
                continue;
            } else if (result) {
                return result;
            }
        }
        const regexpEndRegex = typeof regExpEnd === 'object' && 'regExp' in regExpEnd ? regExpEnd.regExp : regExpEnd;
        const isEndOptional = regExpEnd && typeof regExpEnd === 'object' && 'optional' in regExpEnd ? regExpEnd.optional : !regExpEnd;
        let endLineIndex = startLineIndex;
        const linesLength = lines.length;
        // check every single line for the closing match. It could also be on the same line as the opening match.
        while(endLineIndex < linesLength){
            const endMatch = regexpEndRegex ? lines[endLineIndex].match(regexpEndRegex) : null;
            if (!endMatch) {
                if (!isEndOptional || isEndOptional && endLineIndex < linesLength - 1 // Optional end, but didn't reach the end of the document yet => continue searching for potential closing match
                ) {
                    endLineIndex++;
                    continue; // Search next line for closing match
                }
            }
            // Now, check if the closing match matched is the same as the opening match.
            // If it is, we need to continue searching for the actual closing match.
            if (endMatch && startLineIndex === endLineIndex && endMatch.index === startMatch.index) {
                endLineIndex++;
                continue; // Search next line for closing match
            }
            // At this point, we have found the closing match. Next: calculate the lines in between open and closing match
            // This should not include the matches themselves, and be split up by lines
            const linesInBetween = [];
            if (endMatch && startLineIndex === endLineIndex) {
                linesInBetween.push(lines[startLineIndex].slice(startMatch[0].length, -endMatch[0].length));
            } else {
                for(let i = startLineIndex; i <= endLineIndex; i++){
                    if (i === startLineIndex) {
                        const text = lines[i].slice(startMatch[0].length);
                        linesInBetween.push(text); // Also include empty text
                    } else if (i === endLineIndex && endMatch) {
                        const text = lines[i].slice(0, -endMatch[0].length);
                        linesInBetween.push(text); // Also include empty text
                    } else {
                        linesInBetween.push(lines[i]);
                    }
                }
            }
            if (replace(rootNode, null, startMatch, endMatch, linesInBetween, true) !== false) {
                // Return here. This $importMultiline function is run line by line and should only process a single multiline element at a time.
                return [
                    true,
                    endLineIndex
                ];
            }
            break;
        }
    }
    // No multiline transformer handled this line successfully
    return [
        false,
        startLineIndex
    ];
}
function $importBlocks(lineText, rootNode, elementTransformers, textFormatTransformersIndex, textMatchTransformers, shouldPreserveNewLines) {
    const textNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(lineText);
    const elementNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])();
    elementNode.append(textNode);
    rootNode.append(elementNode);
    for (const { regExp, replace } of elementTransformers){
        const match = lineText.match(regExp);
        if (match) {
            textNode.setTextContent(lineText.slice(match[0].length));
            if (replace(elementNode, [
                textNode
            ], match, true) !== false) {
                break;
            }
        }
    }
    importTextTransformers(textNode, textFormatTransformersIndex, textMatchTransformers);
    // If no transformer found and we left with original paragraph node
    // can check if its content can be appended to the previous node
    // if it's a paragraph, quote or list
    if (elementNode.isAttached() && lineText.length > 0) {
        const previousNode = elementNode.getPreviousSibling();
        if (!shouldPreserveNewLines && (// Only append if we're not preserving newlines
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isParagraphNode"])(previousNode) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isQuoteNode"])(previousNode) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isListNode"])(previousNode))) {
            let targetNode = previousNode;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isListNode"])(previousNode)) {
                const lastDescendant = previousNode.getLastDescendant();
                if (lastDescendant == null) {
                    targetNode = null;
                } else {
                    targetNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$findMatchingParent"])(lastDescendant, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isListItemNode"]);
                }
            }
            if (targetNode != null && targetNode.getTextContentSize() > 0) {
                targetNode.splice(targetNode.getChildrenSize(), 0, [
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createLineBreakNode"])(),
                    ...elementNode.getChildren()
                ]);
                elementNode.remove();
            }
        }
    }
}
function createTextFormatTransformersIndex(textTransformers) {
    const transformersByTag = {};
    const fullMatchRegExpByTag = {};
    const openTagsRegExp = [];
    const escapeRegExp = `(?<![\\\\])`;
    for (const transformer of textTransformers){
        const { tag } = transformer;
        transformersByTag[tag] = transformer;
        const tagRegExp = tag.replace(/(\*|\^|\+)/g, '\\$1');
        openTagsRegExp.push(tagRegExp);
        // Single-char tag (e.g. "*"),
        if (tag.length === 1) {
            fullMatchRegExpByTag[tag] = new RegExp(`(?<![\\\\${tagRegExp}])(${tagRegExp})((\\\\${tagRegExp})?.*?[^${tagRegExp}\\s](\\\\${tagRegExp})?)((?<!\\\\)|(?<=\\\\\\\\))(${tagRegExp})(?![\\\\${tagRegExp}])`);
        } else {
            // Multi‐char tags (e.g. "**")
            fullMatchRegExpByTag[tag] = new RegExp(`(?<!\\\\)(${tagRegExp})((\\\\${tagRegExp})?.*?[^\\s](\\\\${tagRegExp})?)((?<!\\\\)|(?<=\\\\\\\\))(${tagRegExp})(?!\\\\)`);
        }
    }
    return {
        // Reg exp to find open tag + content + close tag
        fullMatchRegExpByTag,
        // Regexp to locate *any* potential opening tag (longest first).
        openTagsRegExp: new RegExp(`${escapeRegExp}(${openTagsRegExp.join('|')})`, 'g'),
        transformersByTag
    };
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Do not require this module directly! Use normal `invariant` calls.
function formatDevErrorMessage(message) {
    throw new Error(message);
}
function runElementTransformers(parentNode, anchorNode, anchorOffset, elementTransformers) {
    const grandParentNode = parentNode.getParent();
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootOrShadowRoot"])(grandParentNode) || parentNode.getFirstChild() !== anchorNode) {
        return false;
    }
    const textContent = anchorNode.getTextContent();
    // Checking for anchorOffset position to prevent any checks for cases when caret is too far
    // from a line start to be a part of block-level markdown trigger.
    //
    // TODO:
    // Can have a quick check if caret is close enough to the beginning of the string (e.g. offset less than 10-20)
    // since otherwise it won't be a markdown shortcut, but tables are exception
    if (textContent[anchorOffset - 1] !== ' ') {
        return false;
    }
    for (const { regExp, replace } of elementTransformers){
        const match = textContent.match(regExp);
        if (match && match[0].length === (match[0].endsWith(' ') ? anchorOffset : anchorOffset - 1)) {
            const nextSiblings = anchorNode.getNextSiblings();
            const [leadingNode, remainderNode] = anchorNode.splitText(anchorOffset);
            const siblings = remainderNode ? [
                remainderNode,
                ...nextSiblings
            ] : nextSiblings;
            if (replace(parentNode, siblings, match, false) !== false) {
                leadingNode.remove();
                return true;
            }
        }
    }
    return false;
}
function runMultilineElementTransformers(parentNode, anchorNode, anchorOffset, elementTransformers) {
    const grandParentNode = parentNode.getParent();
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRootOrShadowRoot"])(grandParentNode) || parentNode.getFirstChild() !== anchorNode) {
        return false;
    }
    const textContent = anchorNode.getTextContent();
    // Checking for anchorOffset position to prevent any checks for cases when caret is too far
    // from a line start to be a part of block-level markdown trigger.
    //
    // TODO:
    // Can have a quick check if caret is close enough to the beginning of the string (e.g. offset less than 10-20)
    // since otherwise it won't be a markdown shortcut, but tables are exception
    if (textContent[anchorOffset - 1] !== ' ') {
        return false;
    }
    for (const { regExpStart, replace, regExpEnd } of elementTransformers){
        if (regExpEnd && !('optional' in regExpEnd) || regExpEnd && 'optional' in regExpEnd && !regExpEnd.optional) {
            continue;
        }
        const match = textContent.match(regExpStart);
        if (match && match[0].length === (match[0].endsWith(' ') ? anchorOffset : anchorOffset - 1)) {
            const nextSiblings = anchorNode.getNextSiblings();
            const [leadingNode, remainderNode] = anchorNode.splitText(anchorOffset);
            const siblings = remainderNode ? [
                remainderNode,
                ...nextSiblings
            ] : nextSiblings;
            if (replace(parentNode, siblings, match, null, null, false) !== false) {
                leadingNode.remove();
                return true;
            }
        }
    }
    return false;
}
function runTextMatchTransformers(anchorNode, anchorOffset, transformersByTrigger) {
    let textContent = anchorNode.getTextContent();
    const lastChar = textContent[anchorOffset - 1];
    const transformers = transformersByTrigger[lastChar];
    if (transformers == null) {
        return false;
    }
    // If typing in the middle of content, remove the tail to do
    // reg exp match up to a string end (caret position)
    if (anchorOffset < textContent.length) {
        textContent = textContent.slice(0, anchorOffset);
    }
    for (const transformer of transformers){
        if (!transformer.replace || !transformer.regExp) {
            continue;
        }
        const match = textContent.match(transformer.regExp);
        if (match === null) {
            continue;
        }
        const startIndex = match.index || 0;
        const endIndex = startIndex + match[0].length;
        let replaceNode;
        if (startIndex === 0) {
            [replaceNode] = anchorNode.splitText(endIndex);
        } else {
            [, replaceNode] = anchorNode.splitText(startIndex, endIndex);
        }
        replaceNode.selectNext(0, 0);
        transformer.replace(replaceNode, match);
        return true;
    }
    return false;
}
function $runTextFormatTransformers(anchorNode, anchorOffset, textFormatTransformers) {
    const textContent = anchorNode.getTextContent();
    const closeTagEndIndex = anchorOffset - 1;
    const closeChar = textContent[closeTagEndIndex];
    // Quick check if we're possibly at the end of inline markdown style
    const matchers = textFormatTransformers[closeChar];
    if (!matchers) {
        return false;
    }
    for (const matcher of matchers){
        const { tag } = matcher;
        const tagLength = tag.length;
        const closeTagStartIndex = closeTagEndIndex - tagLength + 1;
        // If tag is not single char check if rest of it matches with text content
        if (tagLength > 1) {
            if (!isEqualSubString(textContent, closeTagStartIndex, tag, 0, tagLength)) {
                continue;
            }
        }
        // Space before closing tag cancels inline markdown
        if (textContent[closeTagStartIndex - 1] === ' ') {
            continue;
        }
        // Some tags can not be used within words, hence should have newline/space/punctuation after it
        const afterCloseTagChar = textContent[closeTagEndIndex + 1];
        if (matcher.intraword === false && afterCloseTagChar && !PUNCTUATION_OR_SPACE.test(afterCloseTagChar)) {
            continue;
        }
        const closeNode = anchorNode;
        let openNode = closeNode;
        let openTagStartIndex = getOpenTagStartIndex(textContent, closeTagStartIndex, tag);
        // Go through text node siblings and search for opening tag
        // if haven't found it within the same text node as closing tag
        let sibling = openNode;
        while(openTagStartIndex < 0 && (sibling = sibling.getPreviousSibling())){
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(sibling)) {
                break;
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(sibling)) {
                if (sibling.hasFormat('code')) {
                    continue;
                }
                const siblingTextContent = sibling.getTextContent();
                openNode = sibling;
                openTagStartIndex = getOpenTagStartIndex(siblingTextContent, siblingTextContent.length, tag);
            }
        }
        // Opening tag is not found
        if (openTagStartIndex < 0) {
            continue;
        }
        // No content between opening and closing tag
        if (openNode === closeNode && openTagStartIndex + tagLength === closeTagStartIndex) {
            continue;
        }
        // Checking longer tags for repeating chars (e.g. *** vs **)
        const prevOpenNodeText = openNode.getTextContent();
        if (openTagStartIndex > 0 && prevOpenNodeText[openTagStartIndex - 1] === closeChar) {
            continue;
        }
        // Some tags can not be used within words, hence should have newline/space/punctuation before it
        const beforeOpenTagChar = prevOpenNodeText[openTagStartIndex - 1];
        if (matcher.intraword === false && beforeOpenTagChar && !PUNCTUATION_OR_SPACE.test(beforeOpenTagChar)) {
            continue;
        }
        // Clean text from opening and closing tags (starting from closing tag
        // to prevent any offset shifts if we start from opening one)
        const prevCloseNodeText = closeNode.getTextContent();
        const closeNodeText = prevCloseNodeText.slice(0, closeTagStartIndex) + prevCloseNodeText.slice(closeTagEndIndex + 1);
        closeNode.setTextContent(closeNodeText);
        const openNodeText = openNode === closeNode ? closeNodeText : prevOpenNodeText;
        openNode.setTextContent(openNodeText.slice(0, openTagStartIndex) + openNodeText.slice(openTagStartIndex + tagLength));
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        const nextSelection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createRangeSelection"])();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setSelection"])(nextSelection);
        // Adjust offset based on deleted chars
        const newOffset = closeTagEndIndex - tagLength * (openNode === closeNode ? 2 : 1) + 1;
        nextSelection.anchor.set(openNode.__key, openTagStartIndex, 'text');
        nextSelection.focus.set(closeNode.__key, newOffset, 'text');
        // Apply formatting to selected text
        for (const format of matcher.format){
            if (!nextSelection.hasFormat(format)) {
                nextSelection.formatText(format);
            }
        }
        // Collapse selection up to the focus point
        nextSelection.anchor.set(nextSelection.focus.key, nextSelection.focus.offset, nextSelection.focus.type);
        // Remove formatting from collapsed selection
        for (const format of matcher.format){
            if (nextSelection.hasFormat(format)) {
                nextSelection.toggleFormat(format);
            }
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            nextSelection.format = selection.format;
        }
        return true;
    }
    return false;
}
function getOpenTagStartIndex(string, maxIndex, tag) {
    const tagLength = tag.length;
    for(let i = maxIndex; i >= tagLength; i--){
        const startIndex = i - tagLength;
        if (isEqualSubString(string, startIndex, tag, 0, tagLength) && // Space after opening tag cancels transformation
        string[startIndex + tagLength] !== ' ') {
            return startIndex;
        }
    }
    return -1;
}
function isEqualSubString(stringA, aStart, stringB, bStart, length) {
    for(let i = 0; i < length; i++){
        if (stringA[aStart + i] !== stringB[bStart + i]) {
            return false;
        }
    }
    return true;
}
function registerMarkdownShortcuts(editor, transformers = TRANSFORMERS) {
    const byType = transformersByType(transformers);
    const textFormatTransformersByTrigger = indexBy(byType.textFormat, ({ tag })=>tag[tag.length - 1]);
    const textMatchTransformersByTrigger = indexBy(byType.textMatch, ({ trigger })=>trigger);
    for (const transformer of transformers){
        const type = transformer.type;
        if (type === 'element' || type === 'text-match' || type === 'multiline-element') {
            const dependencies = transformer.dependencies;
            for (const node of dependencies){
                if (!editor.hasNode(node)) {
                    {
                        formatDevErrorMessage(`MarkdownShortcuts: missing dependency ${node.getType()} for transformer. Ensure node dependency is included in editor initial config.`);
                    }
                }
            }
        }
    }
    const $transform = (parentNode, anchorNode, anchorOffset)=>{
        if (runElementTransformers(parentNode, anchorNode, anchorOffset, byType.element)) {
            return;
        }
        if (runMultilineElementTransformers(parentNode, anchorNode, anchorOffset, byType.multilineElement)) {
            return;
        }
        if (runTextMatchTransformers(anchorNode, anchorOffset, textMatchTransformersByTrigger)) {
            return;
        }
        $runTextFormatTransformers(anchorNode, anchorOffset, textFormatTransformersByTrigger);
    };
    return editor.registerUpdateListener(({ tags, dirtyLeaves, editorState, prevEditorState })=>{
        // Ignore updates from collaboration and undo/redo (as changes already calculated)
        if (tags.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLABORATION_TAG"]) || tags.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HISTORIC_TAG"])) {
            return;
        }
        // If editor is still composing (i.e. backticks) we must wait before the user confirms the key
        if (editor.isComposing()) {
            return;
        }
        const selection = editorState.read(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"]);
        const prevSelection = prevEditorState.read(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"]);
        // We expect selection to be a collapsed range and not match previous one (as we want
        // to trigger transforms only as user types)
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(prevSelection) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) || !selection.isCollapsed() || selection.is(prevSelection)) {
            return;
        }
        const anchorKey = selection.anchor.key;
        const anchorOffset = selection.anchor.offset;
        const anchorNode = editorState._nodeMap.get(anchorKey);
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(anchorNode) || !dirtyLeaves.has(anchorKey) || anchorOffset !== 1 && anchorOffset > prevSelection.anchor.offset + 1) {
            return;
        }
        editor.update(()=>{
            if (!canContainTransformableMarkdown(anchorNode)) {
                return;
            }
            const parentNode = anchorNode.getParent();
            if (parentNode === null || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$code$2f$LexicalCode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isCodeNode"])(parentNode)) {
                return;
            }
            $transform(parentNode, anchorNode, selection.anchor.offset);
        });
    });
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const ORDERED_LIST_REGEX = /^(\s*)(\d{1,})\.\s/;
const UNORDERED_LIST_REGEX = /^(\s*)[-*+]\s/;
const CHECK_LIST_REGEX = /^(\s*)(?:-\s)?\s?(\[(\s|x)?\])\s/i;
const HEADING_REGEX = /^(#{1,6})\s/;
const QUOTE_REGEX = /^>\s/;
const CODE_START_REGEX = /^[ \t]*```([\w-]+)?/;
const CODE_END_REGEX = /[ \t]*```$/;
const CODE_SINGLE_LINE_REGEX = /^[ \t]*```[^`]+(?:(?:`{1,2}|`{4,})[^`]+)*```(?:[^`]|$)/;
const TABLE_ROW_REG_EXP = /^(?:\|)(.+)(?:\|)\s?$/;
const TABLE_ROW_DIVIDER_REG_EXP = /^(\| ?:?-*:? ?)+\|\s?$/;
const createBlockNode = (createNode)=>{
    return (parentNode, children, match, isImport)=>{
        const node = createNode(match);
        node.append(...children);
        parentNode.replace(node);
        if (!isImport) {
            node.select(0, 0);
        }
    };
};
// Amount of spaces that define indentation level
// TODO: should be an option
const LIST_INDENT_SIZE = 4;
function getIndent(whitespaces) {
    const tabs = whitespaces.match(/\t/g);
    const spaces = whitespaces.match(/ /g);
    let indent = 0;
    if (tabs) {
        indent += tabs.length;
    }
    if (spaces) {
        indent += Math.floor(spaces.length / LIST_INDENT_SIZE);
    }
    return indent;
}
const listReplace = (listType)=>{
    return (parentNode, children, match, isImport)=>{
        const previousNode = parentNode.getPreviousSibling();
        const nextNode = parentNode.getNextSibling();
        const listItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createListItemNode"])(listType === 'check' ? match[3] === 'x' : undefined);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isListNode"])(nextNode) && nextNode.getListType() === listType) {
            const firstChild = nextNode.getFirstChild();
            if (firstChild !== null) {
                firstChild.insertBefore(listItem);
            } else {
                // should never happen, but let's handle gracefully, just in case.
                nextNode.append(listItem);
            }
            parentNode.remove();
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isListNode"])(previousNode) && previousNode.getListType() === listType) {
            previousNode.append(listItem);
            parentNode.remove();
        } else {
            const list = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createListNode"])(listType, listType === 'number' ? Number(match[2]) : undefined);
            list.append(listItem);
            parentNode.replace(list);
        }
        listItem.append(...children);
        if (!isImport) {
            listItem.select(0, 0);
        }
        const indent = getIndent(match[1]);
        if (indent) {
            listItem.setIndent(indent);
        }
    };
};
const listExport = (listNode, exportChildren, depth)=>{
    const output = [];
    const children = listNode.getChildren();
    let index = 0;
    for (const listItemNode of children){
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isListItemNode"])(listItemNode)) {
            if (listItemNode.getChildrenSize() === 1) {
                const firstChild = listItemNode.getFirstChild();
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isListNode"])(firstChild)) {
                    output.push(listExport(firstChild, exportChildren, depth + 1));
                    continue;
                }
            }
            const indent = ' '.repeat(depth * LIST_INDENT_SIZE);
            const listType = listNode.getListType();
            const prefix = listType === 'number' ? `${listNode.getStart() + index}. ` : listType === 'check' ? `- [${listItemNode.getChecked() ? 'x' : ' '}] ` : '- ';
            output.push(indent + prefix + exportChildren(listItemNode));
            index++;
        }
    }
    return output.join('\n');
};
const HEADING = {
    dependencies: [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HeadingNode"]
    ],
    export: (node, exportChildren)=>{
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isHeadingNode"])(node)) {
            return null;
        }
        const level = Number(node.getTag().slice(1));
        return '#'.repeat(level) + ' ' + exportChildren(node);
    },
    regExp: HEADING_REGEX,
    replace: createBlockNode((match)=>{
        const tag = 'h' + match[1].length;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createHeadingNode"])(tag);
    }),
    type: 'element'
};
const QUOTE = {
    dependencies: [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QuoteNode"]
    ],
    export: (node, exportChildren)=>{
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isQuoteNode"])(node)) {
            return null;
        }
        const lines = exportChildren(node).split('\n');
        const output = [];
        for (const line of lines){
            output.push('> ' + line);
        }
        return output.join('\n');
    },
    regExp: QUOTE_REGEX,
    replace: (parentNode, children, _match, isImport)=>{
        if (isImport) {
            const previousNode = parentNode.getPreviousSibling();
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isQuoteNode"])(previousNode)) {
                previousNode.splice(previousNode.getChildrenSize(), 0, [
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createLineBreakNode"])(),
                    ...children
                ]);
                parentNode.remove();
                return;
            }
        }
        const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createQuoteNode"])();
        node.append(...children);
        parentNode.replace(node);
        if (!isImport) {
            node.select(0, 0);
        }
    },
    type: 'element'
};
const CODE = {
    dependencies: [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$code$2f$LexicalCode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CodeNode"]
    ],
    export: (node)=>{
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$code$2f$LexicalCode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isCodeNode"])(node)) {
            return null;
        }
        const textContent = node.getTextContent();
        return '```' + (node.getLanguage() || '') + (textContent ? '\n' + textContent : '') + '\n' + '```';
    },
    regExpEnd: {
        optional: true,
        regExp: CODE_END_REGEX
    },
    regExpStart: CODE_START_REGEX,
    replace: (rootNode, children, startMatch, endMatch, linesInBetween, isImport)=>{
        let codeBlockNode;
        let code;
        if (!children && linesInBetween) {
            if (linesInBetween.length === 1) {
                // Single-line code blocks
                if (endMatch) {
                    // End match on same line. Example: ```markdown hello```. markdown should not be considered the language here.
                    codeBlockNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$code$2f$LexicalCode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createCodeNode"])();
                    code = startMatch[1] + linesInBetween[0];
                } else {
                    // No end match. We should assume the language is next to the backticks and that code will be typed on the next line in the future
                    codeBlockNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$code$2f$LexicalCode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createCodeNode"])(startMatch[1]);
                    code = linesInBetween[0].startsWith(' ') ? linesInBetween[0].slice(1) : linesInBetween[0];
                }
            } else {
                // Treat multi-line code blocks as if they always have an end match
                codeBlockNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$code$2f$LexicalCode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createCodeNode"])(startMatch[1]);
                if (linesInBetween[0].trim().length === 0) {
                    // Filter out all start and end lines that are length 0 until we find the first line with content
                    while(linesInBetween.length > 0 && !linesInBetween[0].length){
                        linesInBetween.shift();
                    }
                } else {
                    // The first line already has content => Remove the first space of the line if it exists
                    linesInBetween[0] = linesInBetween[0].startsWith(' ') ? linesInBetween[0].slice(1) : linesInBetween[0];
                }
                // Filter out all end lines that are length 0 until we find the last line with content
                while(linesInBetween.length > 0 && !linesInBetween[linesInBetween.length - 1].length){
                    linesInBetween.pop();
                }
                code = linesInBetween.join('\n');
            }
            const textNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(code);
            codeBlockNode.append(textNode);
            rootNode.append(codeBlockNode);
        } else if (children) {
            createBlockNode((match)=>{
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$code$2f$LexicalCode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createCodeNode"])(match ? match[1] : undefined);
            })(rootNode, children, startMatch, isImport);
        }
    },
    type: 'multiline-element'
};
const UNORDERED_LIST = {
    dependencies: [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListNode"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListItemNode"]
    ],
    export: (node, exportChildren)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isListNode"])(node) ? listExport(node, exportChildren, 0) : null;
    },
    regExp: UNORDERED_LIST_REGEX,
    replace: listReplace('bullet'),
    type: 'element'
};
const CHECK_LIST = {
    dependencies: [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListNode"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListItemNode"]
    ],
    export: (node, exportChildren)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isListNode"])(node) ? listExport(node, exportChildren, 0) : null;
    },
    regExp: CHECK_LIST_REGEX,
    replace: listReplace('check'),
    type: 'element'
};
const ORDERED_LIST = {
    dependencies: [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListNode"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListItemNode"]
    ],
    export: (node, exportChildren)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isListNode"])(node) ? listExport(node, exportChildren, 0) : null;
    },
    regExp: ORDERED_LIST_REGEX,
    replace: listReplace('number'),
    type: 'element'
};
const INLINE_CODE = {
    format: [
        'code'
    ],
    tag: '`',
    type: 'text-format'
};
const HIGHLIGHT = {
    format: [
        'highlight'
    ],
    tag: '==',
    type: 'text-format'
};
const BOLD_ITALIC_STAR = {
    format: [
        'bold',
        'italic'
    ],
    tag: '***',
    type: 'text-format'
};
const BOLD_ITALIC_UNDERSCORE = {
    format: [
        'bold',
        'italic'
    ],
    intraword: false,
    tag: '___',
    type: 'text-format'
};
const BOLD_STAR = {
    format: [
        'bold'
    ],
    tag: '**',
    type: 'text-format'
};
const BOLD_UNDERSCORE = {
    format: [
        'bold'
    ],
    intraword: false,
    tag: '__',
    type: 'text-format'
};
const STRIKETHROUGH = {
    format: [
        'strikethrough'
    ],
    tag: '~~',
    type: 'text-format'
};
const ITALIC_STAR = {
    format: [
        'italic'
    ],
    tag: '*',
    type: 'text-format'
};
const ITALIC_UNDERSCORE = {
    format: [
        'italic'
    ],
    intraword: false,
    tag: '_',
    type: 'text-format'
};
// Order of text transformers matters:
//
// - code should go first as it prevents any transformations inside
// - then longer tags match (e.g. ** or __ should go before * or _)
const LINK = {
    dependencies: [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LinkNode"]
    ],
    export: (node, exportChildren, exportFormat)=>{
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLinkNode"])(node) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isAutoLinkNode"])(node)) {
            return null;
        }
        const title = node.getTitle();
        const textContent = exportChildren(node);
        const linkContent = title ? `[${textContent}](${node.getURL()} "${title}")` : `[${textContent}](${node.getURL()})`;
        return linkContent;
    },
    importRegExp: /(?:\[(.*?)\])(?:\((?:([^()\s]+)(?:\s"((?:[^"]*\\")*[^"]*)"\s*)?)\))/,
    regExp: /(?:\[(.*?)\])(?:\((?:([^()\s]+)(?:\s"((?:[^"]*\\")*[^"]*)"\s*)?)\))$/,
    replace: (textNode, match)=>{
        const [, linkText, linkUrl, linkTitle] = match;
        const linkNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createLinkNode"])(linkUrl, {
            title: linkTitle
        });
        const openBracketAmount = linkText.split('[').length - 1;
        const closeBracketAmount = linkText.split(']').length - 1;
        let parsedLinkText = linkText;
        let outsideLinkText = '';
        if (openBracketAmount < closeBracketAmount) {
            return;
        } else if (openBracketAmount > closeBracketAmount) {
            const linkTextParts = linkText.split('[');
            outsideLinkText = '[' + linkTextParts[0];
            parsedLinkText = linkTextParts.slice(1).join('[');
        }
        const linkTextNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(parsedLinkText);
        linkTextNode.setFormat(textNode.getFormat());
        linkNode.append(linkTextNode);
        textNode.replace(linkNode);
        if (outsideLinkText) {
            linkNode.insertBefore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(outsideLinkText));
        }
        return linkTextNode;
    },
    trigger: ')',
    type: 'text-match'
};
function normalizeMarkdown(input, shouldMergeAdjacentLines = false) {
    const lines = input.split('\n');
    let inCodeBlock = false;
    const sanitizedLines = [];
    for(let i = 0; i < lines.length; i++){
        const line = lines[i];
        const lastLine = sanitizedLines[sanitizedLines.length - 1];
        // Code blocks of ```single line``` don't toggle the inCodeBlock flag
        if (CODE_SINGLE_LINE_REGEX.test(line)) {
            sanitizedLines.push(line);
            continue;
        }
        // Detect the start or end of a code block
        if (CODE_START_REGEX.test(line) || CODE_END_REGEX.test(line)) {
            inCodeBlock = !inCodeBlock;
            sanitizedLines.push(line);
            continue;
        }
        // If we are inside a code block, keep the line unchanged
        if (inCodeBlock) {
            sanitizedLines.push(line);
            continue;
        }
        // In markdown the concept of "empty paragraphs" does not exist.
        // Blocks must be separated by an empty line. Non-empty adjacent lines must be merged.
        if (line === '' || lastLine === '' || !lastLine || HEADING_REGEX.test(lastLine) || HEADING_REGEX.test(line) || QUOTE_REGEX.test(line) || ORDERED_LIST_REGEX.test(line) || UNORDERED_LIST_REGEX.test(line) || CHECK_LIST_REGEX.test(line) || TABLE_ROW_REG_EXP.test(line) || TABLE_ROW_DIVIDER_REG_EXP.test(line) || !shouldMergeAdjacentLines) {
            sanitizedLines.push(line);
        } else {
            sanitizedLines[sanitizedLines.length - 1] = lastLine + line;
        }
    }
    return sanitizedLines.join('\n');
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const ELEMENT_TRANSFORMERS = [
    HEADING,
    QUOTE,
    UNORDERED_LIST,
    ORDERED_LIST
];
const MULTILINE_ELEMENT_TRANSFORMERS = [
    CODE
];
// Order of text format transformers matters:
//
// - code should go first as it prevents any transformations inside
// - then longer tags match (e.g. ** or __ should go before * or _)
const TEXT_FORMAT_TRANSFORMERS = [
    INLINE_CODE,
    BOLD_ITALIC_STAR,
    BOLD_ITALIC_UNDERSCORE,
    BOLD_STAR,
    BOLD_UNDERSCORE,
    HIGHLIGHT,
    ITALIC_STAR,
    ITALIC_UNDERSCORE,
    STRIKETHROUGH
];
const TEXT_MATCH_TRANSFORMERS = [
    LINK
];
const TRANSFORMERS = [
    ...ELEMENT_TRANSFORMERS,
    ...MULTILINE_ELEMENT_TRANSFORMERS,
    ...TEXT_FORMAT_TRANSFORMERS,
    ...TEXT_MATCH_TRANSFORMERS
];
/**
 * Renders markdown from a string. The selection is moved to the start after the operation.
 *
 *  @param {boolean} [shouldPreserveNewLines] By setting this to true, new lines will be preserved between conversions
 *  @param {boolean} [shouldMergeAdjacentLines] By setting this to true, adjacent non empty lines will be merged according to commonmark spec: https://spec.commonmark.org/0.24/#example-177. Not applicable if shouldPreserveNewLines = true.
 */ function $convertFromMarkdownString(markdown, transformers = TRANSFORMERS, node, shouldPreserveNewLines = false, shouldMergeAdjacentLines = false) {
    const sanitizedMarkdown = shouldPreserveNewLines ? markdown : normalizeMarkdown(markdown, shouldMergeAdjacentLines);
    const importMarkdown = createMarkdownImport(transformers, shouldPreserveNewLines);
    return importMarkdown(sanitizedMarkdown, node);
}
/**
 * Renders string from markdown. The selection is moved to the start after the operation.
 */ function $convertToMarkdownString(transformers = TRANSFORMERS, node, shouldPreserveNewLines = false) {
    const exportMarkdown = createMarkdownExport(transformers, shouldPreserveNewLines);
    return exportMarkdown(node);
}
;
}}),
}]);

//# sourceMappingURL=node_modules_%40lexical_62658034._.js.map