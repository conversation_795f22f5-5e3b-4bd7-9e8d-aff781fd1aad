{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/turtle.js"], "sourcesContent": ["var curPunc;\n\nfunction wordRegexp(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n}\nvar ops = wordRegexp([]);\nvar keywords = wordRegexp([\"@prefix\", \"@base\", \"a\"]);\nvar operatorChars = /[*+\\-<>=&|]/;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  curPunc = null;\n  if (ch == \"<\" && !stream.match(/^[\\s\\u00a0=]/, false)) {\n    stream.match(/^[^\\s\\u00a0>]*>?/);\n    return \"atom\";\n  }\n  else if (ch == \"\\\"\" || ch == \"'\") {\n    state.tokenize = tokenLiteral(ch);\n    return state.tokenize(stream, state);\n  }\n  else if (/[{}\\(\\),\\.;\\[\\]]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  }\n  else if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  else if (operatorChars.test(ch)) {\n    stream.eatWhile(operatorChars);\n    return null;\n  }\n  else if (ch == \":\") {\n    return \"operator\";\n  } else {\n    stream.eatWhile(/[_\\w\\d]/);\n    if(stream.peek() == \":\") {\n      return \"variableName.special\";\n    } else {\n      var word = stream.current();\n\n      if(keywords.test(word)) {\n        return \"meta\";\n      }\n\n      if(ch >= \"A\" && ch <= \"Z\") {\n        return \"comment\";\n      } else {\n        return \"keyword\";\n      }\n    }\n    var word = stream.current();\n    if (ops.test(word))\n      return null;\n    else if (keywords.test(word))\n      return \"meta\";\n    else\n      return \"variable\";\n  }\n}\n\nfunction tokenLiteral(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    return \"string\";\n  };\n}\n\nfunction pushContext(state, type, col) {\n  state.context = {prev: state.context, indent: state.indent, col: col, type: type};\n}\nfunction popContext(state) {\n  state.indent = state.context.indent;\n  state.context = state.context.prev;\n}\n\nexport const turtle = {\n  name: \"turtle\",\n  startState: function() {\n    return {tokenize: tokenBase,\n            context: null,\n            indent: 0,\n            col: 0};\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if (state.context && state.context.align == null) state.context.align = false;\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n\n    if (style != \"comment\" && state.context && state.context.align == null && state.context.type != \"pattern\") {\n      state.context.align = true;\n    }\n\n    if (curPunc == \"(\") pushContext(state, \")\", stream.column());\n    else if (curPunc == \"[\") pushContext(state, \"]\", stream.column());\n    else if (curPunc == \"{\") pushContext(state, \"}\", stream.column());\n    else if (/[\\]\\}\\)]/.test(curPunc)) {\n      while (state.context && state.context.type == \"pattern\") popContext(state);\n      if (state.context && curPunc == state.context.type) popContext(state);\n    }\n    else if (curPunc == \".\" && state.context && state.context.type == \"pattern\") popContext(state);\n    else if (/atom|string|variable/.test(style) && state.context) {\n      if (/[\\}\\]]/.test(state.context.type))\n        pushContext(state, \"pattern\", stream.column());\n      else if (state.context.type == \"pattern\" && !state.context.align) {\n        state.context.align = true;\n        state.context.col = stream.column();\n      }\n    }\n\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var firstChar = textAfter && textAfter.charAt(0);\n    var context = state.context;\n    if (/[\\]\\}]/.test(firstChar))\n      while (context && context.type == \"pattern\") context = context.prev;\n\n    var closing = context && firstChar == context.type;\n    if (!context)\n      return 0;\n    else if (context.type == \"pattern\")\n      return context.col;\n    else if (context.align)\n      return context.col + (closing ? 0 : 1);\n    else\n      return context.indent + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI;AAEJ,SAAS,WAAW,KAAK;IACvB,OAAO,IAAI,OAAO,SAAS,MAAM,IAAI,CAAC,OAAO,MAAM;AACrD;AACA,IAAI,MAAM,WAAW,EAAE;AACvB,IAAI,WAAW,WAAW;IAAC;IAAW;IAAS;CAAI;AACnD,IAAI,gBAAgB;AAEpB,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IACpB,UAAU;IACV,IAAI,MAAM,OAAO,CAAC,OAAO,KAAK,CAAC,gBAAgB,QAAQ;QACrD,OAAO,KAAK,CAAC;QACb,OAAO;IACT,OACK,IAAI,MAAM,QAAQ,MAAM,KAAK;QAChC,MAAM,QAAQ,GAAG,aAAa;QAC9B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC,OACK,IAAI,mBAAmB,IAAI,CAAC,KAAK;QACpC,UAAU;QACV,OAAO;IACT,OACK,IAAI,MAAM,KAAK;QAClB,OAAO,SAAS;QAChB,OAAO;IACT,OACK,IAAI,cAAc,IAAI,CAAC,KAAK;QAC/B,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OACK,IAAI,MAAM,KAAK;QAClB,OAAO;IACT,OAAO;QACL,OAAO,QAAQ,CAAC;QAChB,IAAG,OAAO,IAAI,MAAM,KAAK;YACvB,OAAO;QACT,OAAO;YACL,IAAI,OAAO,OAAO,OAAO;YAEzB,IAAG,SAAS,IAAI,CAAC,OAAO;gBACtB,OAAO;YACT;YAEA,IAAG,MAAM,OAAO,MAAM,KAAK;gBACzB,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;;QACA,IAAI;IAON;AACF;AAEA,SAAS,aAAa,KAAK;IACzB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO;QACrB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;YACnC,IAAI,MAAM,SAAS,CAAC,SAAS;gBAC3B,MAAM,QAAQ,GAAG;gBACjB;YACF;YACA,UAAU,CAAC,WAAW,MAAM;QAC9B;QACA,OAAO;IACT;AACF;AAEA,SAAS,YAAY,KAAK,EAAE,IAAI,EAAE,GAAG;IACnC,MAAM,OAAO,GAAG;QAAC,MAAM,MAAM,OAAO;QAAE,QAAQ,MAAM,MAAM;QAAE,KAAK;QAAK,MAAM;IAAI;AAClF;AACA,SAAS,WAAW,KAAK;IACvB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM;IACnC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;AACpC;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,YAAY;QACV,OAAO;YAAC,UAAU;YACV,SAAS;YACT,QAAQ;YACR,KAAK;QAAC;IAChB;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,MAAM,MAAM,OAAO,CAAC,KAAK,GAAG;YACxE,MAAM,MAAM,GAAG,OAAO,WAAW;QACnC;QACA,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QAEnC,IAAI,SAAS,aAAa,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,QAAQ,MAAM,OAAO,CAAC,IAAI,IAAI,WAAW;YACzG,MAAM,OAAO,CAAC,KAAK,GAAG;QACxB;QAEA,IAAI,WAAW,KAAK,YAAY,OAAO,KAAK,OAAO,MAAM;aACpD,IAAI,WAAW,KAAK,YAAY,OAAO,KAAK,OAAO,MAAM;aACzD,IAAI,WAAW,KAAK,YAAY,OAAO,KAAK,OAAO,MAAM;aACzD,IAAI,WAAW,IAAI,CAAC,UAAU;YACjC,MAAO,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,UAAW,WAAW;YACpE,IAAI,MAAM,OAAO,IAAI,WAAW,MAAM,OAAO,CAAC,IAAI,EAAE,WAAW;QACjE,OACK,IAAI,WAAW,OAAO,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,WAAW,WAAW;aACnF,IAAI,uBAAuB,IAAI,CAAC,UAAU,MAAM,OAAO,EAAE;YAC5D,IAAI,SAAS,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,GAClC,YAAY,OAAO,WAAW,OAAO,MAAM;iBACxC,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,aAAa,CAAC,MAAM,OAAO,CAAC,KAAK,EAAE;gBAChE,MAAM,OAAO,CAAC,KAAK,GAAG;gBACtB,MAAM,OAAO,CAAC,GAAG,GAAG,OAAO,MAAM;YACnC;QACF;QAEA,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,YAAY,aAAa,UAAU,MAAM,CAAC;QAC9C,IAAI,UAAU,MAAM,OAAO;QAC3B,IAAI,SAAS,IAAI,CAAC,YAChB,MAAO,WAAW,QAAQ,IAAI,IAAI,UAAW,UAAU,QAAQ,IAAI;QAErE,IAAI,UAAU,WAAW,aAAa,QAAQ,IAAI;QAClD,IAAI,CAAC,SACH,OAAO;aACJ,IAAI,QAAQ,IAAI,IAAI,WACvB,OAAO,QAAQ,GAAG;aACf,IAAI,QAAQ,KAAK,EACpB,OAAO,QAAQ,GAAG,GAAG,CAAC,UAAU,IAAI,CAAC;aAErC,OAAO,QAAQ,MAAM,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI;IAClD;IAEA,cAAc;QACZ,eAAe;YAAC,MAAM;QAAG;IAC3B;AACF", "ignoreList": [0], "debugId": null}}]}