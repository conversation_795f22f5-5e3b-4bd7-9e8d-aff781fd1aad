'use client';

import React from 'react';
import { toast } from 'sonner';

// Import MDX Editor and plugins dynamically to avoid SSR issues
import {
  MDXEditor,
  headingsPlugin,
  listsPlugin,
  quotePlugin,
  thematicBreakPlugin,
  markdownShortcutPlugin,
  linkPlugin,
  linkDialogPlugin,
  imagePlugin,
  tablePlugin,
  codeBlockPlugin,
  codeMirrorPlugin,
  toolbarPlugin,
  UndoRedo,
  BoldItalicUnderlineToggles,
  CreateLink,
  InsertImage,
  InsertTable,
  InsertThematicBreak,
  ListsToggle,
  BlockTypeSelect,
  CodeToggle,
  InsertCodeBlock,
} from '@mdxeditor/editor';

interface MDXEditorWrapperProps {
  markdown: string;
  onChange: (markdown: string) => void;
  placeholder?: string;
  className?: string;
}

export default function MDXEditorWrapper({
  markdown,
  onChange,
  placeholder = "Enter your content here...",
  className = "min-h-[200px]",
}: MDXEditorWrapperProps) {
  return (
    <MDXEditor
      markdown={markdown}
      onChange={onChange}
      contentEditableClassName='prose max-w-none'
      plugins={[
        headingsPlugin(),
        listsPlugin(),
        quotePlugin(),
        thematicBreakPlugin(),
        markdownShortcutPlugin(),
        linkPlugin(),
        linkDialogPlugin(),
        imagePlugin({
          imageUploadHandler: async (image) => {
            // Handle image upload
            try {
              const response = await fetch(`/api/upload?filename=${image.name}`, {
                method: 'POST',
                body: image,
              });

              if (!response.ok) {
                throw new Error('Upload failed');
              }

              const result = await response.json();
              return result.url;
            } catch (error) {
              console.error('Image upload failed:', error);
              toast.error('Failed to upload image');
              return '';
            }
          },
        }),
        tablePlugin(),
        codeBlockPlugin({ defaultCodeBlockLanguage: 'javascript' }),
        codeMirrorPlugin({
          codeBlockLanguages: {
            javascript: 'JavaScript',
            typescript: 'TypeScript',
            python: 'Python',
            html: 'HTML',
            css: 'CSS',
            json: 'JSON',
            markdown: 'Markdown',
          },
        }),
        toolbarPlugin({
          toolbarContents: () => (
            <>
              <UndoRedo />
              <BoldItalicUnderlineToggles />
              <CodeToggle />
              <BlockTypeSelect />
              <CreateLink />
              <InsertImage />
              <ListsToggle />
              <InsertTable />
              <InsertThematicBreak />
              <InsertCodeBlock />
            </>
          ),
        }),
      ]}
      placeholder={placeholder}
      className={className}
    />
  );
}
