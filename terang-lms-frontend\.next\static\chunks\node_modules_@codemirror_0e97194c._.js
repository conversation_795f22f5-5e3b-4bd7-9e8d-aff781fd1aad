(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@codemirror/lang-cpp/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_dee9cca3._.js",
  "static/chunks/node_modules_@codemirror_lang-cpp_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-cpp/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-css/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_67c554fa._.js",
  "static/chunks/node_modules_@codemirror_lang-css_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-css/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-go/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_2a0fc8ce._.js",
  "static/chunks/node_modules_@codemirror_lang-go_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-go/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-html/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_45ba6756._.js",
  "static/chunks/node_modules_@codemirror_lang-html_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-html/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-java/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_da57ded4._.js",
  "static/chunks/node_modules_@codemirror_lang-java_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-java/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-javascript/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_85fa6b42._.js",
  "static/chunks/node_modules_@codemirror_lang-javascript_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-javascript/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-json/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_d087dd50._.js",
  "static/chunks/node_modules_@codemirror_lang-json_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-json/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-less/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_2b58d8df._.js",
  "static/chunks/node_modules_@codemirror_lang-less_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-less/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-liquid/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_acf41713._.js",
  "static/chunks/node_modules_@codemirror_lang-liquid_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-liquid/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-markdown/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_4d58d369._.js",
  "static/chunks/node_modules_@codemirror_lang-markdown_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-markdown/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-php/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_abdea744._.js",
  "static/chunks/node_modules_@codemirror_lang-php_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-php/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-python/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_cffcf416._.js",
  "static/chunks/node_modules_@codemirror_lang-python_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-python/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-rust/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_345d4462._.js",
  "static/chunks/node_modules_@codemirror_lang-rust_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-rust/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-sass/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_2b13a0ab._.js",
  "static/chunks/node_modules_@codemirror_lang-sass_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-sass/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-wast/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_d0790815._.js",
  "static/chunks/node_modules_@codemirror_lang-wast_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-wast/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-xml/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_7f224712._.js",
  "static/chunks/node_modules_@codemirror_lang-xml_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-xml/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-yaml/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_39069794._.js",
  "static/chunks/node_modules_@codemirror_lang-yaml_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-yaml/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/apl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_apl_0e210544.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_apl_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/apl.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/asciiarmor.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_asciiarmor_0b2ddc94.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_asciiarmor_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/asciiarmor.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/asn1.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_asn1_d181be25.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_asn1_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/asn1.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/asterisk.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_asterisk_ce3905be.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_asterisk_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/asterisk.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/brainfuck.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_brainfuck_9205343d.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_brainfuck_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/brainfuck.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/cobol.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_cobol_0b8089eb.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_cobol_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/cobol.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/clike.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_clike_a41ef4d2.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_clike_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/clike.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/clojure.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_clojure_2db518ca.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_clojure_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/clojure.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/css.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_css_deae940c.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_css_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/css.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/cmake.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_cmake_46ca3317.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_cmake_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/cmake.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/coffeescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_coffeescript_bd9ce5c7.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_coffeescript_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/coffeescript.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/commonlisp.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_commonlisp_9510cb7e.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_commonlisp_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/commonlisp.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/cypher.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_cypher_0eee1e87.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_cypher_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/cypher.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/python.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_python_61e7f99b.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_python_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/python.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/crystal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_crystal_3e9c581d.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_crystal_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/crystal.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/d.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_d_c52068a5.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_d_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/d.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/diff.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_diff_ac7e1214.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_diff_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/diff.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/dockerfile.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_64c4d459._.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_dockerfile_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/dockerfile.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/dtd.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_dtd_5a811ba3.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_dtd_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/dtd.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/dylan.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_dylan_1b8f1c12.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_dylan_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/dylan.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/ebnf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ebnf_c35d6a30.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ebnf_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/ebnf.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/ecl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ecl_c60b34ed.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ecl_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/ecl.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/eiffel.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_eiffel_5c79451f.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_eiffel_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/eiffel.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/elm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_elm_9a5c11d6.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_elm_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/elm.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/erlang.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_erlang_1b5a2fdc.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_erlang_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/erlang.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/sql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_sql_5a9ee6a6.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_sql_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/sql.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/factor.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_19ef893f._.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_factor_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/factor.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/fcl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_fcl_d224ff83.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_fcl_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/fcl.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/forth.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_forth_c439df08.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_forth_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/forth.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/fortran.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_fortran_89131d90.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_fortran_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/fortran.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/mllike.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mllike_50dee595.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mllike_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/mllike.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/gas.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_gas_a716f104.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_gas_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/gas.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/gherkin.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_gherkin_8c7b3451.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_gherkin_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/gherkin.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/groovy.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_groovy_f83295cc.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_groovy_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/groovy.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/haskell.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_haskell_909de233.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_haskell_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/haskell.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/haxe.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_haxe_09e4f812.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_haxe_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/haxe.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/http.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_http_de2ebcf7.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_http_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/http.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/idl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_idl_e4f9f299.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_idl_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/idl.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/javascript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_javascript_7a23060a.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_javascript_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/javascript.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/jinja2.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_jinja2_2fe3d3a1.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_jinja2_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/jinja2.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/julia.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_julia_724ab9c9.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_julia_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/julia.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/livescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_livescript_1531f815.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_livescript_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/livescript.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/lua.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_lua_aeeea910.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_lua_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/lua.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/mirc.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mirc_1be5c93a.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mirc_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/mirc.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/mathematica.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mathematica_e61542a0.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mathematica_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/mathematica.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/modelica.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_modelica_de2e0324.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_modelica_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/modelica.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/mumps.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mumps_6fa49bb0.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mumps_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/mumps.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/mbox.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mbox_4f958241.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mbox_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/mbox.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/nginx.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_nginx_5da52c81.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_nginx_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/nginx.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/nsis.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_d81f05ab._.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_nsis_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/nsis.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/ntriples.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ntriples_6f59abaa.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ntriples_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/ntriples.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/octave.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_octave_e4067fe2.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_octave_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/octave.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/oz.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_oz_3bbc1140.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_oz_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/oz.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/pascal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_pascal_1710a2a9.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_pascal_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/pascal.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/perl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_perl_5e6e644f.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_perl_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/perl.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/pig.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_pig_04611911.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_pig_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/pig.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/powershell.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_powershell_fa2b8613.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_powershell_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/powershell.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/properties.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_properties_6273cf14.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_properties_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/properties.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/protobuf.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_protobuf_6ad73e02.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_protobuf_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/protobuf.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/pug.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_608d7d96._.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_pug_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/pug.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/puppet.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_puppet_ead0447c.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_puppet_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/puppet.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/q.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_q_9bc7c840.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_q_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/q.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/r.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_r_0c569dc8.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_r_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/r.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/rpm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_rpm_e1f1b8b5.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_rpm_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/rpm.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/ruby.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ruby_95959cd5.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ruby_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/ruby.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/sas.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_sas_e45556da.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_sas_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/sas.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/scheme.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_scheme_1aa1e01a.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_scheme_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/scheme.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/shell.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_shell_1e628f75.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_shell_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/shell.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/sieve.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_sieve_e3c48268.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_sieve_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/sieve.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/smalltalk.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_smalltalk_54ee8272.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_smalltalk_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/smalltalk.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/solr.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_solr_e8e05c75.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_solr_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/solr.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/sparql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_sparql_15824dce.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_sparql_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/sparql.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/spreadsheet.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_spreadsheet_fecaf3e3.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_spreadsheet_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/spreadsheet.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/stylus.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_stylus_1fa2921a.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_stylus_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/stylus.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/swift.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_swift_863a86d2.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_swift_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/swift.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/stex.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_stex_a749f6f4.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_stex_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/stex.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/verilog.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_verilog_543acd55.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_verilog_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/verilog.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/tcl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_tcl_b3f82227.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_tcl_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/tcl.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/textile.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_textile_8554e649.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_textile_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/textile.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/tiddlywiki.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_tiddlywiki_4fc32826.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_tiddlywiki_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/tiddlywiki.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/tiki.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_tiki_585d2b4e.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_tiki_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/tiki.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/toml.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_toml_7e43eb97.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_toml_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/toml.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/troff.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_troff_6f6a26b1.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_troff_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/troff.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/ttcn.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ttcn_9df56edd.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ttcn_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/ttcn.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/ttcn-cfg.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ttcn-cfg_42848e6e.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_ttcn-cfg_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/ttcn-cfg.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/turtle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_turtle_6ad5ec66.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_turtle_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/turtle.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/webidl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_webidl_ff1c724d.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_webidl_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/webidl.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/vb.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_vb_64f0b943.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_vb_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/vb.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/vbscript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_vbscript_ffeced28.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_vbscript_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/vbscript.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/velocity.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_velocity_9d7a73e4.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_velocity_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/velocity.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/vhdl.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_vhdl_44146f56.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_vhdl_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/vhdl.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/xquery.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_xquery_a0b12cdc.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_xquery_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/xquery.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/yacas.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_yacas_32e8365b.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_yacas_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/yacas.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/z80.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_z80_50ed0365.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_z80_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/z80.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/mscgen.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mscgen_23c4ca62.js",
  "static/chunks/node_modules_@codemirror_legacy-modes_mode_mscgen_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/legacy-modes/mode/mscgen.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-vue/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_b339632b._.js",
  "static/chunks/node_modules_@codemirror_lang-vue_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-vue/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-angular/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_26316c92._.js",
  "static/chunks/node_modules_@codemirror_lang-angular_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-angular/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@codemirror/lang-sql/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_33ebaeff._.js",
  "static/chunks/node_modules_@codemirror_lang-sql_dist_index_36365b43.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@codemirror/lang-sql/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
}]);