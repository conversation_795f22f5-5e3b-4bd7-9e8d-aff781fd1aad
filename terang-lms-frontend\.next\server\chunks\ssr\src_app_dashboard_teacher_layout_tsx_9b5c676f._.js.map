{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/app/dashboard/teacher/layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { requireRole } from '@/lib/auth';\r\n\r\nexport default function TeacherLayout({\r\n  children\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  useEffect(() => {\r\n    // Check if user has teacher role\r\n    requireRole('teacher');\r\n  }, []);\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,cAAc,EACpC,QAAQ,EAGT;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iCAAiC;QACjC,CAAA,GAAA,kHAAA,CAAA,cAAW,AAAD,EAAE;IACd,GAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ", "debugId": null}}]}