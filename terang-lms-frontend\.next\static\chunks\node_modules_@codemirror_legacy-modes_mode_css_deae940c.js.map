{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/css.js"], "sourcesContent": ["export function mkCSS(parserConfig) {\n  parserConfig = {...defaults, ...parserConfig}\n  var inline = parserConfig.inline\n\n  var tokenHooks = parserConfig.tokenHooks,\n      documentTypes = parserConfig.documentTypes || {},\n      mediaTypes = parserConfig.mediaTypes || {},\n      mediaFeatures = parserConfig.mediaFeatures || {},\n      mediaValueKeywords = parserConfig.mediaValueKeywords || {},\n      propertyKeywords = parserConfig.propertyKeywords || {},\n      nonStandardPropertyKeywords = parserConfig.nonStandardPropertyKeywords || {},\n      fontProperties = parserConfig.fontProperties || {},\n      counterDescriptors = parserConfig.counterDescriptors || {},\n      colorKeywords = parserConfig.colorKeywords || {},\n      valueKeywords = parserConfig.valueKeywords || {},\n      allowNested = parserConfig.allowNested,\n      lineComment = parserConfig.lineComment,\n      supportsAtComponent = parserConfig.supportsAtComponent === true,\n      highlightNonStandardPropertyKeywords = parserConfig.highlightNonStandardPropertyKeywords !== false;\n\n  var type, override;\n  function ret(style, tp) { type = tp; return style; }\n\n  // Tokenizers\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (tokenHooks[ch]) {\n      var result = tokenHooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n    if (ch == \"@\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"def\", stream.current());\n    } else if (ch == \"=\" || (ch == \"~\" || ch == \"|\") && stream.eat(\"=\")) {\n      return ret(null, \"compare\");\n    } else if (ch == \"\\\"\" || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    } else if (ch == \"#\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"atom\", \"hash\");\n    } else if (ch == \"!\") {\n      stream.match(/^\\s*\\w*/);\n      return ret(\"keyword\", \"important\");\n    } else if (/\\d/.test(ch) || ch == \".\" && stream.eat(/\\d/)) {\n      stream.eatWhile(/[\\w.%]/);\n      return ret(\"number\", \"unit\");\n    } else if (ch === \"-\") {\n      if (/[\\d.]/.test(stream.peek())) {\n        stream.eatWhile(/[\\w.%]/);\n        return ret(\"number\", \"unit\");\n      } else if (stream.match(/^-[\\w\\\\\\-]*/)) {\n        stream.eatWhile(/[\\w\\\\\\-]/);\n        if (stream.match(/^\\s*:/, false))\n          return ret(\"def\", \"variable-definition\");\n        return ret(\"variableName\", \"variable\");\n      } else if (stream.match(/^\\w+-/)) {\n        return ret(\"meta\", \"meta\");\n      }\n    } else if (/[,+>*\\/]/.test(ch)) {\n      return ret(null, \"select-op\");\n    } else if (ch == \".\" && stream.match(/^-?[_a-z][_a-z0-9-]*/i)) {\n      return ret(\"qualifier\", \"qualifier\");\n    } else if (/[:;{}\\[\\]\\(\\)]/.test(ch)) {\n      return ret(null, ch);\n    } else if (stream.match(/^[\\w-.]+(?=\\()/)) {\n      if (/^(url(-prefix)?|domain|regexp)$/i.test(stream.current())) {\n        state.tokenize = tokenParenthesized;\n      }\n      return ret(\"variableName.function\", \"variable\");\n    } else if (/[\\w\\\\\\-]/.test(ch)) {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"property\", \"word\");\n    } else {\n      return ret(null, null);\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == quote && !escaped) {\n          if (quote == \")\") stream.backUp(1);\n          break;\n        }\n        escaped = !escaped && ch == \"\\\\\";\n      }\n      if (ch == quote || !escaped && quote != \")\") state.tokenize = null;\n      return ret(\"string\", \"string\");\n    };\n  }\n\n  function tokenParenthesized(stream, state) {\n    stream.next(); // Must be '('\n    if (!stream.match(/^\\s*[\\\"\\')]/, false))\n      state.tokenize = tokenString(\")\");\n    else\n      state.tokenize = null;\n    return ret(null, \"(\");\n  }\n\n  // Context management\n\n  function Context(type, indent, prev) {\n    this.type = type;\n    this.indent = indent;\n    this.prev = prev;\n  }\n\n  function pushContext(state, stream, type, indent) {\n    state.context = new Context(type, stream.indentation() + (indent === false ? 0 : stream.indentUnit), state.context);\n    return type;\n  }\n\n  function popContext(state) {\n    if (state.context.prev)\n      state.context = state.context.prev;\n    return state.context.type;\n  }\n\n  function pass(type, stream, state) {\n    return states[state.context.type](type, stream, state);\n  }\n  function popAndPass(type, stream, state, n) {\n    for (var i = n || 1; i > 0; i--)\n      state.context = state.context.prev;\n    return pass(type, stream, state);\n  }\n\n  // Parser\n\n  function wordAsValue(stream) {\n    var word = stream.current().toLowerCase();\n    if (valueKeywords.hasOwnProperty(word))\n      override = \"atom\";\n    else if (colorKeywords.hasOwnProperty(word))\n      override = \"keyword\";\n    else\n      override = \"variable\";\n  }\n\n  var states = {};\n\n  states.top = function(type, stream, state) {\n    if (type == \"{\") {\n      return pushContext(state, stream, \"block\");\n    } else if (type == \"}\" && state.context.prev) {\n      return popContext(state);\n    } else if (supportsAtComponent && /@component/i.test(type)) {\n      return pushContext(state, stream, \"atComponentBlock\");\n    } else if (/^@(-moz-)?document$/i.test(type)) {\n      return pushContext(state, stream, \"documentTypes\");\n    } else if (/^@(media|supports|(-moz-)?document|import)$/i.test(type)) {\n      return pushContext(state, stream, \"atBlock\");\n    } else if (/^@(font-face|counter-style)/i.test(type)) {\n      state.stateArg = type;\n      return \"restricted_atBlock_before\";\n    } else if (/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(type)) {\n      return \"keyframes\";\n    } else if (type && type.charAt(0) == \"@\") {\n      return pushContext(state, stream, \"at\");\n    } else if (type == \"hash\") {\n      override = \"builtin\";\n    } else if (type == \"word\") {\n      override = \"tag\";\n    } else if (type == \"variable-definition\") {\n      return \"maybeprop\";\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    } else if (type == \":\") {\n      return \"pseudo\";\n    } else if (allowNested && type == \"(\") {\n      return pushContext(state, stream, \"parens\");\n    }\n    return state.context.type;\n  };\n\n  states.block = function(type, stream, state) {\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (propertyKeywords.hasOwnProperty(word)) {\n        override = \"property\";\n        return \"maybeprop\";\n      } else if (nonStandardPropertyKeywords.hasOwnProperty(word)) {\n        override = highlightNonStandardPropertyKeywords ? \"string.special\" : \"property\";\n        return \"maybeprop\";\n      } else if (allowNested) {\n        override = stream.match(/^\\s*:(?:\\s|$)/, false) ? \"property\" : \"tag\";\n        return \"block\";\n      } else {\n        override = \"error\";\n        return \"maybeprop\";\n      }\n    } else if (type == \"meta\") {\n      return \"block\";\n    } else if (!allowNested && (type == \"hash\" || type == \"qualifier\")) {\n      override = \"error\";\n      return \"block\";\n    } else {\n      return states.top(type, stream, state);\n    }\n  };\n\n  states.maybeprop = function(type, stream, state) {\n    if (type == \":\") return pushContext(state, stream, \"prop\");\n    return pass(type, stream, state);\n  };\n\n  states.prop = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" && allowNested) return pushContext(state, stream, \"propBlock\");\n    if (type == \"}\" || type == \"{\") return popAndPass(type, stream, state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n\n    if (type == \"hash\" && !/^#([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(stream.current())) {\n      override = \"error\";\n    } else if (type == \"word\") {\n      wordAsValue(stream);\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    }\n    return \"prop\";\n  };\n\n  states.propBlock = function(type, _stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"word\") { override = \"property\"; return \"maybeprop\"; }\n    return state.context.type;\n  };\n\n  states.parens = function(type, stream, state) {\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \")\") return popContext(state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n    if (type == \"word\") wordAsValue(stream);\n    return \"parens\";\n  };\n\n  states.pseudo = function(type, stream, state) {\n    if (type == \"meta\") return \"pseudo\";\n\n    if (type == \"word\") {\n      override = \"variableName.constant\";\n      return state.context.type;\n    }\n    return pass(type, stream, state);\n  };\n\n  states.documentTypes = function(type, stream, state) {\n    if (type == \"word\" && documentTypes.hasOwnProperty(stream.current())) {\n      override = \"tag\";\n      return state.context.type;\n    } else {\n      return states.atBlock(type, stream, state);\n    }\n  };\n\n  states.atBlock = function(type, stream, state) {\n    if (type == \"(\") return pushContext(state, stream, \"atBlock_parens\");\n    if (type == \"}\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"{\") return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\");\n\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (word == \"only\" || word == \"not\" || word == \"and\" || word == \"or\")\n        override = \"keyword\";\n      else if (mediaTypes.hasOwnProperty(word))\n        override = \"attribute\";\n      else if (mediaFeatures.hasOwnProperty(word))\n        override = \"property\";\n      else if (mediaValueKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else if (propertyKeywords.hasOwnProperty(word))\n        override = \"property\";\n      else if (nonStandardPropertyKeywords.hasOwnProperty(word))\n        override = highlightNonStandardPropertyKeywords ? \"string.special\" : \"property\";\n      else if (valueKeywords.hasOwnProperty(word))\n        override = \"atom\";\n      else if (colorKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else\n        override = \"error\";\n    }\n    return state.context.type;\n  };\n\n  states.atComponentBlock = function(type, stream, state) {\n    if (type == \"}\")\n      return popAndPass(type, stream, state);\n    if (type == \"{\")\n      return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\", false);\n    if (type == \"word\")\n      override = \"error\";\n    return state.context.type;\n  };\n\n  states.atBlock_parens = function(type, stream, state) {\n    if (type == \")\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state, 2);\n    return states.atBlock(type, stream, state);\n  };\n\n  states.restricted_atBlock_before = function(type, stream, state) {\n    if (type == \"{\")\n      return pushContext(state, stream, \"restricted_atBlock\");\n    if (type == \"word\" && state.stateArg == \"@counter-style\") {\n      override = \"variable\";\n      return \"restricted_atBlock_before\";\n    }\n    return pass(type, stream, state);\n  };\n\n  states.restricted_atBlock = function(type, stream, state) {\n    if (type == \"}\") {\n      state.stateArg = null;\n      return popContext(state);\n    }\n    if (type == \"word\") {\n      if ((state.stateArg == \"@font-face\" && !fontProperties.hasOwnProperty(stream.current().toLowerCase())) ||\n          (state.stateArg == \"@counter-style\" && !counterDescriptors.hasOwnProperty(stream.current().toLowerCase())))\n        override = \"error\";\n      else\n        override = \"property\";\n      return \"maybeprop\";\n    }\n    return \"restricted_atBlock\";\n  };\n\n  states.keyframes = function(type, stream, state) {\n    if (type == \"word\") { override = \"variable\"; return \"keyframes\"; }\n    if (type == \"{\") return pushContext(state, stream, \"top\");\n    return pass(type, stream, state);\n  };\n\n  states.at = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"tag\";\n    else if (type == \"hash\") override = \"builtin\";\n    return \"at\";\n  };\n\n  states.interpolation = function(type, stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"{\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"variable\";\n    else if (type != \"variable\" && type != \"(\" && type != \")\") override = \"error\";\n    return \"interpolation\";\n  };\n\n  return {\n    name: parserConfig.name,\n    startState: function() {\n      return {tokenize: null,\n              state: inline ? \"block\" : \"top\",\n              stateArg: null,\n              context: new Context(inline ? \"block\" : \"top\", 0, null)};\n    },\n\n    token: function(stream, state) {\n      if (!state.tokenize && stream.eatSpace()) return null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style && typeof style == \"object\") {\n        type = style[1];\n        style = style[0];\n      }\n      override = style;\n      if (type != \"comment\")\n        state.state = states[state.state](type, stream, state);\n      return override;\n    },\n\n    indent: function(state, textAfter, iCx) {\n      var cx = state.context, ch = textAfter && textAfter.charAt(0);\n      var indent = cx.indent;\n      if (cx.type == \"prop\" && (ch == \"}\" || ch == \")\")) cx = cx.prev;\n      if (cx.prev) {\n        if (ch == \"}\" && (cx.type == \"block\" || cx.type == \"top\" ||\n                          cx.type == \"interpolation\" || cx.type == \"restricted_atBlock\")) {\n          // Resume indentation from parent context.\n          cx = cx.prev;\n          indent = cx.indent;\n        } else if (ch == \")\" && (cx.type == \"parens\" || cx.type == \"atBlock_parens\") ||\n                   ch == \"{\" && (cx.type == \"at\" || cx.type == \"atBlock\")) {\n          // Dedent relative to current context.\n          indent = Math.max(0, cx.indent - iCx.unit);\n        }\n      }\n      return indent;\n    },\n\n    languageData: {\n      indentOnInput: /^\\s*\\}$/,\n      commentTokens: {line: lineComment, block: {open: \"/*\", close: \"*/\"}},\n      autocomplete: allWords\n    }\n  };\n};\n\nfunction keySet(array) {\n  var keys = {};\n  for (var i = 0; i < array.length; ++i) {\n    keys[array[i].toLowerCase()] = true;\n  }\n  return keys;\n}\n\nvar documentTypes_ = [\n  \"domain\", \"regexp\", \"url\", \"url-prefix\"\n], documentTypes = keySet(documentTypes_);\n\nvar mediaTypes_ = [\n  \"all\", \"aural\", \"braille\", \"handheld\", \"print\", \"projection\", \"screen\",\n  \"tty\", \"tv\", \"embossed\"\n], mediaTypes = keySet(mediaTypes_);\n\nvar mediaFeatures_ = [\n  \"width\", \"min-width\", \"max-width\", \"height\", \"min-height\", \"max-height\",\n  \"device-width\", \"min-device-width\", \"max-device-width\", \"device-height\",\n  \"min-device-height\", \"max-device-height\", \"aspect-ratio\",\n  \"min-aspect-ratio\", \"max-aspect-ratio\", \"device-aspect-ratio\",\n  \"min-device-aspect-ratio\", \"max-device-aspect-ratio\", \"color\", \"min-color\",\n  \"max-color\", \"color-index\", \"min-color-index\", \"max-color-index\",\n  \"monochrome\", \"min-monochrome\", \"max-monochrome\", \"resolution\",\n  \"min-resolution\", \"max-resolution\", \"scan\", \"grid\", \"orientation\",\n  \"device-pixel-ratio\", \"min-device-pixel-ratio\", \"max-device-pixel-ratio\",\n  \"pointer\", \"any-pointer\", \"hover\", \"any-hover\", \"prefers-color-scheme\",\n  \"dynamic-range\", \"video-dynamic-range\"\n], mediaFeatures = keySet(mediaFeatures_);\n\nvar mediaValueKeywords_ = [\n  \"landscape\", \"portrait\", \"none\", \"coarse\", \"fine\", \"on-demand\", \"hover\",\n  \"interlace\", \"progressive\",\n  \"dark\", \"light\",\n  \"standard\", \"high\"\n], mediaValueKeywords = keySet(mediaValueKeywords_);\n\nvar propertyKeywords_ = [\n  \"align-content\", \"align-items\", \"align-self\", \"alignment-adjust\",\n  \"alignment-baseline\", \"all\", \"anchor-point\", \"animation\", \"animation-delay\",\n  \"animation-direction\", \"animation-duration\", \"animation-fill-mode\",\n  \"animation-iteration-count\", \"animation-name\", \"animation-play-state\",\n  \"animation-timing-function\", \"appearance\", \"azimuth\", \"backdrop-filter\",\n  \"backface-visibility\", \"background\", \"background-attachment\",\n  \"background-blend-mode\", \"background-clip\", \"background-color\",\n  \"background-image\", \"background-origin\", \"background-position\",\n  \"background-position-x\", \"background-position-y\", \"background-repeat\",\n  \"background-size\", \"baseline-shift\", \"binding\", \"bleed\", \"block-size\",\n  \"bookmark-label\", \"bookmark-level\", \"bookmark-state\", \"bookmark-target\",\n  \"border\", \"border-bottom\", \"border-bottom-color\", \"border-bottom-left-radius\",\n  \"border-bottom-right-radius\", \"border-bottom-style\", \"border-bottom-width\",\n  \"border-collapse\", \"border-color\", \"border-image\", \"border-image-outset\",\n  \"border-image-repeat\", \"border-image-slice\", \"border-image-source\",\n  \"border-image-width\", \"border-left\", \"border-left-color\", \"border-left-style\",\n  \"border-left-width\", \"border-radius\", \"border-right\", \"border-right-color\",\n  \"border-right-style\", \"border-right-width\", \"border-spacing\", \"border-style\",\n  \"border-top\", \"border-top-color\", \"border-top-left-radius\",\n  \"border-top-right-radius\", \"border-top-style\", \"border-top-width\",\n  \"border-width\", \"bottom\", \"box-decoration-break\", \"box-shadow\", \"box-sizing\",\n  \"break-after\", \"break-before\", \"break-inside\", \"caption-side\", \"caret-color\",\n  \"clear\", \"clip\", \"color\", \"color-profile\", \"column-count\", \"column-fill\",\n  \"column-gap\", \"column-rule\", \"column-rule-color\", \"column-rule-style\",\n  \"column-rule-width\", \"column-span\", \"column-width\", \"columns\", \"contain\",\n  \"content\", \"counter-increment\", \"counter-reset\", \"crop\", \"cue\", \"cue-after\",\n  \"cue-before\", \"cursor\", \"direction\", \"display\", \"dominant-baseline\",\n  \"drop-initial-after-adjust\", \"drop-initial-after-align\",\n  \"drop-initial-before-adjust\", \"drop-initial-before-align\", \"drop-initial-size\",\n  \"drop-initial-value\", \"elevation\", \"empty-cells\", \"fit\", \"fit-content\", \"fit-position\",\n  \"flex\", \"flex-basis\", \"flex-direction\", \"flex-flow\", \"flex-grow\",\n  \"flex-shrink\", \"flex-wrap\", \"float\", \"float-offset\", \"flow-from\", \"flow-into\",\n  \"font\", \"font-family\", \"font-feature-settings\", \"font-kerning\",\n  \"font-language-override\", \"font-optical-sizing\", \"font-size\",\n  \"font-size-adjust\", \"font-stretch\", \"font-style\", \"font-synthesis\",\n  \"font-variant\", \"font-variant-alternates\", \"font-variant-caps\",\n  \"font-variant-east-asian\", \"font-variant-ligatures\", \"font-variant-numeric\",\n  \"font-variant-position\", \"font-variation-settings\", \"font-weight\", \"gap\",\n  \"grid\", \"grid-area\", \"grid-auto-columns\", \"grid-auto-flow\", \"grid-auto-rows\",\n  \"grid-column\", \"grid-column-end\", \"grid-column-gap\", \"grid-column-start\",\n  \"grid-gap\", \"grid-row\", \"grid-row-end\", \"grid-row-gap\", \"grid-row-start\",\n  \"grid-template\", \"grid-template-areas\", \"grid-template-columns\",\n  \"grid-template-rows\", \"hanging-punctuation\", \"height\", \"hyphens\", \"icon\",\n  \"image-orientation\", \"image-rendering\", \"image-resolution\", \"inline-box-align\",\n  \"inset\", \"inset-block\", \"inset-block-end\", \"inset-block-start\", \"inset-inline\",\n  \"inset-inline-end\", \"inset-inline-start\", \"isolation\", \"justify-content\",\n  \"justify-items\", \"justify-self\", \"left\", \"letter-spacing\", \"line-break\",\n  \"line-height\", \"line-height-step\", \"line-stacking\", \"line-stacking-ruby\",\n  \"line-stacking-shift\", \"line-stacking-strategy\", \"list-style\",\n  \"list-style-image\", \"list-style-position\", \"list-style-type\", \"margin\",\n  \"margin-bottom\", \"margin-left\", \"margin-right\", \"margin-top\", \"marks\",\n  \"marquee-direction\", \"marquee-loop\", \"marquee-play-count\", \"marquee-speed\",\n  \"marquee-style\", \"mask-clip\", \"mask-composite\", \"mask-image\", \"mask-mode\",\n  \"mask-origin\", \"mask-position\", \"mask-repeat\", \"mask-size\",\"mask-type\",\n  \"max-block-size\", \"max-height\", \"max-inline-size\",\n  \"max-width\", \"min-block-size\", \"min-height\", \"min-inline-size\", \"min-width\",\n  \"mix-blend-mode\", \"move-to\", \"nav-down\", \"nav-index\", \"nav-left\", \"nav-right\",\n  \"nav-up\", \"object-fit\", \"object-position\", \"offset\", \"offset-anchor\",\n  \"offset-distance\", \"offset-path\", \"offset-position\", \"offset-rotate\",\n  \"opacity\", \"order\", \"orphans\", \"outline\", \"outline-color\", \"outline-offset\",\n  \"outline-style\", \"outline-width\", \"overflow\", \"overflow-style\",\n  \"overflow-wrap\", \"overflow-x\", \"overflow-y\", \"padding\", \"padding-bottom\",\n  \"padding-left\", \"padding-right\", \"padding-top\", \"page\", \"page-break-after\",\n  \"page-break-before\", \"page-break-inside\", \"page-policy\", \"pause\",\n  \"pause-after\", \"pause-before\", \"perspective\", \"perspective-origin\", \"pitch\",\n  \"pitch-range\", \"place-content\", \"place-items\", \"place-self\", \"play-during\",\n  \"position\", \"presentation-level\", \"punctuation-trim\", \"quotes\",\n  \"region-break-after\", \"region-break-before\", \"region-break-inside\",\n  \"region-fragment\", \"rendering-intent\", \"resize\", \"rest\", \"rest-after\",\n  \"rest-before\", \"richness\", \"right\", \"rotate\", \"rotation\", \"rotation-point\",\n  \"row-gap\", \"ruby-align\", \"ruby-overhang\", \"ruby-position\", \"ruby-span\",\n  \"scale\", \"scroll-behavior\", \"scroll-margin\", \"scroll-margin-block\",\n  \"scroll-margin-block-end\", \"scroll-margin-block-start\", \"scroll-margin-bottom\",\n  \"scroll-margin-inline\", \"scroll-margin-inline-end\",\n  \"scroll-margin-inline-start\", \"scroll-margin-left\", \"scroll-margin-right\",\n  \"scroll-margin-top\", \"scroll-padding\", \"scroll-padding-block\",\n  \"scroll-padding-block-end\", \"scroll-padding-block-start\",\n  \"scroll-padding-bottom\", \"scroll-padding-inline\", \"scroll-padding-inline-end\",\n  \"scroll-padding-inline-start\", \"scroll-padding-left\", \"scroll-padding-right\",\n  \"scroll-padding-top\", \"scroll-snap-align\", \"scroll-snap-type\",\n  \"shape-image-threshold\", \"shape-inside\", \"shape-margin\", \"shape-outside\",\n  \"size\", \"speak\", \"speak-as\", \"speak-header\", \"speak-numeral\",\n  \"speak-punctuation\", \"speech-rate\", \"stress\", \"string-set\", \"tab-size\",\n  \"table-layout\", \"target\", \"target-name\", \"target-new\", \"target-position\",\n  \"text-align\", \"text-align-last\", \"text-combine-upright\", \"text-decoration\",\n  \"text-decoration-color\", \"text-decoration-line\", \"text-decoration-skip\",\n  \"text-decoration-skip-ink\", \"text-decoration-style\", \"text-emphasis\",\n  \"text-emphasis-color\", \"text-emphasis-position\", \"text-emphasis-style\",\n  \"text-height\", \"text-indent\", \"text-justify\", \"text-orientation\",\n  \"text-outline\", \"text-overflow\", \"text-rendering\", \"text-shadow\",\n  \"text-size-adjust\", \"text-space-collapse\", \"text-transform\",\n  \"text-underline-position\", \"text-wrap\", \"top\", \"touch-action\", \"transform\", \"transform-origin\",\n  \"transform-style\", \"transition\", \"transition-delay\", \"transition-duration\",\n  \"transition-property\", \"transition-timing-function\", \"translate\",\n  \"unicode-bidi\", \"user-select\", \"vertical-align\", \"visibility\", \"voice-balance\",\n  \"voice-duration\", \"voice-family\", \"voice-pitch\", \"voice-range\", \"voice-rate\",\n  \"voice-stress\", \"voice-volume\", \"volume\", \"white-space\", \"widows\", \"width\",\n  \"will-change\", \"word-break\", \"word-spacing\", \"word-wrap\", \"writing-mode\", \"z-index\",\n  // SVG-specific\n  \"clip-path\", \"clip-rule\", \"mask\", \"enable-background\", \"filter\", \"flood-color\",\n  \"flood-opacity\", \"lighting-color\", \"stop-color\", \"stop-opacity\", \"pointer-events\",\n  \"color-interpolation\", \"color-interpolation-filters\",\n  \"color-rendering\", \"fill\", \"fill-opacity\", \"fill-rule\", \"image-rendering\",\n  \"marker\", \"marker-end\", \"marker-mid\", \"marker-start\", \"paint-order\", \"shape-rendering\", \"stroke\",\n  \"stroke-dasharray\", \"stroke-dashoffset\", \"stroke-linecap\", \"stroke-linejoin\",\n  \"stroke-miterlimit\", \"stroke-opacity\", \"stroke-width\", \"text-rendering\",\n  \"baseline-shift\", \"dominant-baseline\", \"glyph-orientation-horizontal\",\n  \"glyph-orientation-vertical\", \"text-anchor\", \"writing-mode\",\n], propertyKeywords = keySet(propertyKeywords_);\n\nvar nonStandardPropertyKeywords_ = [\n  \"accent-color\", \"aspect-ratio\", \"border-block\", \"border-block-color\", \"border-block-end\",\n  \"border-block-end-color\", \"border-block-end-style\", \"border-block-end-width\",\n  \"border-block-start\", \"border-block-start-color\", \"border-block-start-style\",\n  \"border-block-start-width\", \"border-block-style\", \"border-block-width\",\n  \"border-inline\", \"border-inline-color\", \"border-inline-end\",\n  \"border-inline-end-color\", \"border-inline-end-style\",\n  \"border-inline-end-width\", \"border-inline-start\", \"border-inline-start-color\",\n  \"border-inline-start-style\", \"border-inline-start-width\",\n  \"border-inline-style\", \"border-inline-width\", \"content-visibility\", \"margin-block\",\n  \"margin-block-end\", \"margin-block-start\", \"margin-inline\", \"margin-inline-end\",\n  \"margin-inline-start\", \"overflow-anchor\", \"overscroll-behavior\", \"padding-block\", \"padding-block-end\",\n  \"padding-block-start\", \"padding-inline\", \"padding-inline-end\",\n  \"padding-inline-start\", \"scroll-snap-stop\", \"scrollbar-3d-light-color\",\n  \"scrollbar-arrow-color\", \"scrollbar-base-color\", \"scrollbar-dark-shadow-color\",\n  \"scrollbar-face-color\", \"scrollbar-highlight-color\", \"scrollbar-shadow-color\",\n  \"scrollbar-track-color\", \"searchfield-cancel-button\", \"searchfield-decoration\",\n  \"searchfield-results-button\", \"searchfield-results-decoration\", \"shape-inside\", \"zoom\"\n], nonStandardPropertyKeywords = keySet(nonStandardPropertyKeywords_);\n\nvar fontProperties_ = [\n  \"font-display\", \"font-family\", \"src\", \"unicode-range\", \"font-variant\",\n  \"font-feature-settings\", \"font-stretch\", \"font-weight\", \"font-style\"\n], fontProperties = keySet(fontProperties_);\n\nvar counterDescriptors_ = [\n  \"additive-symbols\", \"fallback\", \"negative\", \"pad\", \"prefix\", \"range\",\n  \"speak-as\", \"suffix\", \"symbols\", \"system\"\n], counterDescriptors = keySet(counterDescriptors_);\n\nvar colorKeywords_ = [\n  \"aliceblue\", \"antiquewhite\", \"aqua\", \"aquamarine\", \"azure\", \"beige\",\n  \"bisque\", \"black\", \"blanchedalmond\", \"blue\", \"blueviolet\", \"brown\",\n  \"burlywood\", \"cadetblue\", \"chartreuse\", \"chocolate\", \"coral\", \"cornflowerblue\",\n  \"cornsilk\", \"crimson\", \"cyan\", \"darkblue\", \"darkcyan\", \"darkgoldenrod\",\n  \"darkgray\", \"darkgreen\", \"darkgrey\", \"darkkhaki\", \"darkmagenta\", \"darkolivegreen\",\n  \"darkorange\", \"darkorchid\", \"darkred\", \"darksalmon\", \"darkseagreen\",\n  \"darkslateblue\", \"darkslategray\", \"darkslategrey\", \"darkturquoise\", \"darkviolet\",\n  \"deeppink\", \"deepskyblue\", \"dimgray\", \"dimgrey\", \"dodgerblue\", \"firebrick\",\n  \"floralwhite\", \"forestgreen\", \"fuchsia\", \"gainsboro\", \"ghostwhite\",\n  \"gold\", \"goldenrod\", \"gray\", \"grey\", \"green\", \"greenyellow\", \"honeydew\",\n  \"hotpink\", \"indianred\", \"indigo\", \"ivory\", \"khaki\", \"lavender\",\n  \"lavenderblush\", \"lawngreen\", \"lemonchiffon\", \"lightblue\", \"lightcoral\",\n  \"lightcyan\", \"lightgoldenrodyellow\", \"lightgray\", \"lightgreen\", \"lightgrey\", \"lightpink\",\n  \"lightsalmon\", \"lightseagreen\", \"lightskyblue\", \"lightslategray\", \"lightslategrey\",\n  \"lightsteelblue\", \"lightyellow\", \"lime\", \"limegreen\", \"linen\", \"magenta\",\n  \"maroon\", \"mediumaquamarine\", \"mediumblue\", \"mediumorchid\", \"mediumpurple\",\n  \"mediumseagreen\", \"mediumslateblue\", \"mediumspringgreen\", \"mediumturquoise\",\n  \"mediumvioletred\", \"midnightblue\", \"mintcream\", \"mistyrose\", \"moccasin\",\n  \"navajowhite\", \"navy\", \"oldlace\", \"olive\", \"olivedrab\", \"orange\", \"orangered\",\n  \"orchid\", \"palegoldenrod\", \"palegreen\", \"paleturquoise\", \"palevioletred\",\n  \"papayawhip\", \"peachpuff\", \"peru\", \"pink\", \"plum\", \"powderblue\",\n  \"purple\", \"rebeccapurple\", \"red\", \"rosybrown\", \"royalblue\", \"saddlebrown\",\n  \"salmon\", \"sandybrown\", \"seagreen\", \"seashell\", \"sienna\", \"silver\", \"skyblue\",\n  \"slateblue\", \"slategray\", \"slategrey\", \"snow\", \"springgreen\", \"steelblue\", \"tan\",\n  \"teal\", \"thistle\", \"tomato\", \"turquoise\", \"violet\", \"wheat\", \"white\",\n  \"whitesmoke\", \"yellow\", \"yellowgreen\"\n], colorKeywords = keySet(colorKeywords_);\n\nvar valueKeywords_ = [\n  \"above\", \"absolute\", \"activeborder\", \"additive\", \"activecaption\", \"afar\",\n  \"after-white-space\", \"ahead\", \"alias\", \"all\", \"all-scroll\", \"alphabetic\", \"alternate\",\n  \"always\", \"amharic\", \"amharic-abegede\", \"antialiased\", \"appworkspace\",\n  \"arabic-indic\", \"armenian\", \"asterisks\", \"attr\", \"auto\", \"auto-flow\", \"avoid\", \"avoid-column\", \"avoid-page\",\n  \"avoid-region\", \"axis-pan\", \"background\", \"backwards\", \"baseline\", \"below\", \"bidi-override\", \"binary\",\n  \"bengali\", \"blink\", \"block\", \"block-axis\", \"blur\", \"bold\", \"bolder\", \"border\", \"border-box\",\n  \"both\", \"bottom\", \"break\", \"break-all\", \"break-word\", \"brightness\", \"bullets\", \"button\",\n  \"buttonface\", \"buttonhighlight\", \"buttonshadow\", \"buttontext\", \"calc\", \"cambodian\",\n  \"capitalize\", \"caps-lock-indicator\", \"caption\", \"captiontext\", \"caret\",\n  \"cell\", \"center\", \"checkbox\", \"circle\", \"cjk-decimal\", \"cjk-earthly-branch\",\n  \"cjk-heavenly-stem\", \"cjk-ideographic\", \"clear\", \"clip\", \"close-quote\",\n  \"col-resize\", \"collapse\", \"color\", \"color-burn\", \"color-dodge\", \"column\", \"column-reverse\",\n  \"compact\", \"condensed\", \"conic-gradient\", \"contain\", \"content\", \"contents\",\n  \"content-box\", \"context-menu\", \"continuous\", \"contrast\", \"copy\", \"counter\", \"counters\", \"cover\", \"crop\",\n  \"cross\", \"crosshair\", \"cubic-bezier\", \"currentcolor\", \"cursive\", \"cyclic\", \"darken\", \"dashed\", \"decimal\",\n  \"decimal-leading-zero\", \"default\", \"default-button\", \"dense\", \"destination-atop\",\n  \"destination-in\", \"destination-out\", \"destination-over\", \"devanagari\", \"difference\",\n  \"disc\", \"discard\", \"disclosure-closed\", \"disclosure-open\", \"document\",\n  \"dot-dash\", \"dot-dot-dash\",\n  \"dotted\", \"double\", \"down\", \"drop-shadow\", \"e-resize\", \"ease\", \"ease-in\", \"ease-in-out\", \"ease-out\",\n  \"element\", \"ellipse\", \"ellipsis\", \"embed\", \"end\", \"ethiopic\", \"ethiopic-abegede\",\n  \"ethiopic-abegede-am-et\", \"ethiopic-abegede-gez\", \"ethiopic-abegede-ti-er\",\n  \"ethiopic-abegede-ti-et\", \"ethiopic-halehame-aa-er\",\n  \"ethiopic-halehame-aa-et\", \"ethiopic-halehame-am-et\",\n  \"ethiopic-halehame-gez\", \"ethiopic-halehame-om-et\",\n  \"ethiopic-halehame-sid-et\", \"ethiopic-halehame-so-et\",\n  \"ethiopic-halehame-ti-er\", \"ethiopic-halehame-ti-et\", \"ethiopic-halehame-tig\",\n  \"ethiopic-numeric\", \"ew-resize\", \"exclusion\", \"expanded\", \"extends\", \"extra-condensed\",\n  \"extra-expanded\", \"fantasy\", \"fast\", \"fill\", \"fill-box\", \"fixed\", \"flat\", \"flex\", \"flex-end\", \"flex-start\", \"footnotes\",\n  \"forwards\", \"from\", \"geometricPrecision\", \"georgian\", \"grayscale\", \"graytext\", \"grid\", \"groove\",\n  \"gujarati\", \"gurmukhi\", \"hand\", \"hangul\", \"hangul-consonant\", \"hard-light\", \"hebrew\",\n  \"help\", \"hidden\", \"hide\", \"higher\", \"highlight\", \"highlighttext\",\n  \"hiragana\", \"hiragana-iroha\", \"horizontal\", \"hsl\", \"hsla\", \"hue\", \"hue-rotate\", \"icon\", \"ignore\",\n  \"inactiveborder\", \"inactivecaption\", \"inactivecaptiontext\", \"infinite\",\n  \"infobackground\", \"infotext\", \"inherit\", \"initial\", \"inline\", \"inline-axis\",\n  \"inline-block\", \"inline-flex\", \"inline-grid\", \"inline-table\", \"inset\", \"inside\", \"intrinsic\", \"invert\",\n  \"italic\", \"japanese-formal\", \"japanese-informal\", \"justify\", \"kannada\",\n  \"katakana\", \"katakana-iroha\", \"keep-all\", \"khmer\",\n  \"korean-hangul-formal\", \"korean-hanja-formal\", \"korean-hanja-informal\",\n  \"landscape\", \"lao\", \"large\", \"larger\", \"left\", \"level\", \"lighter\", \"lighten\",\n  \"line-through\", \"linear\", \"linear-gradient\", \"lines\", \"list-item\", \"listbox\", \"listitem\",\n  \"local\", \"logical\", \"loud\", \"lower\", \"lower-alpha\", \"lower-armenian\",\n  \"lower-greek\", \"lower-hexadecimal\", \"lower-latin\", \"lower-norwegian\",\n  \"lower-roman\", \"lowercase\", \"ltr\", \"luminosity\", \"malayalam\", \"manipulation\", \"match\", \"matrix\", \"matrix3d\",\n  \"media-play-button\", \"media-slider\", \"media-sliderthumb\",\n  \"media-volume-slider\", \"media-volume-sliderthumb\", \"medium\",\n  \"menu\", \"menulist\", \"menulist-button\",\n  \"menutext\", \"message-box\", \"middle\", \"min-intrinsic\",\n  \"mix\", \"mongolian\", \"monospace\", \"move\", \"multiple\", \"multiple_mask_images\", \"multiply\", \"myanmar\", \"n-resize\",\n  \"narrower\", \"ne-resize\", \"nesw-resize\", \"no-close-quote\", \"no-drop\",\n  \"no-open-quote\", \"no-repeat\", \"none\", \"normal\", \"not-allowed\", \"nowrap\",\n  \"ns-resize\", \"numbers\", \"numeric\", \"nw-resize\", \"nwse-resize\", \"oblique\", \"octal\", \"opacity\", \"open-quote\",\n  \"optimizeLegibility\", \"optimizeSpeed\", \"oriya\", \"oromo\", \"outset\",\n  \"outside\", \"outside-shape\", \"overlay\", \"overline\", \"padding\", \"padding-box\",\n  \"painted\", \"page\", \"paused\", \"persian\", \"perspective\", \"pinch-zoom\", \"plus-darker\", \"plus-lighter\",\n  \"pointer\", \"polygon\", \"portrait\", \"pre\", \"pre-line\", \"pre-wrap\", \"preserve-3d\",\n  \"progress\", \"push-button\", \"radial-gradient\", \"radio\", \"read-only\",\n  \"read-write\", \"read-write-plaintext-only\", \"rectangle\", \"region\",\n  \"relative\", \"repeat\", \"repeating-linear-gradient\", \"repeating-radial-gradient\",\n  \"repeating-conic-gradient\", \"repeat-x\", \"repeat-y\", \"reset\", \"reverse\",\n  \"rgb\", \"rgba\", \"ridge\", \"right\", \"rotate\", \"rotate3d\", \"rotateX\", \"rotateY\",\n  \"rotateZ\", \"round\", \"row\", \"row-resize\", \"row-reverse\", \"rtl\", \"run-in\", \"running\",\n  \"s-resize\", \"sans-serif\", \"saturate\", \"saturation\", \"scale\", \"scale3d\", \"scaleX\", \"scaleY\", \"scaleZ\", \"screen\",\n  \"scroll\", \"scrollbar\", \"scroll-position\", \"se-resize\", \"searchfield\",\n  \"searchfield-cancel-button\", \"searchfield-decoration\",\n  \"searchfield-results-button\", \"searchfield-results-decoration\", \"self-start\", \"self-end\",\n  \"semi-condensed\", \"semi-expanded\", \"separate\", \"sepia\", \"serif\", \"show\", \"sidama\",\n  \"simp-chinese-formal\", \"simp-chinese-informal\", \"single\",\n  \"skew\", \"skewX\", \"skewY\", \"skip-white-space\", \"slide\", \"slider-horizontal\",\n  \"slider-vertical\", \"sliderthumb-horizontal\", \"sliderthumb-vertical\", \"slow\",\n  \"small\", \"small-caps\", \"small-caption\", \"smaller\", \"soft-light\", \"solid\", \"somali\",\n  \"source-atop\", \"source-in\", \"source-out\", \"source-over\", \"space\", \"space-around\", \"space-between\", \"space-evenly\", \"spell-out\", \"square\",\n  \"square-button\", \"start\", \"static\", \"status-bar\", \"stretch\", \"stroke\", \"stroke-box\", \"sub\",\n  \"subpixel-antialiased\", \"svg_masks\", \"super\", \"sw-resize\", \"symbolic\", \"symbols\", \"system-ui\", \"table\",\n  \"table-caption\", \"table-cell\", \"table-column\", \"table-column-group\",\n  \"table-footer-group\", \"table-header-group\", \"table-row\", \"table-row-group\",\n  \"tamil\",\n  \"telugu\", \"text\", \"text-bottom\", \"text-top\", \"textarea\", \"textfield\", \"thai\",\n  \"thick\", \"thin\", \"threeddarkshadow\", \"threedface\", \"threedhighlight\",\n  \"threedlightshadow\", \"threedshadow\", \"tibetan\", \"tigre\", \"tigrinya-er\",\n  \"tigrinya-er-abegede\", \"tigrinya-et\", \"tigrinya-et-abegede\", \"to\", \"top\",\n  \"trad-chinese-formal\", \"trad-chinese-informal\", \"transform\",\n  \"translate\", \"translate3d\", \"translateX\", \"translateY\", \"translateZ\",\n  \"transparent\", \"ultra-condensed\", \"ultra-expanded\", \"underline\", \"unidirectional-pan\", \"unset\", \"up\",\n  \"upper-alpha\", \"upper-armenian\", \"upper-greek\", \"upper-hexadecimal\",\n  \"upper-latin\", \"upper-norwegian\", \"upper-roman\", \"uppercase\", \"urdu\", \"url\",\n  \"var\", \"vertical\", \"vertical-text\", \"view-box\", \"visible\", \"visibleFill\", \"visiblePainted\",\n  \"visibleStroke\", \"visual\", \"w-resize\", \"wait\", \"wave\", \"wider\",\n  \"window\", \"windowframe\", \"windowtext\", \"words\", \"wrap\", \"wrap-reverse\", \"x-large\", \"x-small\", \"xor\",\n  \"xx-large\", \"xx-small\"\n], valueKeywords = keySet(valueKeywords_);\n\nvar allWords = documentTypes_.concat(mediaTypes_).concat(mediaFeatures_).concat(mediaValueKeywords_)\n    .concat(propertyKeywords_).concat(nonStandardPropertyKeywords_).concat(colorKeywords_)\n    .concat(valueKeywords_);\n\nexport const keywords = {properties: propertyKeywords_, colors: colorKeywords_,\n                         fonts: fontProperties_, values: valueKeywords_, all: allWords}\n\nconst defaults = {\n  documentTypes: documentTypes,\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  fontProperties: fontProperties,\n  counterDescriptors: counterDescriptors,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false;\n      state.tokenize = tokenCComment;\n      return tokenCComment(stream, state);\n    }\n  }\n}\n\nexport const css = mkCSS({name: \"css\"})\n\nfunction tokenCComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return [\"comment\", \"comment\"];\n}\n\nexport const sCSS = mkCSS({\n  name: \"scss\",\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  fontProperties: fontProperties,\n  allowNested: true,\n  lineComment: \"//\",\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return [\"comment\", \"comment\"];\n      } else if (stream.eat(\"*\")) {\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      } else {\n        return [\"operator\", \"operator\"];\n      }\n    },\n    \":\": function(stream) {\n      if (stream.match(/^\\s*\\{/, false))\n        return [null, null]\n      return false;\n    },\n    \"$\": function(stream) {\n      stream.match(/^[\\w-]+/);\n      if (stream.match(/^\\s*:/, false))\n        return [\"def\", \"variable-definition\"];\n      return [\"variableName.special\", \"variable\"];\n    },\n    \"#\": function(stream) {\n      if (!stream.eat(\"{\")) return false;\n      return [null, \"interpolation\"];\n    }\n  }\n})\n\nexport const less = mkCSS({\n  name: \"less\",\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  fontProperties: fontProperties,\n  allowNested: true,\n  lineComment: \"//\",\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return [\"comment\", \"comment\"];\n      } else if (stream.eat(\"*\")) {\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      } else {\n        return [\"operator\", \"operator\"];\n      }\n    },\n    \"@\": function(stream) {\n      if (stream.eat(\"{\")) return [null, \"interpolation\"];\n      if (stream.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\\b/i, false)) return false;\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      if (stream.match(/^\\s*:/, false))\n        return [\"def\", \"variable-definition\"];\n      return [\"variableName\", \"variable\"];\n    },\n    \"&\": function() {\n      return [\"atom\", \"atom\"];\n    }\n  }\n})\n\nexport const gss = mkCSS({\n  name: \"gss\",\n  documentTypes: documentTypes,\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  fontProperties: fontProperties,\n  counterDescriptors: counterDescriptors,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  supportsAtComponent: true,\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false;\n      state.tokenize = tokenCComment;\n      return tokenCComment(stream, state);\n    }\n  }\n})\n"], "names": [], "mappings": ";;;;;;;;AAAO,SAAS,MAAM,YAAY;IAChC,eAAe;QAAC,GAAG,QAAQ;QAAE,GAAG,YAAY;IAAA;IAC5C,IAAI,SAAS,aAAa,MAAM;IAEhC,IAAI,aAAa,aAAa,UAAU,EACpC,gBAAgB,aAAa,aAAa,IAAI,CAAC,GAC/C,aAAa,aAAa,UAAU,IAAI,CAAC,GACzC,gBAAgB,aAAa,aAAa,IAAI,CAAC,GAC/C,qBAAqB,aAAa,kBAAkB,IAAI,CAAC,GACzD,mBAAmB,aAAa,gBAAgB,IAAI,CAAC,GACrD,8BAA8B,aAAa,2BAA2B,IAAI,CAAC,GAC3E,iBAAiB,aAAa,cAAc,IAAI,CAAC,GACjD,qBAAqB,aAAa,kBAAkB,IAAI,CAAC,GACzD,gBAAgB,aAAa,aAAa,IAAI,CAAC,GAC/C,gBAAgB,aAAa,aAAa,IAAI,CAAC,GAC/C,cAAc,aAAa,WAAW,EACtC,cAAc,aAAa,WAAW,EACtC,sBAAsB,aAAa,mBAAmB,KAAK,MAC3D,uCAAuC,aAAa,oCAAoC,KAAK;IAEjG,IAAI,MAAM;IACV,SAAS,IAAI,KAAK,EAAE,EAAE;QAAI,OAAO;QAAI,OAAO;IAAO;IAEnD,aAAa;IAEb,SAAS,UAAU,MAAM,EAAE,KAAK;QAC9B,IAAI,KAAK,OAAO,IAAI;QACpB,IAAI,UAAU,CAAC,GAAG,EAAE;YAClB,IAAI,SAAS,UAAU,CAAC,GAAG,CAAC,QAAQ;YACpC,IAAI,WAAW,OAAO,OAAO;QAC/B;QACA,IAAI,MAAM,KAAK;YACb,OAAO,QAAQ,CAAC;YAChB,OAAO,IAAI,OAAO,OAAO,OAAO;QAClC,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO,MAAM,GAAG,KAAK,OAAO,GAAG,CAAC,MAAM;YACnE,OAAO,IAAI,MAAM;QACnB,OAAO,IAAI,MAAM,QAAQ,MAAM,KAAK;YAClC,MAAM,QAAQ,GAAG,YAAY;YAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;QAChC,OAAO,IAAI,MAAM,KAAK;YACpB,OAAO,QAAQ,CAAC;YAChB,OAAO,IAAI,QAAQ;QACrB,OAAO,IAAI,MAAM,KAAK;YACpB,OAAO,KAAK,CAAC;YACb,OAAO,IAAI,WAAW;QACxB,OAAO,IAAI,KAAK,IAAI,CAAC,OAAO,MAAM,OAAO,OAAO,GAAG,CAAC,OAAO;YACzD,OAAO,QAAQ,CAAC;YAChB,OAAO,IAAI,UAAU;QACvB,OAAO,IAAI,OAAO,KAAK;YACrB,IAAI,QAAQ,IAAI,CAAC,OAAO,IAAI,KAAK;gBAC/B,OAAO,QAAQ,CAAC;gBAChB,OAAO,IAAI,UAAU;YACvB,OAAO,IAAI,OAAO,KAAK,CAAC,gBAAgB;gBACtC,OAAO,QAAQ,CAAC;gBAChB,IAAI,OAAO,KAAK,CAAC,SAAS,QACxB,OAAO,IAAI,OAAO;gBACpB,OAAO,IAAI,gBAAgB;YAC7B,OAAO,IAAI,OAAO,KAAK,CAAC,UAAU;gBAChC,OAAO,IAAI,QAAQ;YACrB;QACF,OAAO,IAAI,WAAW,IAAI,CAAC,KAAK;YAC9B,OAAO,IAAI,MAAM;QACnB,OAAO,IAAI,MAAM,OAAO,OAAO,KAAK,CAAC,0BAA0B;YAC7D,OAAO,IAAI,aAAa;QAC1B,OAAO,IAAI,iBAAiB,IAAI,CAAC,KAAK;YACpC,OAAO,IAAI,MAAM;QACnB,OAAO,IAAI,OAAO,KAAK,CAAC,mBAAmB;YACzC,IAAI,mCAAmC,IAAI,CAAC,OAAO,OAAO,KAAK;gBAC7D,MAAM,QAAQ,GAAG;YACnB;YACA,OAAO,IAAI,yBAAyB;QACtC,OAAO,IAAI,WAAW,IAAI,CAAC,KAAK;YAC9B,OAAO,QAAQ,CAAC;YAChB,OAAO,IAAI,YAAY;QACzB,OAAO;YACL,OAAO,IAAI,MAAM;QACnB;IACF;IAEA,SAAS,YAAY,KAAK;QACxB,OAAO,SAAS,MAAM,EAAE,KAAK;YAC3B,IAAI,UAAU,OAAO;YACrB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;gBACnC,IAAI,MAAM,SAAS,CAAC,SAAS;oBAC3B,IAAI,SAAS,KAAK,OAAO,MAAM,CAAC;oBAChC;gBACF;gBACA,UAAU,CAAC,WAAW,MAAM;YAC9B;YACA,IAAI,MAAM,SAAS,CAAC,WAAW,SAAS,KAAK,MAAM,QAAQ,GAAG;YAC9D,OAAO,IAAI,UAAU;QACvB;IACF;IAEA,SAAS,mBAAmB,MAAM,EAAE,KAAK;QACvC,OAAO,IAAI,IAAI,cAAc;QAC7B,IAAI,CAAC,OAAO,KAAK,CAAC,eAAe,QAC/B,MAAM,QAAQ,GAAG,YAAY;aAE7B,MAAM,QAAQ,GAAG;QACnB,OAAO,IAAI,MAAM;IACnB;IAEA,qBAAqB;IAErB,SAAS,QAAQ,IAAI,EAAE,MAAM,EAAE,IAAI;QACjC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;IACd;IAEA,SAAS,YAAY,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;QAC9C,MAAM,OAAO,GAAG,IAAI,QAAQ,MAAM,OAAO,WAAW,KAAK,CAAC,WAAW,QAAQ,IAAI,OAAO,UAAU,GAAG,MAAM,OAAO;QAClH,OAAO;IACT;IAEA,SAAS,WAAW,KAAK;QACvB,IAAI,MAAM,OAAO,CAAC,IAAI,EACpB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;QACpC,OAAO,MAAM,OAAO,CAAC,IAAI;IAC3B;IAEA,SAAS,KAAK,IAAI,EAAE,MAAM,EAAE,KAAK;QAC/B,OAAO,MAAM,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,QAAQ;IAClD;IACA,SAAS,WAAW,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;QACxC,IAAK,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,IAC1B,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;QACpC,OAAO,KAAK,MAAM,QAAQ;IAC5B;IAEA,SAAS;IAET,SAAS,YAAY,MAAM;QACzB,IAAI,OAAO,OAAO,OAAO,GAAG,WAAW;QACvC,IAAI,cAAc,cAAc,CAAC,OAC/B,WAAW;aACR,IAAI,cAAc,cAAc,CAAC,OACpC,WAAW;aAEX,WAAW;IACf;IAEA,IAAI,SAAS,CAAC;IAEd,OAAO,GAAG,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QACvC,IAAI,QAAQ,KAAK;YACf,OAAO,YAAY,OAAO,QAAQ;QACpC,OAAO,IAAI,QAAQ,OAAO,MAAM,OAAO,CAAC,IAAI,EAAE;YAC5C,OAAO,WAAW;QACpB,OAAO,IAAI,uBAAuB,cAAc,IAAI,CAAC,OAAO;YAC1D,OAAO,YAAY,OAAO,QAAQ;QACpC,OAAO,IAAI,uBAAuB,IAAI,CAAC,OAAO;YAC5C,OAAO,YAAY,OAAO,QAAQ;QACpC,OAAO,IAAI,+CAA+C,IAAI,CAAC,OAAO;YACpE,OAAO,YAAY,OAAO,QAAQ;QACpC,OAAO,IAAI,+BAA+B,IAAI,CAAC,OAAO;YACpD,MAAM,QAAQ,GAAG;YACjB,OAAO;QACT,OAAO,IAAI,sCAAsC,IAAI,CAAC,OAAO;YAC3D,OAAO;QACT,OAAO,IAAI,QAAQ,KAAK,MAAM,CAAC,MAAM,KAAK;YACxC,OAAO,YAAY,OAAO,QAAQ;QACpC,OAAO,IAAI,QAAQ,QAAQ;YACzB,WAAW;QACb,OAAO,IAAI,QAAQ,QAAQ;YACzB,WAAW;QACb,OAAO,IAAI,QAAQ,uBAAuB;YACxC,OAAO;QACT,OAAO,IAAI,QAAQ,iBAAiB;YAClC,OAAO,YAAY,OAAO,QAAQ;QACpC,OAAO,IAAI,QAAQ,KAAK;YACtB,OAAO;QACT,OAAO,IAAI,eAAe,QAAQ,KAAK;YACrC,OAAO,YAAY,OAAO,QAAQ;QACpC;QACA,OAAO,MAAM,OAAO,CAAC,IAAI;IAC3B;IAEA,OAAO,KAAK,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QACzC,IAAI,QAAQ,QAAQ;YAClB,IAAI,OAAO,OAAO,OAAO,GAAG,WAAW;YACvC,IAAI,iBAAiB,cAAc,CAAC,OAAO;gBACzC,WAAW;gBACX,OAAO;YACT,OAAO,IAAI,4BAA4B,cAAc,CAAC,OAAO;gBAC3D,WAAW,uCAAuC,mBAAmB;gBACrE,OAAO;YACT,OAAO,IAAI,aAAa;gBACtB,WAAW,OAAO,KAAK,CAAC,iBAAiB,SAAS,aAAa;gBAC/D,OAAO;YACT,OAAO;gBACL,WAAW;gBACX,OAAO;YACT;QACF,OAAO,IAAI,QAAQ,QAAQ;YACzB,OAAO;QACT,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,UAAU,QAAQ,WAAW,GAAG;YAClE,WAAW;YACX,OAAO;QACT,OAAO;YACL,OAAO,OAAO,GAAG,CAAC,MAAM,QAAQ;QAClC;IACF;IAEA,OAAO,SAAS,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QAC7C,IAAI,QAAQ,KAAK,OAAO,YAAY,OAAO,QAAQ;QACnD,OAAO,KAAK,MAAM,QAAQ;IAC5B;IAEA,OAAO,IAAI,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QACxC,IAAI,QAAQ,KAAK,OAAO,WAAW;QACnC,IAAI,QAAQ,OAAO,aAAa,OAAO,YAAY,OAAO,QAAQ;QAClE,IAAI,QAAQ,OAAO,QAAQ,KAAK,OAAO,WAAW,MAAM,QAAQ;QAChE,IAAI,QAAQ,KAAK,OAAO,YAAY,OAAO,QAAQ;QAEnD,IAAI,QAAQ,UAAU,CAAC,sDAAsD,IAAI,CAAC,OAAO,OAAO,KAAK;YACnG,WAAW;QACb,OAAO,IAAI,QAAQ,QAAQ;YACzB,YAAY;QACd,OAAO,IAAI,QAAQ,iBAAiB;YAClC,OAAO,YAAY,OAAO,QAAQ;QACpC;QACA,OAAO;IACT;IAEA,OAAO,SAAS,GAAG,SAAS,IAAI,EAAE,OAAO,EAAE,KAAK;QAC9C,IAAI,QAAQ,KAAK,OAAO,WAAW;QACnC,IAAI,QAAQ,QAAQ;YAAE,WAAW;YAAY,OAAO;QAAa;QACjE,OAAO,MAAM,OAAO,CAAC,IAAI;IAC3B;IAEA,OAAO,MAAM,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QAC1C,IAAI,QAAQ,OAAO,QAAQ,KAAK,OAAO,WAAW,MAAM,QAAQ;QAChE,IAAI,QAAQ,KAAK,OAAO,WAAW;QACnC,IAAI,QAAQ,KAAK,OAAO,YAAY,OAAO,QAAQ;QACnD,IAAI,QAAQ,iBAAiB,OAAO,YAAY,OAAO,QAAQ;QAC/D,IAAI,QAAQ,QAAQ,YAAY;QAChC,OAAO;IACT;IAEA,OAAO,MAAM,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QAC1C,IAAI,QAAQ,QAAQ,OAAO;QAE3B,IAAI,QAAQ,QAAQ;YAClB,WAAW;YACX,OAAO,MAAM,OAAO,CAAC,IAAI;QAC3B;QACA,OAAO,KAAK,MAAM,QAAQ;IAC5B;IAEA,OAAO,aAAa,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QACjD,IAAI,QAAQ,UAAU,cAAc,cAAc,CAAC,OAAO,OAAO,KAAK;YACpE,WAAW;YACX,OAAO,MAAM,OAAO,CAAC,IAAI;QAC3B,OAAO;YACL,OAAO,OAAO,OAAO,CAAC,MAAM,QAAQ;QACtC;IACF;IAEA,OAAO,OAAO,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QAC3C,IAAI,QAAQ,KAAK,OAAO,YAAY,OAAO,QAAQ;QACnD,IAAI,QAAQ,OAAO,QAAQ,KAAK,OAAO,WAAW,MAAM,QAAQ;QAChE,IAAI,QAAQ,KAAK,OAAO,WAAW,UAAU,YAAY,OAAO,QAAQ,cAAc,UAAU;QAEhG,IAAI,QAAQ,iBAAiB,OAAO,YAAY,OAAO,QAAQ;QAE/D,IAAI,QAAQ,QAAQ;YAClB,IAAI,OAAO,OAAO,OAAO,GAAG,WAAW;YACvC,IAAI,QAAQ,UAAU,QAAQ,SAAS,QAAQ,SAAS,QAAQ,MAC9D,WAAW;iBACR,IAAI,WAAW,cAAc,CAAC,OACjC,WAAW;iBACR,IAAI,cAAc,cAAc,CAAC,OACpC,WAAW;iBACR,IAAI,mBAAmB,cAAc,CAAC,OACzC,WAAW;iBACR,IAAI,iBAAiB,cAAc,CAAC,OACvC,WAAW;iBACR,IAAI,4BAA4B,cAAc,CAAC,OAClD,WAAW,uCAAuC,mBAAmB;iBAClE,IAAI,cAAc,cAAc,CAAC,OACpC,WAAW;iBACR,IAAI,cAAc,cAAc,CAAC,OACpC,WAAW;iBAEX,WAAW;QACf;QACA,OAAO,MAAM,OAAO,CAAC,IAAI;IAC3B;IAEA,OAAO,gBAAgB,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QACpD,IAAI,QAAQ,KACV,OAAO,WAAW,MAAM,QAAQ;QAClC,IAAI,QAAQ,KACV,OAAO,WAAW,UAAU,YAAY,OAAO,QAAQ,cAAc,UAAU,OAAO;QACxF,IAAI,QAAQ,QACV,WAAW;QACb,OAAO,MAAM,OAAO,CAAC,IAAI;IAC3B;IAEA,OAAO,cAAc,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QAClD,IAAI,QAAQ,KAAK,OAAO,WAAW;QACnC,IAAI,QAAQ,OAAO,QAAQ,KAAK,OAAO,WAAW,MAAM,QAAQ,OAAO;QACvE,OAAO,OAAO,OAAO,CAAC,MAAM,QAAQ;IACtC;IAEA,OAAO,yBAAyB,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QAC7D,IAAI,QAAQ,KACV,OAAO,YAAY,OAAO,QAAQ;QACpC,IAAI,QAAQ,UAAU,MAAM,QAAQ,IAAI,kBAAkB;YACxD,WAAW;YACX,OAAO;QACT;QACA,OAAO,KAAK,MAAM,QAAQ;IAC5B;IAEA,OAAO,kBAAkB,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QACtD,IAAI,QAAQ,KAAK;YACf,MAAM,QAAQ,GAAG;YACjB,OAAO,WAAW;QACpB;QACA,IAAI,QAAQ,QAAQ;YAClB,IAAI,AAAC,MAAM,QAAQ,IAAI,gBAAgB,CAAC,eAAe,cAAc,CAAC,OAAO,OAAO,GAAG,WAAW,OAC7F,MAAM,QAAQ,IAAI,oBAAoB,CAAC,mBAAmB,cAAc,CAAC,OAAO,OAAO,GAAG,WAAW,KACxG,WAAW;iBAEX,WAAW;YACb,OAAO;QACT;QACA,OAAO;IACT;IAEA,OAAO,SAAS,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QAC7C,IAAI,QAAQ,QAAQ;YAAE,WAAW;YAAY,OAAO;QAAa;QACjE,IAAI,QAAQ,KAAK,OAAO,YAAY,OAAO,QAAQ;QACnD,OAAO,KAAK,MAAM,QAAQ;IAC5B;IAEA,OAAO,EAAE,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QACtC,IAAI,QAAQ,KAAK,OAAO,WAAW;QACnC,IAAI,QAAQ,OAAO,QAAQ,KAAK,OAAO,WAAW,MAAM,QAAQ;QAChE,IAAI,QAAQ,QAAQ,WAAW;aAC1B,IAAI,QAAQ,QAAQ,WAAW;QACpC,OAAO;IACT;IAEA,OAAO,aAAa,GAAG,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;QACjD,IAAI,QAAQ,KAAK,OAAO,WAAW;QACnC,IAAI,QAAQ,OAAO,QAAQ,KAAK,OAAO,WAAW,MAAM,QAAQ;QAChE,IAAI,QAAQ,QAAQ,WAAW;aAC1B,IAAI,QAAQ,cAAc,QAAQ,OAAO,QAAQ,KAAK,WAAW;QACtE,OAAO;IACT;IAEA,OAAO;QACL,MAAM,aAAa,IAAI;QACvB,YAAY;YACV,OAAO;gBAAC,UAAU;gBACV,OAAO,SAAS,UAAU;gBAC1B,UAAU;gBACV,SAAS,IAAI,QAAQ,SAAS,UAAU,OAAO,GAAG;YAAK;QACjE;QAEA,OAAO,SAAS,MAAM,EAAE,KAAK;YAC3B,IAAI,CAAC,MAAM,QAAQ,IAAI,OAAO,QAAQ,IAAI,OAAO;YACjD,IAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,SAAS,EAAE,QAAQ;YAClD,IAAI,SAAS,OAAO,SAAS,UAAU;gBACrC,OAAO,KAAK,CAAC,EAAE;gBACf,QAAQ,KAAK,CAAC,EAAE;YAClB;YACA,WAAW;YACX,IAAI,QAAQ,WACV,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,QAAQ;YAClD,OAAO;QACT;QAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG;YACpC,IAAI,KAAK,MAAM,OAAO,EAAE,KAAK,aAAa,UAAU,MAAM,CAAC;YAC3D,IAAI,SAAS,GAAG,MAAM;YACtB,IAAI,GAAG,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,MAAM,GAAG,GAAG,KAAK,GAAG,IAAI;YAC/D,IAAI,GAAG,IAAI,EAAE;gBACX,IAAI,MAAM,OAAO,CAAC,GAAG,IAAI,IAAI,WAAW,GAAG,IAAI,IAAI,SACjC,GAAG,IAAI,IAAI,mBAAmB,GAAG,IAAI,IAAI,oBAAoB,GAAG;oBAChF,0CAA0C;oBAC1C,KAAK,GAAG,IAAI;oBACZ,SAAS,GAAG,MAAM;gBACpB,OAAO,IAAI,MAAM,OAAO,CAAC,GAAG,IAAI,IAAI,YAAY,GAAG,IAAI,IAAI,gBAAgB,KAChE,MAAM,OAAO,CAAC,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI,IAAI,SAAS,GAAG;oBACjE,sCAAsC;oBACtC,SAAS,KAAK,GAAG,CAAC,GAAG,GAAG,MAAM,GAAG,IAAI,IAAI;gBAC3C;YACF;YACA,OAAO;QACT;QAEA,cAAc;YACZ,eAAe;YACf,eAAe;gBAAC,MAAM;gBAAa,OAAO;oBAAC,MAAM;oBAAM,OAAO;gBAAI;YAAC;YACnE,cAAc;QAChB;IACF;AACF;;AAEA,SAAS,OAAO,KAAK;IACnB,IAAI,OAAO,CAAC;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;QACrC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,GAAG,GAAG;IACjC;IACA,OAAO;AACT;AAEA,IAAI,iBAAiB;IACnB;IAAU;IAAU;IAAO;CAC5B,EAAE,gBAAgB,OAAO;AAE1B,IAAI,cAAc;IAChB;IAAO;IAAS;IAAW;IAAY;IAAS;IAAc;IAC9D;IAAO;IAAM;CACd,EAAE,aAAa,OAAO;AAEvB,IAAI,iBAAiB;IACnB;IAAS;IAAa;IAAa;IAAU;IAAc;IAC3D;IAAgB;IAAoB;IAAoB;IACxD;IAAqB;IAAqB;IAC1C;IAAoB;IAAoB;IACxC;IAA2B;IAA2B;IAAS;IAC/D;IAAa;IAAe;IAAmB;IAC/C;IAAc;IAAkB;IAAkB;IAClD;IAAkB;IAAkB;IAAQ;IAAQ;IACpD;IAAsB;IAA0B;IAChD;IAAW;IAAe;IAAS;IAAa;IAChD;IAAiB;CAClB,EAAE,gBAAgB,OAAO;AAE1B,IAAI,sBAAsB;IACxB;IAAa;IAAY;IAAQ;IAAU;IAAQ;IAAa;IAChE;IAAa;IACb;IAAQ;IACR;IAAY;CACb,EAAE,qBAAqB,OAAO;AAE/B,IAAI,oBAAoB;IACtB;IAAiB;IAAe;IAAc;IAC9C;IAAsB;IAAO;IAAgB;IAAa;IAC1D;IAAuB;IAAsB;IAC7C;IAA6B;IAAkB;IAC/C;IAA6B;IAAc;IAAW;IACtD;IAAuB;IAAc;IACrC;IAAyB;IAAmB;IAC5C;IAAoB;IAAqB;IACzC;IAAyB;IAAyB;IAClD;IAAmB;IAAkB;IAAW;IAAS;IACzD;IAAkB;IAAkB;IAAkB;IACtD;IAAU;IAAiB;IAAuB;IAClD;IAA8B;IAAuB;IACrD;IAAmB;IAAgB;IAAgB;IACnD;IAAuB;IAAsB;IAC7C;IAAsB;IAAe;IAAqB;IAC1D;IAAqB;IAAiB;IAAgB;IACtD;IAAsB;IAAsB;IAAkB;IAC9D;IAAc;IAAoB;IAClC;IAA2B;IAAoB;IAC/C;IAAgB;IAAU;IAAwB;IAAc;IAChE;IAAe;IAAgB;IAAgB;IAAgB;IAC/D;IAAS;IAAQ;IAAS;IAAiB;IAAgB;IAC3D;IAAc;IAAe;IAAqB;IAClD;IAAqB;IAAe;IAAgB;IAAW;IAC/D;IAAW;IAAqB;IAAiB;IAAQ;IAAO;IAChE;IAAc;IAAU;IAAa;IAAW;IAChD;IAA6B;IAC7B;IAA8B;IAA6B;IAC3D;IAAsB;IAAa;IAAe;IAAO;IAAe;IACxE;IAAQ;IAAc;IAAkB;IAAa;IACrD;IAAe;IAAa;IAAS;IAAgB;IAAa;IAClE;IAAQ;IAAe;IAAyB;IAChD;IAA0B;IAAuB;IACjD;IAAoB;IAAgB;IAAc;IAClD;IAAgB;IAA2B;IAC3C;IAA2B;IAA0B;IACrD;IAAyB;IAA2B;IAAe;IACnE;IAAQ;IAAa;IAAqB;IAAkB;IAC5D;IAAe;IAAmB;IAAmB;IACrD;IAAY;IAAY;IAAgB;IAAgB;IACxD;IAAiB;IAAuB;IACxC;IAAsB;IAAuB;IAAU;IAAW;IAClE;IAAqB;IAAmB;IAAoB;IAC5D;IAAS;IAAe;IAAmB;IAAqB;IAChE;IAAoB;IAAsB;IAAa;IACvD;IAAiB;IAAgB;IAAQ;IAAkB;IAC3D;IAAe;IAAoB;IAAiB;IACpD;IAAuB;IAA0B;IACjD;IAAoB;IAAuB;IAAmB;IAC9D;IAAiB;IAAe;IAAgB;IAAc;IAC9D;IAAqB;IAAgB;IAAsB;IAC3D;IAAiB;IAAa;IAAkB;IAAc;IAC9D;IAAe;IAAiB;IAAe;IAAY;IAC3D;IAAkB;IAAc;IAChC;IAAa;IAAkB;IAAc;IAAmB;IAChE;IAAkB;IAAW;IAAY;IAAa;IAAY;IAClE;IAAU;IAAc;IAAmB;IAAU;IACrD;IAAmB;IAAe;IAAmB;IACrD;IAAW;IAAS;IAAW;IAAW;IAAiB;IAC3D;IAAiB;IAAiB;IAAY;IAC9C;IAAiB;IAAc;IAAc;IAAW;IACxD;IAAgB;IAAiB;IAAe;IAAQ;IACxD;IAAqB;IAAqB;IAAe;IACzD;IAAe;IAAgB;IAAe;IAAsB;IACpE;IAAe;IAAiB;IAAe;IAAc;IAC7D;IAAY;IAAsB;IAAoB;IACtD;IAAsB;IAAuB;IAC7C;IAAmB;IAAoB;IAAU;IAAQ;IACzD;IAAe;IAAY;IAAS;IAAU;IAAY;IAC1D;IAAW;IAAc;IAAiB;IAAiB;IAC3D;IAAS;IAAmB;IAAiB;IAC7C;IAA2B;IAA6B;IACxD;IAAwB;IACxB;IAA8B;IAAsB;IACpD;IAAqB;IAAkB;IACvC;IAA4B;IAC5B;IAAyB;IAAyB;IAClD;IAA+B;IAAuB;IACtD;IAAsB;IAAqB;IAC3C;IAAyB;IAAgB;IAAgB;IACzD;IAAQ;IAAS;IAAY;IAAgB;IAC7C;IAAqB;IAAe;IAAU;IAAc;IAC5D;IAAgB;IAAU;IAAe;IAAc;IACvD;IAAc;IAAmB;IAAwB;IACzD;IAAyB;IAAwB;IACjD;IAA4B;IAAyB;IACrD;IAAuB;IAA0B;IACjD;IAAe;IAAe;IAAgB;IAC9C;IAAgB;IAAiB;IAAkB;IACnD;IAAoB;IAAuB;IAC3C;IAA2B;IAAa;IAAO;IAAgB;IAAa;IAC5E;IAAmB;IAAc;IAAoB;IACrD;IAAuB;IAA8B;IACrD;IAAgB;IAAe;IAAkB;IAAc;IAC/D;IAAkB;IAAgB;IAAe;IAAe;IAChE;IAAgB;IAAgB;IAAU;IAAe;IAAU;IACnE;IAAe;IAAc;IAAgB;IAAa;IAAgB;IAC1E,eAAe;IACf;IAAa;IAAa;IAAQ;IAAqB;IAAU;IACjE;IAAiB;IAAkB;IAAc;IAAgB;IACjE;IAAuB;IACvB;IAAmB;IAAQ;IAAgB;IAAa;IACxD;IAAU;IAAc;IAAc;IAAgB;IAAe;IAAmB;IACxF;IAAoB;IAAqB;IAAkB;IAC3D;IAAqB;IAAkB;IAAgB;IACvD;IAAkB;IAAqB;IACvC;IAA8B;IAAe;CAC9C,EAAE,mBAAmB,OAAO;AAE7B,IAAI,+BAA+B;IACjC;IAAgB;IAAgB;IAAgB;IAAsB;IACtE;IAA0B;IAA0B;IACpD;IAAsB;IAA4B;IAClD;IAA4B;IAAsB;IAClD;IAAiB;IAAuB;IACxC;IAA2B;IAC3B;IAA2B;IAAuB;IAClD;IAA6B;IAC7B;IAAuB;IAAuB;IAAsB;IACpE;IAAoB;IAAsB;IAAiB;IAC3D;IAAuB;IAAmB;IAAuB;IAAiB;IAClF;IAAuB;IAAkB;IACzC;IAAwB;IAAoB;IAC5C;IAAyB;IAAwB;IACjD;IAAwB;IAA6B;IACrD;IAAyB;IAA6B;IACtD;IAA8B;IAAkC;IAAgB;CACjF,EAAE,8BAA8B,OAAO;AAExC,IAAI,kBAAkB;IACpB;IAAgB;IAAe;IAAO;IAAiB;IACvD;IAAyB;IAAgB;IAAe;CACzD,EAAE,iBAAiB,OAAO;AAE3B,IAAI,sBAAsB;IACxB;IAAoB;IAAY;IAAY;IAAO;IAAU;IAC7D;IAAY;IAAU;IAAW;CAClC,EAAE,qBAAqB,OAAO;AAE/B,IAAI,iBAAiB;IACnB;IAAa;IAAgB;IAAQ;IAAc;IAAS;IAC5D;IAAU;IAAS;IAAkB;IAAQ;IAAc;IAC3D;IAAa;IAAa;IAAc;IAAa;IAAS;IAC9D;IAAY;IAAW;IAAQ;IAAY;IAAY;IACvD;IAAY;IAAa;IAAY;IAAa;IAAe;IACjE;IAAc;IAAc;IAAW;IAAc;IACrD;IAAiB;IAAiB;IAAiB;IAAiB;IACpE;IAAY;IAAe;IAAW;IAAW;IAAc;IAC/D;IAAe;IAAe;IAAW;IAAa;IACtD;IAAQ;IAAa;IAAQ;IAAQ;IAAS;IAAe;IAC7D;IAAW;IAAa;IAAU;IAAS;IAAS;IACpD;IAAiB;IAAa;IAAgB;IAAa;IAC3D;IAAa;IAAwB;IAAa;IAAc;IAAa;IAC7E;IAAe;IAAiB;IAAgB;IAAkB;IAClE;IAAkB;IAAe;IAAQ;IAAa;IAAS;IAC/D;IAAU;IAAoB;IAAc;IAAgB;IAC5D;IAAkB;IAAmB;IAAqB;IAC1D;IAAmB;IAAgB;IAAa;IAAa;IAC7D;IAAe;IAAQ;IAAW;IAAS;IAAa;IAAU;IAClE;IAAU;IAAiB;IAAa;IAAiB;IACzD;IAAc;IAAa;IAAQ;IAAQ;IAAQ;IACnD;IAAU;IAAiB;IAAO;IAAa;IAAa;IAC5D;IAAU;IAAc;IAAY;IAAY;IAAU;IAAU;IACpE;IAAa;IAAa;IAAa;IAAQ;IAAe;IAAa;IAC3E;IAAQ;IAAW;IAAU;IAAa;IAAU;IAAS;IAC7D;IAAc;IAAU;CACzB,EAAE,gBAAgB,OAAO;AAE1B,IAAI,iBAAiB;IACnB;IAAS;IAAY;IAAgB;IAAY;IAAiB;IAClE;IAAqB;IAAS;IAAS;IAAO;IAAc;IAAc;IAC1E;IAAU;IAAW;IAAmB;IAAe;IACvD;IAAgB;IAAY;IAAa;IAAQ;IAAQ;IAAa;IAAS;IAAgB;IAC/F;IAAgB;IAAY;IAAc;IAAa;IAAY;IAAS;IAAiB;IAC7F;IAAW;IAAS;IAAS;IAAc;IAAQ;IAAQ;IAAU;IAAU;IAC/E;IAAQ;IAAU;IAAS;IAAa;IAAc;IAAc;IAAW;IAC/E;IAAc;IAAmB;IAAgB;IAAc;IAAQ;IACvE;IAAc;IAAuB;IAAW;IAAe;IAC/D;IAAQ;IAAU;IAAY;IAAU;IAAe;IACvD;IAAqB;IAAmB;IAAS;IAAQ;IACzD;IAAc;IAAY;IAAS;IAAc;IAAe;IAAU;IAC1E;IAAW;IAAa;IAAkB;IAAW;IAAW;IAChE;IAAe;IAAgB;IAAc;IAAY;IAAQ;IAAW;IAAY;IAAS;IACjG;IAAS;IAAa;IAAgB;IAAgB;IAAW;IAAU;IAAU;IAAU;IAC/F;IAAwB;IAAW;IAAkB;IAAS;IAC9D;IAAkB;IAAmB;IAAoB;IAAc;IACvE;IAAQ;IAAW;IAAqB;IAAmB;IAC3D;IAAY;IACZ;IAAU;IAAU;IAAQ;IAAe;IAAY;IAAQ;IAAW;IAAe;IACzF;IAAW;IAAW;IAAY;IAAS;IAAO;IAAY;IAC9D;IAA0B;IAAwB;IAClD;IAA0B;IAC1B;IAA2B;IAC3B;IAAyB;IACzB;IAA4B;IAC5B;IAA2B;IAA2B;IACtD;IAAoB;IAAa;IAAa;IAAY;IAAW;IACrE;IAAkB;IAAW;IAAQ;IAAQ;IAAY;IAAS;IAAQ;IAAQ;IAAY;IAAc;IAC5G;IAAY;IAAQ;IAAsB;IAAY;IAAa;IAAY;IAAQ;IACvF;IAAY;IAAY;IAAQ;IAAU;IAAoB;IAAc;IAC5E;IAAQ;IAAU;IAAQ;IAAU;IAAa;IACjD;IAAY;IAAkB;IAAc;IAAO;IAAQ;IAAO;IAAc;IAAQ;IACxF;IAAkB;IAAmB;IAAuB;IAC5D;IAAkB;IAAY;IAAW;IAAW;IAAU;IAC9D;IAAgB;IAAe;IAAe;IAAgB;IAAS;IAAU;IAAa;IAC9F;IAAU;IAAmB;IAAqB;IAAW;IAC7D;IAAY;IAAkB;IAAY;IAC1C;IAAwB;IAAuB;IAC/C;IAAa;IAAO;IAAS;IAAU;IAAQ;IAAS;IAAW;IACnE;IAAgB;IAAU;IAAmB;IAAS;IAAa;IAAW;IAC9E;IAAS;IAAW;IAAQ;IAAS;IAAe;IACpD;IAAe;IAAqB;IAAe;IACnD;IAAe;IAAa;IAAO;IAAc;IAAa;IAAgB;IAAS;IAAU;IACjG;IAAqB;IAAgB;IACrC;IAAuB;IAA4B;IACnD;IAAQ;IAAY;IACpB;IAAY;IAAe;IAAU;IACrC;IAAO;IAAa;IAAa;IAAQ;IAAY;IAAwB;IAAY;IAAW;IACpG;IAAY;IAAa;IAAe;IAAkB;IAC1D;IAAiB;IAAa;IAAQ;IAAU;IAAe;IAC/D;IAAa;IAAW;IAAW;IAAa;IAAe;IAAW;IAAS;IAAW;IAC9F;IAAsB;IAAiB;IAAS;IAAS;IACzD;IAAW;IAAiB;IAAW;IAAY;IAAW;IAC9D;IAAW;IAAQ;IAAU;IAAW;IAAe;IAAc;IAAe;IACpF;IAAW;IAAW;IAAY;IAAO;IAAY;IAAY;IACjE;IAAY;IAAe;IAAmB;IAAS;IACvD;IAAc;IAA6B;IAAa;IACxD;IAAY;IAAU;IAA6B;IACnD;IAA4B;IAAY;IAAY;IAAS;IAC7D;IAAO;IAAQ;IAAS;IAAS;IAAU;IAAY;IAAW;IAClE;IAAW;IAAS;IAAO;IAAc;IAAe;IAAO;IAAU;IACzE;IAAY;IAAc;IAAY;IAAc;IAAS;IAAW;IAAU;IAAU;IAAU;IACtG;IAAU;IAAa;IAAmB;IAAa;IACvD;IAA6B;IAC7B;IAA8B;IAAkC;IAAc;IAC9E;IAAkB;IAAiB;IAAY;IAAS;IAAS;IAAQ;IACzE;IAAuB;IAAyB;IAChD;IAAQ;IAAS;IAAS;IAAoB;IAAS;IACvD;IAAmB;IAA0B;IAAwB;IACrE;IAAS;IAAc;IAAiB;IAAW;IAAc;IAAS;IAC1E;IAAe;IAAa;IAAc;IAAe;IAAS;IAAgB;IAAiB;IAAgB;IAAa;IAChI;IAAiB;IAAS;IAAU;IAAc;IAAW;IAAU;IAAc;IACrF;IAAwB;IAAa;IAAS;IAAa;IAAY;IAAW;IAAa;IAC/F;IAAiB;IAAc;IAAgB;IAC/C;IAAsB;IAAsB;IAAa;IACzD;IACA;IAAU;IAAQ;IAAe;IAAY;IAAY;IAAa;IACtE;IAAS;IAAQ;IAAoB;IAAc;IACnD;IAAqB;IAAgB;IAAW;IAAS;IACzD;IAAuB;IAAe;IAAuB;IAAM;IACnE;IAAuB;IAAyB;IAChD;IAAa;IAAe;IAAc;IAAc;IACxD;IAAe;IAAmB;IAAkB;IAAa;IAAsB;IAAS;IAChG;IAAe;IAAkB;IAAe;IAChD;IAAe;IAAmB;IAAe;IAAa;IAAQ;IACtE;IAAO;IAAY;IAAiB;IAAY;IAAW;IAAe;IAC1E;IAAiB;IAAU;IAAY;IAAQ;IAAQ;IACvD;IAAU;IAAe;IAAc;IAAS;IAAQ;IAAgB;IAAW;IAAW;IAC9F;IAAY;CACb,EAAE,gBAAgB,OAAO;AAE1B,IAAI,WAAW,eAAe,MAAM,CAAC,aAAa,MAAM,CAAC,gBAAgB,MAAM,CAAC,qBAC3E,MAAM,CAAC,mBAAmB,MAAM,CAAC,8BAA8B,MAAM,CAAC,gBACtE,MAAM,CAAC;AAEL,MAAM,WAAW;IAAC,YAAY;IAAmB,QAAQ;IACvC,OAAO;IAAiB,QAAQ;IAAgB,KAAK;AAAQ;AAEtF,MAAM,WAAW;IACf,eAAe;IACf,YAAY;IACZ,eAAe;IACf,oBAAoB;IACpB,kBAAkB;IAClB,6BAA6B;IAC7B,gBAAgB;IAChB,oBAAoB;IACpB,eAAe;IACf,eAAe;IACf,YAAY;QACV,KAAK,SAAS,MAAM,EAAE,KAAK;YACzB,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,OAAO;YAC7B,MAAM,QAAQ,GAAG;YACjB,OAAO,cAAc,QAAQ;QAC/B;IACF;AACF;AAEO,MAAM,MAAM,MAAM;IAAC,MAAM;AAAK;AAErC,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,IAAI,WAAW,OAAO;IACtB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;QACnC,IAAI,YAAY,MAAM,KAAK;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;QAAC;QAAW;KAAU;AAC/B;AAEO,MAAM,OAAO,MAAM;IACxB,MAAM;IACN,YAAY;IACZ,eAAe;IACf,oBAAoB;IACpB,kBAAkB;IAClB,6BAA6B;IAC7B,eAAe;IACf,eAAe;IACf,gBAAgB;IAChB,aAAa;IACb,aAAa;IACb,YAAY;QACV,KAAK,SAAS,MAAM,EAAE,KAAK;YACzB,IAAI,OAAO,GAAG,CAAC,MAAM;gBACnB,OAAO,SAAS;gBAChB,OAAO;oBAAC;oBAAW;iBAAU;YAC/B,OAAO,IAAI,OAAO,GAAG,CAAC,MAAM;gBAC1B,MAAM,QAAQ,GAAG;gBACjB,OAAO,cAAc,QAAQ;YAC/B,OAAO;gBACL,OAAO;oBAAC;oBAAY;iBAAW;YACjC;QACF;QACA,KAAK,SAAS,MAAM;YAClB,IAAI,OAAO,KAAK,CAAC,UAAU,QACzB,OAAO;gBAAC;gBAAM;aAAK;YACrB,OAAO;QACT;QACA,KAAK,SAAS,MAAM;YAClB,OAAO,KAAK,CAAC;YACb,IAAI,OAAO,KAAK,CAAC,SAAS,QACxB,OAAO;gBAAC;gBAAO;aAAsB;YACvC,OAAO;gBAAC;gBAAwB;aAAW;QAC7C;QACA,KAAK,SAAS,MAAM;YAClB,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,OAAO;YAC7B,OAAO;gBAAC;gBAAM;aAAgB;QAChC;IACF;AACF;AAEO,MAAM,OAAO,MAAM;IACxB,MAAM;IACN,YAAY;IACZ,eAAe;IACf,oBAAoB;IACpB,kBAAkB;IAClB,6BAA6B;IAC7B,eAAe;IACf,eAAe;IACf,gBAAgB;IAChB,aAAa;IACb,aAAa;IACb,YAAY;QACV,KAAK,SAAS,MAAM,EAAE,KAAK;YACzB,IAAI,OAAO,GAAG,CAAC,MAAM;gBACnB,OAAO,SAAS;gBAChB,OAAO;oBAAC;oBAAW;iBAAU;YAC/B,OAAO,IAAI,OAAO,GAAG,CAAC,MAAM;gBAC1B,MAAM,QAAQ,GAAG;gBACjB,OAAO,cAAc,QAAQ;YAC/B,OAAO;gBACL,OAAO;oBAAC;oBAAY;iBAAW;YACjC;QACF;QACA,KAAK,SAAS,MAAM;YAClB,IAAI,OAAO,GAAG,CAAC,MAAM,OAAO;gBAAC;gBAAM;aAAgB;YACnD,IAAI,OAAO,KAAK,CAAC,yGAAyG,QAAQ,OAAO;YACzI,OAAO,QAAQ,CAAC;YAChB,IAAI,OAAO,KAAK,CAAC,SAAS,QACxB,OAAO;gBAAC;gBAAO;aAAsB;YACvC,OAAO;gBAAC;gBAAgB;aAAW;QACrC;QACA,KAAK;YACH,OAAO;gBAAC;gBAAQ;aAAO;QACzB;IACF;AACF;AAEO,MAAM,MAAM,MAAM;IACvB,MAAM;IACN,eAAe;IACf,YAAY;IACZ,eAAe;IACf,kBAAkB;IAClB,6BAA6B;IAC7B,gBAAgB;IAChB,oBAAoB;IACpB,eAAe;IACf,eAAe;IACf,qBAAqB;IACrB,YAAY;QACV,KAAK,SAAS,MAAM,EAAE,KAAK;YACzB,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,OAAO;YAC7B,MAAM,QAAQ,GAAG;YACjB,OAAO,cAAc,QAAQ;QAC/B;IACF;AACF", "ignoreList": [0], "debugId": null}}]}