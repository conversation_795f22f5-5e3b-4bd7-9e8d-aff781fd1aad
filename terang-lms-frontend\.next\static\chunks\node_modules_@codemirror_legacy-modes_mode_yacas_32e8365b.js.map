{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/yacas.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar bodiedOps = words(\"Assert BackQuote D Defun Deriv For ForEach FromFile \" +\n                      \"FromString Function Integrate InverseTaylor Limit \" +\n                      \"LocalSymbols Macro MacroRule MacroRulePattern \" +\n                      \"NIntegrate Rule RulePattern Subst TD TExplicitSum \" +\n                      \"TSum Taylor Taylor1 Taylor2 Taylor3 ToFile \" +\n                      \"ToStdout ToString TraceRule Until While\");\n\n// patterns\nvar pFloatForm  = \"(?:(?:\\\\.\\\\d+|\\\\d+\\\\.\\\\d*|\\\\d+)(?:[eE][+-]?\\\\d+)?)\";\nvar pIdentifier = \"(?:[a-zA-Z\\\\$'][a-zA-Z0-9\\\\$']*)\";\n\n// regular expressions\nvar reFloatForm    = new RegExp(pFloatForm);\nvar reIdentifier   = new RegExp(pIdentifier);\nvar rePattern      = new RegExp(pIdentifier + \"?_\" + pIdentifier);\nvar reFunctionLike = new RegExp(pIdentifier + \"\\\\s*\\\\(\");\n\nfunction tokenBase(stream, state) {\n  var ch;\n\n  // get next character\n  ch = stream.next();\n\n  // string\n  if (ch === '\"') {\n    state.tokenize = tokenString;\n    return state.tokenize(stream, state);\n  }\n\n  // comment\n  if (ch === '/') {\n    if (stream.eat('*')) {\n      state.tokenize = tokenComment;\n      return state.tokenize(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n\n  // go back one character\n  stream.backUp(1);\n\n  // update scope info\n  var m = stream.match(/^(\\w+)\\s*\\(/, false);\n  if (m !== null && bodiedOps.hasOwnProperty(m[1]))\n    state.scopes.push('bodied');\n\n  var scope = currentScope(state);\n\n  if (scope === 'bodied' && ch === '[')\n    state.scopes.pop();\n\n  if (ch === '[' || ch === '{' || ch === '(')\n    state.scopes.push(ch);\n\n  scope = currentScope(state);\n\n  if (scope === '[' && ch === ']' ||\n      scope === '{' && ch === '}' ||\n      scope === '(' && ch === ')')\n    state.scopes.pop();\n\n  if (ch === ';') {\n    while (scope === 'bodied') {\n      state.scopes.pop();\n      scope = currentScope(state);\n    }\n  }\n\n  // look for ordered rules\n  if (stream.match(/\\d+ *#/, true, false)) {\n    return 'qualifier';\n  }\n\n  // look for numbers\n  if (stream.match(reFloatForm, true, false)) {\n    return 'number';\n  }\n\n  // look for placeholders\n  if (stream.match(rePattern, true, false)) {\n    return 'variableName.special';\n  }\n\n  // match all braces separately\n  if (stream.match(/(?:\\[|\\]|{|}|\\(|\\))/, true, false)) {\n    return 'bracket';\n  }\n\n  // literals looking like function calls\n  if (stream.match(reFunctionLike, true, false)) {\n    stream.backUp(1);\n    return 'variableName.function';\n  }\n\n  // all other identifiers\n  if (stream.match(reIdentifier, true, false)) {\n    return 'variable';\n  }\n\n  // operators; note that operators like @@ or /; are matched separately for each symbol.\n  if (stream.match(/(?:\\\\|\\+|\\-|\\*|\\/|,|;|\\.|:|@|~|=|>|<|&|\\||_|`|'|\\^|\\?|!|%|#)/, true, false)) {\n    return 'operator';\n  }\n\n  // everything else is an error\n  return 'error';\n}\n\nfunction tokenString(stream, state) {\n  var next, end = false, escaped = false;\n  while ((next = stream.next()) != null) {\n    if (next === '\"' && !escaped) {\n      end = true;\n      break;\n    }\n    escaped = !escaped && next === '\\\\';\n  }\n  if (end && !escaped) {\n    state.tokenize = tokenBase;\n  }\n  return 'string';\n};\n\nfunction tokenComment(stream, state) {\n  var prev, next;\n  while((next = stream.next()) != null) {\n    if (prev === '*' && next === '/') {\n      state.tokenize = tokenBase;\n      break;\n    }\n    prev = next;\n  }\n  return 'comment';\n}\n\nfunction currentScope(state) {\n  var scope = null;\n  if (state.scopes.length > 0)\n    scope = state.scopes[state.scopes.length - 1];\n  return scope;\n}\n\nexport const yacas = {\n  name: \"yacas\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      scopes: []\n    };\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  },\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize !== tokenBase && state.tokenize !== null)\n      return null;\n\n    var delta = 0;\n    if (textAfter === ']' || textAfter === '];' ||\n        textAfter === '}' || textAfter === '};' ||\n        textAfter === ');')\n      delta = -1;\n\n    return (state.scopes.length + delta) * cx.unit;\n  },\n\n  languageData: {\n    electricInput: /[{}\\[\\]()\\;]/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AAEA,IAAI,YAAY,MAAM,yDACA,uDACA,mDACA,uDACA,gDACA;AAEtB,WAAW;AACX,IAAI,aAAc;AAClB,IAAI,cAAc;AAElB,sBAAsB;AACtB,IAAI,cAAiB,IAAI,OAAO;AAChC,IAAI,eAAiB,IAAI,OAAO;AAChC,IAAI,YAAiB,IAAI,OAAO,cAAc,OAAO;AACrD,IAAI,iBAAiB,IAAI,OAAO,cAAc;AAE9C,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI;IAEJ,qBAAqB;IACrB,KAAK,OAAO,IAAI;IAEhB,SAAS;IACT,IAAI,OAAO,KAAK;QACd,MAAM,QAAQ,GAAG;QACjB,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IAEA,UAAU;IACV,IAAI,OAAO,KAAK;QACd,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,MAAM,QAAQ,GAAG;YACjB,OAAO,MAAM,QAAQ,CAAC,QAAQ;QAChC;QACA,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO,SAAS;YAChB,OAAO;QACT;IACF;IAEA,wBAAwB;IACxB,OAAO,MAAM,CAAC;IAEd,oBAAoB;IACpB,IAAI,IAAI,OAAO,KAAK,CAAC,eAAe;IACpC,IAAI,MAAM,QAAQ,UAAU,cAAc,CAAC,CAAC,CAAC,EAAE,GAC7C,MAAM,MAAM,CAAC,IAAI,CAAC;IAEpB,IAAI,QAAQ,aAAa;IAEzB,IAAI,UAAU,YAAY,OAAO,KAC/B,MAAM,MAAM,CAAC,GAAG;IAElB,IAAI,OAAO,OAAO,OAAO,OAAO,OAAO,KACrC,MAAM,MAAM,CAAC,IAAI,CAAC;IAEpB,QAAQ,aAAa;IAErB,IAAI,UAAU,OAAO,OAAO,OACxB,UAAU,OAAO,OAAO,OACxB,UAAU,OAAO,OAAO,KAC1B,MAAM,MAAM,CAAC,GAAG;IAElB,IAAI,OAAO,KAAK;QACd,MAAO,UAAU,SAAU;YACzB,MAAM,MAAM,CAAC,GAAG;YAChB,QAAQ,aAAa;QACvB;IACF;IAEA,yBAAyB;IACzB,IAAI,OAAO,KAAK,CAAC,UAAU,MAAM,QAAQ;QACvC,OAAO;IACT;IAEA,mBAAmB;IACnB,IAAI,OAAO,KAAK,CAAC,aAAa,MAAM,QAAQ;QAC1C,OAAO;IACT;IAEA,wBAAwB;IACxB,IAAI,OAAO,KAAK,CAAC,WAAW,MAAM,QAAQ;QACxC,OAAO;IACT;IAEA,8BAA8B;IAC9B,IAAI,OAAO,KAAK,CAAC,uBAAuB,MAAM,QAAQ;QACpD,OAAO;IACT;IAEA,uCAAuC;IACvC,IAAI,OAAO,KAAK,CAAC,gBAAgB,MAAM,QAAQ;QAC7C,OAAO,MAAM,CAAC;QACd,OAAO;IACT;IAEA,wBAAwB;IACxB,IAAI,OAAO,KAAK,CAAC,cAAc,MAAM,QAAQ;QAC3C,OAAO;IACT;IAEA,uFAAuF;IACvF,IAAI,OAAO,KAAK,CAAC,gEAAgE,MAAM,QAAQ;QAC7F,OAAO;IACT;IAEA,8BAA8B;IAC9B,OAAO;AACT;AAEA,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,IAAI,MAAM,MAAM,OAAO,UAAU;IACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;QACrC,IAAI,SAAS,OAAO,CAAC,SAAS;YAC5B,MAAM;YACN;QACF;QACA,UAAU,CAAC,WAAW,SAAS;IACjC;IACA,IAAI,OAAO,CAAC,SAAS;QACnB,MAAM,QAAQ,GAAG;IACnB;IACA,OAAO;AACT;;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,MAAM;IACV,MAAM,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;QACpC,IAAI,SAAS,OAAO,SAAS,KAAK;YAChC,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AAEA,SAAS,aAAa,KAAK;IACzB,IAAI,QAAQ;IACZ,IAAI,MAAM,MAAM,CAAC,MAAM,GAAG,GACxB,QAAQ,MAAM,MAAM,CAAC,MAAM,MAAM,CAAC,MAAM,GAAG,EAAE;IAC/C,OAAO;AACT;AAEO,MAAM,QAAQ;IACnB,MAAM;IACN,YAAY;QACV,OAAO;YACL,UAAU;YACV,QAAQ,EAAE;QACZ;IACF;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IACA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,MAAM,QAAQ,KAAK,aAAa,MAAM,QAAQ,KAAK,MACrD,OAAO;QAET,IAAI,QAAQ;QACZ,IAAI,cAAc,OAAO,cAAc,QACnC,cAAc,OAAO,cAAc,QACnC,cAAc,MAChB,QAAQ,CAAC;QAEX,OAAO,CAAC,MAAM,MAAM,CAAC,MAAM,GAAG,KAAK,IAAI,GAAG,IAAI;IAChD;IAEA,cAAc;QACZ,eAAe;QACf,eAAe;YAAC,MAAM;YAAM,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;IAC9D;AACF", "ignoreList": [0], "debugId": null}}]}