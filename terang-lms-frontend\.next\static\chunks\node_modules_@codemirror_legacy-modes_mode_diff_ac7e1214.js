(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@codemirror/legacy-modes/mode/diff.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "diff": (()=>diff)
});
var TOKEN_NAMES = {
    '+': 'inserted',
    '-': 'deleted',
    '@': 'meta'
};
const diff = {
    name: "diff",
    token: function(stream) {
        var tw_pos = stream.string.search(/[\t ]+?$/);
        if (!stream.sol() || tw_pos === 0) {
            stream.skipToEnd();
            return ("error " + (TOKEN_NAMES[stream.string.charAt(0)] || '')).replace(/ $/, '');
        }
        var token_name = TOKEN_NAMES[stream.peek()] || stream.skipToEnd();
        if (tw_pos === -1) {
            stream.skipToEnd();
        } else {
            stream.pos = tw_pos;
        }
        return token_name;
    }
};
}}),
}]);

//# sourceMappingURL=node_modules_%40codemirror_legacy-modes_mode_diff_ac7e1214.js.map