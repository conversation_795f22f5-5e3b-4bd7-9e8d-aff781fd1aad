{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/modelica.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i=0; i<words.length; ++i)\n    obj[words[i]] = true;\n  return obj;\n}\n\nvar keywords = words(\"algorithm and annotation assert block break class connect connector constant constrainedby der discrete each else elseif elsewhen encapsulated end enumeration equation expandable extends external false final flow for function if import impure in initial inner input loop model not operator or outer output package parameter partial protected public pure record redeclare replaceable return stream then true type when while within\")\nvar builtin = words(\"abs acos actualStream asin atan atan2 cardinality ceil cos cosh delay div edge exp floor getInstanceName homotopy inStream integer log log10 mod pre reinit rem semiLinear sign sin sinh spatialDistribution sqrt tan tanh\")\nvar atoms = words(\"Real Boolean Integer String\")\n\nvar completions = [].concat(Object.keys(keywords), Object.keys(builtin), Object.keys(atoms))\n\nvar isSingleOperatorChar = /[;=\\(:\\),{}.*<>+\\-\\/^\\[\\]]/;\nvar isDoubleOperatorChar = /(:=|<=|>=|==|<>|\\.\\+|\\.\\-|\\.\\*|\\.\\/|\\.\\^)/;\nvar isDigit = /[0-9]/;\nvar isNonDigit = /[_a-zA-Z]/;\n\nfunction tokenLineComment(stream, state) {\n  stream.skipToEnd();\n  state.tokenize = null;\n  return \"comment\";\n}\n\nfunction tokenBlockComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(stream, state) {\n  var escaped = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (ch == '\"' && !escaped) {\n      state.tokenize = null;\n      state.sol = false;\n      break;\n    }\n    escaped = !escaped && ch == \"\\\\\";\n  }\n\n  return \"string\";\n}\n\nfunction tokenIdent(stream, state) {\n  stream.eatWhile(isDigit);\n  while (stream.eat(isDigit) || stream.eat(isNonDigit)) { }\n\n\n  var cur = stream.current();\n\n  if(state.sol && (cur == \"package\" || cur == \"model\" || cur == \"when\" || cur == \"connector\")) state.level++;\n  else if(state.sol && cur == \"end\" && state.level > 0) state.level--;\n\n  state.tokenize = null;\n  state.sol = false;\n\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  else if (builtin.propertyIsEnumerable(cur)) return \"builtin\";\n  else if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  else return \"variable\";\n}\n\nfunction tokenQIdent(stream, state) {\n  while (stream.eat(/[^']/)) { }\n\n  state.tokenize = null;\n  state.sol = false;\n\n  if(stream.eat(\"'\"))\n    return \"variable\";\n  else\n    return \"error\";\n}\n\nfunction tokenUnsignedNumber(stream, state) {\n  stream.eatWhile(isDigit);\n  if (stream.eat('.')) {\n    stream.eatWhile(isDigit);\n  }\n  if (stream.eat('e') || stream.eat('E')) {\n    if (!stream.eat('-'))\n      stream.eat('+');\n    stream.eatWhile(isDigit);\n  }\n\n  state.tokenize = null;\n  state.sol = false;\n  return \"number\";\n}\n\n// Interface\nexport const modelica = {\n  name: \"modelica\",\n  startState: function() {\n    return {\n      tokenize: null,\n      level: 0,\n      sol: true\n    };\n  },\n\n  token: function(stream, state) {\n    if(state.tokenize != null) {\n      return state.tokenize(stream, state);\n    }\n\n    if(stream.sol()) {\n      state.sol = true;\n    }\n\n    // WHITESPACE\n    if(stream.eatSpace()) {\n      state.tokenize = null;\n      return null;\n    }\n\n    var ch = stream.next();\n\n    // LINECOMMENT\n    if(ch == '/' && stream.eat('/')) {\n      state.tokenize = tokenLineComment;\n    }\n    // BLOCKCOMMENT\n    else if(ch == '/' && stream.eat('*')) {\n      state.tokenize = tokenBlockComment;\n    }\n    // TWO SYMBOL TOKENS\n    else if(isDoubleOperatorChar.test(ch+stream.peek())) {\n      stream.next();\n      state.tokenize = null;\n      return \"operator\";\n    }\n    // SINGLE SYMBOL TOKENS\n    else if(isSingleOperatorChar.test(ch)) {\n      state.tokenize = null;\n      return \"operator\";\n    }\n    // IDENT\n    else if(isNonDigit.test(ch)) {\n      state.tokenize = tokenIdent;\n    }\n    // Q-IDENT\n    else if(ch == \"'\" && stream.peek() && stream.peek() != \"'\") {\n      state.tokenize = tokenQIdent;\n    }\n    // STRING\n    else if(ch == '\"') {\n      state.tokenize = tokenString;\n    }\n    // UNSIGNED_NUMBER\n    else if(isDigit.test(ch)) {\n      state.tokenize = tokenUnsignedNumber;\n    }\n    // ERROR\n    else {\n      state.tokenize = null;\n      return \"error\";\n    }\n\n    return state.tokenize(stream, state);\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != null) return null;\n\n    var level = state.level;\n    if(/(algorithm)/.test(textAfter)) level--;\n    if(/(equation)/.test(textAfter)) level--;\n    if(/(initial algorithm)/.test(textAfter)) level--;\n    if(/(initial equation)/.test(textAfter)) level--;\n    if(/(end)/.test(textAfter)) level--;\n\n    if(level > 0)\n      return cx.unit*level;\n    else\n      return 0;\n  },\n\n  languageData: {\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    autocomplete: completions\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAE,GAAG,IAAE,MAAM,MAAM,EAAE,EAAE,EAC9B,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IAClB,OAAO;AACT;AAEA,IAAI,WAAW,MAAM;AACrB,IAAI,UAAU,MAAM;AACpB,IAAI,QAAQ,MAAM;AAElB,IAAI,cAAc,EAAE,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC;AAErF,IAAI,uBAAuB;AAC3B,IAAI,uBAAuB;AAC3B,IAAI,UAAU;AACd,IAAI,aAAa;AAEjB,SAAS,iBAAiB,MAAM,EAAE,KAAK;IACrC,OAAO,SAAS;IAChB,MAAM,QAAQ,GAAG;IACjB,OAAO;AACT;AAEA,SAAS,kBAAkB,MAAM,EAAE,KAAK;IACtC,IAAI,WAAW,OAAO;IACtB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,YAAY,MAAM,KAAK;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,IAAI,UAAU,OAAO;IACrB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;QACnC,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,MAAM,QAAQ,GAAG;YACjB,MAAM,GAAG,GAAG;YACZ;QACF;QACA,UAAU,CAAC,WAAW,MAAM;IAC9B;IAEA,OAAO;AACT;AAEA,SAAS,WAAW,MAAM,EAAE,KAAK;IAC/B,OAAO,QAAQ,CAAC;IAChB,MAAO,OAAO,GAAG,CAAC,YAAY,OAAO,GAAG,CAAC,YAAa,CAAE;IAGxD,IAAI,MAAM,OAAO,OAAO;IAExB,IAAG,MAAM,GAAG,IAAI,CAAC,OAAO,aAAa,OAAO,WAAW,OAAO,UAAU,OAAO,WAAW,GAAG,MAAM,KAAK;SACnG,IAAG,MAAM,GAAG,IAAI,OAAO,SAAS,MAAM,KAAK,GAAG,GAAG,MAAM,KAAK;IAEjE,MAAM,QAAQ,GAAG;IACjB,MAAM,GAAG,GAAG;IAEZ,IAAI,SAAS,oBAAoB,CAAC,MAAM,OAAO;SAC1C,IAAI,QAAQ,oBAAoB,CAAC,MAAM,OAAO;SAC9C,IAAI,MAAM,oBAAoB,CAAC,MAAM,OAAO;SAC5C,OAAO;AACd;AAEA,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,MAAO,OAAO,GAAG,CAAC,QAAS,CAAE;IAE7B,MAAM,QAAQ,GAAG;IACjB,MAAM,GAAG,GAAG;IAEZ,IAAG,OAAO,GAAG,CAAC,MACZ,OAAO;SAEP,OAAO;AACX;AAEA,SAAS,oBAAoB,MAAM,EAAE,KAAK;IACxC,OAAO,QAAQ,CAAC;IAChB,IAAI,OAAO,GAAG,CAAC,MAAM;QACnB,OAAO,QAAQ,CAAC;IAClB;IACA,IAAI,OAAO,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM;QACtC,IAAI,CAAC,OAAO,GAAG,CAAC,MACd,OAAO,GAAG,CAAC;QACb,OAAO,QAAQ,CAAC;IAClB;IAEA,MAAM,QAAQ,GAAG;IACjB,MAAM,GAAG,GAAG;IACZ,OAAO;AACT;AAGO,MAAM,WAAW;IACtB,MAAM;IACN,YAAY;QACV,OAAO;YACL,UAAU;YACV,OAAO;YACP,KAAK;QACP;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAG,MAAM,QAAQ,IAAI,MAAM;YACzB,OAAO,MAAM,QAAQ,CAAC,QAAQ;QAChC;QAEA,IAAG,OAAO,GAAG,IAAI;YACf,MAAM,GAAG,GAAG;QACd;QAEA,aAAa;QACb,IAAG,OAAO,QAAQ,IAAI;YACpB,MAAM,QAAQ,GAAG;YACjB,OAAO;QACT;QAEA,IAAI,KAAK,OAAO,IAAI;QAEpB,cAAc;QACd,IAAG,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;YAC/B,MAAM,QAAQ,GAAG;QACnB,OAEK,IAAG,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;YACpC,MAAM,QAAQ,GAAG;QACnB,OAEK,IAAG,qBAAqB,IAAI,CAAC,KAAG,OAAO,IAAI,KAAK;YACnD,OAAO,IAAI;YACX,MAAM,QAAQ,GAAG;YACjB,OAAO;QACT,OAEK,IAAG,qBAAqB,IAAI,CAAC,KAAK;YACrC,MAAM,QAAQ,GAAG;YACjB,OAAO;QACT,OAEK,IAAG,WAAW,IAAI,CAAC,KAAK;YAC3B,MAAM,QAAQ,GAAG;QACnB,OAEK,IAAG,MAAM,OAAO,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,KAAK;YAC1D,MAAM,QAAQ,GAAG;QACnB,OAEK,IAAG,MAAM,KAAK;YACjB,MAAM,QAAQ,GAAG;QACnB,OAEK,IAAG,QAAQ,IAAI,CAAC,KAAK;YACxB,MAAM,QAAQ,GAAG;QACnB,OAEK;YACH,MAAM,QAAQ,GAAG;YACjB,OAAO;QACT;QAEA,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,MAAM,QAAQ,IAAI,MAAM,OAAO;QAEnC,IAAI,QAAQ,MAAM,KAAK;QACvB,IAAG,cAAc,IAAI,CAAC,YAAY;QAClC,IAAG,aAAa,IAAI,CAAC,YAAY;QACjC,IAAG,sBAAsB,IAAI,CAAC,YAAY;QAC1C,IAAG,qBAAqB,IAAI,CAAC,YAAY;QACzC,IAAG,QAAQ,IAAI,CAAC,YAAY;QAE5B,IAAG,QAAQ,GACT,OAAO,GAAG,IAAI,GAAC;aAEf,OAAO;IACX;IAEA,cAAc;QACZ,eAAe;YAAC,MAAM;YAAM,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;QAC5D,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}]}