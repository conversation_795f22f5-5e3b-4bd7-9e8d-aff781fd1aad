{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/verilog.js"], "sourcesContent": ["function mkVerilog(parserConfig) {\n\n  var statementIndentUnit = parserConfig.statementIndentUnit,\n      dontAlignCalls = parserConfig.dontAlignCalls,\n      noIndentKeywords = parserConfig.noIndentKeywords || [],\n      multiLineStrings = parserConfig.multiLineStrings,\n      hooks = parserConfig.hooks || {};\n\n  function words(str) {\n    var obj = {}, words = str.split(\" \");\n    for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n    return obj;\n  }\n\n  /**\n   * Keywords from IEEE 1800-2012\n   */\n  var keywords = words(\n    \"accept_on alias always always_comb always_ff always_latch and assert assign assume automatic before begin bind \" +\n      \"bins binsof bit break buf bufif0 bufif1 byte case casex casez cell chandle checker class clocking cmos config \" +\n      \"const constraint context continue cover covergroup coverpoint cross deassign default defparam design disable \" +\n      \"dist do edge else end endcase endchecker endclass endclocking endconfig endfunction endgenerate endgroup \" +\n      \"endinterface endmodule endpackage endprimitive endprogram endproperty endspecify endsequence endtable endtask \" +\n      \"enum event eventually expect export extends extern final first_match for force foreach forever fork forkjoin \" +\n      \"function generate genvar global highz0 highz1 if iff ifnone ignore_bins illegal_bins implements implies import \" +\n      \"incdir include initial inout input inside instance int integer interconnect interface intersect join join_any \" +\n      \"join_none large let liblist library local localparam logic longint macromodule matches medium modport module \" +\n      \"nand negedge nettype new nexttime nmos nor noshowcancelled not notif0 notif1 null or output package packed \" +\n      \"parameter pmos posedge primitive priority program property protected pull0 pull1 pulldown pullup \" +\n      \"pulsestyle_ondetect pulsestyle_onevent pure rand randc randcase randsequence rcmos real realtime ref reg \" +\n      \"reject_on release repeat restrict return rnmos rpmos rtran rtranif0 rtranif1 s_always s_eventually s_nexttime \" +\n      \"s_until s_until_with scalared sequence shortint shortreal showcancelled signed small soft solve specify \" +\n      \"specparam static string strong strong0 strong1 struct super supply0 supply1 sync_accept_on sync_reject_on \" +\n      \"table tagged task this throughout time timeprecision timeunit tran tranif0 tranif1 tri tri0 tri1 triand trior \" +\n      \"trireg type typedef union unique unique0 unsigned until until_with untyped use uwire var vectored virtual void \" +\n      \"wait wait_order wand weak weak0 weak1 while wildcard wire with within wor xnor xor\");\n\n  /** Operators from IEEE 1800-2012\n      unary_operator ::=\n      + | - | ! | ~ | & | ~& | | | ~| | ^ | ~^ | ^~\n      binary_operator ::=\n      + | - | * | / | % | == | != | === | !== | ==? | !=? | && | || | **\n      | < | <= | > | >= | & | | | ^ | ^~ | ~^ | >> | << | >>> | <<<\n      | -> | <->\n      inc_or_dec_operator ::= ++ | --\n      unary_module_path_operator ::=\n      ! | ~ | & | ~& | | | ~| | ^ | ~^ | ^~\n      binary_module_path_operator ::=\n      == | != | && | || | & | | | ^ | ^~ | ~^\n  */\n  var isOperatorChar = /[\\+\\-\\*\\/!~&|^%=?:]/;\n  var isBracketChar = /[\\[\\]{}()]/;\n\n  var unsignedNumber = /\\d[0-9_]*/;\n  var decimalLiteral = /\\d*\\s*'s?d\\s*\\d[0-9_]*/i;\n  var binaryLiteral = /\\d*\\s*'s?b\\s*[xz01][xz01_]*/i;\n  var octLiteral = /\\d*\\s*'s?o\\s*[xz0-7][xz0-7_]*/i;\n  var hexLiteral = /\\d*\\s*'s?h\\s*[0-9a-fxz?][0-9a-fxz?_]*/i;\n  var realLiteral = /(\\d[\\d_]*(\\.\\d[\\d_]*)?E-?[\\d_]+)|(\\d[\\d_]*\\.\\d[\\d_]*)/i;\n\n  var closingBracketOrWord = /^((\\w+)|[)}\\]])/;\n  var closingBracket = /[)}\\]]/;\n\n  var curPunc;\n  var curKeyword;\n\n  // Block openings which are closed by a matching keyword in the form of (\"end\" + keyword)\n  // E.g. \"task\" => \"endtask\"\n  var blockKeywords = words(\n    \"case checker class clocking config function generate interface module package \" +\n      \"primitive program property specify sequence table task\"\n  );\n\n  // Opening/closing pairs\n  var openClose = {};\n  for (var keyword in blockKeywords) {\n    openClose[keyword] = \"end\" + keyword;\n  }\n  openClose[\"begin\"] = \"end\";\n  openClose[\"casex\"] = \"endcase\";\n  openClose[\"casez\"] = \"endcase\";\n  openClose[\"do\"   ] = \"while\";\n  openClose[\"fork\" ] = \"join;join_any;join_none\";\n  openClose[\"covergroup\"] = \"endgroup\";\n\n  for (var i in noIndentKeywords) {\n    var keyword = noIndentKeywords[i];\n    if (openClose[keyword]) {\n      openClose[keyword] = undefined;\n    }\n  }\n\n  // Keywords which open statements that are ended with a semi-colon\n  var statementKeywords = words(\"always always_comb always_ff always_latch assert assign assume else export for foreach forever if import initial repeat while\");\n\n  function tokenBase(stream, state) {\n    var ch = stream.peek(), style;\n    if (hooks[ch] && (style = hooks[ch](stream, state)) != false) return style;\n    if (hooks.tokenBase && (style = hooks.tokenBase(stream, state)) != false)\n      return style;\n\n    if (/[,;:\\.]/.test(ch)) {\n      curPunc = stream.next();\n      return null;\n    }\n    if (isBracketChar.test(ch)) {\n      curPunc = stream.next();\n      return \"bracket\";\n    }\n    // Macros (tick-defines)\n    if (ch == '`') {\n      stream.next();\n      if (stream.eatWhile(/[\\w\\$_]/)) {\n        return \"def\";\n      } else {\n        return null;\n      }\n    }\n    // System calls\n    if (ch == '$') {\n      stream.next();\n      if (stream.eatWhile(/[\\w\\$_]/)) {\n        return \"meta\";\n      } else {\n        return null;\n      }\n    }\n    // Time literals\n    if (ch == '#') {\n      stream.next();\n      stream.eatWhile(/[\\d_.]/);\n      return \"def\";\n    }\n    // Strings\n    if (ch == '\"') {\n      stream.next();\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    }\n    // Comments\n    if (ch == \"/\") {\n      stream.next();\n      if (stream.eat(\"*\")) {\n        state.tokenize = tokenComment;\n        return tokenComment(stream, state);\n      }\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return \"comment\";\n      }\n      stream.backUp(1);\n    }\n\n    // Numeric literals\n    if (stream.match(realLiteral) ||\n        stream.match(decimalLiteral) ||\n        stream.match(binaryLiteral) ||\n        stream.match(octLiteral) ||\n        stream.match(hexLiteral) ||\n        stream.match(unsignedNumber) ||\n        stream.match(realLiteral)) {\n      return \"number\";\n    }\n\n    // Operators\n    if (stream.eatWhile(isOperatorChar)) {\n      return \"meta\";\n    }\n\n    // Keywords / plain variables\n    if (stream.eatWhile(/[\\w\\$_]/)) {\n      var cur = stream.current();\n      if (keywords[cur]) {\n        if (openClose[cur]) {\n          curPunc = \"newblock\";\n        }\n        if (statementKeywords[cur]) {\n          curPunc = \"newstatement\";\n        }\n        curKeyword = cur;\n        return \"keyword\";\n      }\n      return \"variable\";\n    }\n\n    stream.next();\n    return null;\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next, end = false;\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped) {end = true; break;}\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (end || !(escaped || multiLineStrings))\n        state.tokenize = tokenBase;\n      return \"string\";\n    };\n  }\n\n  function tokenComment(stream, state) {\n    var maybeEnd = false, ch;\n    while (ch = stream.next()) {\n      if (ch == \"/\" && maybeEnd) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      maybeEnd = (ch == \"*\");\n    }\n    return \"comment\";\n  }\n\n  function Context(indented, column, type, align, prev) {\n    this.indented = indented;\n    this.column = column;\n    this.type = type;\n    this.align = align;\n    this.prev = prev;\n  }\n  function pushContext(state, col, type) {\n    var indent = state.indented;\n    var c = new Context(indent, col, type, null, state.context);\n    return state.context = c;\n  }\n  function popContext(state) {\n    var t = state.context.type;\n    if (t == \")\" || t == \"]\" || t == \"}\") {\n      state.indented = state.context.indented;\n    }\n    return state.context = state.context.prev;\n  }\n\n  function isClosing(text, contextClosing) {\n    if (text == contextClosing) {\n      return true;\n    } else {\n      // contextClosing may be multiple keywords separated by ;\n      var closingKeywords = contextClosing.split(\";\");\n      for (var i in closingKeywords) {\n        if (text == closingKeywords[i]) {\n          return true;\n        }\n      }\n      return false;\n    }\n  }\n\n  function buildElectricInputRegEx() {\n    // Reindentation should occur on any bracket char: {}()[]\n    // or on a match of any of the block closing keywords, at\n    // the end of a line\n    var allClosings = [];\n    for (var i in openClose) {\n      if (openClose[i]) {\n        var closings = openClose[i].split(\";\");\n        for (var j in closings) {\n          allClosings.push(closings[j]);\n        }\n      }\n    }\n    var re = new RegExp(\"[{}()\\\\[\\\\]]|(\" + allClosings.join(\"|\") + \")$\");\n    return re;\n  }\n\n  // Interface\n  return {\n    name: \"verilog\",\n\n    startState: function(indentUnit) {\n      var state = {\n        tokenize: null,\n        context: new Context(-indentUnit, 0, \"top\", false),\n        indented: 0,\n        startOfLine: true\n      };\n      if (hooks.startState) hooks.startState(state);\n      return state;\n    },\n\n    token: function(stream, state) {\n      var ctx = state.context;\n      if (stream.sol()) {\n        if (ctx.align == null) ctx.align = false;\n        state.indented = stream.indentation();\n        state.startOfLine = true;\n      }\n      if (hooks.token) {\n        // Call hook, with an optional return value of a style to override verilog styling.\n        var style = hooks.token(stream, state);\n        if (style !== undefined) {\n          return style;\n        }\n      }\n      if (stream.eatSpace()) return null;\n      curPunc = null;\n      curKeyword = null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style == \"comment\" || style == \"meta\" || style == \"variable\") return style;\n      if (ctx.align == null) ctx.align = true;\n\n      if (curPunc == ctx.type) {\n        popContext(state);\n      } else if ((curPunc == \";\" && ctx.type == \"statement\") ||\n                 (ctx.type && isClosing(curKeyword, ctx.type))) {\n        ctx = popContext(state);\n        while (ctx && ctx.type == \"statement\") ctx = popContext(state);\n      } else if (curPunc == \"{\") {\n        pushContext(state, stream.column(), \"}\");\n      } else if (curPunc == \"[\") {\n        pushContext(state, stream.column(), \"]\");\n      } else if (curPunc == \"(\") {\n        pushContext(state, stream.column(), \")\");\n      } else if (ctx && ctx.type == \"endcase\" && curPunc == \":\") {\n        pushContext(state, stream.column(), \"statement\");\n      } else if (curPunc == \"newstatement\") {\n        pushContext(state, stream.column(), \"statement\");\n      } else if (curPunc == \"newblock\") {\n        if (curKeyword == \"function\" && ctx && (ctx.type == \"statement\" || ctx.type == \"endgroup\")) {\n          // The 'function' keyword can appear in some other contexts where it actually does not\n          // indicate a function (import/export DPI and covergroup definitions).\n          // Do nothing in this case\n        } else if (curKeyword == \"task\" && ctx && ctx.type == \"statement\") {\n          // Same thing for task\n        } else {\n          var close = openClose[curKeyword];\n          pushContext(state, stream.column(), close);\n        }\n      }\n\n      state.startOfLine = false;\n      return style;\n    },\n\n    indent: function(state, textAfter, cx) {\n      if (state.tokenize != tokenBase && state.tokenize != null) return null;\n      if (hooks.indent) {\n        var fromHook = hooks.indent(state);\n        if (fromHook >= 0) return fromHook;\n      }\n      var ctx = state.context, firstChar = textAfter && textAfter.charAt(0);\n      if (ctx.type == \"statement\" && firstChar == \"}\") ctx = ctx.prev;\n      var closing = false;\n      var possibleClosing = textAfter.match(closingBracketOrWord);\n      if (possibleClosing)\n        closing = isClosing(possibleClosing[0], ctx.type);\n      if (ctx.type == \"statement\") return ctx.indented + (firstChar == \"{\" ? 0 : statementIndentUnit || cx.unit);\n      else if (closingBracket.test(ctx.type) && ctx.align && !dontAlignCalls) return ctx.column + (closing ? 0 : 1);\n      else if (ctx.type == \")\" && !closing) return ctx.indented + (statementIndentUnit || cx.unit);\n      else return ctx.indented + (closing ? 0 : cx.unit);\n    },\n\n    languageData: {\n      indentOnInput: buildElectricInputRegEx(),\n      commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}}\n    }\n  };\n};\n\nexport const verilog = mkVerilog({})\n\n// TL-Verilog mode.\n// See tl-x.org for language spec.\n// See the mode in action at makerchip.com.\n// Contact: <EMAIL>\n\n// TLV Identifier prefixes.\n// Note that sign is not treated separately, so \"+/-\" versions of numeric identifiers\n// are included.\nvar tlvIdentifierStyle = {\n  \"|\": \"link\",\n  \">\": \"property\",  // Should condition this off for > TLV 1c.\n  \"$\": \"variable\",\n  \"$$\": \"variable\",\n  \"?$\": \"qualifier\",\n  \"?*\": \"qualifier\",\n  \"-\": \"contentSeparator\",\n  \"/\": \"property\",\n  \"/-\": \"property\",\n  \"@\": \"variableName.special\",\n  \"@-\": \"variableName.special\",\n  \"@++\": \"variableName.special\",\n  \"@+=\": \"variableName.special\",\n  \"@+=-\": \"variableName.special\",\n  \"@--\": \"variableName.special\",\n  \"@-=\": \"variableName.special\",\n  \"%+\": \"tag\",\n  \"%-\": \"tag\",\n  \"%\": \"tag\",\n  \">>\": \"tag\",\n  \"<<\": \"tag\",\n  \"<>\": \"tag\",\n  \"#\": \"tag\",  // Need to choose a style for this.\n  \"^\": \"attribute\",\n  \"^^\": \"attribute\",\n  \"^!\": \"attribute\",\n  \"*\": \"variable\",\n  \"**\": \"variable\",\n  \"\\\\\": \"keyword\",\n  \"\\\"\": \"comment\"\n};\n\n// Lines starting with these characters define scope (result in indentation).\nvar tlvScopePrefixChars = {\n  \"/\": \"beh-hier\",\n  \">\": \"beh-hier\",\n  \"-\": \"phys-hier\",\n  \"|\": \"pipe\",\n  \"?\": \"when\",\n  \"@\": \"stage\",\n  \"\\\\\": \"keyword\"\n};\nvar tlvIndentUnit = 3;\nvar tlvTrackStatements = false;\nvar tlvIdentMatch = /^([~!@#\\$%\\^&\\*-\\+=\\?\\/\\\\\\|'\"<>]+)([\\d\\w_]*)/;  // Matches an identifier.\n// Note that ':' is excluded, because of it's use in [:].\nvar tlvLineIndentationMatch = /^[! ] */;\nvar tlvCommentMatch = /^\\/[\\/\\*]/;\n\nexport const tlv = mkVerilog({\n  hooks: {\n    electricInput: false,\n\n    // Return undefined for verilog tokenizing, or style for TLV token (null not used).\n    // Standard CM styles are used for most formatting, but some TL-Verilog-specific highlighting\n    // can be enabled with the definition of cm-tlv-* styles, including highlighting for:\n    //   - M4 tokens\n    //   - TLV scope indentation\n    //   - Statement delimitation (enabled by tlvTrackStatements)\n    token: function(stream, state) {\n      var style = undefined;\n      var match;  // Return value of pattern matches.\n\n      // Set highlighting mode based on code region (TLV or SV).\n      if (stream.sol() && ! state.tlvInBlockComment) {\n        // Process region.\n        if (stream.peek() == '\\\\') {\n          style = \"def\";\n          stream.skipToEnd();\n          if (stream.string.match(/\\\\SV/)) {\n            state.tlvCodeActive = false;\n          } else if (stream.string.match(/\\\\TLV/)){\n            state.tlvCodeActive = true;\n          }\n        }\n        // Correct indentation in the face of a line prefix char.\n        if (state.tlvCodeActive && stream.pos == 0 &&\n            (state.indented == 0) && (match = stream.match(tlvLineIndentationMatch, false))) {\n          state.indented = match[0].length;\n        }\n\n        // Compute indentation state:\n        //   o Auto indentation on next line\n        //   o Indentation scope styles\n        var indented = state.indented;\n        var depth = indented / tlvIndentUnit;\n        if (depth <= state.tlvIndentationStyle.length) {\n          // not deeper than current scope\n\n          var blankline = stream.string.length == indented;\n          var chPos = depth * tlvIndentUnit;\n          if (chPos < stream.string.length) {\n            var bodyString = stream.string.slice(chPos);\n            var ch = bodyString[0];\n            if (tlvScopePrefixChars[ch] && ((match = bodyString.match(tlvIdentMatch)) &&\n                                            tlvIdentifierStyle[match[1]])) {\n              // This line begins scope.\n              // Next line gets indented one level.\n              indented += tlvIndentUnit;\n              // Style the next level of indentation (except non-region keyword identifiers,\n              //   which are statements themselves)\n              if (!(ch == \"\\\\\" && chPos > 0)) {\n                state.tlvIndentationStyle[depth] = tlvScopePrefixChars[ch];\n                if (tlvTrackStatements) {state.statementComment = false;}\n                depth++;\n              }\n            }\n          }\n          // Clear out deeper indentation levels unless line is blank.\n          if (!blankline) {\n            while (state.tlvIndentationStyle.length > depth) {\n              state.tlvIndentationStyle.pop();\n            }\n          }\n        }\n        // Set next level of indentation.\n        state.tlvNextIndent = indented;\n      }\n\n      if (state.tlvCodeActive) {\n        // Highlight as TLV.\n\n        var beginStatement = false;\n        if (tlvTrackStatements) {\n          // This starts a statement if the position is at the scope level\n          // and we're not within a statement leading comment.\n          beginStatement =\n            (stream.peek() != \" \") &&   // not a space\n            (style === undefined) &&    // not a region identifier\n            !state.tlvInBlockComment && // not in block comment\n            //!stream.match(tlvCommentMatch, false) && // not comment start\n          (stream.column() == state.tlvIndentationStyle.length * tlvIndentUnit);  // at scope level\n          if (beginStatement) {\n            if (state.statementComment) {\n              // statement already started by comment\n              beginStatement = false;\n            }\n            state.statementComment =\n              stream.match(tlvCommentMatch, false); // comment start\n          }\n        }\n\n        var match;\n        if (style !== undefined) {\n        } else if (state.tlvInBlockComment) {\n          // In a block comment.\n          if (stream.match(/^.*?\\*\\//)) {\n            // Exit block comment.\n            state.tlvInBlockComment = false;\n            if (tlvTrackStatements && !stream.eol()) {\n              // Anything after comment is assumed to be real statement content.\n              state.statementComment = false;\n            }\n          } else {\n            stream.skipToEnd();\n          }\n          style = \"comment\";\n        } else if ((match = stream.match(tlvCommentMatch)) && !state.tlvInBlockComment) {\n          // Start comment.\n          if (match[0] == \"//\") {\n            // Line comment.\n            stream.skipToEnd();\n          } else {\n            // Block comment.\n            state.tlvInBlockComment = true;\n          }\n          style = \"comment\";\n        } else if (match = stream.match(tlvIdentMatch)) {\n          // looks like an identifier (or identifier prefix)\n          var prefix = match[1];\n          var mnemonic = match[2];\n          if (// is identifier prefix\n            tlvIdentifierStyle.hasOwnProperty(prefix) &&\n              // has mnemonic or we're at the end of the line (maybe it hasn't been typed yet)\n            (mnemonic.length > 0 || stream.eol())) {\n            style = tlvIdentifierStyle[prefix];\n          } else {\n            // Just swallow one character and try again.\n            // This enables subsequent identifier match with preceding symbol character, which\n            //   is legal within a statement.  (Eg, !$reset).  It also enables detection of\n            //   comment start with preceding symbols.\n            stream.backUp(stream.current().length - 1);\n          }\n        } else if (stream.match(/^\\t+/)) {\n          // Highlight tabs, which are illegal.\n          style = \"invalid\";\n        } else if (stream.match(/^[\\[\\]{}\\(\\);\\:]+/)) {\n          // [:], (), {}, ;.\n          style = \"meta\";\n        } else if (match = stream.match(/^[mM]4([\\+_])?[\\w\\d_]*/)) {\n          // m4 pre proc\n          style = (match[1] == \"+\") ? \"keyword.special\" : \"keyword\";\n        } else if (stream.match(/^ +/)){\n          // Skip over spaces.\n          if (stream.eol()) {\n            // Trailing spaces.\n            style = \"error\";\n          }\n        } else if (stream.match(/^[\\w\\d_]+/)) {\n          // alpha-numeric token.\n          style = \"number\";\n        } else {\n          // Eat the next char w/ no formatting.\n          stream.next();\n        }\n      } else {\n        if (stream.match(/^[mM]4([\\w\\d_]*)/)) {\n          // m4 pre proc\n          style = \"keyword\";\n        }\n      }\n      return style;\n    },\n\n    indent: function(state) {\n      return (state.tlvCodeActive == true) ? state.tlvNextIndent : -1;\n    },\n\n    startState: function(state) {\n      state.tlvIndentationStyle = [];  // Styles to use for each level of indentation.\n      state.tlvCodeActive = true;  // True when we're in a TLV region (and at beginning of file).\n      state.tlvNextIndent = -1;    // The number of spaces to autoindent the next line if tlvCodeActive.\n      state.tlvInBlockComment = false;  // True inside /**/ comment.\n      if (tlvTrackStatements) {\n        state.statementComment = false;  // True inside a statement's header comment.\n      }\n    }\n\n  }\n});\n"], "names": [], "mappings": ";;;;AAAA,SAAS,UAAU,YAAY;IAE7B,IAAI,sBAAsB,aAAa,mBAAmB,EACtD,iBAAiB,aAAa,cAAc,EAC5C,mBAAmB,aAAa,gBAAgB,IAAI,EAAE,EACtD,mBAAmB,aAAa,gBAAgB,EAChD,QAAQ,aAAa,KAAK,IAAI,CAAC;IAEnC,SAAS,MAAM,GAAG;QAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;QAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;QACvD,OAAO;IACT;IAEA;;GAEC,GACD,IAAI,WAAW,MACb,oHACE,mHACA,kHACA,8GACA,mHACA,kHACA,oHACA,mHACA,kHACA,gHACA,sGACA,8GACA,mHACA,6GACA,+GACA,mHACA,oHACA;IAEJ;;;;;;;;;;;;EAYA,GACA,IAAI,iBAAiB;IACrB,IAAI,gBAAgB;IAEpB,IAAI,iBAAiB;IACrB,IAAI,iBAAiB;IACrB,IAAI,gBAAgB;IACpB,IAAI,aAAa;IACjB,IAAI,aAAa;IACjB,IAAI,cAAc;IAElB,IAAI,uBAAuB;IAC3B,IAAI,iBAAiB;IAErB,IAAI;IACJ,IAAI;IAEJ,yFAAyF;IACzF,2BAA2B;IAC3B,IAAI,gBAAgB,MAClB,mFACE;IAGJ,wBAAwB;IACxB,IAAI,YAAY,CAAC;IACjB,IAAK,IAAI,WAAW,cAAe;QACjC,SAAS,CAAC,QAAQ,GAAG,QAAQ;IAC/B;IACA,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,QAAQ,GAAG;IACrB,SAAS,CAAC,KAAQ,GAAG;IACrB,SAAS,CAAC,OAAQ,GAAG;IACrB,SAAS,CAAC,aAAa,GAAG;IAE1B,IAAK,IAAI,KAAK,iBAAkB;QAC9B,IAAI,UAAU,gBAAgB,CAAC,EAAE;QACjC,IAAI,SAAS,CAAC,QAAQ,EAAE;YACtB,SAAS,CAAC,QAAQ,GAAG;QACvB;IACF;IAEA,kEAAkE;IAClE,IAAI,oBAAoB,MAAM;IAE9B,SAAS,UAAU,MAAM,EAAE,KAAK;QAC9B,IAAI,KAAK,OAAO,IAAI,IAAI;QACxB,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,KAAK,CAAC,GAAG,CAAC,QAAQ,MAAM,KAAK,OAAO,OAAO;QACrE,IAAI,MAAM,SAAS,IAAI,CAAC,QAAQ,MAAM,SAAS,CAAC,QAAQ,MAAM,KAAK,OACjE,OAAO;QAET,IAAI,UAAU,IAAI,CAAC,KAAK;YACtB,UAAU,OAAO,IAAI;YACrB,OAAO;QACT;QACA,IAAI,cAAc,IAAI,CAAC,KAAK;YAC1B,UAAU,OAAO,IAAI;YACrB,OAAO;QACT;QACA,wBAAwB;QACxB,IAAI,MAAM,KAAK;YACb,OAAO,IAAI;YACX,IAAI,OAAO,QAAQ,CAAC,YAAY;gBAC9B,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;QACA,eAAe;QACf,IAAI,MAAM,KAAK;YACb,OAAO,IAAI;YACX,IAAI,OAAO,QAAQ,CAAC,YAAY;gBAC9B,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;QACA,gBAAgB;QAChB,IAAI,MAAM,KAAK;YACb,OAAO,IAAI;YACX,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,UAAU;QACV,IAAI,MAAM,KAAK;YACb,OAAO,IAAI;YACX,MAAM,QAAQ,GAAG,YAAY;YAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;QAChC;QACA,WAAW;QACX,IAAI,MAAM,KAAK;YACb,OAAO,IAAI;YACX,IAAI,OAAO,GAAG,CAAC,MAAM;gBACnB,MAAM,QAAQ,GAAG;gBACjB,OAAO,aAAa,QAAQ;YAC9B;YACA,IAAI,OAAO,GAAG,CAAC,MAAM;gBACnB,OAAO,SAAS;gBAChB,OAAO;YACT;YACA,OAAO,MAAM,CAAC;QAChB;QAEA,mBAAmB;QACnB,IAAI,OAAO,KAAK,CAAC,gBACb,OAAO,KAAK,CAAC,mBACb,OAAO,KAAK,CAAC,kBACb,OAAO,KAAK,CAAC,eACb,OAAO,KAAK,CAAC,eACb,OAAO,KAAK,CAAC,mBACb,OAAO,KAAK,CAAC,cAAc;YAC7B,OAAO;QACT;QAEA,YAAY;QACZ,IAAI,OAAO,QAAQ,CAAC,iBAAiB;YACnC,OAAO;QACT;QAEA,6BAA6B;QAC7B,IAAI,OAAO,QAAQ,CAAC,YAAY;YAC9B,IAAI,MAAM,OAAO,OAAO;YACxB,IAAI,QAAQ,CAAC,IAAI,EAAE;gBACjB,IAAI,SAAS,CAAC,IAAI,EAAE;oBAClB,UAAU;gBACZ;gBACA,IAAI,iBAAiB,CAAC,IAAI,EAAE;oBAC1B,UAAU;gBACZ;gBACA,aAAa;gBACb,OAAO;YACT;YACA,OAAO;QACT;QAEA,OAAO,IAAI;QACX,OAAO;IACT;IAEA,SAAS,YAAY,KAAK;QACxB,OAAO,SAAS,MAAM,EAAE,KAAK;YAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;YACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;gBACrC,IAAI,QAAQ,SAAS,CAAC,SAAS;oBAAC,MAAM;oBAAM;gBAAM;gBAClD,UAAU,CAAC,WAAW,QAAQ;YAChC;YACA,IAAI,OAAO,CAAC,CAAC,WAAW,gBAAgB,GACtC,MAAM,QAAQ,GAAG;YACnB,OAAO;QACT;IACF;IAEA,SAAS,aAAa,MAAM,EAAE,KAAK;QACjC,IAAI,WAAW,OAAO;QACtB,MAAO,KAAK,OAAO,IAAI,GAAI;YACzB,IAAI,MAAM,OAAO,UAAU;gBACzB,MAAM,QAAQ,GAAG;gBACjB;YACF;YACA,WAAY,MAAM;QACpB;QACA,OAAO;IACT;IAEA,SAAS,QAAQ,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;QAClD,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;IACd;IACA,SAAS,YAAY,KAAK,EAAE,GAAG,EAAE,IAAI;QACnC,IAAI,SAAS,MAAM,QAAQ;QAC3B,IAAI,IAAI,IAAI,QAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,OAAO;QAC1D,OAAO,MAAM,OAAO,GAAG;IACzB;IACA,SAAS,WAAW,KAAK;QACvB,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI;QAC1B,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAAK;YACpC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ;QACzC;QACA,OAAO,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;IAC3C;IAEA,SAAS,UAAU,IAAI,EAAE,cAAc;QACrC,IAAI,QAAQ,gBAAgB;YAC1B,OAAO;QACT,OAAO;YACL,yDAAyD;YACzD,IAAI,kBAAkB,eAAe,KAAK,CAAC;YAC3C,IAAK,IAAI,KAAK,gBAAiB;gBAC7B,IAAI,QAAQ,eAAe,CAAC,EAAE,EAAE;oBAC9B,OAAO;gBACT;YACF;YACA,OAAO;QACT;IACF;IAEA,SAAS;QACP,yDAAyD;QACzD,yDAAyD;QACzD,oBAAoB;QACpB,IAAI,cAAc,EAAE;QACpB,IAAK,IAAI,KAAK,UAAW;YACvB,IAAI,SAAS,CAAC,EAAE,EAAE;gBAChB,IAAI,WAAW,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;gBAClC,IAAK,IAAI,KAAK,SAAU;oBACtB,YAAY,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC9B;YACF;QACF;QACA,IAAI,KAAK,IAAI,OAAO,mBAAmB,YAAY,IAAI,CAAC,OAAO;QAC/D,OAAO;IACT;IAEA,YAAY;IACZ,OAAO;QACL,MAAM;QAEN,YAAY,SAAS,UAAU;YAC7B,IAAI,QAAQ;gBACV,UAAU;gBACV,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,OAAO;gBAC5C,UAAU;gBACV,aAAa;YACf;YACA,IAAI,MAAM,UAAU,EAAE,MAAM,UAAU,CAAC;YACvC,OAAO;QACT;QAEA,OAAO,SAAS,MAAM,EAAE,KAAK;YAC3B,IAAI,MAAM,MAAM,OAAO;YACvB,IAAI,OAAO,GAAG,IAAI;gBAChB,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;gBACnC,MAAM,QAAQ,GAAG,OAAO,WAAW;gBACnC,MAAM,WAAW,GAAG;YACtB;YACA,IAAI,MAAM,KAAK,EAAE;gBACf,mFAAmF;gBACnF,IAAI,QAAQ,MAAM,KAAK,CAAC,QAAQ;gBAChC,IAAI,UAAU,WAAW;oBACvB,OAAO;gBACT;YACF;YACA,IAAI,OAAO,QAAQ,IAAI,OAAO;YAC9B,UAAU;YACV,aAAa;YACb,IAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,SAAS,EAAE,QAAQ;YAClD,IAAI,SAAS,aAAa,SAAS,UAAU,SAAS,YAAY,OAAO;YACzE,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;YAEnC,IAAI,WAAW,IAAI,IAAI,EAAE;gBACvB,WAAW;YACb,OAAO,IAAI,AAAC,WAAW,OAAO,IAAI,IAAI,IAAI,eAC9B,IAAI,IAAI,IAAI,UAAU,YAAY,IAAI,IAAI,GAAI;gBACxD,MAAM,WAAW;gBACjB,MAAO,OAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;YAC1D,OAAO,IAAI,WAAW,KAAK;gBACzB,YAAY,OAAO,OAAO,MAAM,IAAI;YACtC,OAAO,IAAI,WAAW,KAAK;gBACzB,YAAY,OAAO,OAAO,MAAM,IAAI;YACtC,OAAO,IAAI,WAAW,KAAK;gBACzB,YAAY,OAAO,OAAO,MAAM,IAAI;YACtC,OAAO,IAAI,OAAO,IAAI,IAAI,IAAI,aAAa,WAAW,KAAK;gBACzD,YAAY,OAAO,OAAO,MAAM,IAAI;YACtC,OAAO,IAAI,WAAW,gBAAgB;gBACpC,YAAY,OAAO,OAAO,MAAM,IAAI;YACtC,OAAO,IAAI,WAAW,YAAY;gBAChC,IAAI,cAAc,cAAc,OAAO,CAAC,IAAI,IAAI,IAAI,eAAe,IAAI,IAAI,IAAI,UAAU,GAAG;gBAC1F,sFAAsF;gBACtF,sEAAsE;gBACtE,0BAA0B;gBAC5B,OAAO,IAAI,cAAc,UAAU,OAAO,IAAI,IAAI,IAAI,aAAa;gBACjE,sBAAsB;gBACxB,OAAO;oBACL,IAAI,QAAQ,SAAS,CAAC,WAAW;oBACjC,YAAY,OAAO,OAAO,MAAM,IAAI;gBACtC;YACF;YAEA,MAAM,WAAW,GAAG;YACpB,OAAO;QACT;QAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;YACnC,IAAI,MAAM,QAAQ,IAAI,aAAa,MAAM,QAAQ,IAAI,MAAM,OAAO;YAClE,IAAI,MAAM,MAAM,EAAE;gBAChB,IAAI,WAAW,MAAM,MAAM,CAAC;gBAC5B,IAAI,YAAY,GAAG,OAAO;YAC5B;YACA,IAAI,MAAM,MAAM,OAAO,EAAE,YAAY,aAAa,UAAU,MAAM,CAAC;YACnE,IAAI,IAAI,IAAI,IAAI,eAAe,aAAa,KAAK,MAAM,IAAI,IAAI;YAC/D,IAAI,UAAU;YACd,IAAI,kBAAkB,UAAU,KAAK,CAAC;YACtC,IAAI,iBACF,UAAU,UAAU,eAAe,CAAC,EAAE,EAAE,IAAI,IAAI;YAClD,IAAI,IAAI,IAAI,IAAI,aAAa,OAAO,IAAI,QAAQ,GAAG,CAAC,aAAa,MAAM,IAAI,uBAAuB,GAAG,IAAI;iBACpG,IAAI,eAAe,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,gBAAgB,OAAO,IAAI,MAAM,GAAG,CAAC,UAAU,IAAI,CAAC;iBACvG,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,SAAS,OAAO,IAAI,QAAQ,GAAG,CAAC,uBAAuB,GAAG,IAAI;iBACtF,OAAO,IAAI,QAAQ,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI;QACnD;QAEA,cAAc;YACZ,eAAe;YACf,eAAe;gBAAC,MAAM;gBAAM,OAAO;oBAAC,MAAM;oBAAM,OAAO;gBAAI;YAAC;QAC9D;IACF;AACF;;AAEO,MAAM,UAAU,UAAU,CAAC;AAElC,mBAAmB;AACnB,kCAAkC;AAClC,2CAA2C;AAC3C,uCAAuC;AAEvC,2BAA2B;AAC3B,qFAAqF;AACrF,gBAAgB;AAChB,IAAI,qBAAqB;IACvB,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,KAAK;IACL,KAAK;IACL,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;AACR;AAEA,6EAA6E;AAC7E,IAAI,sBAAsB;IACxB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;AACR;AACA,IAAI,gBAAgB;AACpB,IAAI,qBAAqB;AACzB,IAAI,gBAAgB,gDAAiD,yBAAyB;AAC9F,yDAAyD;AACzD,IAAI,0BAA0B;AAC9B,IAAI,kBAAkB;AAEf,MAAM,MAAM,UAAU;IAC3B,OAAO;QACL,eAAe;QAEf,mFAAmF;QACnF,6FAA6F;QAC7F,qFAAqF;QACrF,gBAAgB;QAChB,4BAA4B;QAC5B,6DAA6D;QAC7D,OAAO,SAAS,MAAM,EAAE,KAAK;YAC3B,IAAI,QAAQ;YACZ,IAAI,OAAQ,mCAAmC;YAE/C,0DAA0D;YAC1D,IAAI,OAAO,GAAG,MAAM,CAAE,MAAM,iBAAiB,EAAE;gBAC7C,kBAAkB;gBAClB,IAAI,OAAO,IAAI,MAAM,MAAM;oBACzB,QAAQ;oBACR,OAAO,SAAS;oBAChB,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,SAAS;wBAC/B,MAAM,aAAa,GAAG;oBACxB,OAAO,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,UAAS;wBACtC,MAAM,aAAa,GAAG;oBACxB;gBACF;gBACA,yDAAyD;gBACzD,IAAI,MAAM,aAAa,IAAI,OAAO,GAAG,IAAI,KACpC,MAAM,QAAQ,IAAI,KAAM,CAAC,QAAQ,OAAO,KAAK,CAAC,yBAAyB,MAAM,GAAG;oBACnF,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;gBAClC;gBAEA,6BAA6B;gBAC7B,oCAAoC;gBACpC,+BAA+B;gBAC/B,IAAI,WAAW,MAAM,QAAQ;gBAC7B,IAAI,QAAQ,WAAW;gBACvB,IAAI,SAAS,MAAM,mBAAmB,CAAC,MAAM,EAAE;oBAC7C,gCAAgC;oBAEhC,IAAI,YAAY,OAAO,MAAM,CAAC,MAAM,IAAI;oBACxC,IAAI,QAAQ,QAAQ;oBACpB,IAAI,QAAQ,OAAO,MAAM,CAAC,MAAM,EAAE;wBAChC,IAAI,aAAa,OAAO,MAAM,CAAC,KAAK,CAAC;wBACrC,IAAI,KAAK,UAAU,CAAC,EAAE;wBACtB,IAAI,mBAAmB,CAAC,GAAG,IAAK,CAAC,QAAQ,WAAW,KAAK,CAAC,cAAc,KACxC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC,EAAG;4BAC7D,0BAA0B;4BAC1B,qCAAqC;4BACrC,YAAY;4BACZ,8EAA8E;4BAC9E,qCAAqC;4BACrC,IAAI,CAAC,CAAC,MAAM,QAAQ,QAAQ,CAAC,GAAG;gCAC9B,MAAM,mBAAmB,CAAC,MAAM,GAAG,mBAAmB,CAAC,GAAG;gCAC1D,uCAAwB;;gCAAgC;gCACxD;4BACF;wBACF;oBACF;oBACA,4DAA4D;oBAC5D,IAAI,CAAC,WAAW;wBACd,MAAO,MAAM,mBAAmB,CAAC,MAAM,GAAG,MAAO;4BAC/C,MAAM,mBAAmB,CAAC,GAAG;wBAC/B;oBACF;gBACF;gBACA,iCAAiC;gBACjC,MAAM,aAAa,GAAG;YACxB;YAEA,IAAI,MAAM,aAAa,EAAE;gBACvB,oBAAoB;gBAEpB,IAAI,iBAAiB;gBACrB,uCAAwB;;gBAiBxB;gBAEA,IAAI;gBACJ,IAAI,UAAU,WAAW,CACzB,OAAO,IAAI,MAAM,iBAAiB,EAAE;oBAClC,sBAAsB;oBACtB,IAAI,OAAO,KAAK,CAAC,aAAa;wBAC5B,sBAAsB;wBACtB,MAAM,iBAAiB,GAAG;wBAC1B,IAAI,sBAAsB,CAAC,OAAO,GAAG,IAAI;;wBAGzC;oBACF,OAAO;wBACL,OAAO,SAAS;oBAClB;oBACA,QAAQ;gBACV,OAAO,IAAI,CAAC,QAAQ,OAAO,KAAK,CAAC,gBAAgB,KAAK,CAAC,MAAM,iBAAiB,EAAE;oBAC9E,iBAAiB;oBACjB,IAAI,KAAK,CAAC,EAAE,IAAI,MAAM;wBACpB,gBAAgB;wBAChB,OAAO,SAAS;oBAClB,OAAO;wBACL,iBAAiB;wBACjB,MAAM,iBAAiB,GAAG;oBAC5B;oBACA,QAAQ;gBACV,OAAO,IAAI,QAAQ,OAAO,KAAK,CAAC,gBAAgB;oBAC9C,kDAAkD;oBAClD,IAAI,SAAS,KAAK,CAAC,EAAE;oBACrB,IAAI,WAAW,KAAK,CAAC,EAAE;oBACvB,IACE,mBAAmB,cAAc,CAAC,WAChC,gFAAgF;oBAClF,CAAC,SAAS,MAAM,GAAG,KAAK,OAAO,GAAG,EAAE,GAAG;wBACvC,QAAQ,kBAAkB,CAAC,OAAO;oBACpC,OAAO;wBACL,4CAA4C;wBAC5C,kFAAkF;wBAClF,+EAA+E;wBAC/E,0CAA0C;wBAC1C,OAAO,MAAM,CAAC,OAAO,OAAO,GAAG,MAAM,GAAG;oBAC1C;gBACF,OAAO,IAAI,OAAO,KAAK,CAAC,SAAS;oBAC/B,qCAAqC;oBACrC,QAAQ;gBACV,OAAO,IAAI,OAAO,KAAK,CAAC,sBAAsB;oBAC5C,kBAAkB;oBAClB,QAAQ;gBACV,OAAO,IAAI,QAAQ,OAAO,KAAK,CAAC,2BAA2B;oBACzD,cAAc;oBACd,QAAQ,AAAC,KAAK,CAAC,EAAE,IAAI,MAAO,oBAAoB;gBAClD,OAAO,IAAI,OAAO,KAAK,CAAC,QAAO;oBAC7B,oBAAoB;oBACpB,IAAI,OAAO,GAAG,IAAI;wBAChB,mBAAmB;wBACnB,QAAQ;oBACV;gBACF,OAAO,IAAI,OAAO,KAAK,CAAC,cAAc;oBACpC,uBAAuB;oBACvB,QAAQ;gBACV,OAAO;oBACL,sCAAsC;oBACtC,OAAO,IAAI;gBACb;YACF,OAAO;gBACL,IAAI,OAAO,KAAK,CAAC,qBAAqB;oBACpC,cAAc;oBACd,QAAQ;gBACV;YACF;YACA,OAAO;QACT;QAEA,QAAQ,SAAS,KAAK;YACpB,OAAO,AAAC,MAAM,aAAa,IAAI,OAAQ,MAAM,aAAa,GAAG,CAAC;QAChE;QAEA,YAAY,SAAS,KAAK;YACxB,MAAM,mBAAmB,GAAG,EAAE,EAAG,+CAA+C;YAChF,MAAM,aAAa,GAAG,MAAO,8DAA8D;YAC3F,MAAM,aAAa,GAAG,CAAC,GAAM,qEAAqE;YAClG,MAAM,iBAAiB,GAAG,OAAQ,4BAA4B;YAC9D,uCAAwB;;YAExB;QACF;IAEF;AACF", "ignoreList": [0], "debugId": null}}]}