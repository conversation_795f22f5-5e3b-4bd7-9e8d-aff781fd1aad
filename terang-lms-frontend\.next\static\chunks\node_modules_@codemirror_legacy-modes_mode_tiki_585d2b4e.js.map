{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/tiki.js"], "sourcesContent": ["function inBlock(style, terminator, returnTokenizer) {\n  return function(stream, state) {\n    while (!stream.eol()) {\n      if (stream.match(terminator)) {\n        state.tokenize = inText;\n        break;\n      }\n      stream.next();\n    }\n\n    if (returnTokenizer) state.tokenize = returnTokenizer;\n\n    return style;\n  };\n}\n\nfunction inLine(style) {\n  return function(stream, state) {\n    while(!stream.eol()) {\n      stream.next();\n    }\n    state.tokenize = inText;\n    return style;\n  };\n}\n\nfunction inText(stream, state) {\n  function chain(parser) {\n    state.tokenize = parser;\n    return parser(stream, state);\n  }\n\n  var sol = stream.sol();\n  var ch = stream.next();\n\n  //non start of line\n  switch (ch) { //switch is generally much faster than if, so it is used here\n  case \"{\": //plugin\n    stream.eat(\"/\");\n    stream.eatSpace();\n    stream.eatWhile(/[^\\s\\u00a0=\\\"\\'\\/?(}]/);\n    state.tokenize = inPlugin;\n    return \"tag\";\n  case \"_\": //bold\n    if (stream.eat(\"_\"))\n      return chain(inBlock(\"strong\", \"__\", inText));\n    break;\n  case \"'\": //italics\n    if (stream.eat(\"'\"))\n      return chain(inBlock(\"em\", \"''\", inText));\n    break;\n  case \"(\":// Wiki Link\n    if (stream.eat(\"(\"))\n      return chain(inBlock(\"link\", \"))\", inText));\n    break;\n  case \"[\":// Weblink\n    return chain(inBlock(\"url\", \"]\", inText));\n    break;\n  case \"|\": //table\n    if (stream.eat(\"|\"))\n      return chain(inBlock(\"comment\", \"||\"));\n    break;\n  case \"-\":\n    if (stream.eat(\"=\")) {//titleBar\n      return chain(inBlock(\"header string\", \"=-\", inText));\n    } else if (stream.eat(\"-\")) {//deleted\n      return chain(inBlock(\"error tw-deleted\", \"--\", inText));\n    }\n    break;\n  case \"=\": //underline\n    if (stream.match(\"==\"))\n      return chain(inBlock(\"tw-underline\", \"===\", inText));\n    break;\n  case \":\":\n    if (stream.eat(\":\"))\n      return chain(inBlock(\"comment\", \"::\"));\n    break;\n  case \"^\": //box\n    return chain(inBlock(\"tw-box\", \"^\"));\n    break;\n  case \"~\": //np\n    if (stream.match(\"np~\"))\n      return chain(inBlock(\"meta\", \"~/np~\"));\n    break;\n  }\n\n  //start of line types\n  if (sol) {\n    switch (ch) {\n    case \"!\": //header at start of line\n      if (stream.match('!!!!!')) {\n        return chain(inLine(\"header string\"));\n      } else if (stream.match('!!!!')) {\n        return chain(inLine(\"header string\"));\n      } else if (stream.match('!!!')) {\n        return chain(inLine(\"header string\"));\n      } else if (stream.match('!!')) {\n        return chain(inLine(\"header string\"));\n      } else {\n        return chain(inLine(\"header string\"));\n      }\n      break;\n    case \"*\": //unordered list line item, or <li /> at start of line\n    case \"#\": //ordered list line item, or <li /> at start of line\n    case \"+\": //ordered list line item, or <li /> at start of line\n      return chain(inLine(\"tw-listitem bracket\"));\n      break;\n    }\n  }\n\n  //stream.eatWhile(/[&{]/); was eating up plugins, turned off to act less like html and more like tiki\n  return null;\n}\n\n// Return variables for tokenizers\nvar pluginName, type;\nfunction inPlugin(stream, state) {\n  var ch = stream.next();\n  var peek = stream.peek();\n\n  if (ch == \"}\") {\n    state.tokenize = inText;\n    //type = ch == \")\" ? \"endPlugin\" : \"selfclosePlugin\"; inPlugin\n    return \"tag\";\n  } else if (ch == \"(\" || ch == \")\") {\n    return \"bracket\";\n  } else if (ch == \"=\") {\n    type = \"equals\";\n\n    if (peek == \">\") {\n      stream.next();\n      peek = stream.peek();\n    }\n\n    //here we detect values directly after equal character with no quotes\n    if (!/[\\'\\\"]/.test(peek)) {\n      state.tokenize = inAttributeNoQuote();\n    }\n    //end detect values\n\n    return \"operator\";\n  } else if (/[\\'\\\"]/.test(ch)) {\n    state.tokenize = inAttribute(ch);\n    return state.tokenize(stream, state);\n  } else {\n    stream.eatWhile(/[^\\s\\u00a0=\\\"\\'\\/?]/);\n    return \"keyword\";\n  }\n}\n\nfunction inAttribute(quote) {\n  return function(stream, state) {\n    while (!stream.eol()) {\n      if (stream.next() == quote) {\n        state.tokenize = inPlugin;\n        break;\n      }\n    }\n    return \"string\";\n  };\n}\n\nfunction inAttributeNoQuote() {\n  return function(stream, state) {\n    while (!stream.eol()) {\n      var ch = stream.next();\n      var peek = stream.peek();\n      if (ch == \" \" || ch == \",\" || /[ )}]/.test(peek)) {\n        state.tokenize = inPlugin;\n        break;\n      }\n    }\n    return \"string\";\n  };\n}\n\nvar curState, setStyle;\nfunction pass() {\n  for (var i = arguments.length - 1; i >= 0; i--) curState.cc.push(arguments[i]);\n}\n\nfunction cont() {\n  pass.apply(null, arguments);\n  return true;\n}\n\nfunction pushContext(pluginName, startOfLine) {\n  var noIndent = curState.context && curState.context.noIndent;\n  curState.context = {\n    prev: curState.context,\n    pluginName: pluginName,\n    indent: curState.indented,\n    startOfLine: startOfLine,\n    noIndent: noIndent\n  };\n}\n\nfunction popContext() {\n  if (curState.context) curState.context = curState.context.prev;\n}\n\nfunction element(type) {\n  if (type == \"openPlugin\") {curState.pluginName = pluginName; return cont(attributes, endplugin(curState.startOfLine));}\n  else if (type == \"closePlugin\") {\n    var err = false;\n    if (curState.context) {\n      err = curState.context.pluginName != pluginName;\n      popContext();\n    } else {\n      err = true;\n    }\n    if (err) setStyle = \"error\";\n    return cont(endcloseplugin(err));\n  }\n  else if (type == \"string\") {\n    if (!curState.context || curState.context.name != \"!cdata\") pushContext(\"!cdata\");\n    if (curState.tokenize == inText) popContext();\n    return cont();\n  }\n  else return cont();\n}\n\nfunction endplugin(startOfLine) {\n  return function(type) {\n    if (\n      type == \"selfclosePlugin\" ||\n        type == \"endPlugin\"\n    )\n      return cont();\n    if (type == \"endPlugin\") {pushContext(curState.pluginName, startOfLine); return cont();}\n    return cont();\n  };\n}\n\nfunction endcloseplugin(err) {\n  return function(type) {\n    if (err) setStyle = \"error\";\n    if (type == \"endPlugin\") return cont();\n    return pass();\n  };\n}\n\nfunction attributes(type) {\n  if (type == \"keyword\") {setStyle = \"attribute\"; return cont(attributes);}\n  if (type == \"equals\") return cont(attvalue, attributes);\n  return pass();\n}\nfunction attvalue(type) {\n  if (type == \"keyword\") {setStyle = \"string\"; return cont();}\n  if (type == \"string\") return cont(attvaluemaybe);\n  return pass();\n}\nfunction attvaluemaybe(type) {\n  if (type == \"string\") return cont(attvaluemaybe);\n  else return pass();\n}\nexport const tiki = {\n  name: \"tiki\",\n  startState: function() {\n    return {tokenize: inText, cc: [], indented: 0, startOfLine: true, pluginName: null, context: null};\n  },\n  token: function(stream, state) {\n    if (stream.sol()) {\n      state.startOfLine = true;\n      state.indented = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n\n    setStyle = type = pluginName = null;\n    var style = state.tokenize(stream, state);\n    if ((style || type) && style != \"comment\") {\n      curState = state;\n      while (true) {\n        var comb = state.cc.pop() || element;\n        if (comb(type || style)) break;\n      }\n    }\n    state.startOfLine = false;\n    return setStyle || style;\n  },\n  indent: function(state, textAfter, cx) {\n    var context = state.context;\n    if (context && context.noIndent) return 0;\n    if (context && /^{\\//.test(textAfter))\n      context = context.prev;\n    while (context && !context.startOfLine)\n      context = context.prev;\n    if (context) return context.indent + cx.unit;\n    else return 0;\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,QAAQ,KAAK,EAAE,UAAU,EAAE,eAAe;IACjD,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,MAAO,CAAC,OAAO,GAAG,GAAI;YACpB,IAAI,OAAO,KAAK,CAAC,aAAa;gBAC5B,MAAM,QAAQ,GAAG;gBACjB;YACF;YACA,OAAO,IAAI;QACb;QAEA,IAAI,iBAAiB,MAAM,QAAQ,GAAG;QAEtC,OAAO;IACT;AACF;AAEA,SAAS,OAAO,KAAK;IACnB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,MAAM,CAAC,OAAO,GAAG,GAAI;YACnB,OAAO,IAAI;QACb;QACA,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT;AACF;AAEA,SAAS,OAAO,MAAM,EAAE,KAAK;IAC3B,SAAS,MAAM,MAAM;QACnB,MAAM,QAAQ,GAAG;QACjB,OAAO,OAAO,QAAQ;IACxB;IAEA,IAAI,MAAM,OAAO,GAAG;IACpB,IAAI,KAAK,OAAO,IAAI;IAEpB,mBAAmB;IACnB,OAAQ;QACR,KAAK;YACH,OAAO,GAAG,CAAC;YACX,OAAO,QAAQ;YACf,OAAO,QAAQ,CAAC;YAChB,MAAM,QAAQ,GAAG;YACjB,OAAO;QACT,KAAK;YACH,IAAI,OAAO,GAAG,CAAC,MACb,OAAO,MAAM,QAAQ,UAAU,MAAM;YACvC;QACF,KAAK;YACH,IAAI,OAAO,GAAG,CAAC,MACb,OAAO,MAAM,QAAQ,MAAM,MAAM;YACnC;QACF,KAAK;YACH,IAAI,OAAO,GAAG,CAAC,MACb,OAAO,MAAM,QAAQ,QAAQ,MAAM;YACrC;QACF,KAAK;YACH,OAAO,MAAM,QAAQ,OAAO,KAAK;;QAEnC,KAAK;YACH,IAAI,OAAO,GAAG,CAAC,MACb,OAAO,MAAM,QAAQ,WAAW;YAClC;QACF,KAAK;YACH,IAAI,OAAO,GAAG,CAAC,MAAM;gBACnB,OAAO,MAAM,QAAQ,iBAAiB,MAAM;YAC9C,OAAO,IAAI,OAAO,GAAG,CAAC,MAAM;gBAC1B,OAAO,MAAM,QAAQ,oBAAoB,MAAM;YACjD;YACA;QACF,KAAK;YACH,IAAI,OAAO,KAAK,CAAC,OACf,OAAO,MAAM,QAAQ,gBAAgB,OAAO;YAC9C;QACF,KAAK;YACH,IAAI,OAAO,GAAG,CAAC,MACb,OAAO,MAAM,QAAQ,WAAW;YAClC;QACF,KAAK;YACH,OAAO,MAAM,QAAQ,UAAU;;QAEjC,KAAK;YACH,IAAI,OAAO,KAAK,CAAC,QACf,OAAO,MAAM,QAAQ,QAAQ;YAC/B;IACF;IAEA,qBAAqB;IACrB,IAAI,KAAK;QACP,OAAQ;YACR,KAAK;gBACH,IAAI,OAAO,KAAK,CAAC,UAAU;oBACzB,OAAO,MAAM,OAAO;gBACtB,OAAO,IAAI,OAAO,KAAK,CAAC,SAAS;oBAC/B,OAAO,MAAM,OAAO;gBACtB,OAAO,IAAI,OAAO,KAAK,CAAC,QAAQ;oBAC9B,OAAO,MAAM,OAAO;gBACtB,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO;oBAC7B,OAAO,MAAM,OAAO;gBACtB,OAAO;oBACL,OAAO,MAAM,OAAO;gBACtB;;YAEF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,MAAM,OAAO;;QAEtB;IACF;IAEA,qGAAqG;IACrG,OAAO;AACT;AAEA,kCAAkC;AAClC,IAAI,YAAY;AAChB,SAAS,SAAS,MAAM,EAAE,KAAK;IAC7B,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,OAAO,OAAO,IAAI;IAEtB,IAAI,MAAM,KAAK;QACb,MAAM,QAAQ,GAAG;QACjB,8DAA8D;QAC9D,OAAO;IACT,OAAO,IAAI,MAAM,OAAO,MAAM,KAAK;QACjC,OAAO;IACT,OAAO,IAAI,MAAM,KAAK;QACpB,OAAO;QAEP,IAAI,QAAQ,KAAK;YACf,OAAO,IAAI;YACX,OAAO,OAAO,IAAI;QACpB;QAEA,qEAAqE;QACrE,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO;YACxB,MAAM,QAAQ,GAAG;QACnB;QACA,mBAAmB;QAEnB,OAAO;IACT,OAAO,IAAI,SAAS,IAAI,CAAC,KAAK;QAC5B,MAAM,QAAQ,GAAG,YAAY;QAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC,OAAO;QACL,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;AACF;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,MAAO,CAAC,OAAO,GAAG,GAAI;YACpB,IAAI,OAAO,IAAI,MAAM,OAAO;gBAC1B,MAAM,QAAQ,GAAG;gBACjB;YACF;QACF;QACA,OAAO;IACT;AACF;AAEA,SAAS;IACP,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,MAAO,CAAC,OAAO,GAAG,GAAI;YACpB,IAAI,KAAK,OAAO,IAAI;YACpB,IAAI,OAAO,OAAO,IAAI;YACtB,IAAI,MAAM,OAAO,MAAM,OAAO,QAAQ,IAAI,CAAC,OAAO;gBAChD,MAAM,QAAQ,GAAG;gBACjB;YACF;QACF;QACA,OAAO;IACT;AACF;AAEA,IAAI,UAAU;AACd,SAAS;IACP,IAAK,IAAI,IAAI,UAAU,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AAC/E;AAEA,SAAS;IACP,KAAK,KAAK,CAAC,MAAM;IACjB,OAAO;AACT;AAEA,SAAS,YAAY,UAAU,EAAE,WAAW;IAC1C,IAAI,WAAW,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,QAAQ;IAC5D,SAAS,OAAO,GAAG;QACjB,MAAM,SAAS,OAAO;QACtB,YAAY;QACZ,QAAQ,SAAS,QAAQ;QACzB,aAAa;QACb,UAAU;IACZ;AACF;AAEA,SAAS;IACP,IAAI,SAAS,OAAO,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,IAAI;AAChE;AAEA,SAAS,QAAQ,IAAI;IACnB,IAAI,QAAQ,cAAc;QAAC,SAAS,UAAU,GAAG;QAAY,OAAO,KAAK,YAAY,UAAU,SAAS,WAAW;IAAG,OACjH,IAAI,QAAQ,eAAe;QAC9B,IAAI,MAAM;QACV,IAAI,SAAS,OAAO,EAAE;YACpB,MAAM,SAAS,OAAO,CAAC,UAAU,IAAI;YACrC;QACF,OAAO;YACL,MAAM;QACR;QACA,IAAI,KAAK,WAAW;QACpB,OAAO,KAAK,eAAe;IAC7B,OACK,IAAI,QAAQ,UAAU;QACzB,IAAI,CAAC,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,IAAI,UAAU,YAAY;QACxE,IAAI,SAAS,QAAQ,IAAI,QAAQ;QACjC,OAAO;IACT,OACK,OAAO;AACd;AAEA,SAAS,UAAU,WAAW;IAC5B,OAAO,SAAS,IAAI;QAClB,IACE,QAAQ,qBACN,QAAQ,aAEV,OAAO;QACT,IAAI,QAAQ,aAAa;YAAC,YAAY,SAAS,UAAU,EAAE;YAAc,OAAO;QAAO;QACvF,OAAO;IACT;AACF;AAEA,SAAS,eAAe,GAAG;IACzB,OAAO,SAAS,IAAI;QAClB,IAAI,KAAK,WAAW;QACpB,IAAI,QAAQ,aAAa,OAAO;QAChC,OAAO;IACT;AACF;AAEA,SAAS,WAAW,IAAI;IACtB,IAAI,QAAQ,WAAW;QAAC,WAAW;QAAa,OAAO,KAAK;IAAY;IACxE,IAAI,QAAQ,UAAU,OAAO,KAAK,UAAU;IAC5C,OAAO;AACT;AACA,SAAS,SAAS,IAAI;IACpB,IAAI,QAAQ,WAAW;QAAC,WAAW;QAAU,OAAO;IAAO;IAC3D,IAAI,QAAQ,UAAU,OAAO,KAAK;IAClC,OAAO;AACT;AACA,SAAS,cAAc,IAAI;IACzB,IAAI,QAAQ,UAAU,OAAO,KAAK;SAC7B,OAAO;AACd;AACO,MAAM,OAAO;IAClB,MAAM;IACN,YAAY;QACV,OAAO;YAAC,UAAU;YAAQ,IAAI,EAAE;YAAE,UAAU;YAAG,aAAa;YAAM,YAAY;YAAM,SAAS;QAAI;IACnG;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,GAAG,IAAI;YAChB,MAAM,WAAW,GAAG;YACpB,MAAM,QAAQ,GAAG,OAAO,WAAW;QACrC;QACA,IAAI,OAAO,QAAQ,IAAI,OAAO;QAE9B,WAAW,OAAO,aAAa;QAC/B,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QACnC,IAAI,CAAC,SAAS,IAAI,KAAK,SAAS,WAAW;YACzC,WAAW;YACX,MAAO,KAAM;gBACX,IAAI,OAAO,MAAM,EAAE,CAAC,GAAG,MAAM;gBAC7B,IAAI,KAAK,QAAQ,QAAQ;YAC3B;QACF;QACA,MAAM,WAAW,GAAG;QACpB,OAAO,YAAY;IACrB;IACA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,UAAU,MAAM,OAAO;QAC3B,IAAI,WAAW,QAAQ,QAAQ,EAAE,OAAO;QACxC,IAAI,WAAW,OAAO,IAAI,CAAC,YACzB,UAAU,QAAQ,IAAI;QACxB,MAAO,WAAW,CAAC,QAAQ,WAAW,CACpC,UAAU,QAAQ,IAAI;QACxB,IAAI,SAAS,OAAO,QAAQ,MAAM,GAAG,GAAG,IAAI;aACvC,OAAO;IACd;AACF", "ignoreList": [0], "debugId": null}}]}