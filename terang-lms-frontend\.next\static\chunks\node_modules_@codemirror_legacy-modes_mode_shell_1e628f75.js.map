{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/shell.js"], "sourcesContent": ["var words = {};\nfunction define(style, dict) {\n  for(var i = 0; i < dict.length; i++) {\n    words[dict[i]] = style;\n  }\n};\n\nvar commonAtoms = [\"true\", \"false\"];\nvar commonKeywords = [\"if\", \"then\", \"do\", \"else\", \"elif\", \"while\", \"until\", \"for\", \"in\", \"esac\", \"fi\",\n                      \"fin\", \"fil\", \"done\", \"exit\", \"set\", \"unset\", \"export\", \"function\"];\nvar commonCommands = [\"ab\", \"awk\", \"bash\", \"beep\", \"cat\", \"cc\", \"cd\", \"chown\", \"chmod\", \"chroot\", \"clear\",\n                      \"cp\", \"curl\", \"cut\", \"diff\", \"echo\", \"find\", \"gawk\", \"gcc\", \"get\", \"git\", \"grep\", \"hg\", \"kill\", \"killall\",\n                      \"ln\", \"ls\", \"make\", \"mkdir\", \"openssl\", \"mv\", \"nc\", \"nl\", \"node\", \"npm\", \"ping\", \"ps\", \"restart\", \"rm\",\n                      \"rmdir\", \"sed\", \"service\", \"sh\", \"shopt\", \"shred\", \"source\", \"sort\", \"sleep\", \"ssh\", \"start\", \"stop\",\n                      \"su\", \"sudo\", \"svn\", \"tee\", \"telnet\", \"top\", \"touch\", \"vi\", \"vim\", \"wall\", \"wc\", \"wget\", \"who\", \"write\",\n                      \"yes\", \"zsh\"];\n\ndefine('atom', commonAtoms);\ndefine('keyword', commonKeywords);\ndefine('builtin', commonCommands);\n\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) return null;\n\n  var sol = stream.sol();\n  var ch = stream.next();\n\n  if (ch === '\\\\') {\n    stream.next();\n    return null;\n  }\n  if (ch === '\\'' || ch === '\"' || ch === '`') {\n    state.tokens.unshift(tokenString(ch, ch === \"`\" ? \"quote\" : \"string\"));\n    return tokenize(stream, state);\n  }\n  if (ch === '#') {\n    if (sol && stream.eat('!')) {\n      stream.skipToEnd();\n      return 'meta'; // 'comment'?\n    }\n    stream.skipToEnd();\n    return 'comment';\n  }\n  if (ch === '$') {\n    state.tokens.unshift(tokenDollar);\n    return tokenize(stream, state);\n  }\n  if (ch === '+' || ch === '=') {\n    return 'operator';\n  }\n  if (ch === '-') {\n    stream.eat('-');\n    stream.eatWhile(/\\w/);\n    return 'attribute';\n  }\n  if (ch == \"<\") {\n    if (stream.match(\"<<\")) return \"operator\"\n    var heredoc = stream.match(/^<-?\\s*(?:['\"]([^'\"]*)['\"]|([^'\"\\s]*))/)\n    if (heredoc) {\n      state.tokens.unshift(tokenHeredoc(heredoc[1] || heredoc[2]))\n      return 'string.special'\n    }\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/\\d/);\n    if(stream.eol() || !/\\w/.test(stream.peek())) {\n      return 'number';\n    }\n  }\n  stream.eatWhile(/[\\w-]/);\n  var cur = stream.current();\n  if (stream.peek() === '=' && /\\w+/.test(cur)) return 'def';\n  return words.hasOwnProperty(cur) ? words[cur] : null;\n}\n\nfunction tokenString(quote, style) {\n  var close = quote == \"(\" ? \")\" : quote == \"{\" ? \"}\" : quote\n  return function(stream, state) {\n    var next, escaped = false;\n    while ((next = stream.next()) != null) {\n      if (next === close && !escaped) {\n        state.tokens.shift();\n        break;\n      } else if (next === '$' && !escaped && quote !== \"'\" && stream.peek() != close) {\n        escaped = true;\n        stream.backUp(1);\n        state.tokens.unshift(tokenDollar);\n        break;\n      } else if (!escaped && quote !== close && next === quote) {\n        state.tokens.unshift(tokenString(quote, style))\n        return tokenize(stream, state)\n      } else if (!escaped && /['\"]/.test(next) && !/['\"]/.test(quote)) {\n        state.tokens.unshift(tokenStringStart(next, \"string\"));\n        stream.backUp(1);\n        break;\n      }\n      escaped = !escaped && next === '\\\\';\n    }\n    return style;\n  };\n};\n\nfunction tokenStringStart(quote, style) {\n  return function(stream, state) {\n    state.tokens[0] = tokenString(quote, style)\n    stream.next()\n    return tokenize(stream, state)\n  }\n}\n\nvar tokenDollar = function(stream, state) {\n  if (state.tokens.length > 1) stream.eat('$');\n  var ch = stream.next()\n  if (/['\"({]/.test(ch)) {\n    state.tokens[0] = tokenString(ch, ch == \"(\" ? \"quote\" : ch == \"{\" ? \"def\" : \"string\");\n    return tokenize(stream, state);\n  }\n  if (!/\\d/.test(ch)) stream.eatWhile(/\\w/);\n  state.tokens.shift();\n  return 'def';\n};\n\nfunction tokenHeredoc(delim) {\n  return function(stream, state) {\n    if (stream.sol() && stream.string == delim) state.tokens.shift()\n    stream.skipToEnd()\n    return \"string.special\"\n  }\n}\n\nfunction tokenize(stream, state) {\n  return (state.tokens[0] || tokenBase) (stream, state);\n};\n\nexport const shell = {\n  name: \"shell\",\n  startState: function() {return {tokens:[]};},\n  token: function(stream, state) {\n    return tokenize(stream, state);\n  },\n  languageData: {\n    autocomplete: commonAtoms.concat(commonKeywords, commonCommands),\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]},\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,QAAQ,CAAC;AACb,SAAS,OAAO,KAAK,EAAE,IAAI;IACzB,IAAI,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACnC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG;IACnB;AACF;;AAEA,IAAI,cAAc;IAAC;IAAQ;CAAQ;AACnC,IAAI,iBAAiB;IAAC;IAAM;IAAQ;IAAM;IAAQ;IAAQ;IAAS;IAAS;IAAO;IAAM;IAAQ;IAC3E;IAAO;IAAO;IAAQ;IAAQ;IAAO;IAAS;IAAU;CAAW;AACzF,IAAI,iBAAiB;IAAC;IAAM;IAAO;IAAQ;IAAQ;IAAO;IAAM;IAAM;IAAS;IAAS;IAAU;IAC5E;IAAM;IAAQ;IAAO;IAAQ;IAAQ;IAAQ;IAAQ;IAAO;IAAO;IAAO;IAAQ;IAAM;IAAQ;IAChG;IAAM;IAAM;IAAQ;IAAS;IAAW;IAAM;IAAM;IAAM;IAAQ;IAAO;IAAQ;IAAM;IAAW;IAClG;IAAS;IAAO;IAAW;IAAM;IAAS;IAAS;IAAU;IAAQ;IAAS;IAAO;IAAS;IAC9F;IAAM;IAAQ;IAAO;IAAO;IAAU;IAAO;IAAS;IAAM;IAAO;IAAQ;IAAM;IAAQ;IAAO;IAChG;IAAO;CAAM;AAEnC,OAAO,QAAQ;AACf,OAAO,WAAW;AAClB,OAAO,WAAW;AAElB,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,OAAO,QAAQ,IAAI,OAAO;IAE9B,IAAI,MAAM,OAAO,GAAG;IACpB,IAAI,KAAK,OAAO,IAAI;IAEpB,IAAI,OAAO,MAAM;QACf,OAAO,IAAI;QACX,OAAO;IACT;IACA,IAAI,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK;QAC3C,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,IAAI,OAAO,MAAM,UAAU;QAC5D,OAAO,SAAS,QAAQ;IAC1B;IACA,IAAI,OAAO,KAAK;QACd,IAAI,OAAO,OAAO,GAAG,CAAC,MAAM;YAC1B,OAAO,SAAS;YAChB,OAAO,QAAQ,aAAa;QAC9B;QACA,OAAO,SAAS;QAChB,OAAO;IACT;IACA,IAAI,OAAO,KAAK;QACd,MAAM,MAAM,CAAC,OAAO,CAAC;QACrB,OAAO,SAAS,QAAQ;IAC1B;IACA,IAAI,OAAO,OAAO,OAAO,KAAK;QAC5B,OAAO;IACT;IACA,IAAI,OAAO,KAAK;QACd,OAAO,GAAG,CAAC;QACX,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,IAAI,MAAM,KAAK;QACb,IAAI,OAAO,KAAK,CAAC,OAAO,OAAO;QAC/B,IAAI,UAAU,OAAO,KAAK,CAAC;QAC3B,IAAI,SAAS;YACX,MAAM,MAAM,CAAC,OAAO,CAAC,aAAa,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;YAC1D,OAAO;QACT;IACF;IACA,IAAI,KAAK,IAAI,CAAC,KAAK;QACjB,OAAO,QAAQ,CAAC;QAChB,IAAG,OAAO,GAAG,MAAM,CAAC,KAAK,IAAI,CAAC,OAAO,IAAI,KAAK;YAC5C,OAAO;QACT;IACF;IACA,OAAO,QAAQ,CAAC;IAChB,IAAI,MAAM,OAAO,OAAO;IACxB,IAAI,OAAO,IAAI,OAAO,OAAO,MAAM,IAAI,CAAC,MAAM,OAAO;IACrD,OAAO,MAAM,cAAc,CAAC,OAAO,KAAK,CAAC,IAAI,GAAG;AAClD;AAEA,SAAS,YAAY,KAAK,EAAE,KAAK;IAC/B,IAAI,QAAQ,SAAS,MAAM,MAAM,SAAS,MAAM,MAAM;IACtD,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,MAAM,UAAU;QACpB,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,SAAS,SAAS,CAAC,SAAS;gBAC9B,MAAM,MAAM,CAAC,KAAK;gBAClB;YACF,OAAO,IAAI,SAAS,OAAO,CAAC,WAAW,UAAU,OAAO,OAAO,IAAI,MAAM,OAAO;gBAC9E,UAAU;gBACV,OAAO,MAAM,CAAC;gBACd,MAAM,MAAM,CAAC,OAAO,CAAC;gBACrB;YACF,OAAO,IAAI,CAAC,WAAW,UAAU,SAAS,SAAS,OAAO;gBACxD,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,OAAO;gBACxC,OAAO,SAAS,QAAQ;YAC1B,OAAO,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,QAAQ;gBAC/D,MAAM,MAAM,CAAC,OAAO,CAAC,iBAAiB,MAAM;gBAC5C,OAAO,MAAM,CAAC;gBACd;YACF;YACA,UAAU,CAAC,WAAW,SAAS;QACjC;QACA,OAAO;IACT;AACF;;AAEA,SAAS,iBAAiB,KAAK,EAAE,KAAK;IACpC,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,MAAM,MAAM,CAAC,EAAE,GAAG,YAAY,OAAO;QACrC,OAAO,IAAI;QACX,OAAO,SAAS,QAAQ;IAC1B;AACF;AAEA,IAAI,cAAc,SAAS,MAAM,EAAE,KAAK;IACtC,IAAI,MAAM,MAAM,CAAC,MAAM,GAAG,GAAG,OAAO,GAAG,CAAC;IACxC,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,SAAS,IAAI,CAAC,KAAK;QACrB,MAAM,MAAM,CAAC,EAAE,GAAG,YAAY,IAAI,MAAM,MAAM,UAAU,MAAM,MAAM,QAAQ;QAC5E,OAAO,SAAS,QAAQ;IAC1B;IACA,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,OAAO,QAAQ,CAAC;IACpC,MAAM,MAAM,CAAC,KAAK;IAClB,OAAO;AACT;AAEA,SAAS,aAAa,KAAK;IACzB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,GAAG,MAAM,OAAO,MAAM,IAAI,OAAO,MAAM,MAAM,CAAC,KAAK;QAC9D,OAAO,SAAS;QAChB,OAAO;IACT;AACF;AAEA,SAAS,SAAS,MAAM,EAAE,KAAK;IAC7B,OAAO,CAAC,MAAM,MAAM,CAAC,EAAE,IAAI,SAAS,EAAG,QAAQ;AACjD;;AAEO,MAAM,QAAQ;IACnB,MAAM;IACN,YAAY;QAAY,OAAO;YAAC,QAAO,EAAE;QAAA;IAAE;IAC3C,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,OAAO,SAAS,QAAQ;IAC1B;IACA,cAAc;QACZ,cAAc,YAAY,MAAM,CAAC,gBAAgB;QACjD,eAAe;YAAC,UAAU;gBAAC;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;aAAI;QAAA;QACxD,eAAe;YAAC,MAAM;QAAG;IAC3B;AACF", "ignoreList": [0], "debugId": null}}]}