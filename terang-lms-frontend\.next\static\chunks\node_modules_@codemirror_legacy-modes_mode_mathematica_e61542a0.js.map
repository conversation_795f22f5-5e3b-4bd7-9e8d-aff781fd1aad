{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/mathematica.js"], "sourcesContent": ["// used pattern building blocks\nvar Identifier = '[a-zA-Z\\\\$][a-zA-Z0-9\\\\$]*';\nvar pBase      = \"(?:\\\\d+)\";\nvar pFloat     = \"(?:\\\\.\\\\d+|\\\\d+\\\\.\\\\d*|\\\\d+)\";\nvar pFloatBase = \"(?:\\\\.\\\\w+|\\\\w+\\\\.\\\\w*|\\\\w+)\";\nvar pPrecision = \"(?:`(?:`?\"+pFloat+\")?)\";\n\n// regular expressions\nvar reBaseForm        = new RegExp('(?:'+pBase+'(?:\\\\^\\\\^'+pFloatBase+pPrecision+'?(?:\\\\*\\\\^[+-]?\\\\d+)?))');\nvar reFloatForm       = new RegExp('(?:' + pFloat + pPrecision + '?(?:\\\\*\\\\^[+-]?\\\\d+)?)');\nvar reIdInContext     = new RegExp('(?:`?)(?:' + Identifier + ')(?:`(?:' + Identifier + '))*(?:`?)');\n\nfunction tokenBase(stream, state) {\n  var ch;\n\n  // get next character\n  ch = stream.next();\n\n  // string\n  if (ch === '\"') {\n    state.tokenize = tokenString;\n    return state.tokenize(stream, state);\n  }\n\n  // comment\n  if (ch === '(') {\n    if (stream.eat('*')) {\n      state.commentLevel++;\n      state.tokenize = tokenComment;\n      return state.tokenize(stream, state);\n    }\n  }\n\n  // go back one character\n  stream.backUp(1);\n\n  // look for numbers\n  // Numbers in a baseform\n  if (stream.match(reBaseForm, true, false)) {\n    return 'number';\n  }\n\n  // Mathematica numbers. Floats (1.2, .2, 1.) can have optionally a precision (`float) or an accuracy definition\n  // (``float). Note: while 1.2` is possible 1.2`` is not. At the end an exponent (float*^+12) can follow.\n  if (stream.match(reFloatForm, true, false)) {\n    return 'number';\n  }\n\n  /* In[23] and Out[34] */\n  if (stream.match(/(?:In|Out)\\[[0-9]*\\]/, true, false)) {\n    return 'atom';\n  }\n\n  // usage\n  if (stream.match(/([a-zA-Z\\$][a-zA-Z0-9\\$]*(?:`[a-zA-Z0-9\\$]+)*::usage)/, true, false)) {\n    return 'meta';\n  }\n\n  // message\n  if (stream.match(/([a-zA-Z\\$][a-zA-Z0-9\\$]*(?:`[a-zA-Z0-9\\$]+)*::[a-zA-Z\\$][a-zA-Z0-9\\$]*):?/, true, false)) {\n    return 'string.special';\n  }\n\n  // this makes a look-ahead match for something like variable:{_Integer}\n  // the match is then forwarded to the mma-patterns tokenizer.\n  if (stream.match(/([a-zA-Z\\$][a-zA-Z0-9\\$]*\\s*:)(?:(?:[a-zA-Z\\$][a-zA-Z0-9\\$]*)|(?:[^:=>~@\\^\\&\\*\\)\\[\\]'\\?,\\|])).*/, true, false)) {\n    return 'variableName.special';\n  }\n\n  // catch variables which are used together with Blank (_), BlankSequence (__) or BlankNullSequence (___)\n  // Cannot start with a number, but can have numbers at any other position. Examples\n  // blub__Integer, a1_, b34_Integer32\n  if (stream.match(/[a-zA-Z\\$][a-zA-Z0-9\\$]*_+[a-zA-Z\\$][a-zA-Z0-9\\$]*/, true, false)) {\n    return 'variableName.special';\n  }\n  if (stream.match(/[a-zA-Z\\$][a-zA-Z0-9\\$]*_+/, true, false)) {\n    return 'variableName.special';\n  }\n  if (stream.match(/_+[a-zA-Z\\$][a-zA-Z0-9\\$]*/, true, false)) {\n    return 'variableName.special';\n  }\n\n  // Named characters in Mathematica, like \\[Gamma].\n  if (stream.match(/\\\\\\[[a-zA-Z\\$][a-zA-Z0-9\\$]*\\]/, true, false)) {\n    return 'character';\n  }\n\n  // Match all braces separately\n  if (stream.match(/(?:\\[|\\]|{|}|\\(|\\))/, true, false)) {\n    return 'bracket';\n  }\n\n  // Catch Slots (#, ##, #3, ##9 and the V10 named slots #name). I have never seen someone using more than one digit after #, so we match\n  // only one.\n  if (stream.match(/(?:#[a-zA-Z\\$][a-zA-Z0-9\\$]*|#+[0-9]?)/, true, false)) {\n    return 'variableName.constant';\n  }\n\n  // Literals like variables, keywords, functions\n  if (stream.match(reIdInContext, true, false)) {\n    return 'keyword';\n  }\n\n  // operators. Note that operators like @@ or /; are matched separately for each symbol.\n  if (stream.match(/(?:\\\\|\\+|\\-|\\*|\\/|,|;|\\.|:|@|~|=|>|<|&|\\||_|`|'|\\^|\\?|!|%)/, true, false)) {\n    return 'operator';\n  }\n\n  // everything else is an error\n  stream.next(); // advance the stream.\n  return 'error';\n}\n\nfunction tokenString(stream, state) {\n  var next, end = false, escaped = false;\n  while ((next = stream.next()) != null) {\n    if (next === '\"' && !escaped) {\n      end = true;\n      break;\n    }\n    escaped = !escaped && next === '\\\\';\n  }\n  if (end && !escaped) {\n    state.tokenize = tokenBase;\n  }\n  return 'string';\n};\n\nfunction tokenComment(stream, state) {\n  var prev, next;\n  while(state.commentLevel > 0 && (next = stream.next()) != null) {\n    if (prev === '(' && next === '*') state.commentLevel++;\n    if (prev === '*' && next === ')') state.commentLevel--;\n    prev = next;\n  }\n  if (state.commentLevel <= 0) {\n    state.tokenize = tokenBase;\n  }\n  return 'comment';\n}\n\nexport const mathematica = {\n  name: \"mathematica\",\n  startState: function() {return {tokenize: tokenBase, commentLevel: 0};},\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  },\n  languageData: {\n    commentTokens: {block: {open: \"(*\", close: \"*)\"}}\n  }\n}\n\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;AAC/B,IAAI,aAAa;AACjB,IAAI,QAAa;AACjB,IAAI,SAAa;AACjB,IAAI,aAAa;AACjB,IAAI,aAAa,cAAY,SAAO;AAEpC,sBAAsB;AACtB,IAAI,aAAoB,IAAI,OAAO,QAAM,QAAM,cAAY,aAAW,aAAW;AACjF,IAAI,cAAoB,IAAI,OAAO,QAAQ,SAAS,aAAa;AACjE,IAAI,gBAAoB,IAAI,OAAO,cAAc,aAAa,aAAa,aAAa;AAExF,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI;IAEJ,qBAAqB;IACrB,KAAK,OAAO,IAAI;IAEhB,SAAS;IACT,IAAI,OAAO,KAAK;QACd,MAAM,QAAQ,GAAG;QACjB,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IAEA,UAAU;IACV,IAAI,OAAO,KAAK;QACd,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,MAAM,YAAY;YAClB,MAAM,QAAQ,GAAG;YACjB,OAAO,MAAM,QAAQ,CAAC,QAAQ;QAChC;IACF;IAEA,wBAAwB;IACxB,OAAO,MAAM,CAAC;IAEd,mBAAmB;IACnB,wBAAwB;IACxB,IAAI,OAAO,KAAK,CAAC,YAAY,MAAM,QAAQ;QACzC,OAAO;IACT;IAEA,+GAA+G;IAC/G,wGAAwG;IACxG,IAAI,OAAO,KAAK,CAAC,aAAa,MAAM,QAAQ;QAC1C,OAAO;IACT;IAEA,sBAAsB,GACtB,IAAI,OAAO,KAAK,CAAC,wBAAwB,MAAM,QAAQ;QACrD,OAAO;IACT;IAEA,QAAQ;IACR,IAAI,OAAO,KAAK,CAAC,yDAAyD,MAAM,QAAQ;QACtF,OAAO;IACT;IAEA,UAAU;IACV,IAAI,OAAO,KAAK,CAAC,8EAA8E,MAAM,QAAQ;QAC3G,OAAO;IACT;IAEA,uEAAuE;IACvE,6DAA6D;IAC7D,IAAI,OAAO,KAAK,CAAC,mGAAmG,MAAM,QAAQ;QAChI,OAAO;IACT;IAEA,wGAAwG;IACxG,mFAAmF;IACnF,oCAAoC;IACpC,IAAI,OAAO,KAAK,CAAC,sDAAsD,MAAM,QAAQ;QACnF,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,8BAA8B,MAAM,QAAQ;QAC3D,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,8BAA8B,MAAM,QAAQ;QAC3D,OAAO;IACT;IAEA,kDAAkD;IAClD,IAAI,OAAO,KAAK,CAAC,kCAAkC,MAAM,QAAQ;QAC/D,OAAO;IACT;IAEA,8BAA8B;IAC9B,IAAI,OAAO,KAAK,CAAC,uBAAuB,MAAM,QAAQ;QACpD,OAAO;IACT;IAEA,uIAAuI;IACvI,YAAY;IACZ,IAAI,OAAO,KAAK,CAAC,0CAA0C,MAAM,QAAQ;QACvE,OAAO;IACT;IAEA,+CAA+C;IAC/C,IAAI,OAAO,KAAK,CAAC,eAAe,MAAM,QAAQ;QAC5C,OAAO;IACT;IAEA,uFAAuF;IACvF,IAAI,OAAO,KAAK,CAAC,8DAA8D,MAAM,QAAQ;QAC3F,OAAO;IACT;IAEA,8BAA8B;IAC9B,OAAO,IAAI,IAAI,sBAAsB;IACrC,OAAO;AACT;AAEA,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,IAAI,MAAM,MAAM,OAAO,UAAU;IACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;QACrC,IAAI,SAAS,OAAO,CAAC,SAAS;YAC5B,MAAM;YACN;QACF;QACA,UAAU,CAAC,WAAW,SAAS;IACjC;IACA,IAAI,OAAO,CAAC,SAAS;QACnB,MAAM,QAAQ,GAAG;IACnB;IACA,OAAO;AACT;;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,MAAM;IACV,MAAM,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;QAC9D,IAAI,SAAS,OAAO,SAAS,KAAK,MAAM,YAAY;QACpD,IAAI,SAAS,OAAO,SAAS,KAAK,MAAM,YAAY;QACpD,OAAO;IACT;IACA,IAAI,MAAM,YAAY,IAAI,GAAG;QAC3B,MAAM,QAAQ,GAAG;IACnB;IACA,OAAO;AACT;AAEO,MAAM,cAAc;IACzB,MAAM;IACN,YAAY;QAAY,OAAO;YAAC,UAAU;YAAW,cAAc;QAAC;IAAE;IACtE,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IACA,cAAc;QACZ,eAAe;YAAC,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;IAClD;AACF", "ignoreList": [0], "debugId": null}}]}