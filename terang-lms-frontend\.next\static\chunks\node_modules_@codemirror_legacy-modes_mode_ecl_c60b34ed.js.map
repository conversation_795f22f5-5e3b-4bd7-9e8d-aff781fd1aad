{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/ecl.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nfunction metaHook(stream, state) {\n  if (!state.startOfLine) return false;\n  stream.skipToEnd();\n  return \"meta\";\n}\n\nvar keyword = words(\"abs acos allnodes ascii asin asstring atan atan2 ave case choose choosen choosesets clustersize combine correlation cos cosh count covariance cron dataset dedup define denormalize distribute distributed distribution ebcdic enth error evaluate event eventextra eventname exists exp failcode failmessage fetch fromunicode getisvalid global graph group hash hash32 hash64 hashcrc hashmd5 having if index intformat isvalid iterate join keyunicode length library limit ln local log loop map matched matchlength matchposition matchtext matchunicode max merge mergejoin min nolocal nonempty normalize parse pipe power preload process project pull random range rank ranked realformat recordof regexfind regexreplace regroup rejected rollup round roundup row rowdiff sample set sin sinh sizeof soapcall sort sorted sqrt stepped stored sum table tan tanh thisnode topn tounicode transfer trim truncate typeof ungroup unicodeorder variance which workunit xmldecode xmlencode xmltext xmlunicode\");\nvar variable = words(\"apply assert build buildindex evaluate fail keydiff keypatch loadxml nothor notify output parallel sequential soapcall wait\");\nvar variable_2 = words(\"__compressed__ all and any as atmost before beginc++ best between case const counter csv descend encrypt end endc++ endmacro except exclusive expire export extend false few first flat from full function group header heading hole ifblock import in interface joined keep keyed last left limit load local locale lookup macro many maxcount maxlength min skew module named nocase noroot noscan nosort not of only opt or outer overwrite packed partition penalty physicallength pipe quote record relationship repeat return right scan self separator service shared skew skip sql store terminator thor threshold token transform trim true type unicodeorder unsorted validate virtual whole wild within xml xpath\");\nvar variable_3 = words(\"ascii big_endian boolean data decimal ebcdic integer pattern qstring real record rule set of string token udecimal unicode unsigned varstring varunicode\");\nvar builtin = words(\"checkpoint deprecated failcode failmessage failure global independent onwarning persist priority recovery stored success wait when\");\nvar blockKeywords = words(\"catch class do else finally for if switch try while\");\nvar atoms = words(\"true false null\");\nvar hooks = {\"#\": metaHook};\nvar isOperatorChar = /[+\\-*&%=<>!?|\\/]/;\n\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (hooks[ch]) {\n    var result = hooks[ch](stream, state);\n    if (result !== false) return result;\n  }\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_]/);\n  var cur = stream.current().toLowerCase();\n  if (keyword.propertyIsEnumerable(cur)) {\n    if (blockKeywords.propertyIsEnumerable(cur)) curPunc = \"newstatement\";\n    return \"keyword\";\n  } else if (variable.propertyIsEnumerable(cur)) {\n    if (blockKeywords.propertyIsEnumerable(cur)) curPunc = \"newstatement\";\n    return \"variable\";\n  } else if (variable_2.propertyIsEnumerable(cur)) {\n    if (blockKeywords.propertyIsEnumerable(cur)) curPunc = \"newstatement\";\n    return \"modifier\";\n  } else if (variable_3.propertyIsEnumerable(cur)) {\n    if (blockKeywords.propertyIsEnumerable(cur)) curPunc = \"newstatement\";\n    return \"type\";\n  } else if (builtin.propertyIsEnumerable(cur)) {\n    if (blockKeywords.propertyIsEnumerable(cur)) curPunc = \"newstatement\";\n    return \"builtin\";\n  } else { //Data types are of from KEYWORD##\n    var i = cur.length - 1;\n    while(i >= 0 && (!isNaN(cur[i]) || cur[i] == '_'))\n      --i;\n\n    if (i > 0) {\n      var cur2 = cur.substr(0, i + 1);\n      if (variable_3.propertyIsEnumerable(cur2)) {\n        if (blockKeywords.propertyIsEnumerable(cur2)) curPunc = \"newstatement\";\n        return \"type\";\n      }\n    }\n  }\n  if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  return null;\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {end = true; break;}\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped)\n      state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type) {\n  return state.context = new Context(state.indented, col, type, null, state.context);\n}\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n// Interface\n\nexport const ecl = {\n  name: \"ecl\",\n  startState: function(indentUnit) {\n    return {\n      tokenize: null,\n      context: new Context(-indentUnit, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\" || style == \"meta\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\") && ctx.type == \"statement\") popContext(state);\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (ctx.type == \"}\" || ctx.type == \"top\" || (ctx.type == \"statement\" && curPunc == \"newstatement\"))\n      pushContext(state, stream.column(), \"statement\");\n    state.startOfLine = false;\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase && state.tokenize != null) return 0;\n    var ctx = state.context, firstChar = textAfter && textAfter.charAt(0);\n    if (ctx.type == \"statement\" && firstChar == \"}\") ctx = ctx.prev;\n    var closing = firstChar == ctx.type;\n    if (ctx.type == \"statement\") return ctx.indented + (firstChar == \"{\" ? 0 : cx.unit);\n    else if (ctx.align) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indented + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AAEA,SAAS,SAAS,MAAM,EAAE,KAAK;IAC7B,IAAI,CAAC,MAAM,WAAW,EAAE,OAAO;IAC/B,OAAO,SAAS;IAChB,OAAO;AACT;AAEA,IAAI,UAAU,MAAM;AACpB,IAAI,WAAW,MAAM;AACrB,IAAI,aAAa,MAAM;AACvB,IAAI,aAAa,MAAM;AACvB,IAAI,UAAU,MAAM;AACpB,IAAI,gBAAgB,MAAM;AAC1B,IAAI,QAAQ,MAAM;AAClB,IAAI,QAAQ;IAAC,KAAK;AAAQ;AAC1B,IAAI,iBAAiB;AAErB,IAAI;AAEJ,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,KAAK,CAAC,GAAG,EAAE;QACb,IAAI,SAAS,KAAK,CAAC,GAAG,CAAC,QAAQ;QAC/B,IAAI,WAAW,OAAO,OAAO;IAC/B;IACA,IAAI,MAAM,OAAO,MAAM,KAAK;QAC1B,MAAM,QAAQ,GAAG,YAAY;QAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IACA,IAAI,qBAAqB,IAAI,CAAC,KAAK;QACjC,UAAU;QACV,OAAO;IACT;IACA,IAAI,KAAK,IAAI,CAAC,KAAK;QACjB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,IAAI,MAAM,KAAK;QACb,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,MAAM,QAAQ,GAAG;YACjB,OAAO,aAAa,QAAQ;QAC9B;QACA,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO,SAAS;YAChB,OAAO;QACT;IACF;IACA,IAAI,eAAe,IAAI,CAAC,KAAK;QAC3B,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,OAAO,QAAQ,CAAC;IAChB,IAAI,MAAM,OAAO,OAAO,GAAG,WAAW;IACtC,IAAI,QAAQ,oBAAoB,CAAC,MAAM;QACrC,IAAI,cAAc,oBAAoB,CAAC,MAAM,UAAU;QACvD,OAAO;IACT,OAAO,IAAI,SAAS,oBAAoB,CAAC,MAAM;QAC7C,IAAI,cAAc,oBAAoB,CAAC,MAAM,UAAU;QACvD,OAAO;IACT,OAAO,IAAI,WAAW,oBAAoB,CAAC,MAAM;QAC/C,IAAI,cAAc,oBAAoB,CAAC,MAAM,UAAU;QACvD,OAAO;IACT,OAAO,IAAI,WAAW,oBAAoB,CAAC,MAAM;QAC/C,IAAI,cAAc,oBAAoB,CAAC,MAAM,UAAU;QACvD,OAAO;IACT,OAAO,IAAI,QAAQ,oBAAoB,CAAC,MAAM;QAC5C,IAAI,cAAc,oBAAoB,CAAC,MAAM,UAAU;QACvD,OAAO;IACT,OAAO;QACL,IAAI,IAAI,IAAI,MAAM,GAAG;QACrB,MAAM,KAAK,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,GAAG,EAC9C,EAAE;QAEJ,IAAI,IAAI,GAAG;YACT,IAAI,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI;YAC7B,IAAI,WAAW,oBAAoB,CAAC,OAAO;gBACzC,IAAI,cAAc,oBAAoB,CAAC,OAAO,UAAU;gBACxD,OAAO;YACT;QACF;IACF;IACA,IAAI,MAAM,oBAAoB,CAAC,MAAM,OAAO;IAC5C,OAAO;AACT;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;QACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,QAAQ,SAAS,CAAC,SAAS;gBAAC,MAAM;gBAAM;YAAM;YAClD,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,OAAO,CAAC,SACV,MAAM,QAAQ,GAAG;QACnB,OAAO;IACT;AACF;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,WAAW,OAAO;IACtB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,SAAS,QAAQ,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;IAClD,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,IAAI,GAAG;AACd;AACA,SAAS,YAAY,KAAK,EAAE,GAAG,EAAE,IAAI;IACnC,OAAO,MAAM,OAAO,GAAG,IAAI,QAAQ,MAAM,QAAQ,EAAE,KAAK,MAAM,MAAM,MAAM,OAAO;AACnF;AACA,SAAS,WAAW,KAAK;IACvB,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI;IAC1B,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAC/B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ;IACzC,OAAO,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;AAC3C;AAIO,MAAM,MAAM;IACjB,MAAM;IACN,YAAY,SAAS,UAAU;QAC7B,OAAO;YACL,UAAU;YACV,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,OAAO;YAC5C,UAAU;YACV,aAAa;QACf;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,MAAM,MAAM,OAAO;QACvB,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;YACnC,MAAM,QAAQ,GAAG,OAAO,WAAW;YACnC,MAAM,WAAW,GAAG;QACtB;QACA,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,UAAU;QACV,IAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,SAAS,EAAE,QAAQ;QAClD,IAAI,SAAS,aAAa,SAAS,QAAQ,OAAO;QAClD,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;QAEnC,IAAI,CAAC,WAAW,OAAO,WAAW,GAAG,KAAK,IAAI,IAAI,IAAI,aAAa,WAAW;aACzE,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK;YACvB,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;YACjD,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM,WAAW;YACtC,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;QACnD,OACK,IAAI,WAAW,IAAI,IAAI,EAAE,WAAW;aACpC,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,SAAU,IAAI,IAAI,IAAI,eAAe,WAAW,gBACtF,YAAY,OAAO,OAAO,MAAM,IAAI;QACtC,MAAM,WAAW,GAAG;QACpB,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,MAAM,QAAQ,IAAI,aAAa,MAAM,QAAQ,IAAI,MAAM,OAAO;QAClE,IAAI,MAAM,MAAM,OAAO,EAAE,YAAY,aAAa,UAAU,MAAM,CAAC;QACnE,IAAI,IAAI,IAAI,IAAI,eAAe,aAAa,KAAK,MAAM,IAAI,IAAI;QAC/D,IAAI,UAAU,aAAa,IAAI,IAAI;QACnC,IAAI,IAAI,IAAI,IAAI,aAAa,OAAO,IAAI,QAAQ,GAAG,CAAC,aAAa,MAAM,IAAI,GAAG,IAAI;aAC7E,IAAI,IAAI,KAAK,EAAE,OAAO,IAAI,MAAM,GAAG,CAAC,UAAU,IAAI,CAAC;aACnD,OAAO,IAAI,QAAQ,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI;IACnD;IAEA,cAAc;QACZ,eAAe;IACjB;AACF", "ignoreList": [0], "debugId": null}}]}