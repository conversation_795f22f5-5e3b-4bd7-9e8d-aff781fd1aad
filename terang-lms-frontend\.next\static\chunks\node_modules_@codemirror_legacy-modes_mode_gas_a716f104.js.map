{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/gas.js"], "sourcesContent": ["function mkGas(arch) {\n  // If an architecture is specified, its initialization function may\n  // populate this array with custom parsing functions which will be\n  // tried in the event that the standard functions do not find a match.\n  var custom = [];\n\n  // The symbol used to start a line comment changes based on the target\n  // architecture.\n  // If no architecture is pased in \"parserConfig\" then only multiline\n  // comments will have syntax support.\n  var lineCommentStartSymbol = \"\";\n\n  // These directives are architecture independent.\n  // Machine specific directives should go in their respective\n  // architecture initialization function.\n  // Reference:\n  // http://sourceware.org/binutils/docs/as/Pseudo-Ops.html#Pseudo-Ops\n  var directives = {\n    \".abort\" : \"builtin\",\n    \".align\" : \"builtin\",\n    \".altmacro\" : \"builtin\",\n    \".ascii\" : \"builtin\",\n    \".asciz\" : \"builtin\",\n    \".balign\" : \"builtin\",\n    \".balignw\" : \"builtin\",\n    \".balignl\" : \"builtin\",\n    \".bundle_align_mode\" : \"builtin\",\n    \".bundle_lock\" : \"builtin\",\n    \".bundle_unlock\" : \"builtin\",\n    \".byte\" : \"builtin\",\n    \".cfi_startproc\" : \"builtin\",\n    \".comm\" : \"builtin\",\n    \".data\" : \"builtin\",\n    \".def\" : \"builtin\",\n    \".desc\" : \"builtin\",\n    \".dim\" : \"builtin\",\n    \".double\" : \"builtin\",\n    \".eject\" : \"builtin\",\n    \".else\" : \"builtin\",\n    \".elseif\" : \"builtin\",\n    \".end\" : \"builtin\",\n    \".endef\" : \"builtin\",\n    \".endfunc\" : \"builtin\",\n    \".endif\" : \"builtin\",\n    \".equ\" : \"builtin\",\n    \".equiv\" : \"builtin\",\n    \".eqv\" : \"builtin\",\n    \".err\" : \"builtin\",\n    \".error\" : \"builtin\",\n    \".exitm\" : \"builtin\",\n    \".extern\" : \"builtin\",\n    \".fail\" : \"builtin\",\n    \".file\" : \"builtin\",\n    \".fill\" : \"builtin\",\n    \".float\" : \"builtin\",\n    \".func\" : \"builtin\",\n    \".global\" : \"builtin\",\n    \".gnu_attribute\" : \"builtin\",\n    \".hidden\" : \"builtin\",\n    \".hword\" : \"builtin\",\n    \".ident\" : \"builtin\",\n    \".if\" : \"builtin\",\n    \".incbin\" : \"builtin\",\n    \".include\" : \"builtin\",\n    \".int\" : \"builtin\",\n    \".internal\" : \"builtin\",\n    \".irp\" : \"builtin\",\n    \".irpc\" : \"builtin\",\n    \".lcomm\" : \"builtin\",\n    \".lflags\" : \"builtin\",\n    \".line\" : \"builtin\",\n    \".linkonce\" : \"builtin\",\n    \".list\" : \"builtin\",\n    \".ln\" : \"builtin\",\n    \".loc\" : \"builtin\",\n    \".loc_mark_labels\" : \"builtin\",\n    \".local\" : \"builtin\",\n    \".long\" : \"builtin\",\n    \".macro\" : \"builtin\",\n    \".mri\" : \"builtin\",\n    \".noaltmacro\" : \"builtin\",\n    \".nolist\" : \"builtin\",\n    \".octa\" : \"builtin\",\n    \".offset\" : \"builtin\",\n    \".org\" : \"builtin\",\n    \".p2align\" : \"builtin\",\n    \".popsection\" : \"builtin\",\n    \".previous\" : \"builtin\",\n    \".print\" : \"builtin\",\n    \".protected\" : \"builtin\",\n    \".psize\" : \"builtin\",\n    \".purgem\" : \"builtin\",\n    \".pushsection\" : \"builtin\",\n    \".quad\" : \"builtin\",\n    \".reloc\" : \"builtin\",\n    \".rept\" : \"builtin\",\n    \".sbttl\" : \"builtin\",\n    \".scl\" : \"builtin\",\n    \".section\" : \"builtin\",\n    \".set\" : \"builtin\",\n    \".short\" : \"builtin\",\n    \".single\" : \"builtin\",\n    \".size\" : \"builtin\",\n    \".skip\" : \"builtin\",\n    \".sleb128\" : \"builtin\",\n    \".space\" : \"builtin\",\n    \".stab\" : \"builtin\",\n    \".string\" : \"builtin\",\n    \".struct\" : \"builtin\",\n    \".subsection\" : \"builtin\",\n    \".symver\" : \"builtin\",\n    \".tag\" : \"builtin\",\n    \".text\" : \"builtin\",\n    \".title\" : \"builtin\",\n    \".type\" : \"builtin\",\n    \".uleb128\" : \"builtin\",\n    \".val\" : \"builtin\",\n    \".version\" : \"builtin\",\n    \".vtable_entry\" : \"builtin\",\n    \".vtable_inherit\" : \"builtin\",\n    \".warning\" : \"builtin\",\n    \".weak\" : \"builtin\",\n    \".weakref\" : \"builtin\",\n    \".word\" : \"builtin\"\n  };\n\n  var registers = {};\n\n  function x86() {\n    lineCommentStartSymbol = \"#\";\n\n    registers.al  = \"variable\";\n    registers.ah  = \"variable\";\n    registers.ax  = \"variable\";\n    registers.eax = \"variableName.special\";\n    registers.rax = \"variableName.special\";\n\n    registers.bl  = \"variable\";\n    registers.bh  = \"variable\";\n    registers.bx  = \"variable\";\n    registers.ebx = \"variableName.special\";\n    registers.rbx = \"variableName.special\";\n\n    registers.cl  = \"variable\";\n    registers.ch  = \"variable\";\n    registers.cx  = \"variable\";\n    registers.ecx = \"variableName.special\";\n    registers.rcx = \"variableName.special\";\n\n    registers.dl  = \"variable\";\n    registers.dh  = \"variable\";\n    registers.dx  = \"variable\";\n    registers.edx = \"variableName.special\";\n    registers.rdx = \"variableName.special\";\n\n    registers.si  = \"variable\";\n    registers.esi = \"variableName.special\";\n    registers.rsi = \"variableName.special\";\n\n    registers.di  = \"variable\";\n    registers.edi = \"variableName.special\";\n    registers.rdi = \"variableName.special\";\n\n    registers.sp  = \"variable\";\n    registers.esp = \"variableName.special\";\n    registers.rsp = \"variableName.special\";\n\n    registers.bp  = \"variable\";\n    registers.ebp = \"variableName.special\";\n    registers.rbp = \"variableName.special\";\n\n    registers.ip  = \"variable\";\n    registers.eip = \"variableName.special\";\n    registers.rip = \"variableName.special\";\n\n    registers.cs  = \"keyword\";\n    registers.ds  = \"keyword\";\n    registers.ss  = \"keyword\";\n    registers.es  = \"keyword\";\n    registers.fs  = \"keyword\";\n    registers.gs  = \"keyword\";\n  }\n\n  function armv6() {\n    // Reference:\n    // http://infocenter.arm.com/help/topic/com.arm.doc.qrc0001l/QRC0001_UAL.pdf\n    // http://infocenter.arm.com/help/topic/com.arm.doc.ddi0301h/DDI0301H_arm1176jzfs_r0p7_trm.pdf\n    lineCommentStartSymbol = \"@\";\n    directives.syntax = \"builtin\";\n\n    registers.r0  = \"variable\";\n    registers.r1  = \"variable\";\n    registers.r2  = \"variable\";\n    registers.r3  = \"variable\";\n    registers.r4  = \"variable\";\n    registers.r5  = \"variable\";\n    registers.r6  = \"variable\";\n    registers.r7  = \"variable\";\n    registers.r8  = \"variable\";\n    registers.r9  = \"variable\";\n    registers.r10 = \"variable\";\n    registers.r11 = \"variable\";\n    registers.r12 = \"variable\";\n\n    registers.sp  = \"variableName.special\";\n    registers.lr  = \"variableName.special\";\n    registers.pc  = \"variableName.special\";\n    registers.r13 = registers.sp;\n    registers.r14 = registers.lr;\n    registers.r15 = registers.pc;\n\n    custom.push(function(ch, stream) {\n      if (ch === '#') {\n        stream.eatWhile(/\\w/);\n        return \"number\";\n      }\n    });\n  }\n\n  if (arch === \"x86\") {\n    x86();\n  } else if (arch === \"arm\" || arch === \"armv6\") {\n    armv6();\n  }\n\n  function nextUntilUnescaped(stream, end) {\n    var escaped = false, next;\n    while ((next = stream.next()) != null) {\n      if (next === end && !escaped) {\n        return false;\n      }\n      escaped = !escaped && next === \"\\\\\";\n    }\n    return escaped;\n  }\n\n  function clikeComment(stream, state) {\n    var maybeEnd = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch === \"/\" && maybeEnd) {\n        state.tokenize = null;\n        break;\n      }\n      maybeEnd = (ch === \"*\");\n    }\n    return \"comment\";\n  }\n\n  return {\n    name: \"gas\",\n    startState: function() {\n      return {\n        tokenize: null\n      };\n    },\n\n    token: function(stream, state) {\n      if (state.tokenize) {\n        return state.tokenize(stream, state);\n      }\n\n      if (stream.eatSpace()) {\n        return null;\n      }\n\n      var style, cur, ch = stream.next();\n\n      if (ch === \"/\") {\n        if (stream.eat(\"*\")) {\n          state.tokenize = clikeComment;\n          return clikeComment(stream, state);\n        }\n      }\n\n      if (ch === lineCommentStartSymbol) {\n        stream.skipToEnd();\n        return \"comment\";\n      }\n\n      if (ch === '\"') {\n        nextUntilUnescaped(stream, '\"');\n        return \"string\";\n      }\n\n      if (ch === '.') {\n        stream.eatWhile(/\\w/);\n        cur = stream.current().toLowerCase();\n        style = directives[cur];\n        return style || null;\n      }\n\n      if (ch === '=') {\n        stream.eatWhile(/\\w/);\n        return \"tag\";\n      }\n\n      if (ch === '{') {\n        return \"bracket\";\n      }\n\n      if (ch === '}') {\n        return \"bracket\";\n      }\n\n      if (/\\d/.test(ch)) {\n        if (ch === \"0\" && stream.eat(\"x\")) {\n          stream.eatWhile(/[0-9a-fA-F]/);\n          return \"number\";\n        }\n        stream.eatWhile(/\\d/);\n        return \"number\";\n      }\n\n      if (/\\w/.test(ch)) {\n        stream.eatWhile(/\\w/);\n        if (stream.eat(\":\")) {\n          return 'tag';\n        }\n        cur = stream.current().toLowerCase();\n        style = registers[cur];\n        return style || null;\n      }\n\n      for (var i = 0; i < custom.length; i++) {\n        style = custom[i](ch, stream, state);\n        if (style) {\n          return style;\n        }\n      }\n    },\n\n    languageData: {\n      commentTokens: {\n        line: lineCommentStartSymbol,\n        block: {open: \"/*\", close: \"*/\"}\n      }\n    }\n  };\n};\n\nexport const gas = mkGas(\"x86\")\nexport const gasArm = mkGas(\"arm\")\n"], "names": [], "mappings": ";;;;AAAA,SAAS,MAAM,IAAI;IACjB,mEAAmE;IACnE,kEAAkE;IAClE,sEAAsE;IACtE,IAAI,SAAS,EAAE;IAEf,sEAAsE;IACtE,gBAAgB;IAChB,oEAAoE;IACpE,qCAAqC;IACrC,IAAI,yBAAyB;IAE7B,iDAAiD;IACjD,4DAA4D;IAC5D,wCAAwC;IACxC,aAAa;IACb,oEAAoE;IACpE,IAAI,aAAa;QACf,UAAW;QACX,UAAW;QACX,aAAc;QACd,UAAW;QACX,UAAW;QACX,WAAY;QACZ,YAAa;QACb,YAAa;QACb,sBAAuB;QACvB,gBAAiB;QACjB,kBAAmB;QACnB,SAAU;QACV,kBAAmB;QACnB,SAAU;QACV,SAAU;QACV,QAAS;QACT,SAAU;QACV,QAAS;QACT,WAAY;QACZ,UAAW;QACX,SAAU;QACV,WAAY;QACZ,QAAS;QACT,UAAW;QACX,YAAa;QACb,UAAW;QACX,QAAS;QACT,UAAW;QACX,QAAS;QACT,QAAS;QACT,UAAW;QACX,UAAW;QACX,WAAY;QACZ,SAAU;QACV,SAAU;QACV,SAAU;QACV,UAAW;QACX,SAAU;QACV,WAAY;QACZ,kBAAmB;QACnB,WAAY;QACZ,UAAW;QACX,UAAW;QACX,OAAQ;QACR,WAAY;QACZ,YAAa;QACb,QAAS;QACT,aAAc;QACd,QAAS;QACT,SAAU;QACV,UAAW;QACX,WAAY;QACZ,SAAU;QACV,aAAc;QACd,SAAU;QACV,OAAQ;QACR,QAAS;QACT,oBAAqB;QACrB,UAAW;QACX,SAAU;QACV,UAAW;QACX,QAAS;QACT,eAAgB;QAChB,WAAY;QACZ,SAAU;QACV,WAAY;QACZ,QAAS;QACT,YAAa;QACb,eAAgB;QAChB,aAAc;QACd,UAAW;QACX,cAAe;QACf,UAAW;QACX,WAAY;QACZ,gBAAiB;QACjB,SAAU;QACV,UAAW;QACX,SAAU;QACV,UAAW;QACX,QAAS;QACT,YAAa;QACb,QAAS;QACT,UAAW;QACX,WAAY;QACZ,SAAU;QACV,SAAU;QACV,YAAa;QACb,UAAW;QACX,SAAU;QACV,WAAY;QACZ,WAAY;QACZ,eAAgB;QAChB,WAAY;QACZ,QAAS;QACT,SAAU;QACV,UAAW;QACX,SAAU;QACV,YAAa;QACb,QAAS;QACT,YAAa;QACb,iBAAkB;QAClB,mBAAoB;QACpB,YAAa;QACb,SAAU;QACV,YAAa;QACb,SAAU;IACZ;IAEA,IAAI,YAAY,CAAC;IAEjB,SAAS;QACP,yBAAyB;QAEzB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,GAAG,GAAG;QAChB,UAAU,GAAG,GAAG;QAEhB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,GAAG,GAAG;QAChB,UAAU,GAAG,GAAG;QAEhB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,GAAG,GAAG;QAChB,UAAU,GAAG,GAAG;QAEhB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,GAAG,GAAG;QAChB,UAAU,GAAG,GAAG;QAEhB,UAAU,EAAE,GAAI;QAChB,UAAU,GAAG,GAAG;QAChB,UAAU,GAAG,GAAG;QAEhB,UAAU,EAAE,GAAI;QAChB,UAAU,GAAG,GAAG;QAChB,UAAU,GAAG,GAAG;QAEhB,UAAU,EAAE,GAAI;QAChB,UAAU,GAAG,GAAG;QAChB,UAAU,GAAG,GAAG;QAEhB,UAAU,EAAE,GAAI;QAChB,UAAU,GAAG,GAAG;QAChB,UAAU,GAAG,GAAG;QAEhB,UAAU,EAAE,GAAI;QAChB,UAAU,GAAG,GAAG;QAChB,UAAU,GAAG,GAAG;QAEhB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;IAClB;IAEA,SAAS;QACP,aAAa;QACb,4EAA4E;QAC5E,8FAA8F;QAC9F,yBAAyB;QACzB,WAAW,MAAM,GAAG;QAEpB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,GAAG,GAAG;QAChB,UAAU,GAAG,GAAG;QAChB,UAAU,GAAG,GAAG;QAEhB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,EAAE,GAAI;QAChB,UAAU,GAAG,GAAG,UAAU,EAAE;QAC5B,UAAU,GAAG,GAAG,UAAU,EAAE;QAC5B,UAAU,GAAG,GAAG,UAAU,EAAE;QAE5B,OAAO,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM;YAC7B,IAAI,OAAO,KAAK;gBACd,OAAO,QAAQ,CAAC;gBAChB,OAAO;YACT;QACF;IACF;IAEA,IAAI,SAAS,OAAO;QAClB;IACF,OAAO,IAAI,SAAS,SAAS,SAAS,SAAS;QAC7C;IACF;IAEA,SAAS,mBAAmB,MAAM,EAAE,GAAG;QACrC,IAAI,UAAU,OAAO;QACrB,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,SAAS,OAAO,CAAC,SAAS;gBAC5B,OAAO;YACT;YACA,UAAU,CAAC,WAAW,SAAS;QACjC;QACA,OAAO;IACT;IAEA,SAAS,aAAa,MAAM,EAAE,KAAK;QACjC,IAAI,WAAW,OAAO;QACtB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;YACnC,IAAI,OAAO,OAAO,UAAU;gBAC1B,MAAM,QAAQ,GAAG;gBACjB;YACF;YACA,WAAY,OAAO;QACrB;QACA,OAAO;IACT;IAEA,OAAO;QACL,MAAM;QACN,YAAY;YACV,OAAO;gBACL,UAAU;YACZ;QACF;QAEA,OAAO,SAAS,MAAM,EAAE,KAAK;YAC3B,IAAI,MAAM,QAAQ,EAAE;gBAClB,OAAO,MAAM,QAAQ,CAAC,QAAQ;YAChC;YAEA,IAAI,OAAO,QAAQ,IAAI;gBACrB,OAAO;YACT;YAEA,IAAI,OAAO,KAAK,KAAK,OAAO,IAAI;YAEhC,IAAI,OAAO,KAAK;gBACd,IAAI,OAAO,GAAG,CAAC,MAAM;oBACnB,MAAM,QAAQ,GAAG;oBACjB,OAAO,aAAa,QAAQ;gBAC9B;YACF;YAEA,IAAI,OAAO,wBAAwB;gBACjC,OAAO,SAAS;gBAChB,OAAO;YACT;YAEA,IAAI,OAAO,KAAK;gBACd,mBAAmB,QAAQ;gBAC3B,OAAO;YACT;YAEA,IAAI,OAAO,KAAK;gBACd,OAAO,QAAQ,CAAC;gBAChB,MAAM,OAAO,OAAO,GAAG,WAAW;gBAClC,QAAQ,UAAU,CAAC,IAAI;gBACvB,OAAO,SAAS;YAClB;YAEA,IAAI,OAAO,KAAK;gBACd,OAAO,QAAQ,CAAC;gBAChB,OAAO;YACT;YAEA,IAAI,OAAO,KAAK;gBACd,OAAO;YACT;YAEA,IAAI,OAAO,KAAK;gBACd,OAAO;YACT;YAEA,IAAI,KAAK,IAAI,CAAC,KAAK;gBACjB,IAAI,OAAO,OAAO,OAAO,GAAG,CAAC,MAAM;oBACjC,OAAO,QAAQ,CAAC;oBAChB,OAAO;gBACT;gBACA,OAAO,QAAQ,CAAC;gBAChB,OAAO;YACT;YAEA,IAAI,KAAK,IAAI,CAAC,KAAK;gBACjB,OAAO,QAAQ,CAAC;gBAChB,IAAI,OAAO,GAAG,CAAC,MAAM;oBACnB,OAAO;gBACT;gBACA,MAAM,OAAO,OAAO,GAAG,WAAW;gBAClC,QAAQ,SAAS,CAAC,IAAI;gBACtB,OAAO,SAAS;YAClB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,QAAQ,MAAM,CAAC,EAAE,CAAC,IAAI,QAAQ;gBAC9B,IAAI,OAAO;oBACT,OAAO;gBACT;YACF;QACF;QAEA,cAAc;YACZ,eAAe;gBACb,MAAM;gBACN,OAAO;oBAAC,MAAM;oBAAM,OAAO;gBAAI;YACjC;QACF;IACF;AACF;;AAEO,MAAM,MAAM,MAAM;AAClB,MAAM,SAAS,MAAM", "ignoreList": [0], "debugId": null}}]}