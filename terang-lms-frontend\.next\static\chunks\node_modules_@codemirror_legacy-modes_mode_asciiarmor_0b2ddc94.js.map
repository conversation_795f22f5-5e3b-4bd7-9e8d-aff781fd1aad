{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/asciiarmor.js"], "sourcesContent": ["function errorIfNotEmpty(stream) {\n  var nonWS = stream.match(/^\\s*\\S/);\n  stream.skipToEnd();\n  return nonWS ? \"error\" : null;\n}\n\nexport const asciiArmor = {\n  name: \"asciiarmor\",\n  token: function(stream, state) {\n    var m;\n    if (state.state == \"top\") {\n      if (stream.sol() && (m = stream.match(/^-----BEGIN (.*)?-----\\s*$/))) {\n        state.state = \"headers\";\n        state.type = m[1];\n        return \"tag\";\n      }\n      return errorIfNotEmpty(stream);\n    } else if (state.state == \"headers\") {\n      if (stream.sol() && stream.match(/^\\w+:/)) {\n        state.state = \"header\";\n        return \"atom\";\n      } else {\n        var result = errorIfNotEmpty(stream);\n        if (result) state.state = \"body\";\n        return result;\n      }\n    } else if (state.state == \"header\") {\n      stream.skipToEnd();\n      state.state = \"headers\";\n      return \"string\";\n    } else if (state.state == \"body\") {\n      if (stream.sol() && (m = stream.match(/^-----END (.*)?-----\\s*$/))) {\n        if (m[1] != state.type) return \"error\";\n        state.state = \"end\";\n        return \"tag\";\n      } else {\n        if (stream.eatWhile(/[A-Za-z0-9+\\/=]/)) {\n          return null;\n        } else {\n          stream.next();\n          return \"error\";\n        }\n      }\n    } else if (state.state == \"end\") {\n      return errorIfNotEmpty(stream);\n    }\n  },\n  blankLine: function(state) {\n    if (state.state == \"headers\") state.state = \"body\";\n  },\n  startState: function() {\n    return {state: \"top\", type: null};\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,MAAM;IAC7B,IAAI,QAAQ,OAAO,KAAK,CAAC;IACzB,OAAO,SAAS;IAChB,OAAO,QAAQ,UAAU;AAC3B;AAEO,MAAM,aAAa;IACxB,MAAM;IACN,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI;QACJ,IAAI,MAAM,KAAK,IAAI,OAAO;YACxB,IAAI,OAAO,GAAG,MAAM,CAAC,IAAI,OAAO,KAAK,CAAC,6BAA6B,GAAG;gBACpE,MAAM,KAAK,GAAG;gBACd,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;gBACjB,OAAO;YACT;YACA,OAAO,gBAAgB;QACzB,OAAO,IAAI,MAAM,KAAK,IAAI,WAAW;YACnC,IAAI,OAAO,GAAG,MAAM,OAAO,KAAK,CAAC,UAAU;gBACzC,MAAM,KAAK,GAAG;gBACd,OAAO;YACT,OAAO;gBACL,IAAI,SAAS,gBAAgB;gBAC7B,IAAI,QAAQ,MAAM,KAAK,GAAG;gBAC1B,OAAO;YACT;QACF,OAAO,IAAI,MAAM,KAAK,IAAI,UAAU;YAClC,OAAO,SAAS;YAChB,MAAM,KAAK,GAAG;YACd,OAAO;QACT,OAAO,IAAI,MAAM,KAAK,IAAI,QAAQ;YAChC,IAAI,OAAO,GAAG,MAAM,CAAC,IAAI,OAAO,KAAK,CAAC,2BAA2B,GAAG;gBAClE,IAAI,CAAC,CAAC,EAAE,IAAI,MAAM,IAAI,EAAE,OAAO;gBAC/B,MAAM,KAAK,GAAG;gBACd,OAAO;YACT,OAAO;gBACL,IAAI,OAAO,QAAQ,CAAC,oBAAoB;oBACtC,OAAO;gBACT,OAAO;oBACL,OAAO,IAAI;oBACX,OAAO;gBACT;YACF;QACF,OAAO,IAAI,MAAM,KAAK,IAAI,OAAO;YAC/B,OAAO,gBAAgB;QACzB;IACF;IACA,WAAW,SAAS,KAAK;QACvB,IAAI,MAAM,KAAK,IAAI,WAAW,MAAM,KAAK,GAAG;IAC9C;IACA,YAAY;QACV,OAAO;YAAC,OAAO;YAAO,MAAM;QAAI;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}