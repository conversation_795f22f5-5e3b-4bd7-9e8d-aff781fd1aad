{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/spreadsheet.js"], "sourcesContent": ["export const spreadsheet = {\n  name: \"spreadsheet\",\n\n  startState: function () {\n    return {\n      stringType: null,\n      stack: []\n    };\n  },\n  token: function (stream, state) {\n    if (!stream) return;\n\n    //check for state changes\n    if (state.stack.length === 0) {\n      //strings\n      if ((stream.peek() == '\"') || (stream.peek() == \"'\")) {\n        state.stringType = stream.peek();\n        stream.next(); // Skip quote\n        state.stack.unshift(\"string\");\n      }\n    }\n\n    //return state\n    //stack has\n    switch (state.stack[0]) {\n    case \"string\":\n      while (state.stack[0] === \"string\" && !stream.eol()) {\n        if (stream.peek() === state.stringType) {\n          stream.next(); // Skip quote\n          state.stack.shift(); // Clear flag\n        } else if (stream.peek() === \"\\\\\") {\n          stream.next();\n          stream.next();\n        } else {\n          stream.match(/^.[^\\\\\\\"\\']*/);\n        }\n      }\n      return \"string\";\n\n    case \"characterClass\":\n      while (state.stack[0] === \"characterClass\" && !stream.eol()) {\n        if (!(stream.match(/^[^\\]\\\\]+/) || stream.match(/^\\\\./)))\n          state.stack.shift();\n      }\n      return \"operator\";\n    }\n\n    var peek = stream.peek();\n\n    //no stack\n    switch (peek) {\n    case \"[\":\n      stream.next();\n      state.stack.unshift(\"characterClass\");\n      return \"bracket\";\n    case \":\":\n      stream.next();\n      return \"operator\";\n    case \"\\\\\":\n      if (stream.match(/\\\\[a-z]+/)) return \"string.special\";\n      else {\n        stream.next();\n        return \"atom\";\n      }\n    case \".\":\n    case \",\":\n    case \";\":\n    case \"*\":\n    case \"-\":\n    case \"+\":\n    case \"^\":\n    case \"<\":\n    case \"/\":\n    case \"=\":\n      stream.next();\n      return \"atom\";\n    case \"$\":\n      stream.next();\n      return \"builtin\";\n    }\n\n    if (stream.match(/\\d+/)) {\n      if (stream.match(/^\\w+/)) return \"error\";\n      return \"number\";\n    } else if (stream.match(/^[a-zA-Z_]\\w*/)) {\n      if (stream.match(/(?=[\\(.])/, false)) return \"keyword\";\n      return \"variable\";\n    } else if ([\"[\", \"]\", \"(\", \")\", \"{\", \"}\"].indexOf(peek) != -1) {\n      stream.next();\n      return \"bracket\";\n    } else if (!stream.eatSpace()) {\n      stream.next();\n    }\n    return null;\n  }\n};\n"], "names": [], "mappings": ";;;AAAO,MAAM,cAAc;IACzB,MAAM;IAEN,YAAY;QACV,OAAO;YACL,YAAY;YACZ,OAAO,EAAE;QACX;IACF;IACA,OAAO,SAAU,MAAM,EAAE,KAAK;QAC5B,IAAI,CAAC,QAAQ;QAEb,yBAAyB;QACzB,IAAI,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG;YAC5B,SAAS;YACT,IAAI,AAAC,OAAO,IAAI,MAAM,OAAS,OAAO,IAAI,MAAM,KAAM;gBACpD,MAAM,UAAU,GAAG,OAAO,IAAI;gBAC9B,OAAO,IAAI,IAAI,aAAa;gBAC5B,MAAM,KAAK,CAAC,OAAO,CAAC;YACtB;QACF;QAEA,cAAc;QACd,WAAW;QACX,OAAQ,MAAM,KAAK,CAAC,EAAE;YACtB,KAAK;gBACH,MAAO,MAAM,KAAK,CAAC,EAAE,KAAK,YAAY,CAAC,OAAO,GAAG,GAAI;oBACnD,IAAI,OAAO,IAAI,OAAO,MAAM,UAAU,EAAE;wBACtC,OAAO,IAAI,IAAI,aAAa;wBAC5B,MAAM,KAAK,CAAC,KAAK,IAAI,aAAa;oBACpC,OAAO,IAAI,OAAO,IAAI,OAAO,MAAM;wBACjC,OAAO,IAAI;wBACX,OAAO,IAAI;oBACb,OAAO;wBACL,OAAO,KAAK,CAAC;oBACf;gBACF;gBACA,OAAO;YAET,KAAK;gBACH,MAAO,MAAM,KAAK,CAAC,EAAE,KAAK,oBAAoB,CAAC,OAAO,GAAG,GAAI;oBAC3D,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC,OAAO,GACrD,MAAM,KAAK,CAAC,KAAK;gBACrB;gBACA,OAAO;QACT;QAEA,IAAI,OAAO,OAAO,IAAI;QAEtB,UAAU;QACV,OAAQ;YACR,KAAK;gBACH,OAAO,IAAI;gBACX,MAAM,KAAK,CAAC,OAAO,CAAC;gBACpB,OAAO;YACT,KAAK;gBACH,OAAO,IAAI;gBACX,OAAO;YACT,KAAK;gBACH,IAAI,OAAO,KAAK,CAAC,aAAa,OAAO;qBAChC;oBACH,OAAO,IAAI;oBACX,OAAO;gBACT;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,IAAI;gBACX,OAAO;YACT,KAAK;gBACH,OAAO,IAAI;gBACX,OAAO;QACT;QAEA,IAAI,OAAO,KAAK,CAAC,QAAQ;YACvB,IAAI,OAAO,KAAK,CAAC,SAAS,OAAO;YACjC,OAAO;QACT,OAAO,IAAI,OAAO,KAAK,CAAC,kBAAkB;YACxC,IAAI,OAAO,KAAK,CAAC,aAAa,QAAQ,OAAO;YAC7C,OAAO;QACT,OAAO,IAAI;YAAC;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG;YAC7D,OAAO,IAAI;YACX,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,QAAQ,IAAI;YAC7B,OAAO,IAAI;QACb;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}