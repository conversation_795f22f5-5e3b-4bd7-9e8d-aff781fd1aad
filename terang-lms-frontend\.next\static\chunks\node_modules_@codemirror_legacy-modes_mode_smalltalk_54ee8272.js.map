{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/smalltalk.js"], "sourcesContent": ["var specialChars = /[+\\-\\/\\\\*~<>=@%|&?!.,:;^]/;\nvar keywords = /true|false|nil|self|super|thisContext/;\n\nvar Context = function(tokenizer, parent) {\n  this.next = tokenizer;\n  this.parent = parent;\n};\n\nvar Token = function(name, context, eos) {\n  this.name = name;\n  this.context = context;\n  this.eos = eos;\n};\n\nvar State = function() {\n  this.context = new Context(next, null);\n  this.expectVariable = true;\n  this.indentation = 0;\n  this.userIndentationDelta = 0;\n};\n\nState.prototype.userIndent = function(indentation, indentUnit) {\n  this.userIndentationDelta = indentation > 0 ? (indentation / indentUnit - this.indentation) : 0;\n};\n\nvar next = function(stream, context, state) {\n  var token = new Token(null, context, false);\n  var aChar = stream.next();\n\n  if (aChar === '\"') {\n    token = nextComment(stream, new Context(nextComment, context));\n\n  } else if (aChar === '\\'') {\n    token = nextString(stream, new Context(nextString, context));\n\n  } else if (aChar === '#') {\n    if (stream.peek() === '\\'') {\n      stream.next();\n      token = nextSymbol(stream, new Context(nextSymbol, context));\n    } else {\n      if (stream.eatWhile(/[^\\s.{}\\[\\]()]/))\n        token.name = 'string.special';\n      else\n        token.name = 'meta';\n    }\n\n  } else if (aChar === '$') {\n    if (stream.next() === '<') {\n      stream.eatWhile(/[^\\s>]/);\n      stream.next();\n    }\n    token.name = 'string.special';\n\n  } else if (aChar === '|' && state.expectVariable) {\n    token.context = new Context(nextTemporaries, context);\n\n  } else if (/[\\[\\]{}()]/.test(aChar)) {\n    token.name = 'bracket';\n    token.eos = /[\\[{(]/.test(aChar);\n\n    if (aChar === '[') {\n      state.indentation++;\n    } else if (aChar === ']') {\n      state.indentation = Math.max(0, state.indentation - 1);\n    }\n\n  } else if (specialChars.test(aChar)) {\n    stream.eatWhile(specialChars);\n    token.name = 'operator';\n    token.eos = aChar !== ';'; // ; cascaded message expression\n\n  } else if (/\\d/.test(aChar)) {\n    stream.eatWhile(/[\\w\\d]/);\n    token.name = 'number';\n\n  } else if (/[\\w_]/.test(aChar)) {\n    stream.eatWhile(/[\\w\\d_]/);\n    token.name = state.expectVariable ? (keywords.test(stream.current()) ? 'keyword' : 'variable') : null;\n\n  } else {\n    token.eos = state.expectVariable;\n  }\n\n  return token;\n};\n\nvar nextComment = function(stream, context) {\n  stream.eatWhile(/[^\"]/);\n  return new Token('comment', stream.eat('\"') ? context.parent : context, true);\n};\n\nvar nextString = function(stream, context) {\n  stream.eatWhile(/[^']/);\n  return new Token('string', stream.eat('\\'') ? context.parent : context, false);\n};\n\nvar nextSymbol = function(stream, context) {\n  stream.eatWhile(/[^']/);\n  return new Token('string.special', stream.eat('\\'') ? context.parent : context, false);\n};\n\nvar nextTemporaries = function(stream, context) {\n  var token = new Token(null, context, false);\n  var aChar = stream.next();\n\n  if (aChar === '|') {\n    token.context = context.parent;\n    token.eos = true;\n\n  } else {\n    stream.eatWhile(/[^|]/);\n    token.name = 'variable';\n  }\n\n  return token;\n};\n\nexport const smalltalk = {\n  name: \"smalltalk\",\n\n  startState: function() {\n    return new State;\n  },\n\n  token: function(stream, state) {\n    state.userIndent(stream.indentation(), stream.indentUnit);\n\n    if (stream.eatSpace()) {\n      return null;\n    }\n\n    var token = state.context.next(stream, state.context, state);\n    state.context = token.context;\n    state.expectVariable = token.eos;\n\n    return token.name;\n  },\n\n  blankLine: function(state, indentUnit) {\n    state.userIndent(0, indentUnit);\n  },\n\n  indent: function(state, textAfter, cx) {\n    var i = state.context.next === next && textAfter && textAfter.charAt(0) === ']' ? -1 : state.userIndentationDelta;\n    return (state.indentation + i) * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*\\]$/\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,eAAe;AACnB,IAAI,WAAW;AAEf,IAAI,UAAU,SAAS,SAAS,EAAE,MAAM;IACtC,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,MAAM,GAAG;AAChB;AAEA,IAAI,QAAQ,SAAS,IAAI,EAAE,OAAO,EAAE,GAAG;IACrC,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,GAAG,GAAG;AACb;AAEA,IAAI,QAAQ;IACV,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,MAAM;IACjC,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI,CAAC,oBAAoB,GAAG;AAC9B;AAEA,MAAM,SAAS,CAAC,UAAU,GAAG,SAAS,WAAW,EAAE,UAAU;IAC3D,IAAI,CAAC,oBAAoB,GAAG,cAAc,IAAK,cAAc,aAAa,IAAI,CAAC,WAAW,GAAI;AAChG;AAEA,IAAI,OAAO,SAAS,MAAM,EAAE,OAAO,EAAE,KAAK;IACxC,IAAI,QAAQ,IAAI,MAAM,MAAM,SAAS;IACrC,IAAI,QAAQ,OAAO,IAAI;IAEvB,IAAI,UAAU,KAAK;QACjB,QAAQ,YAAY,QAAQ,IAAI,QAAQ,aAAa;IAEvD,OAAO,IAAI,UAAU,MAAM;QACzB,QAAQ,WAAW,QAAQ,IAAI,QAAQ,YAAY;IAErD,OAAO,IAAI,UAAU,KAAK;QACxB,IAAI,OAAO,IAAI,OAAO,MAAM;YAC1B,OAAO,IAAI;YACX,QAAQ,WAAW,QAAQ,IAAI,QAAQ,YAAY;QACrD,OAAO;YACL,IAAI,OAAO,QAAQ,CAAC,mBAClB,MAAM,IAAI,GAAG;iBAEb,MAAM,IAAI,GAAG;QACjB;IAEF,OAAO,IAAI,UAAU,KAAK;QACxB,IAAI,OAAO,IAAI,OAAO,KAAK;YACzB,OAAO,QAAQ,CAAC;YAChB,OAAO,IAAI;QACb;QACA,MAAM,IAAI,GAAG;IAEf,OAAO,IAAI,UAAU,OAAO,MAAM,cAAc,EAAE;QAChD,MAAM,OAAO,GAAG,IAAI,QAAQ,iBAAiB;IAE/C,OAAO,IAAI,aAAa,IAAI,CAAC,QAAQ;QACnC,MAAM,IAAI,GAAG;QACb,MAAM,GAAG,GAAG,SAAS,IAAI,CAAC;QAE1B,IAAI,UAAU,KAAK;YACjB,MAAM,WAAW;QACnB,OAAO,IAAI,UAAU,KAAK;YACxB,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;QACtD;IAEF,OAAO,IAAI,aAAa,IAAI,CAAC,QAAQ;QACnC,OAAO,QAAQ,CAAC;QAChB,MAAM,IAAI,GAAG;QACb,MAAM,GAAG,GAAG,UAAU,KAAK,gCAAgC;IAE7D,OAAO,IAAI,KAAK,IAAI,CAAC,QAAQ;QAC3B,OAAO,QAAQ,CAAC;QAChB,MAAM,IAAI,GAAG;IAEf,OAAO,IAAI,QAAQ,IAAI,CAAC,QAAQ;QAC9B,OAAO,QAAQ,CAAC;QAChB,MAAM,IAAI,GAAG,MAAM,cAAc,GAAI,SAAS,IAAI,CAAC,OAAO,OAAO,MAAM,YAAY,aAAc;IAEnG,OAAO;QACL,MAAM,GAAG,GAAG,MAAM,cAAc;IAClC;IAEA,OAAO;AACT;AAEA,IAAI,cAAc,SAAS,MAAM,EAAE,OAAO;IACxC,OAAO,QAAQ,CAAC;IAChB,OAAO,IAAI,MAAM,WAAW,OAAO,GAAG,CAAC,OAAO,QAAQ,MAAM,GAAG,SAAS;AAC1E;AAEA,IAAI,aAAa,SAAS,MAAM,EAAE,OAAO;IACvC,OAAO,QAAQ,CAAC;IAChB,OAAO,IAAI,MAAM,UAAU,OAAO,GAAG,CAAC,QAAQ,QAAQ,MAAM,GAAG,SAAS;AAC1E;AAEA,IAAI,aAAa,SAAS,MAAM,EAAE,OAAO;IACvC,OAAO,QAAQ,CAAC;IAChB,OAAO,IAAI,MAAM,kBAAkB,OAAO,GAAG,CAAC,QAAQ,QAAQ,MAAM,GAAG,SAAS;AAClF;AAEA,IAAI,kBAAkB,SAAS,MAAM,EAAE,OAAO;IAC5C,IAAI,QAAQ,IAAI,MAAM,MAAM,SAAS;IACrC,IAAI,QAAQ,OAAO,IAAI;IAEvB,IAAI,UAAU,KAAK;QACjB,MAAM,OAAO,GAAG,QAAQ,MAAM;QAC9B,MAAM,GAAG,GAAG;IAEd,OAAO;QACL,OAAO,QAAQ,CAAC;QAChB,MAAM,IAAI,GAAG;IACf;IAEA,OAAO;AACT;AAEO,MAAM,YAAY;IACvB,MAAM;IAEN,YAAY;QACV,OAAO,IAAI;IACb;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,MAAM,UAAU,CAAC,OAAO,WAAW,IAAI,OAAO,UAAU;QAExD,IAAI,OAAO,QAAQ,IAAI;YACrB,OAAO;QACT;QAEA,IAAI,QAAQ,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,MAAM,OAAO,EAAE;QACtD,MAAM,OAAO,GAAG,MAAM,OAAO;QAC7B,MAAM,cAAc,GAAG,MAAM,GAAG;QAEhC,OAAO,MAAM,IAAI;IACnB;IAEA,WAAW,SAAS,KAAK,EAAE,UAAU;QACnC,MAAM,UAAU,CAAC,GAAG;IACtB;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI,KAAK,QAAQ,aAAa,UAAU,MAAM,CAAC,OAAO,MAAM,CAAC,IAAI,MAAM,oBAAoB;QACjH,OAAO,CAAC,MAAM,WAAW,GAAG,CAAC,IAAI,GAAG,IAAI;IAC1C;IAEA,cAAc;QACZ,eAAe;IACjB;AACF", "ignoreList": [0], "debugId": null}}]}