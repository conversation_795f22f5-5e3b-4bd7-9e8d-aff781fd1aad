{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/simple-mode.js"], "sourcesContent": ["export function simpleMode(states) {\n  ensureState(states, \"start\");\n  var states_ = {}, meta = states.languageData || {}, hasIndentation = false;\n  for (var state in states) if (state != meta && states.hasOwnProperty(state)) {\n    var list = states_[state] = [], orig = states[state];\n    for (var i = 0; i < orig.length; i++) {\n      var data = orig[i];\n      list.push(new Rule(data, states));\n      if (data.indent || data.dedent) hasIndentation = true;\n    }\n  }\n  return {\n    name: meta.name,\n    startState: function() {\n      return {state: \"start\", pending: null, indent: hasIndentation ? [] : null};\n    },\n    copyState: function(state) {\n      var s = {state: state.state, pending: state.pending, indent: state.indent && state.indent.slice(0)};\n      if (state.stack)\n        s.stack = state.stack.slice(0);\n      return s;\n    },\n    token: tokenFunction(states_),\n    indent: indentFunction(states_, meta),\n    mergeTokens: meta.mergeTokens,\n    languageData: meta\n  }\n};\n\nfunction ensureState(states, name) {\n  if (!states.hasOwnProperty(name))\n    throw new Error(\"Undefined state \" + name + \" in simple mode\");\n}\n\nfunction toRegex(val, caret) {\n  if (!val) return /(?:)/;\n  var flags = \"\";\n  if (val instanceof RegExp) {\n    if (val.ignoreCase) flags = \"i\";\n    val = val.source;\n  } else {\n    val = String(val);\n  }\n  return new RegExp((caret === false ? \"\" : \"^\") + \"(?:\" + val + \")\", flags);\n}\n\nfunction asToken(val) {\n  if (!val) return null;\n  if (val.apply) return val\n  if (typeof val == \"string\") return val.replace(/\\./g, \" \");\n  var result = [];\n  for (var i = 0; i < val.length; i++)\n    result.push(val[i] && val[i].replace(/\\./g, \" \"));\n  return result;\n}\n\nfunction Rule(data, states) {\n  if (data.next || data.push) ensureState(states, data.next || data.push);\n  this.regex = toRegex(data.regex);\n  this.token = asToken(data.token);\n  this.data = data;\n}\n\nfunction tokenFunction(states) {\n  return function(stream, state) {\n    if (state.pending) {\n      var pend = state.pending.shift();\n      if (state.pending.length == 0) state.pending = null;\n      stream.pos += pend.text.length;\n      return pend.token;\n    }\n\n    var curState = states[state.state];\n    for (var i = 0; i < curState.length; i++) {\n      var rule = curState[i];\n      var matches = (!rule.data.sol || stream.sol()) && stream.match(rule.regex);\n      if (matches) {\n        if (rule.data.next) {\n          state.state = rule.data.next;\n        } else if (rule.data.push) {\n          (state.stack || (state.stack = [])).push(state.state);\n          state.state = rule.data.push;\n        } else if (rule.data.pop && state.stack && state.stack.length) {\n          state.state = state.stack.pop();\n        }\n\n        if (rule.data.indent)\n          state.indent.push(stream.indentation() + stream.indentUnit);\n        if (rule.data.dedent)\n          state.indent.pop();\n        var token = rule.token\n        if (token && token.apply) token = token(matches)\n        if (matches.length > 2 && rule.token && typeof rule.token != \"string\") {\n          state.pending = [];\n          for (var j = 2; j < matches.length; j++)\n            if (matches[j])\n              state.pending.push({text: matches[j], token: rule.token[j - 1]});\n          stream.backUp(matches[0].length - (matches[1] ? matches[1].length : 0));\n          return token[0];\n        } else if (token && token.join) {\n          return token[0];\n        } else {\n          return token;\n        }\n      }\n    }\n    stream.next();\n    return null;\n  };\n}\n\nfunction indentFunction(states, meta) {\n  return function(state, textAfter) {\n    if (state.indent == null || meta.dontIndentStates && meta.dontIndentStates.indexOf(state.state) > -1)\n      return null\n\n    var pos = state.indent.length - 1, rules = states[state.state];\n    scan: for (;;) {\n      for (var i = 0; i < rules.length; i++) {\n        var rule = rules[i];\n        if (rule.data.dedent && rule.data.dedentIfLineStart !== false) {\n          var m = rule.regex.exec(textAfter);\n          if (m && m[0]) {\n            pos--;\n            if (rule.next || rule.push) rules = states[rule.next || rule.push];\n            textAfter = textAfter.slice(m[0].length);\n            continue scan;\n          }\n        }\n      }\n      break;\n    }\n    return pos < 0 ? 0 : state.indent[pos];\n  };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,WAAW,MAAM;IAC/B,YAAY,QAAQ;IACpB,IAAI,UAAU,CAAC,GAAG,OAAO,OAAO,YAAY,IAAI,CAAC,GAAG,iBAAiB;IACrE,IAAK,IAAI,SAAS,OAAQ,IAAI,SAAS,QAAQ,OAAO,cAAc,CAAC,QAAQ;QAC3E,IAAI,OAAO,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,OAAO,MAAM,CAAC,MAAM;QACpD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM;YACzB,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,EAAE,iBAAiB;QACnD;IACF;IACA,OAAO;QACL,MAAM,KAAK,IAAI;QACf,YAAY;YACV,OAAO;gBAAC,OAAO;gBAAS,SAAS;gBAAM,QAAQ,iBAAiB,EAAE,GAAG;YAAI;QAC3E;QACA,WAAW,SAAS,KAAK;YACvB,IAAI,IAAI;gBAAC,OAAO,MAAM,KAAK;gBAAE,SAAS,MAAM,OAAO;gBAAE,QAAQ,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,KAAK,CAAC;YAAE;YAClG,IAAI,MAAM,KAAK,EACb,EAAE,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC;YAC9B,OAAO;QACT;QACA,OAAO,cAAc;QACrB,QAAQ,eAAe,SAAS;QAChC,aAAa,KAAK,WAAW;QAC7B,cAAc;IAChB;AACF;;AAEA,SAAS,YAAY,MAAM,EAAE,IAAI;IAC/B,IAAI,CAAC,OAAO,cAAc,CAAC,OACzB,MAAM,IAAI,MAAM,qBAAqB,OAAO;AAChD;AAEA,SAAS,QAAQ,GAAG,EAAE,KAAK;IACzB,IAAI,CAAC,KAAK,OAAO;IACjB,IAAI,QAAQ;IACZ,IAAI,eAAe,QAAQ;QACzB,IAAI,IAAI,UAAU,EAAE,QAAQ;QAC5B,MAAM,IAAI,MAAM;IAClB,OAAO;QACL,MAAM,OAAO;IACf;IACA,OAAO,IAAI,OAAO,CAAC,UAAU,QAAQ,KAAK,GAAG,IAAI,QAAQ,MAAM,KAAK;AACtE;AAEA,SAAS,QAAQ,GAAG;IAClB,IAAI,CAAC,KAAK,OAAO;IACjB,IAAI,IAAI,KAAK,EAAE,OAAO;IACtB,IAAI,OAAO,OAAO,UAAU,OAAO,IAAI,OAAO,CAAC,OAAO;IACtD,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAC9B,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;IAC9C,OAAO;AACT;AAEA,SAAS,KAAK,IAAI,EAAE,MAAM;IACxB,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,EAAE,YAAY,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI;IACtE,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK;IAC/B,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK;IAC/B,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,SAAS,cAAc,MAAM;IAC3B,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,MAAM,OAAO,EAAE;YACjB,IAAI,OAAO,MAAM,OAAO,CAAC,KAAK;YAC9B,IAAI,MAAM,OAAO,CAAC,MAAM,IAAI,GAAG,MAAM,OAAO,GAAG;YAC/C,OAAO,GAAG,IAAI,KAAK,IAAI,CAAC,MAAM;YAC9B,OAAO,KAAK,KAAK;QACnB;QAEA,IAAI,WAAW,MAAM,CAAC,MAAM,KAAK,CAAC;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACxC,IAAI,OAAO,QAAQ,CAAC,EAAE;YACtB,IAAI,UAAU,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,EAAE,KAAK,OAAO,KAAK,CAAC,KAAK,KAAK;YACzE,IAAI,SAAS;gBACX,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;oBAClB,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,IAAI;gBAC9B,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;oBACzB,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,KAAK;oBACpD,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,IAAI;gBAC9B,OAAO,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE;oBAC7D,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,GAAG;gBAC/B;gBAEA,IAAI,KAAK,IAAI,CAAC,MAAM,EAClB,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO,WAAW,KAAK,OAAO,UAAU;gBAC5D,IAAI,KAAK,IAAI,CAAC,MAAM,EAClB,MAAM,MAAM,CAAC,GAAG;gBAClB,IAAI,QAAQ,KAAK,KAAK;gBACtB,IAAI,SAAS,MAAM,KAAK,EAAE,QAAQ,MAAM;gBACxC,IAAI,QAAQ,MAAM,GAAG,KAAK,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,IAAI,UAAU;oBACrE,MAAM,OAAO,GAAG,EAAE;oBAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAClC,IAAI,OAAO,CAAC,EAAE,EACZ,MAAM,OAAO,CAAC,IAAI,CAAC;wBAAC,MAAM,OAAO,CAAC,EAAE;wBAAE,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;oBAAA;oBAClE,OAAO,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC;oBACrE,OAAO,KAAK,CAAC,EAAE;gBACjB,OAAO,IAAI,SAAS,MAAM,IAAI,EAAE;oBAC9B,OAAO,KAAK,CAAC,EAAE;gBACjB,OAAO;oBACL,OAAO;gBACT;YACF;QACF;QACA,OAAO,IAAI;QACX,OAAO;IACT;AACF;AAEA,SAAS,eAAe,MAAM,EAAE,IAAI;IAClC,OAAO,SAAS,KAAK,EAAE,SAAS;QAC9B,IAAI,MAAM,MAAM,IAAI,QAAQ,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,GACjG,OAAO;QAET,IAAI,MAAM,MAAM,MAAM,CAAC,MAAM,GAAG,GAAG,QAAQ,MAAM,CAAC,MAAM,KAAK,CAAC;QAC9D,MAAM,OAAS;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,IAAI,OAAO,KAAK,CAAC,EAAE;gBACnB,IAAI,KAAK,IAAI,CAAC,MAAM,IAAI,KAAK,IAAI,CAAC,iBAAiB,KAAK,OAAO;oBAC7D,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC;oBACxB,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE;wBACb;wBACA,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,EAAE,QAAQ,MAAM,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;wBAClE,YAAY,UAAU,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM;wBACvC,SAAS;oBACX;gBACF;YACF;YACA;QACF;QACA,OAAO,MAAM,IAAI,IAAI,MAAM,MAAM,CAAC,IAAI;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/nsis.js"], "sourcesContent": ["import {simpleMode} from \"./simple-mode.js\"\nexport const nsis = simpleMode({\n  start:[\n    // Numbers\n    {regex: /(?:[+-]?)(?:0x[\\d,a-f]+)|(?:0o[0-7]+)|(?:0b[0,1]+)|(?:\\d+.?\\d*)/, token: \"number\"},\n\n    // Strings\n    { regex: /\"(?:[^\\\\\"]|\\\\.)*\"?/, token: \"string\" },\n    { regex: /'(?:[^\\\\']|\\\\.)*'?/, token: \"string\" },\n    { regex: /`(?:[^\\\\`]|\\\\.)*`?/, token: \"string\" },\n\n    // Compile Time Commands\n    {regex: /^\\s*(?:\\!(addincludedir|addplugindir|appendfile|assert|cd|define|delfile|echo|error|execute|finalize|getdllversion|gettlbversion|include|insertmacro|macro|macroend|makensis|packhdr|pragma|searchparse|searchreplace|system|tempfile|undef|uninstfinalize|verbose|warning))\\b/i, token: \"keyword\"},\n\n    // Conditional Compilation\n    {regex: /^\\s*(?:\\!(if(?:n?def)?|ifmacron?def|macro))\\b/i, token: \"keyword\", indent: true},\n    {regex: /^\\s*(?:\\!(else|endif|macroend))\\b/i, token: \"keyword\", dedent: true},\n\n    // Runtime Commands\n    {regex: /^\\s*(?:Abort|AddBrandingImage|AddSize|AllowRootDirInstall|AllowSkipFiles|AutoCloseWindow|BGFont|BGGradient|BrandingText|BringToFront|Call|CallInstDLL|Caption|ChangeUI|CheckBitmap|ClearErrors|CompletedText|ComponentText|CopyFiles|CRCCheck|CreateDirectory|CreateFont|CreateShortCut|Delete|DeleteINISec|DeleteINIStr|DeleteRegKey|DeleteRegValue|DetailPrint|DetailsButtonText|DirText|DirVar|DirVerify|EnableWindow|EnumRegKey|EnumRegValue|Exch|Exec|ExecShell|ExecShellWait|ExecWait|ExpandEnvStrings|File|FileBufSize|FileClose|FileErrorText|FileOpen|FileRead|FileReadByte|FileReadUTF16LE|FileReadWord|FileWriteUTF16LE|FileSeek|FileWrite|FileWriteByte|FileWriteWord|FindClose|FindFirst|FindNext|FindWindow|FlushINI|GetCurInstType|GetCurrentAddress|GetDlgItem|GetDLLVersion|GetDLLVersionLocal|GetErrorLevel|GetFileTime|GetFileTimeLocal|GetFullPathName|GetFunctionAddress|GetInstDirError|GetKnownFolderPath|GetLabelAddress|GetTempFileName|GetWinVer|Goto|HideWindow|Icon|IfAbort|IfErrors|IfFileExists|IfRebootFlag|IfRtlLanguage|IfShellVarContextAll|IfSilent|InitPluginsDir|InstallButtonText|InstallColors|InstallDir|InstallDirRegKey|InstProgressFlags|InstType|InstTypeGetText|InstTypeSetText|Int64Cmp|Int64CmpU|Int64Fmt|IntCmp|IntCmpU|IntFmt|IntOp|IntPtrCmp|IntPtrCmpU|IntPtrOp|IsWindow|LangString|LicenseBkColor|LicenseData|LicenseForceSelection|LicenseLangString|LicenseText|LoadAndSetImage|LoadLanguageFile|LockWindow|LogSet|LogText|ManifestDPIAware|ManifestLongPathAware|ManifestMaxVersionTested|ManifestSupportedOS|MessageBox|MiscButtonText|Name|Nop|OutFile|Page|PageCallbacks|PEAddResource|PEDllCharacteristics|PERemoveResource|PESubsysVer|Pop|Push|Quit|ReadEnvStr|ReadINIStr|ReadRegDWORD|ReadRegStr|Reboot|RegDLL|Rename|RequestExecutionLevel|ReserveFile|Return|RMDir|SearchPath|SectionGetFlags|SectionGetInstTypes|SectionGetSize|SectionGetText|SectionIn|SectionSetFlags|SectionSetInstTypes|SectionSetSize|SectionSetText|SendMessage|SetAutoClose|SetBrandingImage|SetCompress|SetCompressor|SetCompressorDictSize|SetCtlColors|SetCurInstType|SetDatablockOptimize|SetDateSave|SetDetailsPrint|SetDetailsView|SetErrorLevel|SetErrors|SetFileAttributes|SetFont|SetOutPath|SetOverwrite|SetRebootFlag|SetRegView|SetShellVarContext|SetSilent|ShowInstDetails|ShowUninstDetails|ShowWindow|SilentInstall|SilentUnInstall|Sleep|SpaceTexts|StrCmp|StrCmpS|StrCpy|StrLen|SubCaption|Target|Unicode|UninstallButtonText|UninstallCaption|UninstallIcon|UninstallSubCaption|UninstallText|UninstPage|UnRegDLL|Var|VIAddVersionKey|VIFileVersion|VIProductVersion|WindowIcon|WriteINIStr|WriteRegBin|WriteRegDWORD|WriteRegExpandStr|WriteRegMultiStr|WriteRegNone|WriteRegStr|WriteUninstaller|XPStyle)\\b/i, token: \"keyword\"},\n    {regex: /^\\s*(?:Function|PageEx|Section(?:Group)?)\\b/i, token: \"keyword\", indent: true},\n    {regex: /^\\s*(?:(Function|PageEx|Section(?:Group)?)End)\\b/i, token: \"keyword\", dedent: true},\n\n    // Command Options\n    {regex: /\\b(?:ARCHIVE|FILE_ATTRIBUTE_ARCHIVE|FILE_ATTRIBUTE_HIDDEN|FILE_ATTRIBUTE_NORMAL|FILE_ATTRIBUTE_OFFLINE|FILE_ATTRIBUTE_READONLY|FILE_ATTRIBUTE_SYSTEM|FILE_ATTRIBUTE_TEMPORARY|HIDDEN|HKCC|HKCR(32|64)?|HKCU(32|64)?|HKDD|HKEY_CLASSES_ROOT|HKEY_CURRENT_CONFIG|HKEY_CURRENT_USER|HKEY_DYN_DATA|HKEY_LOCAL_MACHINE|HKEY_PERFORMANCE_DATA|HKEY_USERS|HKLM(32|64)?|HKPD|HKU|IDABORT|IDCANCEL|IDD_DIR|IDD_INST|IDD_INSTFILES|IDD_LICENSE|IDD_SELCOM|IDD_UNINST|IDD_VERIFY|IDIGNORE|IDNO|IDOK|IDRETRY|IDYES|MB_ABORTRETRYIGNORE|MB_DEFBUTTON1|MB_DEFBUTTON2|MB_DEFBUTTON3|MB_DEFBUTTON4|MB_ICONEXCLAMATION|MB_ICONINFORMATION|MB_ICONQUESTION|MB_ICONSTOP|MB_OK|MB_OKCANCEL|MB_RETRYCANCEL|MB_RIGHT|MB_RTLREADING|MB_SETFOREGROUND|MB_TOPMOST|MB_USERICON|MB_YESNO|MB_YESNOCANCEL|NORMAL|OFFLINE|READONLY|SHCTX|SHELL_CONTEXT|SW_HIDE|SW_SHOWDEFAULT|SW_SHOWMAXIMIZED|SW_SHOWMINIMIZED|SW_SHOWNORMAL|SYSTEM|TEMPORARY)\\b/i, token: \"atom\"},\n    {regex: /\\b(?:admin|all|amd64-unicode|auto|both|bottom|bzip2|components|current|custom|directory|false|force|hide|highest|ifdiff|ifnewer|instfiles|lastused|leave|left|license|listonly|lzma|nevershow|none|normal|notset|off|on|right|show|silent|silentlog|textonly|top|true|try|un\\.components|un\\.custom|un\\.directory|un\\.instfiles|un\\.license|uninstConfirm|user|Win10|Win7|Win8|WinVista|x-86-(ansi|unicode)|zlib)\\b/i, token: \"builtin\"},\n\n    // LogicLib.nsh\n    {regex: /\\$\\{(?:And(?:If(?:Not)?|Unless)|Break|Case(?:2|3|4|5|Else)?|Continue|Default|Do(?:Until|While)?|Else(?:If(?:Not)?|Unless)?|End(?:If|Select|Switch)|Exit(?:Do|For|While)|For(?:Each)?|If(?:Cmd|Not(?:Then)?|Then)?|Loop(?:Until|While)?|Or(?:If(?:Not)?|Unless)|Select|Switch|Unless|While)\\}/i, token: \"variable-2\", indent: true},\n\n    // FileFunc.nsh\n    {regex: /\\$\\{(?:BannerTrimPath|DirState|DriveSpace|Get(BaseName|Drives|ExeName|ExePath|FileAttributes|FileExt|FileName|FileVersion|Options|OptionsS|Parameters|Parent|Root|Size|Time)|Locate|RefreshShellIcons)\\}/i, token: \"variable-2\", dedent: true},\n\n    // Memento.nsh\n    {regex: /\\$\\{(?:Memento(?:Section(?:Done|End|Restore|Save)?|UnselectedSection))\\}/i, token: \"variable-2\", dedent: true},\n\n    // TextFunc.nsh\n    {regex: /\\$\\{(?:Config(?:Read|ReadS|Write|WriteS)|File(?:Join|ReadFromEnd|Recode)|Line(?:Find|Read|Sum)|Text(?:Compare|CompareS)|TrimNewLines)\\}/i, token: \"variable-2\", dedent: true},\n\n    // WinVer.nsh\n    {regex: /\\$\\{(?:(?:At(?:Least|Most)|Is)(?:ServicePack|Win(?:7|8|10|95|98|200(?:0|3|8(?:R2)?)|ME|NT4|Vista|XP))|Is(?:NT|Server))\\}/i, token: \"variable\", dedent: true},\n\n    // WordFunc.nsh\n    {regex: /\\$\\{(?:StrFilterS?|Version(?:Compare|Convert)|Word(?:AddS?|Find(?:(?:2|3)X)?S?|InsertS?|ReplaceS?))\\}/i, token: \"keyword\", dedent: true},\n\n    // x64.nsh\n    {regex: /\\$\\{(?:RunningX64)\\}/i, token: \"variable\", dedent: true},\n    {regex: /\\$\\{(?:Disable|Enable)X64FSRedirection\\}/i, token: \"keyword\", dedent: true},\n\n    // Line Comment\n    {regex: /(#|;).*/, token: \"comment\"},\n\n    // Block Comment\n    {regex: /\\/\\*/, token: \"comment\", next: \"comment\"},\n\n    // Operator\n    {regex: /[-+\\/*=<>!]+/, token: \"operator\"},\n\n    // Variable\n    {regex: /\\$\\w[\\w\\.]*/, token: \"variable\"},\n\n    // Constant\n    {regex: /\\${[\\!\\w\\.:-]+}/, token: \"variableName.constant\"},\n\n    // Language String\n    {regex: /\\$\\([\\!\\w\\.:-]+\\)/, token: \"atom\"}\n  ],\n  comment: [\n    {regex: /.*?\\*\\//, token: \"comment\", next: \"start\"},\n    {regex: /.*/, token: \"comment\"}\n  ],\n  languageData: {\n    name: \"nsis\",\n    indentOnInput: /^\\s*((Function|PageEx|Section|Section(Group)?)End|(\\!(endif|macroend))|\\$\\{(End(If|Unless|While)|Loop(Until)|Next)\\})$/i,\n    commentTokens: {line: \"#\", block: {open: \"/*\", close: \"*/\"}}\n  }\n});\n\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAE;IAC7B,OAAM;QACJ,UAAU;QACV;YAAC,OAAO;YAAmE,OAAO;QAAQ;QAE1F,UAAU;QACV;YAAE,OAAO;YAAsB,OAAO;QAAS;QAC/C;YAAE,OAAO;YAAsB,OAAO;QAAS;QAC/C;YAAE,OAAO;YAAsB,OAAO;QAAS;QAE/C,wBAAwB;QACxB;YAAC,OAAO;YAAmR,OAAO;QAAS;QAE3S,0BAA0B;QAC1B;YAAC,OAAO;YAAkD,OAAO;YAAW,QAAQ;QAAI;QACxF;YAAC,OAAO;YAAsC,OAAO;YAAW,QAAQ;QAAI;QAE5E,mBAAmB;QACnB;YAAC,OAAO;YAA8mF,OAAO;QAAS;QACtoF;YAAC,OAAO;YAAgD,OAAO;YAAW,QAAQ;QAAI;QACtF;YAAC,OAAO;YAAqD,OAAO;YAAW,QAAQ;QAAI;QAE3F,kBAAkB;QAClB;YAAC,OAAO;YAAw3B,OAAO;QAAM;QAC74B;YAAC,OAAO;YAAwZ,OAAO;QAAS;QAEhb,eAAe;QACf;YAAC,OAAO;YAAiS,OAAO;YAAc,QAAQ;QAAI;QAE1U,eAAe;QACf;YAAC,OAAO;YAA6M,OAAO;YAAc,QAAQ;QAAI;QAEtP,cAAc;QACd;YAAC,OAAO;YAA6E,OAAO;YAAc,QAAQ;QAAI;QAEtH,eAAe;QACf;YAAC,OAAO;YAA4I,OAAO;YAAc,QAAQ;QAAI;QAErL,aAAa;QACb;YAAC,OAAO;YAA6H,OAAO;YAAY,QAAQ;QAAI;QAEpK,eAAe;QACf;YAAC,OAAO;YAA0G,OAAO;YAAW,QAAQ;QAAI;QAEhJ,UAAU;QACV;YAAC,OAAO;YAAyB,OAAO;YAAY,QAAQ;QAAI;QAChE;YAAC,OAAO;YAA6C,OAAO;YAAW,QAAQ;QAAI;QAEnF,eAAe;QACf;YAAC,OAAO;YAAW,OAAO;QAAS;QAEnC,gBAAgB;QAChB;YAAC,OAAO;YAAQ,OAAO;YAAW,MAAM;QAAS;QAEjD,WAAW;QACX;YAAC,OAAO;YAAgB,OAAO;QAAU;QAEzC,WAAW;QACX;YAAC,OAAO;YAAe,OAAO;QAAU;QAExC,WAAW;QACX;YAAC,OAAO;YAAmB,OAAO;QAAuB;QAEzD,kBAAkB;QAClB;YAAC,OAAO;YAAqB,OAAO;QAAM;KAC3C;IACD,SAAS;QACP;YAAC,OAAO;YAAW,OAAO;YAAW,MAAM;QAAO;QAClD;YAAC,OAAO;YAAM,OAAO;QAAS;KAC/B;IACD,cAAc;QACZ,MAAM;QACN,eAAe;QACf,eAAe;YAAC,MAAM;YAAK,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;IAC7D;AACF", "ignoreList": [0], "debugId": null}}]}