{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/jinja2.js"], "sourcesContent": ["var keywords = [\"and\", \"as\", \"block\", \"endblock\", \"by\", \"cycle\", \"debug\", \"else\", \"elif\",\n                \"extends\", \"filter\", \"endfilter\", \"firstof\", \"do\", \"for\",\n                \"endfor\", \"if\", \"endif\", \"ifchanged\", \"endifchanged\",\n                \"ifequal\", \"endifequal\", \"ifnotequal\", \"set\", \"raw\", \"endraw\",\n                \"endifnotequal\", \"in\", \"include\", \"load\", \"not\", \"now\", \"or\",\n                \"parsed\", \"regroup\", \"reversed\", \"spaceless\", \"call\", \"endcall\", \"macro\",\n                \"endmacro\", \"endspaceless\", \"ssi\", \"templatetag\", \"openblock\",\n                \"closeblock\", \"openvariable\", \"closevariable\", \"without\", \"context\",\n                \"openbrace\", \"closebrace\", \"opencomment\",\n                \"closecomment\", \"widthratio\", \"url\", \"with\", \"endwith\",\n                \"get_current_language\", \"trans\", \"endtrans\", \"noop\", \"blocktrans\",\n                \"endblocktrans\", \"get_available_languages\",\n                \"get_current_language_bidi\", \"pluralize\", \"autoescape\", \"endautoescape\"],\n    operator = /^[+\\-*&%=<>!?|~^]/,\n    sign = /^[:\\[\\(\\{]/,\n    atom = [\"true\", \"false\"],\n    number = /^(\\d[+\\-\\*\\/])?\\d+(\\.\\d+)?/;\n\nkeywords = new RegExp(\"((\" + keywords.join(\")|(\") + \"))\\\\b\");\natom = new RegExp(\"((\" + atom.join(\")|(\") + \"))\\\\b\");\n\nfunction tokenBase (stream, state) {\n  var ch = stream.peek();\n\n  //Comment\n  if (state.incomment) {\n    if(!stream.skipTo(\"#}\")) {\n      stream.skipToEnd();\n    } else {\n      stream.eatWhile(/\\#|}/);\n      state.incomment = false;\n    }\n    return \"comment\";\n    //Tag\n  } else if (state.intag) {\n    //After operator\n    if(state.operator) {\n      state.operator = false;\n      if(stream.match(atom)) {\n        return \"atom\";\n      }\n      if(stream.match(number)) {\n        return \"number\";\n      }\n    }\n    //After sign\n    if(state.sign) {\n      state.sign = false;\n      if(stream.match(atom)) {\n        return \"atom\";\n      }\n      if(stream.match(number)) {\n        return \"number\";\n      }\n    }\n\n    if(state.instring) {\n      if(ch == state.instring) {\n        state.instring = false;\n      }\n      stream.next();\n      return \"string\";\n    } else if(ch == \"'\" || ch == '\"') {\n      state.instring = ch;\n      stream.next();\n      return \"string\";\n    } else if (state.inbraces > 0 && ch ==\")\") {\n      stream.next()\n      state.inbraces--;\n    }\n    else if (ch == \"(\") {\n      stream.next()\n      state.inbraces++;\n    }\n    else if (state.inbrackets > 0 && ch ==\"]\") {\n      stream.next()\n      state.inbrackets--;\n    }\n    else if (ch == \"[\") {\n      stream.next()\n      state.inbrackets++;\n    } else if (!state.lineTag && (stream.match(state.intag + \"}\") || stream.eat(\"-\") && stream.match(state.intag + \"}\"))) {\n      state.intag = false;\n      return \"tag\";\n    } else if(stream.match(operator)) {\n      state.operator = true;\n      return \"operator\";\n    } else if(stream.match(sign)) {\n      state.sign = true;\n    } else {\n      if (stream.column() == 1 && state.lineTag && stream.match(keywords)) {\n        //allow nospace after tag before the keyword\n        return \"keyword\";\n      }\n      if(stream.eat(\" \") || stream.sol()) {\n        if(stream.match(keywords)) {\n          return \"keyword\";\n        }\n        if(stream.match(atom)) {\n          return \"atom\";\n        }\n        if(stream.match(number)) {\n          return \"number\";\n        }\n        if(stream.sol()) {\n          stream.next();\n        }\n      } else {\n        stream.next();\n      }\n\n    }\n    return \"variable\";\n  } else if (stream.eat(\"{\")) {\n    if (stream.eat(\"#\")) {\n      state.incomment = true;\n      if(!stream.skipTo(\"#}\")) {\n        stream.skipToEnd();\n      } else {\n        stream.eatWhile(/\\#|}/);\n        state.incomment = false;\n      }\n      return \"comment\";\n      //Open tag\n    } else if (ch = stream.eat(/\\{|%/)) {\n      //Cache close tag\n      state.intag = ch;\n      state.inbraces = 0;\n      state.inbrackets = 0;\n      if(ch == \"{\") {\n        state.intag = \"}\";\n      }\n      stream.eat(\"-\");\n      return \"tag\";\n    }\n    //Line statements\n  } else if (stream.eat('#')) {\n    if (stream.peek() == '#') {\n      stream.skipToEnd();\n      return \"comment\"\n    }\n    else if (!stream.eol()) {\n      state.intag = true;\n      state.lineTag = true;\n      state.inbraces = 0;\n      state.inbrackets = 0;\n      return \"tag\";\n    }\n  }\n  stream.next();\n};\n\nexport const jinja2 = {\n  name: \"jinja2\",\n  startState: function () {\n    return {tokenize: tokenBase, inbrackets: 0, inbraces: 0};\n  },\n  token: function(stream, state) {\n    var style = state.tokenize(stream, state);\n    if (stream.eol() && state.lineTag && !state.instring && state.inbraces == 0 && state.inbrackets == 0) {\n      //Close line statement at the EOL\n      state.intag = false\n      state.lineTag = false\n    }\n    return style;\n  },\n  languageData: {\n    commentTokens: {block: {open: \"{#\", close: \"#}\", line: \"##\"}}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,WAAW;IAAC;IAAO;IAAM;IAAS;IAAY;IAAM;IAAS;IAAS;IAAQ;IAClE;IAAW;IAAU;IAAa;IAAW;IAAM;IACnD;IAAU;IAAM;IAAS;IAAa;IACtC;IAAW;IAAc;IAAc;IAAO;IAAO;IACrD;IAAiB;IAAM;IAAW;IAAQ;IAAO;IAAO;IACxD;IAAU;IAAW;IAAY;IAAa;IAAQ;IAAW;IACjE;IAAY;IAAgB;IAAO;IAAe;IAClD;IAAc;IAAgB;IAAiB;IAAW;IAC1D;IAAa;IAAc;IAC3B;IAAgB;IAAc;IAAO;IAAQ;IAC7C;IAAwB;IAAS;IAAY;IAAQ;IACrD;IAAiB;IACjB;IAA6B;IAAa;IAAc;CAAgB,EACpF,WAAW,qBACX,OAAO,cACP,OAAO;IAAC;IAAQ;CAAQ,EACxB,SAAS;AAEb,WAAW,IAAI,OAAO,OAAO,SAAS,IAAI,CAAC,SAAS;AACpD,OAAO,IAAI,OAAO,OAAO,KAAK,IAAI,CAAC,SAAS;AAE5C,SAAS,UAAW,MAAM,EAAE,KAAK;IAC/B,IAAI,KAAK,OAAO,IAAI;IAEpB,SAAS;IACT,IAAI,MAAM,SAAS,EAAE;QACnB,IAAG,CAAC,OAAO,MAAM,CAAC,OAAO;YACvB,OAAO,SAAS;QAClB,OAAO;YACL,OAAO,QAAQ,CAAC;YAChB,MAAM,SAAS,GAAG;QACpB;QACA,OAAO;IACP,KAAK;IACP,OAAO,IAAI,MAAM,KAAK,EAAE;QACtB,gBAAgB;QAChB,IAAG,MAAM,QAAQ,EAAE;YACjB,MAAM,QAAQ,GAAG;YACjB,IAAG,OAAO,KAAK,CAAC,OAAO;gBACrB,OAAO;YACT;YACA,IAAG,OAAO,KAAK,CAAC,SAAS;gBACvB,OAAO;YACT;QACF;QACA,YAAY;QACZ,IAAG,MAAM,IAAI,EAAE;YACb,MAAM,IAAI,GAAG;YACb,IAAG,OAAO,KAAK,CAAC,OAAO;gBACrB,OAAO;YACT;YACA,IAAG,OAAO,KAAK,CAAC,SAAS;gBACvB,OAAO;YACT;QACF;QAEA,IAAG,MAAM,QAAQ,EAAE;YACjB,IAAG,MAAM,MAAM,QAAQ,EAAE;gBACvB,MAAM,QAAQ,GAAG;YACnB;YACA,OAAO,IAAI;YACX,OAAO;QACT,OAAO,IAAG,MAAM,OAAO,MAAM,KAAK;YAChC,MAAM,QAAQ,GAAG;YACjB,OAAO,IAAI;YACX,OAAO;QACT,OAAO,IAAI,MAAM,QAAQ,GAAG,KAAK,MAAK,KAAK;YACzC,OAAO,IAAI;YACX,MAAM,QAAQ;QAChB,OACK,IAAI,MAAM,KAAK;YAClB,OAAO,IAAI;YACX,MAAM,QAAQ;QAChB,OACK,IAAI,MAAM,UAAU,GAAG,KAAK,MAAK,KAAK;YACzC,OAAO,IAAI;YACX,MAAM,UAAU;QAClB,OACK,IAAI,MAAM,KAAK;YAClB,OAAO,IAAI;YACX,MAAM,UAAU;QAClB,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,OAAO,KAAK,CAAC,MAAM,KAAK,GAAG,QAAQ,OAAO,GAAG,CAAC,QAAQ,OAAO,KAAK,CAAC,MAAM,KAAK,GAAG,IAAI,GAAG;YACpH,MAAM,KAAK,GAAG;YACd,OAAO;QACT,OAAO,IAAG,OAAO,KAAK,CAAC,WAAW;YAChC,MAAM,QAAQ,GAAG;YACjB,OAAO;QACT,OAAO,IAAG,OAAO,KAAK,CAAC,OAAO;YAC5B,MAAM,IAAI,GAAG;QACf,OAAO;YACL,IAAI,OAAO,MAAM,MAAM,KAAK,MAAM,OAAO,IAAI,OAAO,KAAK,CAAC,WAAW;gBACnE,4CAA4C;gBAC5C,OAAO;YACT;YACA,IAAG,OAAO,GAAG,CAAC,QAAQ,OAAO,GAAG,IAAI;gBAClC,IAAG,OAAO,KAAK,CAAC,WAAW;oBACzB,OAAO;gBACT;gBACA,IAAG,OAAO,KAAK,CAAC,OAAO;oBACrB,OAAO;gBACT;gBACA,IAAG,OAAO,KAAK,CAAC,SAAS;oBACvB,OAAO;gBACT;gBACA,IAAG,OAAO,GAAG,IAAI;oBACf,OAAO,IAAI;gBACb;YACF,OAAO;gBACL,OAAO,IAAI;YACb;QAEF;QACA,OAAO;IACT,OAAO,IAAI,OAAO,GAAG,CAAC,MAAM;QAC1B,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,MAAM,SAAS,GAAG;YAClB,IAAG,CAAC,OAAO,MAAM,CAAC,OAAO;gBACvB,OAAO,SAAS;YAClB,OAAO;gBACL,OAAO,QAAQ,CAAC;gBAChB,MAAM,SAAS,GAAG;YACpB;YACA,OAAO;QACP,UAAU;QACZ,OAAO,IAAI,KAAK,OAAO,GAAG,CAAC,SAAS;YAClC,iBAAiB;YACjB,MAAM,KAAK,GAAG;YACd,MAAM,QAAQ,GAAG;YACjB,MAAM,UAAU,GAAG;YACnB,IAAG,MAAM,KAAK;gBACZ,MAAM,KAAK,GAAG;YAChB;YACA,OAAO,GAAG,CAAC;YACX,OAAO;QACT;IACA,iBAAiB;IACnB,OAAO,IAAI,OAAO,GAAG,CAAC,MAAM;QAC1B,IAAI,OAAO,IAAI,MAAM,KAAK;YACxB,OAAO,SAAS;YAChB,OAAO;QACT,OACK,IAAI,CAAC,OAAO,GAAG,IAAI;YACtB,MAAM,KAAK,GAAG;YACd,MAAM,OAAO,GAAG;YAChB,MAAM,QAAQ,GAAG;YACjB,MAAM,UAAU,GAAG;YACnB,OAAO;QACT;IACF;IACA,OAAO,IAAI;AACb;;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,YAAY;QACV,OAAO;YAAC,UAAU;YAAW,YAAY;YAAG,UAAU;QAAC;IACzD;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QACnC,IAAI,OAAO,GAAG,MAAM,MAAM,OAAO,IAAI,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,IAAI,KAAK,MAAM,UAAU,IAAI,GAAG;YACpG,iCAAiC;YACjC,MAAM,KAAK,GAAG;YACd,MAAM,OAAO,GAAG;QAClB;QACA,OAAO;IACT;IACA,cAAc;QACZ,eAAe;YAAC,OAAO;gBAAC,MAAM;gBAAM,OAAO;gBAAM,MAAM;YAAI;QAAC;IAC9D;AACF", "ignoreList": [0], "debugId": null}}]}