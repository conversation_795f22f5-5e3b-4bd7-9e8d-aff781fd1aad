(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@codemirror/legacy-modes/mode/simple-mode.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "simpleMode": (()=>simpleMode)
});
function simpleMode(states) {
    ensureState(states, "start");
    var states_ = {}, meta = states.languageData || {}, hasIndentation = false;
    for(var state in states)if (state != meta && states.hasOwnProperty(state)) {
        var list = states_[state] = [], orig = states[state];
        for(var i = 0; i < orig.length; i++){
            var data = orig[i];
            list.push(new Rule(data, states));
            if (data.indent || data.dedent) hasIndentation = true;
        }
    }
    return {
        name: meta.name,
        startState: function() {
            return {
                state: "start",
                pending: null,
                indent: hasIndentation ? [] : null
            };
        },
        copyState: function(state) {
            var s = {
                state: state.state,
                pending: state.pending,
                indent: state.indent && state.indent.slice(0)
            };
            if (state.stack) s.stack = state.stack.slice(0);
            return s;
        },
        token: tokenFunction(states_),
        indent: indentFunction(states_, meta),
        mergeTokens: meta.mergeTokens,
        languageData: meta
    };
}
;
function ensureState(states, name) {
    if (!states.hasOwnProperty(name)) throw new Error("Undefined state " + name + " in simple mode");
}
function toRegex(val, caret) {
    if (!val) return /(?:)/;
    var flags = "";
    if (val instanceof RegExp) {
        if (val.ignoreCase) flags = "i";
        val = val.source;
    } else {
        val = String(val);
    }
    return new RegExp((caret === false ? "" : "^") + "(?:" + val + ")", flags);
}
function asToken(val) {
    if (!val) return null;
    if (val.apply) return val;
    if (typeof val == "string") return val.replace(/\./g, " ");
    var result = [];
    for(var i = 0; i < val.length; i++)result.push(val[i] && val[i].replace(/\./g, " "));
    return result;
}
function Rule(data, states) {
    if (data.next || data.push) ensureState(states, data.next || data.push);
    this.regex = toRegex(data.regex);
    this.token = asToken(data.token);
    this.data = data;
}
function tokenFunction(states) {
    return function(stream, state) {
        if (state.pending) {
            var pend = state.pending.shift();
            if (state.pending.length == 0) state.pending = null;
            stream.pos += pend.text.length;
            return pend.token;
        }
        var curState = states[state.state];
        for(var i = 0; i < curState.length; i++){
            var rule = curState[i];
            var matches = (!rule.data.sol || stream.sol()) && stream.match(rule.regex);
            if (matches) {
                if (rule.data.next) {
                    state.state = rule.data.next;
                } else if (rule.data.push) {
                    (state.stack || (state.stack = [])).push(state.state);
                    state.state = rule.data.push;
                } else if (rule.data.pop && state.stack && state.stack.length) {
                    state.state = state.stack.pop();
                }
                if (rule.data.indent) state.indent.push(stream.indentation() + stream.indentUnit);
                if (rule.data.dedent) state.indent.pop();
                var token = rule.token;
                if (token && token.apply) token = token(matches);
                if (matches.length > 2 && rule.token && typeof rule.token != "string") {
                    state.pending = [];
                    for(var j = 2; j < matches.length; j++)if (matches[j]) state.pending.push({
                        text: matches[j],
                        token: rule.token[j - 1]
                    });
                    stream.backUp(matches[0].length - (matches[1] ? matches[1].length : 0));
                    return token[0];
                } else if (token && token.join) {
                    return token[0];
                } else {
                    return token;
                }
            }
        }
        stream.next();
        return null;
    };
}
function indentFunction(states, meta) {
    return function(state, textAfter) {
        if (state.indent == null || meta.dontIndentStates && meta.dontIndentStates.indexOf(state.state) > -1) return null;
        var pos = state.indent.length - 1, rules = states[state.state];
        scan: for(;;){
            for(var i = 0; i < rules.length; i++){
                var rule = rules[i];
                if (rule.data.dedent && rule.data.dedentIfLineStart !== false) {
                    var m = rule.regex.exec(textAfter);
                    if (m && m[0]) {
                        pos--;
                        if (rule.next || rule.push) rules = states[rule.next || rule.push];
                        textAfter = textAfter.slice(m[0].length);
                        continue scan;
                    }
                }
            }
            break;
        }
        return pos < 0 ? 0 : state.indent[pos];
    };
}
}}),
"[project]/node_modules/@codemirror/legacy-modes/mode/nsis.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "nsis": (()=>nsis)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$legacy$2d$modes$2f$mode$2f$simple$2d$mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codemirror/legacy-modes/mode/simple-mode.js [app-client] (ecmascript)");
;
const nsis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$legacy$2d$modes$2f$mode$2f$simple$2d$mode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["simpleMode"])({
    start: [
        // Numbers
        {
            regex: /(?:[+-]?)(?:0x[\d,a-f]+)|(?:0o[0-7]+)|(?:0b[0,1]+)|(?:\d+.?\d*)/,
            token: "number"
        },
        // Strings
        {
            regex: /"(?:[^\\"]|\\.)*"?/,
            token: "string"
        },
        {
            regex: /'(?:[^\\']|\\.)*'?/,
            token: "string"
        },
        {
            regex: /`(?:[^\\`]|\\.)*`?/,
            token: "string"
        },
        // Compile Time Commands
        {
            regex: /^\s*(?:\!(addincludedir|addplugindir|appendfile|assert|cd|define|delfile|echo|error|execute|finalize|getdllversion|gettlbversion|include|insertmacro|macro|macroend|makensis|packhdr|pragma|searchparse|searchreplace|system|tempfile|undef|uninstfinalize|verbose|warning))\b/i,
            token: "keyword"
        },
        // Conditional Compilation
        {
            regex: /^\s*(?:\!(if(?:n?def)?|ifmacron?def|macro))\b/i,
            token: "keyword",
            indent: true
        },
        {
            regex: /^\s*(?:\!(else|endif|macroend))\b/i,
            token: "keyword",
            dedent: true
        },
        // Runtime Commands
        {
            regex: /^\s*(?:Abort|AddBrandingImage|AddSize|AllowRootDirInstall|AllowSkipFiles|AutoCloseWindow|BGFont|BGGradient|BrandingText|BringToFront|Call|CallInstDLL|Caption|ChangeUI|CheckBitmap|ClearErrors|CompletedText|ComponentText|CopyFiles|CRCCheck|CreateDirectory|CreateFont|CreateShortCut|Delete|DeleteINISec|DeleteINIStr|DeleteRegKey|DeleteRegValue|DetailPrint|DetailsButtonText|DirText|DirVar|DirVerify|EnableWindow|EnumRegKey|EnumRegValue|Exch|Exec|ExecShell|ExecShellWait|ExecWait|ExpandEnvStrings|File|FileBufSize|FileClose|FileErrorText|FileOpen|FileRead|FileReadByte|FileReadUTF16LE|FileReadWord|FileWriteUTF16LE|FileSeek|FileWrite|FileWriteByte|FileWriteWord|FindClose|FindFirst|FindNext|FindWindow|FlushINI|GetCurInstType|GetCurrentAddress|GetDlgItem|GetDLLVersion|GetDLLVersionLocal|GetErrorLevel|GetFileTime|GetFileTimeLocal|GetFullPathName|GetFunctionAddress|GetInstDirError|GetKnownFolderPath|GetLabelAddress|GetTempFileName|GetWinVer|Goto|HideWindow|Icon|IfAbort|IfErrors|IfFileExists|IfRebootFlag|IfRtlLanguage|IfShellVarContextAll|IfSilent|InitPluginsDir|InstallButtonText|InstallColors|InstallDir|InstallDirRegKey|InstProgressFlags|InstType|InstTypeGetText|InstTypeSetText|Int64Cmp|Int64CmpU|Int64Fmt|IntCmp|IntCmpU|IntFmt|IntOp|IntPtrCmp|IntPtrCmpU|IntPtrOp|IsWindow|LangString|LicenseBkColor|LicenseData|LicenseForceSelection|LicenseLangString|LicenseText|LoadAndSetImage|LoadLanguageFile|LockWindow|LogSet|LogText|ManifestDPIAware|ManifestLongPathAware|ManifestMaxVersionTested|ManifestSupportedOS|MessageBox|MiscButtonText|Name|Nop|OutFile|Page|PageCallbacks|PEAddResource|PEDllCharacteristics|PERemoveResource|PESubsysVer|Pop|Push|Quit|ReadEnvStr|ReadINIStr|ReadRegDWORD|ReadRegStr|Reboot|RegDLL|Rename|RequestExecutionLevel|ReserveFile|Return|RMDir|SearchPath|SectionGetFlags|SectionGetInstTypes|SectionGetSize|SectionGetText|SectionIn|SectionSetFlags|SectionSetInstTypes|SectionSetSize|SectionSetText|SendMessage|SetAutoClose|SetBrandingImage|SetCompress|SetCompressor|SetCompressorDictSize|SetCtlColors|SetCurInstType|SetDatablockOptimize|SetDateSave|SetDetailsPrint|SetDetailsView|SetErrorLevel|SetErrors|SetFileAttributes|SetFont|SetOutPath|SetOverwrite|SetRebootFlag|SetRegView|SetShellVarContext|SetSilent|ShowInstDetails|ShowUninstDetails|ShowWindow|SilentInstall|SilentUnInstall|Sleep|SpaceTexts|StrCmp|StrCmpS|StrCpy|StrLen|SubCaption|Target|Unicode|UninstallButtonText|UninstallCaption|UninstallIcon|UninstallSubCaption|UninstallText|UninstPage|UnRegDLL|Var|VIAddVersionKey|VIFileVersion|VIProductVersion|WindowIcon|WriteINIStr|WriteRegBin|WriteRegDWORD|WriteRegExpandStr|WriteRegMultiStr|WriteRegNone|WriteRegStr|WriteUninstaller|XPStyle)\b/i,
            token: "keyword"
        },
        {
            regex: /^\s*(?:Function|PageEx|Section(?:Group)?)\b/i,
            token: "keyword",
            indent: true
        },
        {
            regex: /^\s*(?:(Function|PageEx|Section(?:Group)?)End)\b/i,
            token: "keyword",
            dedent: true
        },
        // Command Options
        {
            regex: /\b(?:ARCHIVE|FILE_ATTRIBUTE_ARCHIVE|FILE_ATTRIBUTE_HIDDEN|FILE_ATTRIBUTE_NORMAL|FILE_ATTRIBUTE_OFFLINE|FILE_ATTRIBUTE_READONLY|FILE_ATTRIBUTE_SYSTEM|FILE_ATTRIBUTE_TEMPORARY|HIDDEN|HKCC|HKCR(32|64)?|HKCU(32|64)?|HKDD|HKEY_CLASSES_ROOT|HKEY_CURRENT_CONFIG|HKEY_CURRENT_USER|HKEY_DYN_DATA|HKEY_LOCAL_MACHINE|HKEY_PERFORMANCE_DATA|HKEY_USERS|HKLM(32|64)?|HKPD|HKU|IDABORT|IDCANCEL|IDD_DIR|IDD_INST|IDD_INSTFILES|IDD_LICENSE|IDD_SELCOM|IDD_UNINST|IDD_VERIFY|IDIGNORE|IDNO|IDOK|IDRETRY|IDYES|MB_ABORTRETRYIGNORE|MB_DEFBUTTON1|MB_DEFBUTTON2|MB_DEFBUTTON3|MB_DEFBUTTON4|MB_ICONEXCLAMATION|MB_ICONINFORMATION|MB_ICONQUESTION|MB_ICONSTOP|MB_OK|MB_OKCANCEL|MB_RETRYCANCEL|MB_RIGHT|MB_RTLREADING|MB_SETFOREGROUND|MB_TOPMOST|MB_USERICON|MB_YESNO|MB_YESNOCANCEL|NORMAL|OFFLINE|READONLY|SHCTX|SHELL_CONTEXT|SW_HIDE|SW_SHOWDEFAULT|SW_SHOWMAXIMIZED|SW_SHOWMINIMIZED|SW_SHOWNORMAL|SYSTEM|TEMPORARY)\b/i,
            token: "atom"
        },
        {
            regex: /\b(?:admin|all|amd64-unicode|auto|both|bottom|bzip2|components|current|custom|directory|false|force|hide|highest|ifdiff|ifnewer|instfiles|lastused|leave|left|license|listonly|lzma|nevershow|none|normal|notset|off|on|right|show|silent|silentlog|textonly|top|true|try|un\.components|un\.custom|un\.directory|un\.instfiles|un\.license|uninstConfirm|user|Win10|Win7|Win8|WinVista|x-86-(ansi|unicode)|zlib)\b/i,
            token: "builtin"
        },
        // LogicLib.nsh
        {
            regex: /\$\{(?:And(?:If(?:Not)?|Unless)|Break|Case(?:2|3|4|5|Else)?|Continue|Default|Do(?:Until|While)?|Else(?:If(?:Not)?|Unless)?|End(?:If|Select|Switch)|Exit(?:Do|For|While)|For(?:Each)?|If(?:Cmd|Not(?:Then)?|Then)?|Loop(?:Until|While)?|Or(?:If(?:Not)?|Unless)|Select|Switch|Unless|While)\}/i,
            token: "variable-2",
            indent: true
        },
        // FileFunc.nsh
        {
            regex: /\$\{(?:BannerTrimPath|DirState|DriveSpace|Get(BaseName|Drives|ExeName|ExePath|FileAttributes|FileExt|FileName|FileVersion|Options|OptionsS|Parameters|Parent|Root|Size|Time)|Locate|RefreshShellIcons)\}/i,
            token: "variable-2",
            dedent: true
        },
        // Memento.nsh
        {
            regex: /\$\{(?:Memento(?:Section(?:Done|End|Restore|Save)?|UnselectedSection))\}/i,
            token: "variable-2",
            dedent: true
        },
        // TextFunc.nsh
        {
            regex: /\$\{(?:Config(?:Read|ReadS|Write|WriteS)|File(?:Join|ReadFromEnd|Recode)|Line(?:Find|Read|Sum)|Text(?:Compare|CompareS)|TrimNewLines)\}/i,
            token: "variable-2",
            dedent: true
        },
        // WinVer.nsh
        {
            regex: /\$\{(?:(?:At(?:Least|Most)|Is)(?:ServicePack|Win(?:7|8|10|95|98|200(?:0|3|8(?:R2)?)|ME|NT4|Vista|XP))|Is(?:NT|Server))\}/i,
            token: "variable",
            dedent: true
        },
        // WordFunc.nsh
        {
            regex: /\$\{(?:StrFilterS?|Version(?:Compare|Convert)|Word(?:AddS?|Find(?:(?:2|3)X)?S?|InsertS?|ReplaceS?))\}/i,
            token: "keyword",
            dedent: true
        },
        // x64.nsh
        {
            regex: /\$\{(?:RunningX64)\}/i,
            token: "variable",
            dedent: true
        },
        {
            regex: /\$\{(?:Disable|Enable)X64FSRedirection\}/i,
            token: "keyword",
            dedent: true
        },
        // Line Comment
        {
            regex: /(#|;).*/,
            token: "comment"
        },
        // Block Comment
        {
            regex: /\/\*/,
            token: "comment",
            next: "comment"
        },
        // Operator
        {
            regex: /[-+\/*=<>!]+/,
            token: "operator"
        },
        // Variable
        {
            regex: /\$\w[\w\.]*/,
            token: "variable"
        },
        // Constant
        {
            regex: /\${[\!\w\.:-]+}/,
            token: "variableName.constant"
        },
        // Language String
        {
            regex: /\$\([\!\w\.:-]+\)/,
            token: "atom"
        }
    ],
    comment: [
        {
            regex: /.*?\*\//,
            token: "comment",
            next: "start"
        },
        {
            regex: /.*/,
            token: "comment"
        }
    ],
    languageData: {
        name: "nsis",
        indentOnInput: /^\s*((Function|PageEx|Section|Section(Group)?)End|(\!(endif|macroend))|\$\{(End(If|Unless|While)|Loop(Until)|Next)\})$/i,
        commentTokens: {
            line: "#",
            block: {
                open: "/*",
                close: "*/"
            }
        }
    }
});
}}),
}]);

//# sourceMappingURL=node_modules_%40codemirror_legacy-modes_mode_d81f05ab._.js.map