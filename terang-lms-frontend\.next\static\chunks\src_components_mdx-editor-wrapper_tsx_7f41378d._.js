(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/mdx-editor-wrapper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>MDXEditorWrapper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
// Import MDX Editor and plugins dynamically to avoid SSR issues
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$MDXEditor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/MDXEditor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$headings$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/headings/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$lists$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/lists/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$quote$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/quote/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$thematic$2d$break$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/thematic-break/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$markdown$2d$shortcut$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/markdown-shortcut/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$link$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/link/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$link$2d$dialog$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/link-dialog/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$image$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/image/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$table$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/table/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$codeblock$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/codeblock/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$codemirror$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/codemirror/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/toolbar/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$UndoRedo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/toolbar/components/UndoRedo.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$BoldItalicUnderlineToggles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/toolbar/components/BoldItalicUnderlineToggles.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$CreateLink$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/toolbar/components/CreateLink.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$InsertImage$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/toolbar/components/InsertImage.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$InsertTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/toolbar/components/InsertTable.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$InsertThematicBreak$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/toolbar/components/InsertThematicBreak.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$ListsToggle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/toolbar/components/ListsToggle.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$BlockTypeSelect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/toolbar/components/BlockTypeSelect.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$CodeToggle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/toolbar/components/CodeToggle.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$InsertCodeBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mdxeditor/editor/dist/plugins/toolbar/components/InsertCodeBlock.js [app-client] (ecmascript)");
'use client';
;
;
;
function MDXEditorWrapper({ markdown, onChange, placeholder = "Enter your content here...", className = "min-h-[200px]" }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$MDXEditor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MDXEditor"], {
        markdown: markdown,
        onChange: onChange,
        contentEditableClassName: "prose max-w-none",
        plugins: [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$headings$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["headingsPlugin"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$lists$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["listsPlugin"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$quote$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["quotePlugin"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$thematic$2d$break$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["thematicBreakPlugin"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$markdown$2d$shortcut$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["markdownShortcutPlugin"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$link$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["linkPlugin"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$link$2d$dialog$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["linkDialogPlugin"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$image$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["imagePlugin"])({
                imageUploadHandler: async (image)=>{
                    // Handle image upload
                    try {
                        const response = await fetch(`/api/upload?filename=${image.name}`, {
                            method: 'POST',
                            body: image
                        });
                        if (!response.ok) {
                            throw new Error('Upload failed');
                        }
                        const result = await response.json();
                        return result.url;
                    } catch (error) {
                        console.error('Image upload failed:', error);
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to upload image');
                        return '';
                    }
                }
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$table$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["tablePlugin"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$codeblock$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["codeBlockPlugin"])({
                defaultCodeBlockLanguage: 'javascript'
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$codemirror$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["codeMirrorPlugin"])({
                codeBlockLanguages: {
                    javascript: 'JavaScript',
                    typescript: 'TypeScript',
                    python: 'Python',
                    html: 'HTML',
                    css: 'CSS',
                    json: 'JSON',
                    markdown: 'Markdown'
                }
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toolbarPlugin"])({
                toolbarContents: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$UndoRedo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UndoRedo"], {}, void 0, false, {
                                fileName: "[project]/src/components/mdx-editor-wrapper.tsx",
                                lineNumber: 97,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$BoldItalicUnderlineToggles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BoldItalicUnderlineToggles"], {}, void 0, false, {
                                fileName: "[project]/src/components/mdx-editor-wrapper.tsx",
                                lineNumber: 98,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$CodeToggle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CodeToggle"], {}, void 0, false, {
                                fileName: "[project]/src/components/mdx-editor-wrapper.tsx",
                                lineNumber: 99,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$BlockTypeSelect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BlockTypeSelect"], {}, void 0, false, {
                                fileName: "[project]/src/components/mdx-editor-wrapper.tsx",
                                lineNumber: 100,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$CreateLink$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CreateLink"], {}, void 0, false, {
                                fileName: "[project]/src/components/mdx-editor-wrapper.tsx",
                                lineNumber: 101,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$InsertImage$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InsertImage"], {}, void 0, false, {
                                fileName: "[project]/src/components/mdx-editor-wrapper.tsx",
                                lineNumber: 102,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$ListsToggle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListsToggle"], {}, void 0, false, {
                                fileName: "[project]/src/components/mdx-editor-wrapper.tsx",
                                lineNumber: 103,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$InsertTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InsertTable"], {}, void 0, false, {
                                fileName: "[project]/src/components/mdx-editor-wrapper.tsx",
                                lineNumber: 104,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$InsertThematicBreak$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InsertThematicBreak"], {}, void 0, false, {
                                fileName: "[project]/src/components/mdx-editor-wrapper.tsx",
                                lineNumber: 105,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mdxeditor$2f$editor$2f$dist$2f$plugins$2f$toolbar$2f$components$2f$InsertCodeBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InsertCodeBlock"], {}, void 0, false, {
                                fileName: "[project]/src/components/mdx-editor-wrapper.tsx",
                                lineNumber: 106,
                                columnNumber: 15
                            }, void 0)
                        ]
                    }, void 0, true)
            })
        ],
        placeholder: placeholder,
        className: className
    }, void 0, false, {
        fileName: "[project]/src/components/mdx-editor-wrapper.tsx",
        lineNumber: 47,
        columnNumber: 5
    }, this);
}
_c = MDXEditorWrapper;
var _c;
__turbopack_context__.k.register(_c, "MDXEditorWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/mdx-editor-wrapper.tsx [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/components/mdx-editor-wrapper.tsx [app-client] (ecmascript)"));
}}),
}]);

//# sourceMappingURL=src_components_mdx-editor-wrapper_tsx_7f41378d._.js.map