{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lezer/lr/dist/index.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, NodeProp, NodeSet, NodeType, <PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tree, IterMode } from '@lezer/common';\n\n/**\nA parse stack. These are used internally by the parser to track\nparsing progress. They also provide some properties and methods\nthat external code such as a tokenizer can use to get information\nabout the parse state.\n*/\nclass Stack {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parse that this stack is part of @internal\n    */\n    p, \n    /**\n    Holds state, input pos, buffer index triplets for all but the\n    top state @internal\n    */\n    stack, \n    /**\n    The current parse state @internal\n    */\n    state, \n    // The position at which the next reduce should take place. This\n    // can be less than `this.pos` when skipped expressions have been\n    // added to the stack (which should be moved outside of the next\n    // reduction)\n    /**\n    @internal\n    */\n    reducePos, \n    /**\n    The input position up to which this stack has parsed.\n    */\n    pos, \n    /**\n    The dynamic score of the stack, including dynamic precedence\n    and error-recovery penalties\n    @internal\n    */\n    score, \n    // The output buffer. Holds (type, start, end, size) quads\n    // representing nodes created by the parser, where `size` is\n    // amount of buffer array entries covered by this node.\n    /**\n    @internal\n    */\n    buffer, \n    // The base offset of the buffer. When stacks are split, the split\n    // instance shared the buffer history with its parent up to\n    // `bufferBase`, which is the absolute offset (including the\n    // offset of previous splits) into the buffer at which this stack\n    // starts writing.\n    /**\n    @internal\n    */\n    bufferBase, \n    /**\n    @internal\n    */\n    curContext, \n    /**\n    @internal\n    */\n    lookAhead = 0, \n    // A parent stack from which this was split off, if any. This is\n    // set up so that it always points to a stack that has some\n    // additional buffer content, never to a stack with an equal\n    // `bufferBase`.\n    /**\n    @internal\n    */\n    parent) {\n        this.p = p;\n        this.stack = stack;\n        this.state = state;\n        this.reducePos = reducePos;\n        this.pos = pos;\n        this.score = score;\n        this.buffer = buffer;\n        this.bufferBase = bufferBase;\n        this.curContext = curContext;\n        this.lookAhead = lookAhead;\n        this.parent = parent;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n    }\n    // Start an empty stack\n    /**\n    @internal\n    */\n    static start(p, state, pos = 0) {\n        let cx = p.parser.context;\n        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n    }\n    /**\n    The stack's current [context](#lr.ContextTracker) value, if\n    any. Its type will depend on the context tracker's type\n    parameter, or it will be `null` if there is no context\n    tracker.\n    */\n    get context() { return this.curContext ? this.curContext.context : null; }\n    // Push a state onto the stack, tracking its start position as well\n    // as the buffer base at that point.\n    /**\n    @internal\n    */\n    pushState(state, start) {\n        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n        this.state = state;\n    }\n    // Apply a reduce action\n    /**\n    @internal\n    */\n    reduce(action) {\n        var _a;\n        let depth = action >> 19 /* Action.ReduceDepthShift */, type = action & 65535 /* Action.ValueMask */;\n        let { parser } = this.p;\n        let lookaheadRecord = this.reducePos < this.pos - 25 /* Lookahead.Margin */;\n        if (lookaheadRecord)\n            this.setLookAhead(this.pos);\n        let dPrec = parser.dynamicPrecedence(type);\n        if (dPrec)\n            this.score += dPrec;\n        if (depth == 0) {\n            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n            // Zero-depth reductions are a special case—they add stuff to\n            // the stack without popping anything off.\n            if (type < parser.minRepeatTerm)\n                this.storeNode(type, this.reducePos, this.reducePos, lookaheadRecord ? 8 : 4, true);\n            this.reduceContext(type, this.reducePos);\n            return;\n        }\n        // Find the base index into `this.stack`, content after which will\n        // be dropped. Note that with `StayFlag` reductions we need to\n        // consume two extra frames (the dummy parent node for the skipped\n        // expression and the state that we'll be staying in, which should\n        // be moved to `this.state`).\n        let base = this.stack.length - ((depth - 1) * 3) - (action & 262144 /* Action.StayFlag */ ? 6 : 0);\n        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n        // This is a kludge to try and detect overly deep left-associative\n        // trees, which will not increase the parse stack depth and thus\n        // won't be caught by the regular stack-depth limit check.\n        if (size >= 2000 /* Recover.MinBigReduction */ && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n            if (start == this.p.lastBigReductionStart) {\n                this.p.bigReductionCount++;\n                this.p.lastBigReductionSize = size;\n            }\n            else if (this.p.lastBigReductionSize < size) {\n                this.p.bigReductionCount = 1;\n                this.p.lastBigReductionStart = start;\n                this.p.lastBigReductionSize = size;\n            }\n        }\n        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n        // Store normal terms or `R -> R R` repeat reductions\n        if (type < parser.minRepeatTerm || (action & 131072 /* Action.RepeatFlag */)) {\n            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */) ? this.pos : this.reducePos;\n            this.storeNode(type, start, pos, count + 4, true);\n        }\n        if (action & 262144 /* Action.StayFlag */) {\n            this.state = this.stack[base];\n        }\n        else {\n            let baseStateID = this.stack[base - 3];\n            this.state = parser.getGoto(baseStateID, type, true);\n        }\n        while (this.stack.length > base)\n            this.stack.pop();\n        this.reduceContext(type, start);\n    }\n    // Shift a value into the buffer\n    /**\n    @internal\n    */\n    storeNode(term, start, end, size = 4, mustSink = false) {\n        if (term == 0 /* Term.Err */ &&\n            (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n            // Try to omit/merge adjacent error nodes\n            let cur = this, top = this.buffer.length;\n            if (top == 0 && cur.parent) {\n                top = cur.bufferBase - cur.parent.bufferBase;\n                cur = cur.parent;\n            }\n            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */ && cur.buffer[top - 1] > -1) {\n                if (start == end)\n                    return;\n                if (cur.buffer[top - 2] >= start) {\n                    cur.buffer[top - 2] = end;\n                    return;\n                }\n            }\n        }\n        if (!mustSink || this.pos == end) { // Simple case, just append\n            this.buffer.push(term, start, end, size);\n        }\n        else { // There may be skipped nodes that have to be moved forward\n            let index = this.buffer.length;\n            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */) {\n                let mustMove = false;\n                for (let scan = index; scan > 0 && this.buffer[scan - 2] > end; scan -= 4) {\n                    if (this.buffer[scan - 1] >= 0) {\n                        mustMove = true;\n                        break;\n                    }\n                }\n                if (mustMove)\n                    while (index > 0 && this.buffer[index - 2] > end) {\n                        // Move this record forward\n                        this.buffer[index] = this.buffer[index - 4];\n                        this.buffer[index + 1] = this.buffer[index - 3];\n                        this.buffer[index + 2] = this.buffer[index - 2];\n                        this.buffer[index + 3] = this.buffer[index - 1];\n                        index -= 4;\n                        if (size > 4)\n                            size -= 4;\n                    }\n            }\n            this.buffer[index] = term;\n            this.buffer[index + 1] = start;\n            this.buffer[index + 2] = end;\n            this.buffer[index + 3] = size;\n        }\n    }\n    // Apply a shift action\n    /**\n    @internal\n    */\n    shift(action, type, start, end) {\n        if (action & 131072 /* Action.GotoFlag */) {\n            this.pushState(action & 65535 /* Action.ValueMask */, this.pos);\n        }\n        else if ((action & 262144 /* Action.StayFlag */) == 0) { // Regular shift\n            let nextState = action, { parser } = this.p;\n            if (end > this.pos || type <= parser.maxNode) {\n                this.pos = end;\n                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */))\n                    this.reducePos = end;\n            }\n            this.pushState(nextState, start);\n            this.shiftContext(type, start);\n            if (type <= parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n        else { // Shift-and-stay, which means this is a skipped token\n            this.pos = end;\n            this.shiftContext(type, start);\n            if (type <= this.p.parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n    }\n    // Apply an action\n    /**\n    @internal\n    */\n    apply(action, next, nextStart, nextEnd) {\n        if (action & 65536 /* Action.ReduceFlag */)\n            this.reduce(action);\n        else\n            this.shift(action, next, nextStart, nextEnd);\n    }\n    // Add a prebuilt (reused) node into the buffer.\n    /**\n    @internal\n    */\n    useNode(value, next) {\n        let index = this.p.reused.length - 1;\n        if (index < 0 || this.p.reused[index] != value) {\n            this.p.reused.push(value);\n            index++;\n        }\n        let start = this.pos;\n        this.reducePos = this.pos = start + value.length;\n        this.pushState(next, start);\n        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */);\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n    }\n    // Split the stack. Due to the buffer sharing and the fact\n    // that `this.stack` tends to stay quite shallow, this isn't very\n    // expensive.\n    /**\n    @internal\n    */\n    split() {\n        let parent = this;\n        let off = parent.buffer.length;\n        // Because the top of the buffer (after this.pos) may be mutated\n        // to reorder reductions and skipped tokens, and shared buffers\n        // should be immutable, this copies any outstanding skipped tokens\n        // to the new buffer, and puts the base pointer before them.\n        while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n            off -= 4;\n        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n        // Make sure parent points to an actual parent with content, if there is such a parent.\n        while (parent && base == parent.bufferBase)\n            parent = parent.parent;\n        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n    }\n    // Try to recover from an error by 'deleting' (ignoring) one token.\n    /**\n    @internal\n    */\n    recoverByDelete(next, nextEnd) {\n        let isNode = next <= this.p.parser.maxNode;\n        if (isNode)\n            this.storeNode(next, this.pos, nextEnd, 4);\n        this.storeNode(0 /* Term.Err */, this.pos, nextEnd, isNode ? 8 : 4);\n        this.pos = this.reducePos = nextEnd;\n        this.score -= 190 /* Recover.Delete */;\n    }\n    /**\n    Check if the given term would be able to be shifted (optionally\n    after some reductions) on this stack. This can be useful for\n    external tokenizers that want to make sure they only provide a\n    given token when it applies.\n    */\n    canShift(term) {\n        for (let sim = new SimulatedStack(this);;) {\n            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */) || this.p.parser.hasAction(sim.state, term);\n            if (action == 0)\n                return false;\n            if ((action & 65536 /* Action.ReduceFlag */) == 0)\n                return true;\n            sim.reduce(action);\n        }\n    }\n    // Apply up to Recover.MaxNext recovery actions that conceptually\n    // inserts some missing token or rule.\n    /**\n    @internal\n    */\n    recoverByInsert(next) {\n        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */)\n            return [];\n        let nextStates = this.p.parser.nextStates(this.state);\n        if (nextStates.length > 4 /* Recover.MaxNext */ << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */) {\n            let best = [];\n            for (let i = 0, s; i < nextStates.length; i += 2) {\n                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n                    best.push(nextStates[i], s);\n            }\n            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */)\n                for (let i = 0; best.length < 4 /* Recover.MaxNext */ << 1 && i < nextStates.length; i += 2) {\n                    let s = nextStates[i + 1];\n                    if (!best.some((v, i) => (i & 1) && v == s))\n                        best.push(nextStates[i], s);\n                }\n            nextStates = best;\n        }\n        let result = [];\n        for (let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */; i += 2) {\n            let s = nextStates[i + 1];\n            if (s == this.state)\n                continue;\n            let stack = this.split();\n            stack.pushState(s, this.pos);\n            stack.storeNode(0 /* Term.Err */, stack.pos, stack.pos, 4, true);\n            stack.shiftContext(nextStates[i], this.pos);\n            stack.reducePos = this.pos;\n            stack.score -= 200 /* Recover.Insert */;\n            result.push(stack);\n        }\n        return result;\n    }\n    // Force a reduce, if possible. Return false if that can't\n    // be done.\n    /**\n    @internal\n    */\n    forceReduce() {\n        let { parser } = this.p;\n        let reduce = parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */);\n        if ((reduce & 65536 /* Action.ReduceFlag */) == 0)\n            return false;\n        if (!parser.validAction(this.state, reduce)) {\n            let depth = reduce >> 19 /* Action.ReduceDepthShift */, term = reduce & 65535 /* Action.ValueMask */;\n            let target = this.stack.length - depth * 3;\n            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {\n                let backup = this.findForcedReduction();\n                if (backup == null)\n                    return false;\n                reduce = backup;\n            }\n            this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n            this.score -= 100 /* Recover.Reduce */;\n        }\n        this.reducePos = this.pos;\n        this.reduce(reduce);\n        return true;\n    }\n    /**\n    Try to scan through the automaton to find some kind of reduction\n    that can be applied. Used when the regular ForcedReduce field\n    isn't a valid action. @internal\n    */\n    findForcedReduction() {\n        let { parser } = this.p, seen = [];\n        let explore = (state, depth) => {\n            if (seen.includes(state))\n                return;\n            seen.push(state);\n            return parser.allActions(state, (action) => {\n                if (action & (262144 /* Action.StayFlag */ | 131072 /* Action.GotoFlag */)) ;\n                else if (action & 65536 /* Action.ReduceFlag */) {\n                    let rDepth = (action >> 19 /* Action.ReduceDepthShift */) - depth;\n                    if (rDepth > 1) {\n                        let term = action & 65535 /* Action.ValueMask */, target = this.stack.length - rDepth * 3;\n                        if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0)\n                            return (rDepth << 19 /* Action.ReduceDepthShift */) | 65536 /* Action.ReduceFlag */ | term;\n                    }\n                }\n                else {\n                    let found = explore(action, depth + 1);\n                    if (found != null)\n                        return found;\n                }\n            });\n        };\n        return explore(this.state, 0);\n    }\n    /**\n    @internal\n    */\n    forceAll() {\n        while (!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */)) {\n            if (!this.forceReduce()) {\n                this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n                break;\n            }\n        }\n        return this;\n    }\n    /**\n    Check whether this state has no further actions (assumed to be a direct descendant of the\n    top state, since any other states must be able to continue\n    somehow). @internal\n    */\n    get deadEnd() {\n        if (this.stack.length != 3)\n            return false;\n        let { parser } = this.p;\n        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */)] == 65535 /* Seq.End */ &&\n            !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */);\n    }\n    /**\n    Restart the stack (put it back in its start state). Only safe\n    when this.stack.length == 3 (state is directly below the top\n    state). @internal\n    */\n    restart() {\n        this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n        this.state = this.stack[0];\n        this.stack.length = 0;\n    }\n    /**\n    @internal\n    */\n    sameState(other) {\n        if (this.state != other.state || this.stack.length != other.stack.length)\n            return false;\n        for (let i = 0; i < this.stack.length; i += 3)\n            if (this.stack[i] != other.stack[i])\n                return false;\n        return true;\n    }\n    /**\n    Get the parser used by this stack.\n    */\n    get parser() { return this.p.parser; }\n    /**\n    Test whether a given dialect (by numeric ID, as exported from\n    the terms file) is enabled.\n    */\n    dialectEnabled(dialectID) { return this.p.parser.dialect.flags[dialectID]; }\n    shiftContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    reduceContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    /**\n    @internal\n    */\n    emitContext() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -3)\n            this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);\n    }\n    /**\n    @internal\n    */\n    emitLookAhead() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -4)\n            this.buffer.push(this.lookAhead, this.pos, this.pos, -4);\n    }\n    updateContext(context) {\n        if (context != this.curContext.context) {\n            let newCx = new StackContext(this.curContext.tracker, context);\n            if (newCx.hash != this.curContext.hash)\n                this.emitContext();\n            this.curContext = newCx;\n        }\n    }\n    /**\n    @internal\n    */\n    setLookAhead(lookAhead) {\n        if (lookAhead > this.lookAhead) {\n            this.emitLookAhead();\n            this.lookAhead = lookAhead;\n        }\n    }\n    /**\n    @internal\n    */\n    close() {\n        if (this.curContext && this.curContext.tracker.strict)\n            this.emitContext();\n        if (this.lookAhead > 0)\n            this.emitLookAhead();\n    }\n}\nclass StackContext {\n    constructor(tracker, context) {\n        this.tracker = tracker;\n        this.context = context;\n        this.hash = tracker.strict ? tracker.hash(context) : 0;\n    }\n}\n// Used to cheaply run some reductions to scan ahead without mutating\n// an entire stack\nclass SimulatedStack {\n    constructor(start) {\n        this.start = start;\n        this.state = start.state;\n        this.stack = start.stack;\n        this.base = this.stack.length;\n    }\n    reduce(action) {\n        let term = action & 65535 /* Action.ValueMask */, depth = action >> 19 /* Action.ReduceDepthShift */;\n        if (depth == 0) {\n            if (this.stack == this.start.stack)\n                this.stack = this.stack.slice();\n            this.stack.push(this.state, 0, 0);\n            this.base += 3;\n        }\n        else {\n            this.base -= (depth - 1) * 3;\n        }\n        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n        this.state = goto;\n    }\n}\n// This is given to `Tree.build` to build a buffer, and encapsulates\n// the parent-stack-walking necessary to read the nodes.\nclass StackBufferCursor {\n    constructor(stack, pos, index) {\n        this.stack = stack;\n        this.pos = pos;\n        this.index = index;\n        this.buffer = stack.buffer;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n    }\n    maybeNext() {\n        let next = this.stack.parent;\n        if (next != null) {\n            this.index = this.stack.bufferBase - next.bufferBase;\n            this.stack = next;\n            this.buffer = next.buffer;\n        }\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    next() {\n        this.index -= 4;\n        this.pos -= 4;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    fork() {\n        return new StackBufferCursor(this.stack, this.pos, this.index);\n    }\n}\n\n// See lezer-generator/src/encode.ts for comments about the encoding\n// used here\nfunction decodeArray(input, Type = Uint16Array) {\n    if (typeof input != \"string\")\n        return input;\n    let array = null;\n    for (let pos = 0, out = 0; pos < input.length;) {\n        let value = 0;\n        for (;;) {\n            let next = input.charCodeAt(pos++), stop = false;\n            if (next == 126 /* Encode.BigValCode */) {\n                value = 65535 /* Encode.BigVal */;\n                break;\n            }\n            if (next >= 92 /* Encode.Gap2 */)\n                next--;\n            if (next >= 34 /* Encode.Gap1 */)\n                next--;\n            let digit = next - 32 /* Encode.Start */;\n            if (digit >= 46 /* Encode.Base */) {\n                digit -= 46 /* Encode.Base */;\n                stop = true;\n            }\n            value += digit;\n            if (stop)\n                break;\n            value *= 46 /* Encode.Base */;\n        }\n        if (array)\n            array[out++] = value;\n        else\n            array = new Type(value);\n    }\n    return array;\n}\n\nclass CachedToken {\n    constructor() {\n        this.start = -1;\n        this.value = -1;\n        this.end = -1;\n        this.extended = -1;\n        this.lookAhead = 0;\n        this.mask = 0;\n        this.context = 0;\n    }\n}\nconst nullToken = new CachedToken;\n/**\n[Tokenizers](#lr.ExternalTokenizer) interact with the input\nthrough this interface. It presents the input as a stream of\ncharacters, tracking lookahead and hiding the complexity of\n[ranges](#common.Parser.parse^ranges) from tokenizer code.\n*/\nclass InputStream {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    input, \n    /**\n    @internal\n    */\n    ranges) {\n        this.input = input;\n        this.ranges = ranges;\n        /**\n        @internal\n        */\n        this.chunk = \"\";\n        /**\n        @internal\n        */\n        this.chunkOff = 0;\n        /**\n        Backup chunk\n        */\n        this.chunk2 = \"\";\n        this.chunk2Pos = 0;\n        /**\n        The character code of the next code unit in the input, or -1\n        when the stream is at the end of the input.\n        */\n        this.next = -1;\n        /**\n        @internal\n        */\n        this.token = nullToken;\n        this.rangeIndex = 0;\n        this.pos = this.chunkPos = ranges[0].from;\n        this.range = ranges[0];\n        this.end = ranges[ranges.length - 1].to;\n        this.readNext();\n    }\n    /**\n    @internal\n    */\n    resolveOffset(offset, assoc) {\n        let range = this.range, index = this.rangeIndex;\n        let pos = this.pos + offset;\n        while (pos < range.from) {\n            if (!index)\n                return null;\n            let next = this.ranges[--index];\n            pos -= range.from - next.to;\n            range = next;\n        }\n        while (assoc < 0 ? pos > range.to : pos >= range.to) {\n            if (index == this.ranges.length - 1)\n                return null;\n            let next = this.ranges[++index];\n            pos += next.from - range.to;\n            range = next;\n        }\n        return pos;\n    }\n    /**\n    @internal\n    */\n    clipPos(pos) {\n        if (pos >= this.range.from && pos < this.range.to)\n            return pos;\n        for (let range of this.ranges)\n            if (range.to > pos)\n                return Math.max(pos, range.from);\n        return this.end;\n    }\n    /**\n    Look at a code unit near the stream position. `.peek(0)` equals\n    `.next`, `.peek(-1)` gives you the previous character, and so\n    on.\n    \n    Note that looking around during tokenizing creates dependencies\n    on potentially far-away content, which may reduce the\n    effectiveness incremental parsing—when looking forward—or even\n    cause invalid reparses when looking backward more than 25 code\n    units, since the library does not track lookbehind.\n    */\n    peek(offset) {\n        let idx = this.chunkOff + offset, pos, result;\n        if (idx >= 0 && idx < this.chunk.length) {\n            pos = this.pos + offset;\n            result = this.chunk.charCodeAt(idx);\n        }\n        else {\n            let resolved = this.resolveOffset(offset, 1);\n            if (resolved == null)\n                return -1;\n            pos = resolved;\n            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n            }\n            else {\n                let i = this.rangeIndex, range = this.range;\n                while (range.to <= pos)\n                    range = this.ranges[++i];\n                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n                if (pos + this.chunk2.length > range.to)\n                    this.chunk2 = this.chunk2.slice(0, range.to - pos);\n                result = this.chunk2.charCodeAt(0);\n            }\n        }\n        if (pos >= this.token.lookAhead)\n            this.token.lookAhead = pos + 1;\n        return result;\n    }\n    /**\n    Accept a token. By default, the end of the token is set to the\n    current stream position, but you can pass an offset (relative to\n    the stream position) to change that.\n    */\n    acceptToken(token, endOffset = 0) {\n        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n        if (end == null || end < this.token.start)\n            throw new RangeError(\"Token end out of bounds\");\n        this.token.value = token;\n        this.token.end = end;\n    }\n    /**\n    Accept a token ending at a specific given position.\n    */\n    acceptTokenTo(token, endPos) {\n        this.token.value = token;\n        this.token.end = endPos;\n    }\n    getChunk() {\n        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n            let { chunk, chunkPos } = this;\n            this.chunk = this.chunk2;\n            this.chunkPos = this.chunk2Pos;\n            this.chunk2 = chunk;\n            this.chunk2Pos = chunkPos;\n            this.chunkOff = this.pos - this.chunkPos;\n        }\n        else {\n            this.chunk2 = this.chunk;\n            this.chunk2Pos = this.chunkPos;\n            let nextChunk = this.input.chunk(this.pos);\n            let end = this.pos + nextChunk.length;\n            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n            this.chunkPos = this.pos;\n            this.chunkOff = 0;\n        }\n    }\n    readNext() {\n        if (this.chunkOff >= this.chunk.length) {\n            this.getChunk();\n            if (this.chunkOff == this.chunk.length)\n                return this.next = -1;\n        }\n        return this.next = this.chunk.charCodeAt(this.chunkOff);\n    }\n    /**\n    Move the stream forward N (defaults to 1) code units. Returns\n    the new value of [`next`](#lr.InputStream.next).\n    */\n    advance(n = 1) {\n        this.chunkOff += n;\n        while (this.pos + n >= this.range.to) {\n            if (this.rangeIndex == this.ranges.length - 1)\n                return this.setDone();\n            n -= this.range.to - this.pos;\n            this.range = this.ranges[++this.rangeIndex];\n            this.pos = this.range.from;\n        }\n        this.pos += n;\n        if (this.pos >= this.token.lookAhead)\n            this.token.lookAhead = this.pos + 1;\n        return this.readNext();\n    }\n    setDone() {\n        this.pos = this.chunkPos = this.end;\n        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n        this.chunk = \"\";\n        return this.next = -1;\n    }\n    /**\n    @internal\n    */\n    reset(pos, token) {\n        if (token) {\n            this.token = token;\n            token.start = pos;\n            token.lookAhead = pos + 1;\n            token.value = token.extended = -1;\n        }\n        else {\n            this.token = nullToken;\n        }\n        if (this.pos != pos) {\n            this.pos = pos;\n            if (pos == this.end) {\n                this.setDone();\n                return this;\n            }\n            while (pos < this.range.from)\n                this.range = this.ranges[--this.rangeIndex];\n            while (pos >= this.range.to)\n                this.range = this.ranges[++this.rangeIndex];\n            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n                this.chunkOff = pos - this.chunkPos;\n            }\n            else {\n                this.chunk = \"\";\n                this.chunkOff = 0;\n            }\n            this.readNext();\n        }\n        return this;\n    }\n    /**\n    @internal\n    */\n    read(from, to) {\n        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n            return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n            return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n        if (from >= this.range.from && to <= this.range.to)\n            return this.input.read(from, to);\n        let result = \"\";\n        for (let r of this.ranges) {\n            if (r.from >= to)\n                break;\n            if (r.to > from)\n                result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n        }\n        return result;\n    }\n}\n/**\n@internal\n*/\nclass TokenGroup {\n    constructor(data, id) {\n        this.data = data;\n        this.id = id;\n    }\n    token(input, stack) {\n        let { parser } = stack.p;\n        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n    }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n@hide\n*/\nclass LocalTokenGroup {\n    constructor(data, precTable, elseToken) {\n        this.precTable = precTable;\n        this.elseToken = elseToken;\n        this.data = typeof data == \"string\" ? decodeArray(data) : data;\n    }\n    token(input, stack) {\n        let start = input.pos, skipped = 0;\n        for (;;) {\n            let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);\n            readToken(this.data, input, stack, 0, this.data, this.precTable);\n            if (input.token.value > -1)\n                break;\n            if (this.elseToken == null)\n                return;\n            if (!atEof)\n                skipped++;\n            if (nextPos == null)\n                break;\n            input.reset(nextPos, input.token);\n        }\n        if (skipped) {\n            input.reset(start, input.token);\n            input.acceptToken(this.elseToken, skipped);\n        }\n    }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n`@external tokens` declarations in the grammar should resolve to\nan instance of this class.\n*/\nclass ExternalTokenizer {\n    /**\n    Create a tokenizer. The first argument is the function that,\n    given an input stream, scans for the types of tokens it\n    recognizes at the stream's position, and calls\n    [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n    one.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    token, options = {}) {\n        this.token = token;\n        this.contextual = !!options.contextual;\n        this.fallback = !!options.fallback;\n        this.extend = !!options.extend;\n    }\n}\n// Tokenizer data is stored a big uint16 array containing, for each\n// state:\n//\n//  - A group bitmask, indicating what token groups are reachable from\n//    this state, so that paths that can only lead to tokens not in\n//    any of the current groups can be cut off early.\n//\n//  - The position of the end of the state's sequence of accepting\n//    tokens\n//\n//  - The number of outgoing edges for the state\n//\n//  - The accepting tokens, as (token id, group mask) pairs\n//\n//  - The outgoing edges, as (start character, end character, state\n//    index) triples, with end character being exclusive\n//\n// This function interprets that data, running through a stream as\n// long as new states with the a matching group mask can be reached,\n// and updating `input.token` when it matches a token.\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n    scan: for (;;) {\n        if ((groupMask & data[state]) == 0)\n            break;\n        let accEnd = data[state + 1];\n        // Check whether this state can lead to a token in the current group\n        // Accept tokens in this state, possibly overwriting\n        // lower-precedence / shorter tokens\n        for (let i = state + 3; i < accEnd; i += 2)\n            if ((data[i + 1] & groupMask) > 0) {\n                let term = data[i];\n                if (dialect.allows(term) &&\n                    (input.token.value == -1 || input.token.value == term ||\n                        overrides(term, input.token.value, precTable, precOffset))) {\n                    input.acceptToken(term);\n                    break;\n                }\n            }\n        let next = input.next, low = 0, high = data[state + 2];\n        // Special case for EOF\n        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */) {\n            state = data[accEnd + high * 3 - 1];\n            continue scan;\n        }\n        // Do a binary search on the state's edges\n        for (; low < high;) {\n            let mid = (low + high) >> 1;\n            let index = accEnd + mid + (mid << 1);\n            let from = data[index], to = data[index + 1] || 0x10000;\n            if (next < from)\n                high = mid;\n            else if (next >= to)\n                low = mid + 1;\n            else {\n                state = data[index + 2];\n                input.advance();\n                continue scan;\n            }\n        }\n        break;\n    }\n}\nfunction findOffset(data, start, term) {\n    for (let i = start, next; (next = data[i]) != 65535 /* Seq.End */; i++)\n        if (next == term)\n            return i - start;\n    return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n    let iPrev = findOffset(tableData, tableOffset, prev);\n    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\n\n// Environment variable used to control console output\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nfunction cutAt(tree, pos, side) {\n    let cursor = tree.cursor(IterMode.IncludeAnonymous);\n    cursor.moveTo(pos);\n    for (;;) {\n        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n            for (;;) {\n                if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n                    return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Lookahead.Margin */))\n                        : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Lookahead.Margin */));\n                if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n                    break;\n                if (!cursor.parent())\n                    return side < 0 ? 0 : tree.length;\n            }\n    }\n}\nclass FragmentCursor {\n    constructor(fragments, nodeSet) {\n        this.fragments = fragments;\n        this.nodeSet = nodeSet;\n        this.i = 0;\n        this.fragment = null;\n        this.safeFrom = -1;\n        this.safeTo = -1;\n        this.trees = [];\n        this.start = [];\n        this.index = [];\n        this.nextFragment();\n    }\n    nextFragment() {\n        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n        if (fr) {\n            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n            while (this.trees.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n            }\n            this.trees.push(fr.tree);\n            this.start.push(-fr.offset);\n            this.index.push(0);\n            this.nextStart = this.safeFrom;\n        }\n        else {\n            this.nextStart = 1e9;\n        }\n    }\n    // `pos` must be >= any previously given `pos` for this cursor\n    nodeAt(pos) {\n        if (pos < this.nextStart)\n            return null;\n        while (this.fragment && this.safeTo <= pos)\n            this.nextFragment();\n        if (!this.fragment)\n            return null;\n        for (;;) {\n            let last = this.trees.length - 1;\n            if (last < 0) { // End of tree\n                this.nextFragment();\n                return null;\n            }\n            let top = this.trees[last], index = this.index[last];\n            if (index == top.children.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n                continue;\n            }\n            let next = top.children[index];\n            let start = this.start[last] + top.positions[index];\n            if (start > pos) {\n                this.nextStart = start;\n                return null;\n            }\n            if (next instanceof Tree) {\n                if (start == pos) {\n                    if (start < this.safeFrom)\n                        return null;\n                    let end = start + next.length;\n                    if (end <= this.safeTo) {\n                        let lookAhead = next.prop(NodeProp.lookAhead);\n                        if (!lookAhead || end + lookAhead < this.fragment.to)\n                            return next;\n                    }\n                }\n                this.index[last]++;\n                if (start + next.length >= Math.max(this.safeFrom, pos)) { // Enter this node\n                    this.trees.push(next);\n                    this.start.push(start);\n                    this.index.push(0);\n                }\n            }\n            else {\n                this.index[last]++;\n                this.nextStart = start + next.length;\n            }\n        }\n    }\n}\nclass TokenCache {\n    constructor(parser, stream) {\n        this.stream = stream;\n        this.tokens = [];\n        this.mainToken = null;\n        this.actions = [];\n        this.tokens = parser.tokenizers.map(_ => new CachedToken);\n    }\n    getActions(stack) {\n        let actionIndex = 0;\n        let main = null;\n        let { parser } = stack.p, { tokenizers } = parser;\n        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */);\n        let context = stack.curContext ? stack.curContext.hash : 0;\n        let lookAhead = 0;\n        for (let i = 0; i < tokenizers.length; i++) {\n            if (((1 << i) & mask) == 0)\n                continue;\n            let tokenizer = tokenizers[i], token = this.tokens[i];\n            if (main && !tokenizer.fallback)\n                continue;\n            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n                this.updateCachedToken(token, tokenizer, stack);\n                token.mask = mask;\n                token.context = context;\n            }\n            if (token.lookAhead > token.end + 25 /* Lookahead.Margin */)\n                lookAhead = Math.max(token.lookAhead, lookAhead);\n            if (token.value != 0 /* Term.Err */) {\n                let startIndex = actionIndex;\n                if (token.extended > -1)\n                    actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n                if (!tokenizer.extend) {\n                    main = token;\n                    if (actionIndex > startIndex)\n                        break;\n                }\n            }\n        }\n        while (this.actions.length > actionIndex)\n            this.actions.pop();\n        if (lookAhead)\n            stack.setLookAhead(lookAhead);\n        if (!main && stack.pos == this.stream.end) {\n            main = new CachedToken;\n            main.value = stack.p.parser.eofTerm;\n            main.start = main.end = stack.pos;\n            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n        }\n        this.mainToken = main;\n        return this.actions;\n    }\n    getMainToken(stack) {\n        if (this.mainToken)\n            return this.mainToken;\n        let main = new CachedToken, { pos, p } = stack;\n        main.start = pos;\n        main.end = Math.min(pos + 1, p.stream.end);\n        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */;\n        return main;\n    }\n    updateCachedToken(token, tokenizer, stack) {\n        let start = this.stream.clipPos(stack.pos);\n        tokenizer.token(this.stream.reset(start, token), stack);\n        if (token.value > -1) {\n            let { parser } = stack.p;\n            for (let i = 0; i < parser.specialized.length; i++)\n                if (parser.specialized[i] == token.value) {\n                    let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n                    if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n                        if ((result & 1) == 0 /* Specialize.Specialize */)\n                            token.value = result >> 1;\n                        else\n                            token.extended = result >> 1;\n                        break;\n                    }\n                }\n        }\n        else {\n            token.value = 0 /* Term.Err */;\n            token.end = this.stream.clipPos(start + 1);\n        }\n    }\n    putAction(action, token, end, index) {\n        // Don't add duplicate actions\n        for (let i = 0; i < index; i += 3)\n            if (this.actions[i] == action)\n                return index;\n        this.actions[index++] = action;\n        this.actions[index++] = token;\n        this.actions[index++] = end;\n        return index;\n    }\n    addActions(stack, token, end, index) {\n        let { state } = stack, { parser } = stack.p, { data } = parser;\n        for (let set = 0; set < 2; set++) {\n            for (let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */);; i += 3) {\n                if (data[i] == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */) {\n                        i = pair(data, i + 2);\n                    }\n                    else {\n                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */)\n                            index = this.putAction(pair(data, i + 2), token, end, index);\n                        break;\n                    }\n                }\n                if (data[i] == token)\n                    index = this.putAction(pair(data, i + 1), token, end, index);\n            }\n        }\n        return index;\n    }\n}\nclass Parse {\n    constructor(parser, input, fragments, ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.recovering = 0;\n        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧\n        this.minStackPos = 0;\n        this.reused = [];\n        this.stoppedAt = null;\n        this.lastBigReductionStart = -1;\n        this.lastBigReductionSize = 0;\n        this.bigReductionCount = 0;\n        this.stream = new InputStream(input, ranges);\n        this.tokens = new TokenCache(parser, this.stream);\n        this.topTerm = parser.top[1];\n        let { from } = ranges[0];\n        this.stacks = [Stack.start(this, parser.top[0], from)];\n        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4\n            ? new FragmentCursor(fragments, parser.nodeSet) : null;\n    }\n    get parsedPos() {\n        return this.minStackPos;\n    }\n    // Move the parser forward. This will process all parse stacks at\n    // `this.pos` and try to advance them to a further position. If no\n    // stack for such a position is found, it'll start error-recovery.\n    //\n    // When the parse is finished, this will return a syntax tree. When\n    // not, it returns `null`.\n    advance() {\n        let stacks = this.stacks, pos = this.minStackPos;\n        // This will hold stacks beyond `pos`.\n        let newStacks = this.stacks = [];\n        let stopped, stoppedTokens;\n        // If a large amount of reductions happened with the same start\n        // position, force the stack out of that production in order to\n        // avoid creating a tree too deep to recurse through.\n        // (This is an ugly kludge, because unfortunately there is no\n        // straightforward, cheap way to check for this happening, due to\n        // the history of reductions only being available in an\n        // expensive-to-access format in the stack buffers.)\n        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */ && stacks.length == 1) {\n            let [s] = stacks;\n            while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) { }\n            this.bigReductionCount = this.lastBigReductionSize = 0;\n        }\n        // Keep advancing any stacks at `pos` until they either move\n        // forward or can't be advanced. Gather stacks that can't be\n        // advanced further in `stopped`.\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i];\n            for (;;) {\n                this.tokens.mainToken = null;\n                if (stack.pos > pos) {\n                    newStacks.push(stack);\n                }\n                else if (this.advanceStack(stack, newStacks, stacks)) {\n                    continue;\n                }\n                else {\n                    if (!stopped) {\n                        stopped = [];\n                        stoppedTokens = [];\n                    }\n                    stopped.push(stack);\n                    let tok = this.tokens.getMainToken(stack);\n                    stoppedTokens.push(tok.value, tok.end);\n                }\n                break;\n            }\n        }\n        if (!newStacks.length) {\n            let finished = stopped && findFinished(stopped);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Finish with \" + this.stackID(finished));\n                return this.stackToTree(finished);\n            }\n            if (this.parser.strict) {\n                if (verbose && stopped)\n                    console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n                throw new SyntaxError(\"No parse at \" + pos);\n            }\n            if (!this.recovering)\n                this.recovering = 5 /* Rec.Distance */;\n        }\n        if (this.recovering && stopped) {\n            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0]\n                : this.runRecovery(stopped, stoppedTokens, newStacks);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Force-finish \" + this.stackID(finished));\n                return this.stackToTree(finished.forceAll());\n            }\n        }\n        if (this.recovering) {\n            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */;\n            if (newStacks.length > maxRemaining) {\n                newStacks.sort((a, b) => b.score - a.score);\n                while (newStacks.length > maxRemaining)\n                    newStacks.pop();\n            }\n            if (newStacks.some(s => s.reducePos > pos))\n                this.recovering--;\n        }\n        else if (newStacks.length > 1) {\n            // Prune stacks that are in the same state, or that have been\n            // running without splitting for a while, to avoid getting stuck\n            // with multiple successful stacks running endlessly on.\n            outer: for (let i = 0; i < newStacks.length - 1; i++) {\n                let stack = newStacks[i];\n                for (let j = i + 1; j < newStacks.length; j++) {\n                    let other = newStacks[j];\n                    if (stack.sameState(other) ||\n                        stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */ && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */) {\n                        if (((stack.score - other.score) || (stack.buffer.length - other.buffer.length)) > 0) {\n                            newStacks.splice(j--, 1);\n                        }\n                        else {\n                            newStacks.splice(i--, 1);\n                            continue outer;\n                        }\n                    }\n                }\n            }\n            if (newStacks.length > 12 /* Rec.MaxStackCount */)\n                newStacks.splice(12 /* Rec.MaxStackCount */, newStacks.length - 12 /* Rec.MaxStackCount */);\n        }\n        this.minStackPos = newStacks[0].pos;\n        for (let i = 1; i < newStacks.length; i++)\n            if (newStacks[i].pos < this.minStackPos)\n                this.minStackPos = newStacks[i].pos;\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    // Returns an updated version of the given stack, or null if the\n    // stack can't advance normally. When `split` and `stacks` are\n    // given, stacks split off by ambiguous operations will be pushed to\n    // `split`, or added to `stacks` if they move `pos` forward.\n    advanceStack(stack, stacks, split) {\n        let start = stack.pos, { parser } = this;\n        let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n        if (this.stoppedAt != null && start > this.stoppedAt)\n            return stack.forceReduce() ? stack : null;\n        if (this.fragments) {\n            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n            for (let cached = this.fragments.nodeAt(start); cached;) {\n                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n                if (match > -1 && cached.length && (!strictCx || (cached.prop(NodeProp.contextHash) || 0) == cxHash)) {\n                    stack.useNode(cached, match);\n                    if (verbose)\n                        console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n                    return true;\n                }\n                if (!(cached instanceof Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n                    break;\n                let inner = cached.children[0];\n                if (inner instanceof Tree && cached.positions[0] == 0)\n                    cached = inner;\n                else\n                    break;\n            }\n        }\n        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */);\n        if (defaultReduce > 0) {\n            stack.reduce(defaultReduce);\n            if (verbose)\n                console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */)})`);\n            return true;\n        }\n        if (stack.stack.length >= 8400 /* Rec.CutDepth */) {\n            while (stack.stack.length > 6000 /* Rec.CutTo */ && stack.forceReduce()) { }\n        }\n        let actions = this.tokens.getActions(stack);\n        for (let i = 0; i < actions.length;) {\n            let action = actions[i++], term = actions[i++], end = actions[i++];\n            let last = i == actions.length || !split;\n            let localStack = last ? stack : stack.split();\n            let main = this.tokens.mainToken;\n            localStack.apply(action, term, main ? main.start : localStack.pos, end);\n            if (verbose)\n                console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */) == 0 ? \"shift\"\n                    : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */)}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n            if (last)\n                return true;\n            else if (localStack.pos > start)\n                stacks.push(localStack);\n            else\n                split.push(localStack);\n        }\n        return false;\n    }\n    // Advance a given stack forward as far as it will go. Returns the\n    // (possibly updated) stack if it got stuck, or null if it moved\n    // forward and was given to `pushStackDedup`.\n    advanceFully(stack, newStacks) {\n        let pos = stack.pos;\n        for (;;) {\n            if (!this.advanceStack(stack, null, null))\n                return false;\n            if (stack.pos > pos) {\n                pushStackDedup(stack, newStacks);\n                return true;\n            }\n        }\n    }\n    runRecovery(stacks, tokens, newStacks) {\n        let finished = null, restarted = false;\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n            let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n            if (stack.deadEnd) {\n                if (restarted)\n                    continue;\n                restarted = true;\n                stack.restart();\n                if (verbose)\n                    console.log(base + this.stackID(stack) + \" (restarted)\");\n                let done = this.advanceFully(stack, newStacks);\n                if (done)\n                    continue;\n            }\n            let force = stack.split(), forceBase = base;\n            for (let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */; j++) {\n                if (verbose)\n                    console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n                let done = this.advanceFully(force, newStacks);\n                if (done)\n                    break;\n                if (verbose)\n                    forceBase = this.stackID(force) + \" -> \";\n            }\n            for (let insert of stack.recoverByInsert(token)) {\n                if (verbose)\n                    console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n                this.advanceFully(insert, newStacks);\n            }\n            if (this.stream.end > stack.pos) {\n                if (tokenEnd == stack.pos) {\n                    tokenEnd++;\n                    token = 0 /* Term.Err */;\n                }\n                stack.recoverByDelete(token, tokenEnd);\n                if (verbose)\n                    console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n                pushStackDedup(stack, newStacks);\n            }\n            else if (!finished || finished.score < stack.score) {\n                finished = stack;\n            }\n        }\n        return finished;\n    }\n    // Convert the stack's buffer to a syntax tree.\n    stackToTree(stack) {\n        stack.close();\n        return Tree.build({ buffer: StackBufferCursor.create(stack),\n            nodeSet: this.parser.nodeSet,\n            topID: this.topTerm,\n            maxBufferLength: this.parser.bufferLength,\n            reused: this.reused,\n            start: this.ranges[0].from,\n            length: stack.pos - this.ranges[0].from,\n            minRepeatType: this.parser.minRepeatTerm });\n    }\n    stackID(stack) {\n        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);\n        if (!id)\n            stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));\n        return id + stack;\n    }\n}\nfunction pushStackDedup(stack, newStacks) {\n    for (let i = 0; i < newStacks.length; i++) {\n        let other = newStacks[i];\n        if (other.pos == stack.pos && other.sameState(stack)) {\n            if (newStacks[i].score < stack.score)\n                newStacks[i] = stack;\n            return;\n        }\n    }\n    newStacks.push(stack);\n}\nclass Dialect {\n    constructor(source, flags, disabled) {\n        this.source = source;\n        this.flags = flags;\n        this.disabled = disabled;\n    }\n    allows(term) { return !this.disabled || this.disabled[term] == 0; }\n}\nconst id = x => x;\n/**\nContext trackers are used to track stateful context (such as\nindentation in the Python grammar, or parent elements in the XML\ngrammar) needed by external tokenizers. You declare them in a\ngrammar file as `@context exportName from \"module\"`.\n\nContext values should be immutable, and can be updated (replaced)\non shift or reduce actions.\n\nThe export used in a `@context` declaration should be of this\ntype.\n*/\nclass ContextTracker {\n    /**\n    Define a context tracker.\n    */\n    constructor(spec) {\n        this.start = spec.start;\n        this.shift = spec.shift || id;\n        this.reduce = spec.reduce || id;\n        this.reuse = spec.reuse || id;\n        this.hash = spec.hash || (() => 0);\n        this.strict = spec.strict !== false;\n    }\n}\n/**\nHolds the parse tables for a given grammar, as generated by\n`lezer-generator`, and provides [methods](#common.Parser) to parse\ncontent with.\n*/\nclass LRParser extends Parser {\n    /**\n    @internal\n    */\n    constructor(spec) {\n        super();\n        /**\n        @internal\n        */\n        this.wrappers = [];\n        if (spec.version != 14 /* File.Version */)\n            throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */})`);\n        let nodeNames = spec.nodeNames.split(\" \");\n        this.minRepeatTerm = nodeNames.length;\n        for (let i = 0; i < spec.repeatNodeCount; i++)\n            nodeNames.push(\"\");\n        let topTerms = Object.keys(spec.topRules).map(r => spec.topRules[r][1]);\n        let nodeProps = [];\n        for (let i = 0; i < nodeNames.length; i++)\n            nodeProps.push([]);\n        function setProp(nodeID, prop, value) {\n            nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n        }\n        if (spec.nodeProps)\n            for (let propSpec of spec.nodeProps) {\n                let prop = propSpec[0];\n                if (typeof prop == \"string\")\n                    prop = NodeProp[prop];\n                for (let i = 1; i < propSpec.length;) {\n                    let next = propSpec[i++];\n                    if (next >= 0) {\n                        setProp(next, prop, propSpec[i++]);\n                    }\n                    else {\n                        let value = propSpec[i + -next];\n                        for (let j = -next; j > 0; j--)\n                            setProp(propSpec[i++], prop, value);\n                        i++;\n                    }\n                }\n            }\n        this.nodeSet = new NodeSet(nodeNames.map((name, i) => NodeType.define({\n            name: i >= this.minRepeatTerm ? undefined : name,\n            id: i,\n            props: nodeProps[i],\n            top: topTerms.indexOf(i) > -1,\n            error: i == 0,\n            skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n        })));\n        if (spec.propSources)\n            this.nodeSet = this.nodeSet.extend(...spec.propSources);\n        this.strict = false;\n        this.bufferLength = DefaultBufferLength;\n        let tokenArray = decodeArray(spec.tokenData);\n        this.context = spec.context;\n        this.specializerSpecs = spec.specialized || [];\n        this.specialized = new Uint16Array(this.specializerSpecs.length);\n        for (let i = 0; i < this.specializerSpecs.length; i++)\n            this.specialized[i] = this.specializerSpecs[i].term;\n        this.specializers = this.specializerSpecs.map(getSpecializer);\n        this.states = decodeArray(spec.states, Uint32Array);\n        this.data = decodeArray(spec.stateData);\n        this.goto = decodeArray(spec.goto);\n        this.maxTerm = spec.maxTerm;\n        this.tokenizers = spec.tokenizers.map(value => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n        this.topRules = spec.topRules;\n        this.dialects = spec.dialects || {};\n        this.dynamicPrecedences = spec.dynamicPrecedences || null;\n        this.tokenPrecTable = spec.tokenPrec;\n        this.termNames = spec.termNames || null;\n        this.maxNode = this.nodeSet.types.length - 1;\n        this.dialect = this.parseDialect();\n        this.top = this.topRules[Object.keys(this.topRules)[0]];\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new Parse(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Get a goto table entry @internal\n    */\n    getGoto(state, term, loose = false) {\n        let table = this.goto;\n        if (term >= table[0])\n            return -1;\n        for (let pos = table[term + 1];;) {\n            let groupTag = table[pos++], last = groupTag & 1;\n            let target = table[pos++];\n            if (last && loose)\n                return target;\n            for (let end = pos + (groupTag >> 1); pos < end; pos++)\n                if (table[pos] == state)\n                    return target;\n            if (last)\n                return -1;\n        }\n    }\n    /**\n    Check if this state has an action for a given terminal @internal\n    */\n    hasAction(state, terminal) {\n        let data = this.data;\n        for (let set = 0; set < 2; set++) {\n            for (let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */), next;; i += 3) {\n                if ((next = data[i]) == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */)\n                        next = data[i = pair(data, i + 2)];\n                    else if (data[i + 1] == 2 /* Seq.Other */)\n                        return pair(data, i + 2);\n                    else\n                        break;\n                }\n                if (next == terminal || next == 0 /* Term.Err */)\n                    return pair(data, i + 1);\n            }\n        }\n        return 0;\n    }\n    /**\n    @internal\n    */\n    stateSlot(state, slot) {\n        return this.states[(state * 6 /* ParseState.Size */) + slot];\n    }\n    /**\n    @internal\n    */\n    stateFlag(state, flag) {\n        return (this.stateSlot(state, 0 /* ParseState.Flags */) & flag) > 0;\n    }\n    /**\n    @internal\n    */\n    validAction(state, action) {\n        return !!this.allActions(state, a => a == action ? true : null);\n    }\n    /**\n    @internal\n    */\n    allActions(state, action) {\n        let deflt = this.stateSlot(state, 4 /* ParseState.DefaultReduce */);\n        let result = deflt ? action(deflt) : undefined;\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */); result == null; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            result = action(pair(this.data, i + 1));\n        }\n        return result;\n    }\n    /**\n    Get the states that can follow this one through shift actions or\n    goto jumps. @internal\n    */\n    nextStates(state) {\n        let result = [];\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            if ((this.data[i + 2] & (65536 /* Action.ReduceFlag */ >> 16)) == 0) {\n                let value = this.data[i + 1];\n                if (!result.some((v, i) => (i & 1) && v == value))\n                    result.push(this.data[i], value);\n            }\n        }\n        return result;\n    }\n    /**\n    Configure the parser. Returns a new parser instance that has the\n    given settings modified. Settings not provided in `config` are\n    kept from the original parser.\n    */\n    configure(config) {\n        // Hideous reflection-based kludge to make it easy to create a\n        // slightly modified copy of a parser.\n        let copy = Object.assign(Object.create(LRParser.prototype), this);\n        if (config.props)\n            copy.nodeSet = this.nodeSet.extend(...config.props);\n        if (config.top) {\n            let info = this.topRules[config.top];\n            if (!info)\n                throw new RangeError(`Invalid top rule name ${config.top}`);\n            copy.top = info;\n        }\n        if (config.tokenizers)\n            copy.tokenizers = this.tokenizers.map(t => {\n                let found = config.tokenizers.find(r => r.from == t);\n                return found ? found.to : t;\n            });\n        if (config.specializers) {\n            copy.specializers = this.specializers.slice();\n            copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n                let found = config.specializers.find(r => r.from == s.external);\n                if (!found)\n                    return s;\n                let spec = Object.assign(Object.assign({}, s), { external: found.to });\n                copy.specializers[i] = getSpecializer(spec);\n                return spec;\n            });\n        }\n        if (config.contextTracker)\n            copy.context = config.contextTracker;\n        if (config.dialect)\n            copy.dialect = this.parseDialect(config.dialect);\n        if (config.strict != null)\n            copy.strict = config.strict;\n        if (config.wrap)\n            copy.wrappers = copy.wrappers.concat(config.wrap);\n        if (config.bufferLength != null)\n            copy.bufferLength = config.bufferLength;\n        return copy;\n    }\n    /**\n    Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n    are registered for this parser.\n    */\n    hasWrappers() {\n        return this.wrappers.length > 0;\n    }\n    /**\n    Returns the name associated with a given term. This will only\n    work for all terms when the parser was generated with the\n    `--names` option. By default, only the names of tagged terms are\n    stored.\n    */\n    getName(term) {\n        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n    }\n    /**\n    The eof term id is always allocated directly after the node\n    types. @internal\n    */\n    get eofTerm() { return this.maxNode + 1; }\n    /**\n    The type of top node produced by the parser.\n    */\n    get topNode() { return this.nodeSet.types[this.top[1]]; }\n    /**\n    @internal\n    */\n    dynamicPrecedence(term) {\n        let prec = this.dynamicPrecedences;\n        return prec == null ? 0 : prec[term] || 0;\n    }\n    /**\n    @internal\n    */\n    parseDialect(dialect) {\n        let values = Object.keys(this.dialects), flags = values.map(() => false);\n        if (dialect)\n            for (let part of dialect.split(\" \")) {\n                let id = values.indexOf(part);\n                if (id >= 0)\n                    flags[id] = true;\n            }\n        let disabled = null;\n        for (let i = 0; i < values.length; i++)\n            if (!flags[i]) {\n                for (let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */;)\n                    (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;\n            }\n        return new Dialect(dialect, flags, disabled);\n    }\n    /**\n    Used by the output of the parser generator. Not available to\n    user code. @hide\n    */\n    static deserialize(spec) {\n        return new LRParser(spec);\n    }\n}\nfunction pair(data, off) { return data[off] | (data[off + 1] << 16); }\nfunction findFinished(stacks) {\n    let best = null;\n    for (let stack of stacks) {\n        let stopped = stack.p.stoppedAt;\n        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) &&\n            stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */) &&\n            (!best || best.score < stack.score))\n            best = stack;\n    }\n    return best;\n}\nfunction getSpecializer(spec) {\n    if (spec.external) {\n        let mask = spec.extend ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */;\n        return (value, stack) => (spec.external(value, stack) << 1) | mask;\n    }\n    return spec.get;\n}\n\nexport { ContextTracker, ExternalTokenizer, InputStream, LRParser, LocalTokenGroup, Stack };\n"], "names": [], "mappings": ";;;;;;;;AA8gCuB;AA9gCvB;;AAEA;;;;;AAKA,GACA,MAAM;IACF;;IAEA,GACA,YACA;;IAEA,GACA,CAAC,EACD;;;IAGA,GACA,KAAK,EACL;;IAEA,GACA,KAAK,EACL,gEAAgE;IAChE,iEAAiE;IACjE,gEAAgE;IAChE,aAAa;IACb;;IAEA,GACA,SAAS,EACT;;IAEA,GACA,GAAG,EACH;;;;IAIA,GACA,KAAK,EACL,0DAA0D;IAC1D,4DAA4D;IAC5D,uDAAuD;IACvD;;IAEA,GACA,MAAM,EACN,kEAAkE;IAClE,2DAA2D;IAC3D,4DAA4D;IAC5D,iEAAiE;IACjE,kBAAkB;IAClB;;IAEA,GACA,UAAU,EACV;;IAEA,GACA,UAAU,EACV;;IAEA,GACA,YAAY,CAAC,EACb,gEAAgE;IAChE,2DAA2D;IAC3D,4DAA4D;IAC5D,gBAAgB;IAChB;;IAEA,GACA,MAAM,CAAE;QACJ,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;IAEA,GACA,WAAW;QACP,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI;IAC7H;IACA,uBAAuB;IACvB;;IAEA,GACA,OAAO,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE;QAC5B,IAAI,KAAK,EAAE,MAAM,CAAC,OAAO;QACzB,OAAO,IAAI,MAAM,GAAG,EAAE,EAAE,OAAO,KAAK,KAAK,GAAG,EAAE,EAAE,GAAG,KAAK,IAAI,aAAa,IAAI,GAAG,KAAK,IAAI,MAAM,GAAG;IACtG;IACA;;;;;IAKA,GACA,IAAI,UAAU;QAAE,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG;IAAM;IACzE,mEAAmE;IACnE,oCAAoC;IACpC;;IAEA,GACA,UAAU,KAAK,EAAE,KAAK,EAAE;QACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;QACvE,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,wBAAwB;IACxB;;IAEA,GACA,OAAO,MAAM,EAAE;QACX,IAAI;QACJ,IAAI,QAAQ,UAAU,GAAG,2BAA2B,KAAI,OAAO,SAAS,MAAM,oBAAoB;QAClG,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;QACvB,IAAI,kBAAkB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,oBAAoB;QACzE,IAAI,iBACA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG;QAC9B,IAAI,QAAQ,OAAO,iBAAiB,CAAC;QACrC,IAAI,OACA,IAAI,CAAC,KAAK,IAAI;QAClB,IAAI,SAAS,GAAG;YACZ,IAAI,CAAC,SAAS,CAAC,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,OAAO,IAAI,CAAC,SAAS;YACrE,6DAA6D;YAC7D,0CAA0C;YAC1C,IAAI,OAAO,OAAO,aAAa,EAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,kBAAkB,IAAI,GAAG;YAClF,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,SAAS;YACvC;QACJ;QACA,kEAAkE;QAClE,8DAA8D;QAC9D,kEAAkE;QAClE,kEAAkE;QAClE,6BAA6B;QAC7B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAI,CAAC,QAAQ,CAAC,IAAI,IAAK,CAAC,SAAS,OAAO,mBAAmB,MAAK,IAAI,CAAC;QACjG,IAAI,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,SAAS,GAAG;QACzF,kEAAkE;QAClE,gEAAgE;QAChE,0DAA0D;QAC1D,IAAI,QAAQ,KAAK,2BAA2B,OAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,GAAG;YAC/I,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC,qBAAqB,EAAE;gBACvC,IAAI,CAAC,CAAC,CAAC,iBAAiB;gBACxB,IAAI,CAAC,CAAC,CAAC,oBAAoB,GAAG;YAClC,OACK,IAAI,IAAI,CAAC,CAAC,CAAC,oBAAoB,GAAG,MAAM;gBACzC,IAAI,CAAC,CAAC,CAAC,iBAAiB,GAAG;gBAC3B,IAAI,CAAC,CAAC,CAAC,qBAAqB,GAAG;gBAC/B,IAAI,CAAC,CAAC,CAAC,oBAAoB,GAAG;YAClC;QACJ;QACA,IAAI,aAAa,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,GAAG,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;QACjG,qDAAqD;QACrD,IAAI,OAAO,OAAO,aAAa,IAAK,SAAS,OAAO,qBAAqB,KAAK;YAC1E,IAAI,MAAM,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,qBAAqB,OAAM,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS;YAC7F,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,KAAK,QAAQ,GAAG;QAChD;QACA,IAAI,SAAS,OAAO,mBAAmB,KAAI;YACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK;QACjC,OACK;YACD,IAAI,cAAc,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtC,IAAI,CAAC,KAAK,GAAG,OAAO,OAAO,CAAC,aAAa,MAAM;QACnD;QACA,MAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KACvB,IAAI,CAAC,KAAK,CAAC,GAAG;QAClB,IAAI,CAAC,aAAa,CAAC,MAAM;IAC7B;IACA,gCAAgC;IAChC;;IAEA,GACA,UAAU,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,WAAW,KAAK,EAAE;QACpD,IAAI,QAAQ,EAAE,YAAY,OACtB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG;YAClG,yCAAyC;YACzC,IAAI,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM;YACxC,IAAI,OAAO,KAAK,IAAI,MAAM,EAAE;gBACxB,MAAM,IAAI,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU;gBAC5C,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,YAAY,OAAM,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;gBAChF,IAAI,SAAS,KACT;gBACJ,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,OAAO;oBAC9B,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG;oBACtB;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,YAAY,IAAI,CAAC,GAAG,IAAI,KAAK;YAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,OAAO,KAAK;QACvC,OACK;YACD,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM;YAC9B,IAAI,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAI;gBACzD,IAAI,WAAW;gBACf,IAAK,IAAI,OAAO,OAAO,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,KAAK,QAAQ,EAAG;oBACvE,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,GAAG;wBAC5B,WAAW;wBACX;oBACJ;gBACJ;gBACA,IAAI,UACA,MAAO,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,IAAK;oBAC9C,2BAA2B;oBAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;oBAC3C,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;oBAC/C,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;oBAC/C,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;oBAC/C,SAAS;oBACT,IAAI,OAAO,GACP,QAAQ;gBAChB;YACR;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;YACrB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG;YACzB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG;YACzB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG;QAC7B;IACJ;IACA,uBAAuB;IACvB;;IAEA,GACA,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE;QAC5B,IAAI,SAAS,OAAO,mBAAmB,KAAI;YACvC,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,oBAAoB,KAAI,IAAI,CAAC,GAAG;QAClE,OACK,IAAI,CAAC,SAAS,OAAO,mBAAmB,GAAE,KAAK,GAAG;YACnD,IAAI,YAAY,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;YAC3C,IAAI,MAAM,IAAI,CAAC,GAAG,IAAI,QAAQ,OAAO,OAAO,EAAE;gBAC1C,IAAI,CAAC,GAAG,GAAG;gBACX,IAAI,CAAC,OAAO,SAAS,CAAC,WAAW,EAAE,qBAAqB,MACpD,IAAI,CAAC,SAAS,GAAG;YACzB;YACA,IAAI,CAAC,SAAS,CAAC,WAAW;YAC1B,IAAI,CAAC,YAAY,CAAC,MAAM;YACxB,IAAI,QAAQ,OAAO,OAAO,EACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,OAAO,KAAK;QAC3C,OACK;YACD,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,YAAY,CAAC,MAAM;YACxB,IAAI,QAAQ,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,OAAO,KAAK;QAC3C;IACJ;IACA,kBAAkB;IAClB;;IAEA,GACA,MAAM,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE;QACpC,IAAI,SAAS,MAAM,qBAAqB,KACpC,IAAI,CAAC,MAAM,CAAC;aAEZ,IAAI,CAAC,KAAK,CAAC,QAAQ,MAAM,WAAW;IAC5C;IACA,gDAAgD;IAChD;;IAEA,GACA,QAAQ,KAAK,EAAE,IAAI,EAAE;QACjB,IAAI,QAAQ,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG;QACnC,IAAI,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO;YAC5C,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;YACnB;QACJ;QACA,IAAI,QAAQ,IAAI,CAAC,GAAG;QACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,QAAQ,MAAM,MAAM;QAChD,IAAI,CAAC,SAAS,CAAC,MAAM;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,2CAA2C;QAC7F,IAAI,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,MAAM;IAC1I;IACA,0DAA0D;IAC1D,iEAAiE;IACjE,aAAa;IACb;;IAEA,GACA,QAAQ;QACJ,IAAI,SAAS,IAAI;QACjB,IAAI,MAAM,OAAO,MAAM,CAAC,MAAM;QAC9B,gEAAgE;QAChE,+DAA+D;QAC/D,kEAAkE;QAClE,4DAA4D;QAC5D,MAAO,MAAM,KAAK,OAAO,MAAM,CAAC,MAAM,EAAE,GAAG,OAAO,SAAS,CACvD,OAAO;QACX,IAAI,SAAS,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,OAAO,OAAO,UAAU,GAAG;QAClE,uFAAuF;QACvF,MAAO,UAAU,QAAQ,OAAO,UAAU,CACtC,SAAS,OAAO,MAAM;QAC1B,OAAO,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,MAAM,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE;IAClJ;IACA,mEAAmE;IACnE;;IAEA,GACA,gBAAgB,IAAI,EAAE,OAAO,EAAE;QAC3B,IAAI,SAAS,QAAQ,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO;QAC1C,IAAI,QACA,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,SAAS;QAC5C,IAAI,CAAC,SAAS,CAAC,EAAE,YAAY,KAAI,IAAI,CAAC,GAAG,EAAE,SAAS,SAAS,IAAI;QACjE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG;QAC5B,IAAI,CAAC,KAAK,IAAI,IAAI,kBAAkB;IACxC;IACA;;;;;IAKA,GACA,SAAS,IAAI,EAAE;QACX,IAAK,IAAI,MAAM,IAAI,eAAe,IAAI,IAAK;YACvC,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,EAAE,EAAE,4BAA4B,QAAO,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,EAAE;YACxH,IAAI,UAAU,GACV,OAAO;YACX,IAAI,CAAC,SAAS,MAAM,qBAAqB,GAAE,KAAK,GAC5C,OAAO;YACX,IAAI,MAAM,CAAC;QACf;IACJ;IACA,iEAAiE;IACjE,sCAAsC;IACtC;;IAEA,GACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,+BAA+B,KACxD,OAAO,EAAE;QACb,IAAI,aAAa,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK;QACpD,IAAI,WAAW,MAAM,GAAG,EAAE,mBAAmB,OAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,kCAAkC,KAAI;YACnH,IAAI,OAAO,EAAE;YACb,IAAK,IAAI,IAAI,GAAG,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;gBAC9C,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,OACpE,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE;YACjC;YACA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,kCAAkC,KAC1D,IAAK,IAAI,IAAI,GAAG,KAAK,MAAM,GAAG,EAAE,mBAAmB,OAAM,KAAK,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;gBACzF,IAAI,IAAI,UAAU,CAAC,IAAI,EAAE;gBACzB,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,AAAC,IAAI,KAAM,KAAK,IACrC,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE;YACjC;YACJ,aAAa;QACjB;QACA,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,IAAI,OAAO,MAAM,GAAG,EAAE,mBAAmB,KAAI,KAAK,EAAG;YACtF,IAAI,IAAI,UAAU,CAAC,IAAI,EAAE;YACzB,IAAI,KAAK,IAAI,CAAC,KAAK,EACf;YACJ,IAAI,QAAQ,IAAI,CAAC,KAAK;YACtB,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG;YAC3B,MAAM,SAAS,CAAC,EAAE,YAAY,KAAI,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE,GAAG;YAC3D,MAAM,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG;YAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG;YAC1B,MAAM,KAAK,IAAI,IAAI,kBAAkB;YACrC,OAAO,IAAI,CAAC;QAChB;QACA,OAAO;IACX;IACA,0DAA0D;IAC1D,WAAW;IACX;;IAEA,GACA,cAAc;QACV,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;QACvB,IAAI,SAAS,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,2BAA2B;QACvE,IAAI,CAAC,SAAS,MAAM,qBAAqB,GAAE,KAAK,GAC5C,OAAO;QACX,IAAI,CAAC,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS;YACzC,IAAI,QAAQ,UAAU,GAAG,2BAA2B,KAAI,OAAO,SAAS,MAAM,oBAAoB;YAClG,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,QAAQ;YACzC,IAAI,SAAS,KAAK,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,SAAS,GAAG;gBACnE,IAAI,SAAS,IAAI,CAAC,mBAAmB;gBACrC,IAAI,UAAU,MACV,OAAO;gBACX,SAAS;YACb;YACA,IAAI,CAAC,SAAS,CAAC,EAAE,YAAY,KAAI,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG;YACxD,IAAI,CAAC,KAAK,IAAI,IAAI,kBAAkB;QACxC;QACA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG;QACzB,IAAI,CAAC,MAAM,CAAC;QACZ,OAAO;IACX;IACA;;;;IAIA,GACA,sBAAsB;QAClB,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE;QAClC,IAAI,UAAU,CAAC,OAAO;YAClB,IAAI,KAAK,QAAQ,CAAC,QACd;YACJ,KAAK,IAAI,CAAC;YACV,OAAO,OAAO,UAAU,CAAC,OAAO,CAAC;gBAC7B,IAAI,SAAS,CAAC,OAAO,mBAAmB,MAAK,OAAO,mBAAmB,GAAE;qBACpE,IAAI,SAAS,MAAM,qBAAqB,KAAI;oBAC7C,IAAI,SAAS,CAAC,UAAU,GAAG,2BAA2B,GAAE,IAAI;oBAC5D,IAAI,SAAS,GAAG;wBACZ,IAAI,OAAO,SAAS,MAAM,oBAAoB,KAAI,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS;wBACxF,IAAI,UAAU,KAAK,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,UAAU,GAClE,OAAO,AAAC,UAAU,GAAG,2BAA2B,MAAM,MAAM,qBAAqB,MAAK;oBAC9F;gBACJ,OACK;oBACD,IAAI,QAAQ,QAAQ,QAAQ,QAAQ;oBACpC,IAAI,SAAS,MACT,OAAO;gBACf;YACJ;QACJ;QACA,OAAO,QAAQ,IAAI,CAAC,KAAK,EAAE;IAC/B;IACA;;IAEA,GACA,WAAW;QACP,MAAO,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,uBAAuB,KAAK;YACtE,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI;gBACrB,IAAI,CAAC,SAAS,CAAC,EAAE,YAAY,KAAI,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG;gBACxD;YACJ;QACJ;QACA,OAAO,IAAI;IACf;IACA;;;;IAIA,GACA,IAAI,UAAU;QACV,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,GACrB,OAAO;QACX,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;QACvB,OAAO,OAAO,IAAI,CAAC,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,sBAAsB,KAAI,IAAI,MAAM,WAAW,OAC7F,CAAC,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,4BAA4B;IACpE;IACA;;;;IAIA,GACA,UAAU;QACN,IAAI,CAAC,SAAS,CAAC,EAAE,YAAY,KAAI,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG;QACxD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;IACxB;IACA;;IAEA,GACA,UAAU,KAAK,EAAE;QACb,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,MAAM,KAAK,CAAC,MAAM,EACpE,OAAO;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EACxC,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,EAC/B,OAAO;QACf,OAAO;IACX;IACA;;IAEA,GACA,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM;IAAE;IACrC;;;IAGA,GACA,eAAe,SAAS,EAAE;QAAE,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU;IAAE;IAC3E,aAAa,IAAI,EAAE,KAAK,EAAE;QACtB,IAAI,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;IAClH;IACA,cAAc,IAAI,EAAE,KAAK,EAAE;QACvB,IAAI,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;IACnH;IACA;;IAEA,GACA,cAAc;QACV,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;QAChC,IAAI,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,GAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;IACpE;IACA;;IAEA,GACA,gBAAgB;QACZ,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;QAChC,IAAI,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,GAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;IAC9D;IACA,cAAc,OAAO,EAAE;QACnB,IAAI,WAAW,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACpC,IAAI,QAAQ,IAAI,aAAa,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACtD,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,EAClC,IAAI,CAAC,WAAW;YACpB,IAAI,CAAC,UAAU,GAAG;QACtB;IACJ;IACA;;IAEA,GACA,aAAa,SAAS,EAAE;QACpB,IAAI,YAAY,IAAI,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,SAAS,GAAG;QACrB;IACJ;IACA;;IAEA,GACA,QAAQ;QACJ,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,EACjD,IAAI,CAAC,WAAW;QACpB,IAAI,IAAI,CAAC,SAAS,GAAG,GACjB,IAAI,CAAC,aAAa;IAC1B;AACJ;AACA,MAAM;IACF,YAAY,OAAO,EAAE,OAAO,CAAE;QAC1B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG,QAAQ,MAAM,GAAG,QAAQ,IAAI,CAAC,WAAW;IACzD;AACJ;AACA,qEAAqE;AACrE,kBAAkB;AAClB,MAAM;IACF,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;QACxB,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;QACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;IACjC;IACA,OAAO,MAAM,EAAE;QACX,IAAI,OAAO,SAAS,MAAM,oBAAoB,KAAI,QAAQ,UAAU,GAAG,2BAA2B;QAClG,IAAI,SAAS,GAAG;YACZ,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAC9B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK;YACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG;YAC/B,IAAI,CAAC,IAAI,IAAI;QACjB,OACK;YACD,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI;QAC/B;QACA,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,EAAE,MAAM;QACxE,IAAI,CAAC,KAAK,GAAG;IACjB;AACJ;AACA,oEAAoE;AACpE,wDAAwD;AACxD,MAAM;IACF,YAAY,KAAK,EAAE,GAAG,EAAE,KAAK,CAAE;QAC3B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;QAC1B,IAAI,IAAI,CAAC,KAAK,IAAI,GACd,IAAI,CAAC,SAAS;IACtB;IACA,OAAO,OAAO,KAAK,EAAE,MAAM,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,EAAE;QAC/D,OAAO,IAAI,kBAAkB,OAAO,KAAK,MAAM,MAAM,UAAU;IACnE;IACA,YAAY;QACR,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;QAC5B,IAAI,QAAQ,MAAM;YACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,UAAU;YACpD,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QAC7B;IACJ;IACA,IAAI,KAAK;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE;IAAE;IAC/C,IAAI,QAAQ;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE;IAAE;IAClD,IAAI,MAAM;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE;IAAE;IAChD,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE;IAAE;IACjD,OAAO;QACH,IAAI,CAAC,KAAK,IAAI;QACd,IAAI,CAAC,GAAG,IAAI;QACZ,IAAI,IAAI,CAAC,KAAK,IAAI,GACd,IAAI,CAAC,SAAS;IACtB;IACA,OAAO;QACH,OAAO,IAAI,kBAAkB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK;IACjE;AACJ;AAEA,oEAAoE;AACpE,YAAY;AACZ,SAAS,YAAY,KAAK,EAAE,OAAO,WAAW;IAC1C,IAAI,OAAO,SAAS,UAChB,OAAO;IACX,IAAI,QAAQ;IACZ,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,MAAM,MAAM,EAAG;QAC5C,IAAI,QAAQ;QACZ,OAAS;YACL,IAAI,OAAO,MAAM,UAAU,CAAC,QAAQ,OAAO;YAC3C,IAAI,QAAQ,IAAI,qBAAqB,KAAI;gBACrC,QAAQ,MAAM,iBAAiB;gBAC/B;YACJ;YACA,IAAI,QAAQ,GAAG,eAAe,KAC1B;YACJ,IAAI,QAAQ,GAAG,eAAe,KAC1B;YACJ,IAAI,QAAQ,OAAO,GAAG,gBAAgB;YACtC,IAAI,SAAS,GAAG,eAAe,KAAI;gBAC/B,SAAS,GAAG,eAAe;gBAC3B,OAAO;YACX;YACA,SAAS;YACT,IAAI,MACA;YACJ,SAAS,GAAG,eAAe;QAC/B;QACA,IAAI,OACA,KAAK,CAAC,MAAM,GAAG;aAEf,QAAQ,IAAI,KAAK;IACzB;IACA,OAAO;AACX;AAEA,MAAM;IACF,aAAc;QACV,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,IAAI,CAAC,GAAG,GAAG,CAAC;QACZ,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AACA,MAAM,YAAY,IAAI;AACtB;;;;;AAKA,GACA,MAAM;IACF;;IAEA,GACA,YACA;;IAEA,GACA,KAAK,EACL;;IAEA,GACA,MAAM,CAAE;QACJ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd;;QAEA,GACA,IAAI,CAAC,KAAK,GAAG;QACb;;QAEA,GACA,IAAI,CAAC,QAAQ,GAAG;QAChB;;QAEA,GACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;QACjB;;;QAGA,GACA,IAAI,CAAC,IAAI,GAAG,CAAC;QACb;;QAEA,GACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI;QACzC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,EAAE;QACtB,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE;QACvC,IAAI,CAAC,QAAQ;IACjB;IACA;;IAEA,GACA,cAAc,MAAM,EAAE,KAAK,EAAE;QACzB,IAAI,QAAQ,IAAI,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC,UAAU;QAC/C,IAAI,MAAM,IAAI,CAAC,GAAG,GAAG;QACrB,MAAO,MAAM,MAAM,IAAI,CAAE;YACrB,IAAI,CAAC,OACD,OAAO;YACX,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM;YAC/B,OAAO,MAAM,IAAI,GAAG,KAAK,EAAE;YAC3B,QAAQ;QACZ;QACA,MAAO,QAAQ,IAAI,MAAM,MAAM,EAAE,GAAG,OAAO,MAAM,EAAE,CAAE;YACjD,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAC9B,OAAO;YACX,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM;YAC/B,OAAO,KAAK,IAAI,GAAG,MAAM,EAAE;YAC3B,QAAQ;QACZ;QACA,OAAO;IACX;IACA;;IAEA,GACA,QAAQ,GAAG,EAAE;QACT,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,EAC7C,OAAO;QACX,KAAK,IAAI,SAAS,IAAI,CAAC,MAAM,CACzB,IAAI,MAAM,EAAE,GAAG,KACX,OAAO,KAAK,GAAG,CAAC,KAAK,MAAM,IAAI;QACvC,OAAO,IAAI,CAAC,GAAG;IACnB;IACA;;;;;;;;;;IAUA,GACA,KAAK,MAAM,EAAE;QACT,IAAI,MAAM,IAAI,CAAC,QAAQ,GAAG,QAAQ,KAAK;QACvC,IAAI,OAAO,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACrC,MAAM,IAAI,CAAC,GAAG,GAAG;YACjB,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;QACnC,OACK;YACD,IAAI,WAAW,IAAI,CAAC,aAAa,CAAC,QAAQ;YAC1C,IAAI,YAAY,MACZ,OAAO,CAAC;YACZ,MAAM;YACN,IAAI,OAAO,IAAI,CAAC,SAAS,IAAI,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBACpE,SAAS,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,SAAS;YACxD,OACK;gBACD,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,KAAK;gBAC3C,MAAO,MAAM,EAAE,IAAI,IACf,QAAQ,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;gBAC5B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG;gBAChD,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,EAAE,EACnC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,GAAG;gBAClD,SAAS,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACpC;QACJ;QACA,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,EAC3B,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM;QACjC,OAAO;IACX;IACA;;;;IAIA,GACA,YAAY,KAAK,EAAE,YAAY,CAAC,EAAE;QAC9B,IAAI,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,GAAG;QAClE,IAAI,OAAO,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EACrC,MAAM,IAAI,WAAW;QACzB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;QACnB,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG;IACrB;IACA;;IAEA,GACA,cAAc,KAAK,EAAE,MAAM,EAAE;QACzB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;QACnB,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG;IACrB;IACA,WAAW;QACP,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC9E,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI;YAC9B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM;YACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS;YAC9B,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ;QAC5C,OACK;YACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK;YACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ;YAC9B,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;YACzC,IAAI,MAAM,IAAI,CAAC,GAAG,GAAG,UAAU,MAAM;YACrC,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,UAAU,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI;YAClF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG;YACxB,IAAI,CAAC,QAAQ,GAAG;QACpB;IACJ;IACA,WAAW;QACP,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACpC,IAAI,CAAC,QAAQ;YACb,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAClC,OAAO,IAAI,CAAC,IAAI,GAAG,CAAC;QAC5B;QACA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ;IAC1D;IACA;;;IAGA,GACA,QAAQ,IAAI,CAAC,EAAE;QACX,IAAI,CAAC,QAAQ,IAAI;QACjB,MAAO,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE,CAAE;YAClC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GACxC,OAAO,IAAI,CAAC,OAAO;YACvB,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG;YAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC;YAC3C,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI;QAC9B;QACA,IAAI,CAAC,GAAG,IAAI;QACZ,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAChC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG;QACtC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,UAAU;QACN,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG;QACnC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE;QAClE,IAAI,CAAC,KAAK,GAAG;QACb,OAAO,IAAI,CAAC,IAAI,GAAG,CAAC;IACxB;IACA;;IAEA,GACA,MAAM,GAAG,EAAE,KAAK,EAAE;QACd,IAAI,OAAO;YACP,IAAI,CAAC,KAAK,GAAG;YACb,MAAM,KAAK,GAAG;YACd,MAAM,SAAS,GAAG,MAAM;YACxB,MAAM,KAAK,GAAG,MAAM,QAAQ,GAAG,CAAC;QACpC,OACK;YACD,IAAI,CAAC,KAAK,GAAG;QACjB;QACA,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK;YACjB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE;gBACjB,IAAI,CAAC,OAAO;gBACZ,OAAO,IAAI;YACf;YACA,MAAO,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC;YAC/C,MAAO,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC;YAC/C,IAAI,OAAO,IAAI,CAAC,QAAQ,IAAI,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACjE,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ;YACvC,OACK;gBACD,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,QAAQ,GAAG;YACpB;YACA,IAAI,CAAC,QAAQ;QACjB;QACA,OAAO,IAAI;IACf;IACA;;IAEA,GACA,KAAK,IAAI,EAAE,EAAE,EAAE;QACX,IAAI,QAAQ,IAAI,CAAC,QAAQ,IAAI,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAChE,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,QAAQ;QACpE,IAAI,QAAQ,IAAI,CAAC,SAAS,IAAI,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EACnE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,SAAS;QACvE,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,EAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM;QACjC,IAAI,SAAS;QACb,KAAK,IAAI,KAAK,IAAI,CAAC,MAAM,CAAE;YACvB,IAAI,EAAE,IAAI,IAAI,IACV;YACJ,IAAI,EAAE,EAAE,GAAG,MACP,UAAU,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,EAAE,OAAO,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE;QACzE;QACA,OAAO;IACX;AACJ;AACA;;AAEA,GACA,MAAM;IACF,YAAY,IAAI,EAAE,EAAE,CAAE;QAClB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,EAAE,GAAG;IACd;IACA,MAAM,KAAK,EAAE,KAAK,EAAE;QAChB,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;QACxB,UAAU,IAAI,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,CAAC,EAAE,EAAE,OAAO,IAAI,EAAE,OAAO,cAAc;IAClF;AACJ;AACA,WAAW,SAAS,CAAC,UAAU,GAAG,WAAW,SAAS,CAAC,QAAQ,GAAG,WAAW,SAAS,CAAC,MAAM,GAAG;AAChG;;AAEA,GACA,MAAM;IACF,YAAY,IAAI,EAAE,SAAS,EAAE,SAAS,CAAE;QACpC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,IAAI,GAAG,OAAO,QAAQ,WAAW,YAAY,QAAQ;IAC9D;IACA,MAAM,KAAK,EAAE,KAAK,EAAE;QAChB,IAAI,QAAQ,MAAM,GAAG,EAAE,UAAU;QACjC,OAAS;YACL,IAAI,QAAQ,MAAM,IAAI,GAAG,GAAG,UAAU,MAAM,aAAa,CAAC,GAAG;YAC7D,UAAU,IAAI,CAAC,IAAI,EAAE,OAAO,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS;YAC/D,IAAI,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,GACrB;YACJ,IAAI,IAAI,CAAC,SAAS,IAAI,MAClB;YACJ,IAAI,CAAC,OACD;YACJ,IAAI,WAAW,MACX;YACJ,MAAM,KAAK,CAAC,SAAS,MAAM,KAAK;QACpC;QACA,IAAI,SAAS;YACT,MAAM,KAAK,CAAC,OAAO,MAAM,KAAK;YAC9B,MAAM,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE;QACtC;IACJ;AACJ;AACA,gBAAgB,SAAS,CAAC,UAAU,GAAG,WAAW,SAAS,CAAC,QAAQ,GAAG,WAAW,SAAS,CAAC,MAAM,GAAG;AACrG;;;AAGA,GACA,MAAM;IACF;;;;;;IAMA,GACA,YACA;;IAEA,GACA,KAAK,EAAE,UAAU,CAAC,CAAC,CAAE;QACjB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,QAAQ,UAAU;QACtC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,QAAQ;QAClC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,QAAQ,MAAM;IAClC;AACJ;AACA,mEAAmE;AACnE,SAAS;AACT,EAAE;AACF,sEAAsE;AACtE,mEAAmE;AACnE,qDAAqD;AACrD,EAAE;AACF,kEAAkE;AAClE,YAAY;AACZ,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,2DAA2D;AAC3D,EAAE;AACF,mEAAmE;AACnE,wDAAwD;AACxD,EAAE;AACF,kEAAkE;AAClE,oEAAoE;AACpE,sDAAsD;AACtD,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU;IAC/D,IAAI,QAAQ,GAAG,YAAY,KAAK,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC,MAAM;IACnE,MAAM,OAAS;QACX,IAAI,CAAC,YAAY,IAAI,CAAC,MAAM,KAAK,GAC7B;QACJ,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;QAC5B,oEAAoE;QACpE,oDAAoD;QACpD,oCAAoC;QACpC,IAAK,IAAI,IAAI,QAAQ,GAAG,IAAI,QAAQ,KAAK,EACrC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,SAAS,IAAI,GAAG;YAC/B,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,IAAI,QAAQ,MAAM,CAAC,SACf,CAAC,MAAM,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK,MAAM,KAAK,CAAC,KAAK,IAAI,QAC7C,UAAU,MAAM,MAAM,KAAK,CAAC,KAAK,EAAE,WAAW,WAAW,GAAG;gBAChE,MAAM,WAAW,CAAC;gBAClB;YACJ;QACJ;QACJ,IAAI,OAAO,MAAM,IAAI,EAAE,MAAM,GAAG,OAAO,IAAI,CAAC,QAAQ,EAAE;QACtD,uBAAuB;QACvB,IAAI,MAAM,IAAI,GAAG,KAAK,OAAO,OAAO,IAAI,CAAC,SAAS,OAAO,IAAI,EAAE,IAAI,MAAM,WAAW,KAAI;YACpF,QAAQ,IAAI,CAAC,SAAS,OAAO,IAAI,EAAE;YACnC,SAAS;QACb;QACA,0CAA0C;QAC1C,MAAO,MAAM,MAAO;YAChB,IAAI,MAAM,AAAC,MAAM,QAAS;YAC1B,IAAI,QAAQ,SAAS,MAAM,CAAC,OAAO,CAAC;YACpC,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,IAAI;YAChD,IAAI,OAAO,MACP,OAAO;iBACN,IAAI,QAAQ,IACb,MAAM,MAAM;iBACX;gBACD,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBACvB,MAAM,OAAO;gBACb,SAAS;YACb;QACJ;QACA;IACJ;AACJ;AACA,SAAS,WAAW,IAAI,EAAE,KAAK,EAAE,IAAI;IACjC,IAAK,IAAI,IAAI,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,KAAK,MAAM,WAAW,KAAI,IAC/D,IAAI,QAAQ,MACR,OAAO,IAAI;IACnB,OAAO,CAAC;AACZ;AACA,SAAS,UAAU,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW;IAClD,IAAI,QAAQ,WAAW,WAAW,aAAa;IAC/C,OAAO,QAAQ,KAAK,WAAW,WAAW,aAAa,SAAS;AACpE;AAEA,sDAAsD;AACtD,MAAM,UAAU,OAAO,gKAAA,CAAA,UAAO,IAAI,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,IAAI,YAAY,IAAI,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,GAAG;AAChG,IAAI,WAAW;AACf,SAAS,MAAM,IAAI,EAAE,GAAG,EAAE,IAAI;IAC1B,IAAI,SAAS,KAAK,MAAM,CAAC,qJAAA,CAAA,WAAQ,CAAC,gBAAgB;IAClD,OAAO,MAAM,CAAC;IACd,OAAS;QACL,IAAI,CAAC,CAAC,OAAO,IAAI,OAAO,WAAW,CAAC,OAAO,OAAO,UAAU,CAAC,IAAI,GAC7D,OAAS;YACL,IAAI,CAAC,OAAO,IAAI,OAAO,EAAE,GAAG,MAAM,OAAO,IAAI,GAAG,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,OAAO,EACxE,OAAO,OAAO,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,EAAE,GAAG,GAAG,MAAM,GAAG,oBAAoB,QAC7E,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE,KAAK,GAAG,CAAC,OAAO,IAAI,GAAG,GAAG,MAAM,GAAG,oBAAoB;YACvF,IAAI,OAAO,IAAI,OAAO,WAAW,KAAK,OAAO,WAAW,IACpD;YACJ,IAAI,CAAC,OAAO,MAAM,IACd,OAAO,OAAO,IAAI,IAAI,KAAK,MAAM;QACzC;IACR;AACJ;AACA,MAAM;IACF,YAAY,SAAS,EAAE,OAAO,CAAE;QAC5B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,CAAC;QACf,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,YAAY;IACrB;IACA,eAAe;QACX,IAAI,KAAK,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG;QAC1F,IAAI,IAAI;YACJ,IAAI,CAAC,QAAQ,GAAG,GAAG,SAAS,GAAG,MAAM,GAAG,IAAI,EAAE,GAAG,IAAI,GAAG,GAAG,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI;YAC3F,IAAI,CAAC,MAAM,GAAG,GAAG,OAAO,GAAG,MAAM,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,EAAE;YACpF,MAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAE;gBACtB,IAAI,CAAC,KAAK,CAAC,GAAG;gBACd,IAAI,CAAC,KAAK,CAAC,GAAG;gBACd,IAAI,CAAC,KAAK,CAAC,GAAG;YAClB;YACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI;YACvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM;YAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAChB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ;QAClC,OACK;YACD,IAAI,CAAC,SAAS,GAAG;QACrB;IACJ;IACA,8DAA8D;IAC9D,OAAO,GAAG,EAAE;QACR,IAAI,MAAM,IAAI,CAAC,SAAS,EACpB,OAAO;QACX,MAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,IACnC,IAAI,CAAC,YAAY;QACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,EACd,OAAO;QACX,OAAS;YACL,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;YAC/B,IAAI,OAAO,GAAG;gBACV,IAAI,CAAC,YAAY;gBACjB,OAAO;YACX;YACA,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK;YACpD,IAAI,SAAS,IAAI,QAAQ,CAAC,MAAM,EAAE;gBAC9B,IAAI,CAAC,KAAK,CAAC,GAAG;gBACd,IAAI,CAAC,KAAK,CAAC,GAAG;gBACd,IAAI,CAAC,KAAK,CAAC,GAAG;gBACd;YACJ;YACA,IAAI,OAAO,IAAI,QAAQ,CAAC,MAAM;YAC9B,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,SAAS,CAAC,MAAM;YACnD,IAAI,QAAQ,KAAK;gBACb,IAAI,CAAC,SAAS,GAAG;gBACjB,OAAO;YACX;YACA,IAAI,gBAAgB,qJAAA,CAAA,OAAI,EAAE;gBACtB,IAAI,SAAS,KAAK;oBACd,IAAI,QAAQ,IAAI,CAAC,QAAQ,EACrB,OAAO;oBACX,IAAI,MAAM,QAAQ,KAAK,MAAM;oBAC7B,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;wBACpB,IAAI,YAAY,KAAK,IAAI,CAAC,qJAAA,CAAA,WAAQ,CAAC,SAAS;wBAC5C,IAAI,CAAC,aAAa,MAAM,YAAY,IAAI,CAAC,QAAQ,CAAC,EAAE,EAChD,OAAO;oBACf;gBACJ;gBACA,IAAI,CAAC,KAAK,CAAC,KAAK;gBAChB,IAAI,QAAQ,KAAK,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM;oBACrD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;oBAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;oBAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBACpB;YACJ,OACK;gBACD,IAAI,CAAC,KAAK,CAAC,KAAK;gBAChB,IAAI,CAAC,SAAS,GAAG,QAAQ,KAAK,MAAM;YACxC;QACJ;IACJ;AACJ;AACA,MAAM;IACF,YAAY,MAAM,EAAE,MAAM,CAAE;QACxB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,MAAM,GAAG,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,IAAI;IACjD;IACA,WAAW,KAAK,EAAE;QACd,IAAI,cAAc;QAClB,IAAI,OAAO;QACX,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,GAAG;QAC3C,IAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,EAAE,EAAE,4BAA4B;QACvE,IAAI,UAAU,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,IAAI,GAAG;QACzD,IAAI,YAAY;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YACxC,IAAI,CAAC,AAAC,KAAK,IAAK,IAAI,KAAK,GACrB;YACJ,IAAI,YAAY,UAAU,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,MAAM,CAAC,EAAE;YACrD,IAAI,QAAQ,CAAC,UAAU,QAAQ,EAC3B;YACJ,IAAI,UAAU,UAAU,IAAI,MAAM,KAAK,IAAI,MAAM,GAAG,IAAI,MAAM,IAAI,IAAI,QAAQ,MAAM,OAAO,IAAI,SAAS;gBACpG,IAAI,CAAC,iBAAiB,CAAC,OAAO,WAAW;gBACzC,MAAM,IAAI,GAAG;gBACb,MAAM,OAAO,GAAG;YACpB;YACA,IAAI,MAAM,SAAS,GAAG,MAAM,GAAG,GAAG,GAAG,oBAAoB,KACrD,YAAY,KAAK,GAAG,CAAC,MAAM,SAAS,EAAE;YAC1C,IAAI,MAAM,KAAK,IAAI,EAAE,YAAY,KAAI;gBACjC,IAAI,aAAa;gBACjB,IAAI,MAAM,QAAQ,GAAG,CAAC,GAClB,cAAc,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,QAAQ,EAAE,MAAM,GAAG,EAAE;gBACpE,cAAc,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,KAAK,EAAE,MAAM,GAAG,EAAE;gBAC7D,IAAI,CAAC,UAAU,MAAM,EAAE;oBACnB,OAAO;oBACP,IAAI,cAAc,YACd;gBACR;YACJ;QACJ;QACA,MAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,YACzB,IAAI,CAAC,OAAO,CAAC,GAAG;QACpB,IAAI,WACA,MAAM,YAAY,CAAC;QACvB,IAAI,CAAC,QAAQ,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YACvC,OAAO,IAAI;YACX,KAAK,KAAK,GAAG,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO;YACnC,KAAK,KAAK,GAAG,KAAK,GAAG,GAAG,MAAM,GAAG;YACjC,cAAc,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE,KAAK,GAAG,EAAE;QAC/D;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,aAAa,KAAK,EAAE;QAChB,IAAI,IAAI,CAAC,SAAS,EACd,OAAO,IAAI,CAAC,SAAS;QACzB,IAAI,OAAO,IAAI,aAAa,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG;QACzC,KAAK,KAAK,GAAG;QACb,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,MAAM,GAAG,EAAE,MAAM,CAAC,GAAG;QACzC,KAAK,KAAK,GAAG,OAAO,EAAE,MAAM,CAAC,GAAG,GAAG,EAAE,MAAM,CAAC,OAAO,GAAG,EAAE,YAAY;QACpE,OAAO;IACX;IACA,kBAAkB,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE;QACvC,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG;QACzC,UAAU,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,QAAQ;QACjD,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG;YAClB,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,WAAW,CAAC,MAAM,EAAE,IAC3C,IAAI,OAAO,WAAW,CAAC,EAAE,IAAI,MAAM,KAAK,EAAE;gBACtC,IAAI,SAAS,OAAO,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,EAAE,MAAM,GAAG,GAAG;gBAC9E,IAAI,UAAU,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,IAAI;oBAC3D,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,yBAAyB,KAC3C,MAAM,KAAK,GAAG,UAAU;yBAExB,MAAM,QAAQ,GAAG,UAAU;oBAC/B;gBACJ;YACJ;QACR,OACK;YACD,MAAM,KAAK,GAAG,EAAE,YAAY;YAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ;QAC5C;IACJ;IACA,UAAU,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;QACjC,8BAA8B;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,KAAK,EAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,QACnB,OAAO;QACf,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG;QACxB,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG;QACxB,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG;QACxB,OAAO;IACX;IACA,WAAW,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;QACjC,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG;QACxD,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;YAC9B,IAAK,IAAI,IAAI,OAAO,SAAS,CAAC,OAAO,MAAM,EAAE,mBAAmB,MAAK,EAAE,sBAAsB,OAAM,KAAK,EAAG;gBACvG,IAAI,IAAI,CAAC,EAAE,IAAI,MAAM,WAAW,KAAI;oBAChC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,KAAI;wBACjC,IAAI,KAAK,MAAM,IAAI;oBACvB,OACK;wBACD,IAAI,SAAS,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,KAC5C,QAAQ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM,IAAI,IAAI,OAAO,KAAK;wBAC1D;oBACJ;gBACJ;gBACA,IAAI,IAAI,CAAC,EAAE,IAAI,OACX,QAAQ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM,IAAI,IAAI,OAAO,KAAK;YAC9D;QACJ;QACA,OAAO;IACX;AACJ;AACA,MAAM;IACF,YAAY,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAE;QAC1C,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,WAAW,GAAG,QAAQ,2CAA2C;QACtE,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,qBAAqB,GAAG,CAAC;QAC9B,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,YAAY,OAAO;QACrC,IAAI,CAAC,MAAM,GAAG,IAAI,WAAW,QAAQ,IAAI,CAAC,MAAM;QAChD,IAAI,CAAC,OAAO,GAAG,OAAO,GAAG,CAAC,EAAE;QAC5B,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,EAAE;QACxB,IAAI,CAAC,MAAM,GAAG;YAAC,MAAM,KAAK,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,EAAE,EAAE;SAAM;QACtD,IAAI,CAAC,SAAS,GAAG,UAAU,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,OAAO,OAAO,YAAY,GAAG,IAC9E,IAAI,eAAe,WAAW,OAAO,OAAO,IAAI;IAC1D;IACA,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,iEAAiE;IACjE,kEAAkE;IAClE,kEAAkE;IAClE,EAAE;IACF,mEAAmE;IACnE,0BAA0B;IAC1B,UAAU;QACN,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,WAAW;QAChD,sCAAsC;QACtC,IAAI,YAAY,IAAI,CAAC,MAAM,GAAG,EAAE;QAChC,IAAI,SAAS;QACb,+DAA+D;QAC/D,+DAA+D;QAC/D,qDAAqD;QACrD,6DAA6D;QAC7D,iEAAiE;QACjE,uDAAuD;QACvD,oDAAoD;QACpD,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,wCAAwC,OAAM,OAAO,MAAM,IAAI,GAAG;YAC/F,IAAI,CAAC,EAAE,GAAG;YACV,MAAO,EAAE,WAAW,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,IAAI,IAAI,CAAC,qBAAqB,CAAE,CAAE;YACzG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,GAAG;QACzD;QACA,4DAA4D;QAC5D,4DAA4D;QAC5D,iCAAiC;QACjC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACpC,IAAI,QAAQ,MAAM,CAAC,EAAE;YACrB,OAAS;gBACL,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG;gBACxB,IAAI,MAAM,GAAG,GAAG,KAAK;oBACjB,UAAU,IAAI,CAAC;gBACnB,OACK,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,WAAW,SAAS;oBAClD;gBACJ,OACK;oBACD,IAAI,CAAC,SAAS;wBACV,UAAU,EAAE;wBACZ,gBAAgB,EAAE;oBACtB;oBACA,QAAQ,IAAI,CAAC;oBACb,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;oBACnC,cAAc,IAAI,CAAC,IAAI,KAAK,EAAE,IAAI,GAAG;gBACzC;gBACA;YACJ;QACJ;QACA,IAAI,CAAC,UAAU,MAAM,EAAE;YACnB,IAAI,WAAW,WAAW,aAAa;YACvC,IAAI,UAAU;gBACV,IAAI,SACA,QAAQ,GAAG,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC;gBAC9C,OAAO,IAAI,CAAC,WAAW,CAAC;YAC5B;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBACpB,IAAI,WAAW,SACX,QAAQ,GAAG,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,MAAM;gBACxH,MAAM,IAAI,YAAY,iBAAiB;YAC3C;YACA,IAAI,CAAC,IAAI,CAAC,UAAU,EAChB,IAAI,CAAC,UAAU,GAAG,EAAE,gBAAgB;QAC5C;QACA,IAAI,IAAI,CAAC,UAAU,IAAI,SAAS;YAC5B,IAAI,WAAW,IAAI,CAAC,SAAS,IAAI,QAAQ,OAAO,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,EAAE,GAC/E,IAAI,CAAC,WAAW,CAAC,SAAS,eAAe;YAC/C,IAAI,UAAU;gBACV,IAAI,SACA,QAAQ,GAAG,CAAC,kBAAkB,IAAI,CAAC,OAAO,CAAC;gBAC/C,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,QAAQ;YAC7C;QACJ;QACA,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,eAAe,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,2BAA2B;YAC7F,IAAI,UAAU,MAAM,GAAG,cAAc;gBACjC,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;gBAC1C,MAAO,UAAU,MAAM,GAAG,aACtB,UAAU,GAAG;YACrB;YACA,IAAI,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG,MAClC,IAAI,CAAC,UAAU;QACvB,OACK,IAAI,UAAU,MAAM,GAAG,GAAG;YAC3B,6DAA6D;YAC7D,gEAAgE;YAChE,wDAAwD;YACxD,OAAO,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,GAAG,GAAG,IAAK;gBAClD,IAAI,QAAQ,SAAS,CAAC,EAAE;gBACxB,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;oBAC3C,IAAI,QAAQ,SAAS,CAAC,EAAE;oBACxB,IAAI,MAAM,SAAS,CAAC,UAChB,MAAM,MAAM,CAAC,MAAM,GAAG,IAAI,4BAA4B,OAAM,MAAM,MAAM,CAAC,MAAM,GAAG,IAAI,4BAA4B,KAAI;wBACtH,IAAI,CAAC,AAAC,MAAM,KAAK,GAAG,MAAM,KAAK,IAAM,MAAM,MAAM,CAAC,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,AAAC,IAAI,GAAG;4BAClF,UAAU,MAAM,CAAC,KAAK;wBAC1B,OACK;4BACD,UAAU,MAAM,CAAC,KAAK;4BACtB,SAAS;wBACb;oBACJ;gBACJ;YACJ;YACA,IAAI,UAAU,MAAM,GAAG,GAAG,qBAAqB,KAC3C,UAAU,MAAM,CAAC,GAAG,qBAAqB,KAAI,UAAU,MAAM,GAAG,GAAG,qBAAqB;QAChG;QACA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAClC,IAAI,SAAS,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,EACnC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG;QAC3C,OAAO;IACX;IACA,OAAO,GAAG,EAAE;QACR,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ,IAAI,CAAC,SAAS,GAAG,KAC3C,MAAM,IAAI,WAAW;QACzB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,gEAAgE;IAChE,8DAA8D;IAC9D,oEAAoE;IACpE,4DAA4D;IAC5D,aAAa,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;QAC/B,IAAI,QAAQ,MAAM,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI;QACxC,IAAI,OAAO,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS,SAAS;QACpD,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ,QAAQ,IAAI,CAAC,SAAS,EAChD,OAAO,MAAM,WAAW,KAAK,QAAQ;QACzC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,WAAW,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,WAAW,MAAM,UAAU,CAAC,IAAI,GAAG;YAChH,IAAK,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,QAAS;gBACrD,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,OAAO,IAAI,GAAG,OAAO,OAAO,CAAC,MAAM,KAAK,EAAE,OAAO,IAAI,CAAC,EAAE,IAAI,CAAC;gBACtH,IAAI,QAAQ,CAAC,KAAK,OAAO,MAAM,IAAI,CAAC,CAAC,YAAY,CAAC,OAAO,IAAI,CAAC,qJAAA,CAAA,WAAQ,CAAC,WAAW,KAAK,CAAC,KAAK,MAAM,GAAG;oBAClG,MAAM,OAAO,CAAC,QAAQ;oBACtB,IAAI,SACA,QAAQ,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,EAAE,OAAO,OAAO,CAAC,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;oBAChG,OAAO;gBACX;gBACA,IAAI,CAAC,CAAC,kBAAkB,qJAAA,CAAA,OAAI,KAAK,OAAO,QAAQ,CAAC,MAAM,IAAI,KAAK,OAAO,SAAS,CAAC,EAAE,GAAG,GAClF;gBACJ,IAAI,QAAQ,OAAO,QAAQ,CAAC,EAAE;gBAC9B,IAAI,iBAAiB,qJAAA,CAAA,OAAI,IAAI,OAAO,SAAS,CAAC,EAAE,IAAI,GAChD,SAAS;qBAET;YACR;QACJ;QACA,IAAI,gBAAgB,OAAO,SAAS,CAAC,MAAM,KAAK,EAAE,EAAE,4BAA4B;QAChF,IAAI,gBAAgB,GAAG;YACnB,MAAM,MAAM,CAAC;YACb,IAAI,SACA,QAAQ,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,oBAAoB,EAAE,OAAO,OAAO,CAAC,gBAAgB,MAAM,oBAAoB,KAAI,CAAC,CAAC;YACnI,OAAO;QACX;QACA,IAAI,MAAM,KAAK,CAAC,MAAM,IAAI,KAAK,gBAAgB,KAAI;YAC/C,MAAO,MAAM,KAAK,CAAC,MAAM,GAAG,KAAK,aAAa,OAAM,MAAM,WAAW,GAAI,CAAE;QAC/E;QACA,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAG;YACjC,IAAI,SAAS,OAAO,CAAC,IAAI,EAAE,OAAO,OAAO,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC,IAAI;YAClE,IAAI,OAAO,KAAK,QAAQ,MAAM,IAAI,CAAC;YACnC,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK;YAC3C,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS;YAChC,WAAW,KAAK,CAAC,QAAQ,MAAM,OAAO,KAAK,KAAK,GAAG,WAAW,GAAG,EAAE;YACnE,IAAI,SACA,QAAQ,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,SAAS,MAAM,qBAAqB,GAAE,KAAK,IAAI,UACjG,CAAC,UAAU,EAAE,OAAO,OAAO,CAAC,SAAS,MAAM,oBAAoB,MAAK,CAAC,KAAK,EAAE,OAAO,OAAO,CAAC,MAAM,GAAG,EAAE,QAAQ,cAAc,QAAQ,KAAK,UAAU,CAAC,CAAC;YAC/J,IAAI,MACA,OAAO;iBACN,IAAI,WAAW,GAAG,GAAG,OACtB,OAAO,IAAI,CAAC;iBAEZ,MAAM,IAAI,CAAC;QACnB;QACA,OAAO;IACX;IACA,kEAAkE;IAClE,gEAAgE;IAChE,6CAA6C;IAC7C,aAAa,KAAK,EAAE,SAAS,EAAE;QAC3B,IAAI,MAAM,MAAM,GAAG;QACnB,OAAS;YACL,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,MAAM,OAChC,OAAO;YACX,IAAI,MAAM,GAAG,GAAG,KAAK;gBACjB,eAAe,OAAO;gBACtB,OAAO;YACX;QACJ;IACJ;IACA,YAAY,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE;QACnC,IAAI,WAAW,MAAM,YAAY;QACjC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACpC,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,KAAK,EAAE,EAAE,WAAW,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE;YAC9E,IAAI,OAAO,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS,SAAS;YACpD,IAAI,MAAM,OAAO,EAAE;gBACf,IAAI,WACA;gBACJ,YAAY;gBACZ,MAAM,OAAO;gBACb,IAAI,SACA,QAAQ,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS;gBAC7C,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO;gBACpC,IAAI,MACA;YACR;YACA,IAAI,QAAQ,MAAM,KAAK,IAAI,YAAY;YACvC,IAAK,IAAI,IAAI,GAAG,MAAM,WAAW,MAAM,IAAI,GAAG,wBAAwB,KAAI,IAAK;gBAC3E,IAAI,SACA,QAAQ,GAAG,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,SAAS;gBAClD,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO;gBACpC,IAAI,MACA;gBACJ,IAAI,SACA,YAAY,IAAI,CAAC,OAAO,CAAC,SAAS;YAC1C;YACA,KAAK,IAAI,UAAU,MAAM,eAAe,CAAC,OAAQ;gBAC7C,IAAI,SACA,QAAQ,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU;gBAC9C,IAAI,CAAC,YAAY,CAAC,QAAQ;YAC9B;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE;gBAC7B,IAAI,YAAY,MAAM,GAAG,EAAE;oBACvB;oBACA,QAAQ,EAAE,YAAY;gBAC1B;gBACA,MAAM,eAAe,CAAC,OAAO;gBAC7B,IAAI,SACA,QAAQ,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAClG,eAAe,OAAO;YAC1B,OACK,IAAI,CAAC,YAAY,SAAS,KAAK,GAAG,MAAM,KAAK,EAAE;gBAChD,WAAW;YACf;QACJ;QACA,OAAO;IACX;IACA,+CAA+C;IAC/C,YAAY,KAAK,EAAE;QACf,MAAM,KAAK;QACX,OAAO,qJAAA,CAAA,OAAI,CAAC,KAAK,CAAC;YAAE,QAAQ,kBAAkB,MAAM,CAAC;YACjD,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,OAAO,IAAI,CAAC,OAAO;YACnB,iBAAiB,IAAI,CAAC,MAAM,CAAC,YAAY;YACzC,QAAQ,IAAI,CAAC,MAAM;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI;YAC1B,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI;YACvC,eAAe,IAAI,CAAC,MAAM,CAAC,aAAa;QAAC;IACjD;IACA,QAAQ,KAAK,EAAE;QACX,IAAI,KAAK,CAAC,YAAY,CAAC,WAAW,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC;QACpD,IAAI,CAAC,IACD,SAAS,GAAG,CAAC,OAAO,KAAK,OAAO,aAAa,CAAC,IAAI,CAAC,WAAW;QAClE,OAAO,KAAK;IAChB;AACJ;AACA,SAAS,eAAe,KAAK,EAAE,SAAS;IACpC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACvC,IAAI,QAAQ,SAAS,CAAC,EAAE;QACxB,IAAI,MAAM,GAAG,IAAI,MAAM,GAAG,IAAI,MAAM,SAAS,CAAC,QAAQ;YAClD,IAAI,SAAS,CAAC,EAAE,CAAC,KAAK,GAAG,MAAM,KAAK,EAChC,SAAS,CAAC,EAAE,GAAG;YACnB;QACJ;IACJ;IACA,UAAU,IAAI,CAAC;AACnB;AACA,MAAM;IACF,YAAY,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAE;QACjC,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,OAAO,IAAI,EAAE;QAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI;IAAG;AACtE;AACA,MAAM,KAAK,CAAA,IAAK;AAChB;;;;;;;;;;;AAWA,GACA,MAAM;IACF;;IAEA,GACA,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK;QACvB,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI;QAC3B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI;QAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,IAAI;QAC3B,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,IAAI,CAAC,IAAM,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,KAAK;IAClC;AACJ;AACA;;;;AAIA,GACA,MAAM,iBAAiB,qJAAA,CAAA,SAAM;IACzB;;IAEA,GACA,YAAY,IAAI,CAAE;QACd,KAAK;QACL;;QAEA,GACA,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,KAAK,OAAO,IAAI,GAAG,gBAAgB,KACnC,MAAM,IAAI,WAAW,CAAC,gBAAgB,EAAE,KAAK,OAAO,CAAC,iCAAiC,EAAE,GAAG,gBAAgB,IAAG,CAAC,CAAC;QACpH,IAAI,YAAY,KAAK,SAAS,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,UAAU,MAAM;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,eAAe,EAAE,IACtC,UAAU,IAAI,CAAC;QACnB,IAAI,WAAW,OAAO,IAAI,CAAC,KAAK,QAAQ,EAAE,GAAG,CAAC,CAAA,IAAK,KAAK,QAAQ,CAAC,EAAE,CAAC,EAAE;QACtE,IAAI,YAAY,EAAE;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAClC,UAAU,IAAI,CAAC,EAAE;QACrB,SAAS,QAAQ,MAAM,EAAE,IAAI,EAAE,KAAK;YAChC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;gBAAC;gBAAM,KAAK,WAAW,CAAC,OAAO;aAAQ;QAClE;QACA,IAAI,KAAK,SAAS,EACd,KAAK,IAAI,YAAY,KAAK,SAAS,CAAE;YACjC,IAAI,OAAO,QAAQ,CAAC,EAAE;YACtB,IAAI,OAAO,QAAQ,UACf,OAAO,qJAAA,CAAA,WAAQ,CAAC,KAAK;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAG;gBAClC,IAAI,OAAO,QAAQ,CAAC,IAAI;gBACxB,IAAI,QAAQ,GAAG;oBACX,QAAQ,MAAM,MAAM,QAAQ,CAAC,IAAI;gBACrC,OACK;oBACD,IAAI,QAAQ,QAAQ,CAAC,IAAI,CAAC,KAAK;oBAC/B,IAAK,IAAI,IAAI,CAAC,MAAM,IAAI,GAAG,IACvB,QAAQ,QAAQ,CAAC,IAAI,EAAE,MAAM;oBACjC;gBACJ;YACJ;QACJ;QACJ,IAAI,CAAC,OAAO,GAAG,IAAI,qJAAA,CAAA,UAAO,CAAC,UAAU,GAAG,CAAC,CAAC,MAAM,IAAM,qJAAA,CAAA,WAAQ,CAAC,MAAM,CAAC;gBAClE,MAAM,KAAK,IAAI,CAAC,aAAa,GAAG,YAAY;gBAC5C,IAAI;gBACJ,OAAO,SAAS,CAAC,EAAE;gBACnB,KAAK,SAAS,OAAO,CAAC,KAAK,CAAC;gBAC5B,OAAO,KAAK;gBACZ,SAAS,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC;YAClE;QACA,IAAI,KAAK,WAAW,EAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,WAAW;QAC1D,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,YAAY,GAAG,qJAAA,CAAA,sBAAmB;QACvC,IAAI,aAAa,YAAY,KAAK,SAAS;QAC3C,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;QAC3B,IAAI,CAAC,gBAAgB,GAAG,KAAK,WAAW,IAAI,EAAE;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,IAAI,CAAC,gBAAgB,CAAC,MAAM;QAC/D,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAC9C,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,IAAI;QACvD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QAC9C,IAAI,CAAC,MAAM,GAAG,YAAY,KAAK,MAAM,EAAE;QACvC,IAAI,CAAC,IAAI,GAAG,YAAY,KAAK,SAAS;QACtC,IAAI,CAAC,IAAI,GAAG,YAAY,KAAK,IAAI;QACjC,IAAI,CAAC,OAAO,GAAG,KAAK,OAAO;QAC3B,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU,CAAC,GAAG,CAAC,CAAA,QAAS,OAAO,SAAS,WAAW,IAAI,WAAW,YAAY,SAAS;QAC9G,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;QAC7B,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ,IAAI,CAAC;QAClC,IAAI,CAAC,kBAAkB,GAAG,KAAK,kBAAkB,IAAI;QACrD,IAAI,CAAC,cAAc,GAAG,KAAK,SAAS;QACpC,IAAI,CAAC,SAAS,GAAG,KAAK,SAAS,IAAI;QACnC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;QAC3C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY;QAChC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;IAC3D;IACA,YAAY,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE;QAClC,IAAI,QAAQ,IAAI,MAAM,IAAI,EAAE,OAAO,WAAW;QAC9C,KAAK,IAAI,KAAK,IAAI,CAAC,QAAQ,CACvB,QAAQ,EAAE,OAAO,OAAO,WAAW;QACvC,OAAO;IACX;IACA;;IAEA,GACA,QAAQ,KAAK,EAAE,IAAI,EAAE,QAAQ,KAAK,EAAE;QAChC,IAAI,QAAQ,IAAI,CAAC,IAAI;QACrB,IAAI,QAAQ,KAAK,CAAC,EAAE,EAChB,OAAO,CAAC;QACZ,IAAK,IAAI,MAAM,KAAK,CAAC,OAAO,EAAE,GAAI;YAC9B,IAAI,WAAW,KAAK,CAAC,MAAM,EAAE,OAAO,WAAW;YAC/C,IAAI,SAAS,KAAK,CAAC,MAAM;YACzB,IAAI,QAAQ,OACR,OAAO;YACX,IAAK,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC,GAAG,MAAM,KAAK,MAC7C,IAAI,KAAK,CAAC,IAAI,IAAI,OACd,OAAO;YACf,IAAI,MACA,OAAO,CAAC;QAChB;IACJ;IACA;;IAEA,GACA,UAAU,KAAK,EAAE,QAAQ,EAAE;QACvB,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;YAC9B,IAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,MAAM,EAAE,mBAAmB,MAAK,EAAE,sBAAsB,MAAK,OAAO,KAAK,EAAG;gBAC3G,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,KAAK,MAAM,WAAW,KAAI;oBACzC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,KAC7B,OAAO,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,GAAG;yBACjC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,KACnC,OAAO,KAAK,MAAM,IAAI;yBAEtB;gBACR;gBACA,IAAI,QAAQ,YAAY,QAAQ,EAAE,YAAY,KAC1C,OAAO,KAAK,MAAM,IAAI;YAC9B;QACJ;QACA,OAAO;IACX;IACA;;IAEA,GACA,UAAU,KAAK,EAAE,IAAI,EAAE;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,AAAC,QAAQ,EAAE,mBAAmB,MAAM,KAAK;IAChE;IACA;;IAEA,GACA,UAAU,KAAK,EAAE,IAAI,EAAE;QACnB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,oBAAoB,OAAM,IAAI,IAAI;IACtE;IACA;;IAEA,GACA,YAAY,KAAK,EAAE,MAAM,EAAE;QACvB,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAA,IAAK,KAAK,SAAS,OAAO;IAC9D;IACA;;IAEA,GACA,WAAW,KAAK,EAAE,MAAM,EAAE;QACtB,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,4BAA4B;QAChE,IAAI,SAAS,QAAQ,OAAO,SAAS;QACrC,IAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,sBAAsB,MAAK,UAAU,MAAM,KAAK,EAAG;YACpF,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,WAAW,KAAI;gBACrC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,KAClC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI;qBAExB;YACR;YACA,SAAS,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI;QACxC;QACA,OAAO;IACX;IACA;;;IAGA,GACA,WAAW,KAAK,EAAE;QACd,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,sBAAsB,OAAM,KAAK,EAAG;YACrE,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,WAAW,KAAI;gBACrC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,KAClC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI;qBAExB;YACR;YACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAI,MAAM,qBAAqB,OAAM,EAAG,KAAK,GAAG;gBACjE,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAC5B,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,AAAC,IAAI,KAAM,KAAK,QACvC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YAClC;QACJ;QACA,OAAO;IACX;IACA;;;;IAIA,GACA,UAAU,MAAM,EAAE;QACd,8DAA8D;QAC9D,sCAAsC;QACtC,IAAI,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,SAAS,SAAS,GAAG,IAAI;QAChE,IAAI,OAAO,KAAK,EACZ,KAAK,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,KAAK;QACtD,IAAI,OAAO,GAAG,EAAE;YACZ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,CAAC;YACpC,IAAI,CAAC,MACD,MAAM,IAAI,WAAW,CAAC,sBAAsB,EAAE,OAAO,GAAG,EAAE;YAC9D,KAAK,GAAG,GAAG;QACf;QACA,IAAI,OAAO,UAAU,EACjB,KAAK,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;YAClC,IAAI,QAAQ,OAAO,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI;YAClD,OAAO,QAAQ,MAAM,EAAE,GAAG;QAC9B;QACJ,IAAI,OAAO,YAAY,EAAE;YACrB,KAAK,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK;YAC3C,KAAK,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG;gBAClD,IAAI,QAAQ,OAAO,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,EAAE,QAAQ;gBAC9D,IAAI,CAAC,OACD,OAAO;gBACX,IAAI,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI;oBAAE,UAAU,MAAM,EAAE;gBAAC;gBACpE,KAAK,YAAY,CAAC,EAAE,GAAG,eAAe;gBACtC,OAAO;YACX;QACJ;QACA,IAAI,OAAO,cAAc,EACrB,KAAK,OAAO,GAAG,OAAO,cAAc;QACxC,IAAI,OAAO,OAAO,EACd,KAAK,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,OAAO;QACnD,IAAI,OAAO,MAAM,IAAI,MACjB,KAAK,MAAM,GAAG,OAAO,MAAM;QAC/B,IAAI,OAAO,IAAI,EACX,KAAK,QAAQ,GAAG,KAAK,QAAQ,CAAC,MAAM,CAAC,OAAO,IAAI;QACpD,IAAI,OAAO,YAAY,IAAI,MACvB,KAAK,YAAY,GAAG,OAAO,YAAY;QAC3C,OAAO;IACX;IACA;;;IAGA,GACA,cAAc;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;IAClC;IACA;;;;;IAKA,GACA,QAAQ,IAAI,EAAE;QACV,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,OAAO,QAAQ,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI;IACnH;IACA;;;IAGA,GACA,IAAI,UAAU;QAAE,OAAO,IAAI,CAAC,OAAO,GAAG;IAAG;IACzC;;IAEA,GACA,IAAI,UAAU;QAAE,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;IAAE;IACxD;;IAEA,GACA,kBAAkB,IAAI,EAAE;QACpB,IAAI,OAAO,IAAI,CAAC,kBAAkB;QAClC,OAAO,QAAQ,OAAO,IAAI,IAAI,CAAC,KAAK,IAAI;IAC5C;IACA;;IAEA,GACA,aAAa,OAAO,EAAE;QAClB,IAAI,SAAS,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,OAAO,GAAG,CAAC,IAAM;QAClE,IAAI,SACA,KAAK,IAAI,QAAQ,QAAQ,KAAK,CAAC,KAAM;YACjC,IAAI,KAAK,OAAO,OAAO,CAAC;YACxB,IAAI,MAAM,GACN,KAAK,CAAC,GAAG,GAAG;QACpB;QACJ,IAAI,WAAW;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAC/B,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;YACX,IAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,WAAW,KACjF,CAAC,YAAY,CAAC,WAAW,IAAI,WAAW,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG;QAC1E;QACJ,OAAO,IAAI,QAAQ,SAAS,OAAO;IACvC;IACA;;;IAGA,GACA,OAAO,YAAY,IAAI,EAAE;QACrB,OAAO,IAAI,SAAS;IACxB;AACJ;AACA,SAAS,KAAK,IAAI,EAAE,GAAG;IAAI,OAAO,IAAI,CAAC,IAAI,GAAI,IAAI,CAAC,MAAM,EAAE,IAAI;AAAK;AACrE,SAAS,aAAa,MAAM;IACxB,IAAI,OAAO;IACX,KAAK,IAAI,SAAS,OAAQ;QACtB,IAAI,UAAU,MAAM,CAAC,CAAC,SAAS;QAC/B,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG,OAAO,KAC1E,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,EAAE,EAAE,uBAAuB,QAC/D,CAAC,CAAC,QAAQ,KAAK,KAAK,GAAG,MAAM,KAAK,GAClC,OAAO;IACf;IACA,OAAO;AACX;AACA,SAAS,eAAe,IAAI;IACxB,IAAI,KAAK,QAAQ,EAAE;QACf,IAAI,OAAO,KAAK,MAAM,GAAG,EAAE,qBAAqB,MAAK,EAAE,yBAAyB;QAChF,OAAO,CAAC,OAAO,QAAU,AAAC,KAAK,QAAQ,CAAC,OAAO,UAAU,IAAK;IAClE;IACA,OAAO,KAAK,GAAG;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1635, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lezer/css/dist/index.js"], "sourcesContent": ["import { ExternalTokenizer, LRParser, LocalTokenGroup } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst descendantOp = 122,\n  Unit = 1,\n  identifier = 123,\n  callee = 124,\n  VariableName = 2,\n  queryIdentifier = 125,\n  queryVariableName = 3,\n  QueryCallee = 4;\n\n/* Hand-written tokenizers for CSS tokens that can't be\n   expressed by <PERSON><PERSON>'s built-in tokenizer. */\n\nconst space = [9, 10, 11, 12, 13, 32, 133, 160, 5760, 8192, 8193, 8194, 8195, 8196, 8197,\n               8198, 8199, 8200, 8201, 8202, 8232, 8233, 8239, 8287, 12288];\nconst colon = 58, parenL = 40, underscore = 95, bracketL = 91, dash = 45, period = 46,\n      hash = 35, percent = 37, ampersand = 38, backslash = 92, newline = 10, asterisk = 42;\n\nfunction isAlpha(ch) { return ch >= 65 && ch <= 90 || ch >= 97 && ch <= 122 || ch >= 161 }\n\nfunction isDigit(ch) { return ch >= 48 && ch <= 57 }\n\nfunction isHex(ch) { return isDigit(ch) || ch >= 97 && ch <= 102 || ch >= 65 && ch <= 70 }\n\nconst identifierTokens = (id, varName, callee) => (input, stack) => {\n  for (let inside = false, dashes = 0, i = 0;; i++) {\n    let {next} = input;\n    if (isAlpha(next) || next == dash || next == underscore || (inside && isDigit(next))) {\n      if (!inside && (next != dash || i > 0)) inside = true;\n      if (dashes === i && next == dash) dashes++;\n      input.advance();\n    } else if (next == backslash && input.peek(1) != newline) {\n      input.advance();\n      if (isHex(input.next)) {\n        do { input.advance(); } while (isHex(input.next))\n        if (input.next == 32) input.advance();\n      } else if (input.next > -1) {\n        input.advance();\n      }\n      inside = true;\n    } else {\n      if (inside) input.acceptToken(\n        dashes == 2 && stack.canShift(VariableName) ? varName : next == parenL ? callee : id\n      );\n      break\n    }\n  }\n};\n\nconst identifiers = new ExternalTokenizer(\n  identifierTokens(identifier, VariableName, callee)\n);\nconst queryIdentifiers = new ExternalTokenizer(\n  identifierTokens(queryIdentifier, queryVariableName, QueryCallee)\n);\n\nconst descendant = new ExternalTokenizer(input => {\n  if (space.includes(input.peek(-1))) {\n    let {next} = input;\n    if (isAlpha(next) || next == underscore || next == hash || next == period ||\n        next == asterisk || next == bracketL || next == colon && isAlpha(input.peek(1)) ||\n        next == dash || next == ampersand)\n      input.acceptToken(descendantOp);\n  }\n});\n\nconst unitToken = new ExternalTokenizer(input => {\n  if (!space.includes(input.peek(-1))) {\n    let {next} = input;\n    if (next == percent) { input.advance(); input.acceptToken(Unit); }\n    if (isAlpha(next)) {\n      do { input.advance(); } while (isAlpha(input.next) || isDigit(input.next))\n      input.acceptToken(Unit);\n    }\n  }\n});\n\nconst cssHighlighting = styleTags({\n  \"AtKeyword import charset namespace keyframes media supports\": tags.definitionKeyword,\n  \"from to selector\": tags.keyword,\n  NamespaceName: tags.namespace,\n  KeyframeName: tags.labelName,\n  KeyframeRangeName: tags.operatorKeyword,\n  TagName: tags.tagName,\n  ClassName: tags.className,\n  PseudoClassName: tags.constant(tags.className),\n  IdName: tags.labelName,\n  \"FeatureName PropertyName\": tags.propertyName,\n  AttributeName: tags.attributeName,\n  NumberLiteral: tags.number,\n  KeywordQuery: tags.keyword,\n  UnaryQueryOp: tags.operatorKeyword,\n  \"CallTag ValueName\": tags.atom,\n  VariableName: tags.variableName,\n  Callee: tags.operatorKeyword,\n  Unit: tags.unit,\n  \"UniversalSelector NestingSelector\": tags.definitionOperator,\n  \"MatchOp CompareOp\": tags.compareOperator,\n  \"ChildOp SiblingOp, LogicOp\": tags.logicOperator,\n  BinOp: tags.arithmeticOperator,\n  Important: tags.modifier,\n  Comment: tags.blockComment,\n  ColorLiteral: tags.color,\n  \"ParenthesizedContent StringLiteral\": tags.string,\n  \":\": tags.punctuation,\n  \"PseudoOp #\": tags.derefOperator,\n  \"; ,\": tags.separator,\n  \"( )\": tags.paren,\n  \"[ ]\": tags.squareBracket,\n  \"{ }\": tags.brace\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_callee = {__proto__:null,lang:38, \"nth-child\":38, \"nth-last-child\":38, \"nth-of-type\":38, \"nth-last-of-type\":38, dir:38, \"host-context\":38, if:84, url:124, \"url-prefix\":124, domain:124, regexp:124};\nconst spec_queryIdentifier = {__proto__:null,or:98, and:98, not:106, only:106, layer:170};\nconst spec_QueryCallee = {__proto__:null,selector:112, layer:166};\nconst spec_AtKeyword = {__proto__:null,\"@import\":162, \"@media\":174, \"@charset\":178, \"@namespace\":182, \"@keyframes\":188, \"@supports\":200, \"@scope\":204};\nconst spec_identifier = {__proto__:null,to:207};\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"EbQYQdOOO#qQdOOP#xO`OOOOQP'#Cf'#CfOOQP'#Ce'#CeO#}QdO'#ChO$nQaO'#CcO$xQdO'#CkO%TQdO'#DpO%YQdO'#DrO%_QdO'#DuO%_QdO'#DxOOQP'#FV'#FVO&eQhO'#EhOOQS'#FU'#FUOOQS'#Ek'#EkQYQdOOO&lQdO'#EOO&PQhO'#EUO&lQdO'#EWO'aQdO'#EYO'lQdO'#E]O'tQhO'#EcO(VQdO'#EeO(bQaO'#CfO)VQ`O'#D{O)[Q`O'#F`O)gQdO'#F`QOQ`OOP)qO&jO'#CaPOOO)C@t)C@tOOQP'#Cj'#CjOOQP,59S,59SO#}QdO,59SO)|QdO,59VO%TQdO,5:[O%YQdO,5:^O%_QdO,5:aO%_QdO,5:cO%_QdO,5:dO%_QdO'#ErO*XQ`O,58}O*aQdO'#DzOOQS,58},58}OOQP'#Cn'#CnOOQO'#Dn'#DnOOQP,59V,59VO*hQ`O,59VO*mQ`O,59VOOQP'#Dq'#DqOOQP,5:[,5:[OOQO'#Ds'#DsO*rQpO,5:^O+]QaO,5:aO+sQaO,5:dOOQW'#DZ'#DZO,ZQhO'#DdO,xQhO'#FaO'tQhO'#DbO-WQ`O'#DhOOQW'#F['#F[O-]Q`O,5;SO-eQ`O'#DeOOQS-E8i-E8iOOQ['#Cs'#CsO-jQdO'#CtO.QQdO'#CzO.hQdO'#C}O/OQ!pO'#DPO1RQ!jO,5:jOOQO'#DU'#DUO*mQ`O'#DTO1cQ!nO'#FXO3`Q`O'#DVO3eQ`O'#DkOOQ['#FX'#FXO-`Q`O,5:pO3jQ!bO,5:rOOQS'#E['#E[O3rQ`O,5:tO3wQdO,5:tOOQO'#E_'#E_O4PQ`O,5:wO4UQhO,5:}O%_QdO'#DgOOQS,5;P,5;PO-eQ`O,5;PO4^QdO,5;PO4fQdO,5:gO4vQdO'#EtO5TQ`O,5;zO5TQ`O,5;zPOOO'#Ej'#EjP5`O&jO,58{POOO,58{,58{OOQP1G.n1G.nOOQP1G.q1G.qO*hQ`O1G.qO*mQ`O1G.qOOQP1G/v1G/vO5kQpO1G/xO5sQaO1G/{O6ZQaO1G/}O6qQaO1G0OO7XQaO,5;^OOQO-E8p-E8pOOQS1G.i1G.iO7cQ`O,5:fO7hQdO'#DoO7oQdO'#CrOOQP1G/x1G/xO&lQdO1G/xO7vQ!jO'#DZO8UQ!bO,59vO8^QhO,5:OOOQO'#F]'#F]O8XQ!bO,59zO'tQhO,59xO8fQhO'#EvO8sQ`O,5;{O9OQhO,59|O9uQhO'#DiOOQW,5:S,5:SOOQS1G0n1G0nOOQW,5:P,5:PO9|Q!fO'#FYOOQS'#FY'#FYOOQS'#Em'#EmO;^QdO,59`OOQ[,59`,59`O;tQdO,59fOOQ[,59f,59fO<[QdO,59iOOQ[,59i,59iOOQ[,59k,59kO&lQdO,59mO<rQhO'#EQOOQW'#EQ'#EQO=WQ`O1G0UO1[QhO1G0UOOQ[,59o,59oO'tQhO'#DXOOQ[,59q,59qO=]Q#tO,5:VOOQS1G0[1G0[OOQS1G0^1G0^OOQS1G0`1G0`O=hQ`O1G0`O=mQdO'#E`OOQS1G0c1G0cOOQS1G0i1G0iO=xQaO,5:RO-`Q`O1G0kOOQS1G0k1G0kO-eQ`O1G0kO>PQ!fO1G0ROOQO1G0R1G0ROOQO,5;`,5;`O>gQdO,5;`OOQO-E8r-E8rO>tQ`O1G1fPOOO-E8h-E8hPOOO1G.g1G.gOOQP7+$]7+$]OOQP7+%d7+%dO&lQdO7+%dOOQS1G0Q1G0QO?PQaO'#F_O?ZQ`O,5:ZO?`Q!fO'#ElO@^QdO'#FWO@hQ`O,59^O@mQ!bO7+%dO&lQdO1G/bO@uQhO1G/fOOQW1G/j1G/jOOQW1G/d1G/dOAWQhO,5;bOOQO-E8t-E8tOAfQhO'#DZOAtQhO'#F^OBPQ`O'#F^OBUQ`O,5:TOOQS-E8k-E8kOOQ[1G.z1G.zOOQ[1G/Q1G/QOOQ[1G/T1G/TOOQ[1G/X1G/XOBZQdO,5:lOOQS7+%p7+%pOB`Q`O7+%pOBeQhO'#DYOBmQ`O,59sO'tQhO,59sOOQ[1G/q1G/qOBuQ`O1G/qOOQS7+%z7+%zOBzQbO'#DPOOQO'#Eb'#EbOCYQ`O'#EaOOQO'#Ea'#EaOCeQ`O'#EwOCmQdO,5:zOOQS,5:z,5:zOOQ[1G/m1G/mOOQS7+&V7+&VO-`Q`O7+&VOCxQ!fO'#EsO&lQdO'#EsOEPQdO7+%mOOQO7+%m7+%mOOQO1G0z1G0zOEdQ!bO<<IOOElQdO'#EqOEvQ`O,5;yOOQP1G/u1G/uOOQS-E8j-E8jOFOQdO'#EpOFYQ`O,5;rOOQ]1G.x1G.xOOQP<<IO<<IOOFbQdO7+$|OOQO'#D]'#D]OFiQ!bO7+%QOFqQhO'#EoOF{Q`O,5;xO&lQdO,5;xOOQW1G/o1G/oOOQO'#ES'#ESOGTQ`O1G0WOOQS<<I[<<I[O&lQdO,59tOGnQhO1G/_OOQ[1G/_1G/_OGuQ`O1G/_OOQW-E8l-E8lOOQ[7+%]7+%]OOQO,5:{,5:{O=pQdO'#ExOCeQ`O,5;cOOQS,5;c,5;cOOQS-E8u-E8uOOQS1G0f1G0fOOQS<<Iq<<IqOG}Q!fO,5;_OOQS-E8q-E8qOOQO<<IX<<IXOOQPAN>jAN>jOIUQaO,5;]OOQO-E8o-E8oOI`QdO,5;[OOQO-E8n-E8nOOQW<<Hh<<HhOOQW<<Hl<<HlOIjQhO<<HlOI{QhO,5;ZOJWQ`O,5;ZOOQO-E8m-E8mOJ]QdO1G1dOBZQdO'#EuOJgQ`O7+%rOOQW7+%r7+%rOJoQ!bO1G/`OOQ[7+$y7+$yOJzQhO7+$yPKRQ`O'#EnOOQO,5;d,5;dOOQO-E8v-E8vOOQS1G0}1G0}OKWQ`OAN>WO&lQdO1G0uOK]Q`O7+'OOOQO,5;a,5;aOOQO-E8s-E8sOOQW<<I^<<I^OOQ[<<He<<HePOQW,5;Y,5;YOOQWG23rG23rOKeQdO7+&a\",\n  stateData: \"Kx~O#sOS#tQQ~OW[OZ[O]TO`VOaVOi]OjWOmXO!jYO!mZO!saO!ybO!{cO!}dO#QeO#WfO#YgO#oRO~OQiOW[OZ[O]TO`VOaVOi]OjWOmXO!jYO!mZO!saO!ybO!{cO!}dO#QeO#WfO#YgO#ohO~O#m$SP~P!dO#tmO~O#ooO~O]qO`rOarOjsOmtO!juO!mwO#nvO~OpzO!^xO~P$SOc!QO#o|O#p}O~O#o!RO~O#o!TO~OW[OZ[O]TO`VOaVOjWOmXO!jYO!mZO#oRO~OS!]Oe!YO!V![O!Y!`O#q!XOp$TP~Ok$TP~P&POQ!jOe!cOm!dOp!eOr!mOt!mOz!kO!`!lO#o!bO#p!hO#}!fO~Ot!qO!`!lO#o!pO~Ot!sO#o!sO~OS!]Oe!YO!V![O!Y!`O#q!XO~Oe!vOpzO#Z!xO~O]YX`YX`!pXaYXjYXmYXpYX!^YX!jYX!mYX#nYX~O`!zO~Ok!{O#m$SXo$SX~O#m$SXo$SX~P!dO#u#OO#v#OO#w#QO~Oc#UO#o|O#p}O~OpzO!^xO~Oo$SP~P!dOe#`O~Oe#aO~Ol#bO!h#cO~O]qO`rOarOjsOmtO~Op!ia!^!ia!j!ia!m!ia#n!iad!ia~P*zOp!la!^!la!j!la!m!la#n!lad!la~P*zOR#gOS!]Oe!YOr#gOt#gO!V![O!Y!`O#q#dO#}!fO~O!R#iO!^#jOk$TXp$TX~Oe#mO~Ok#oOpzO~Oe!vO~O]#rO`#rOd#uOi#rOj#rOk#rO~P&lO]#rO`#rOi#rOj#rOk#rOl#wO~P&lO]#rO`#rOi#rOj#rOk#rOo#yO~P&lOP#zOSsXesXksXvsX!VsX!YsX!usX!wsX#qsX!TsXQsX]sX`sXdsXisXjsXmsXpsXrsXtsXzsX!`sX#osX#psX#}sXlsXosX!^sX!qsX#msX~Ov#{O!u#|O!w#}Ok$TP~P'tOe#aOS#{Xk#{Xv#{X!V#{X!Y#{X!u#{X!w#{X#q#{XQ#{X]#{X`#{Xd#{Xi#{Xj#{Xm#{Xp#{Xr#{Xt#{Xz#{X!`#{X#o#{X#p#{X#}#{Xl#{Xo#{X!^#{X!q#{X#m#{X~Oe$RO~Oe$TO~Ok$VOv#{O~Ok$WO~Ot$XO!`!lO~Op$YO~OpzO!R#iO~OpzO#Z$`O~O!q$bOk!oa#m!oao!oa~P&lOk#hX#m#hXo#hX~P!dOk!{O#m$Sao$Sa~O#u#OO#v#OO#w$hO~Ol$jO!h$kO~Op!ii!^!ii!j!ii!m!ii#n!iid!ii~P*zOp!ki!^!ki!j!ki!m!ki#n!kid!ki~P*zOp!li!^!li!j!li!m!li#n!lid!li~P*zOp#fa!^#fa~P$SOo$lO~Od$RP~P%_Od#zP~P&lO`!PXd}X!R}X!T!PX~O`$sO!T$tO~Od$uO!R#iO~Ok#jXp#jX!^#jX~P'tO!^#jOk$Tap$Ta~O!R#iOk!Uap!Ua!^!Uad!Ua`!Ua~OS!]Oe!YO!V![O!Y!`O#q$yO~Od$QP~P9dOv#{OQ#|X]#|X`#|Xd#|Xe#|Xi#|Xj#|Xk#|Xm#|Xp#|Xr#|Xt#|Xz#|X!`#|X#o#|X#p#|X#}#|Xl#|Xo#|X~O]#rO`#rOd%OOi#rOj#rOk#rO~P&lO]#rO`#rOi#rOj#rOk#rOl%PO~P&lO]#rO`#rOi#rOj#rOk#rOo%QO~P&lOe%SOS!tXk!tX!V!tX!Y!tX#q!tX~Ok%TO~Od%YOt%ZO!a%ZO~Ok%[O~Oo%cO#o%^O#}%]O~Od%dO~P$SOv#{O!^%hO!q%jOk!oi#m!oio!oi~P&lOk#ha#m#hao#ha~P!dOk!{O#m$Sio$Si~O!^%mOd$RX~P$SOd%oO~Ov#{OQ#`Xd#`Xe#`Xm#`Xp#`Xr#`Xt#`Xz#`X!^#`X!`#`X#o#`X#p#`X#}#`X~O!^%qOd#zX~P&lOd%sO~Ol%tOv#{O~OR#gOr#gOt#gO#q%vO#}!fO~O!R#iOk#jap#ja!^#ja~O`!PXd}X!R}X!^}X~O!R#iO!^%xOd$QX~O`%zO~Od%{O~O#o%|O~Ok&OO~O`&PO!R#iO~Od&ROk&QO~Od&UO~OP#zOpsX!^sXdsX~O#}%]Op#TX!^#TX~OpzO!^&WO~Oo&[O#o%^O#}%]O~Ov#{OQ#gXe#gXk#gXm#gXp#gXr#gXt#gXz#gX!^#gX!`#gX!q#gX#m#gX#o#gX#p#gX#}#gXo#gX~O!^%hO!q&`Ok!oq#m!oqo!oq~P&lOl&aOv#{O~Od#eX!^#eX~P%_O!^%mOd$Ra~Od#dX!^#dX~P&lO!^%qOd#za~Od&fO~P&lOd&gO!T&hO~Od#cX!^#cX~P9dO!^%xOd$Qa~O]&mOd&oO~OS#bae#ba!V#ba!Y#ba#q#ba~Od&qO~PG]Od&qOk&rO~Ov#{OQ#gae#gak#gam#gap#gar#gat#gaz#ga!^#ga!`#ga!q#ga#m#ga#o#ga#p#ga#}#gao#ga~Od#ea!^#ea~P$SOd#da!^#da~P&lOR#gOr#gOt#gO#q%vO#}%]O~O!R#iOd#ca!^#ca~O`&xO~O!^%xOd$Qi~P&lO]&mOd&|O~Ov#{Od|ik|i~Od&}O~PG]Ok'OO~Od'PO~O!^%xOd$Qq~Od#cq!^#cq~P&lO#s!a#t#}]#}v!m~\",\n  goto: \"2h$UPPPPP$VP$YP$c$uP$cP%X$cPP%_PPP%e%o%oPPPPP%oPP%oP&]P%oP%o'W%oP't'w'}'}(^'}P'}P'}P'}'}P(m'}(yP(|PP)p)v$c)|$c*SP$cP$c$cP*Y*{+YP$YP+aP+dP$YP$YP$YP+j$YP+m+p+s+z$YP$YPP$YP,P,V,f,|-[-b-l-r-x.O.U.`.f.l.rPPPPPPPPPPP.x/R/w/z0|P1U1u2O2R2U2[RnQ_^OP`kz!{$dq[OPYZ`kuvwxz!v!{#`$d%mqSOPYZ`kuvwxz!v!{#`$d%mQpTR#RqQ!OVR#SrQ#S!QS$Q!i!jR$i#U!V!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'Q!U!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QU#g!Y$t&hU%`$Y%b&WR&V%_!V!iac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QR$S!kQ%W$RR&S%Xk!^]bf!Y![!g#i#j#m$P$R%X%xQ#e!YQ${#mQ%w$tQ&j%xR&w&hQ!ygQ#p!`Q$^!xR%f$`R#n!]!U!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QQ!qdR$X!rQ!PVR#TrQ#S!PR$i#TQ!SWR#VsQ!UXR#WtQ{UQ!wgQ#^yQ#o!_Q$U!nQ$[!uQ$_!yQ%e$^Q&Y%aQ&]%fR&v&XSjPzQ!}kQ$c!{R%k$dZiPkz!{$dR$P!gQ%}%SR&z&mR!rdR!teR$Z!tS%a$Y%bR&t&WV%_$Y%b&WQ#PmR$g#PQ`OSkPzU!a`k$dR$d!{Q$p#aY%p$p%u&d&l'QQ%u$sQ&d%qQ&l%zR'Q&xQ#t!cQ#v!dQ#x!eV$}#t#v#xQ%X$RR&T%XQ%y$zS&k%y&yR&y&lQ%r$pR&e%rQ%n$mR&c%nQyUR#]yQ%i$aR&_%iQ!|jS$e!|$fR$f!}Q&n%}R&{&nQ#k!ZR$x#kQ%b$YR&Z%bQ&X%aR&u&X__OP`kz!{$d^UOP`kz!{$dQ!VYQ!WZQ#XuQ#YvQ#ZwQ#[xQ$]!vQ$m#`R&b%mR$q#aQ!gaQ!oc[#q!c!d!e#t#v#xQ$a!zd$o#a$p$s%q%u%z&d&l&x'QQ$r#cQ%R#{S%g$a%iQ%l$kQ&^%hR&p&P]#s!c!d!e#t#v#xW!Z]b!g$PQ!ufQ#f!YQ#l![Q$v#iQ$w#jQ$z#mS%V$R%XR&i%xQ#h!YQ%w$tR&w&hR$|#mR$n#`QlPR#_zQ!_]Q!nbQ$O!gR%U$P\",\n  nodeNames: \"⚠ Unit VariableName VariableName QueryCallee Comment StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector ClassSelector . ClassName PseudoClassSelector : :: PseudoClassName PseudoClassName ) ( ArgList ValueName ParenthesizedValue AtKeyword # ; ] [ BracketedValue } { BracedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp CallExpression Callee IfExpression if ArgList IfBranch KeywordQuery FeatureQuery FeatureName BinaryQuery LogicOp ComparisonQuery CompareOp UnaryQuery UnaryQueryOp ParenthesizedQuery SelectorQuery selector ParenthesizedSelector CallQuery ArgList , CallLiteral CallTag ParenthesizedContent PseudoClassName ArgList IdSelector IdName AttributeSelector AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp Block Declaration PropertyName Important ImportStatement import Layer layer LayerName layer MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList KeyframeSelector KeyframeRangeName SupportsStatement supports ScopeStatement scope to AtRule Styles\",\n  maxTerm: 143,\n  nodeProps: [\n    [\"isolate\", -2,5,36,\"\"],\n    [\"openedBy\", 20,\"(\",28,\"[\",31,\"{\"],\n    [\"closedBy\", 21,\")\",29,\"]\",32,\"}\"]\n  ],\n  propSources: [cssHighlighting],\n  skippedNodes: [0,5,106],\n  repeatNodeCount: 15,\n  tokenData: \"JQ~R!YOX$qX^%i^p$qpq%iqr({rs-ust/itu6Wuv$qvw7Qwx7cxy9Qyz9cz{9h{|:R|}>t}!O?V!O!P?t!P!Q@]!Q![AU![!]BP!]!^B{!^!_C^!_!`DY!`!aDm!a!b$q!b!cEn!c!}$q!}#OG{#O#P$q#P#QH^#Q#R6W#R#o$q#o#pHo#p#q6W#q#rIQ#r#sIc#s#y$q#y#z%i#z$f$q$f$g%i$g#BY$q#BY#BZ%i#BZ$IS$q$IS$I_%i$I_$I|$q$I|$JO%i$JO$JT$q$JT$JU%i$JU$KV$q$KV$KW%i$KW&FU$q&FU&FV%i&FV;'S$q;'S;=`Iz<%lO$q`$tSOy%Qz;'S%Q;'S;=`%c<%lO%Q`%VS!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Q`%fP;=`<%l%Q~%nh#s~OX%QX^'Y^p%Qpq'Yqy%Qz#y%Q#y#z'Y#z$f%Q$f$g'Y$g#BY%Q#BY#BZ'Y#BZ$IS%Q$IS$I_'Y$I_$I|%Q$I|$JO'Y$JO$JT%Q$JT$JU'Y$JU$KV%Q$KV$KW'Y$KW&FU%Q&FU&FV'Y&FV;'S%Q;'S;=`%c<%lO%Q~'ah#s~!a`OX%QX^'Y^p%Qpq'Yqy%Qz#y%Q#y#z'Y#z$f%Q$f$g'Y$g#BY%Q#BY#BZ'Y#BZ$IS%Q$IS$I_'Y$I_$I|%Q$I|$JO'Y$JO$JT%Q$JT$JU'Y$JU$KV%Q$KV$KW'Y$KW&FU%Q&FU&FV'Y&FV;'S%Q;'S;=`%c<%lO%Qj)OUOy%Qz#]%Q#]#^)b#^;'S%Q;'S;=`%c<%lO%Qj)gU!a`Oy%Qz#a%Q#a#b)y#b;'S%Q;'S;=`%c<%lO%Qj*OU!a`Oy%Qz#d%Q#d#e*b#e;'S%Q;'S;=`%c<%lO%Qj*gU!a`Oy%Qz#c%Q#c#d*y#d;'S%Q;'S;=`%c<%lO%Qj+OU!a`Oy%Qz#f%Q#f#g+b#g;'S%Q;'S;=`%c<%lO%Qj+gU!a`Oy%Qz#h%Q#h#i+y#i;'S%Q;'S;=`%c<%lO%Qj,OU!a`Oy%Qz#T%Q#T#U,b#U;'S%Q;'S;=`%c<%lO%Qj,gU!a`Oy%Qz#b%Q#b#c,y#c;'S%Q;'S;=`%c<%lO%Qj-OU!a`Oy%Qz#h%Q#h#i-b#i;'S%Q;'S;=`%c<%lO%Qj-iS!qY!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Q~-xWOY-uZr-urs.bs#O-u#O#P.g#P;'S-u;'S;=`/c<%lO-u~.gOt~~.jRO;'S-u;'S;=`.s;=`O-u~.vXOY-uZr-urs.bs#O-u#O#P.g#P;'S-u;'S;=`/c;=`<%l-u<%lO-u~/fP;=`<%l-uj/nYjYOy%Qz!Q%Q!Q![0^![!c%Q!c!i0^!i#T%Q#T#Z0^#Z;'S%Q;'S;=`%c<%lO%Qj0cY!a`Oy%Qz!Q%Q!Q![1R![!c%Q!c!i1R!i#T%Q#T#Z1R#Z;'S%Q;'S;=`%c<%lO%Qj1WY!a`Oy%Qz!Q%Q!Q![1v![!c%Q!c!i1v!i#T%Q#T#Z1v#Z;'S%Q;'S;=`%c<%lO%Qj1}YrY!a`Oy%Qz!Q%Q!Q![2m![!c%Q!c!i2m!i#T%Q#T#Z2m#Z;'S%Q;'S;=`%c<%lO%Qj2tYrY!a`Oy%Qz!Q%Q!Q![3d![!c%Q!c!i3d!i#T%Q#T#Z3d#Z;'S%Q;'S;=`%c<%lO%Qj3iY!a`Oy%Qz!Q%Q!Q![4X![!c%Q!c!i4X!i#T%Q#T#Z4X#Z;'S%Q;'S;=`%c<%lO%Qj4`YrY!a`Oy%Qz!Q%Q!Q![5O![!c%Q!c!i5O!i#T%Q#T#Z5O#Z;'S%Q;'S;=`%c<%lO%Qj5TY!a`Oy%Qz!Q%Q!Q![5s![!c%Q!c!i5s!i#T%Q#T#Z5s#Z;'S%Q;'S;=`%c<%lO%Qj5zSrY!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Qd6ZUOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Qd6tS!hS!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Qb7VSZQOy%Qz;'S%Q;'S;=`%c<%lO%Q~7fWOY7cZw7cwx.bx#O7c#O#P8O#P;'S7c;'S;=`8z<%lO7c~8RRO;'S7c;'S;=`8[;=`O7c~8_XOY7cZw7cwx.bx#O7c#O#P8O#P;'S7c;'S;=`8z;=`<%l7c<%lO7c~8}P;=`<%l7cj9VSeYOy%Qz;'S%Q;'S;=`%c<%lO%Q~9hOd~n9oUWQvWOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Qj:YWvW!mQOy%Qz!O%Q!O!P:r!P!Q%Q!Q![=w![;'S%Q;'S;=`%c<%lO%Qj:wU!a`Oy%Qz!Q%Q!Q![;Z![;'S%Q;'S;=`%c<%lO%Qj;bY!a`#}YOy%Qz!Q%Q!Q![;Z![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%Qj<VY!a`Oy%Qz{%Q{|<u|}%Q}!O<u!O!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj<zU!a`Oy%Qz!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj=eU!a`#}YOy%Qz!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj>O[!a`#}YOy%Qz!O%Q!O!P;Z!P!Q%Q!Q![=w![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%Qj>yS!^YOy%Qz;'S%Q;'S;=`%c<%lO%Qj?[WvWOy%Qz!O%Q!O!P:r!P!Q%Q!Q![=w![;'S%Q;'S;=`%c<%lO%Qj?yU]YOy%Qz!Q%Q!Q![;Z![;'S%Q;'S;=`%c<%lO%Q~@bTvWOy%Qz{@q{;'S%Q;'S;=`%c<%lO%Q~@xS!a`#t~Oy%Qz;'S%Q;'S;=`%c<%lO%QjAZ[#}YOy%Qz!O%Q!O!P;Z!P!Q%Q!Q![=w![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%QjBUU`YOy%Qz![%Q![!]Bh!];'S%Q;'S;=`%c<%lO%QbBoSaQ!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QjCQSkYOy%Qz;'S%Q;'S;=`%c<%lO%QhCcU!TWOy%Qz!_%Q!_!`Cu!`;'S%Q;'S;=`%c<%lO%QhC|S!TW!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QlDaS!TW!hSOy%Qz;'S%Q;'S;=`%c<%lO%QjDtV!jQ!TWOy%Qz!_%Q!_!`Cu!`!aEZ!a;'S%Q;'S;=`%c<%lO%QbEbS!jQ!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QjEqYOy%Qz}%Q}!OFa!O!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjFfW!a`Oy%Qz!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjGV[iY!a`Oy%Qz}%Q}!OGO!O!Q%Q!Q![GO![!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjHQSmYOy%Qz;'S%Q;'S;=`%c<%lO%QnHcSl^Oy%Qz;'S%Q;'S;=`%c<%lO%QjHtSpYOy%Qz;'S%Q;'S;=`%c<%lO%QjIVSoYOy%Qz;'S%Q;'S;=`%c<%lO%QfIhU!mQOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Q`I}P;=`<%l$q\",\n  tokenizers: [descendant, unitToken, identifiers, queryIdentifiers, 1, 2, 3, 4, new LocalTokenGroup(\"m~RRYZ[z{a~~g~aO#v~~dP!P!Qg~lO#w~~\", 28, 129)],\n  topRules: {\"StyleSheet\":[0,6],\"Styles\":[1,105]},\n  specialized: [{term: 124, get: (value) => spec_callee[value] || -1},{term: 125, get: (value) => spec_queryIdentifier[value] || -1},{term: 4, get: (value) => spec_QueryCallee[value] || -1},{term: 25, get: (value) => spec_AtKeyword[value] || -1},{term: 123, get: (value) => spec_identifier[value] || -1}],\n  tokenPrec: 1963\n});\n\nexport { parser };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,8EAA8E;AAC9E,MAAM,eAAe,KACnB,OAAO,GACP,aAAa,KACb,SAAS,KACT,eAAe,GACf,kBAAkB,KAClB,oBAAoB,GACpB,cAAc;AAEhB;4CAC4C,GAE5C,MAAM,QAAQ;IAAC;IAAG;IAAI;IAAI;IAAI;IAAI;IAAI;IAAK;IAAK;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACrE;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;CAAM;AAC3E,MAAM,QAAQ,IAAI,SAAS,IAAI,aAAa,IAAI,WAAW,IAAI,OAAO,IAAI,SAAS,IAC7E,OAAO,IAAI,UAAU,IAAI,YAAY,IAAI,YAAY,IAAI,UAAU,IAAI,WAAW;AAExF,SAAS,QAAQ,EAAE;IAAI,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM;AAAI;AAEzF,SAAS,QAAQ,EAAE;IAAI,OAAO,MAAM,MAAM,MAAM;AAAG;AAEnD,SAAS,MAAM,EAAE;IAAI,OAAO,QAAQ,OAAO,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM;AAAG;AAEzF,MAAM,mBAAmB,CAAC,IAAI,SAAS,SAAW,CAAC,OAAO;QACxD,IAAK,IAAI,SAAS,OAAO,SAAS,GAAG,IAAI,IAAI,IAAK;YAChD,IAAI,EAAC,IAAI,EAAC,GAAG;YACb,IAAI,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,cAAe,UAAU,QAAQ,OAAQ;gBACpF,IAAI,CAAC,UAAU,CAAC,QAAQ,QAAQ,IAAI,CAAC,GAAG,SAAS;gBACjD,IAAI,WAAW,KAAK,QAAQ,MAAM;gBAClC,MAAM,OAAO;YACf,OAAO,IAAI,QAAQ,aAAa,MAAM,IAAI,CAAC,MAAM,SAAS;gBACxD,MAAM,OAAO;gBACb,IAAI,MAAM,MAAM,IAAI,GAAG;oBACrB,GAAG;wBAAE,MAAM,OAAO;oBAAI,QAAS,MAAM,MAAM,IAAI,EAAE;oBACjD,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,OAAO;gBACrC,OAAO,IAAI,MAAM,IAAI,GAAG,CAAC,GAAG;oBAC1B,MAAM,OAAO;gBACf;gBACA,SAAS;YACX,OAAO;gBACL,IAAI,QAAQ,MAAM,WAAW,CAC3B,UAAU,KAAK,MAAM,QAAQ,CAAC,gBAAgB,UAAU,QAAQ,SAAS,SAAS;gBAEpF;YACF;QACF;IACF;AAEA,MAAM,cAAc,IAAI,iJAAA,CAAA,oBAAiB,CACvC,iBAAiB,YAAY,cAAc;AAE7C,MAAM,mBAAmB,IAAI,iJAAA,CAAA,oBAAiB,CAC5C,iBAAiB,iBAAiB,mBAAmB;AAGvD,MAAM,aAAa,IAAI,iJAAA,CAAA,oBAAiB,CAAC,CAAA;IACvC,IAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK;QAClC,IAAI,EAAC,IAAI,EAAC,GAAG;QACb,IAAI,QAAQ,SAAS,QAAQ,cAAc,QAAQ,QAAQ,QAAQ,UAC/D,QAAQ,YAAY,QAAQ,YAAY,QAAQ,SAAS,QAAQ,MAAM,IAAI,CAAC,OAC5E,QAAQ,QAAQ,QAAQ,WAC1B,MAAM,WAAW,CAAC;IACtB;AACF;AAEA,MAAM,YAAY,IAAI,iJAAA,CAAA,oBAAiB,CAAC,CAAA;IACtC,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK;QACnC,IAAI,EAAC,IAAI,EAAC,GAAG;QACb,IAAI,QAAQ,SAAS;YAAE,MAAM,OAAO;YAAI,MAAM,WAAW,CAAC;QAAO;QACjE,IAAI,QAAQ,OAAO;YACjB,GAAG;gBAAE,MAAM,OAAO;YAAI,QAAS,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,EAAE;YAC1E,MAAM,WAAW,CAAC;QACpB;IACF;AACF;AAEA,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE;IAChC,+DAA+D,wJAAA,CAAA,OAAI,CAAC,iBAAiB;IACrF,oBAAoB,wJAAA,CAAA,OAAI,CAAC,OAAO;IAChC,eAAe,wJAAA,CAAA,OAAI,CAAC,SAAS;IAC7B,cAAc,wJAAA,CAAA,OAAI,CAAC,SAAS;IAC5B,mBAAmB,wJAAA,CAAA,OAAI,CAAC,eAAe;IACvC,SAAS,wJAAA,CAAA,OAAI,CAAC,OAAO;IACrB,WAAW,wJAAA,CAAA,OAAI,CAAC,SAAS;IACzB,iBAAiB,wJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC,wJAAA,CAAA,OAAI,CAAC,SAAS;IAC7C,QAAQ,wJAAA,CAAA,OAAI,CAAC,SAAS;IACtB,4BAA4B,wJAAA,CAAA,OAAI,CAAC,YAAY;IAC7C,eAAe,wJAAA,CAAA,OAAI,CAAC,aAAa;IACjC,eAAe,wJAAA,CAAA,OAAI,CAAC,MAAM;IAC1B,cAAc,wJAAA,CAAA,OAAI,CAAC,OAAO;IAC1B,cAAc,wJAAA,CAAA,OAAI,CAAC,eAAe;IAClC,qBAAqB,wJAAA,CAAA,OAAI,CAAC,IAAI;IAC9B,cAAc,wJAAA,CAAA,OAAI,CAAC,YAAY;IAC/B,QAAQ,wJAAA,CAAA,OAAI,CAAC,eAAe;IAC5B,MAAM,wJAAA,CAAA,OAAI,CAAC,IAAI;IACf,qCAAqC,wJAAA,CAAA,OAAI,CAAC,kBAAkB;IAC5D,qBAAqB,wJAAA,CAAA,OAAI,CAAC,eAAe;IACzC,8BAA8B,wJAAA,CAAA,OAAI,CAAC,aAAa;IAChD,OAAO,wJAAA,CAAA,OAAI,CAAC,kBAAkB;IAC9B,WAAW,wJAAA,CAAA,OAAI,CAAC,QAAQ;IACxB,SAAS,wJAAA,CAAA,OAAI,CAAC,YAAY;IAC1B,cAAc,wJAAA,CAAA,OAAI,CAAC,KAAK;IACxB,sCAAsC,wJAAA,CAAA,OAAI,CAAC,MAAM;IACjD,KAAK,wJAAA,CAAA,OAAI,CAAC,WAAW;IACrB,cAAc,wJAAA,CAAA,OAAI,CAAC,aAAa;IAChC,OAAO,wJAAA,CAAA,OAAI,CAAC,SAAS;IACrB,OAAO,wJAAA,CAAA,OAAI,CAAC,KAAK;IACjB,OAAO,wJAAA,CAAA,OAAI,CAAC,aAAa;IACzB,OAAO,wJAAA,CAAA,OAAI,CAAC,KAAK;AACnB;AAEA,8EAA8E;AAC9E,MAAM,cAAc;IAAC,WAAU;IAAK,MAAK;IAAI,aAAY;IAAI,kBAAiB;IAAI,eAAc;IAAI,oBAAmB;IAAI,KAAI;IAAI,gBAAe;IAAI,IAAG;IAAI,KAAI;IAAK,cAAa;IAAK,QAAO;IAAK,QAAO;AAAG;AAC9M,MAAM,uBAAuB;IAAC,WAAU;IAAK,IAAG;IAAI,KAAI;IAAI,KAAI;IAAK,MAAK;IAAK,OAAM;AAAG;AACxF,MAAM,mBAAmB;IAAC,WAAU;IAAK,UAAS;IAAK,OAAM;AAAG;AAChE,MAAM,iBAAiB;IAAC,WAAU;IAAK,WAAU;IAAK,UAAS;IAAK,YAAW;IAAK,cAAa;IAAK,cAAa;IAAK,aAAY;IAAK,UAAS;AAAG;AACrJ,MAAM,kBAAkB;IAAC,WAAU;IAAK,IAAG;AAAG;AAC9C,MAAM,SAAS,iJAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;IAClC,SAAS;IACT,QAAQ;IACR,WAAW;IACX,MAAM;IACN,WAAW;IACX,SAAS;IACT,WAAW;QACT;YAAC;YAAW,CAAC;YAAE;YAAE;YAAG;SAAG;QACvB;YAAC;YAAY;YAAG;YAAI;YAAG;YAAI;YAAG;SAAI;QAClC;YAAC;YAAY;YAAG;YAAI;YAAG;YAAI;YAAG;SAAI;KACnC;IACD,aAAa;QAAC;KAAgB;IAC9B,cAAc;QAAC;QAAE;QAAE;KAAI;IACvB,iBAAiB;IACjB,WAAW;IACX,YAAY;QAAC;QAAY;QAAW;QAAa;QAAkB;QAAG;QAAG;QAAG;QAAG,IAAI,iJAAA,CAAA,kBAAe,CAAC,sCAAsC,IAAI;KAAK;IAClJ,UAAU;QAAC,cAAa;YAAC;YAAE;SAAE;QAAC,UAAS;YAAC;YAAE;SAAI;IAAA;IAC9C,aAAa;QAAC;YAAC,MAAM;YAAK,KAAK,CAAC,QAAU,WAAW,CAAC,MAAM,IAAI,CAAC;QAAC;QAAE;YAAC,MAAM;YAAK,KAAK,CAAC,QAAU,oBAAoB,CAAC,MAAM,IAAI,CAAC;QAAC;QAAE;YAAC,MAAM;YAAG,KAAK,CAAC,QAAU,gBAAgB,CAAC,MAAM,IAAI,CAAC;QAAC;QAAE;YAAC,MAAM;YAAI,KAAK,CAAC,QAAU,cAAc,CAAC,MAAM,IAAI,CAAC;QAAC;QAAE;YAAC,MAAM;YAAK,KAAK,CAAC,QAAU,eAAe,CAAC,MAAM,IAAI,CAAC;QAAC;KAAE;IAC9S,WAAW;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1902, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/lang-css/dist/index.js"], "sourcesContent": ["import { parser } from '@lezer/css';\nimport { syntax<PERSON>ree, LRLanguage, indentNodeProp, continuedIndent, foldNodeProp, foldInside, LanguageSupport } from '@codemirror/language';\nimport { NodeWeakMap, IterMode } from '@lezer/common';\n\nlet _properties = null;\nfunction properties() {\n    if (!_properties && typeof document == \"object\" && document.body) {\n        let { style } = document.body, names = [], seen = new Set;\n        for (let prop in style)\n            if (prop != \"cssText\" && prop != \"cssFloat\") {\n                if (typeof style[prop] == \"string\") {\n                    if (/[A-Z]/.test(prop))\n                        prop = prop.replace(/[A-Z]/g, ch => \"-\" + ch.toLowerCase());\n                    if (!seen.has(prop)) {\n                        names.push(prop);\n                        seen.add(prop);\n                    }\n                }\n            }\n        _properties = names.sort().map(name => ({ type: \"property\", label: name, apply: name + \": \" }));\n    }\n    return _properties || [];\n}\nconst pseudoClasses = /*@__PURE__*/[\n    \"active\", \"after\", \"any-link\", \"autofill\", \"backdrop\", \"before\",\n    \"checked\", \"cue\", \"default\", \"defined\", \"disabled\", \"empty\",\n    \"enabled\", \"file-selector-button\", \"first\", \"first-child\",\n    \"first-letter\", \"first-line\", \"first-of-type\", \"focus\",\n    \"focus-visible\", \"focus-within\", \"fullscreen\", \"has\", \"host\",\n    \"host-context\", \"hover\", \"in-range\", \"indeterminate\", \"invalid\",\n    \"is\", \"lang\", \"last-child\", \"last-of-type\", \"left\", \"link\", \"marker\",\n    \"modal\", \"not\", \"nth-child\", \"nth-last-child\", \"nth-last-of-type\",\n    \"nth-of-type\", \"only-child\", \"only-of-type\", \"optional\", \"out-of-range\",\n    \"part\", \"placeholder\", \"placeholder-shown\", \"read-only\", \"read-write\",\n    \"required\", \"right\", \"root\", \"scope\", \"selection\", \"slotted\", \"target\",\n    \"target-text\", \"valid\", \"visited\", \"where\"\n].map(name => ({ type: \"class\", label: name }));\nconst values = /*@__PURE__*/[\n    \"above\", \"absolute\", \"activeborder\", \"additive\", \"activecaption\", \"after-white-space\",\n    \"ahead\", \"alias\", \"all\", \"all-scroll\", \"alphabetic\", \"alternate\", \"always\",\n    \"antialiased\", \"appworkspace\", \"asterisks\", \"attr\", \"auto\", \"auto-flow\", \"avoid\", \"avoid-column\",\n    \"avoid-page\", \"avoid-region\", \"axis-pan\", \"background\", \"backwards\", \"baseline\", \"below\",\n    \"bidi-override\", \"blink\", \"block\", \"block-axis\", \"bold\", \"bolder\", \"border\", \"border-box\",\n    \"both\", \"bottom\", \"break\", \"break-all\", \"break-word\", \"bullets\", \"button\", \"button-bevel\",\n    \"buttonface\", \"buttonhighlight\", \"buttonshadow\", \"buttontext\", \"calc\", \"capitalize\",\n    \"caps-lock-indicator\", \"caption\", \"captiontext\", \"caret\", \"cell\", \"center\", \"checkbox\", \"circle\",\n    \"cjk-decimal\", \"clear\", \"clip\", \"close-quote\", \"col-resize\", \"collapse\", \"color\", \"color-burn\",\n    \"color-dodge\", \"column\", \"column-reverse\", \"compact\", \"condensed\", \"contain\", \"content\",\n    \"contents\", \"content-box\", \"context-menu\", \"continuous\", \"copy\", \"counter\", \"counters\", \"cover\",\n    \"crop\", \"cross\", \"crosshair\", \"currentcolor\", \"cursive\", \"cyclic\", \"darken\", \"dashed\", \"decimal\",\n    \"decimal-leading-zero\", \"default\", \"default-button\", \"dense\", \"destination-atop\", \"destination-in\",\n    \"destination-out\", \"destination-over\", \"difference\", \"disc\", \"discard\", \"disclosure-closed\",\n    \"disclosure-open\", \"document\", \"dot-dash\", \"dot-dot-dash\", \"dotted\", \"double\", \"down\", \"e-resize\",\n    \"ease\", \"ease-in\", \"ease-in-out\", \"ease-out\", \"element\", \"ellipse\", \"ellipsis\", \"embed\", \"end\",\n    \"ethiopic-abegede-gez\", \"ethiopic-halehame-aa-er\", \"ethiopic-halehame-gez\", \"ew-resize\", \"exclusion\",\n    \"expanded\", \"extends\", \"extra-condensed\", \"extra-expanded\", \"fantasy\", \"fast\", \"fill\", \"fill-box\",\n    \"fixed\", \"flat\", \"flex\", \"flex-end\", \"flex-start\", \"footnotes\", \"forwards\", \"from\",\n    \"geometricPrecision\", \"graytext\", \"grid\", \"groove\", \"hand\", \"hard-light\", \"help\", \"hidden\", \"hide\",\n    \"higher\", \"highlight\", \"highlighttext\", \"horizontal\", \"hsl\", \"hsla\", \"hue\", \"icon\", \"ignore\",\n    \"inactiveborder\", \"inactivecaption\", \"inactivecaptiontext\", \"infinite\", \"infobackground\", \"infotext\",\n    \"inherit\", \"initial\", \"inline\", \"inline-axis\", \"inline-block\", \"inline-flex\", \"inline-grid\",\n    \"inline-table\", \"inset\", \"inside\", \"intrinsic\", \"invert\", \"italic\", \"justify\", \"keep-all\",\n    \"landscape\", \"large\", \"larger\", \"left\", \"level\", \"lighter\", \"lighten\", \"line-through\", \"linear\",\n    \"linear-gradient\", \"lines\", \"list-item\", \"listbox\", \"listitem\", \"local\", \"logical\", \"loud\", \"lower\",\n    \"lower-hexadecimal\", \"lower-latin\", \"lower-norwegian\", \"lowercase\", \"ltr\", \"luminosity\", \"manipulation\",\n    \"match\", \"matrix\", \"matrix3d\", \"medium\", \"menu\", \"menutext\", \"message-box\", \"middle\", \"min-intrinsic\",\n    \"mix\", \"monospace\", \"move\", \"multiple\", \"multiple_mask_images\", \"multiply\", \"n-resize\", \"narrower\",\n    \"ne-resize\", \"nesw-resize\", \"no-close-quote\", \"no-drop\", \"no-open-quote\", \"no-repeat\", \"none\",\n    \"normal\", \"not-allowed\", \"nowrap\", \"ns-resize\", \"numbers\", \"numeric\", \"nw-resize\", \"nwse-resize\",\n    \"oblique\", \"opacity\", \"open-quote\", \"optimizeLegibility\", \"optimizeSpeed\", \"outset\", \"outside\",\n    \"outside-shape\", \"overlay\", \"overline\", \"padding\", \"padding-box\", \"painted\", \"page\", \"paused\",\n    \"perspective\", \"pinch-zoom\", \"plus-darker\", \"plus-lighter\", \"pointer\", \"polygon\", \"portrait\",\n    \"pre\", \"pre-line\", \"pre-wrap\", \"preserve-3d\", \"progress\", \"push-button\", \"radial-gradient\", \"radio\",\n    \"read-only\", \"read-write\", \"read-write-plaintext-only\", \"rectangle\", \"region\", \"relative\", \"repeat\",\n    \"repeating-linear-gradient\", \"repeating-radial-gradient\", \"repeat-x\", \"repeat-y\", \"reset\", \"reverse\",\n    \"rgb\", \"rgba\", \"ridge\", \"right\", \"rotate\", \"rotate3d\", \"rotateX\", \"rotateY\", \"rotateZ\", \"round\",\n    \"row\", \"row-resize\", \"row-reverse\", \"rtl\", \"run-in\", \"running\", \"s-resize\", \"sans-serif\", \"saturation\",\n    \"scale\", \"scale3d\", \"scaleX\", \"scaleY\", \"scaleZ\", \"screen\", \"scroll\", \"scrollbar\", \"scroll-position\",\n    \"se-resize\", \"self-start\", \"self-end\", \"semi-condensed\", \"semi-expanded\", \"separate\", \"serif\", \"show\",\n    \"single\", \"skew\", \"skewX\", \"skewY\", \"skip-white-space\", \"slide\", \"slider-horizontal\",\n    \"slider-vertical\", \"sliderthumb-horizontal\", \"sliderthumb-vertical\", \"slow\", \"small\", \"small-caps\",\n    \"small-caption\", \"smaller\", \"soft-light\", \"solid\", \"source-atop\", \"source-in\", \"source-out\",\n    \"source-over\", \"space\", \"space-around\", \"space-between\", \"space-evenly\", \"spell-out\", \"square\", \"start\",\n    \"static\", \"status-bar\", \"stretch\", \"stroke\", \"stroke-box\", \"sub\", \"subpixel-antialiased\", \"svg_masks\",\n    \"super\", \"sw-resize\", \"symbolic\", \"symbols\", \"system-ui\", \"table\", \"table-caption\", \"table-cell\",\n    \"table-column\", \"table-column-group\", \"table-footer-group\", \"table-header-group\", \"table-row\",\n    \"table-row-group\", \"text\", \"text-bottom\", \"text-top\", \"textarea\", \"textfield\", \"thick\", \"thin\",\n    \"threeddarkshadow\", \"threedface\", \"threedhighlight\", \"threedlightshadow\", \"threedshadow\", \"to\", \"top\",\n    \"transform\", \"translate\", \"translate3d\", \"translateX\", \"translateY\", \"translateZ\", \"transparent\",\n    \"ultra-condensed\", \"ultra-expanded\", \"underline\", \"unidirectional-pan\", \"unset\", \"up\", \"upper-latin\",\n    \"uppercase\", \"url\", \"var\", \"vertical\", \"vertical-text\", \"view-box\", \"visible\", \"visibleFill\",\n    \"visiblePainted\", \"visibleStroke\", \"visual\", \"w-resize\", \"wait\", \"wave\", \"wider\", \"window\", \"windowframe\",\n    \"windowtext\", \"words\", \"wrap\", \"wrap-reverse\", \"x-large\", \"x-small\", \"xor\", \"xx-large\", \"xx-small\"\n].map(name => ({ type: \"keyword\", label: name })).concat(/*@__PURE__*/[\n    \"aliceblue\", \"antiquewhite\", \"aqua\", \"aquamarine\", \"azure\", \"beige\",\n    \"bisque\", \"black\", \"blanchedalmond\", \"blue\", \"blueviolet\", \"brown\",\n    \"burlywood\", \"cadetblue\", \"chartreuse\", \"chocolate\", \"coral\", \"cornflowerblue\",\n    \"cornsilk\", \"crimson\", \"cyan\", \"darkblue\", \"darkcyan\", \"darkgoldenrod\",\n    \"darkgray\", \"darkgreen\", \"darkkhaki\", \"darkmagenta\", \"darkolivegreen\",\n    \"darkorange\", \"darkorchid\", \"darkred\", \"darksalmon\", \"darkseagreen\",\n    \"darkslateblue\", \"darkslategray\", \"darkturquoise\", \"darkviolet\",\n    \"deeppink\", \"deepskyblue\", \"dimgray\", \"dodgerblue\", \"firebrick\",\n    \"floralwhite\", \"forestgreen\", \"fuchsia\", \"gainsboro\", \"ghostwhite\",\n    \"gold\", \"goldenrod\", \"gray\", \"grey\", \"green\", \"greenyellow\", \"honeydew\",\n    \"hotpink\", \"indianred\", \"indigo\", \"ivory\", \"khaki\", \"lavender\",\n    \"lavenderblush\", \"lawngreen\", \"lemonchiffon\", \"lightblue\", \"lightcoral\",\n    \"lightcyan\", \"lightgoldenrodyellow\", \"lightgray\", \"lightgreen\", \"lightpink\",\n    \"lightsalmon\", \"lightseagreen\", \"lightskyblue\", \"lightslategray\",\n    \"lightsteelblue\", \"lightyellow\", \"lime\", \"limegreen\", \"linen\", \"magenta\",\n    \"maroon\", \"mediumaquamarine\", \"mediumblue\", \"mediumorchid\", \"mediumpurple\",\n    \"mediumseagreen\", \"mediumslateblue\", \"mediumspringgreen\", \"mediumturquoise\",\n    \"mediumvioletred\", \"midnightblue\", \"mintcream\", \"mistyrose\", \"moccasin\",\n    \"navajowhite\", \"navy\", \"oldlace\", \"olive\", \"olivedrab\", \"orange\", \"orangered\",\n    \"orchid\", \"palegoldenrod\", \"palegreen\", \"paleturquoise\", \"palevioletred\",\n    \"papayawhip\", \"peachpuff\", \"peru\", \"pink\", \"plum\", \"powderblue\",\n    \"purple\", \"rebeccapurple\", \"red\", \"rosybrown\", \"royalblue\", \"saddlebrown\",\n    \"salmon\", \"sandybrown\", \"seagreen\", \"seashell\", \"sienna\", \"silver\", \"skyblue\",\n    \"slateblue\", \"slategray\", \"snow\", \"springgreen\", \"steelblue\", \"tan\",\n    \"teal\", \"thistle\", \"tomato\", \"turquoise\", \"violet\", \"wheat\", \"white\",\n    \"whitesmoke\", \"yellow\", \"yellowgreen\"\n].map(name => ({ type: \"constant\", label: name })));\nconst tags = /*@__PURE__*/[\n    \"a\", \"abbr\", \"address\", \"article\", \"aside\", \"b\", \"bdi\", \"bdo\", \"blockquote\", \"body\",\n    \"br\", \"button\", \"canvas\", \"caption\", \"cite\", \"code\", \"col\", \"colgroup\", \"dd\", \"del\",\n    \"details\", \"dfn\", \"dialog\", \"div\", \"dl\", \"dt\", \"em\", \"figcaption\", \"figure\", \"footer\",\n    \"form\", \"header\", \"hgroup\", \"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\", \"hr\", \"html\", \"i\", \"iframe\",\n    \"img\", \"input\", \"ins\", \"kbd\", \"label\", \"legend\", \"li\", \"main\", \"meter\", \"nav\", \"ol\", \"output\",\n    \"p\", \"pre\", \"ruby\", \"section\", \"select\", \"small\", \"source\", \"span\", \"strong\", \"sub\", \"summary\",\n    \"sup\", \"table\", \"tbody\", \"td\", \"template\", \"textarea\", \"tfoot\", \"th\", \"thead\", \"tr\", \"u\", \"ul\"\n].map(name => ({ type: \"type\", label: name }));\nconst atRules = /*@__PURE__*/[\n    \"@charset\", \"@color-profile\", \"@container\", \"@counter-style\", \"@font-face\", \"@font-feature-values\",\n    \"@font-palette-values\", \"@import\", \"@keyframes\", \"@layer\", \"@media\", \"@namespace\", \"@page\",\n    \"@position-try\", \"@property\", \"@scope\", \"@starting-style\", \"@supports\", \"@view-transition\"\n].map(label => ({ type: \"keyword\", label }));\nconst identifier = /^(\\w[\\w-]*|-\\w[\\w-]*|)$/, variable = /^-(-[\\w-]*)?$/;\nfunction isVarArg(node, doc) {\n    var _a;\n    if (node.name == \"(\" || node.type.isError)\n        node = node.parent || node;\n    if (node.name != \"ArgList\")\n        return false;\n    let callee = (_a = node.parent) === null || _a === void 0 ? void 0 : _a.firstChild;\n    if ((callee === null || callee === void 0 ? void 0 : callee.name) != \"Callee\")\n        return false;\n    return doc.sliceString(callee.from, callee.to) == \"var\";\n}\nconst VariablesByNode = /*@__PURE__*/new NodeWeakMap();\nconst declSelector = [\"Declaration\"];\nfunction astTop(node) {\n    for (let cur = node;;) {\n        if (cur.type.isTop)\n            return cur;\n        if (!(cur = cur.parent))\n            return node;\n    }\n}\nfunction variableNames(doc, node, isVariable) {\n    if (node.to - node.from > 4096) {\n        let known = VariablesByNode.get(node);\n        if (known)\n            return known;\n        let result = [], seen = new Set, cursor = node.cursor(IterMode.IncludeAnonymous);\n        if (cursor.firstChild())\n            do {\n                for (let option of variableNames(doc, cursor.node, isVariable))\n                    if (!seen.has(option.label)) {\n                        seen.add(option.label);\n                        result.push(option);\n                    }\n            } while (cursor.nextSibling());\n        VariablesByNode.set(node, result);\n        return result;\n    }\n    else {\n        let result = [], seen = new Set;\n        node.cursor().iterate(node => {\n            var _a;\n            if (isVariable(node) && node.matchContext(declSelector) && ((_a = node.node.nextSibling) === null || _a === void 0 ? void 0 : _a.name) == \":\") {\n                let name = doc.sliceString(node.from, node.to);\n                if (!seen.has(name)) {\n                    seen.add(name);\n                    result.push({ label: name, type: \"variable\" });\n                }\n            }\n        });\n        return result;\n    }\n}\n/**\nCreate a completion source for a CSS dialect, providing a\npredicate for determining what kind of syntax node can act as a\ncompletable variable. This is used by language modes like Sass and\nLess to reuse this package's completion logic.\n*/\nconst defineCSSCompletionSource = (isVariable) => context => {\n    let { state, pos } = context, node = syntaxTree(state).resolveInner(pos, -1);\n    let isDash = node.type.isError && node.from == node.to - 1 && state.doc.sliceString(node.from, node.to) == \"-\";\n    if (node.name == \"PropertyName\" ||\n        (isDash || node.name == \"TagName\") && /^(Block|Styles)$/.test(node.resolve(node.to).name))\n        return { from: node.from, options: properties(), validFor: identifier };\n    if (node.name == \"ValueName\")\n        return { from: node.from, options: values, validFor: identifier };\n    if (node.name == \"PseudoClassName\")\n        return { from: node.from, options: pseudoClasses, validFor: identifier };\n    if (isVariable(node) || (context.explicit || isDash) && isVarArg(node, state.doc))\n        return { from: isVariable(node) || isDash ? node.from : pos,\n            options: variableNames(state.doc, astTop(node), isVariable),\n            validFor: variable };\n    if (node.name == \"TagName\") {\n        for (let { parent } = node; parent; parent = parent.parent)\n            if (parent.name == \"Block\")\n                return { from: node.from, options: properties(), validFor: identifier };\n        return { from: node.from, options: tags, validFor: identifier };\n    }\n    if (node.name == \"AtKeyword\")\n        return { from: node.from, options: atRules, validFor: identifier };\n    if (!context.explicit)\n        return null;\n    let above = node.resolve(pos), before = above.childBefore(pos);\n    if (before && before.name == \":\" && above.name == \"PseudoClassSelector\")\n        return { from: pos, options: pseudoClasses, validFor: identifier };\n    if (before && before.name == \":\" && above.name == \"Declaration\" || above.name == \"ArgList\")\n        return { from: pos, options: values, validFor: identifier };\n    if (above.name == \"Block\" || above.name == \"Styles\")\n        return { from: pos, options: properties(), validFor: identifier };\n    return null;\n};\n/**\nCSS property, variable, and value keyword completion source.\n*/\nconst cssCompletionSource = /*@__PURE__*/defineCSSCompletionSource(n => n.name == \"VariableName\");\n\n/**\nA language provider based on the [Lezer CSS\nparser](https://github.com/lezer-parser/css), extended with\nhighlighting and indentation information.\n*/\nconst cssLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"css\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Declaration: /*@__PURE__*/continuedIndent()\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                \"Block KeyframeList\": foldInside\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"/*\", close: \"*/\" } },\n        indentOnInput: /^\\s*\\}$/,\n        wordChars: \"-\"\n    }\n});\n/**\nLanguage support for CSS.\n*/\nfunction css() {\n    return new LanguageSupport(cssLanguage, cssLanguage.data.of({ autocomplete: cssCompletionSource }));\n}\n\nexport { css, cssCompletionSource, cssLanguage, defineCSSCompletionSource };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEA,IAAI,cAAc;AAClB,SAAS;IACL,IAAI,CAAC,eAAe,OAAO,YAAY,YAAY,SAAS,IAAI,EAAE;QAC9D,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI,EAAE,QAAQ,EAAE,EAAE,OAAO,IAAI;QACtD,IAAK,IAAI,QAAQ,MACb,IAAI,QAAQ,aAAa,QAAQ,YAAY;YACzC,IAAI,OAAO,KAAK,CAAC,KAAK,IAAI,UAAU;gBAChC,IAAI,QAAQ,IAAI,CAAC,OACb,OAAO,KAAK,OAAO,CAAC,UAAU,CAAA,KAAM,MAAM,GAAG,WAAW;gBAC5D,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO;oBACjB,MAAM,IAAI,CAAC;oBACX,KAAK,GAAG,CAAC;gBACb;YACJ;QACJ;QACJ,cAAc,MAAM,IAAI,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,MAAM;gBAAY,OAAO;gBAAM,OAAO,OAAO;YAAK,CAAC;IACjG;IACA,OAAO,eAAe,EAAE;AAC5B;AACA,MAAM,gBAAgB,WAAW,GAAE;IAC/B;IAAU;IAAS;IAAY;IAAY;IAAY;IACvD;IAAW;IAAO;IAAW;IAAW;IAAY;IACpD;IAAW;IAAwB;IAAS;IAC5C;IAAgB;IAAc;IAAiB;IAC/C;IAAiB;IAAgB;IAAc;IAAO;IACtD;IAAgB;IAAS;IAAY;IAAiB;IACtD;IAAM;IAAQ;IAAc;IAAgB;IAAQ;IAAQ;IAC5D;IAAS;IAAO;IAAa;IAAkB;IAC/C;IAAe;IAAc;IAAgB;IAAY;IACzD;IAAQ;IAAe;IAAqB;IAAa;IACzD;IAAY;IAAS;IAAQ;IAAS;IAAa;IAAW;IAC9D;IAAe;IAAS;IAAW;CACtC,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;QAAE,MAAM;QAAS,OAAO;IAAK,CAAC;AAC7C,MAAM,SAAS,WAAW,GAAE;IACxB;IAAS;IAAY;IAAgB;IAAY;IAAiB;IAClE;IAAS;IAAS;IAAO;IAAc;IAAc;IAAa;IAClE;IAAe;IAAgB;IAAa;IAAQ;IAAQ;IAAa;IAAS;IAClF;IAAc;IAAgB;IAAY;IAAc;IAAa;IAAY;IACjF;IAAiB;IAAS;IAAS;IAAc;IAAQ;IAAU;IAAU;IAC7E;IAAQ;IAAU;IAAS;IAAa;IAAc;IAAW;IAAU;IAC3E;IAAc;IAAmB;IAAgB;IAAc;IAAQ;IACvE;IAAuB;IAAW;IAAe;IAAS;IAAQ;IAAU;IAAY;IACxF;IAAe;IAAS;IAAQ;IAAe;IAAc;IAAY;IAAS;IAClF;IAAe;IAAU;IAAkB;IAAW;IAAa;IAAW;IAC9E;IAAY;IAAe;IAAgB;IAAc;IAAQ;IAAW;IAAY;IACxF;IAAQ;IAAS;IAAa;IAAgB;IAAW;IAAU;IAAU;IAAU;IACvF;IAAwB;IAAW;IAAkB;IAAS;IAAoB;IAClF;IAAmB;IAAoB;IAAc;IAAQ;IAAW;IACxE;IAAmB;IAAY;IAAY;IAAgB;IAAU;IAAU;IAAQ;IACvF;IAAQ;IAAW;IAAe;IAAY;IAAW;IAAW;IAAY;IAAS;IACzF;IAAwB;IAA2B;IAAyB;IAAa;IACzF;IAAY;IAAW;IAAmB;IAAkB;IAAW;IAAQ;IAAQ;IACvF;IAAS;IAAQ;IAAQ;IAAY;IAAc;IAAa;IAAY;IAC5E;IAAsB;IAAY;IAAQ;IAAU;IAAQ;IAAc;IAAQ;IAAU;IAC5F;IAAU;IAAa;IAAiB;IAAc;IAAO;IAAQ;IAAO;IAAQ;IACpF;IAAkB;IAAmB;IAAuB;IAAY;IAAkB;IAC1F;IAAW;IAAW;IAAU;IAAe;IAAgB;IAAe;IAC9E;IAAgB;IAAS;IAAU;IAAa;IAAU;IAAU;IAAW;IAC/E;IAAa;IAAS;IAAU;IAAQ;IAAS;IAAW;IAAW;IAAgB;IACvF;IAAmB;IAAS;IAAa;IAAW;IAAY;IAAS;IAAW;IAAQ;IAC5F;IAAqB;IAAe;IAAmB;IAAa;IAAO;IAAc;IACzF;IAAS;IAAU;IAAY;IAAU;IAAQ;IAAY;IAAe;IAAU;IACtF;IAAO;IAAa;IAAQ;IAAY;IAAwB;IAAY;IAAY;IACxF;IAAa;IAAe;IAAkB;IAAW;IAAiB;IAAa;IACvF;IAAU;IAAe;IAAU;IAAa;IAAW;IAAW;IAAa;IACnF;IAAW;IAAW;IAAc;IAAsB;IAAiB;IAAU;IACrF;IAAiB;IAAW;IAAY;IAAW;IAAe;IAAW;IAAQ;IACrF;IAAe;IAAc;IAAe;IAAgB;IAAW;IAAW;IAClF;IAAO;IAAY;IAAY;IAAe;IAAY;IAAe;IAAmB;IAC5F;IAAa;IAAc;IAA6B;IAAa;IAAU;IAAY;IAC3F;IAA6B;IAA6B;IAAY;IAAY;IAAS;IAC3F;IAAO;IAAQ;IAAS;IAAS;IAAU;IAAY;IAAW;IAAW;IAAW;IACxF;IAAO;IAAc;IAAe;IAAO;IAAU;IAAW;IAAY;IAAc;IAC1F;IAAS;IAAW;IAAU;IAAU;IAAU;IAAU;IAAU;IAAa;IACnF;IAAa;IAAc;IAAY;IAAkB;IAAiB;IAAY;IAAS;IAC/F;IAAU;IAAQ;IAAS;IAAS;IAAoB;IAAS;IACjE;IAAmB;IAA0B;IAAwB;IAAQ;IAAS;IACtF;IAAiB;IAAW;IAAc;IAAS;IAAe;IAAa;IAC/E;IAAe;IAAS;IAAgB;IAAiB;IAAgB;IAAa;IAAU;IAChG;IAAU;IAAc;IAAW;IAAU;IAAc;IAAO;IAAwB;IAC1F;IAAS;IAAa;IAAY;IAAW;IAAa;IAAS;IAAiB;IACpF;IAAgB;IAAsB;IAAsB;IAAsB;IAClF;IAAmB;IAAQ;IAAe;IAAY;IAAY;IAAa;IAAS;IACxF;IAAoB;IAAc;IAAmB;IAAqB;IAAgB;IAAM;IAChG;IAAa;IAAa;IAAe;IAAc;IAAc;IAAc;IACnF;IAAmB;IAAkB;IAAa;IAAsB;IAAS;IAAM;IACvF;IAAa;IAAO;IAAO;IAAY;IAAiB;IAAY;IAAW;IAC/E;IAAkB;IAAiB;IAAU;IAAY;IAAQ;IAAQ;IAAS;IAAU;IAC5F;IAAc;IAAS;IAAQ;IAAgB;IAAW;IAAW;IAAO;IAAY;CAC3F,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;QAAE,MAAM;QAAW,OAAO;IAAK,CAAC,GAAG,MAAM,CAAC,WAAW,GAAE;IAClE;IAAa;IAAgB;IAAQ;IAAc;IAAS;IAC5D;IAAU;IAAS;IAAkB;IAAQ;IAAc;IAC3D;IAAa;IAAa;IAAc;IAAa;IAAS;IAC9D;IAAY;IAAW;IAAQ;IAAY;IAAY;IACvD;IAAY;IAAa;IAAa;IAAe;IACrD;IAAc;IAAc;IAAW;IAAc;IACrD;IAAiB;IAAiB;IAAiB;IACnD;IAAY;IAAe;IAAW;IAAc;IACpD;IAAe;IAAe;IAAW;IAAa;IACtD;IAAQ;IAAa;IAAQ;IAAQ;IAAS;IAAe;IAC7D;IAAW;IAAa;IAAU;IAAS;IAAS;IACpD;IAAiB;IAAa;IAAgB;IAAa;IAC3D;IAAa;IAAwB;IAAa;IAAc;IAChE;IAAe;IAAiB;IAAgB;IAChD;IAAkB;IAAe;IAAQ;IAAa;IAAS;IAC/D;IAAU;IAAoB;IAAc;IAAgB;IAC5D;IAAkB;IAAmB;IAAqB;IAC1D;IAAmB;IAAgB;IAAa;IAAa;IAC7D;IAAe;IAAQ;IAAW;IAAS;IAAa;IAAU;IAClE;IAAU;IAAiB;IAAa;IAAiB;IACzD;IAAc;IAAa;IAAQ;IAAQ;IAAQ;IACnD;IAAU;IAAiB;IAAO;IAAa;IAAa;IAC5D;IAAU;IAAc;IAAY;IAAY;IAAU;IAAU;IACpE;IAAa;IAAa;IAAQ;IAAe;IAAa;IAC9D;IAAQ;IAAW;IAAU;IAAa;IAAU;IAAS;IAC7D;IAAc;IAAU;CAC3B,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;QAAE,MAAM;QAAY,OAAO;IAAK,CAAC;AAChD,MAAM,OAAO,WAAW,GAAE;IACtB;IAAK;IAAQ;IAAW;IAAW;IAAS;IAAK;IAAO;IAAO;IAAc;IAC7E;IAAM;IAAU;IAAU;IAAW;IAAQ;IAAQ;IAAO;IAAY;IAAM;IAC9E;IAAW;IAAO;IAAU;IAAO;IAAM;IAAM;IAAM;IAAc;IAAU;IAC7E;IAAQ;IAAU;IAAU;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAQ;IAAK;IACnF;IAAO;IAAS;IAAO;IAAO;IAAS;IAAU;IAAM;IAAQ;IAAS;IAAO;IAAM;IACrF;IAAK;IAAO;IAAQ;IAAW;IAAU;IAAS;IAAU;IAAQ;IAAU;IAAO;IACrF;IAAO;IAAS;IAAS;IAAM;IAAY;IAAY;IAAS;IAAM;IAAS;IAAM;IAAK;CAC7F,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;QAAE,MAAM;QAAQ,OAAO;IAAK,CAAC;AAC5C,MAAM,UAAU,WAAW,GAAE;IACzB;IAAY;IAAkB;IAAc;IAAkB;IAAc;IAC5E;IAAwB;IAAW;IAAc;IAAU;IAAU;IAAc;IACnF;IAAiB;IAAa;IAAU;IAAmB;IAAa;CAC3E,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;QAAE,MAAM;QAAW;IAAM,CAAC;AAC1C,MAAM,aAAa,2BAA2B,WAAW;AACzD,SAAS,SAAS,IAAI,EAAE,GAAG;IACvB,IAAI;IACJ,IAAI,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EACrC,OAAO,KAAK,MAAM,IAAI;IAC1B,IAAI,KAAK,IAAI,IAAI,WACb,OAAO;IACX,IAAI,SAAS,CAAC,KAAK,KAAK,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU;IAClF,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,IAAI,KAAK,UACjE,OAAO;IACX,OAAO,IAAI,WAAW,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,KAAK;AACtD;AACA,MAAM,kBAAkB,WAAW,GAAE,IAAI,qJAAA,CAAA,cAAW;AACpD,MAAM,eAAe;IAAC;CAAc;AACpC,SAAS,OAAO,IAAI;IAChB,IAAK,IAAI,MAAM,OAAQ;QACnB,IAAI,IAAI,IAAI,CAAC,KAAK,EACd,OAAO;QACX,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,GAClB,OAAO;IACf;AACJ;AACA,SAAS,cAAc,GAAG,EAAE,IAAI,EAAE,UAAU;IACxC,IAAI,KAAK,EAAE,GAAG,KAAK,IAAI,GAAG,MAAM;QAC5B,IAAI,QAAQ,gBAAgB,GAAG,CAAC;QAChC,IAAI,OACA,OAAO;QACX,IAAI,SAAS,EAAE,EAAE,OAAO,IAAI,KAAK,SAAS,KAAK,MAAM,CAAC,qJAAA,CAAA,WAAQ,CAAC,gBAAgB;QAC/E,IAAI,OAAO,UAAU,IACjB,GAAG;YACC,KAAK,IAAI,UAAU,cAAc,KAAK,OAAO,IAAI,EAAE,YAC/C,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG;gBACzB,KAAK,GAAG,CAAC,OAAO,KAAK;gBACrB,OAAO,IAAI,CAAC;YAChB;QACR,QAAS,OAAO,WAAW,GAAI;QACnC,gBAAgB,GAAG,CAAC,MAAM;QAC1B,OAAO;IACX,OACK;QACD,IAAI,SAAS,EAAE,EAAE,OAAO,IAAI;QAC5B,KAAK,MAAM,GAAG,OAAO,CAAC,CAAA;YAClB,IAAI;YACJ,IAAI,WAAW,SAAS,KAAK,YAAY,CAAC,iBAAiB,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK;gBAC3I,IAAI,OAAO,IAAI,WAAW,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE;gBAC7C,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO;oBACjB,KAAK,GAAG,CAAC;oBACT,OAAO,IAAI,CAAC;wBAAE,OAAO;wBAAM,MAAM;oBAAW;gBAChD;YACJ;QACJ;QACA,OAAO;IACX;AACJ;AACA;;;;;AAKA,GACA,MAAM,4BAA4B,CAAC,aAAe,CAAA;QAC9C,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,SAAS,OAAO,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,OAAO,YAAY,CAAC,KAAK,CAAC;QAC1E,IAAI,SAAS,KAAK,IAAI,CAAC,OAAO,IAAI,KAAK,IAAI,IAAI,KAAK,EAAE,GAAG,KAAK,MAAM,GAAG,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,KAAK;QAC3G,IAAI,KAAK,IAAI,IAAI,kBACb,CAAC,UAAU,KAAK,IAAI,IAAI,SAAS,KAAK,mBAAmB,IAAI,CAAC,KAAK,OAAO,CAAC,KAAK,EAAE,EAAE,IAAI,GACxF,OAAO;YAAE,MAAM,KAAK,IAAI;YAAE,SAAS;YAAc,UAAU;QAAW;QAC1E,IAAI,KAAK,IAAI,IAAI,aACb,OAAO;YAAE,MAAM,KAAK,IAAI;YAAE,SAAS;YAAQ,UAAU;QAAW;QACpE,IAAI,KAAK,IAAI,IAAI,mBACb,OAAO;YAAE,MAAM,KAAK,IAAI;YAAE,SAAS;YAAe,UAAU;QAAW;QAC3E,IAAI,WAAW,SAAS,CAAC,QAAQ,QAAQ,IAAI,MAAM,KAAK,SAAS,MAAM,MAAM,GAAG,GAC5E,OAAO;YAAE,MAAM,WAAW,SAAS,SAAS,KAAK,IAAI,GAAG;YACpD,SAAS,cAAc,MAAM,GAAG,EAAE,OAAO,OAAO;YAChD,UAAU;QAAS;QAC3B,IAAI,KAAK,IAAI,IAAI,WAAW;YACxB,IAAK,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,SAAS,OAAO,MAAM,CACtD,IAAI,OAAO,IAAI,IAAI,SACf,OAAO;gBAAE,MAAM,KAAK,IAAI;gBAAE,SAAS;gBAAc,UAAU;YAAW;YAC9E,OAAO;gBAAE,MAAM,KAAK,IAAI;gBAAE,SAAS;gBAAM,UAAU;YAAW;QAClE;QACA,IAAI,KAAK,IAAI,IAAI,aACb,OAAO;YAAE,MAAM,KAAK,IAAI;YAAE,SAAS;YAAS,UAAU;QAAW;QACrE,IAAI,CAAC,QAAQ,QAAQ,EACjB,OAAO;QACX,IAAI,QAAQ,KAAK,OAAO,CAAC,MAAM,SAAS,MAAM,WAAW,CAAC;QAC1D,IAAI,UAAU,OAAO,IAAI,IAAI,OAAO,MAAM,IAAI,IAAI,uBAC9C,OAAO;YAAE,MAAM;YAAK,SAAS;YAAe,UAAU;QAAW;QACrE,IAAI,UAAU,OAAO,IAAI,IAAI,OAAO,MAAM,IAAI,IAAI,iBAAiB,MAAM,IAAI,IAAI,WAC7E,OAAO;YAAE,MAAM;YAAK,SAAS;YAAQ,UAAU;QAAW;QAC9D,IAAI,MAAM,IAAI,IAAI,WAAW,MAAM,IAAI,IAAI,UACvC,OAAO;YAAE,MAAM;YAAK,SAAS;YAAc,UAAU;QAAW;QACpE,OAAO;IACX;AACA;;AAEA,GACA,MAAM,sBAAsB,WAAW,GAAE,0BAA0B,CAAA,IAAK,EAAE,IAAI,IAAI;AAElF;;;;AAIA,GACA,MAAM,cAAc,WAAW,GAAE,4JAAA,CAAA,aAAU,CAAC,MAAM,CAAC;IAC/C,MAAM;IACN,QAAQ,WAAW,GAAE,kJAAA,CAAA,SAAM,CAAC,SAAS,CAAC;QAClC,OAAO;YACH,WAAW,GAAE,4JAAA,CAAA,iBAAc,CAAC,GAAG,CAAC;gBAC5B,aAAa,WAAW,GAAE,CAAA,GAAA,4JAAA,CAAA,kBAAe,AAAD;YAC5C;YACA,WAAW,GAAE,4JAAA,CAAA,eAAY,CAAC,GAAG,CAAC;gBAC1B,sBAAsB,4JAAA,CAAA,aAAU;YACpC;SACH;IACL;IACA,cAAc;QACV,eAAe;YAAE,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAK;QAAE;QACpD,eAAe;QACf,WAAW;IACf;AACJ;AACA;;AAEA,GACA,SAAS;IACL,OAAO,IAAI,4JAAA,CAAA,kBAAe,CAAC,aAAa,YAAY,IAAI,CAAC,EAAE,CAAC;QAAE,cAAc;IAAoB;AACpG", "ignoreList": [0], "debugId": null}}]}