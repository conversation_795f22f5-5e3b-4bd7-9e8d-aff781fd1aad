{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/ebnf.js"], "sourcesContent": ["var commentType = {slash: 0, parenthesis: 1};\nvar stateType = {comment: 0, _string: 1, characterClass: 2};\n\nexport const ebnf = {\n  name: \"ebnf\",\n  startState: function () {\n    return {\n      stringType: null,\n      commentType: null,\n      braced: 0,\n      lhs: true,\n      localState: null,\n      stack: [],\n      inDefinition: false\n    };\n  },\n  token: function (stream, state) {\n    if (!stream) return;\n\n    //check for state changes\n    if (state.stack.length === 0) {\n      //strings\n      if ((stream.peek() == '\"') || (stream.peek() == \"'\")) {\n        state.stringType = stream.peek();\n        stream.next(); // Skip quote\n        state.stack.unshift(stateType._string);\n      } else if (stream.match('/*')) { //comments starting with /*\n        state.stack.unshift(stateType.comment);\n        state.commentType = commentType.slash;\n      } else if (stream.match('(*')) { //comments starting with (*\n        state.stack.unshift(stateType.comment);\n        state.commentType = commentType.parenthesis;\n      }\n    }\n\n    //return state\n    //stack has\n    switch (state.stack[0]) {\n    case stateType._string:\n      while (state.stack[0] === stateType._string && !stream.eol()) {\n        if (stream.peek() === state.stringType) {\n          stream.next(); // Skip quote\n          state.stack.shift(); // Clear flag\n        } else if (stream.peek() === \"\\\\\") {\n          stream.next();\n          stream.next();\n        } else {\n          stream.match(/^.[^\\\\\\\"\\']*/);\n        }\n      }\n      return state.lhs ? \"property\" : \"string\"; // Token style\n\n    case stateType.comment:\n      while (state.stack[0] === stateType.comment && !stream.eol()) {\n        if (state.commentType === commentType.slash && stream.match('*/')) {\n          state.stack.shift(); // Clear flag\n          state.commentType = null;\n        } else if (state.commentType === commentType.parenthesis && stream.match('*)')) {\n          state.stack.shift(); // Clear flag\n          state.commentType = null;\n        } else {\n          stream.match(/^.[^\\*]*/);\n        }\n      }\n      return \"comment\";\n\n    case stateType.characterClass:\n      while (state.stack[0] === stateType.characterClass && !stream.eol()) {\n        if (!(stream.match(/^[^\\]\\\\]+/) || stream.match('.'))) {\n          state.stack.shift();\n        }\n      }\n      return \"operator\";\n    }\n\n    var peek = stream.peek();\n\n    //no stack\n    switch (peek) {\n    case \"[\":\n      stream.next();\n      state.stack.unshift(stateType.characterClass);\n      return \"bracket\";\n    case \":\":\n    case \"|\":\n    case \";\":\n      stream.next();\n      return \"operator\";\n    case \"%\":\n      if (stream.match(\"%%\")) {\n        return \"header\";\n      } else if (stream.match(/[%][A-Za-z]+/)) {\n        return \"keyword\";\n      } else if (stream.match(/[%][}]/)) {\n        return \"bracket\";\n      }\n      break;\n    case \"/\":\n      if (stream.match(/[\\/][A-Za-z]+/)) {\n        return \"keyword\";\n      }\n    case \"\\\\\":\n      if (stream.match(/[\\][a-z]+/)) {\n        return \"string.special\";\n      }\n    case \".\":\n      if (stream.match(\".\")) {\n        return \"atom\";\n      }\n    case \"*\":\n    case \"-\":\n    case \"+\":\n    case \"^\":\n      if (stream.match(peek)) {\n        return \"atom\";\n      }\n    case \"$\":\n      if (stream.match(\"$$\")) {\n        return \"builtin\";\n      } else if (stream.match(/[$][0-9]+/)) {\n        return \"variableName.special\";\n      }\n    case \"<\":\n      if (stream.match(/<<[a-zA-Z_]+>>/)) {\n        return \"builtin\";\n      }\n    }\n\n    if (stream.match('//')) {\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (stream.match('return')) {\n      return \"operator\";\n    } else if (stream.match(/^[a-zA-Z_][a-zA-Z0-9_]*/)) {\n      if (stream.match(/(?=[\\(.])/)) {\n        return \"variable\";\n      } else if (stream.match(/(?=[\\s\\n]*[:=])/)) {\n        return \"def\";\n      }\n      return \"variableName.special\";\n    } else if ([\"[\", \"]\", \"(\", \")\"].indexOf(stream.peek()) != -1) {\n      stream.next();\n      return \"bracket\";\n    } else if (!stream.eatSpace()) {\n      stream.next();\n    }\n    return null;\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,cAAc;IAAC,OAAO;IAAG,aAAa;AAAC;AAC3C,IAAI,YAAY;IAAC,SAAS;IAAG,SAAS;IAAG,gBAAgB;AAAC;AAEnD,MAAM,OAAO;IAClB,MAAM;IACN,YAAY;QACV,OAAO;YACL,YAAY;YACZ,aAAa;YACb,QAAQ;YACR,KAAK;YACL,YAAY;YACZ,OAAO,EAAE;YACT,cAAc;QAChB;IACF;IACA,OAAO,SAAU,MAAM,EAAE,KAAK;QAC5B,IAAI,CAAC,QAAQ;QAEb,yBAAyB;QACzB,IAAI,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG;YAC5B,SAAS;YACT,IAAI,AAAC,OAAO,IAAI,MAAM,OAAS,OAAO,IAAI,MAAM,KAAM;gBACpD,MAAM,UAAU,GAAG,OAAO,IAAI;gBAC9B,OAAO,IAAI,IAAI,aAAa;gBAC5B,MAAM,KAAK,CAAC,OAAO,CAAC,UAAU,OAAO;YACvC,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO;gBAC7B,MAAM,KAAK,CAAC,OAAO,CAAC,UAAU,OAAO;gBACrC,MAAM,WAAW,GAAG,YAAY,KAAK;YACvC,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO;gBAC7B,MAAM,KAAK,CAAC,OAAO,CAAC,UAAU,OAAO;gBACrC,MAAM,WAAW,GAAG,YAAY,WAAW;YAC7C;QACF;QAEA,cAAc;QACd,WAAW;QACX,OAAQ,MAAM,KAAK,CAAC,EAAE;YACtB,KAAK,UAAU,OAAO;gBACpB,MAAO,MAAM,KAAK,CAAC,EAAE,KAAK,UAAU,OAAO,IAAI,CAAC,OAAO,GAAG,GAAI;oBAC5D,IAAI,OAAO,IAAI,OAAO,MAAM,UAAU,EAAE;wBACtC,OAAO,IAAI,IAAI,aAAa;wBAC5B,MAAM,KAAK,CAAC,KAAK,IAAI,aAAa;oBACpC,OAAO,IAAI,OAAO,IAAI,OAAO,MAAM;wBACjC,OAAO,IAAI;wBACX,OAAO,IAAI;oBACb,OAAO;wBACL,OAAO,KAAK,CAAC;oBACf;gBACF;gBACA,OAAO,MAAM,GAAG,GAAG,aAAa,UAAU,cAAc;YAE1D,KAAK,UAAU,OAAO;gBACpB,MAAO,MAAM,KAAK,CAAC,EAAE,KAAK,UAAU,OAAO,IAAI,CAAC,OAAO,GAAG,GAAI;oBAC5D,IAAI,MAAM,WAAW,KAAK,YAAY,KAAK,IAAI,OAAO,KAAK,CAAC,OAAO;wBACjE,MAAM,KAAK,CAAC,KAAK,IAAI,aAAa;wBAClC,MAAM,WAAW,GAAG;oBACtB,OAAO,IAAI,MAAM,WAAW,KAAK,YAAY,WAAW,IAAI,OAAO,KAAK,CAAC,OAAO;wBAC9E,MAAM,KAAK,CAAC,KAAK,IAAI,aAAa;wBAClC,MAAM,WAAW,GAAG;oBACtB,OAAO;wBACL,OAAO,KAAK,CAAC;oBACf;gBACF;gBACA,OAAO;YAET,KAAK,UAAU,cAAc;gBAC3B,MAAO,MAAM,KAAK,CAAC,EAAE,KAAK,UAAU,cAAc,IAAI,CAAC,OAAO,GAAG,GAAI;oBACnE,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC,IAAI,GAAG;wBACrD,MAAM,KAAK,CAAC,KAAK;oBACnB;gBACF;gBACA,OAAO;QACT;QAEA,IAAI,OAAO,OAAO,IAAI;QAEtB,UAAU;QACV,OAAQ;YACR,KAAK;gBACH,OAAO,IAAI;gBACX,MAAM,KAAK,CAAC,OAAO,CAAC,UAAU,cAAc;gBAC5C,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,IAAI;gBACX,OAAO;YACT,KAAK;gBACH,IAAI,OAAO,KAAK,CAAC,OAAO;oBACtB,OAAO;gBACT,OAAO,IAAI,OAAO,KAAK,CAAC,iBAAiB;oBACvC,OAAO;gBACT,OAAO,IAAI,OAAO,KAAK,CAAC,WAAW;oBACjC,OAAO;gBACT;gBACA;YACF,KAAK;gBACH,IAAI,OAAO,KAAK,CAAC,kBAAkB;oBACjC,OAAO;gBACT;YACF,KAAK;gBACH,IAAI,OAAO,KAAK,CAAC,cAAc;oBAC7B,OAAO;gBACT;YACF,KAAK;gBACH,IAAI,OAAO,KAAK,CAAC,MAAM;oBACrB,OAAO;gBACT;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,OAAO,KAAK,CAAC,OAAO;oBACtB,OAAO;gBACT;YACF,KAAK;gBACH,IAAI,OAAO,KAAK,CAAC,OAAO;oBACtB,OAAO;gBACT,OAAO,IAAI,OAAO,KAAK,CAAC,cAAc;oBACpC,OAAO;gBACT;YACF,KAAK;gBACH,IAAI,OAAO,KAAK,CAAC,mBAAmB;oBAClC,OAAO;gBACT;QACF;QAEA,IAAI,OAAO,KAAK,CAAC,OAAO;YACtB,OAAO,SAAS;YAChB,OAAO;QACT,OAAO,IAAI,OAAO,KAAK,CAAC,WAAW;YACjC,OAAO;QACT,OAAO,IAAI,OAAO,KAAK,CAAC,4BAA4B;YAClD,IAAI,OAAO,KAAK,CAAC,cAAc;gBAC7B,OAAO;YACT,OAAO,IAAI,OAAO,KAAK,CAAC,oBAAoB;gBAC1C,OAAO;YACT;YACA,OAAO;QACT,OAAO,IAAI;YAAC;YAAK;YAAK;YAAK;SAAI,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG;YAC5D,OAAO,IAAI;YACX,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,QAAQ,IAAI;YAC7B,OAAO,IAAI;QACb;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}