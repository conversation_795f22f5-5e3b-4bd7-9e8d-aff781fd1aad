{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/index.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/kbar/index.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/kbar/index.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/kbar/index.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/kbar/index.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/kbar/index.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/app-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const company = registerClientReference(\n    function() { throw new Error(\"Attempted to call company() from the server but company is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-sidebar.tsx <module evaluation>\",\n    \"company\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/app-sidebar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-sidebar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,uEACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyS,GACtU,uEACA", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/app-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const company = registerClientReference(\n    function() { throw new Error(\"Attempted to call company() from the server but company is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-sidebar.tsx\",\n    \"company\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/app-sidebar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/app-sidebar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,mDACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"Sidebar\",\n);\nexport const SidebarContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarContent\",\n);\nexport const SidebarFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarFooter() from the server but <PERSON><PERSON><PERSON>ooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarFooter\",\n);\nexport const SidebarGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroup\",\n);\nexport const SidebarGroupAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupAction\",\n);\nexport const SidebarGroupContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupContent\",\n);\nexport const SidebarGroupLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupLabel\",\n);\nexport const SidebarHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarHeader\",\n);\nexport const SidebarInput = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarInput\",\n);\nexport const SidebarInset = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarInset\",\n);\nexport const SidebarMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenu\",\n);\nexport const SidebarMenuAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuAction\",\n);\nexport const SidebarMenuBadge = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuBadge\",\n);\nexport const SidebarMenuButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuButton\",\n);\nexport const SidebarMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuItem\",\n);\nexport const SidebarMenuSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSkeleton\",\n);\nexport const SidebarMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSub\",\n);\nexport const SidebarMenuSubButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSubButton\",\n);\nexport const SidebarMenuSubItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSubItem\",\n);\nexport const SidebarProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarProvider\",\n);\nexport const SidebarRail = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarRail\",\n);\nexport const SidebarSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarSeparator\",\n);\nexport const SidebarTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarTrigger\",\n);\nexport const useSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"useSidebar\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,+DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,+DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,+DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,+DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,+DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"Sidebar\",\n);\nexport const SidebarContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarContent\",\n);\nexport const SidebarFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarFooter\",\n);\nexport const SidebarGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarGroup\",\n);\nexport const SidebarGroupAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarGroupAction\",\n);\nexport const SidebarGroupContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarGroupContent\",\n);\nexport const SidebarGroupLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarGroupLabel\",\n);\nexport const SidebarHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarHeader\",\n);\nexport const SidebarInput = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarInput\",\n);\nexport const SidebarInset = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarInset\",\n);\nexport const SidebarMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenu\",\n);\nexport const SidebarMenuAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuAction\",\n);\nexport const SidebarMenuBadge = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuBadge\",\n);\nexport const SidebarMenuButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuButton\",\n);\nexport const SidebarMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuItem\",\n);\nexport const SidebarMenuSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSkeleton\",\n);\nexport const SidebarMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSub\",\n);\nexport const SidebarMenuSubButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSubButton\",\n);\nexport const SidebarMenuSubItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSubItem\",\n);\nexport const SidebarProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarProvider\",\n);\nexport const SidebarRail = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarRail\",\n);\nexport const SidebarSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarSeparator\",\n);\nexport const SidebarTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarTrigger\",\n);\nexport const useSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"useSidebar\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,2CACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,2CACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2CACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2CACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2CACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2CACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2CACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,2CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,2CACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,2CACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2CACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2CACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/app/dashboard/layout.tsx"], "sourcesContent": ["import KBar from '@/components/kbar';\r\nimport AppSidebar from '@/components/layout/app-sidebar';\r\nimport Header from '@/components/layout/header';\r\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\r\nimport type { Metadata } from 'next';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Akademi IAI Dashboard',\r\n  description: 'LMS Sertifikasi Profesional'\r\n};\r\n\r\nexport default async function DashboardLayout({\r\n  children\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  // Persisting the sidebar state in the cookie.\r\n  const cookieStore = await cookies();\r\n  const defaultOpen = cookieStore.get('sidebar_state')?.value === 'true';\r\n  return (\r\n    <KBar>\r\n      <SidebarProvider defaultOpen={defaultOpen}>\r\n        <AppSidebar />\r\n        <SidebarInset>\r\n          <Header />\r\n          {/* page main content */}\r\n          <main className='h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8'>\r\n            {children}\r\n          </main>\r\n          {/* page main content ends */}\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    </KBar>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,eAAe,gBAAgB,EAC5C,QAAQ,EAGT;IACC,8CAA8C;IAC9C,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc,YAAY,GAAG,CAAC,kBAAkB,UAAU;IAChE,qBACE,8OAAC,mIAAA,CAAA,UAAI;kBACH,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,aAAa;;8BAC5B,8OAAC,8IAAA,CAAA,UAAU;;;;;8BACX,8OAAC,mIAAA,CAAA,eAAY;;sCACX,8OAAC,sIAAA,CAAA,UAAM;;;;;sCAEP,8OAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAOb", "debugId": null}}]}