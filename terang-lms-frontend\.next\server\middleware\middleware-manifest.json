{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|static|.*\\..*|_static|_vercel).*){(\\\\.json)}?", "originalSource": "/((?!_next|static|.*\\..*|_static|_vercel).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oQU80BEb9Ysp0y5Cc6KvygFOTlbLolBab1Eeg0Ic8No=", "__NEXT_PREVIEW_MODE_ID": "18d76aa55448619513ac38b3f1e45d5d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6cc6e953d08c946f5b38d8801a173f3f87142dfeb6d28623713420484d387300", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fe8e713a0eec28e19e69eb3b56ee9d4746e0e8052260b01de4bdaf614b91c446"}}}, "instrumentation": null, "functions": {}}