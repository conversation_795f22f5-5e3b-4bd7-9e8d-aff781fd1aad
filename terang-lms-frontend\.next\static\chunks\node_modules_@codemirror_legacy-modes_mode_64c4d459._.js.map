{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/simple-mode.js"], "sourcesContent": ["export function simpleMode(states) {\n  ensureState(states, \"start\");\n  var states_ = {}, meta = states.languageData || {}, hasIndentation = false;\n  for (var state in states) if (state != meta && states.hasOwnProperty(state)) {\n    var list = states_[state] = [], orig = states[state];\n    for (var i = 0; i < orig.length; i++) {\n      var data = orig[i];\n      list.push(new Rule(data, states));\n      if (data.indent || data.dedent) hasIndentation = true;\n    }\n  }\n  return {\n    name: meta.name,\n    startState: function() {\n      return {state: \"start\", pending: null, indent: hasIndentation ? [] : null};\n    },\n    copyState: function(state) {\n      var s = {state: state.state, pending: state.pending, indent: state.indent && state.indent.slice(0)};\n      if (state.stack)\n        s.stack = state.stack.slice(0);\n      return s;\n    },\n    token: tokenFunction(states_),\n    indent: indentFunction(states_, meta),\n    mergeTokens: meta.mergeTokens,\n    languageData: meta\n  }\n};\n\nfunction ensureState(states, name) {\n  if (!states.hasOwnProperty(name))\n    throw new Error(\"Undefined state \" + name + \" in simple mode\");\n}\n\nfunction toRegex(val, caret) {\n  if (!val) return /(?:)/;\n  var flags = \"\";\n  if (val instanceof RegExp) {\n    if (val.ignoreCase) flags = \"i\";\n    val = val.source;\n  } else {\n    val = String(val);\n  }\n  return new RegExp((caret === false ? \"\" : \"^\") + \"(?:\" + val + \")\", flags);\n}\n\nfunction asToken(val) {\n  if (!val) return null;\n  if (val.apply) return val\n  if (typeof val == \"string\") return val.replace(/\\./g, \" \");\n  var result = [];\n  for (var i = 0; i < val.length; i++)\n    result.push(val[i] && val[i].replace(/\\./g, \" \"));\n  return result;\n}\n\nfunction Rule(data, states) {\n  if (data.next || data.push) ensureState(states, data.next || data.push);\n  this.regex = toRegex(data.regex);\n  this.token = asToken(data.token);\n  this.data = data;\n}\n\nfunction tokenFunction(states) {\n  return function(stream, state) {\n    if (state.pending) {\n      var pend = state.pending.shift();\n      if (state.pending.length == 0) state.pending = null;\n      stream.pos += pend.text.length;\n      return pend.token;\n    }\n\n    var curState = states[state.state];\n    for (var i = 0; i < curState.length; i++) {\n      var rule = curState[i];\n      var matches = (!rule.data.sol || stream.sol()) && stream.match(rule.regex);\n      if (matches) {\n        if (rule.data.next) {\n          state.state = rule.data.next;\n        } else if (rule.data.push) {\n          (state.stack || (state.stack = [])).push(state.state);\n          state.state = rule.data.push;\n        } else if (rule.data.pop && state.stack && state.stack.length) {\n          state.state = state.stack.pop();\n        }\n\n        if (rule.data.indent)\n          state.indent.push(stream.indentation() + stream.indentUnit);\n        if (rule.data.dedent)\n          state.indent.pop();\n        var token = rule.token\n        if (token && token.apply) token = token(matches)\n        if (matches.length > 2 && rule.token && typeof rule.token != \"string\") {\n          state.pending = [];\n          for (var j = 2; j < matches.length; j++)\n            if (matches[j])\n              state.pending.push({text: matches[j], token: rule.token[j - 1]});\n          stream.backUp(matches[0].length - (matches[1] ? matches[1].length : 0));\n          return token[0];\n        } else if (token && token.join) {\n          return token[0];\n        } else {\n          return token;\n        }\n      }\n    }\n    stream.next();\n    return null;\n  };\n}\n\nfunction indentFunction(states, meta) {\n  return function(state, textAfter) {\n    if (state.indent == null || meta.dontIndentStates && meta.dontIndentStates.indexOf(state.state) > -1)\n      return null\n\n    var pos = state.indent.length - 1, rules = states[state.state];\n    scan: for (;;) {\n      for (var i = 0; i < rules.length; i++) {\n        var rule = rules[i];\n        if (rule.data.dedent && rule.data.dedentIfLineStart !== false) {\n          var m = rule.regex.exec(textAfter);\n          if (m && m[0]) {\n            pos--;\n            if (rule.next || rule.push) rules = states[rule.next || rule.push];\n            textAfter = textAfter.slice(m[0].length);\n            continue scan;\n          }\n        }\n      }\n      break;\n    }\n    return pos < 0 ? 0 : state.indent[pos];\n  };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,WAAW,MAAM;IAC/B,YAAY,QAAQ;IACpB,IAAI,UAAU,CAAC,GAAG,OAAO,OAAO,YAAY,IAAI,CAAC,GAAG,iBAAiB;IACrE,IAAK,IAAI,SAAS,OAAQ,IAAI,SAAS,QAAQ,OAAO,cAAc,CAAC,QAAQ;QAC3E,IAAI,OAAO,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,OAAO,MAAM,CAAC,MAAM;QACpD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM;YACzB,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,EAAE,iBAAiB;QACnD;IACF;IACA,OAAO;QACL,MAAM,KAAK,IAAI;QACf,YAAY;YACV,OAAO;gBAAC,OAAO;gBAAS,SAAS;gBAAM,QAAQ,iBAAiB,EAAE,GAAG;YAAI;QAC3E;QACA,WAAW,SAAS,KAAK;YACvB,IAAI,IAAI;gBAAC,OAAO,MAAM,KAAK;gBAAE,SAAS,MAAM,OAAO;gBAAE,QAAQ,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,KAAK,CAAC;YAAE;YAClG,IAAI,MAAM,KAAK,EACb,EAAE,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC;YAC9B,OAAO;QACT;QACA,OAAO,cAAc;QACrB,QAAQ,eAAe,SAAS;QAChC,aAAa,KAAK,WAAW;QAC7B,cAAc;IAChB;AACF;;AAEA,SAAS,YAAY,MAAM,EAAE,IAAI;IAC/B,IAAI,CAAC,OAAO,cAAc,CAAC,OACzB,MAAM,IAAI,MAAM,qBAAqB,OAAO;AAChD;AAEA,SAAS,QAAQ,GAAG,EAAE,KAAK;IACzB,IAAI,CAAC,KAAK,OAAO;IACjB,IAAI,QAAQ;IACZ,IAAI,eAAe,QAAQ;QACzB,IAAI,IAAI,UAAU,EAAE,QAAQ;QAC5B,MAAM,IAAI,MAAM;IAClB,OAAO;QACL,MAAM,OAAO;IACf;IACA,OAAO,IAAI,OAAO,CAAC,UAAU,QAAQ,KAAK,GAAG,IAAI,QAAQ,MAAM,KAAK;AACtE;AAEA,SAAS,QAAQ,GAAG;IAClB,IAAI,CAAC,KAAK,OAAO;IACjB,IAAI,IAAI,KAAK,EAAE,OAAO;IACtB,IAAI,OAAO,OAAO,UAAU,OAAO,IAAI,OAAO,CAAC,OAAO;IACtD,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAC9B,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;IAC9C,OAAO;AACT;AAEA,SAAS,KAAK,IAAI,EAAE,MAAM;IACxB,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,EAAE,YAAY,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI;IACtE,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK;IAC/B,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK;IAC/B,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,SAAS,cAAc,MAAM;IAC3B,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,MAAM,OAAO,EAAE;YACjB,IAAI,OAAO,MAAM,OAAO,CAAC,KAAK;YAC9B,IAAI,MAAM,OAAO,CAAC,MAAM,IAAI,GAAG,MAAM,OAAO,GAAG;YAC/C,OAAO,GAAG,IAAI,KAAK,IAAI,CAAC,MAAM;YAC9B,OAAO,KAAK,KAAK;QACnB;QAEA,IAAI,WAAW,MAAM,CAAC,MAAM,KAAK,CAAC;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACxC,IAAI,OAAO,QAAQ,CAAC,EAAE;YACtB,IAAI,UAAU,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,EAAE,KAAK,OAAO,KAAK,CAAC,KAAK,KAAK;YACzE,IAAI,SAAS;gBACX,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;oBAClB,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,IAAI;gBAC9B,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;oBACzB,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,KAAK;oBACpD,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,IAAI;gBAC9B,OAAO,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE;oBAC7D,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,GAAG;gBAC/B;gBAEA,IAAI,KAAK,IAAI,CAAC,MAAM,EAClB,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO,WAAW,KAAK,OAAO,UAAU;gBAC5D,IAAI,KAAK,IAAI,CAAC,MAAM,EAClB,MAAM,MAAM,CAAC,GAAG;gBAClB,IAAI,QAAQ,KAAK,KAAK;gBACtB,IAAI,SAAS,MAAM,KAAK,EAAE,QAAQ,MAAM;gBACxC,IAAI,QAAQ,MAAM,GAAG,KAAK,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,IAAI,UAAU;oBACrE,MAAM,OAAO,GAAG,EAAE;oBAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAClC,IAAI,OAAO,CAAC,EAAE,EACZ,MAAM,OAAO,CAAC,IAAI,CAAC;wBAAC,MAAM,OAAO,CAAC,EAAE;wBAAE,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;oBAAA;oBAClE,OAAO,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC;oBACrE,OAAO,KAAK,CAAC,EAAE;gBACjB,OAAO,IAAI,SAAS,MAAM,IAAI,EAAE;oBAC9B,OAAO,KAAK,CAAC,EAAE;gBACjB,OAAO;oBACL,OAAO;gBACT;YACF;QACF;QACA,OAAO,IAAI;QACX,OAAO;IACT;AACF;AAEA,SAAS,eAAe,MAAM,EAAE,IAAI;IAClC,OAAO,SAAS,KAAK,EAAE,SAAS;QAC9B,IAAI,MAAM,MAAM,IAAI,QAAQ,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,GACjG,OAAO;QAET,IAAI,MAAM,MAAM,MAAM,CAAC,MAAM,GAAG,GAAG,QAAQ,MAAM,CAAC,MAAM,KAAK,CAAC;QAC9D,MAAM,OAAS;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,IAAI,OAAO,KAAK,CAAC,EAAE;gBACnB,IAAI,KAAK,IAAI,CAAC,MAAM,IAAI,KAAK,IAAI,CAAC,iBAAiB,KAAK,OAAO;oBAC7D,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC;oBACxB,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE;wBACb;wBACA,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,EAAE,QAAQ,MAAM,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;wBAClE,YAAY,UAAU,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM;wBACvC,SAAS;oBACX;gBACF;YACF;YACA;QACF;QACA,OAAO,MAAM,IAAI,IAAI,MAAM,MAAM,CAAC,IAAI;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/dockerfile.js"], "sourcesContent": ["import {simpleMode} from \"./simple-mode.js\"\n\nvar from = \"from\";\nvar fromRegex = new RegExp(\"^(\\\\s*)\\\\b(\" + from + \")\\\\b\", \"i\");\n\nvar shells = [\"run\", \"cmd\", \"entrypoint\", \"shell\"];\nvar shellsAsArrayRegex = new RegExp(\"^(\\\\s*)(\" + shells.join('|') + \")(\\\\s+\\\\[)\", \"i\");\n\nvar expose = \"expose\";\nvar exposeRegex = new RegExp(\"^(\\\\s*)(\" + expose + \")(\\\\s+)\", \"i\");\n\nvar others = [\n  \"arg\", \"from\", \"maintainer\", \"label\", \"env\",\n  \"add\", \"copy\", \"volume\", \"user\",\n  \"workdir\", \"onbuild\", \"stopsignal\", \"healthcheck\", \"shell\"\n];\n\n// Collect all Dockerfile directives\nvar instructions = [from, expose].concat(shells).concat(others),\n    instructionRegex = \"(\" + instructions.join('|') + \")\",\n    instructionOnlyLine = new RegExp(\"^(\\\\s*)\" + instructionRegex + \"(\\\\s*)(#.*)?$\", \"i\"),\n    instructionWithArguments = new RegExp(\"^(\\\\s*)\" + instructionRegex + \"(\\\\s+)\", \"i\");\n\nexport const dockerFile = simpleMode({\n  start: [\n    // Block comment: This is a line starting with a comment\n    {\n      regex: /^\\s*#.*$/,\n      sol: true,\n      token: \"comment\"\n    },\n    {\n      regex: fromRegex,\n      token: [null, \"keyword\"],\n      sol: true,\n      next: \"from\"\n    },\n    // Highlight an instruction without any arguments (for convenience)\n    {\n      regex: instructionOnlyLine,\n      token: [null, \"keyword\", null, \"error\"],\n      sol: true\n    },\n    {\n      regex: shellsAsArrayRegex,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"array\"\n    },\n    {\n      regex: exposeRegex,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"expose\"\n    },\n    // Highlight an instruction followed by arguments\n    {\n      regex: instructionWithArguments,\n      token: [null, \"keyword\", null],\n      sol: true,\n      next: \"arguments\"\n    },\n    {\n      regex: /./,\n      token: null\n    }\n  ],\n  from: [\n    {\n      regex: /\\s*$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      // Line comment without instruction arguments is an error\n      regex: /(\\s*)(#.*)$/,\n      token: [null, \"error\"],\n      next: \"start\"\n    },\n    {\n      regex: /(\\s*\\S+\\s+)(as)/i,\n      token: [null, \"keyword\"],\n      next: \"start\"\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  single: [\n    {\n      regex: /(?:[^\\\\']|\\\\.)/,\n      token: \"string\"\n    },\n    {\n      regex: /'/,\n      token: \"string\",\n      pop: true\n    }\n  ],\n  double: [\n    {\n      regex: /(?:[^\\\\\"]|\\\\.)/,\n      token: \"string\"\n    },\n    {\n      regex: /\"/,\n      token: \"string\",\n      pop: true\n    }\n  ],\n  array: [\n    {\n      regex: /\\]/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /\"(?:[^\\\\\"]|\\\\.)*\"?/,\n      token: \"string\"\n    }\n  ],\n  expose: [\n    {\n      regex: /\\d+$/,\n      token: \"number\",\n      next: \"start\"\n    },\n    {\n      regex: /[^\\d]+$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /\\d+/,\n      token: \"number\"\n    },\n    {\n      regex: /[^\\d]+/,\n      token: null\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  arguments: [\n    {\n      regex: /^\\s*#.*$/,\n      sol: true,\n      token: \"comment\"\n    },\n    {\n      regex: /\"(?:[^\\\\\"]|\\\\.)*\"?$/,\n      token: \"string\",\n      next: \"start\"\n    },\n    {\n      regex: /\"/,\n      token: \"string\",\n      push: \"double\"\n    },\n    {\n      regex: /'(?:[^\\\\']|\\\\.)*'?$/,\n      token: \"string\",\n      next: \"start\"\n    },\n    {\n      regex: /'/,\n      token: \"string\",\n      push: \"single\"\n    },\n    {\n      regex: /[^#\"']+[\\\\`]$/,\n      token: null\n    },\n    {\n      regex: /[^#\"']+$/,\n      token: null,\n      next: \"start\"\n    },\n    {\n      regex: /[^#\"']+/,\n      token: null\n    },\n    // Fail safe return to start\n    {\n      token: null,\n      next: \"start\"\n    }\n  ],\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n});\n\n"], "names": [], "mappings": ";;;AAAA;;AAEA,IAAI,OAAO;AACX,IAAI,YAAY,IAAI,OAAO,gBAAgB,OAAO,QAAQ;AAE1D,IAAI,SAAS;IAAC;IAAO;IAAO;IAAc;CAAQ;AAClD,IAAI,qBAAqB,IAAI,OAAO,aAAa,OAAO,IAAI,CAAC,OAAO,cAAc;AAElF,IAAI,SAAS;AACb,IAAI,cAAc,IAAI,OAAO,aAAa,SAAS,WAAW;AAE9D,IAAI,SAAS;IACX;IAAO;IAAQ;IAAc;IAAS;IACtC;IAAO;IAAQ;IAAU;IACzB;IAAW;IAAW;IAAc;IAAe;CACpD;AAED,oCAAoC;AACpC,IAAI,eAAe;IAAC;IAAM;CAAO,CAAC,MAAM,CAAC,QAAQ,MAAM,CAAC,SACpD,mBAAmB,MAAM,aAAa,IAAI,CAAC,OAAO,KAClD,sBAAsB,IAAI,OAAO,YAAY,mBAAmB,iBAAiB,MACjF,2BAA2B,IAAI,OAAO,YAAY,mBAAmB,UAAU;AAE5E,MAAM,aAAa,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAE;IACnC,OAAO;QACL,wDAAwD;QACxD;YACE,OAAO;YACP,KAAK;YACL,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;gBAAC;gBAAM;aAAU;YACxB,KAAK;YACL,MAAM;QACR;QACA,mEAAmE;QACnE;YACE,OAAO;YACP,OAAO;gBAAC;gBAAM;gBAAW;gBAAM;aAAQ;YACvC,KAAK;QACP;QACA;YACE,OAAO;YACP,OAAO;gBAAC;gBAAM;gBAAW;aAAK;YAC9B,KAAK;YACL,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;gBAAC;gBAAM;gBAAW;aAAK;YAC9B,KAAK;YACL,MAAM;QACR;QACA,iDAAiD;QACjD;YACE,OAAO;YACP,OAAO;gBAAC;gBAAM;gBAAW;aAAK;YAC9B,KAAK;YACL,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;QACT;KACD;IACD,MAAM;QACJ;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,yDAAyD;YACzD,OAAO;YACP,OAAO;gBAAC;gBAAM;aAAQ;YACtB,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;gBAAC;gBAAM;aAAU;YACxB,MAAM;QACR;QACA,4BAA4B;QAC5B;YACE,OAAO;YACP,MAAM;QACR;KACD;IACD,QAAQ;QACN;YACE,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,KAAK;QACP;KACD;IACD,QAAQ;QACN;YACE,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,KAAK;QACP;KACD;IACD,OAAO;QACL;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;QACT;KACD;IACD,QAAQ;QACN;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;QACT;QACA,4BAA4B;QAC5B;YACE,OAAO;YACP,MAAM;QACR;KACD;IACD,WAAW;QACT;YACE,OAAO;YACP,KAAK;YACL,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;QACT;QACA,4BAA4B;QAC5B;YACE,OAAO;YACP,MAAM;QACR;KACD;IACD,cAAc;QACZ,eAAe;YAAC,MAAM;QAAG;IAC3B;AACF", "ignoreList": [0], "debugId": null}}]}