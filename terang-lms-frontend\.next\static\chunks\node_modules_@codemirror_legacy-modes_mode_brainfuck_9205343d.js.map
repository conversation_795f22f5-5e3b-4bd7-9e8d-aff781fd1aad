{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/brainfuck.js"], "sourcesContent": ["var reserve = \"><+-.,[]\".split(\"\");\n/*\n  comments can be either:\n  placed behind lines\n\n  +++    this is a comment\n\n  where reserved characters cannot be used\n  or in a loop\n  [\n  this is ok to use [ ] and stuff\n  ]\n  or preceded by #\n*/\nexport const brainfuck = {\n  name: \"brainfuck\",\n  startState: function() {\n    return {\n      commentLine: false,\n      left: 0,\n      right: 0,\n      commentLoop: false\n    }\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null\n    if(stream.sol()){\n      state.commentLine = false;\n    }\n    var ch = stream.next().toString();\n    if(reserve.indexOf(ch) !== -1){\n      if(state.commentLine === true){\n        if(stream.eol()){\n          state.commentLine = false;\n        }\n        return \"comment\";\n      }\n      if(ch === \"]\" || ch === \"[\"){\n        if(ch === \"[\"){\n          state.left++;\n        }\n        else{\n          state.right++;\n        }\n        return \"bracket\";\n      }\n      else if(ch === \"+\" || ch === \"-\"){\n        return \"keyword\";\n      }\n      else if(ch === \"<\" || ch === \">\"){\n        return \"atom\";\n      }\n      else if(ch === \".\" || ch === \",\"){\n        return \"def\";\n      }\n    }\n    else{\n      state.commentLine = true;\n      if(stream.eol()){\n        state.commentLine = false;\n      }\n      return \"comment\";\n    }\n    if(stream.eol()){\n      state.commentLine = false;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,UAAU,WAAW,KAAK,CAAC;AAcxB,MAAM,YAAY;IACvB,MAAM;IACN,YAAY;QACV,OAAO;YACL,aAAa;YACb,MAAM;YACN,OAAO;YACP,aAAa;QACf;IACF;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,IAAG,OAAO,GAAG,IAAG;YACd,MAAM,WAAW,GAAG;QACtB;QACA,IAAI,KAAK,OAAO,IAAI,GAAG,QAAQ;QAC/B,IAAG,QAAQ,OAAO,CAAC,QAAQ,CAAC,GAAE;YAC5B,IAAG,MAAM,WAAW,KAAK,MAAK;gBAC5B,IAAG,OAAO,GAAG,IAAG;oBACd,MAAM,WAAW,GAAG;gBACtB;gBACA,OAAO;YACT;YACA,IAAG,OAAO,OAAO,OAAO,KAAI;gBAC1B,IAAG,OAAO,KAAI;oBACZ,MAAM,IAAI;gBACZ,OACI;oBACF,MAAM,KAAK;gBACb;gBACA,OAAO;YACT,OACK,IAAG,OAAO,OAAO,OAAO,KAAI;gBAC/B,OAAO;YACT,OACK,IAAG,OAAO,OAAO,OAAO,KAAI;gBAC/B,OAAO;YACT,OACK,IAAG,OAAO,OAAO,OAAO,KAAI;gBAC/B,OAAO;YACT;QACF,OACI;YACF,MAAM,WAAW,GAAG;YACpB,IAAG,OAAO,GAAG,IAAG;gBACd,MAAM,WAAW,GAAG;YACtB;YACA,OAAO;QACT;QACA,IAAG,OAAO,GAAG,IAAG;YACd,MAAM,WAAW,GAAG;QACtB;IACF;AACF", "ignoreList": [0], "debugId": null}}]}