{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/stex.js"], "sourcesContent": ["function mkStex(mathMode) {\n  function pushCommand(state, command) {\n    state.cmdState.push(command);\n  }\n\n  function peekCommand(state) {\n    if (state.cmdState.length > 0) {\n      return state.cmdState[state.cmdState.length - 1];\n    } else {\n      return null;\n    }\n  }\n\n  function popCommand(state) {\n    var plug = state.cmdState.pop();\n    if (plug) {\n      plug.closeBracket();\n    }\n  }\n\n  // returns the non-default plugin closest to the end of the list\n  function getMostPowerful(state) {\n    var context = state.cmdState;\n    for (var i = context.length - 1; i >= 0; i--) {\n      var plug = context[i];\n      if (plug.name == \"DEFAULT\") {\n        continue;\n      }\n      return plug;\n    }\n    return { styleIdentifier: function() { return null; } };\n  }\n\n  function addPluginPattern(pluginName, cmdStyle, styles) {\n    return function () {\n      this.name = pluginName;\n      this.bracketNo = 0;\n      this.style = cmdStyle;\n      this.styles = styles;\n      this.argument = null;   // \\begin and \\end have arguments that follow. These are stored in the plugin\n\n      this.styleIdentifier = function() {\n        return this.styles[this.bracketNo - 1] || null;\n      };\n      this.openBracket = function() {\n        this.bracketNo++;\n        return \"bracket\";\n      };\n      this.closeBracket = function() {};\n    };\n  }\n\n  var plugins = {};\n\n  plugins[\"importmodule\"] = addPluginPattern(\"importmodule\", \"tag\", [\"string\", \"builtin\"]);\n  plugins[\"documentclass\"] = addPluginPattern(\"documentclass\", \"tag\", [\"\", \"atom\"]);\n  plugins[\"usepackage\"] = addPluginPattern(\"usepackage\", \"tag\", [\"atom\"]);\n  plugins[\"begin\"] = addPluginPattern(\"begin\", \"tag\", [\"atom\"]);\n  plugins[\"end\"] = addPluginPattern(\"end\", \"tag\", [\"atom\"]);\n\n  plugins[\"label\"    ] = addPluginPattern(\"label\"    , \"tag\", [\"atom\"]);\n  plugins[\"ref\"      ] = addPluginPattern(\"ref\"      , \"tag\", [\"atom\"]);\n  plugins[\"eqref\"    ] = addPluginPattern(\"eqref\"    , \"tag\", [\"atom\"]);\n  plugins[\"cite\"     ] = addPluginPattern(\"cite\"     , \"tag\", [\"atom\"]);\n  plugins[\"bibitem\"  ] = addPluginPattern(\"bibitem\"  , \"tag\", [\"atom\"]);\n  plugins[\"Bibitem\"  ] = addPluginPattern(\"Bibitem\"  , \"tag\", [\"atom\"]);\n  plugins[\"RBibitem\" ] = addPluginPattern(\"RBibitem\" , \"tag\", [\"atom\"]);\n\n  plugins[\"DEFAULT\"] = function () {\n    this.name = \"DEFAULT\";\n    this.style = \"tag\";\n\n    this.styleIdentifier = this.openBracket = this.closeBracket = function() {};\n  };\n\n  function setState(state, f) {\n    state.f = f;\n  }\n\n  // called when in a normal (no environment) context\n  function normal(source, state) {\n    var plug;\n    // Do we look like '\\command' ?  If so, attempt to apply the plugin 'command'\n    if (source.match(/^\\\\[a-zA-Z@\\xc0-\\u1fff\\u2060-\\uffff]+/)) {\n      var cmdName = source.current().slice(1);\n      plug = plugins.hasOwnProperty(cmdName) ? plugins[cmdName] : plugins[\"DEFAULT\"];\n      plug = new plug();\n      pushCommand(state, plug);\n      setState(state, beginParams);\n      return plug.style;\n    }\n\n    // escape characters\n    if (source.match(/^\\\\[$&%#{}_]/)) {\n      return \"tag\";\n    }\n\n    // white space control characters\n    if (source.match(/^\\\\[,;!\\/\\\\]/)) {\n      return \"tag\";\n    }\n\n    // find if we're starting various math modes\n    if (source.match(\"\\\\[\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"\\\\]\"); });\n      return \"keyword\";\n    }\n    if (source.match(\"\\\\(\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"\\\\)\"); });\n      return \"keyword\";\n    }\n    if (source.match(\"$$\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"$$\"); });\n      return \"keyword\";\n    }\n    if (source.match(\"$\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"$\"); });\n      return \"keyword\";\n    }\n\n    var ch = source.next();\n    if (ch == \"%\") {\n      source.skipToEnd();\n      return \"comment\";\n    } else if (ch == '}' || ch == ']') {\n      plug = peekCommand(state);\n      if (plug) {\n        plug.closeBracket(ch);\n        setState(state, beginParams);\n      } else {\n        return \"error\";\n      }\n      return \"bracket\";\n    } else if (ch == '{' || ch == '[') {\n      plug = plugins[\"DEFAULT\"];\n      plug = new plug();\n      pushCommand(state, plug);\n      return \"bracket\";\n    } else if (/\\d/.test(ch)) {\n      source.eatWhile(/[\\w.%]/);\n      return \"atom\";\n    } else {\n      source.eatWhile(/[\\w\\-_]/);\n      plug = getMostPowerful(state);\n      if (plug.name == 'begin') {\n        plug.argument = source.current();\n      }\n      return plug.styleIdentifier();\n    }\n  }\n\n  function inMathMode(source, state, endModeSeq) {\n    if (source.eatSpace()) {\n      return null;\n    }\n    if (endModeSeq && source.match(endModeSeq)) {\n      setState(state, normal);\n      return \"keyword\";\n    }\n    if (source.match(/^\\\\[a-zA-Z@]+/)) {\n      return \"tag\";\n    }\n    if (source.match(/^[a-zA-Z]+/)) {\n      return \"variableName.special\";\n    }\n    // escape characters\n    if (source.match(/^\\\\[$&%#{}_]/)) {\n      return \"tag\";\n    }\n    // white space control characters\n    if (source.match(/^\\\\[,;!\\/]/)) {\n      return \"tag\";\n    }\n    // special math-mode characters\n    if (source.match(/^[\\^_&]/)) {\n      return \"tag\";\n    }\n    // non-special characters\n    if (source.match(/^[+\\-<>|=,\\/@!*:;'\"`~#?]/)) {\n      return null;\n    }\n    if (source.match(/^(\\d+\\.\\d*|\\d*\\.\\d+|\\d+)/)) {\n      return \"number\";\n    }\n    var ch = source.next();\n    if (ch == \"{\" || ch == \"}\" || ch == \"[\" || ch == \"]\" || ch == \"(\" || ch == \")\") {\n      return \"bracket\";\n    }\n\n    if (ch == \"%\") {\n      source.skipToEnd();\n      return \"comment\";\n    }\n    return \"error\";\n  }\n\n  function beginParams(source, state) {\n    var ch = source.peek(), lastPlug;\n    if (ch == '{' || ch == '[') {\n      lastPlug = peekCommand(state);\n      lastPlug.openBracket(ch);\n      source.eat(ch);\n      setState(state, normal);\n      return \"bracket\";\n    }\n    if (/[ \\t\\r]/.test(ch)) {\n      source.eat(ch);\n      return null;\n    }\n    setState(state, normal);\n    popCommand(state);\n\n    return normal(source, state);\n  }\n\n  return {\n    name: \"stex\",\n    startState: function() {\n      var f = mathMode ? function(source, state){ return inMathMode(source, state); } : normal;\n      return {\n        cmdState: [],\n        f: f\n      };\n    },\n    copyState: function(s) {\n      return {\n        cmdState: s.cmdState.slice(),\n        f: s.f\n      };\n    },\n    token: function(stream, state) {\n      return state.f(stream, state);\n    },\n    blankLine: function(state) {\n      state.f = normal;\n      state.cmdState.length = 0;\n    },\n    languageData: {\n      commentTokens: {line: \"%\"}\n    }\n  };\n};\n\nexport const stex = mkStex(false)\nexport const stexMath = mkStex(true)\n"], "names": [], "mappings": ";;;;AAAA,SAAS,OAAO,QAAQ;IACtB,SAAS,YAAY,KAAK,EAAE,OAAO;QACjC,MAAM,QAAQ,CAAC,IAAI,CAAC;IACtB;IAEA,SAAS,YAAY,KAAK;QACxB,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC7B,OAAO,MAAM,QAAQ,CAAC,MAAM,QAAQ,CAAC,MAAM,GAAG,EAAE;QAClD,OAAO;YACL,OAAO;QACT;IACF;IAEA,SAAS,WAAW,KAAK;QACvB,IAAI,OAAO,MAAM,QAAQ,CAAC,GAAG;QAC7B,IAAI,MAAM;YACR,KAAK,YAAY;QACnB;IACF;IAEA,gEAAgE;IAChE,SAAS,gBAAgB,KAAK;QAC5B,IAAI,UAAU,MAAM,QAAQ;QAC5B,IAAK,IAAI,IAAI,QAAQ,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC5C,IAAI,OAAO,OAAO,CAAC,EAAE;YACrB,IAAI,KAAK,IAAI,IAAI,WAAW;gBAC1B;YACF;YACA,OAAO;QACT;QACA,OAAO;YAAE,iBAAiB;gBAAa,OAAO;YAAM;QAAE;IACxD;IAEA,SAAS,iBAAiB,UAAU,EAAE,QAAQ,EAAE,MAAM;QACpD,OAAO;YACL,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,QAAQ,GAAG,MAAQ,6EAA6E;YAErG,IAAI,CAAC,eAAe,GAAG;gBACrB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,IAAI;YAC5C;YACA,IAAI,CAAC,WAAW,GAAG;gBACjB,IAAI,CAAC,SAAS;gBACd,OAAO;YACT;YACA,IAAI,CAAC,YAAY,GAAG,YAAY;QAClC;IACF;IAEA,IAAI,UAAU,CAAC;IAEf,OAAO,CAAC,eAAe,GAAG,iBAAiB,gBAAgB,OAAO;QAAC;QAAU;KAAU;IACvF,OAAO,CAAC,gBAAgB,GAAG,iBAAiB,iBAAiB,OAAO;QAAC;QAAI;KAAO;IAChF,OAAO,CAAC,aAAa,GAAG,iBAAiB,cAAc,OAAO;QAAC;KAAO;IACtE,OAAO,CAAC,QAAQ,GAAG,iBAAiB,SAAS,OAAO;QAAC;KAAO;IAC5D,OAAO,CAAC,MAAM,GAAG,iBAAiB,OAAO,OAAO;QAAC;KAAO;IAExD,OAAO,CAAC,QAAY,GAAG,iBAAiB,SAAa,OAAO;QAAC;KAAO;IACpE,OAAO,CAAC,MAAY,GAAG,iBAAiB,OAAa,OAAO;QAAC;KAAO;IACpE,OAAO,CAAC,QAAY,GAAG,iBAAiB,SAAa,OAAO;QAAC;KAAO;IACpE,OAAO,CAAC,OAAY,GAAG,iBAAiB,QAAa,OAAO;QAAC;KAAO;IACpE,OAAO,CAAC,UAAY,GAAG,iBAAiB,WAAa,OAAO;QAAC;KAAO;IACpE,OAAO,CAAC,UAAY,GAAG,iBAAiB,WAAa,OAAO;QAAC;KAAO;IACpE,OAAO,CAAC,WAAY,GAAG,iBAAiB,YAAa,OAAO;QAAC;KAAO;IAEpE,OAAO,CAAC,UAAU,GAAG;QACnB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QAEb,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,GAAG,YAAY;IAC5E;IAEA,SAAS,SAAS,KAAK,EAAE,CAAC;QACxB,MAAM,CAAC,GAAG;IACZ;IAEA,mDAAmD;IACnD,SAAS,OAAO,MAAM,EAAE,KAAK;QAC3B,IAAI;QACJ,6EAA6E;QAC7E,IAAI,OAAO,KAAK,CAAC,0CAA0C;YACzD,IAAI,UAAU,OAAO,OAAO,GAAG,KAAK,CAAC;YACrC,OAAO,QAAQ,cAAc,CAAC,WAAW,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU;YAC9E,OAAO,IAAI;YACX,YAAY,OAAO;YACnB,SAAS,OAAO;YAChB,OAAO,KAAK,KAAK;QACnB;QAEA,oBAAoB;QACpB,IAAI,OAAO,KAAK,CAAC,iBAAiB;YAChC,OAAO;QACT;QAEA,iCAAiC;QACjC,IAAI,OAAO,KAAK,CAAC,iBAAiB;YAChC,OAAO;QACT;QAEA,4CAA4C;QAC5C,IAAI,OAAO,KAAK,CAAC,QAAQ;YACvB,SAAS,OAAO,SAAS,MAAM,EAAE,KAAK;gBAAG,OAAO,WAAW,QAAQ,OAAO;YAAQ;YAClF,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,QAAQ;YACvB,SAAS,OAAO,SAAS,MAAM,EAAE,KAAK;gBAAG,OAAO,WAAW,QAAQ,OAAO;YAAQ;YAClF,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,OAAO;YACtB,SAAS,OAAO,SAAS,MAAM,EAAE,KAAK;gBAAG,OAAO,WAAW,QAAQ,OAAO;YAAO;YACjF,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,MAAM;YACrB,SAAS,OAAO,SAAS,MAAM,EAAE,KAAK;gBAAG,OAAO,WAAW,QAAQ,OAAO;YAAM;YAChF,OAAO;QACT;QAEA,IAAI,KAAK,OAAO,IAAI;QACpB,IAAI,MAAM,KAAK;YACb,OAAO,SAAS;YAChB,OAAO;QACT,OAAO,IAAI,MAAM,OAAO,MAAM,KAAK;YACjC,OAAO,YAAY;YACnB,IAAI,MAAM;gBACR,KAAK,YAAY,CAAC;gBAClB,SAAS,OAAO;YAClB,OAAO;gBACL,OAAO;YACT;YACA,OAAO;QACT,OAAO,IAAI,MAAM,OAAO,MAAM,KAAK;YACjC,OAAO,OAAO,CAAC,UAAU;YACzB,OAAO,IAAI;YACX,YAAY,OAAO;YACnB,OAAO;QACT,OAAO,IAAI,KAAK,IAAI,CAAC,KAAK;YACxB,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT,OAAO;YACL,OAAO,QAAQ,CAAC;YAChB,OAAO,gBAAgB;YACvB,IAAI,KAAK,IAAI,IAAI,SAAS;gBACxB,KAAK,QAAQ,GAAG,OAAO,OAAO;YAChC;YACA,OAAO,KAAK,eAAe;QAC7B;IACF;IAEA,SAAS,WAAW,MAAM,EAAE,KAAK,EAAE,UAAU;QAC3C,IAAI,OAAO,QAAQ,IAAI;YACrB,OAAO;QACT;QACA,IAAI,cAAc,OAAO,KAAK,CAAC,aAAa;YAC1C,SAAS,OAAO;YAChB,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,kBAAkB;YACjC,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,eAAe;YAC9B,OAAO;QACT;QACA,oBAAoB;QACpB,IAAI,OAAO,KAAK,CAAC,iBAAiB;YAChC,OAAO;QACT;QACA,iCAAiC;QACjC,IAAI,OAAO,KAAK,CAAC,eAAe;YAC9B,OAAO;QACT;QACA,+BAA+B;QAC/B,IAAI,OAAO,KAAK,CAAC,YAAY;YAC3B,OAAO;QACT;QACA,yBAAyB;QACzB,IAAI,OAAO,KAAK,CAAC,6BAA6B;YAC5C,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,6BAA6B;YAC5C,OAAO;QACT;QACA,IAAI,KAAK,OAAO,IAAI;QACpB,IAAI,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;YAC9E,OAAO;QACT;QAEA,IAAI,MAAM,KAAK;YACb,OAAO,SAAS;YAChB,OAAO;QACT;QACA,OAAO;IACT;IAEA,SAAS,YAAY,MAAM,EAAE,KAAK;QAChC,IAAI,KAAK,OAAO,IAAI,IAAI;QACxB,IAAI,MAAM,OAAO,MAAM,KAAK;YAC1B,WAAW,YAAY;YACvB,SAAS,WAAW,CAAC;YACrB,OAAO,GAAG,CAAC;YACX,SAAS,OAAO;YAChB,OAAO;QACT;QACA,IAAI,UAAU,IAAI,CAAC,KAAK;YACtB,OAAO,GAAG,CAAC;YACX,OAAO;QACT;QACA,SAAS,OAAO;QAChB,WAAW;QAEX,OAAO,OAAO,QAAQ;IACxB;IAEA,OAAO;QACL,MAAM;QACN,YAAY;YACV,IAAI,IAAI,WAAW,SAAS,MAAM,EAAE,KAAK;gBAAG,OAAO,WAAW,QAAQ;YAAQ,IAAI;YAClF,OAAO;gBACL,UAAU,EAAE;gBACZ,GAAG;YACL;QACF;QACA,WAAW,SAAS,CAAC;YACnB,OAAO;gBACL,UAAU,EAAE,QAAQ,CAAC,KAAK;gBAC1B,GAAG,EAAE,CAAC;YACR;QACF;QACA,OAAO,SAAS,MAAM,EAAE,KAAK;YAC3B,OAAO,MAAM,CAAC,CAAC,QAAQ;QACzB;QACA,WAAW,SAAS,KAAK;YACvB,MAAM,CAAC,GAAG;YACV,MAAM,QAAQ,CAAC,MAAM,GAAG;QAC1B;QACA,cAAc;YACZ,eAAe;gBAAC,MAAM;YAAG;QAC3B;IACF;AACF;;AAEO,MAAM,OAAO,OAAO;AACpB,MAAM,WAAW,OAAO", "ignoreList": [0], "debugId": null}}]}