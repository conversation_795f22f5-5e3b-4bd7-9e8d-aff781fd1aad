{"version": 3, "file": "../app/api/auth/signup/route.js", "mappings": "2dAEA,GAAI,CAACA,QAAQC,GAAG,CAACC,YAAY,CAC3B,CAD6B,KACvB,MAAU,iDAGlB,IAAMC,EAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAACJ,QAAQC,GAAG,CAACC,YAAY,EAElC,eAAeG,EAAMC,CAA0B,CAAE,GAAGC,CAAa,EAEtE,OADe,MAAMJ,EAAIG,KAASC,EAEpC,yBCXA,6GCAA,oDCAA,qaCKO,eAAeC,EAAKC,CAAoB,EAC7C,GAAI,CAEF,GAAM,MAAEC,CAAI,OAAEC,CAAK,UAAEC,CAAQ,MAAEC,CAAI,eAAEC,CAAa,CAAE,CADzB,EAC4BC,IAAAA,EADdC,IAAI,CAAZP,EAGjC,GAAI,CAACC,GAAQ,CAARA,GAAkB,CAACE,GAAY,CAACC,EACnC,EADsBD,EAAmB,GAClCK,EAAAA,YAAAA,CAAaD,IAAI,CACtB,CACEE,OAAAA,CAAS,GACTC,KAAAA,CAAO,+CACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,GASlB,GAFqBC,CADnB,MAAMhB,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC,mCAAmC,EAAEM,EAAMW,GAANX,QAAiB,IAAI,CACjC,CAAC,EAAE,CAGxC,OAAOM,EAAAA,YAAAA,CAAaD,IAAI,CACtB,CACEE,OAAAA,EAAS,EACTC,KAAAA,CAAO,sCACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,GAMlB,IAAMG,EAAiB,MAAMC,EAAAA,EAAAA,CAAAA,CAAvBD,GAAkC,CAACX,EADtB,IAKba,EAJmCb,EAAUc,IAIvBrB,CAAAA,EAAAA,EAAtBoB,CAJ6CC,CAAAA,CAIjB;;cAExB,EAAEhB,EAAK,EAALA,EAASC,EAAMW,GAANX,QAAiB,GAAG,EAAE,EAAEY,EAAe,EAAE,EAAEV,EAAK,EAALA,EAH3B,EAGQU,UAHlB,GAAgC,CAA/BV,EAG6Cc,CAHvBd,UAAsB,KAAQC,GAAiB,KAGL,KAHZA;;IAKhF,CAAC,CAGD,GAAI,CAFYW,CAEJ,CAAC,EAAE,CACb,CADe,MACRR,CAHOQ,CAGPR,YAAAA,CAAaD,IAAI,CACtB,CAAEE,OAAAA,CAAS,GAAOC,KAAAA,CAAO,wBAAwB,CACjD,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAMQ,EAAqB,CACzBC,EAAAA,CAAIC,CAAO,CAAC,EAAE,CAACD,EAAE,CACjBnB,IAAAA,CAAMoB,CAAO,CAAC,EAAE,CAACpB,IAAI,CACrBC,KAAAA,CAAOmB,CAAO,CAAC,EAAE,CAACnB,KAAK,CACvBE,IAAAA,CAAMiB,CAAO,CAAC,EAAE,CAACjB,IAAI,CACrBC,aAAAA,CAAegB,CAAO,CAAC,EAAE,CAAChB,aAAa,EAAIiB,MAC7C,EAEA,OAAOd,EAAAA,YAAAA,CAAaD,IAAI,CAAC,CACvBE,OAAAA,EAAS,EACTc,IAAAA,CAAM,CAAEC,IAAAA,CAAML,CAAS,EACvBM,OAAAA,CAAS,2BACX,EACF,CAAE,MAAOf,EAAO,CAEd,EAFOA,KACPgB,OAAAA,CAAQhB,KAAK,CAAC,iBAAkBA,GACzBF,EADyBE,CAAAA,WACzBF,CAAaD,IAAI,CACtB,CAAEE,OAAAA,CAAS,GAAOC,KAAAA,CAAO,wBAAwB,CACjD,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CCnEA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OAER,EAFiB,OAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAItB,UAA6B,EAAE,OAAxB,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CACrC,IAD0C,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,kBAAkB,SACtC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,MAAegB,EAA4B,EAA7B,GAAkC,EAEnD,EAAO,EAAYC,EAA6B,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAAR,EAEnC,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,8BACA,4BACA,iBACA,sCACA,CAAK,CACL,iJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,0BC5BA,mECAA,oDCAA,iDCAA,iDCAA,iDCAA,wGCAA,gECAA,kDCAA,iECAA,sDCAA,wDCAA,qDCAA,4CCAA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,oCCRA,sGCAA,qDCAA,sECAA,oDCAA,kECAA,yDCAA,uDCAA,6GCAA,qDCAA,4DCAA,wDCAA,iECAA,uDCAA,mEEAA,iDCAA,2DCAA,2DCAA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://terang-lms-ui/./src/lib/db/raw.ts", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/src/app/api/auth/signup/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?a679", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\"", "webpack://terang-lms-ui/?4688"], "sourcesContent": ["import { neon } from '@neondatabase/serverless';\r\n\r\nif (!process.env.DATABASE_URL) {\r\n  throw new Error('DATABASE_URL environment variable is required');\r\n}\r\n\r\nconst sql = neon(process.env.DATABASE_URL);\r\n\r\nexport async function query(text: TemplateStringsArray, ...params: any[]) {\r\n  const result = await sql(text, ...params);\r\n  return result;\r\n}\r\n", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport bcrypt from 'bcryptjs';\r\nimport { query } from '@/lib/db/raw';\r\nimport { RegisterData, ApiResponse, AuthUser } from '@/types/database';\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body: RegisterData = await request.json();\r\n    const { name, email, password, role, institutionId } = body;\r\n\r\n    if (!name || !email || !password || !role) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Name, email, password, and role are required'\r\n        } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Check if user already exists\r\n    const existingUserResult =\r\n      await query`SELECT id FROM users WHERE email = ${email.toLowerCase()}`;\r\n    const existingUser = existingUserResult[0];\r\n\r\n    if (existingUser) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'User with this email already exists'\r\n        } as ApiResponse,\r\n        { status: 409 }\r\n      );\r\n    }\r\n\r\n    // Hash password\r\n    const saltRounds = 12;\r\n    const hashedPassword = await bcrypt.hash(password, saltRounds);\r\n\r\n    // Create new user - institutionId is always null for Teacher and Student roles\r\n    const finalInstitutionId = (role === 'teacher' || role === 'student') ? null : (institutionId || null);\r\n    const newUserResult = await query`\r\n      INSERT INTO users (name, email, password, role, institution_id)\r\n      VALUES (${name}, ${email.toLowerCase()}, ${hashedPassword}, ${role}, ${finalInstitutionId})\r\n      RETURNING id, name, email, role, institution_id\r\n    `;\r\n    const newUser = newUserResult;\r\n\r\n    if (!newUser[0]) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Failed to create user' } as ApiResponse,\r\n        { status: 500 }\r\n      );\r\n    }\r\n\r\n    // Create auth user object (without password)\r\n    const authUser: AuthUser = {\r\n      id: newUser[0].id,\r\n      name: newUser[0].name,\r\n      email: newUser[0].email,\r\n      role: newUser[0].role,\r\n      institutionId: newUser[0].institutionId || undefined\r\n    };\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: { user: authUser },\r\n      message: 'User created successfully'\r\n    } as ApiResponse);\r\n  } catch (error) {\r\n    console.error('Sign up error:', error);\r\n    return NextResponse.json(\r\n      { success: false, error: 'Internal server error' } as ApiResponse,\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/auth/signup',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\auth\\\\signup\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/auth/signup/route\",\n        pathname: \"/api/auth/signup\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/signup/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\auth\\\\signup\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 44725;\nmodule.exports = webpackEmptyContext;", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", null, "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["process", "env", "DATABASE_URL", "sql", "neon", "query", "text", "params", "POST", "request", "name", "email", "password", "role", "institutionId", "body", "json", "NextResponse", "success", "error", "status", "existingUserResult", "toLowerCase", "hashedPassword", "bcrypt", "newUserResult", "saltRounds", "finalInstitutionId", "authUser", "id", "newUser", "undefined", "data", "user", "message", "console", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}