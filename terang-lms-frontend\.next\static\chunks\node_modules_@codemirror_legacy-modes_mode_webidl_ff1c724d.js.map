{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/webidl.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n};\n\nvar builtinArray = [\n  \"Clamp\",\n  \"Constructor\",\n  \"EnforceRange\",\n  \"Exposed\",\n  \"ImplicitThis\",\n  \"Global\", \"PrimaryGlobal\",\n  \"LegacyArrayClass\",\n  \"LegacyUnenumerableNamedProperties\",\n  \"LenientThis\",\n  \"NamedConstructor\",\n  \"NewObject\",\n  \"NoInterfaceObject\",\n  \"OverrideBuiltins\",\n  \"PutForwards\",\n  \"Replaceable\",\n  \"SameObject\",\n  \"TreatNonObjectAsNull\",\n  \"TreatNullAs\",\n    \"EmptyString\",\n  \"Unforgeable\",\n  \"Unscopeable\"\n];\nvar builtins = wordRegexp(builtinArray);\n\nvar typeArray = [\n  \"unsigned\", \"short\", \"long\",                  // UnsignedIntegerType\n  \"unrestricted\", \"float\", \"double\",            // UnrestrictedFloatType\n  \"boolean\", \"byte\", \"octet\",                   // Rest of PrimitiveType\n  \"Promise\",                                    // PromiseType\n  \"ArrayBuffer\", \"DataView\", \"Int8Array\", \"Int16Array\", \"Int32Array\",\n  \"Uint8Array\", \"Uint16Array\", \"Uint32Array\", \"Uint8ClampedArray\",\n  \"Float32Array\", \"Float64Array\",               // BufferRelatedType\n  \"ByteString\", \"DOMString\", \"USVString\", \"sequence\", \"object\", \"RegExp\",\n  \"Error\", \"DOMException\", \"FrozenArray\",       // Rest of NonAnyType\n  \"any\",                                        // Rest of SingleType\n  \"void\"                                        // Rest of ReturnType\n];\nvar types = wordRegexp(typeArray);\n\nvar keywordArray = [\n  \"attribute\", \"callback\", \"const\", \"deleter\", \"dictionary\", \"enum\", \"getter\",\n  \"implements\", \"inherit\", \"interface\", \"iterable\", \"legacycaller\", \"maplike\",\n  \"partial\", \"required\", \"serializer\", \"setlike\", \"setter\", \"static\",\n  \"stringifier\", \"typedef\",                     // ArgumentNameKeyword except\n                                                // \"unrestricted\"\n  \"optional\", \"readonly\", \"or\"\n];\nvar keywords = wordRegexp(keywordArray);\n\nvar atomArray = [\n  \"true\", \"false\",                              // BooleanLiteral\n  \"Infinity\", \"NaN\",                            // FloatLiteral\n  \"null\"                                        // Rest of ConstValue\n];\nvar atoms = wordRegexp(atomArray);\n\nvar startDefArray = [\"callback\", \"dictionary\", \"enum\", \"interface\"];\nvar startDefs = wordRegexp(startDefArray);\n\nvar endDefArray = [\"typedef\"];\nvar endDefs = wordRegexp(endDefArray);\n\nvar singleOperators = /^[:<=>?]/;\nvar integers = /^-?([1-9][0-9]*|0[Xx][0-9A-Fa-f]+|0[0-7]*)/;\nvar floats = /^-?(([0-9]+\\.[0-9]*|[0-9]*\\.[0-9]+)([Ee][+-]?[0-9]+)?|[0-9]+[Ee][+-]?[0-9]+)/;\nvar identifiers = /^_?[A-Za-z][0-9A-Z_a-z-]*/;\nvar identifiersEnd = /^_?[A-Za-z][0-9A-Z_a-z-]*(?=\\s*;)/;\nvar strings = /^\"[^\"]*\"/;\nvar multilineComments = /^\\/\\*.*?\\*\\//;\nvar multilineCommentsStart = /^\\/\\*.*/;\nvar multilineCommentsEnd = /^.*?\\*\\//;\n\nfunction readToken(stream, state) {\n  // whitespace\n  if (stream.eatSpace()) return null;\n\n  // comment\n  if (state.inComment) {\n    if (stream.match(multilineCommentsEnd)) {\n      state.inComment = false;\n      return \"comment\";\n    }\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (stream.match(\"//\")) {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (stream.match(multilineComments)) return \"comment\";\n  if (stream.match(multilineCommentsStart)) {\n    state.inComment = true;\n    return \"comment\";\n  }\n\n  // integer and float\n  if (stream.match(/^-?[0-9\\.]/, false)) {\n    if (stream.match(integers) || stream.match(floats)) return \"number\";\n  }\n\n  // string\n  if (stream.match(strings)) return \"string\";\n\n  // identifier\n  if (state.startDef && stream.match(identifiers)) return \"def\";\n\n  if (state.endDef && stream.match(identifiersEnd)) {\n    state.endDef = false;\n    return \"def\";\n  }\n\n  if (stream.match(keywords)) return \"keyword\";\n\n  if (stream.match(types)) {\n    var lastToken = state.lastToken;\n    var nextToken = (stream.match(/^\\s*(.+?)\\b/, false) || [])[1];\n\n    if (lastToken === \":\" || lastToken === \"implements\" ||\n        nextToken === \"implements\" || nextToken === \"=\") {\n      // Used as identifier\n      return \"builtin\";\n    } else {\n      // Used as type\n      return \"type\";\n    }\n  }\n\n  if (stream.match(builtins)) return \"builtin\";\n  if (stream.match(atoms)) return \"atom\";\n  if (stream.match(identifiers)) return \"variable\";\n\n  // other\n  if (stream.match(singleOperators)) return \"operator\";\n\n  // unrecognized\n  stream.next();\n  return null;\n};\n\nexport const webIDL = {\n  name: \"webidl\",\n  startState: function() {\n    return {\n      // Is in multiline comment\n      inComment: false,\n      // Last non-whitespace, matched token\n      lastToken: \"\",\n      // Next token is a definition\n      startDef: false,\n      // Last token of the statement is a definition\n      endDef: false\n    };\n  },\n  token: function(stream, state) {\n    var style = readToken(stream, state);\n\n    if (style) {\n      var cur = stream.current();\n      state.lastToken = cur;\n      if (style === \"keyword\") {\n        state.startDef = startDefs.test(cur);\n        state.endDef = state.endDef || endDefs.test(cur);\n      } else {\n        state.startDef = false;\n      }\n    }\n\n    return style;\n  },\n\n  languageData: {\n    autocomplete: builtinArray.concat(typeArray).concat(keywordArray).concat(atomArray)\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,WAAW,KAAK;IACvB,OAAO,IAAI,OAAO,QAAQ,MAAM,IAAI,CAAC,SAAS;AAChD;;AAEA,IAAI,eAAe;IACjB;IACA;IACA;IACA;IACA;IACA;IAAU;IACV;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACE;IACF;IACA;CACD;AACD,IAAI,WAAW,WAAW;AAE1B,IAAI,YAAY;IACd;IAAY;IAAS;IACrB;IAAgB;IAAS;IACzB;IAAW;IAAQ;IACnB;IACA;IAAe;IAAY;IAAa;IAAc;IACtD;IAAc;IAAe;IAAe;IAC5C;IAAgB;IAChB;IAAc;IAAa;IAAa;IAAY;IAAU;IAC9D;IAAS;IAAgB;IACzB;IACA,OAA8C,qBAAqB;CACpE;AACD,IAAI,QAAQ,WAAW;AAEvB,IAAI,eAAe;IACjB;IAAa;IAAY;IAAS;IAAW;IAAc;IAAQ;IACnE;IAAc;IAAW;IAAa;IAAY;IAAgB;IAClE;IAAW;IAAY;IAAc;IAAW;IAAU;IAC1D;IAAe;IAC+B,iBAAiB;IAC/D;IAAY;IAAY;CACzB;AACD,IAAI,WAAW,WAAW;AAE1B,IAAI,YAAY;IACd;IAAQ;IACR;IAAY;IACZ,OAA8C,qBAAqB;CACpE;AACD,IAAI,QAAQ,WAAW;AAEvB,IAAI,gBAAgB;IAAC;IAAY;IAAc;IAAQ;CAAY;AACnE,IAAI,YAAY,WAAW;AAE3B,IAAI,cAAc;IAAC;CAAU;AAC7B,IAAI,UAAU,WAAW;AAEzB,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,IAAI,UAAU;AACd,IAAI,oBAAoB;AACxB,IAAI,yBAAyB;AAC7B,IAAI,uBAAuB;AAE3B,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,aAAa;IACb,IAAI,OAAO,QAAQ,IAAI,OAAO;IAE9B,UAAU;IACV,IAAI,MAAM,SAAS,EAAE;QACnB,IAAI,OAAO,KAAK,CAAC,uBAAuB;YACtC,MAAM,SAAS,GAAG;YAClB,OAAO;QACT;QACA,OAAO,SAAS;QAChB,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,OAAO;QACtB,OAAO,SAAS;QAChB,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,oBAAoB,OAAO;IAC5C,IAAI,OAAO,KAAK,CAAC,yBAAyB;QACxC,MAAM,SAAS,GAAG;QAClB,OAAO;IACT;IAEA,oBAAoB;IACpB,IAAI,OAAO,KAAK,CAAC,cAAc,QAAQ;QACrC,IAAI,OAAO,KAAK,CAAC,aAAa,OAAO,KAAK,CAAC,SAAS,OAAO;IAC7D;IAEA,SAAS;IACT,IAAI,OAAO,KAAK,CAAC,UAAU,OAAO;IAElC,aAAa;IACb,IAAI,MAAM,QAAQ,IAAI,OAAO,KAAK,CAAC,cAAc,OAAO;IAExD,IAAI,MAAM,MAAM,IAAI,OAAO,KAAK,CAAC,iBAAiB;QAChD,MAAM,MAAM,GAAG;QACf,OAAO;IACT;IAEA,IAAI,OAAO,KAAK,CAAC,WAAW,OAAO;IAEnC,IAAI,OAAO,KAAK,CAAC,QAAQ;QACvB,IAAI,YAAY,MAAM,SAAS;QAC/B,IAAI,YAAY,CAAC,OAAO,KAAK,CAAC,eAAe,UAAU,EAAE,CAAC,CAAC,EAAE;QAE7D,IAAI,cAAc,OAAO,cAAc,gBACnC,cAAc,gBAAgB,cAAc,KAAK;YACnD,qBAAqB;YACrB,OAAO;QACT,OAAO;YACL,eAAe;YACf,OAAO;QACT;IACF;IAEA,IAAI,OAAO,KAAK,CAAC,WAAW,OAAO;IACnC,IAAI,OAAO,KAAK,CAAC,QAAQ,OAAO;IAChC,IAAI,OAAO,KAAK,CAAC,cAAc,OAAO;IAEtC,QAAQ;IACR,IAAI,OAAO,KAAK,CAAC,kBAAkB,OAAO;IAE1C,eAAe;IACf,OAAO,IAAI;IACX,OAAO;AACT;;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,YAAY;QACV,OAAO;YACL,0BAA0B;YAC1B,WAAW;YACX,qCAAqC;YACrC,WAAW;YACX,6BAA6B;YAC7B,UAAU;YACV,8CAA8C;YAC9C,QAAQ;QACV;IACF;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,QAAQ,UAAU,QAAQ;QAE9B,IAAI,OAAO;YACT,IAAI,MAAM,OAAO,OAAO;YACxB,MAAM,SAAS,GAAG;YAClB,IAAI,UAAU,WAAW;gBACvB,MAAM,QAAQ,GAAG,UAAU,IAAI,CAAC;gBAChC,MAAM,MAAM,GAAG,MAAM,MAAM,IAAI,QAAQ,IAAI,CAAC;YAC9C,OAAO;gBACL,MAAM,QAAQ,GAAG;YACnB;QACF;QAEA,OAAO;IACT;IAEA,cAAc;QACZ,cAAc,aAAa,MAAM,CAAC,WAAW,MAAM,CAAC,cAAc,MAAM,CAAC;IAC3E;AACF", "ignoreList": [0], "debugId": null}}]}