(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@codemirror/legacy-modes/mode/brainfuck.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "brainfuck": (()=>brainfuck)
});
var reserve = "><+-.,[]".split("");
const brainfuck = {
    name: "brainfuck",
    startState: function() {
        return {
            commentLine: false,
            left: 0,
            right: 0,
            commentLoop: false
        };
    },
    token: function(stream, state) {
        if (stream.eatSpace()) return null;
        if (stream.sol()) {
            state.commentLine = false;
        }
        var ch = stream.next().toString();
        if (reserve.indexOf(ch) !== -1) {
            if (state.commentLine === true) {
                if (stream.eol()) {
                    state.commentLine = false;
                }
                return "comment";
            }
            if (ch === "]" || ch === "[") {
                if (ch === "[") {
                    state.left++;
                } else {
                    state.right++;
                }
                return "bracket";
            } else if (ch === "+" || ch === "-") {
                return "keyword";
            } else if (ch === "<" || ch === ">") {
                return "atom";
            } else if (ch === "." || ch === ",") {
                return "def";
            }
        } else {
            state.commentLine = true;
            if (stream.eol()) {
                state.commentLine = false;
            }
            return "comment";
        }
        if (stream.eol()) {
            state.commentLine = false;
        }
    }
};
}}),
}]);

//# sourceMappingURL=node_modules_%40codemirror_legacy-modes_mode_brainfuck_9205343d.js.map