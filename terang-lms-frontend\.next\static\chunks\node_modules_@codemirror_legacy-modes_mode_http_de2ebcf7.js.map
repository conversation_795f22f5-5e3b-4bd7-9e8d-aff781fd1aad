{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/http.js"], "sourcesContent": ["function failFirstLine(stream, state) {\n  stream.skipToEnd();\n  state.cur = header;\n  return \"error\";\n}\n\nfunction start(stream, state) {\n  if (stream.match(/^HTTP\\/\\d\\.\\d/)) {\n    state.cur = responseStatusCode;\n    return \"keyword\";\n  } else if (stream.match(/^[A-Z]+/) && /[ \\t]/.test(stream.peek())) {\n    state.cur = requestPath;\n    return \"keyword\";\n  } else {\n    return failFirstLine(stream, state);\n  }\n}\n\nfunction responseStatusCode(stream, state) {\n  var code = stream.match(/^\\d+/);\n  if (!code) return failFirstLine(stream, state);\n\n  state.cur = responseStatusText;\n  var status = Number(code[0]);\n  if (status >= 100 && status < 400) {\n    return \"atom\";\n  } else {\n    return \"error\";\n  }\n}\n\nfunction responseStatusText(stream, state) {\n  stream.skipToEnd();\n  state.cur = header;\n  return null;\n}\n\nfunction requestPath(stream, state) {\n  stream.eatWhile(/\\S/);\n  state.cur = requestProtocol;\n  return \"string.special\";\n}\n\nfunction requestProtocol(stream, state) {\n  if (stream.match(/^HTTP\\/\\d\\.\\d$/)) {\n    state.cur = header;\n    return \"keyword\";\n  } else {\n    return failFirstLine(stream, state);\n  }\n}\n\nfunction header(stream) {\n  if (stream.sol() && !stream.eat(/[ \\t]/)) {\n    if (stream.match(/^.*?:/)) {\n      return \"atom\";\n    } else {\n      stream.skipToEnd();\n      return \"error\";\n    }\n  } else {\n    stream.skipToEnd();\n    return \"string\";\n  }\n}\n\nfunction body(stream) {\n  stream.skipToEnd();\n  return null;\n}\n\nexport const http = {\n  name: \"http\",\n  token: function(stream, state) {\n    var cur = state.cur;\n    if (cur != header && cur != body && stream.eatSpace()) return null;\n    return cur(stream, state);\n  },\n\n  blankLine: function(state) {\n    state.cur = body;\n  },\n\n  startState: function() {\n    return {cur: start};\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,OAAO,SAAS;IAChB,MAAM,GAAG,GAAG;IACZ,OAAO;AACT;AAEA,SAAS,MAAM,MAAM,EAAE,KAAK;IAC1B,IAAI,OAAO,KAAK,CAAC,kBAAkB;QACjC,MAAM,GAAG,GAAG;QACZ,OAAO;IACT,OAAO,IAAI,OAAO,KAAK,CAAC,cAAc,QAAQ,IAAI,CAAC,OAAO,IAAI,KAAK;QACjE,MAAM,GAAG,GAAG;QACZ,OAAO;IACT,OAAO;QACL,OAAO,cAAc,QAAQ;IAC/B;AACF;AAEA,SAAS,mBAAmB,MAAM,EAAE,KAAK;IACvC,IAAI,OAAO,OAAO,KAAK,CAAC;IACxB,IAAI,CAAC,MAAM,OAAO,cAAc,QAAQ;IAExC,MAAM,GAAG,GAAG;IACZ,IAAI,SAAS,OAAO,IAAI,CAAC,EAAE;IAC3B,IAAI,UAAU,OAAO,SAAS,KAAK;QACjC,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEA,SAAS,mBAAmB,MAAM,EAAE,KAAK;IACvC,OAAO,SAAS;IAChB,MAAM,GAAG,GAAG;IACZ,OAAO;AACT;AAEA,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,OAAO,QAAQ,CAAC;IAChB,MAAM,GAAG,GAAG;IACZ,OAAO;AACT;AAEA,SAAS,gBAAgB,MAAM,EAAE,KAAK;IACpC,IAAI,OAAO,KAAK,CAAC,mBAAmB;QAClC,MAAM,GAAG,GAAG;QACZ,OAAO;IACT,OAAO;QACL,OAAO,cAAc,QAAQ;IAC/B;AACF;AAEA,SAAS,OAAO,MAAM;IACpB,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,UAAU;QACxC,IAAI,OAAO,KAAK,CAAC,UAAU;YACzB,OAAO;QACT,OAAO;YACL,OAAO,SAAS;YAChB,OAAO;QACT;IACF,OAAO;QACL,OAAO,SAAS;QAChB,OAAO;IACT;AACF;AAEA,SAAS,KAAK,MAAM;IAClB,OAAO,SAAS;IAChB,OAAO;AACT;AAEO,MAAM,OAAO;IAClB,MAAM;IACN,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,MAAM,MAAM,GAAG;QACnB,IAAI,OAAO,UAAU,OAAO,QAAQ,OAAO,QAAQ,IAAI,OAAO;QAC9D,OAAO,IAAI,QAAQ;IACrB;IAEA,WAAW,SAAS,KAAK;QACvB,MAAM,GAAG,GAAG;IACd;IAEA,YAAY;QACV,OAAO;YAAC,KAAK;QAAK;IACpB;AACF", "ignoreList": [0], "debugId": null}}]}