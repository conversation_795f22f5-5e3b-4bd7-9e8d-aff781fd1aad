{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/asn1.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nconst defaults = {\n  keywords: words(\"DEFINITIONS OBJECTS IF DERIVED INFORMATION ACTION\" +\n                  \" REPLY ANY NAMED CHARACTERIZED BEHAVIOUR REGISTERED\" +\n                  \" WITH AS IDENTIFIED CONSTRAINED BY PRESENT BEGIN\" +\n                  \" IMPORTS FROM UNITS SYNTAX MIN-ACCESS MAX-ACCESS\" +\n                  \" MINACCESS MAXACCESS REVISION STATUS DESCRIPTION\" +\n                  \" SEQUENCE SET COMPONENTS OF CHOICE DistinguishedName\" +\n                  \" ENUMERATED SIZE MODULE END INDEX AUGMENTS EXTENSIBILITY\" +\n                  \" IMPLIED EXPORTS\"),\n  cmipVerbs: words(\"ACTIONS ADD GET NOTIFICATIONS REPLACE REMOVE\"),\n  compareTypes: words(\"OPTIONAL DEFAULT MANAGED MODULE-TYPE MODULE_IDENTITY\" +\n                      \" MODULE-COMPL<PERSON>NCE OBJECT-TYPE OBJECT-IDENTITY\" +\n                      \" OBJECT-COMPLIANCE MODE CONFIRMED CONDITIONAL\" +\n                      \" SUBORDINATE SUPERIOR CLASS TRUE FALSE NULL\" +\n                      \" TEXTUAL-CONVENTION\"),\n  status: words(\"current deprecated mandatory obsolete\"),\n  tags: words(\"APPLICATION AUTOMATIC EXPLICIT IMPLICIT PRIVATE TAGS\" +\n              \" UNIVERSAL\"),\n  storage: words(\"BOOLEAN INTEGER OBJECT IDENTIFIER BIT OCTET STRING\" +\n                 \" UTCTime InterfaceIndex IANAifType CMIP-Attribute\" +\n                 \" REAL PACKAGE PACKAGES IpAddress PhysAddress\" +\n                 \" NetworkAddress BITS BMPString TimeStamp TimeTicks\" +\n                 \" TruthValue RowStatus DisplayString GeneralString\" +\n                 \" GraphicString IA5String NumericString\" +\n                 \" PrintableString SnmpAdminString TeletexString\" +\n                 \" UTF8String VideotexString VisibleString StringStore\" +\n                 \" ISO646String T61String UniversalString Unsigned32\" +\n                 \" Integer32 Gauge Gauge32 Counter Counter32 Counter64\"),\n  modifier: words(\"ATTRIBUTE ATTRIBUTES MANDATORY-GROUP MANDATORY-GROUPS\" +\n                  \" GROUP GROUPS ELEMENTS EQUALITY ORDERING SUBSTRINGS\" +\n                  \" DEFINED\"),\n  accessTypes: words(\"not-accessible accessible-for-notify read-only\" +\n                     \" read-create read-write\"),\n  multiLineStrings: true\n}\n\nexport function asn1(parserConfig) {\n  var keywords = parserConfig.keywords || defaults.keywords,\n      cmipVerbs = parserConfig.cmipVerbs || defaults.cmipVerbs,\n      compareTypes = parserConfig.compareTypes || defaults.compareTypes,\n      status = parserConfig.status || defaults.status,\n      tags = parserConfig.tags || defaults.tags,\n      storage = parserConfig.storage || defaults.storage,\n      modifier = parserConfig.modifier || defaults.modifier,\n      accessTypes = parserConfig.accessTypes|| defaults.accessTypes,\n      multiLineStrings = parserConfig.multiLineStrings || defaults.multiLineStrings,\n      indentStatements = parserConfig.indentStatements !== false;\n  var isOperatorChar = /[\\|\\^]/;\n  var curPunc;\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (ch == '\"' || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    }\n    if (/[\\[\\]\\(\\){}:=,;]/.test(ch)) {\n      curPunc = ch;\n      return \"punctuation\";\n    }\n    if (ch == \"-\"){\n      if (stream.eat(\"-\")) {\n        stream.skipToEnd();\n        return \"comment\";\n      }\n    }\n    if (/\\d/.test(ch)) {\n      stream.eatWhile(/[\\w\\.]/);\n      return \"number\";\n    }\n    if (isOperatorChar.test(ch)) {\n      stream.eatWhile(isOperatorChar);\n      return \"operator\";\n    }\n\n    stream.eatWhile(/[\\w\\-]/);\n    var cur = stream.current();\n    if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n    if (cmipVerbs.propertyIsEnumerable(cur)) return \"variableName\";\n    if (compareTypes.propertyIsEnumerable(cur)) return \"atom\";\n    if (status.propertyIsEnumerable(cur)) return \"comment\";\n    if (tags.propertyIsEnumerable(cur)) return \"typeName\";\n    if (storage.propertyIsEnumerable(cur)) return \"modifier\";\n    if (modifier.propertyIsEnumerable(cur)) return \"modifier\";\n    if (accessTypes.propertyIsEnumerable(cur)) return \"modifier\";\n\n    return \"variableName\";\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next, end = false;\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped){\n          var afterNext = stream.peek();\n          //look if the character if the quote is like the B in '10100010'B\n          if (afterNext){\n            afterNext = afterNext.toLowerCase();\n            if(afterNext == \"b\" || afterNext == \"h\" || afterNext == \"o\")\n              stream.next();\n          }\n          end = true; break;\n        }\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (end || !(escaped || multiLineStrings))\n        state.tokenize = null;\n      return \"string\";\n    };\n  }\n\n  function Context(indented, column, type, align, prev) {\n    this.indented = indented;\n    this.column = column;\n    this.type = type;\n    this.align = align;\n    this.prev = prev;\n  }\n  function pushContext(state, col, type) {\n    var indent = state.indented;\n    if (state.context && state.context.type == \"statement\")\n      indent = state.context.indented;\n    return state.context = new Context(indent, col, type, null, state.context);\n  }\n  function popContext(state) {\n    var t = state.context.type;\n    if (t == \")\" || t == \"]\" || t == \"}\")\n      state.indented = state.context.indented;\n    return state.context = state.context.prev;\n  }\n\n  //Interface\n  return {\n    name: \"asn1\",\n    startState: function() {\n      return {\n        tokenize: null,\n        context: new Context(-2, 0, \"top\", false),\n        indented: 0,\n        startOfLine: true\n      };\n    },\n\n    token: function(stream, state) {\n      var ctx = state.context;\n      if (stream.sol()) {\n        if (ctx.align == null) ctx.align = false;\n        state.indented = stream.indentation();\n        state.startOfLine = true;\n      }\n      if (stream.eatSpace()) return null;\n      curPunc = null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style == \"comment\") return style;\n      if (ctx.align == null) ctx.align = true;\n\n      if ((curPunc == \";\" || curPunc == \":\" || curPunc == \",\")\n          && ctx.type == \"statement\"){\n        popContext(state);\n      }\n      else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n      else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n      else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n      else if (curPunc == \"}\") {\n        while (ctx.type == \"statement\") ctx = popContext(state);\n        if (ctx.type == \"}\") ctx = popContext(state);\n        while (ctx.type == \"statement\") ctx = popContext(state);\n      }\n      else if (curPunc == ctx.type) popContext(state);\n      else if (indentStatements && (((ctx.type == \"}\" || ctx.type == \"top\")\n                                     && curPunc != ';') || (ctx.type == \"statement\"\n                                                            && curPunc == \"newstatement\")))\n        pushContext(state, stream.column(), \"statement\");\n\n      state.startOfLine = false;\n      return style;\n    },\n\n    languageData: {\n      indentOnInput: /^\\s*[{}]$/,\n      commentTokens: {line: \"--\"}\n    }\n  };\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AAEA,MAAM,WAAW;IACf,UAAU,MAAM,sDACA,wDACA,qDACA,qDACA,qDACA,yDACA,6DACA;IAChB,WAAW,MAAM;IACjB,cAAc,MAAM,yDACA,mDACA,kDACA,gDACA;IACpB,QAAQ,MAAM;IACd,MAAM,MAAM,yDACA;IACZ,SAAS,MAAM,uDACA,sDACA,iDACA,uDACA,sDACA,2CACA,mDACA,yDACA,uDACA;IACf,UAAU,MAAM,0DACA,wDACA;IAChB,aAAa,MAAM,mDACA;IACnB,kBAAkB;AACpB;AAEO,SAAS,KAAK,YAAY;IAC/B,IAAI,WAAW,aAAa,QAAQ,IAAI,SAAS,QAAQ,EACrD,YAAY,aAAa,SAAS,IAAI,SAAS,SAAS,EACxD,eAAe,aAAa,YAAY,IAAI,SAAS,YAAY,EACjE,SAAS,aAAa,MAAM,IAAI,SAAS,MAAM,EAC/C,OAAO,aAAa,IAAI,IAAI,SAAS,IAAI,EACzC,UAAU,aAAa,OAAO,IAAI,SAAS,OAAO,EAClD,WAAW,aAAa,QAAQ,IAAI,SAAS,QAAQ,EACrD,cAAc,aAAa,WAAW,IAAG,SAAS,WAAW,EAC7D,mBAAmB,aAAa,gBAAgB,IAAI,SAAS,gBAAgB,EAC7E,mBAAmB,aAAa,gBAAgB,KAAK;IACzD,IAAI,iBAAiB;IACrB,IAAI;IAEJ,SAAS,UAAU,MAAM,EAAE,KAAK;QAC9B,IAAI,KAAK,OAAO,IAAI;QACpB,IAAI,MAAM,OAAO,MAAM,KAAK;YAC1B,MAAM,QAAQ,GAAG,YAAY;YAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;QAChC;QACA,IAAI,mBAAmB,IAAI,CAAC,KAAK;YAC/B,UAAU;YACV,OAAO;QACT;QACA,IAAI,MAAM,KAAI;YACZ,IAAI,OAAO,GAAG,CAAC,MAAM;gBACnB,OAAO,SAAS;gBAChB,OAAO;YACT;QACF;QACA,IAAI,KAAK,IAAI,CAAC,KAAK;YACjB,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,IAAI,eAAe,IAAI,CAAC,KAAK;YAC3B,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,QAAQ,CAAC;QAChB,IAAI,MAAM,OAAO,OAAO;QACxB,IAAI,SAAS,oBAAoB,CAAC,MAAM,OAAO;QAC/C,IAAI,UAAU,oBAAoB,CAAC,MAAM,OAAO;QAChD,IAAI,aAAa,oBAAoB,CAAC,MAAM,OAAO;QACnD,IAAI,OAAO,oBAAoB,CAAC,MAAM,OAAO;QAC7C,IAAI,KAAK,oBAAoB,CAAC,MAAM,OAAO;QAC3C,IAAI,QAAQ,oBAAoB,CAAC,MAAM,OAAO;QAC9C,IAAI,SAAS,oBAAoB,CAAC,MAAM,OAAO;QAC/C,IAAI,YAAY,oBAAoB,CAAC,MAAM,OAAO;QAElD,OAAO;IACT;IAEA,SAAS,YAAY,KAAK;QACxB,OAAO,SAAS,MAAM,EAAE,KAAK;YAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;YACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;gBACrC,IAAI,QAAQ,SAAS,CAAC,SAAQ;oBAC5B,IAAI,YAAY,OAAO,IAAI;oBAC3B,iEAAiE;oBACjE,IAAI,WAAU;wBACZ,YAAY,UAAU,WAAW;wBACjC,IAAG,aAAa,OAAO,aAAa,OAAO,aAAa,KACtD,OAAO,IAAI;oBACf;oBACA,MAAM;oBAAM;gBACd;gBACA,UAAU,CAAC,WAAW,QAAQ;YAChC;YACA,IAAI,OAAO,CAAC,CAAC,WAAW,gBAAgB,GACtC,MAAM,QAAQ,GAAG;YACnB,OAAO;QACT;IACF;IAEA,SAAS,QAAQ,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;QAClD,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;IACd;IACA,SAAS,YAAY,KAAK,EAAE,GAAG,EAAE,IAAI;QACnC,IAAI,SAAS,MAAM,QAAQ;QAC3B,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,aACzC,SAAS,MAAM,OAAO,CAAC,QAAQ;QACjC,OAAO,MAAM,OAAO,GAAG,IAAI,QAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,OAAO;IAC3E;IACA,SAAS,WAAW,KAAK;QACvB,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI;QAC1B,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAC/B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ;QACzC,OAAO,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;IAC3C;IAEA,WAAW;IACX,OAAO;QACL,MAAM;QACN,YAAY;YACV,OAAO;gBACL,UAAU;gBACV,SAAS,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO;gBACnC,UAAU;gBACV,aAAa;YACf;QACF;QAEA,OAAO,SAAS,MAAM,EAAE,KAAK;YAC3B,IAAI,MAAM,MAAM,OAAO;YACvB,IAAI,OAAO,GAAG,IAAI;gBAChB,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;gBACnC,MAAM,QAAQ,GAAG,OAAO,WAAW;gBACnC,MAAM,WAAW,GAAG;YACtB;YACA,IAAI,OAAO,QAAQ,IAAI,OAAO;YAC9B,UAAU;YACV,IAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,SAAS,EAAE,QAAQ;YAClD,IAAI,SAAS,WAAW,OAAO;YAC/B,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;YAEnC,IAAI,CAAC,WAAW,OAAO,WAAW,OAAO,WAAW,GAAG,KAChD,IAAI,IAAI,IAAI,aAAY;gBAC7B,WAAW;YACb,OACK,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;iBACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;iBACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;iBACxD,IAAI,WAAW,KAAK;gBACvB,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;gBACjD,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM,WAAW;gBACtC,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;YACnD,OACK,IAAI,WAAW,IAAI,IAAI,EAAE,WAAW;iBACpC,IAAI,oBAAoB,CAAC,AAAC,CAAC,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,KAClC,WAAW,OAAS,IAAI,IAAI,IAAI,eACT,WAAW,cAAe,GACjF,YAAY,OAAO,OAAO,MAAM,IAAI;YAEtC,MAAM,WAAW,GAAG;YACpB,OAAO;QACT;QAEA,cAAc;YACZ,eAAe;YACf,eAAe;gBAAC,MAAM;YAAI;QAC5B;IACF;AACF", "ignoreList": [0], "debugId": null}}]}