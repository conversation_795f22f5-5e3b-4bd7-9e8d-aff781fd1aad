{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/vb.js"], "sourcesContent": ["var ERRORCLASS = 'error';\n\nfunction wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\", \"i\");\n}\n\nvar singleOperators = new RegExp(\"^[\\\\+\\\\-\\\\*/%&\\\\\\\\|\\\\^~<>!]\");\nvar singleDelimiters = new RegExp('^[\\\\(\\\\)\\\\[\\\\]\\\\{\\\\}@,:`=;\\\\.]');\nvar doubleOperators = new RegExp(\"^((==)|(<>)|(<=)|(>=)|(<>)|(<<)|(>>)|(//)|(\\\\*\\\\*))\");\nvar doubleDelimiters = new RegExp(\"^((\\\\+=)|(\\\\-=)|(\\\\*=)|(%=)|(/=)|(&=)|(\\\\|=)|(\\\\^=))\");\nvar tripleDelimiters = new RegExp(\"^((//=)|(>>=)|(<<=)|(\\\\*\\\\*=))\");\nvar identifiers = new RegExp(\"^[_A-Za-z][_A-Za-z0-9]*\");\n\nvar openingKeywords = ['class','module', 'sub','enum','select','while','if','function', 'get','set','property', 'try', 'structure', 'synclock', 'using', 'with'];\nvar middleKeywords = ['else','elseif','case', 'catch', 'finally'];\nvar endKeywords = ['next','loop'];\n\nvar operatorKeywords = ['and', \"andalso\", 'or', 'orelse', 'xor', 'in', 'not', 'is', 'isnot', 'like'];\nvar wordOperators = wordRegexp(operatorKeywords);\n\nvar commonKeywords = [\"#const\", \"#else\", \"#elseif\", \"#end\", \"#if\", \"#region\", \"addhandler\", \"addressof\", \"alias\", \"as\", \"byref\", \"byval\", \"cbool\", \"cbyte\", \"cchar\", \"cdate\", \"cdbl\", \"cdec\", \"cint\", \"clng\", \"cobj\", \"compare\", \"const\", \"continue\", \"csbyte\", \"cshort\", \"csng\", \"cstr\", \"cuint\", \"culng\", \"cushort\", \"declare\", \"default\", \"delegate\", \"dim\", \"directcast\", \"each\", \"erase\", \"error\", \"event\", \"exit\", \"explicit\", \"false\", \"for\", \"friend\", \"gettype\", \"goto\", \"handles\", \"implements\", \"imports\", \"infer\", \"inherits\", \"interface\", \"isfalse\", \"istrue\", \"lib\", \"me\", \"mod\", \"mustinherit\", \"mustoverride\", \"my\", \"mybase\", \"myclass\", \"namespace\", \"narrowing\", \"new\", \"nothing\", \"notinheritable\", \"notoverridable\", \"of\", \"off\", \"on\", \"operator\", \"option\", \"optional\", \"out\", \"overloads\", \"overridable\", \"overrides\", \"paramarray\", \"partial\", \"private\", \"protected\", \"public\", \"raiseevent\", \"readonly\", \"redim\", \"removehandler\", \"resume\", \"return\", \"shadows\", \"shared\", \"static\", \"step\", \"stop\", \"strict\", \"then\", \"throw\", \"to\", \"true\", \"trycast\", \"typeof\", \"until\", \"until\", \"when\", \"widening\", \"withevents\", \"writeonly\"];\n\nvar commontypes = ['object', 'boolean', 'char', 'string', 'byte', 'sbyte', 'short', 'ushort', 'int16', 'uint16', 'integer', 'uinteger', 'int32', 'uint32', 'long', 'ulong', 'int64', 'uint64', 'decimal', 'single', 'double', 'float', 'date', 'datetime', 'intptr', 'uintptr'];\n\nvar keywords = wordRegexp(commonKeywords);\nvar types = wordRegexp(commontypes);\nvar stringPrefixes = '\"';\n\nvar opening = wordRegexp(openingKeywords);\nvar middle = wordRegexp(middleKeywords);\nvar closing = wordRegexp(endKeywords);\nvar doubleClosing = wordRegexp(['end']);\nvar doOpening = wordRegexp(['do']);\n\nvar indentInfo = null;\n\nfunction indent(_stream, state) {\n  state.currentIndent++;\n}\n\nfunction dedent(_stream, state) {\n  state.currentIndent--;\n}\n// tokenizers\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  var ch = stream.peek();\n\n  // Handle Comments\n  if (ch === \"'\") {\n    stream.skipToEnd();\n    return 'comment';\n  }\n\n\n  // Handle Number Literals\n  if (stream.match(/^((&H)|(&O))?[0-9\\.a-f]/i, false)) {\n    var floatLiteral = false;\n    // Floats\n    if (stream.match(/^\\d*\\.\\d+F?/i)) { floatLiteral = true; }\n    else if (stream.match(/^\\d+\\.\\d*F?/)) { floatLiteral = true; }\n    else if (stream.match(/^\\.\\d+F?/)) { floatLiteral = true; }\n\n    if (floatLiteral) {\n      // Float literals may be \"imaginary\"\n      stream.eat(/J/i);\n      return 'number';\n    }\n    // Integers\n    var intLiteral = false;\n    // Hex\n    if (stream.match(/^&H[0-9a-f]+/i)) { intLiteral = true; }\n    // Octal\n    else if (stream.match(/^&O[0-7]+/i)) { intLiteral = true; }\n    // Decimal\n    else if (stream.match(/^[1-9]\\d*F?/)) {\n      // Decimal literals may be \"imaginary\"\n      stream.eat(/J/i);\n      // TODO - Can you have imaginary longs?\n      intLiteral = true;\n    }\n    // Zero by itself with no other piece of number.\n    else if (stream.match(/^0(?![\\dx])/i)) { intLiteral = true; }\n    if (intLiteral) {\n      // Integer literals may be \"long\"\n      stream.eat(/L/i);\n      return 'number';\n    }\n  }\n\n  // Handle Strings\n  if (stream.match(stringPrefixes)) {\n    state.tokenize = tokenStringFactory(stream.current());\n    return state.tokenize(stream, state);\n  }\n\n  // Handle operators and Delimiters\n  if (stream.match(tripleDelimiters) || stream.match(doubleDelimiters)) {\n    return null;\n  }\n  if (stream.match(doubleOperators)\n      || stream.match(singleOperators)\n      || stream.match(wordOperators)) {\n    return 'operator';\n  }\n  if (stream.match(singleDelimiters)) {\n    return null;\n  }\n  if (stream.match(doOpening)) {\n    indent(stream,state);\n    state.doInCurrentLine = true;\n    return 'keyword';\n  }\n  if (stream.match(opening)) {\n    if (! state.doInCurrentLine)\n      indent(stream,state);\n    else\n      state.doInCurrentLine = false;\n    return 'keyword';\n  }\n  if (stream.match(middle)) {\n    return 'keyword';\n  }\n\n  if (stream.match(doubleClosing)) {\n    dedent(stream,state);\n    dedent(stream,state);\n    return 'keyword';\n  }\n  if (stream.match(closing)) {\n    dedent(stream,state);\n    return 'keyword';\n  }\n\n  if (stream.match(types)) {\n    return 'keyword';\n  }\n\n  if (stream.match(keywords)) {\n    return 'keyword';\n  }\n\n  if (stream.match(identifiers)) {\n    return 'variable';\n  }\n\n  // Handle non-detected items\n  stream.next();\n  return ERRORCLASS;\n}\n\nfunction tokenStringFactory(delimiter) {\n  var singleline = delimiter.length == 1;\n  var OUTCLASS = 'string';\n\n  return function(stream, state) {\n    while (!stream.eol()) {\n      stream.eatWhile(/[^'\"]/);\n      if (stream.match(delimiter)) {\n        state.tokenize = tokenBase;\n        return OUTCLASS;\n      } else {\n        stream.eat(/['\"]/);\n      }\n    }\n    if (singleline) {\n      state.tokenize = tokenBase;\n    }\n    return OUTCLASS;\n  };\n}\n\n\nfunction tokenLexer(stream, state) {\n  var style = state.tokenize(stream, state);\n  var current = stream.current();\n\n  // Handle '.' connected identifiers\n  if (current === '.') {\n    style = state.tokenize(stream, state);\n    if (style === 'variable') {\n      return 'variable';\n    } else {\n      return ERRORCLASS;\n    }\n  }\n\n\n  var delimiter_index = '[({'.indexOf(current);\n  if (delimiter_index !== -1) {\n    indent(stream, state );\n  }\n  if (indentInfo === 'dedent') {\n    if (dedent(stream, state)) {\n      return ERRORCLASS;\n    }\n  }\n  delimiter_index = '])}'.indexOf(current);\n  if (delimiter_index !== -1) {\n    if (dedent(stream, state)) {\n      return ERRORCLASS;\n    }\n  }\n\n  return style;\n}\n\nexport const vb = {\n  name: \"vb\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      lastToken: null,\n      currentIndent: 0,\n      nextLineIndent: 0,\n      doInCurrentLine: false\n\n\n    };\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      state.currentIndent += state.nextLineIndent;\n      state.nextLineIndent = 0;\n      state.doInCurrentLine = 0;\n    }\n    var style = tokenLexer(stream, state);\n\n    state.lastToken = {style:style, content: stream.current()};\n\n\n\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var trueText = textAfter.replace(/^\\s+|\\s+$/g, '') ;\n    if (trueText.match(closing) || trueText.match(doubleClosing) || trueText.match(middle)) return cx.unit*(state.currentIndent-1);\n    if(state.currentIndent < 0) return 0;\n    return state.currentIndent * cx.unit;\n  },\n\n  languageData: {\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", '\"']},\n    commentTokens: {line: \"'\"},\n    autocomplete: openingKeywords.concat(middleKeywords).concat(endKeywords)\n      .concat(operatorKeywords).concat(commonKeywords).concat(commontypes)\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,aAAa;AAEjB,SAAS,WAAW,KAAK;IACvB,OAAO,IAAI,OAAO,QAAQ,MAAM,IAAI,CAAC,SAAS,SAAS;AACzD;AAEA,IAAI,kBAAkB,IAAI,OAAO;AACjC,IAAI,mBAAmB,IAAI,OAAO;AAClC,IAAI,kBAAkB,IAAI,OAAO;AACjC,IAAI,mBAAmB,IAAI,OAAO;AAClC,IAAI,mBAAmB,IAAI,OAAO;AAClC,IAAI,cAAc,IAAI,OAAO;AAE7B,IAAI,kBAAkB;IAAC;IAAQ;IAAU;IAAM;IAAO;IAAS;IAAQ;IAAK;IAAY;IAAM;IAAM;IAAY;IAAO;IAAa;IAAY;IAAS;CAAO;AAChK,IAAI,iBAAiB;IAAC;IAAO;IAAS;IAAQ;IAAS;CAAU;AACjE,IAAI,cAAc;IAAC;IAAO;CAAO;AAEjC,IAAI,mBAAmB;IAAC;IAAO;IAAW;IAAM;IAAU;IAAO;IAAM;IAAO;IAAM;IAAS;CAAO;AACpG,IAAI,gBAAgB,WAAW;AAE/B,IAAI,iBAAiB;IAAC;IAAU;IAAS;IAAW;IAAQ;IAAO;IAAW;IAAc;IAAa;IAAS;IAAM;IAAS;IAAS;IAAS;IAAS;IAAS;IAAS;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAW;IAAS;IAAY;IAAU;IAAU;IAAQ;IAAQ;IAAS;IAAS;IAAW;IAAW;IAAW;IAAY;IAAO;IAAc;IAAQ;IAAS;IAAS;IAAS;IAAQ;IAAY;IAAS;IAAO;IAAU;IAAW;IAAQ;IAAW;IAAc;IAAW;IAAS;IAAY;IAAa;IAAW;IAAU;IAAO;IAAM;IAAO;IAAe;IAAgB;IAAM;IAAU;IAAW;IAAa;IAAa;IAAO;IAAW;IAAkB;IAAkB;IAAM;IAAO;IAAM;IAAY;IAAU;IAAY;IAAO;IAAa;IAAe;IAAa;IAAc;IAAW;IAAW;IAAa;IAAU;IAAc;IAAY;IAAS;IAAiB;IAAU;IAAU;IAAW;IAAU;IAAU;IAAQ;IAAQ;IAAU;IAAQ;IAAS;IAAM;IAAQ;IAAW;IAAU;IAAS;IAAS;IAAQ;IAAY;IAAc;CAAY;AAEhmC,IAAI,cAAc;IAAC;IAAU;IAAW;IAAQ;IAAU;IAAQ;IAAS;IAAS;IAAU;IAAS;IAAU;IAAW;IAAY;IAAS;IAAU;IAAQ;IAAS;IAAS;IAAU;IAAW;IAAU;IAAU;IAAS;IAAQ;IAAY;IAAU;CAAU;AAE/Q,IAAI,WAAW,WAAW;AAC1B,IAAI,QAAQ,WAAW;AACvB,IAAI,iBAAiB;AAErB,IAAI,UAAU,WAAW;AACzB,IAAI,SAAS,WAAW;AACxB,IAAI,UAAU,WAAW;AACzB,IAAI,gBAAgB,WAAW;IAAC;CAAM;AACtC,IAAI,YAAY,WAAW;IAAC;CAAK;AAEjC,IAAI,aAAa;AAEjB,SAAS,OAAO,OAAO,EAAE,KAAK;IAC5B,MAAM,aAAa;AACrB;AAEA,SAAS,OAAO,OAAO,EAAE,KAAK;IAC5B,MAAM,aAAa;AACrB;AACA,aAAa;AACb,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,OAAO,QAAQ,IAAI;QACrB,OAAO;IACT;IAEA,IAAI,KAAK,OAAO,IAAI;IAEpB,kBAAkB;IAClB,IAAI,OAAO,KAAK;QACd,OAAO,SAAS;QAChB,OAAO;IACT;IAGA,yBAAyB;IACzB,IAAI,OAAO,KAAK,CAAC,4BAA4B,QAAQ;QACnD,IAAI,eAAe;QACnB,SAAS;QACT,IAAI,OAAO,KAAK,CAAC,iBAAiB;YAAE,eAAe;QAAM,OACpD,IAAI,OAAO,KAAK,CAAC,gBAAgB;YAAE,eAAe;QAAM,OACxD,IAAI,OAAO,KAAK,CAAC,aAAa;YAAE,eAAe;QAAM;QAE1D,IAAI,cAAc;YAChB,oCAAoC;YACpC,OAAO,GAAG,CAAC;YACX,OAAO;QACT;QACA,WAAW;QACX,IAAI,aAAa;QACjB,MAAM;QACN,IAAI,OAAO,KAAK,CAAC,kBAAkB;YAAE,aAAa;QAAM,OAEnD,IAAI,OAAO,KAAK,CAAC,eAAe;YAAE,aAAa;QAAM,OAErD,IAAI,OAAO,KAAK,CAAC,gBAAgB;YACpC,sCAAsC;YACtC,OAAO,GAAG,CAAC;YACX,uCAAuC;YACvC,aAAa;QACf,OAEK,IAAI,OAAO,KAAK,CAAC,iBAAiB;YAAE,aAAa;QAAM;QAC5D,IAAI,YAAY;YACd,iCAAiC;YACjC,OAAO,GAAG,CAAC;YACX,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,IAAI,OAAO,KAAK,CAAC,iBAAiB;QAChC,MAAM,QAAQ,GAAG,mBAAmB,OAAO,OAAO;QAClD,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IAEA,kCAAkC;IAClC,IAAI,OAAO,KAAK,CAAC,qBAAqB,OAAO,KAAK,CAAC,mBAAmB;QACpE,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,oBACV,OAAO,KAAK,CAAC,oBACb,OAAO,KAAK,CAAC,gBAAgB;QAClC,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,mBAAmB;QAClC,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,YAAY;QAC3B,OAAO,QAAO;QACd,MAAM,eAAe,GAAG;QACxB,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,UAAU;QACzB,IAAI,CAAE,MAAM,eAAe,EACzB,OAAO,QAAO;aAEd,MAAM,eAAe,GAAG;QAC1B,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,SAAS;QACxB,OAAO;IACT;IAEA,IAAI,OAAO,KAAK,CAAC,gBAAgB;QAC/B,OAAO,QAAO;QACd,OAAO,QAAO;QACd,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,UAAU;QACzB,OAAO,QAAO;QACd,OAAO;IACT;IAEA,IAAI,OAAO,KAAK,CAAC,QAAQ;QACvB,OAAO;IACT;IAEA,IAAI,OAAO,KAAK,CAAC,WAAW;QAC1B,OAAO;IACT;IAEA,IAAI,OAAO,KAAK,CAAC,cAAc;QAC7B,OAAO;IACT;IAEA,4BAA4B;IAC5B,OAAO,IAAI;IACX,OAAO;AACT;AAEA,SAAS,mBAAmB,SAAS;IACnC,IAAI,aAAa,UAAU,MAAM,IAAI;IACrC,IAAI,WAAW;IAEf,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,MAAO,CAAC,OAAO,GAAG,GAAI;YACpB,OAAO,QAAQ,CAAC;YAChB,IAAI,OAAO,KAAK,CAAC,YAAY;gBAC3B,MAAM,QAAQ,GAAG;gBACjB,OAAO;YACT,OAAO;gBACL,OAAO,GAAG,CAAC;YACb;QACF;QACA,IAAI,YAAY;YACd,MAAM,QAAQ,GAAG;QACnB;QACA,OAAO;IACT;AACF;AAGA,SAAS,WAAW,MAAM,EAAE,KAAK;IAC/B,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;IACnC,IAAI,UAAU,OAAO,OAAO;IAE5B,mCAAmC;IACnC,IAAI,YAAY,KAAK;QACnB,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QAC/B,IAAI,UAAU,YAAY;YACxB,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAGA,IAAI,kBAAkB,MAAM,OAAO,CAAC;IACpC,IAAI,oBAAoB,CAAC,GAAG;QAC1B,OAAO,QAAQ;IACjB;IACA,uCAA6B;;IAI7B;IACA,kBAAkB,MAAM,OAAO,CAAC;IAChC,IAAI,oBAAoB,CAAC,GAAG;QAC1B,IAAI,OAAO,QAAQ,QAAQ;;QAE3B;IACF;IAEA,OAAO;AACT;AAEO,MAAM,KAAK;IAChB,MAAM;IAEN,YAAY;QACV,OAAO;YACL,UAAU;YACV,WAAW;YACX,eAAe;YACf,gBAAgB;YAChB,iBAAiB;QAGnB;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,GAAG,IAAI;YAChB,MAAM,aAAa,IAAI,MAAM,cAAc;YAC3C,MAAM,cAAc,GAAG;YACvB,MAAM,eAAe,GAAG;QAC1B;QACA,IAAI,QAAQ,WAAW,QAAQ;QAE/B,MAAM,SAAS,GAAG;YAAC,OAAM;YAAO,SAAS,OAAO,OAAO;QAAE;QAIzD,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,WAAW,UAAU,OAAO,CAAC,cAAc;QAC/C,IAAI,SAAS,KAAK,CAAC,YAAY,SAAS,KAAK,CAAC,kBAAkB,SAAS,KAAK,CAAC,SAAS,OAAO,GAAG,IAAI,GAAC,CAAC,MAAM,aAAa,GAAC,CAAC;QAC7H,IAAG,MAAM,aAAa,GAAG,GAAG,OAAO;QACnC,OAAO,MAAM,aAAa,GAAG,GAAG,IAAI;IACtC;IAEA,cAAc;QACZ,eAAe;YAAC,UAAU;gBAAC;gBAAK;gBAAK;gBAAK;aAAI;QAAA;QAC9C,eAAe;YAAC,MAAM;QAAG;QACzB,cAAc,gBAAgB,MAAM,CAAC,gBAAgB,MAAM,CAAC,aACzD,MAAM,CAAC,kBAAkB,MAAM,CAAC,gBAAgB,MAAM,CAAC;IAC5D;AACF", "ignoreList": [0], "debugId": null}}]}