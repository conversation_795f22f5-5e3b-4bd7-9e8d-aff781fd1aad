{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/octave.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nvar singleOperators = new RegExp(\"^[\\\\+\\\\-\\\\*/&|\\\\^~<>!@'\\\\\\\\]\");\nvar singleDelimiters = new RegExp('^[\\\\(\\\\[\\\\{\\\\},:=;\\\\.]');\nvar doubleOperators = new RegExp(\"^((==)|(~=)|(<=)|(>=)|(<<)|(>>)|(\\\\.[\\\\+\\\\-\\\\*/\\\\^\\\\\\\\]))\");\nvar doubleDelimiters = new RegExp(\"^((!=)|(\\\\+=)|(\\\\-=)|(\\\\*=)|(/=)|(&=)|(\\\\|=)|(\\\\^=))\");\nvar tripleDelimiters = new RegExp(\"^((>>=)|(<<=))\");\nvar expressionEnd = new RegExp(\"^[\\\\]\\\\)]\");\nvar identifiers = new RegExp(\"^[_A-Za-z\\xa1-\\uffff][_A-Za-z0-9\\xa1-\\uffff]*\");\n\nvar builtins = wordRegexp([\n  'error', 'eval', 'function', 'abs', 'acos', 'atan', 'asin', 'cos',\n  'cosh', 'exp', 'log', 'prod', 'sum', 'log10', 'max', 'min', 'sign', 'sin', 'sinh',\n  'sqrt', 'tan', 'reshape', 'break', 'zeros', 'default', 'margin', 'round', 'ones',\n  'rand', 'syn', 'ceil', 'floor', 'size', 'clear', 'zeros', 'eye', 'mean', 'std', 'cov',\n  'det', 'eig', 'inv', 'norm', 'rank', 'trace', 'expm', 'logm', 'sqrtm', 'linspace', 'plot',\n  'title', 'xlabel', 'ylabel', 'legend', 'text', 'grid', 'meshgrid', 'mesh', 'num2str',\n  'fft', 'ifft', 'arrayfun', 'cellfun', 'input', 'fliplr', 'flipud', 'ismember'\n]);\n\nvar keywords = wordRegexp([\n  'return', 'case', 'switch', 'else', 'elseif', 'end', 'endif', 'endfunction',\n  'if', 'otherwise', 'do', 'for', 'while', 'try', 'catch', 'classdef', 'properties', 'events',\n  'methods', 'global', 'persistent', 'endfor', 'endwhile', 'printf', 'sprintf', 'disp', 'until',\n  'continue', 'pkg'\n]);\n\n\n// tokenizers\nfunction tokenTranspose(stream, state) {\n  if (!stream.sol() && stream.peek() === '\\'') {\n    stream.next();\n    state.tokenize = tokenBase;\n    return 'operator';\n  }\n  state.tokenize = tokenBase;\n  return tokenBase(stream, state);\n}\n\n\nfunction tokenComment(stream, state) {\n  if (stream.match(/^.*%}/)) {\n    state.tokenize = tokenBase;\n    return 'comment';\n  };\n  stream.skipToEnd();\n  return 'comment';\n}\n\nfunction tokenBase(stream, state) {\n  // whitespaces\n  if (stream.eatSpace()) return null;\n\n  // Handle one line Comments\n  if (stream.match('%{')){\n    state.tokenize = tokenComment;\n    stream.skipToEnd();\n    return 'comment';\n  }\n\n  if (stream.match(/^[%#]/)){\n    stream.skipToEnd();\n    return 'comment';\n  }\n\n  // Handle Number Literals\n  if (stream.match(/^[0-9\\.+-]/, false)) {\n    if (stream.match(/^[+-]?0x[0-9a-fA-F]+[ij]?/)) {\n      stream.tokenize = tokenBase;\n      return 'number'; };\n    if (stream.match(/^[+-]?\\d*\\.\\d+([EeDd][+-]?\\d+)?[ij]?/)) { return 'number'; };\n    if (stream.match(/^[+-]?\\d+([EeDd][+-]?\\d+)?[ij]?/)) { return 'number'; };\n  }\n  if (stream.match(wordRegexp(['nan','NaN','inf','Inf']))) { return 'number'; };\n\n  // Handle Strings\n  var m = stream.match(/^\"(?:[^\"]|\"\")*(\"|$)/) || stream.match(/^'(?:[^']|'')*('|$)/)\n  if (m) { return m[1] ? 'string' : \"error\"; }\n\n  // Handle words\n  if (stream.match(keywords)) { return 'keyword'; } ;\n  if (stream.match(builtins)) { return 'builtin'; } ;\n  if (stream.match(identifiers)) { return 'variable'; } ;\n\n  if (stream.match(singleOperators) || stream.match(doubleOperators)) { return 'operator'; };\n  if (stream.match(singleDelimiters) || stream.match(doubleDelimiters) || stream.match(tripleDelimiters)) { return null; };\n\n  if (stream.match(expressionEnd)) {\n    state.tokenize = tokenTranspose;\n    return null;\n  };\n\n\n  // Handle non-detected items\n  stream.next();\n  return 'error';\n};\n\n\nexport const octave = {\n  name: \"octave\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase\n    };\n  },\n\n  token: function(stream, state) {\n    var style = state.tokenize(stream, state);\n    if (style === 'number' || style === 'variable'){\n      state.tokenize = tokenTranspose;\n    }\n    return style;\n  },\n\n  languageData: {\n    commentTokens: {line: \"%\"}\n  }\n};\n\n"], "names": [], "mappings": ";;;AAAA,SAAS,WAAW,KAAK;IACvB,OAAO,IAAI,OAAO,QAAQ,MAAM,IAAI,CAAC,SAAS;AAChD;AAEA,IAAI,kBAAkB,IAAI,OAAO;AACjC,IAAI,mBAAmB,IAAI,OAAO;AAClC,IAAI,kBAAkB,IAAI,OAAO;AACjC,IAAI,mBAAmB,IAAI,OAAO;AAClC,IAAI,mBAAmB,IAAI,OAAO;AAClC,IAAI,gBAAgB,IAAI,OAAO;AAC/B,IAAI,cAAc,IAAI,OAAO;AAE7B,IAAI,WAAW,WAAW;IACxB;IAAS;IAAQ;IAAY;IAAO;IAAQ;IAAQ;IAAQ;IAC5D;IAAQ;IAAO;IAAO;IAAQ;IAAO;IAAS;IAAO;IAAO;IAAQ;IAAO;IAC3E;IAAQ;IAAO;IAAW;IAAS;IAAS;IAAW;IAAU;IAAS;IAC1E;IAAQ;IAAO;IAAQ;IAAS;IAAQ;IAAS;IAAS;IAAO;IAAQ;IAAO;IAChF;IAAO;IAAO;IAAO;IAAQ;IAAQ;IAAS;IAAQ;IAAQ;IAAS;IAAY;IACnF;IAAS;IAAU;IAAU;IAAU;IAAQ;IAAQ;IAAY;IAAQ;IAC3E;IAAO;IAAQ;IAAY;IAAW;IAAS;IAAU;IAAU;CACpE;AAED,IAAI,WAAW,WAAW;IACxB;IAAU;IAAQ;IAAU;IAAQ;IAAU;IAAO;IAAS;IAC9D;IAAM;IAAa;IAAM;IAAO;IAAS;IAAO;IAAS;IAAY;IAAc;IACnF;IAAW;IAAU;IAAc;IAAU;IAAY;IAAU;IAAW;IAAQ;IACtF;IAAY;CACb;AAGD,aAAa;AACb,SAAS,eAAe,MAAM,EAAE,KAAK;IACnC,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO,IAAI,OAAO,MAAM;QAC3C,OAAO,IAAI;QACX,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT;IACA,MAAM,QAAQ,GAAG;IACjB,OAAO,UAAU,QAAQ;AAC3B;AAGA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,OAAO,KAAK,CAAC,UAAU;QACzB,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT;;IACA,OAAO,SAAS;IAChB,OAAO;AACT;AAEA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,cAAc;IACd,IAAI,OAAO,QAAQ,IAAI,OAAO;IAE9B,2BAA2B;IAC3B,IAAI,OAAO,KAAK,CAAC,OAAM;QACrB,MAAM,QAAQ,GAAG;QACjB,OAAO,SAAS;QAChB,OAAO;IACT;IAEA,IAAI,OAAO,KAAK,CAAC,UAAS;QACxB,OAAO,SAAS;QAChB,OAAO;IACT;IAEA,yBAAyB;IACzB,IAAI,OAAO,KAAK,CAAC,cAAc,QAAQ;QACrC,IAAI,OAAO,KAAK,CAAC,8BAA8B;YAC7C,OAAO,QAAQ,GAAG;YAClB,OAAO;QAAU;;QACnB,IAAI,OAAO,KAAK,CAAC,yCAAyC;YAAE,OAAO;QAAU;;QAC7E,IAAI,OAAO,KAAK,CAAC,oCAAoC;YAAE,OAAO;QAAU;;IAC1E;IACA,IAAI,OAAO,KAAK,CAAC,WAAW;QAAC;QAAM;QAAM;QAAM;KAAM,IAAI;QAAE,OAAO;IAAU;;IAE5E,iBAAiB;IACjB,IAAI,IAAI,OAAO,KAAK,CAAC,0BAA0B,OAAO,KAAK,CAAC;IAC5D,IAAI,GAAG;QAAE,OAAO,CAAC,CAAC,EAAE,GAAG,WAAW;IAAS;IAE3C,eAAe;IACf,IAAI,OAAO,KAAK,CAAC,WAAW;QAAE,OAAO;IAAW;;IAChD,IAAI,OAAO,KAAK,CAAC,WAAW;QAAE,OAAO;IAAW;;IAChD,IAAI,OAAO,KAAK,CAAC,cAAc;QAAE,OAAO;IAAY;;IAEpD,IAAI,OAAO,KAAK,CAAC,oBAAoB,OAAO,KAAK,CAAC,kBAAkB;QAAE,OAAO;IAAY;;IACzF,IAAI,OAAO,KAAK,CAAC,qBAAqB,OAAO,KAAK,CAAC,qBAAqB,OAAO,KAAK,CAAC,mBAAmB;QAAE,OAAO;IAAM;;IAEvH,IAAI,OAAO,KAAK,CAAC,gBAAgB;QAC/B,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT;;IAGA,4BAA4B;IAC5B,OAAO,IAAI;IACX,OAAO;AACT;;AAGO,MAAM,SAAS;IACpB,MAAM;IAEN,YAAY;QACV,OAAO;YACL,UAAU;QACZ;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QACnC,IAAI,UAAU,YAAY,UAAU,YAAW;YAC7C,MAAM,QAAQ,GAAG;QACnB;QACA,OAAO;IACT;IAEA,cAAc;QACZ,eAAe;YAAC,MAAM;QAAG;IAC3B;AACF", "ignoreList": [0], "debugId": null}}]}