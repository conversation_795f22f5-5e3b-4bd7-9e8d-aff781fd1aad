{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/sparql.js"], "sourcesContent": ["var curPunc;\n\nfunction wordRegexp(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n}\nvar ops = wordRegexp([\"str\", \"lang\", \"langmatches\", \"datatype\", \"bound\", \"sameterm\", \"isiri\", \"isuri\",\n                      \"iri\", \"uri\", \"bnode\", \"count\", \"sum\", \"min\", \"max\", \"avg\", \"sample\",\n                      \"group_concat\", \"rand\", \"abs\", \"ceil\", \"floor\", \"round\", \"concat\", \"substr\", \"strlen\",\n                      \"replace\", \"ucase\", \"lcase\", \"encode_for_uri\", \"contains\", \"strstarts\", \"strends\",\n                      \"strbefore\", \"strafter\", \"year\", \"month\", \"day\", \"hours\", \"minutes\", \"seconds\",\n                      \"timezone\", \"tz\", \"now\", \"uuid\", \"struuid\", \"md5\", \"sha1\", \"sha256\", \"sha384\",\n                      \"sha512\", \"coalesce\", \"if\", \"strlang\", \"strdt\", \"isnumeric\", \"regex\", \"exists\",\n                      \"isblank\", \"isliteral\", \"a\", \"bind\"]);\nvar keywords = wordRegexp([\"base\", \"prefix\", \"select\", \"distinct\", \"reduced\", \"construct\", \"describe\",\n                           \"ask\", \"from\", \"named\", \"where\", \"order\", \"limit\", \"offset\", \"filter\", \"optional\",\n                           \"graph\", \"by\", \"asc\", \"desc\", \"as\", \"having\", \"undef\", \"values\", \"group\",\n                           \"minus\", \"in\", \"not\", \"service\", \"silent\", \"using\", \"insert\", \"delete\", \"union\",\n                           \"true\", \"false\", \"with\",\n                           \"data\", \"copy\", \"to\", \"move\", \"add\", \"create\", \"drop\", \"clear\", \"load\", \"into\"]);\nvar operatorChars = /[*+\\-<>=&|\\^\\/!\\?]/;\nvar PN_CHARS = \"[A-Za-z_\\\\-0-9]\";\nvar PREFIX_START = new RegExp(\"[A-Za-z]\");\nvar PREFIX_REMAINDER = new RegExp(\"((\" + PN_CHARS + \"|\\\\.)*(\" + PN_CHARS + \"))?:\");\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  curPunc = null;\n  if (ch == \"$\" || ch == \"?\") {\n    if(ch == \"?\" && stream.match(/\\s/, false)){\n      return \"operator\";\n    }\n    stream.match(/^[A-Za-z0-9_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][A-Za-z0-9_\\u00B7\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u203F-\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]*/);\n    return \"variableName.local\";\n  }\n  else if (ch == \"<\" && !stream.match(/^[\\s\\u00a0=]/, false)) {\n    stream.match(/^[^\\s\\u00a0>]*>?/);\n    return \"atom\";\n  }\n  else if (ch == \"\\\"\" || ch == \"'\") {\n    state.tokenize = tokenLiteral(ch);\n    return state.tokenize(stream, state);\n  }\n  else if (/[{}\\(\\),\\.;\\[\\]]/.test(ch)) {\n    curPunc = ch;\n    return \"bracket\";\n  }\n  else if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  else if (operatorChars.test(ch)) {\n    return \"operator\";\n  }\n  else if (ch == \":\") {\n    eatPnLocal(stream);\n    return \"atom\";\n  }\n  else if (ch == \"@\") {\n    stream.eatWhile(/[a-z\\d\\-]/i);\n    return \"meta\";\n  }\n  else if (PREFIX_START.test(ch) && stream.match(PREFIX_REMAINDER)) {\n    eatPnLocal(stream);\n    return \"atom\";\n  }\n  stream.eatWhile(/[_\\w\\d]/);\n  var word = stream.current();\n  if (ops.test(word))\n    return \"builtin\";\n  else if (keywords.test(word))\n    return \"keyword\";\n  else\n    return \"variable\";\n}\n\nfunction eatPnLocal(stream) {\n  stream.match(/(\\.(?=[\\w_\\-\\\\%])|[:\\w_-]|\\\\[-\\\\_~.!$&'()*+,;=/?#@%]|%[a-f\\d][a-f\\d])+/i);\n}\n\nfunction tokenLiteral(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    return \"string\";\n  };\n}\n\nfunction pushContext(state, type, col) {\n  state.context = {prev: state.context, indent: state.indent, col: col, type: type};\n}\nfunction popContext(state) {\n  state.indent = state.context.indent;\n  state.context = state.context.prev;\n}\n\nexport const sparql = {\n  name: \"sparql\",\n\n  startState: function() {\n    return {tokenize: tokenBase,\n            context: null,\n            indent: 0,\n            col: 0};\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if (state.context && state.context.align == null) state.context.align = false;\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n\n    if (style != \"comment\" && state.context && state.context.align == null && state.context.type != \"pattern\") {\n      state.context.align = true;\n    }\n\n    if (curPunc == \"(\") pushContext(state, \")\", stream.column());\n    else if (curPunc == \"[\") pushContext(state, \"]\", stream.column());\n    else if (curPunc == \"{\") pushContext(state, \"}\", stream.column());\n    else if (/[\\]\\}\\)]/.test(curPunc)) {\n      while (state.context && state.context.type == \"pattern\") popContext(state);\n      if (state.context && curPunc == state.context.type) {\n        popContext(state);\n        if (curPunc == \"}\" && state.context && state.context.type == \"pattern\")\n          popContext(state);\n      }\n    }\n    else if (curPunc == \".\" && state.context && state.context.type == \"pattern\") popContext(state);\n    else if (/atom|string|variable/.test(style) && state.context) {\n      if (/[\\}\\]]/.test(state.context.type))\n        pushContext(state, \"pattern\", stream.column());\n      else if (state.context.type == \"pattern\" && !state.context.align) {\n        state.context.align = true;\n        state.context.col = stream.column();\n      }\n    }\n\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var firstChar = textAfter && textAfter.charAt(0);\n    var context = state.context;\n    if (/[\\]\\}]/.test(firstChar))\n      while (context && context.type == \"pattern\") context = context.prev;\n\n    var closing = context && firstChar == context.type;\n    if (!context)\n      return 0;\n    else if (context.type == \"pattern\")\n      return context.col;\n    else if (context.align)\n      return context.col + (closing ? 0 : 1);\n    else\n      return context.indent + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n\n"], "names": [], "mappings": ";;;AAAA,IAAI;AAEJ,SAAS,WAAW,KAAK;IACvB,OAAO,IAAI,OAAO,SAAS,MAAM,IAAI,CAAC,OAAO,MAAM;AACrD;AACA,IAAI,MAAM,WAAW;IAAC;IAAO;IAAQ;IAAe;IAAY;IAAS;IAAY;IAAS;IACxE;IAAO;IAAO;IAAS;IAAS;IAAO;IAAO;IAAO;IAAO;IAC5D;IAAgB;IAAQ;IAAO;IAAQ;IAAS;IAAS;IAAU;IAAU;IAC7E;IAAW;IAAS;IAAS;IAAkB;IAAY;IAAa;IACxE;IAAa;IAAY;IAAQ;IAAS;IAAO;IAAS;IAAW;IACrE;IAAY;IAAM;IAAO;IAAQ;IAAW;IAAO;IAAQ;IAAU;IACrE;IAAU;IAAY;IAAM;IAAW;IAAS;IAAa;IAAS;IACtE;IAAW;IAAa;IAAK;CAAO;AAC1D,IAAI,WAAW,WAAW;IAAC;IAAQ;IAAU;IAAU;IAAY;IAAW;IAAa;IAChE;IAAO;IAAQ;IAAS;IAAS;IAAS;IAAS;IAAU;IAAU;IACvE;IAAS;IAAM;IAAO;IAAQ;IAAM;IAAU;IAAS;IAAU;IACjE;IAAS;IAAM;IAAO;IAAW;IAAU;IAAS;IAAU;IAAU;IACxE;IAAQ;IAAS;IACjB;IAAQ;IAAQ;IAAM;IAAQ;IAAO;IAAU;IAAQ;IAAS;IAAQ;CAAO;AAC1G,IAAI,gBAAgB;AACpB,IAAI,WAAW;AACf,IAAI,eAAe,IAAI,OAAO;AAC9B,IAAI,mBAAmB,IAAI,OAAO,OAAO,WAAW,YAAY,WAAW;AAE3E,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IACpB,UAAU;IACV,IAAI,MAAM,OAAO,MAAM,KAAK;QAC1B,IAAG,MAAM,OAAO,OAAO,KAAK,CAAC,MAAM,QAAO;YACxC,OAAO;QACT;QACA,OAAO,KAAK,CAAC;QACb,OAAO;IACT,OACK,IAAI,MAAM,OAAO,CAAC,OAAO,KAAK,CAAC,gBAAgB,QAAQ;QAC1D,OAAO,KAAK,CAAC;QACb,OAAO;IACT,OACK,IAAI,MAAM,QAAQ,MAAM,KAAK;QAChC,MAAM,QAAQ,GAAG,aAAa;QAC9B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC,OACK,IAAI,mBAAmB,IAAI,CAAC,KAAK;QACpC,UAAU;QACV,OAAO;IACT,OACK,IAAI,MAAM,KAAK;QAClB,OAAO,SAAS;QAChB,OAAO;IACT,OACK,IAAI,cAAc,IAAI,CAAC,KAAK;QAC/B,OAAO;IACT,OACK,IAAI,MAAM,KAAK;QAClB,WAAW;QACX,OAAO;IACT,OACK,IAAI,MAAM,KAAK;QAClB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OACK,IAAI,aAAa,IAAI,CAAC,OAAO,OAAO,KAAK,CAAC,mBAAmB;QAChE,WAAW;QACX,OAAO;IACT;IACA,OAAO,QAAQ,CAAC;IAChB,IAAI,OAAO,OAAO,OAAO;IACzB,IAAI,IAAI,IAAI,CAAC,OACX,OAAO;SACJ,IAAI,SAAS,IAAI,CAAC,OACrB,OAAO;SAEP,OAAO;AACX;AAEA,SAAS,WAAW,MAAM;IACxB,OAAO,KAAK,CAAC;AACf;AAEA,SAAS,aAAa,KAAK;IACzB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO;QACrB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;YACnC,IAAI,MAAM,SAAS,CAAC,SAAS;gBAC3B,MAAM,QAAQ,GAAG;gBACjB;YACF;YACA,UAAU,CAAC,WAAW,MAAM;QAC9B;QACA,OAAO;IACT;AACF;AAEA,SAAS,YAAY,KAAK,EAAE,IAAI,EAAE,GAAG;IACnC,MAAM,OAAO,GAAG;QAAC,MAAM,MAAM,OAAO;QAAE,QAAQ,MAAM,MAAM;QAAE,KAAK;QAAK,MAAM;IAAI;AAClF;AACA,SAAS,WAAW,KAAK;IACvB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM;IACnC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;AACpC;AAEO,MAAM,SAAS;IACpB,MAAM;IAEN,YAAY;QACV,OAAO;YAAC,UAAU;YACV,SAAS;YACT,QAAQ;YACR,KAAK;QAAC;IAChB;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,MAAM,MAAM,OAAO,CAAC,KAAK,GAAG;YACxE,MAAM,MAAM,GAAG,OAAO,WAAW;QACnC;QACA,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QAEnC,IAAI,SAAS,aAAa,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,QAAQ,MAAM,OAAO,CAAC,IAAI,IAAI,WAAW;YACzG,MAAM,OAAO,CAAC,KAAK,GAAG;QACxB;QAEA,IAAI,WAAW,KAAK,YAAY,OAAO,KAAK,OAAO,MAAM;aACpD,IAAI,WAAW,KAAK,YAAY,OAAO,KAAK,OAAO,MAAM;aACzD,IAAI,WAAW,KAAK,YAAY,OAAO,KAAK,OAAO,MAAM;aACzD,IAAI,WAAW,IAAI,CAAC,UAAU;YACjC,MAAO,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,UAAW,WAAW;YACpE,IAAI,MAAM,OAAO,IAAI,WAAW,MAAM,OAAO,CAAC,IAAI,EAAE;gBAClD,WAAW;gBACX,IAAI,WAAW,OAAO,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,WAC3D,WAAW;YACf;QACF,OACK,IAAI,WAAW,OAAO,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,WAAW,WAAW;aACnF,IAAI,uBAAuB,IAAI,CAAC,UAAU,MAAM,OAAO,EAAE;YAC5D,IAAI,SAAS,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,GAClC,YAAY,OAAO,WAAW,OAAO,MAAM;iBACxC,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,aAAa,CAAC,MAAM,OAAO,CAAC,KAAK,EAAE;gBAChE,MAAM,OAAO,CAAC,KAAK,GAAG;gBACtB,MAAM,OAAO,CAAC,GAAG,GAAG,OAAO,MAAM;YACnC;QACF;QAEA,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,YAAY,aAAa,UAAU,MAAM,CAAC;QAC9C,IAAI,UAAU,MAAM,OAAO;QAC3B,IAAI,SAAS,IAAI,CAAC,YAChB,MAAO,WAAW,QAAQ,IAAI,IAAI,UAAW,UAAU,QAAQ,IAAI;QAErE,IAAI,UAAU,WAAW,aAAa,QAAQ,IAAI;QAClD,IAAI,CAAC,SACH,OAAO;aACJ,IAAI,QAAQ,IAAI,IAAI,WACvB,OAAO,QAAQ,GAAG;aACf,IAAI,QAAQ,KAAK,EACpB,OAAO,QAAQ,GAAG,GAAG,CAAC,UAAU,IAAI,CAAC;aAErC,OAAO,QAAQ,MAAM,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI;IAClD;IAEA,cAAc;QACZ,eAAe;YAAC,MAAM;QAAG;IAC3B;AACF", "ignoreList": [0], "debugId": null}}]}