{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/nginx.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar keywords = words(\n  /* ngxDirectiveControl */ \"break return rewrite set\" +\n    /* ngxDirective */ \" accept_mutex accept_mutex_delay access_log add_after_body add_before_body add_header addition_types aio alias allow ancient_browser ancient_browser_value auth_basic auth_basic_user_file auth_http auth_http_header auth_http_timeout autoindex autoindex_exact_size autoindex_localtime charset charset_types client_body_buffer_size client_body_in_file_only client_body_in_single_buffer client_body_temp_path client_body_timeout client_header_buffer_size client_header_timeout client_max_body_size connection_pool_size create_full_put_path daemon dav_access dav_methods debug_connection debug_points default_type degradation degrade deny devpoll_changes devpoll_events directio directio_alignment empty_gif env epoll_events error_log eventport_events expires fastcgi_bind fastcgi_buffer_size fastcgi_buffers fastcgi_busy_buffers_size fastcgi_cache fastcgi_cache_key fastcgi_cache_methods fastcgi_cache_min_uses fastcgi_cache_path fastcgi_cache_use_stale fastcgi_cache_valid fastcgi_catch_stderr fastcgi_connect_timeout fastcgi_hide_header fastcgi_ignore_client_abort fastcgi_ignore_headers fastcgi_index fastcgi_intercept_errors fastcgi_max_temp_file_size fastcgi_next_upstream fastcgi_param fastcgi_pass_header fastcgi_pass_request_body fastcgi_pass_request_headers fastcgi_read_timeout fastcgi_send_lowat fastcgi_send_timeout fastcgi_split_path_info fastcgi_store fastcgi_store_access fastcgi_temp_file_write_size fastcgi_temp_path fastcgi_upstream_fail_timeout fastcgi_upstream_max_fails flv geoip_city geoip_country google_perftools_profiles gzip gzip_buffers gzip_comp_level gzip_disable gzip_hash gzip_http_version gzip_min_length gzip_no_buffer gzip_proxied gzip_static gzip_types gzip_vary gzip_window if_modified_since ignore_invalid_headers image_filter image_filter_buffer image_filter_jpeg_quality image_filter_transparency imap_auth imap_capabilities imap_client_buffer index ip_hash keepalive_requests keepalive_timeout kqueue_changes kqueue_events large_client_header_buffers limit_conn limit_conn_log_level limit_rate limit_rate_after limit_req limit_req_log_level limit_req_zone limit_zone lingering_time lingering_timeout lock_file log_format log_not_found log_subrequest map_hash_bucket_size map_hash_max_size master_process memcached_bind memcached_buffer_size memcached_connect_timeout memcached_next_upstream memcached_read_timeout memcached_send_timeout memcached_upstream_fail_timeout memcached_upstream_max_fails merge_slashes min_delete_depth modern_browser modern_browser_value msie_padding msie_refresh multi_accept open_file_cache open_file_cache_errors open_file_cache_events open_file_cache_min_uses open_file_cache_valid open_log_file_cache output_buffers override_charset perl perl_modules perl_require perl_set pid pop3_auth pop3_capabilities port_in_redirect postpone_gzipping postpone_output protocol proxy proxy_bind proxy_buffer proxy_buffer_size proxy_buffering proxy_buffers proxy_busy_buffers_size proxy_cache proxy_cache_key proxy_cache_methods proxy_cache_min_uses proxy_cache_path proxy_cache_use_stale proxy_cache_valid proxy_connect_timeout proxy_headers_hash_bucket_size proxy_headers_hash_max_size proxy_hide_header proxy_ignore_client_abort proxy_ignore_headers proxy_intercept_errors proxy_max_temp_file_size proxy_method proxy_next_upstream proxy_pass_error_message proxy_pass_header proxy_pass_request_body proxy_pass_request_headers proxy_read_timeout proxy_redirect proxy_send_lowat proxy_send_timeout proxy_set_body proxy_set_header proxy_ssl_session_reuse proxy_store proxy_store_access proxy_temp_file_write_size proxy_temp_path proxy_timeout proxy_upstream_fail_timeout proxy_upstream_max_fails random_index read_ahead real_ip_header recursive_error_pages request_pool_size reset_timedout_connection resolver resolver_timeout rewrite_log rtsig_overflow_events rtsig_overflow_test rtsig_overflow_threshold rtsig_signo satisfy secure_link_secret send_lowat send_timeout sendfile sendfile_max_chunk server_name_in_redirect server_names_hash_bucket_size server_names_hash_max_size server_tokens set_real_ip_from smtp_auth smtp_capabilities smtp_client_buffer smtp_greeting_delay so_keepalive source_charset ssi ssi_ignore_recycled_buffers ssi_min_file_chunk ssi_silent_errors ssi_types ssi_value_length ssl ssl_certificate ssl_certificate_key ssl_ciphers ssl_client_certificate ssl_crl ssl_dhparam ssl_engine ssl_prefer_server_ciphers ssl_protocols ssl_session_cache ssl_session_timeout ssl_verify_client ssl_verify_depth starttls stub_status sub_filter sub_filter_once sub_filter_types tcp_nodelay tcp_nopush thread_stack_size timeout timer_resolution types_hash_bucket_size types_hash_max_size underscores_in_headers uninitialized_variable_warn use user userid userid_domain userid_expires userid_mark userid_name userid_p3p userid_path userid_service valid_referers variables_hash_bucket_size variables_hash_max_size worker_connections worker_cpu_affinity worker_priority worker_processes worker_rlimit_core worker_rlimit_nofile worker_rlimit_sigpending worker_threads working_directory xclient xml_entities xslt_stylesheet xslt_typesdrew@li229-23\"\n);\n\nvar keywords_block = words(\n  /* ngxDirectiveBlock */ \"http mail events server types location upstream charset_map limit_except if geo map\"\n);\n\nvar keywords_important = words(\n  /* ngxDirectiveImportant */ \"include root server server_name listen internal proxy_pass memcached_pass fastcgi_pass try_files\"\n);\n\nvar type;\nfunction ret(style, tp) {type = tp; return style;}\n\nfunction tokenBase(stream, state) {\n\n\n  stream.eatWhile(/[\\w\\$_]/);\n\n  var cur = stream.current();\n\n\n  if (keywords.propertyIsEnumerable(cur)) {\n    return \"keyword\";\n  }\n  else if (keywords_block.propertyIsEnumerable(cur)) {\n    return \"controlKeyword\";\n  }\n  else if (keywords_important.propertyIsEnumerable(cur)) {\n    return \"controlKeyword\";\n  }\n  /**/\n\n  var ch = stream.next();\n  if (ch == \"@\") {stream.eatWhile(/[\\w\\\\\\-]/); return ret(\"meta\", stream.current());}\n  else if (ch == \"/\" && stream.eat(\"*\")) {\n    state.tokenize = tokenCComment;\n    return tokenCComment(stream, state);\n  }\n  else if (ch == \"<\" && stream.eat(\"!\")) {\n    state.tokenize = tokenSGMLComment;\n    return tokenSGMLComment(stream, state);\n  }\n  else if (ch == \"=\") ret(null, \"compare\");\n  else if ((ch == \"~\" || ch == \"|\") && stream.eat(\"=\")) return ret(null, \"compare\");\n  else if (ch == \"\\\"\" || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  else if (ch == \"#\") {\n    stream.skipToEnd();\n    return ret(\"comment\", \"comment\");\n  }\n  else if (ch == \"!\") {\n    stream.match(/^\\s*\\w*/);\n    return ret(\"keyword\", \"important\");\n  }\n  else if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w.%]/);\n    return ret(\"number\", \"unit\");\n  }\n  else if (/[,.+>*\\/]/.test(ch)) {\n    return ret(null, \"select-op\");\n  }\n  else if (/[;{}:\\[\\]]/.test(ch)) {\n    return ret(null, ch);\n  }\n  else {\n    stream.eatWhile(/[\\w\\\\\\-]/);\n    return ret(\"variable\", \"variable\");\n  }\n}\n\nfunction tokenCComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return ret(\"comment\", \"comment\");\n}\n\nfunction tokenSGMLComment(stream, state) {\n  var dashes = 0, ch;\n  while ((ch = stream.next()) != null) {\n    if (dashes >= 2 && ch == \">\") {\n      state.tokenize = tokenBase;\n      break;\n    }\n    dashes = (ch == \"-\") ? dashes + 1 : 0;\n  }\n  return ret(\"comment\", \"comment\");\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped)\n        break;\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    if (!escaped) state.tokenize = tokenBase;\n    return ret(\"string\", \"string\");\n  };\n}\n\nexport const nginx = {\n  name: \"nginx\",\n  startState: function() {\n    return {tokenize: tokenBase,\n            baseIndent: 0,\n            stack: []};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    type = null;\n    var style = state.tokenize(stream, state);\n\n    var context = state.stack[state.stack.length-1];\n    if (type == \"hash\" && context == \"rule\") style = \"atom\";\n    else if (style == \"variable\") {\n      if (context == \"rule\") style = \"number\";\n      else if (!context || context == \"@media{\") style = \"tag\";\n    }\n\n    if (context == \"rule\" && /^[\\{\\};]$/.test(type))\n      state.stack.pop();\n    if (type == \"{\") {\n      if (context == \"@media\") state.stack[state.stack.length-1] = \"@media{\";\n      else state.stack.push(\"{\");\n    }\n    else if (type == \"}\") state.stack.pop();\n    else if (type == \"@media\") state.stack.push(\"@media\");\n    else if (context == \"{\" && type != \"comment\") state.stack.push(\"rule\");\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var n = state.stack.length;\n    if (/^\\}/.test(textAfter))\n      n -= state.stack[state.stack.length-1] == \"rule\" ? 2 : 1;\n    return state.baseIndent + n * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*\\}$/\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AAEA,IAAI,WAAW,MACb,uBAAuB,GAAG,6BACxB,gBAAgB,GAAG;AAGvB,IAAI,iBAAiB,MACnB,qBAAqB,GAAG;AAG1B,IAAI,qBAAqB,MACvB,yBAAyB,GAAG;AAG9B,IAAI;AACJ,SAAS,IAAI,KAAK,EAAE,EAAE;IAAG,OAAO;IAAI,OAAO;AAAM;AAEjD,SAAS,UAAU,MAAM,EAAE,KAAK;IAG9B,OAAO,QAAQ,CAAC;IAEhB,IAAI,MAAM,OAAO,OAAO;IAGxB,IAAI,SAAS,oBAAoB,CAAC,MAAM;QACtC,OAAO;IACT,OACK,IAAI,eAAe,oBAAoB,CAAC,MAAM;QACjD,OAAO;IACT,OACK,IAAI,mBAAmB,oBAAoB,CAAC,MAAM;QACrD,OAAO;IACT;IACA,EAAE,GAEF,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,KAAK;QAAC,OAAO,QAAQ,CAAC;QAAa,OAAO,IAAI,QAAQ,OAAO,OAAO;IAAI,OAC7E,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;QACrC,MAAM,QAAQ,GAAG;QACjB,OAAO,cAAc,QAAQ;IAC/B,OACK,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;QACrC,MAAM,QAAQ,GAAG;QACjB,OAAO,iBAAiB,QAAQ;IAClC,OACK,IAAI,MAAM,KAAK,IAAI,MAAM;SACzB,IAAI,CAAC,MAAM,OAAO,MAAM,GAAG,KAAK,OAAO,GAAG,CAAC,MAAM,OAAO,IAAI,MAAM;SAClE,IAAI,MAAM,QAAQ,MAAM,KAAK;QAChC,MAAM,QAAQ,GAAG,YAAY;QAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC,OACK,IAAI,MAAM,KAAK;QAClB,OAAO,SAAS;QAChB,OAAO,IAAI,WAAW;IACxB,OACK,IAAI,MAAM,KAAK;QAClB,OAAO,KAAK,CAAC;QACb,OAAO,IAAI,WAAW;IACxB,OACK,IAAI,KAAK,IAAI,CAAC,KAAK;QACtB,OAAO,QAAQ,CAAC;QAChB,OAAO,IAAI,UAAU;IACvB,OACK,IAAI,YAAY,IAAI,CAAC,KAAK;QAC7B,OAAO,IAAI,MAAM;IACnB,OACK,IAAI,aAAa,IAAI,CAAC,KAAK;QAC9B,OAAO,IAAI,MAAM;IACnB,OACK;QACH,OAAO,QAAQ,CAAC;QAChB,OAAO,IAAI,YAAY;IACzB;AACF;AAEA,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,IAAI,WAAW,OAAO;IACtB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;QACnC,IAAI,YAAY,MAAM,KAAK;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO,IAAI,WAAW;AACxB;AAEA,SAAS,iBAAiB,MAAM,EAAE,KAAK;IACrC,IAAI,SAAS,GAAG;IAChB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;QACnC,IAAI,UAAU,KAAK,MAAM,KAAK;YAC5B,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,SAAS,AAAC,MAAM,MAAO,SAAS,IAAI;IACtC;IACA,OAAO,IAAI,WAAW;AACxB;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO;QACrB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;YACnC,IAAI,MAAM,SAAS,CAAC,SAClB;YACF,UAAU,CAAC,WAAW,MAAM;QAC9B;QACA,IAAI,CAAC,SAAS,MAAM,QAAQ,GAAG;QAC/B,OAAO,IAAI,UAAU;IACvB;AACF;AAEO,MAAM,QAAQ;IACnB,MAAM;IACN,YAAY;QACV,OAAO;YAAC,UAAU;YACV,YAAY;YACZ,OAAO,EAAE;QAAA;IACnB;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,OAAO;QACP,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QAEnC,IAAI,UAAU,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAC,EAAE;QAC/C,IAAI,QAAQ,UAAU,WAAW,QAAQ,QAAQ;aAC5C,IAAI,SAAS,YAAY;YAC5B,IAAI,WAAW,QAAQ,QAAQ;iBAC1B,IAAI,CAAC,WAAW,WAAW,WAAW,QAAQ;QACrD;QAEA,IAAI,WAAW,UAAU,YAAY,IAAI,CAAC,OACxC,MAAM,KAAK,CAAC,GAAG;QACjB,IAAI,QAAQ,KAAK;YACf,IAAI,WAAW,UAAU,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAC,EAAE,GAAG;iBACxD,MAAM,KAAK,CAAC,IAAI,CAAC;QACxB,OACK,IAAI,QAAQ,KAAK,MAAM,KAAK,CAAC,GAAG;aAChC,IAAI,QAAQ,UAAU,MAAM,KAAK,CAAC,IAAI,CAAC;aACvC,IAAI,WAAW,OAAO,QAAQ,WAAW,MAAM,KAAK,CAAC,IAAI,CAAC;QAC/D,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,IAAI,MAAM,KAAK,CAAC,MAAM;QAC1B,IAAI,MAAM,IAAI,CAAC,YACb,KAAK,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAC,EAAE,IAAI,SAAS,IAAI;QACzD,OAAO,MAAM,UAAU,GAAG,IAAI,GAAG,IAAI;IACvC;IAEA,cAAc;QACZ,eAAe;IACjB;AACF", "ignoreList": [0], "debugId": null}}]}