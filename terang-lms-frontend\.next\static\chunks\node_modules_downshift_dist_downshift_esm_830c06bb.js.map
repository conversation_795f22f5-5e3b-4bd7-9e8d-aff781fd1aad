{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/downshift/dist/downshift.esm.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from '@babel/runtime/helpers/esm/objectWithoutPropertiesLoose';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport _assertThisInitialized from '@babel/runtime/helpers/esm/assertThisInitialized';\nimport _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport PropTypes from 'prop-types';\nimport { cloneElement, Component, useRef, useEffect, useCallback, useLayoutEffect, useReducer, useMemo } from 'react';\nimport { isForwardRef } from 'react-is';\nimport compute from 'compute-scroll-into-view';\nimport { __assign } from 'tslib';\n\nvar idCounter = 0;\n\n/**\n * Accepts a parameter and returns it if it's a function\n * or a noop function if it's not. This allows us to\n * accept a callback, but not worry about it if it's not\n * passed.\n * @param {Function} cb the callback\n * @return {Function} a function\n */\nfunction cbToCb(cb) {\n  return typeof cb === 'function' ? cb : noop;\n}\nfunction noop() {}\n\n/**\n * Scroll node into view if necessary\n * @param {HTMLElement} node the element that should scroll into view\n * @param {HTMLElement} menuNode the menu element of the component\n */\nfunction scrollIntoView(node, menuNode) {\n  if (!node) {\n    return;\n  }\n  var actions = compute(node, {\n    boundary: menuNode,\n    block: 'nearest',\n    scrollMode: 'if-needed'\n  });\n  actions.forEach(function (_ref) {\n    var el = _ref.el,\n      top = _ref.top,\n      left = _ref.left;\n    el.scrollTop = top;\n    el.scrollLeft = left;\n  });\n}\n\n/**\n * @param {HTMLElement} parent the parent node\n * @param {HTMLElement} child the child node\n * @param {Window} environment The window context where downshift renders.\n * @return {Boolean} whether the parent is the child or the child is in the parent\n */\nfunction isOrContainsNode(parent, child, environment) {\n  var result = parent === child || child instanceof environment.Node && parent.contains && parent.contains(child);\n  return result;\n}\n\n/**\n * Simple debounce implementation. Will call the given\n * function once after the time given has passed since\n * it was last called.\n * @param {Function} fn the function to call after the time\n * @param {Number} time the time to wait\n * @return {Function} the debounced function\n */\nfunction debounce(fn, time) {\n  var timeoutId;\n  function cancel() {\n    if (timeoutId) {\n      clearTimeout(timeoutId);\n    }\n  }\n  function wrapper() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    cancel();\n    timeoutId = setTimeout(function () {\n      timeoutId = null;\n      fn.apply(void 0, args);\n    }, time);\n  }\n  wrapper.cancel = cancel;\n  return wrapper;\n}\n\n/**\n * This is intended to be used to compose event handlers.\n * They are executed in order until one of them sets\n * `event.preventDownshiftDefault = true`.\n * @param {...Function} fns the event handler functions\n * @return {Function} the event handler to add to an element\n */\nfunction callAllEventHandlers() {\n  for (var _len2 = arguments.length, fns = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    fns[_key2] = arguments[_key2];\n  }\n  return function (event) {\n    for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n      args[_key3 - 1] = arguments[_key3];\n    }\n    return fns.some(function (fn) {\n      if (fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n      return event.preventDownshiftDefault || event.hasOwnProperty('nativeEvent') && event.nativeEvent.preventDownshiftDefault;\n    });\n  };\n}\nfunction handleRefs() {\n  for (var _len4 = arguments.length, refs = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n    refs[_key4] = arguments[_key4];\n  }\n  return function (node) {\n    refs.forEach(function (ref) {\n      if (typeof ref === 'function') {\n        ref(node);\n      } else if (ref) {\n        ref.current = node;\n      }\n    });\n  };\n}\n\n/**\n * This generates a unique ID for an instance of Downshift\n * @return {String} the unique ID\n */\nfunction generateId() {\n  return String(idCounter++);\n}\n\n/**\n * Resets idCounter to 0. Used for SSR.\n */\nfunction resetIdCounter() {\n  idCounter = 0;\n}\n\n/**\n * Default implementation for status message. Only added when menu is open.\n * Will specify if there are results in the list, and if so, how many,\n * and what keys are relevant.\n *\n * @param {Object} param the downshift state and other relevant properties\n * @return {String} the a11y status message\n */\nfunction getA11yStatusMessage$1(_ref2) {\n  var isOpen = _ref2.isOpen,\n    resultCount = _ref2.resultCount,\n    previousResultCount = _ref2.previousResultCount;\n  if (!isOpen) {\n    return '';\n  }\n  if (!resultCount) {\n    return 'No results are available.';\n  }\n  if (resultCount !== previousResultCount) {\n    return resultCount + \" result\" + (resultCount === 1 ? ' is' : 's are') + \" available, use up and down arrow keys to navigate. Press Enter key to select.\";\n  }\n  return '';\n}\n\n/**\n * Takes an argument and if it's an array, returns the first item in the array\n * otherwise returns the argument\n * @param {*} arg the maybe-array\n * @param {*} defaultValue the value if arg is falsey not defined\n * @return {*} the arg or it's first item\n */\nfunction unwrapArray(arg, defaultValue) {\n  arg = Array.isArray(arg) ? /* istanbul ignore next (preact) */arg[0] : arg;\n  if (!arg && defaultValue) {\n    return defaultValue;\n  } else {\n    return arg;\n  }\n}\n\n/**\n * @param {Object} element (P)react element\n * @return {Boolean} whether it's a DOM element\n */\nfunction isDOMElement(element) {\n\n  // then we assume this is react\n  return typeof element.type === 'string';\n}\n\n/**\n * @param {Object} element (P)react element\n * @return {Object} the props\n */\nfunction getElementProps(element) {\n  return element.props;\n}\n\n/**\n * Throws a helpful error message for required properties. Useful\n * to be used as a default in destructuring or object params.\n * @param {String} fnName the function name\n * @param {String} propName the prop name\n */\nfunction requiredProp(fnName, propName) {\n  // eslint-disable-next-line no-console\n  console.error(\"The property \\\"\" + propName + \"\\\" is required in \\\"\" + fnName + \"\\\"\");\n}\nvar stateKeys = ['highlightedIndex', 'inputValue', 'isOpen', 'selectedItem', 'type'];\n/**\n * @param {Object} state the state object\n * @return {Object} state that is relevant to downshift\n */\nfunction pickState(state) {\n  if (state === void 0) {\n    state = {};\n  }\n  var result = {};\n  stateKeys.forEach(function (k) {\n    if (state.hasOwnProperty(k)) {\n      result[k] = state[k];\n    }\n  });\n  return result;\n}\n\n/**\n * This will perform a shallow merge of the given state object\n * with the state coming from props\n * (for the controlled component scenario)\n * This is used in state updater functions so they're referencing\n * the right state regardless of where it comes from.\n *\n * @param {Object} state The state of the component/hook.\n * @param {Object} props The props that may contain controlled values.\n * @returns {Object} The merged controlled state.\n */\nfunction getState(state, props) {\n  return Object.keys(state).reduce(function (prevState, key) {\n    prevState[key] = isControlledProp(props, key) ? props[key] : state[key];\n    return prevState;\n  }, {});\n}\n\n/**\n * This determines whether a prop is a \"controlled prop\" meaning it is\n * state which is controlled by the outside of this component rather\n * than within this component.\n *\n * @param {Object} props The props that may contain controlled values.\n * @param {String} key the key to check\n * @return {Boolean} whether it is a controlled controlled prop\n */\nfunction isControlledProp(props, key) {\n  return props[key] !== undefined;\n}\n\n/**\n * Normalizes the 'key' property of a KeyboardEvent in IE/Edge\n * @param {Object} event a keyboardEvent object\n * @return {String} keyboard key\n */\nfunction normalizeArrowKey(event) {\n  var key = event.key,\n    keyCode = event.keyCode;\n  /* istanbul ignore next (ie) */\n  if (keyCode >= 37 && keyCode <= 40 && key.indexOf('Arrow') !== 0) {\n    return \"Arrow\" + key;\n  }\n  return key;\n}\n\n/**\n * Simple check if the value passed is object literal\n * @param {*} obj any things\n * @return {Boolean} whether it's object literal\n */\nfunction isPlainObject(obj) {\n  return Object.prototype.toString.call(obj) === '[object Object]';\n}\n\n/**\n * Returns the new index in the list, in a circular way. If next value is out of bonds from the total,\n * it will wrap to either 0 or itemCount - 1.\n *\n * @param {number} moveAmount Number of positions to move. Negative to move backwards, positive forwards.\n * @param {number} baseIndex The initial position to move from.\n * @param {number} itemCount The total number of items.\n * @param {Function} getItemNodeFromIndex Used to check if item is disabled.\n * @param {boolean} circular Specify if navigation is circular. Default is true.\n * @returns {number} The new index after the move.\n */\nfunction getNextWrappingIndex(moveAmount, baseIndex, itemCount, getItemNodeFromIndex, circular) {\n  if (circular === void 0) {\n    circular = true;\n  }\n  if (itemCount === 0) {\n    return -1;\n  }\n  var itemsLastIndex = itemCount - 1;\n  if (typeof baseIndex !== 'number' || baseIndex < 0 || baseIndex >= itemCount) {\n    baseIndex = moveAmount > 0 ? -1 : itemsLastIndex + 1;\n  }\n  var newIndex = baseIndex + moveAmount;\n  if (newIndex < 0) {\n    newIndex = circular ? itemsLastIndex : 0;\n  } else if (newIndex > itemsLastIndex) {\n    newIndex = circular ? 0 : itemsLastIndex;\n  }\n  var nonDisabledNewIndex = getNextNonDisabledIndex(moveAmount, newIndex, itemCount, getItemNodeFromIndex, circular);\n  if (nonDisabledNewIndex === -1) {\n    return baseIndex >= itemCount ? -1 : baseIndex;\n  }\n  return nonDisabledNewIndex;\n}\n\n/**\n * Returns the next index in the list of an item that is not disabled.\n *\n * @param {number} moveAmount Number of positions to move. Negative to move backwards, positive forwards.\n * @param {number} baseIndex The initial position to move from.\n * @param {number} itemCount The total number of items.\n * @param {Function} getItemNodeFromIndex Used to check if item is disabled.\n * @param {boolean} circular Specify if navigation is circular. Default is true.\n * @returns {number} The new index. Returns baseIndex if item is not disabled. Returns next non-disabled item otherwise. If no non-disabled found it will return -1.\n */\nfunction getNextNonDisabledIndex(moveAmount, baseIndex, itemCount, getItemNodeFromIndex, circular) {\n  var currentElementNode = getItemNodeFromIndex(baseIndex);\n  if (!currentElementNode || !currentElementNode.hasAttribute('disabled')) {\n    return baseIndex;\n  }\n  if (moveAmount > 0) {\n    for (var index = baseIndex + 1; index < itemCount; index++) {\n      if (!getItemNodeFromIndex(index).hasAttribute('disabled')) {\n        return index;\n      }\n    }\n  } else {\n    for (var _index = baseIndex - 1; _index >= 0; _index--) {\n      if (!getItemNodeFromIndex(_index).hasAttribute('disabled')) {\n        return _index;\n      }\n    }\n  }\n  if (circular) {\n    return moveAmount > 0 ? getNextNonDisabledIndex(1, 0, itemCount, getItemNodeFromIndex, false) : getNextNonDisabledIndex(-1, itemCount - 1, itemCount, getItemNodeFromIndex, false);\n  }\n  return -1;\n}\n\n/**\n * Checks if event target is within the downshift elements.\n *\n * @param {EventTarget} target Target to check.\n * @param {HTMLElement[]} downshiftElements The elements that form downshift (list, toggle button etc).\n * @param {Window} environment The window context where downshift renders.\n * @param {boolean} checkActiveElement Whether to also check activeElement.\n *\n * @returns {boolean} Whether or not the target is within downshift elements.\n */\nfunction targetWithinDownshift(target, downshiftElements, environment, checkActiveElement) {\n  if (checkActiveElement === void 0) {\n    checkActiveElement = true;\n  }\n  return downshiftElements.some(function (contextNode) {\n    return contextNode && (isOrContainsNode(contextNode, target, environment) || checkActiveElement && isOrContainsNode(contextNode, environment.document.activeElement, environment));\n  });\n}\n\n// eslint-disable-next-line import/no-mutable-exports\nvar validateControlledUnchanged = noop;\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n  validateControlledUnchanged = function validateControlledUnchanged(state, prevProps, nextProps) {\n    var warningDescription = \"This prop should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled Downshift element for the lifetime of the component. More info: https://github.com/downshift-js/downshift#control-props\";\n    Object.keys(state).forEach(function (propKey) {\n      if (prevProps[propKey] !== undefined && nextProps[propKey] === undefined) {\n        // eslint-disable-next-line no-console\n        console.error(\"downshift: A component has changed the controlled prop \\\"\" + propKey + \"\\\" to be uncontrolled. \" + warningDescription);\n      } else if (prevProps[propKey] === undefined && nextProps[propKey] !== undefined) {\n        // eslint-disable-next-line no-console\n        console.error(\"downshift: A component has changed the uncontrolled prop \\\"\" + propKey + \"\\\" to be controlled. \" + warningDescription);\n      }\n    });\n  };\n}\n\nvar cleanupStatus = debounce(function (documentProp) {\n  getStatusDiv(documentProp).textContent = '';\n}, 500);\n\n/**\n * @param {String} status the status message\n * @param {Object} documentProp document passed by the user.\n */\nfunction setStatus(status, documentProp) {\n  var div = getStatusDiv(documentProp);\n  if (!status) {\n    return;\n  }\n  div.textContent = status;\n  cleanupStatus(documentProp);\n}\n\n/**\n * Get the status node or create it if it does not already exist.\n * @param {Object} documentProp document passed by the user.\n * @return {HTMLElement} the status node.\n */\nfunction getStatusDiv(documentProp) {\n  if (documentProp === void 0) {\n    documentProp = document;\n  }\n  var statusDiv = documentProp.getElementById('a11y-status-message');\n  if (statusDiv) {\n    return statusDiv;\n  }\n  statusDiv = documentProp.createElement('div');\n  statusDiv.setAttribute('id', 'a11y-status-message');\n  statusDiv.setAttribute('role', 'status');\n  statusDiv.setAttribute('aria-live', 'polite');\n  statusDiv.setAttribute('aria-relevant', 'additions text');\n  Object.assign(statusDiv.style, {\n    border: '0',\n    clip: 'rect(0 0 0 0)',\n    height: '1px',\n    margin: '-1px',\n    overflow: 'hidden',\n    padding: '0',\n    position: 'absolute',\n    width: '1px'\n  });\n  documentProp.body.appendChild(statusDiv);\n  return statusDiv;\n}\n\nvar unknown = process.env.NODE_ENV !== \"production\" ? '__autocomplete_unknown__' : 0;\nvar mouseUp = process.env.NODE_ENV !== \"production\" ? '__autocomplete_mouseup__' : 1;\nvar itemMouseEnter = process.env.NODE_ENV !== \"production\" ? '__autocomplete_item_mouseenter__' : 2;\nvar keyDownArrowUp = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_arrow_up__' : 3;\nvar keyDownArrowDown = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_arrow_down__' : 4;\nvar keyDownEscape = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_escape__' : 5;\nvar keyDownEnter = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_enter__' : 6;\nvar keyDownHome = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_home__' : 7;\nvar keyDownEnd = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_end__' : 8;\nvar clickItem = process.env.NODE_ENV !== \"production\" ? '__autocomplete_click_item__' : 9;\nvar blurInput = process.env.NODE_ENV !== \"production\" ? '__autocomplete_blur_input__' : 10;\nvar changeInput = process.env.NODE_ENV !== \"production\" ? '__autocomplete_change_input__' : 11;\nvar keyDownSpaceButton = process.env.NODE_ENV !== \"production\" ? '__autocomplete_keydown_space_button__' : 12;\nvar clickButton = process.env.NODE_ENV !== \"production\" ? '__autocomplete_click_button__' : 13;\nvar blurButton = process.env.NODE_ENV !== \"production\" ? '__autocomplete_blur_button__' : 14;\nvar controlledPropUpdatedSelectedItem = process.env.NODE_ENV !== \"production\" ? '__autocomplete_controlled_prop_updated_selected_item__' : 15;\nvar touchEnd = process.env.NODE_ENV !== \"production\" ? '__autocomplete_touchend__' : 16;\n\nvar stateChangeTypes$3 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  unknown: unknown,\n  mouseUp: mouseUp,\n  itemMouseEnter: itemMouseEnter,\n  keyDownArrowUp: keyDownArrowUp,\n  keyDownArrowDown: keyDownArrowDown,\n  keyDownEscape: keyDownEscape,\n  keyDownEnter: keyDownEnter,\n  keyDownHome: keyDownHome,\n  keyDownEnd: keyDownEnd,\n  clickItem: clickItem,\n  blurInput: blurInput,\n  changeInput: changeInput,\n  keyDownSpaceButton: keyDownSpaceButton,\n  clickButton: clickButton,\n  blurButton: blurButton,\n  controlledPropUpdatedSelectedItem: controlledPropUpdatedSelectedItem,\n  touchEnd: touchEnd\n});\n\nvar _excluded$4 = [\"refKey\", \"ref\"],\n  _excluded2$3 = [\"onClick\", \"onPress\", \"onKeyDown\", \"onKeyUp\", \"onBlur\"],\n  _excluded3$2 = [\"onKeyDown\", \"onBlur\", \"onChange\", \"onInput\", \"onChangeText\"],\n  _excluded4$1 = [\"refKey\", \"ref\"],\n  _excluded5 = [\"onMouseMove\", \"onMouseDown\", \"onClick\", \"onPress\", \"index\", \"item\"];\nvar Downshift = /*#__PURE__*/function () {\n  var Downshift = /*#__PURE__*/function (_Component) {\n    _inheritsLoose(Downshift, _Component);\n    function Downshift(_props) {\n      var _this;\n      _this = _Component.call(this, _props) || this;\n      // fancy destructuring + defaults + aliases\n      // this basically says each value of state should either be set to\n      // the initial value or the default value if the initial value is not provided\n      _this.id = _this.props.id || \"downshift-\" + generateId();\n      _this.menuId = _this.props.menuId || _this.id + \"-menu\";\n      _this.labelId = _this.props.labelId || _this.id + \"-label\";\n      _this.inputId = _this.props.inputId || _this.id + \"-input\";\n      _this.getItemId = _this.props.getItemId || function (index) {\n        return _this.id + \"-item-\" + index;\n      };\n      _this.input = null;\n      _this.items = [];\n      // itemCount can be changed asynchronously\n      // from within downshift (so it can't come from a prop)\n      // this is why we store it as an instance and use\n      // getItemCount rather than just use items.length\n      // (to support windowing + async)\n      _this.itemCount = null;\n      _this.previousResultCount = 0;\n      _this.timeoutIds = [];\n      /**\n       * @param {Function} fn the function to call after the time\n       * @param {Number} time the time to wait\n       */\n      _this.internalSetTimeout = function (fn, time) {\n        var id = setTimeout(function () {\n          _this.timeoutIds = _this.timeoutIds.filter(function (i) {\n            return i !== id;\n          });\n          fn();\n        }, time);\n        _this.timeoutIds.push(id);\n      };\n      _this.setItemCount = function (count) {\n        _this.itemCount = count;\n      };\n      _this.unsetItemCount = function () {\n        _this.itemCount = null;\n      };\n      _this.setHighlightedIndex = function (highlightedIndex, otherStateToSet) {\n        if (highlightedIndex === void 0) {\n          highlightedIndex = _this.props.defaultHighlightedIndex;\n        }\n        if (otherStateToSet === void 0) {\n          otherStateToSet = {};\n        }\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState(_extends({\n          highlightedIndex: highlightedIndex\n        }, otherStateToSet));\n      };\n      _this.clearSelection = function (cb) {\n        _this.internalSetState({\n          selectedItem: null,\n          inputValue: '',\n          highlightedIndex: _this.props.defaultHighlightedIndex,\n          isOpen: _this.props.defaultIsOpen\n        }, cb);\n      };\n      _this.selectItem = function (item, otherStateToSet, cb) {\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState(_extends({\n          isOpen: _this.props.defaultIsOpen,\n          highlightedIndex: _this.props.defaultHighlightedIndex,\n          selectedItem: item,\n          inputValue: _this.props.itemToString(item)\n        }, otherStateToSet), cb);\n      };\n      _this.selectItemAtIndex = function (itemIndex, otherStateToSet, cb) {\n        var item = _this.items[itemIndex];\n        if (item == null) {\n          return;\n        }\n        _this.selectItem(item, otherStateToSet, cb);\n      };\n      _this.selectHighlightedItem = function (otherStateToSet, cb) {\n        return _this.selectItemAtIndex(_this.getState().highlightedIndex, otherStateToSet, cb);\n      };\n      // any piece of our state can live in two places:\n      // 1. Uncontrolled: it's internal (this.state)\n      //    We will call this.setState to update that state\n      // 2. Controlled: it's external (this.props)\n      //    We will call this.props.onStateChange to update that state\n      //\n      // In addition, we'll call this.props.onChange if the\n      // selectedItem is changed.\n      _this.internalSetState = function (stateToSet, cb) {\n        var isItemSelected, onChangeArg;\n        var onStateChangeArg = {};\n        var isStateToSetFunction = typeof stateToSet === 'function';\n\n        // we want to call `onInputValueChange` before the `setState` call\n        // so someone controlling the `inputValue` state gets notified of\n        // the input change as soon as possible. This avoids issues with\n        // preserving the cursor position.\n        // See https://github.com/downshift-js/downshift/issues/217 for more info.\n        if (!isStateToSetFunction && stateToSet.hasOwnProperty('inputValue')) {\n          _this.props.onInputValueChange(stateToSet.inputValue, _extends({}, _this.getStateAndHelpers(), stateToSet));\n        }\n        return _this.setState(function (state) {\n          state = _this.getState(state);\n          var newStateToSet = isStateToSetFunction ? stateToSet(state) : stateToSet;\n\n          // Your own function that could modify the state that will be set.\n          newStateToSet = _this.props.stateReducer(state, newStateToSet);\n\n          // checks if an item is selected, regardless of if it's different from\n          // what was selected before\n          // used to determine if onSelect and onChange callbacks should be called\n          isItemSelected = newStateToSet.hasOwnProperty('selectedItem');\n          // this keeps track of the object we want to call with setState\n          var nextState = {};\n          // we need to call on change if the outside world is controlling any of our state\n          // and we're trying to update that state. OR if the selection has changed and we're\n          // trying to update the selection\n          if (isItemSelected && newStateToSet.selectedItem !== state.selectedItem) {\n            onChangeArg = newStateToSet.selectedItem;\n          }\n          newStateToSet.type = newStateToSet.type || unknown;\n          Object.keys(newStateToSet).forEach(function (key) {\n            // onStateChangeArg should only have the state that is\n            // actually changing\n            if (state[key] !== newStateToSet[key]) {\n              onStateChangeArg[key] = newStateToSet[key];\n            }\n            // the type is useful for the onStateChangeArg\n            // but we don't actually want to set it in internal state.\n            // this is an undocumented feature for now... Not all internalSetState\n            // calls support it and I'm not certain we want them to yet.\n            // But it enables users controlling the isOpen state to know when\n            // the isOpen state changes due to mouseup events which is quite handy.\n            if (key === 'type') {\n              return;\n            }\n            newStateToSet[key];\n            // if it's coming from props, then we don't care to set it internally\n            if (!isControlledProp(_this.props, key)) {\n              nextState[key] = newStateToSet[key];\n            }\n          });\n\n          // if stateToSet is a function, then we weren't able to call onInputValueChange\n          // earlier, so we'll call it now that we know what the inputValue state will be.\n          if (isStateToSetFunction && newStateToSet.hasOwnProperty('inputValue')) {\n            _this.props.onInputValueChange(newStateToSet.inputValue, _extends({}, _this.getStateAndHelpers(), newStateToSet));\n          }\n          return nextState;\n        }, function () {\n          // call the provided callback if it's a function\n          cbToCb(cb)();\n\n          // only call the onStateChange and onChange callbacks if\n          // we have relevant information to pass them.\n          var hasMoreStateThanType = Object.keys(onStateChangeArg).length > 1;\n          if (hasMoreStateThanType) {\n            _this.props.onStateChange(onStateChangeArg, _this.getStateAndHelpers());\n          }\n          if (isItemSelected) {\n            _this.props.onSelect(stateToSet.selectedItem, _this.getStateAndHelpers());\n          }\n          if (onChangeArg !== undefined) {\n            _this.props.onChange(onChangeArg, _this.getStateAndHelpers());\n          }\n          // this is currently undocumented and therefore subject to change\n          // We'll try to not break it, but just be warned.\n          _this.props.onUserAction(onStateChangeArg, _this.getStateAndHelpers());\n        });\n      };\n      //////////////////////////// ROOT\n      _this.rootRef = function (node) {\n        return _this._rootNode = node;\n      };\n      _this.getRootProps = function (_temp, _temp2) {\n        var _extends2;\n        var _ref = _temp === void 0 ? {} : _temp,\n          _ref$refKey = _ref.refKey,\n          refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey,\n          ref = _ref.ref,\n          rest = _objectWithoutPropertiesLoose(_ref, _excluded$4);\n        var _ref2 = _temp2 === void 0 ? {} : _temp2,\n          _ref2$suppressRefErro = _ref2.suppressRefError,\n          suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;\n        // this is used in the render to know whether the user has called getRootProps.\n        // It uses that to know whether to apply the props automatically\n        _this.getRootProps.called = true;\n        _this.getRootProps.refKey = refKey;\n        _this.getRootProps.suppressRefError = suppressRefError;\n        var _this$getState = _this.getState(),\n          isOpen = _this$getState.isOpen;\n        return _extends((_extends2 = {}, _extends2[refKey] = handleRefs(ref, _this.rootRef), _extends2.role = 'combobox', _extends2['aria-expanded'] = isOpen, _extends2['aria-haspopup'] = 'listbox', _extends2['aria-owns'] = isOpen ? _this.menuId : null, _extends2['aria-labelledby'] = _this.labelId, _extends2), rest);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ROOT\n      _this.keyDownHandlers = {\n        ArrowDown: function ArrowDown(event) {\n          var _this2 = this;\n          event.preventDefault();\n          if (this.getState().isOpen) {\n            var amount = event.shiftKey ? 5 : 1;\n            this.moveHighlightedIndex(amount, {\n              type: keyDownArrowDown\n            });\n          } else {\n            this.internalSetState({\n              isOpen: true,\n              type: keyDownArrowDown\n            }, function () {\n              var itemCount = _this2.getItemCount();\n              if (itemCount > 0) {\n                var _this2$getState = _this2.getState(),\n                  highlightedIndex = _this2$getState.highlightedIndex;\n                var nextHighlightedIndex = getNextWrappingIndex(1, highlightedIndex, itemCount, function (index) {\n                  return _this2.getItemNodeFromIndex(index);\n                });\n                _this2.setHighlightedIndex(nextHighlightedIndex, {\n                  type: keyDownArrowDown\n                });\n              }\n            });\n          }\n        },\n        ArrowUp: function ArrowUp(event) {\n          var _this3 = this;\n          event.preventDefault();\n          if (this.getState().isOpen) {\n            var amount = event.shiftKey ? -5 : -1;\n            this.moveHighlightedIndex(amount, {\n              type: keyDownArrowUp\n            });\n          } else {\n            this.internalSetState({\n              isOpen: true,\n              type: keyDownArrowUp\n            }, function () {\n              var itemCount = _this3.getItemCount();\n              if (itemCount > 0) {\n                var _this3$getState = _this3.getState(),\n                  highlightedIndex = _this3$getState.highlightedIndex;\n                var nextHighlightedIndex = getNextWrappingIndex(-1, highlightedIndex, itemCount, function (index) {\n                  return _this3.getItemNodeFromIndex(index);\n                });\n                _this3.setHighlightedIndex(nextHighlightedIndex, {\n                  type: keyDownArrowUp\n                });\n              }\n            });\n          }\n        },\n        Enter: function Enter(event) {\n          if (event.which === 229) {\n            return;\n          }\n          var _this$getState2 = this.getState(),\n            isOpen = _this$getState2.isOpen,\n            highlightedIndex = _this$getState2.highlightedIndex;\n          if (isOpen && highlightedIndex != null) {\n            event.preventDefault();\n            var item = this.items[highlightedIndex];\n            var itemNode = this.getItemNodeFromIndex(highlightedIndex);\n            if (item == null || itemNode && itemNode.hasAttribute('disabled')) {\n              return;\n            }\n            this.selectHighlightedItem({\n              type: keyDownEnter\n            });\n          }\n        },\n        Escape: function Escape(event) {\n          event.preventDefault();\n          this.reset(_extends({\n            type: keyDownEscape\n          }, !this.state.isOpen && {\n            selectedItem: null,\n            inputValue: ''\n          }));\n        }\n      };\n      //////////////////////////// BUTTON\n      _this.buttonKeyDownHandlers = _extends({}, _this.keyDownHandlers, {\n        ' ': function _(event) {\n          event.preventDefault();\n          this.toggleMenu({\n            type: keyDownSpaceButton\n          });\n        }\n      });\n      _this.inputKeyDownHandlers = _extends({}, _this.keyDownHandlers, {\n        Home: function Home(event) {\n          var _this4 = this;\n          var _this$getState3 = this.getState(),\n            isOpen = _this$getState3.isOpen;\n          if (!isOpen) {\n            return;\n          }\n          event.preventDefault();\n          var itemCount = this.getItemCount();\n          if (itemCount <= 0 || !isOpen) {\n            return;\n          }\n\n          // get next non-disabled starting downwards from 0 if that's disabled.\n          var newHighlightedIndex = getNextNonDisabledIndex(1, 0, itemCount, function (index) {\n            return _this4.getItemNodeFromIndex(index);\n          }, false);\n          this.setHighlightedIndex(newHighlightedIndex, {\n            type: keyDownHome\n          });\n        },\n        End: function End(event) {\n          var _this5 = this;\n          var _this$getState4 = this.getState(),\n            isOpen = _this$getState4.isOpen;\n          if (!isOpen) {\n            return;\n          }\n          event.preventDefault();\n          var itemCount = this.getItemCount();\n          if (itemCount <= 0 || !isOpen) {\n            return;\n          }\n\n          // get next non-disabled starting upwards from last index if that's disabled.\n          var newHighlightedIndex = getNextNonDisabledIndex(-1, itemCount - 1, itemCount, function (index) {\n            return _this5.getItemNodeFromIndex(index);\n          }, false);\n          this.setHighlightedIndex(newHighlightedIndex, {\n            type: keyDownEnd\n          });\n        }\n      });\n      _this.getToggleButtonProps = function (_temp3) {\n        var _ref3 = _temp3 === void 0 ? {} : _temp3,\n          onClick = _ref3.onClick;\n          _ref3.onPress;\n          var onKeyDown = _ref3.onKeyDown,\n          onKeyUp = _ref3.onKeyUp,\n          onBlur = _ref3.onBlur,\n          rest = _objectWithoutPropertiesLoose(_ref3, _excluded2$3);\n        var _this$getState5 = _this.getState(),\n          isOpen = _this$getState5.isOpen;\n        var enabledEventHandlers = {\n          onClick: callAllEventHandlers(onClick, _this.buttonHandleClick),\n          onKeyDown: callAllEventHandlers(onKeyDown, _this.buttonHandleKeyDown),\n          onKeyUp: callAllEventHandlers(onKeyUp, _this.buttonHandleKeyUp),\n          onBlur: callAllEventHandlers(onBlur, _this.buttonHandleBlur)\n        };\n        var eventHandlers = rest.disabled ? {} : enabledEventHandlers;\n        return _extends({\n          type: 'button',\n          role: 'button',\n          'aria-label': isOpen ? 'close menu' : 'open menu',\n          'aria-haspopup': true,\n          'data-toggle': true\n        }, eventHandlers, rest);\n      };\n      _this.buttonHandleKeyUp = function (event) {\n        // Prevent click event from emitting in Firefox\n        event.preventDefault();\n      };\n      _this.buttonHandleKeyDown = function (event) {\n        var key = normalizeArrowKey(event);\n        if (_this.buttonKeyDownHandlers[key]) {\n          _this.buttonKeyDownHandlers[key].call(_assertThisInitialized(_this), event);\n        }\n      };\n      _this.buttonHandleClick = function (event) {\n        event.preventDefault();\n        // handle odd case for Safari and Firefox which\n        // don't give the button the focus properly.\n        /* istanbul ignore if (can't reasonably test this) */\n        if (_this.props.environment.document.activeElement === _this.props.environment.document.body) {\n          event.target.focus();\n        }\n        // to simplify testing components that use downshift, we'll not wrap this in a setTimeout\n        // if the NODE_ENV is test. With the proper build system, this should be dead code eliminated\n        // when building for production and should therefore have no impact on production code.\n        if (process.env.NODE_ENV === 'test') {\n          _this.toggleMenu({\n            type: clickButton\n          });\n        } else {\n          // Ensure that toggle of menu occurs after the potential blur event in iOS\n          _this.internalSetTimeout(function () {\n            return _this.toggleMenu({\n              type: clickButton\n            });\n          });\n        }\n      };\n      _this.buttonHandleBlur = function (event) {\n        var blurTarget = event.target; // Save blur target for comparison with activeElement later\n        // Need setTimeout, so that when the user presses Tab, the activeElement is the next focused element, not body element\n        _this.internalSetTimeout(function () {\n          if (!_this.isMouseDown && (_this.props.environment.document.activeElement == null || _this.props.environment.document.activeElement.id !== _this.inputId) && _this.props.environment.document.activeElement !== blurTarget // Do nothing if we refocus the same element again (to solve issue in Safari on iOS)\n          ) {\n            _this.reset({\n              type: blurButton\n            });\n          }\n        });\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ BUTTON\n      /////////////////////////////// LABEL\n      _this.getLabelProps = function (props) {\n        return _extends({\n          htmlFor: _this.inputId,\n          id: _this.labelId\n        }, props);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ LABEL\n      /////////////////////////////// INPUT\n      _this.getInputProps = function (_temp4) {\n        var _ref4 = _temp4 === void 0 ? {} : _temp4,\n          onKeyDown = _ref4.onKeyDown,\n          onBlur = _ref4.onBlur,\n          onChange = _ref4.onChange,\n          onInput = _ref4.onInput;\n          _ref4.onChangeText;\n          var rest = _objectWithoutPropertiesLoose(_ref4, _excluded3$2);\n        var onChangeKey;\n        var eventHandlers = {};\n\n        /* istanbul ignore next (preact) */\n        {\n          onChangeKey = 'onChange';\n        }\n        var _this$getState6 = _this.getState(),\n          inputValue = _this$getState6.inputValue,\n          isOpen = _this$getState6.isOpen,\n          highlightedIndex = _this$getState6.highlightedIndex;\n        if (!rest.disabled) {\n          var _eventHandlers;\n          eventHandlers = (_eventHandlers = {}, _eventHandlers[onChangeKey] = callAllEventHandlers(onChange, onInput, _this.inputHandleChange), _eventHandlers.onKeyDown = callAllEventHandlers(onKeyDown, _this.inputHandleKeyDown), _eventHandlers.onBlur = callAllEventHandlers(onBlur, _this.inputHandleBlur), _eventHandlers);\n        }\n        return _extends({\n          'aria-autocomplete': 'list',\n          'aria-activedescendant': isOpen && typeof highlightedIndex === 'number' && highlightedIndex >= 0 ? _this.getItemId(highlightedIndex) : null,\n          'aria-controls': isOpen ? _this.menuId : null,\n          'aria-labelledby': rest && rest['aria-label'] ? undefined : _this.labelId,\n          // https://developer.mozilla.org/en-US/docs/Web/Security/Securing_your_site/Turning_off_form_autocompletion\n          // revert back since autocomplete=\"nope\" is ignored on latest Chrome and Opera\n          autoComplete: 'off',\n          value: inputValue,\n          id: _this.inputId\n        }, eventHandlers, rest);\n      };\n      _this.inputHandleKeyDown = function (event) {\n        var key = normalizeArrowKey(event);\n        if (key && _this.inputKeyDownHandlers[key]) {\n          _this.inputKeyDownHandlers[key].call(_assertThisInitialized(_this), event);\n        }\n      };\n      _this.inputHandleChange = function (event) {\n        _this.internalSetState({\n          type: changeInput,\n          isOpen: true,\n          inputValue: event.target.value,\n          highlightedIndex: _this.props.defaultHighlightedIndex\n        });\n      };\n      _this.inputHandleBlur = function () {\n        // Need setTimeout, so that when the user presses Tab, the activeElement is the next focused element, not the body element\n        _this.internalSetTimeout(function () {\n          var downshiftButtonIsActive = _this.props.environment.document && !!_this.props.environment.document.activeElement && !!_this.props.environment.document.activeElement.dataset && _this.props.environment.document.activeElement.dataset.toggle && _this._rootNode && _this._rootNode.contains(_this.props.environment.document.activeElement);\n          if (!_this.isMouseDown && !downshiftButtonIsActive) {\n            _this.reset({\n              type: blurInput\n            });\n          }\n        });\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ INPUT\n      /////////////////////////////// MENU\n      _this.menuRef = function (node) {\n        _this._menuNode = node;\n      };\n      _this.getMenuProps = function (_temp5, _temp6) {\n        var _extends3;\n        var _ref5 = _temp5 === void 0 ? {} : _temp5,\n          _ref5$refKey = _ref5.refKey,\n          refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey,\n          ref = _ref5.ref,\n          props = _objectWithoutPropertiesLoose(_ref5, _excluded4$1);\n        var _ref6 = _temp6 === void 0 ? {} : _temp6,\n          _ref6$suppressRefErro = _ref6.suppressRefError,\n          suppressRefError = _ref6$suppressRefErro === void 0 ? false : _ref6$suppressRefErro;\n        _this.getMenuProps.called = true;\n        _this.getMenuProps.refKey = refKey;\n        _this.getMenuProps.suppressRefError = suppressRefError;\n        return _extends((_extends3 = {}, _extends3[refKey] = handleRefs(ref, _this.menuRef), _extends3.role = 'listbox', _extends3['aria-labelledby'] = props && props['aria-label'] ? null : _this.labelId, _extends3.id = _this.menuId, _extends3), props);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ MENU\n      /////////////////////////////// ITEM\n      _this.getItemProps = function (_temp7) {\n        var _enabledEventHandlers;\n        var _ref7 = _temp7 === void 0 ? {} : _temp7,\n          onMouseMove = _ref7.onMouseMove,\n          onMouseDown = _ref7.onMouseDown,\n          onClick = _ref7.onClick;\n          _ref7.onPress;\n          var index = _ref7.index,\n          _ref7$item = _ref7.item,\n          item = _ref7$item === void 0 ? process.env.NODE_ENV === 'production' ? /* istanbul ignore next */undefined : requiredProp('getItemProps', 'item') : _ref7$item,\n          rest = _objectWithoutPropertiesLoose(_ref7, _excluded5);\n        if (index === undefined) {\n          _this.items.push(item);\n          index = _this.items.indexOf(item);\n        } else {\n          _this.items[index] = item;\n        }\n        var onSelectKey = 'onClick';\n        var customClickHandler = onClick;\n        var enabledEventHandlers = (_enabledEventHandlers = {\n          // onMouseMove is used over onMouseEnter here. onMouseMove\n          // is only triggered on actual mouse movement while onMouseEnter\n          // can fire on DOM changes, interrupting keyboard navigation\n          onMouseMove: callAllEventHandlers(onMouseMove, function () {\n            if (index === _this.getState().highlightedIndex) {\n              return;\n            }\n            _this.setHighlightedIndex(index, {\n              type: itemMouseEnter\n            });\n\n            // We never want to manually scroll when changing state based\n            // on `onMouseMove` because we will be moving the element out\n            // from under the user which is currently scrolling/moving the\n            // cursor\n            _this.avoidScrolling = true;\n            _this.internalSetTimeout(function () {\n              return _this.avoidScrolling = false;\n            }, 250);\n          }),\n          onMouseDown: callAllEventHandlers(onMouseDown, function (event) {\n            // This prevents the activeElement from being changed\n            // to the item so it can remain with the current activeElement\n            // which is a more common use case.\n            event.preventDefault();\n          })\n        }, _enabledEventHandlers[onSelectKey] = callAllEventHandlers(customClickHandler, function () {\n          _this.selectItemAtIndex(index, {\n            type: clickItem\n          });\n        }), _enabledEventHandlers);\n\n        // Passing down the onMouseDown handler to prevent redirect\n        // of the activeElement if clicking on disabled items\n        var eventHandlers = rest.disabled ? {\n          onMouseDown: enabledEventHandlers.onMouseDown\n        } : enabledEventHandlers;\n        return _extends({\n          id: _this.getItemId(index),\n          role: 'option',\n          'aria-selected': _this.getState().highlightedIndex === index\n        }, eventHandlers, rest);\n      };\n      //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ITEM\n      _this.clearItems = function () {\n        _this.items = [];\n      };\n      _this.reset = function (otherStateToSet, cb) {\n        if (otherStateToSet === void 0) {\n          otherStateToSet = {};\n        }\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState(function (_ref8) {\n          var selectedItem = _ref8.selectedItem;\n          return _extends({\n            isOpen: _this.props.defaultIsOpen,\n            highlightedIndex: _this.props.defaultHighlightedIndex,\n            inputValue: _this.props.itemToString(selectedItem)\n          }, otherStateToSet);\n        }, cb);\n      };\n      _this.toggleMenu = function (otherStateToSet, cb) {\n        if (otherStateToSet === void 0) {\n          otherStateToSet = {};\n        }\n        otherStateToSet = pickState(otherStateToSet);\n        _this.internalSetState(function (_ref9) {\n          var isOpen = _ref9.isOpen;\n          return _extends({\n            isOpen: !isOpen\n          }, isOpen && {\n            highlightedIndex: _this.props.defaultHighlightedIndex\n          }, otherStateToSet);\n        }, function () {\n          var _this$getState7 = _this.getState(),\n            isOpen = _this$getState7.isOpen,\n            highlightedIndex = _this$getState7.highlightedIndex;\n          if (isOpen) {\n            if (_this.getItemCount() > 0 && typeof highlightedIndex === 'number') {\n              _this.setHighlightedIndex(highlightedIndex, otherStateToSet);\n            }\n          }\n          cbToCb(cb)();\n        });\n      };\n      _this.openMenu = function (cb) {\n        _this.internalSetState({\n          isOpen: true\n        }, cb);\n      };\n      _this.closeMenu = function (cb) {\n        _this.internalSetState({\n          isOpen: false\n        }, cb);\n      };\n      _this.updateStatus = debounce(function () {\n        var state = _this.getState();\n        var item = _this.items[state.highlightedIndex];\n        var resultCount = _this.getItemCount();\n        var status = _this.props.getA11yStatusMessage(_extends({\n          itemToString: _this.props.itemToString,\n          previousResultCount: _this.previousResultCount,\n          resultCount: resultCount,\n          highlightedItem: item\n        }, state));\n        _this.previousResultCount = resultCount;\n        setStatus(status, _this.props.environment.document);\n      }, 200);\n      var _this$props = _this.props,\n        defaultHighlightedIndex = _this$props.defaultHighlightedIndex,\n        _this$props$initialHi = _this$props.initialHighlightedIndex,\n        _highlightedIndex = _this$props$initialHi === void 0 ? defaultHighlightedIndex : _this$props$initialHi,\n        defaultIsOpen = _this$props.defaultIsOpen,\n        _this$props$initialIs = _this$props.initialIsOpen,\n        _isOpen = _this$props$initialIs === void 0 ? defaultIsOpen : _this$props$initialIs,\n        _this$props$initialIn = _this$props.initialInputValue,\n        _inputValue = _this$props$initialIn === void 0 ? '' : _this$props$initialIn,\n        _this$props$initialSe = _this$props.initialSelectedItem,\n        _selectedItem = _this$props$initialSe === void 0 ? null : _this$props$initialSe;\n      var _state = _this.getState({\n        highlightedIndex: _highlightedIndex,\n        isOpen: _isOpen,\n        inputValue: _inputValue,\n        selectedItem: _selectedItem\n      });\n      if (_state.selectedItem != null && _this.props.initialInputValue === undefined) {\n        _state.inputValue = _this.props.itemToString(_state.selectedItem);\n      }\n      _this.state = _state;\n      return _this;\n    }\n    var _proto = Downshift.prototype;\n    /**\n     * Clear all running timeouts\n     */\n    _proto.internalClearTimeouts = function internalClearTimeouts() {\n      this.timeoutIds.forEach(function (id) {\n        clearTimeout(id);\n      });\n      this.timeoutIds = [];\n    }\n\n    /**\n     * Gets the state based on internal state or props\n     * If a state value is passed via props, then that\n     * is the value given, otherwise it's retrieved from\n     * stateToMerge\n     *\n     * @param {Object} stateToMerge defaults to this.state\n     * @return {Object} the state\n     */;\n    _proto.getState = function getState$1(stateToMerge) {\n      if (stateToMerge === void 0) {\n        stateToMerge = this.state;\n      }\n      return getState(stateToMerge, this.props);\n    };\n    _proto.getItemCount = function getItemCount() {\n      // things read better this way. They're in priority order:\n      // 1. `this.itemCount`\n      // 2. `this.props.itemCount`\n      // 3. `this.items.length`\n      var itemCount = this.items.length;\n      if (this.itemCount != null) {\n        itemCount = this.itemCount;\n      } else if (this.props.itemCount !== undefined) {\n        itemCount = this.props.itemCount;\n      }\n      return itemCount;\n    };\n    _proto.getItemNodeFromIndex = function getItemNodeFromIndex(index) {\n      return this.props.environment.document.getElementById(this.getItemId(index));\n    };\n    _proto.scrollHighlightedItemIntoView = function scrollHighlightedItemIntoView() {\n      /* istanbul ignore else (react-native) */\n      {\n        var node = this.getItemNodeFromIndex(this.getState().highlightedIndex);\n        this.props.scrollIntoView(node, this._menuNode);\n      }\n    };\n    _proto.moveHighlightedIndex = function moveHighlightedIndex(amount, otherStateToSet) {\n      var _this6 = this;\n      var itemCount = this.getItemCount();\n      var _this$getState8 = this.getState(),\n        highlightedIndex = _this$getState8.highlightedIndex;\n      if (itemCount > 0) {\n        var nextHighlightedIndex = getNextWrappingIndex(amount, highlightedIndex, itemCount, function (index) {\n          return _this6.getItemNodeFromIndex(index);\n        });\n        this.setHighlightedIndex(nextHighlightedIndex, otherStateToSet);\n      }\n    };\n    _proto.getStateAndHelpers = function getStateAndHelpers() {\n      var _this$getState9 = this.getState(),\n        highlightedIndex = _this$getState9.highlightedIndex,\n        inputValue = _this$getState9.inputValue,\n        selectedItem = _this$getState9.selectedItem,\n        isOpen = _this$getState9.isOpen;\n      var itemToString = this.props.itemToString;\n      var id = this.id;\n      var getRootProps = this.getRootProps,\n        getToggleButtonProps = this.getToggleButtonProps,\n        getLabelProps = this.getLabelProps,\n        getMenuProps = this.getMenuProps,\n        getInputProps = this.getInputProps,\n        getItemProps = this.getItemProps,\n        openMenu = this.openMenu,\n        closeMenu = this.closeMenu,\n        toggleMenu = this.toggleMenu,\n        selectItem = this.selectItem,\n        selectItemAtIndex = this.selectItemAtIndex,\n        selectHighlightedItem = this.selectHighlightedItem,\n        setHighlightedIndex = this.setHighlightedIndex,\n        clearSelection = this.clearSelection,\n        clearItems = this.clearItems,\n        reset = this.reset,\n        setItemCount = this.setItemCount,\n        unsetItemCount = this.unsetItemCount,\n        setState = this.internalSetState;\n      return {\n        // prop getters\n        getRootProps: getRootProps,\n        getToggleButtonProps: getToggleButtonProps,\n        getLabelProps: getLabelProps,\n        getMenuProps: getMenuProps,\n        getInputProps: getInputProps,\n        getItemProps: getItemProps,\n        // actions\n        reset: reset,\n        openMenu: openMenu,\n        closeMenu: closeMenu,\n        toggleMenu: toggleMenu,\n        selectItem: selectItem,\n        selectItemAtIndex: selectItemAtIndex,\n        selectHighlightedItem: selectHighlightedItem,\n        setHighlightedIndex: setHighlightedIndex,\n        clearSelection: clearSelection,\n        clearItems: clearItems,\n        setItemCount: setItemCount,\n        unsetItemCount: unsetItemCount,\n        setState: setState,\n        // props\n        itemToString: itemToString,\n        // derived\n        id: id,\n        // state\n        highlightedIndex: highlightedIndex,\n        inputValue: inputValue,\n        isOpen: isOpen,\n        selectedItem: selectedItem\n      };\n    };\n    _proto.componentDidMount = function componentDidMount() {\n      var _this7 = this;\n      /* istanbul ignore if (react-native) */\n      if (process.env.NODE_ENV !== 'production' && !false && this.getMenuProps.called && !this.getMenuProps.suppressRefError) {\n        validateGetMenuPropsCalledCorrectly(this._menuNode, this.getMenuProps);\n      }\n\n      /* istanbul ignore if (react-native) */\n      {\n        // this.isMouseDown helps us track whether the mouse is currently held down.\n        // This is useful when the user clicks on an item in the list, but holds the mouse\n        // down long enough for the list to disappear (because the blur event fires on the input)\n        // this.isMouseDown is used in the blur handler on the input to determine whether the blur event should\n        // trigger hiding the menu.\n        var onMouseDown = function onMouseDown() {\n          _this7.isMouseDown = true;\n        };\n        var onMouseUp = function onMouseUp(event) {\n          _this7.isMouseDown = false;\n          // if the target element or the activeElement is within a downshift node\n          // then we don't want to reset downshift\n          var contextWithinDownshift = targetWithinDownshift(event.target, [_this7._rootNode, _this7._menuNode], _this7.props.environment);\n          if (!contextWithinDownshift && _this7.getState().isOpen) {\n            _this7.reset({\n              type: mouseUp\n            }, function () {\n              return _this7.props.onOuterClick(_this7.getStateAndHelpers());\n            });\n          }\n        };\n        // Touching an element in iOS gives focus and hover states, but touching out of\n        // the element will remove hover, and persist the focus state, resulting in the\n        // blur event not being triggered.\n        // this.isTouchMove helps us track whether the user is tapping or swiping on a touch screen.\n        // If the user taps outside of Downshift, the component should be reset,\n        // but not if the user is swiping\n        var onTouchStart = function onTouchStart() {\n          _this7.isTouchMove = false;\n        };\n        var onTouchMove = function onTouchMove() {\n          _this7.isTouchMove = true;\n        };\n        var onTouchEnd = function onTouchEnd(event) {\n          var contextWithinDownshift = targetWithinDownshift(event.target, [_this7._rootNode, _this7._menuNode], _this7.props.environment, false);\n          if (!_this7.isTouchMove && !contextWithinDownshift && _this7.getState().isOpen) {\n            _this7.reset({\n              type: touchEnd\n            }, function () {\n              return _this7.props.onOuterClick(_this7.getStateAndHelpers());\n            });\n          }\n        };\n        var environment = this.props.environment;\n        environment.addEventListener('mousedown', onMouseDown);\n        environment.addEventListener('mouseup', onMouseUp);\n        environment.addEventListener('touchstart', onTouchStart);\n        environment.addEventListener('touchmove', onTouchMove);\n        environment.addEventListener('touchend', onTouchEnd);\n        this.cleanup = function () {\n          _this7.internalClearTimeouts();\n          _this7.updateStatus.cancel();\n          environment.removeEventListener('mousedown', onMouseDown);\n          environment.removeEventListener('mouseup', onMouseUp);\n          environment.removeEventListener('touchstart', onTouchStart);\n          environment.removeEventListener('touchmove', onTouchMove);\n          environment.removeEventListener('touchend', onTouchEnd);\n        };\n      }\n    };\n    _proto.shouldScroll = function shouldScroll(prevState, prevProps) {\n      var _ref10 = this.props.highlightedIndex === undefined ? this.getState() : this.props,\n        currentHighlightedIndex = _ref10.highlightedIndex;\n      var _ref11 = prevProps.highlightedIndex === undefined ? prevState : prevProps,\n        prevHighlightedIndex = _ref11.highlightedIndex;\n      var scrollWhenOpen = currentHighlightedIndex && this.getState().isOpen && !prevState.isOpen;\n      var scrollWhenNavigating = currentHighlightedIndex !== prevHighlightedIndex;\n      return scrollWhenOpen || scrollWhenNavigating;\n    };\n    _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n      if (process.env.NODE_ENV !== 'production') {\n        validateControlledUnchanged(this.state, prevProps, this.props);\n        /* istanbul ignore if (react-native) */\n        if (this.getMenuProps.called && !this.getMenuProps.suppressRefError) {\n          validateGetMenuPropsCalledCorrectly(this._menuNode, this.getMenuProps);\n        }\n      }\n      if (isControlledProp(this.props, 'selectedItem') && this.props.selectedItemChanged(prevProps.selectedItem, this.props.selectedItem)) {\n        this.internalSetState({\n          type: controlledPropUpdatedSelectedItem,\n          inputValue: this.props.itemToString(this.props.selectedItem)\n        });\n      }\n      if (!this.avoidScrolling && this.shouldScroll(prevState, prevProps)) {\n        this.scrollHighlightedItemIntoView();\n      }\n\n      /* istanbul ignore else (react-native) */\n      {\n        this.updateStatus();\n      }\n    };\n    _proto.componentWillUnmount = function componentWillUnmount() {\n      this.cleanup(); // avoids memory leak\n    };\n    _proto.render = function render() {\n      var children = unwrapArray(this.props.children, noop);\n      // because the items are rerendered every time we call the children\n      // we clear this out each render and it will be populated again as\n      // getItemProps is called.\n      this.clearItems();\n      // we reset this so we know whether the user calls getRootProps during\n      // this render. If they do then we don't need to do anything,\n      // if they don't then we need to clone the element they return and\n      // apply the props for them.\n      this.getRootProps.called = false;\n      this.getRootProps.refKey = undefined;\n      this.getRootProps.suppressRefError = undefined;\n      // we do something similar for getMenuProps\n      this.getMenuProps.called = false;\n      this.getMenuProps.refKey = undefined;\n      this.getMenuProps.suppressRefError = undefined;\n      // we do something similar for getLabelProps\n      this.getLabelProps.called = false;\n      // and something similar for getInputProps\n      this.getInputProps.called = false;\n      var element = unwrapArray(children(this.getStateAndHelpers()));\n      if (!element) {\n        return null;\n      }\n      if (this.getRootProps.called || this.props.suppressRefError) {\n        if (process.env.NODE_ENV !== 'production' && !this.getRootProps.suppressRefError && !this.props.suppressRefError) {\n          validateGetRootPropsCalledCorrectly(element, this.getRootProps);\n        }\n        return element;\n      } else if (isDOMElement(element)) {\n        // they didn't apply the root props, but we can clone\n        // this and apply the props ourselves\n        return /*#__PURE__*/cloneElement(element, this.getRootProps(getElementProps(element)));\n      }\n\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        // they didn't apply the root props, but they need to\n        // otherwise we can't query around the autocomplete\n\n        throw new Error('downshift: If you return a non-DOM element, you must apply the getRootProps function');\n      }\n\n      /* istanbul ignore next */\n      return undefined;\n    };\n    return Downshift;\n  }(Component);\n  Downshift.defaultProps = {\n    defaultHighlightedIndex: null,\n    defaultIsOpen: false,\n    getA11yStatusMessage: getA11yStatusMessage$1,\n    itemToString: function itemToString(i) {\n      if (i == null) {\n        return '';\n      }\n      if (process.env.NODE_ENV !== 'production' && isPlainObject(i) && !i.hasOwnProperty('toString')) {\n        // eslint-disable-next-line no-console\n        console.warn('downshift: An object was passed to the default implementation of `itemToString`. You should probably provide your own `itemToString` implementation. Please refer to the `itemToString` API documentation.', 'The object that was passed:', i);\n      }\n      return String(i);\n    },\n    onStateChange: noop,\n    onInputValueChange: noop,\n    onUserAction: noop,\n    onChange: noop,\n    onSelect: noop,\n    onOuterClick: noop,\n    selectedItemChanged: function selectedItemChanged(prevItem, item) {\n      return prevItem !== item;\n    },\n    environment: /* istanbul ignore next (ssr) */\n    typeof window === 'undefined' ? {} : window,\n    stateReducer: function stateReducer(state, stateToSet) {\n      return stateToSet;\n    },\n    suppressRefError: false,\n    scrollIntoView: scrollIntoView\n  };\n  Downshift.stateChangeTypes = stateChangeTypes$3;\n  return Downshift;\n}();\nprocess.env.NODE_ENV !== \"production\" ? Downshift.propTypes = {\n  children: PropTypes.func,\n  defaultHighlightedIndex: PropTypes.number,\n  defaultIsOpen: PropTypes.bool,\n  initialHighlightedIndex: PropTypes.number,\n  initialSelectedItem: PropTypes.any,\n  initialInputValue: PropTypes.string,\n  initialIsOpen: PropTypes.bool,\n  getA11yStatusMessage: PropTypes.func,\n  itemToString: PropTypes.func,\n  onChange: PropTypes.func,\n  onSelect: PropTypes.func,\n  onStateChange: PropTypes.func,\n  onInputValueChange: PropTypes.func,\n  onUserAction: PropTypes.func,\n  onOuterClick: PropTypes.func,\n  selectedItemChanged: PropTypes.func,\n  stateReducer: PropTypes.func,\n  itemCount: PropTypes.number,\n  id: PropTypes.string,\n  environment: PropTypes.shape({\n    addEventListener: PropTypes.func,\n    removeEventListener: PropTypes.func,\n    document: PropTypes.shape({\n      getElementById: PropTypes.func,\n      activeElement: PropTypes.any,\n      body: PropTypes.any\n    })\n  }),\n  suppressRefError: PropTypes.bool,\n  scrollIntoView: PropTypes.func,\n  // things we keep in state for uncontrolled components\n  // but can accept as props for controlled components\n  /* eslint-disable react/no-unused-prop-types */\n  selectedItem: PropTypes.any,\n  isOpen: PropTypes.bool,\n  inputValue: PropTypes.string,\n  highlightedIndex: PropTypes.number,\n  labelId: PropTypes.string,\n  inputId: PropTypes.string,\n  menuId: PropTypes.string,\n  getItemId: PropTypes.func\n  /* eslint-enable react/no-unused-prop-types */\n} : void 0;\nvar Downshift$1 = Downshift;\nfunction validateGetMenuPropsCalledCorrectly(node, _ref12) {\n  var refKey = _ref12.refKey;\n  if (!node) {\n    // eslint-disable-next-line no-console\n    console.error(\"downshift: The ref prop \\\"\" + refKey + \"\\\" from getMenuProps was not applied correctly on your menu element.\");\n  }\n}\nfunction validateGetRootPropsCalledCorrectly(element, _ref13) {\n  var refKey = _ref13.refKey;\n  var refKeySpecified = refKey !== 'ref';\n  var isComposite = !isDOMElement(element);\n  if (isComposite && !refKeySpecified && !isForwardRef(element)) {\n    // eslint-disable-next-line no-console\n    console.error('downshift: You returned a non-DOM element. You must specify a refKey in getRootProps');\n  } else if (!isComposite && refKeySpecified) {\n    // eslint-disable-next-line no-console\n    console.error(\"downshift: You returned a DOM element. You should not specify a refKey in getRootProps. You specified \\\"\" + refKey + \"\\\"\");\n  }\n  if (!isForwardRef(element) && !getElementProps(element)[refKey]) {\n    // eslint-disable-next-line no-console\n    console.error(\"downshift: You must apply the ref prop \\\"\" + refKey + \"\\\" from getRootProps onto your root element.\");\n  }\n}\n\nvar _excluded$3 = [\"isInitialMount\", \"highlightedIndex\", \"items\", \"environment\"];\nvar dropdownDefaultStateValues = {\n  highlightedIndex: -1,\n  isOpen: false,\n  selectedItem: null,\n  inputValue: ''\n};\nfunction callOnChangeProps(action, state, newState) {\n  var props = action.props,\n    type = action.type;\n  var changes = {};\n  Object.keys(state).forEach(function (key) {\n    invokeOnChangeHandler(key, action, state, newState);\n    if (newState[key] !== state[key]) {\n      changes[key] = newState[key];\n    }\n  });\n  if (props.onStateChange && Object.keys(changes).length) {\n    props.onStateChange(_extends({\n      type: type\n    }, changes));\n  }\n}\nfunction invokeOnChangeHandler(key, action, state, newState) {\n  var props = action.props,\n    type = action.type;\n  var handler = \"on\" + capitalizeString(key) + \"Change\";\n  if (props[handler] && newState[key] !== undefined && newState[key] !== state[key]) {\n    props[handler](_extends({\n      type: type\n    }, newState));\n  }\n}\n\n/**\n * Default state reducer that returns the changes.\n *\n * @param {Object} s state.\n * @param {Object} a action with changes.\n * @returns {Object} changes.\n */\nfunction stateReducer(s, a) {\n  return a.changes;\n}\n\n/**\n * Returns a message to be added to aria-live region when item is selected.\n *\n * @param {Object} selectionParameters Parameters required to build the message.\n * @returns {string} The a11y message.\n */\nfunction getA11ySelectionMessage(selectionParameters) {\n  var selectedItem = selectionParameters.selectedItem,\n    itemToStringLocal = selectionParameters.itemToString;\n  return selectedItem ? itemToStringLocal(selectedItem) + \" has been selected.\" : '';\n}\n\n/**\n * Debounced call for updating the a11y message.\n */\nvar updateA11yStatus = debounce(function (getA11yMessage, document) {\n  setStatus(getA11yMessage(), document);\n}, 200);\n\n// istanbul ignore next\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;\nfunction useElementIds(_ref) {\n  var _ref$id = _ref.id,\n    id = _ref$id === void 0 ? \"downshift-\" + generateId() : _ref$id,\n    labelId = _ref.labelId,\n    menuId = _ref.menuId,\n    getItemId = _ref.getItemId,\n    toggleButtonId = _ref.toggleButtonId,\n    inputId = _ref.inputId;\n  var elementIdsRef = useRef({\n    labelId: labelId || id + \"-label\",\n    menuId: menuId || id + \"-menu\",\n    getItemId: getItemId || function (index) {\n      return id + \"-item-\" + index;\n    },\n    toggleButtonId: toggleButtonId || id + \"-toggle-button\",\n    inputId: inputId || id + \"-input\"\n  });\n  return elementIdsRef.current;\n}\nfunction getItemAndIndex(itemProp, indexProp, items, errorMessage) {\n  var item, index;\n  if (itemProp === undefined) {\n    if (indexProp === undefined) {\n      throw new Error(errorMessage);\n    }\n    item = items[indexProp];\n    index = indexProp;\n  } else {\n    index = indexProp === undefined ? items.indexOf(itemProp) : indexProp;\n    item = itemProp;\n  }\n  return [item, index];\n}\nfunction itemToString(item) {\n  return item ? String(item) : '';\n}\nfunction isAcceptedCharacterKey(key) {\n  return /^\\S{1}$/.test(key);\n}\nfunction capitalizeString(string) {\n  return \"\" + string.slice(0, 1).toUpperCase() + string.slice(1);\n}\nfunction useLatestRef(val) {\n  var ref = useRef(val);\n  // technically this is not \"concurrent mode safe\" because we're manipulating\n  // the value during render (so it's not idempotent). However, the places this\n  // hook is used is to support memoizing callbacks which will be called\n  // *during* render, so we need the latest values *during* render.\n  // If not for this, then we'd probably want to use useLayoutEffect instead.\n  ref.current = val;\n  return ref;\n}\n\n/**\n * Computes the controlled state using a the previous state, props,\n * two reducers, one from downshift and an optional one from the user.\n * Also calls the onChange handlers for state values that have changed.\n *\n * @param {Function} reducer Reducer function from downshift.\n * @param {Object} initialState Initial state of the hook.\n * @param {Object} props The hook props.\n * @returns {Array} An array with the state and an action dispatcher.\n */\nfunction useEnhancedReducer(reducer, initialState, props) {\n  var prevStateRef = useRef();\n  var actionRef = useRef();\n  var enhancedReducer = useCallback(function (state, action) {\n    actionRef.current = action;\n    state = getState(state, action.props);\n    var changes = reducer(state, action);\n    var newState = action.props.stateReducer(state, _extends({}, action, {\n      changes: changes\n    }));\n    return newState;\n  }, [reducer]);\n  var _useReducer = useReducer(enhancedReducer, initialState),\n    state = _useReducer[0],\n    dispatch = _useReducer[1];\n  var propsRef = useLatestRef(props);\n  var dispatchWithProps = useCallback(function (action) {\n    return dispatch(_extends({\n      props: propsRef.current\n    }, action));\n  }, [propsRef]);\n  var action = actionRef.current;\n  useEffect(function () {\n    if (action && prevStateRef.current && prevStateRef.current !== state) {\n      callOnChangeProps(action, getState(prevStateRef.current, action.props), state);\n    }\n    prevStateRef.current = state;\n  }, [state, props, action]);\n  return [state, dispatchWithProps];\n}\n\n/**\n * Wraps the useEnhancedReducer and applies the controlled prop values before\n * returning the new state.\n *\n * @param {Function} reducer Reducer function from downshift.\n * @param {Object} initialState Initial state of the hook.\n * @param {Object} props The hook props.\n * @returns {Array} An array with the state and an action dispatcher.\n */\nfunction useControlledReducer$1(reducer, initialState, props) {\n  var _useEnhancedReducer = useEnhancedReducer(reducer, initialState, props),\n    state = _useEnhancedReducer[0],\n    dispatch = _useEnhancedReducer[1];\n  return [getState(state, props), dispatch];\n}\nvar defaultProps$3 = {\n  itemToString: itemToString,\n  stateReducer: stateReducer,\n  getA11ySelectionMessage: getA11ySelectionMessage,\n  scrollIntoView: scrollIntoView,\n  environment: /* istanbul ignore next (ssr) */\n  typeof window === 'undefined' ? {} : window\n};\nfunction getDefaultValue$1(props, propKey, defaultStateValues) {\n  if (defaultStateValues === void 0) {\n    defaultStateValues = dropdownDefaultStateValues;\n  }\n  var defaultValue = props[\"default\" + capitalizeString(propKey)];\n  if (defaultValue !== undefined) {\n    return defaultValue;\n  }\n  return defaultStateValues[propKey];\n}\nfunction getInitialValue$1(props, propKey, defaultStateValues) {\n  if (defaultStateValues === void 0) {\n    defaultStateValues = dropdownDefaultStateValues;\n  }\n  var value = props[propKey];\n  if (value !== undefined) {\n    return value;\n  }\n  var initialValue = props[\"initial\" + capitalizeString(propKey)];\n  if (initialValue !== undefined) {\n    return initialValue;\n  }\n  return getDefaultValue$1(props, propKey, defaultStateValues);\n}\nfunction getInitialState$2(props) {\n  var selectedItem = getInitialValue$1(props, 'selectedItem');\n  var isOpen = getInitialValue$1(props, 'isOpen');\n  var highlightedIndex = getInitialValue$1(props, 'highlightedIndex');\n  var inputValue = getInitialValue$1(props, 'inputValue');\n  return {\n    highlightedIndex: highlightedIndex < 0 && selectedItem && isOpen ? props.items.indexOf(selectedItem) : highlightedIndex,\n    isOpen: isOpen,\n    selectedItem: selectedItem,\n    inputValue: inputValue\n  };\n}\nfunction getHighlightedIndexOnOpen(props, state, offset) {\n  var items = props.items,\n    initialHighlightedIndex = props.initialHighlightedIndex,\n    defaultHighlightedIndex = props.defaultHighlightedIndex;\n  var selectedItem = state.selectedItem,\n    highlightedIndex = state.highlightedIndex;\n  if (items.length === 0) {\n    return -1;\n  }\n\n  // initialHighlightedIndex will give value to highlightedIndex on initial state only.\n  if (initialHighlightedIndex !== undefined && highlightedIndex === initialHighlightedIndex) {\n    return initialHighlightedIndex;\n  }\n  if (defaultHighlightedIndex !== undefined) {\n    return defaultHighlightedIndex;\n  }\n  if (selectedItem) {\n    return items.indexOf(selectedItem);\n  }\n  if (offset === 0) {\n    return -1;\n  }\n  return offset < 0 ? items.length - 1 : 0;\n}\n\n/**\n * Reuse the movement tracking of mouse and touch events.\n *\n * @param {boolean} isOpen Whether the dropdown is open or not.\n * @param {Array<Object>} downshiftElementRefs Downshift element refs to track movement (toggleButton, menu etc.)\n * @param {Object} environment Environment where component/hook exists.\n * @param {Function} handleBlur Handler on blur from mouse or touch.\n * @returns {Object} Ref containing whether mouseDown or touchMove event is happening\n */\nfunction useMouseAndTouchTracker(isOpen, downshiftElementRefs, environment, handleBlur) {\n  var mouseAndTouchTrackersRef = useRef({\n    isMouseDown: false,\n    isTouchMove: false\n  });\n  useEffect(function () {\n    if ((environment == null ? void 0 : environment.addEventListener) == null) {\n      return;\n    }\n\n    // The same strategy for checking if a click occurred inside or outside downshift\n    // as in downshift.js.\n    var onMouseDown = function onMouseDown() {\n      mouseAndTouchTrackersRef.current.isMouseDown = true;\n    };\n    var onMouseUp = function onMouseUp(event) {\n      mouseAndTouchTrackersRef.current.isMouseDown = false;\n      if (isOpen && !targetWithinDownshift(event.target, downshiftElementRefs.map(function (ref) {\n        return ref.current;\n      }), environment)) {\n        handleBlur();\n      }\n    };\n    var onTouchStart = function onTouchStart() {\n      mouseAndTouchTrackersRef.current.isTouchMove = false;\n    };\n    var onTouchMove = function onTouchMove() {\n      mouseAndTouchTrackersRef.current.isTouchMove = true;\n    };\n    var onTouchEnd = function onTouchEnd(event) {\n      if (isOpen && !mouseAndTouchTrackersRef.current.isTouchMove && !targetWithinDownshift(event.target, downshiftElementRefs.map(function (ref) {\n        return ref.current;\n      }), environment, false)) {\n        handleBlur();\n      }\n    };\n    environment.addEventListener('mousedown', onMouseDown);\n    environment.addEventListener('mouseup', onMouseUp);\n    environment.addEventListener('touchstart', onTouchStart);\n    environment.addEventListener('touchmove', onTouchMove);\n    environment.addEventListener('touchend', onTouchEnd);\n\n    // eslint-disable-next-line consistent-return\n    return function cleanup() {\n      environment.removeEventListener('mousedown', onMouseDown);\n      environment.removeEventListener('mouseup', onMouseUp);\n      environment.removeEventListener('touchstart', onTouchStart);\n      environment.removeEventListener('touchmove', onTouchMove);\n      environment.removeEventListener('touchend', onTouchEnd);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isOpen, environment]);\n  return mouseAndTouchTrackersRef;\n}\n\n/* istanbul ignore next */\n// eslint-disable-next-line import/no-mutable-exports\nvar useGetterPropsCalledChecker = function useGetterPropsCalledChecker() {\n  return noop;\n};\n/**\n * Custom hook that checks if getter props are called correctly.\n *\n * @param  {...any} propKeys Getter prop names to be handled.\n * @returns {Function} Setter function called inside getter props to set call information.\n */\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n  useGetterPropsCalledChecker = function useGetterPropsCalledChecker() {\n    var isInitialMountRef = useRef(true);\n    for (var _len = arguments.length, propKeys = new Array(_len), _key = 0; _key < _len; _key++) {\n      propKeys[_key] = arguments[_key];\n    }\n    var getterPropsCalledRef = useRef(propKeys.reduce(function (acc, propKey) {\n      acc[propKey] = {};\n      return acc;\n    }, {}));\n    useEffect(function () {\n      Object.keys(getterPropsCalledRef.current).forEach(function (propKey) {\n        var propCallInfo = getterPropsCalledRef.current[propKey];\n        if (isInitialMountRef.current) {\n          if (!Object.keys(propCallInfo).length) {\n            // eslint-disable-next-line no-console\n            console.error(\"downshift: You forgot to call the \" + propKey + \" getter function on your component / element.\");\n            return;\n          }\n        }\n        var suppressRefError = propCallInfo.suppressRefError,\n          refKey = propCallInfo.refKey,\n          elementRef = propCallInfo.elementRef;\n        if ((!elementRef || !elementRef.current) && !suppressRefError) {\n          // eslint-disable-next-line no-console\n          console.error(\"downshift: The ref prop \\\"\" + refKey + \"\\\" from \" + propKey + \" was not applied correctly on your element.\");\n        }\n      });\n      isInitialMountRef.current = false;\n    });\n    var setGetterPropCallInfo = useCallback(function (propKey, suppressRefError, refKey, elementRef) {\n      getterPropsCalledRef.current[propKey] = {\n        suppressRefError: suppressRefError,\n        refKey: refKey,\n        elementRef: elementRef\n      };\n    }, []);\n    return setGetterPropCallInfo;\n  };\n}\nfunction useA11yMessageSetter(getA11yMessage, dependencyArray, _ref2) {\n  var isInitialMount = _ref2.isInitialMount,\n    highlightedIndex = _ref2.highlightedIndex,\n    items = _ref2.items,\n    environment = _ref2.environment,\n    rest = _objectWithoutPropertiesLoose(_ref2, _excluded$3);\n  // Sets a11y status message on changes in state.\n  useEffect(function () {\n    if (isInitialMount || false) {\n      return;\n    }\n    updateA11yStatus(function () {\n      return getA11yMessage(_extends({\n        highlightedIndex: highlightedIndex,\n        highlightedItem: items[highlightedIndex],\n        resultCount: items.length\n      }, rest));\n    }, environment.document);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencyArray);\n}\nfunction useScrollIntoView(_ref3) {\n  var highlightedIndex = _ref3.highlightedIndex,\n    isOpen = _ref3.isOpen,\n    itemRefs = _ref3.itemRefs,\n    getItemNodeFromIndex = _ref3.getItemNodeFromIndex,\n    menuElement = _ref3.menuElement,\n    scrollIntoViewProp = _ref3.scrollIntoView;\n  // used not to scroll on highlight by mouse.\n  var shouldScrollRef = useRef(true);\n  // Scroll on highlighted item if change comes from keyboard.\n  useIsomorphicLayoutEffect(function () {\n    if (highlightedIndex < 0 || !isOpen || !Object.keys(itemRefs.current).length) {\n      return;\n    }\n    if (shouldScrollRef.current === false) {\n      shouldScrollRef.current = true;\n    } else {\n      scrollIntoViewProp(getItemNodeFromIndex(highlightedIndex), menuElement);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [highlightedIndex]);\n  return shouldScrollRef;\n}\n\n// eslint-disable-next-line import/no-mutable-exports\nvar useControlPropsValidator = noop;\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n  useControlPropsValidator = function useControlPropsValidator(_ref4) {\n    var isInitialMount = _ref4.isInitialMount,\n      props = _ref4.props,\n      state = _ref4.state;\n    // used for checking when props are moving from controlled to uncontrolled.\n    var prevPropsRef = useRef(props);\n    useEffect(function () {\n      if (isInitialMount) {\n        return;\n      }\n      validateControlledUnchanged(state, prevPropsRef.current, props);\n      prevPropsRef.current = props;\n    }, [state, props, isInitialMount]);\n  };\n}\n\n/**\n * Handles selection on Enter / Alt + ArrowUp. Closes the menu and resets the highlighted index, unless there is a highlighted.\n * In that case, selects the item and resets to defaults for open state and highlighted idex.\n * @param {Object} props The useCombobox props.\n * @param {number} highlightedIndex The index from the state.\n * @param {boolean} inputValue Also return the input value for state.\n * @returns The changes for the state.\n */\nfunction getChangesOnSelection(props, highlightedIndex, inputValue) {\n  var _props$items;\n  if (inputValue === void 0) {\n    inputValue = true;\n  }\n  var shouldSelect = ((_props$items = props.items) == null ? void 0 : _props$items.length) && highlightedIndex >= 0;\n  return _extends({\n    isOpen: false,\n    highlightedIndex: -1\n  }, shouldSelect && _extends({\n    selectedItem: props.items[highlightedIndex],\n    isOpen: getDefaultValue$1(props, 'isOpen'),\n    highlightedIndex: getDefaultValue$1(props, 'highlightedIndex')\n  }, inputValue && {\n    inputValue: props.itemToString(props.items[highlightedIndex])\n  }));\n}\n\nfunction downshiftCommonReducer(state, action, stateChangeTypes) {\n  var type = action.type,\n    props = action.props;\n  var changes;\n  switch (type) {\n    case stateChangeTypes.ItemMouseMove:\n      changes = {\n        highlightedIndex: action.disabled ? -1 : action.index\n      };\n      break;\n    case stateChangeTypes.MenuMouseLeave:\n      changes = {\n        highlightedIndex: -1\n      };\n      break;\n    case stateChangeTypes.ToggleButtonClick:\n    case stateChangeTypes.FunctionToggleMenu:\n      changes = {\n        isOpen: !state.isOpen,\n        highlightedIndex: state.isOpen ? -1 : getHighlightedIndexOnOpen(props, state, 0)\n      };\n      break;\n    case stateChangeTypes.FunctionOpenMenu:\n      changes = {\n        isOpen: true,\n        highlightedIndex: getHighlightedIndexOnOpen(props, state, 0)\n      };\n      break;\n    case stateChangeTypes.FunctionCloseMenu:\n      changes = {\n        isOpen: false\n      };\n      break;\n    case stateChangeTypes.FunctionSetHighlightedIndex:\n      changes = {\n        highlightedIndex: action.highlightedIndex\n      };\n      break;\n    case stateChangeTypes.FunctionSetInputValue:\n      changes = {\n        inputValue: action.inputValue\n      };\n      break;\n    case stateChangeTypes.FunctionReset:\n      changes = {\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        isOpen: getDefaultValue$1(props, 'isOpen'),\n        selectedItem: getDefaultValue$1(props, 'selectedItem'),\n        inputValue: getDefaultValue$1(props, 'inputValue')\n      };\n      break;\n    default:\n      throw new Error('Reducer called without proper action type.');\n  }\n  return _extends({}, state, changes);\n}\n/* eslint-enable complexity */\n\nfunction getItemIndexByCharacterKey(_a) {\n    var keysSoFar = _a.keysSoFar, highlightedIndex = _a.highlightedIndex, items = _a.items, itemToString = _a.itemToString, getItemNodeFromIndex = _a.getItemNodeFromIndex;\n    var lowerCasedKeysSoFar = keysSoFar.toLowerCase();\n    for (var index = 0; index < items.length; index++) {\n        // if we already have a search query in progress, we also consider the current highlighted item.\n        var offsetIndex = (index + highlightedIndex + (keysSoFar.length < 2 ? 1 : 0)) % items.length;\n        var item = items[offsetIndex];\n        if (item !== undefined &&\n            itemToString(item).toLowerCase().startsWith(lowerCasedKeysSoFar)) {\n            var element = getItemNodeFromIndex(offsetIndex);\n            if (!(element === null || element === void 0 ? void 0 : element.hasAttribute('disabled'))) {\n                return offsetIndex;\n            }\n        }\n    }\n    return highlightedIndex;\n}\nvar propTypes$2 = {\n    items: PropTypes.array.isRequired,\n    itemToString: PropTypes.func,\n    getA11yStatusMessage: PropTypes.func,\n    getA11ySelectionMessage: PropTypes.func,\n    highlightedIndex: PropTypes.number,\n    defaultHighlightedIndex: PropTypes.number,\n    initialHighlightedIndex: PropTypes.number,\n    isOpen: PropTypes.bool,\n    defaultIsOpen: PropTypes.bool,\n    initialIsOpen: PropTypes.bool,\n    selectedItem: PropTypes.any,\n    initialSelectedItem: PropTypes.any,\n    defaultSelectedItem: PropTypes.any,\n    id: PropTypes.string,\n    labelId: PropTypes.string,\n    menuId: PropTypes.string,\n    getItemId: PropTypes.func,\n    toggleButtonId: PropTypes.string,\n    stateReducer: PropTypes.func,\n    onSelectedItemChange: PropTypes.func,\n    onHighlightedIndexChange: PropTypes.func,\n    onStateChange: PropTypes.func,\n    onIsOpenChange: PropTypes.func,\n    environment: PropTypes.shape({\n        addEventListener: PropTypes.func,\n        removeEventListener: PropTypes.func,\n        document: PropTypes.shape({\n            getElementById: PropTypes.func,\n            activeElement: PropTypes.any,\n            body: PropTypes.any\n        })\n    })\n};\n/**\n * Default implementation for status message. Only added when menu is open.\n * Will specift if there are results in the list, and if so, how many,\n * and what keys are relevant.\n *\n * @param {Object} param the downshift state and other relevant properties\n * @return {String} the a11y status message\n */\nfunction getA11yStatusMessage(_a) {\n    var isOpen = _a.isOpen, resultCount = _a.resultCount, previousResultCount = _a.previousResultCount;\n    if (!isOpen) {\n        return '';\n    }\n    if (!resultCount) {\n        return 'No results are available.';\n    }\n    if (resultCount !== previousResultCount) {\n        return \"\".concat(resultCount, \" result\").concat(resultCount === 1 ? ' is' : 's are', \" available, use up and down arrow keys to navigate. Press Enter or Space Bar keys to select.\");\n    }\n    return '';\n}\nvar defaultProps$2 = __assign(__assign({}, defaultProps$3), { getA11yStatusMessage: getA11yStatusMessage });\n// eslint-disable-next-line import/no-mutable-exports\nvar validatePropTypes$2 = noop;\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n    validatePropTypes$2 = function (options, caller) {\n        PropTypes.checkPropTypes(propTypes$2, options, 'prop', caller.name);\n    };\n}\n\nvar ToggleButtonClick$1 = process.env.NODE_ENV !== \"production\" ? '__togglebutton_click__' : 0;\nvar ToggleButtonKeyDownArrowDown = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_arrow_down__' : 1;\nvar ToggleButtonKeyDownArrowUp = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_arrow_up__' : 2;\nvar ToggleButtonKeyDownCharacter = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_character__' : 3;\nvar ToggleButtonKeyDownEscape = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_escape__' : 4;\nvar ToggleButtonKeyDownHome = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_home__' : 5;\nvar ToggleButtonKeyDownEnd = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_end__' : 6;\nvar ToggleButtonKeyDownEnter = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_enter__' : 7;\nvar ToggleButtonKeyDownSpaceButton = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_space_button__' : 8;\nvar ToggleButtonKeyDownPageUp = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_page_up__' : 9;\nvar ToggleButtonKeyDownPageDown = process.env.NODE_ENV !== \"production\" ? '__togglebutton_keydown_page_down__' : 10;\nvar ToggleButtonBlur = process.env.NODE_ENV !== \"production\" ? '__togglebutton_blur__' : 11;\nvar MenuMouseLeave$1 = process.env.NODE_ENV !== \"production\" ? '__menu_mouse_leave__' : 12;\nvar ItemMouseMove$1 = process.env.NODE_ENV !== \"production\" ? '__item_mouse_move__' : 13;\nvar ItemClick$1 = process.env.NODE_ENV !== \"production\" ? '__item_click__' : 14;\nvar FunctionToggleMenu$1 = process.env.NODE_ENV !== \"production\" ? '__function_toggle_menu__' : 15;\nvar FunctionOpenMenu$1 = process.env.NODE_ENV !== \"production\" ? '__function_open_menu__' : 16;\nvar FunctionCloseMenu$1 = process.env.NODE_ENV !== \"production\" ? '__function_close_menu__' : 17;\nvar FunctionSetHighlightedIndex$1 = process.env.NODE_ENV !== \"production\" ? '__function_set_highlighted_index__' : 18;\nvar FunctionSelectItem$1 = process.env.NODE_ENV !== \"production\" ? '__function_select_item__' : 19;\nvar FunctionSetInputValue$1 = process.env.NODE_ENV !== \"production\" ? '__function_set_input_value__' : 20;\nvar FunctionReset$2 = process.env.NODE_ENV !== \"production\" ? '__function_reset__' : 21;\n\nvar stateChangeTypes$2 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  ToggleButtonClick: ToggleButtonClick$1,\n  ToggleButtonKeyDownArrowDown: ToggleButtonKeyDownArrowDown,\n  ToggleButtonKeyDownArrowUp: ToggleButtonKeyDownArrowUp,\n  ToggleButtonKeyDownCharacter: ToggleButtonKeyDownCharacter,\n  ToggleButtonKeyDownEscape: ToggleButtonKeyDownEscape,\n  ToggleButtonKeyDownHome: ToggleButtonKeyDownHome,\n  ToggleButtonKeyDownEnd: ToggleButtonKeyDownEnd,\n  ToggleButtonKeyDownEnter: ToggleButtonKeyDownEnter,\n  ToggleButtonKeyDownSpaceButton: ToggleButtonKeyDownSpaceButton,\n  ToggleButtonKeyDownPageUp: ToggleButtonKeyDownPageUp,\n  ToggleButtonKeyDownPageDown: ToggleButtonKeyDownPageDown,\n  ToggleButtonBlur: ToggleButtonBlur,\n  MenuMouseLeave: MenuMouseLeave$1,\n  ItemMouseMove: ItemMouseMove$1,\n  ItemClick: ItemClick$1,\n  FunctionToggleMenu: FunctionToggleMenu$1,\n  FunctionOpenMenu: FunctionOpenMenu$1,\n  FunctionCloseMenu: FunctionCloseMenu$1,\n  FunctionSetHighlightedIndex: FunctionSetHighlightedIndex$1,\n  FunctionSelectItem: FunctionSelectItem$1,\n  FunctionSetInputValue: FunctionSetInputValue$1,\n  FunctionReset: FunctionReset$2\n});\n\n/* eslint-disable complexity */\nfunction downshiftSelectReducer(state, action) {\n  var _props$items;\n  var type = action.type,\n    props = action.props,\n    altKey = action.altKey;\n  var changes;\n  switch (type) {\n    case ItemClick$1:\n      changes = {\n        isOpen: getDefaultValue$1(props, 'isOpen'),\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        selectedItem: props.items[action.index]\n      };\n      break;\n    case ToggleButtonKeyDownCharacter:\n      {\n        var lowercasedKey = action.key;\n        var inputValue = \"\" + state.inputValue + lowercasedKey;\n        var prevHighlightedIndex = !state.isOpen && state.selectedItem ? props.items.indexOf(state.selectedItem) : state.highlightedIndex;\n        var highlightedIndex = getItemIndexByCharacterKey({\n          keysSoFar: inputValue,\n          highlightedIndex: prevHighlightedIndex,\n          items: props.items,\n          itemToString: props.itemToString,\n          getItemNodeFromIndex: action.getItemNodeFromIndex\n        });\n        changes = {\n          inputValue: inputValue,\n          highlightedIndex: highlightedIndex,\n          isOpen: true\n        };\n      }\n      break;\n    case ToggleButtonKeyDownArrowDown:\n      {\n        var _highlightedIndex = state.isOpen ? getNextWrappingIndex(1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false) : altKey && state.selectedItem == null ? -1 : getHighlightedIndexOnOpen(props, state, 1);\n        changes = {\n          highlightedIndex: _highlightedIndex,\n          isOpen: true\n        };\n      }\n      break;\n    case ToggleButtonKeyDownArrowUp:\n      if (state.isOpen && altKey) {\n        changes = getChangesOnSelection(props, state.highlightedIndex, false);\n      } else {\n        var _highlightedIndex2 = state.isOpen ? getNextWrappingIndex(-1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false) : getHighlightedIndexOnOpen(props, state, -1);\n        changes = {\n          highlightedIndex: _highlightedIndex2,\n          isOpen: true\n        };\n      }\n      break;\n    // only triggered when menu is open.\n    case ToggleButtonKeyDownEnter:\n    case ToggleButtonKeyDownSpaceButton:\n      changes = getChangesOnSelection(props, state.highlightedIndex, false);\n      break;\n    case ToggleButtonKeyDownHome:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(1, 0, props.items.length, action.getItemNodeFromIndex, false),\n        isOpen: true\n      };\n      break;\n    case ToggleButtonKeyDownEnd:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(-1, props.items.length - 1, props.items.length, action.getItemNodeFromIndex, false),\n        isOpen: true\n      };\n      break;\n    case ToggleButtonKeyDownPageUp:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(-10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case ToggleButtonKeyDownPageDown:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case ToggleButtonKeyDownEscape:\n      changes = {\n        isOpen: false,\n        highlightedIndex: -1\n      };\n      break;\n    case ToggleButtonBlur:\n      changes = _extends({\n        isOpen: false,\n        highlightedIndex: -1\n      }, state.highlightedIndex >= 0 && ((_props$items = props.items) == null ? void 0 : _props$items.length) && {\n        selectedItem: props.items[state.highlightedIndex]\n      });\n      break;\n    case FunctionSelectItem$1:\n      changes = {\n        selectedItem: action.selectedItem\n      };\n      break;\n    default:\n      return downshiftCommonReducer(state, action, stateChangeTypes$2);\n  }\n  return _extends({}, state, changes);\n}\n/* eslint-enable complexity */\n\nvar _excluded$2 = [\"onMouseLeave\", \"refKey\", \"onKeyDown\", \"onBlur\", \"ref\"],\n  _excluded2$2 = [\"onBlur\", \"onClick\", \"onPress\", \"onKeyDown\", \"refKey\", \"ref\"],\n  _excluded3$1 = [\"item\", \"index\", \"onMouseMove\", \"onClick\", \"onPress\", \"refKey\", \"ref\", \"disabled\"];\nuseSelect.stateChangeTypes = stateChangeTypes$2;\nfunction useSelect(userProps) {\n  if (userProps === void 0) {\n    userProps = {};\n  }\n  validatePropTypes$2(userProps, useSelect);\n  // Props defaults and destructuring.\n  var props = _extends({}, defaultProps$2, userProps);\n  var items = props.items,\n    scrollIntoView = props.scrollIntoView,\n    environment = props.environment,\n    itemToString = props.itemToString,\n    getA11ySelectionMessage = props.getA11ySelectionMessage,\n    getA11yStatusMessage = props.getA11yStatusMessage;\n  // Initial state depending on controlled props.\n  var initialState = getInitialState$2(props);\n  var _useControlledReducer = useControlledReducer$1(downshiftSelectReducer, initialState, props),\n    state = _useControlledReducer[0],\n    dispatch = _useControlledReducer[1];\n  var isOpen = state.isOpen,\n    highlightedIndex = state.highlightedIndex,\n    selectedItem = state.selectedItem,\n    inputValue = state.inputValue;\n\n  // Element efs.\n  var toggleButtonRef = useRef(null);\n  var menuRef = useRef(null);\n  var itemRefs = useRef({});\n  // used to keep the inputValue clearTimeout object between renders.\n  var clearTimeoutRef = useRef(null);\n  // prevent id re-generation between renders.\n  var elementIds = useElementIds(props);\n  // used to keep track of how many items we had on previous cycle.\n  var previousResultCountRef = useRef();\n  var isInitialMountRef = useRef(true);\n  // utility callback to get item element.\n  var latest = useLatestRef({\n    state: state,\n    props: props\n  });\n\n  // Some utils.\n  var getItemNodeFromIndex = useCallback(function (index) {\n    return itemRefs.current[elementIds.getItemId(index)];\n  }, [elementIds]);\n\n  // Effects.\n  // Sets a11y status message on changes in state.\n  useA11yMessageSetter(getA11yStatusMessage, [isOpen, highlightedIndex, inputValue, items], _extends({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Sets a11y status message on changes in selectedItem.\n  useA11yMessageSetter(getA11ySelectionMessage, [selectedItem], _extends({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Scroll on highlighted item if change comes from keyboard.\n  var shouldScrollRef = useScrollIntoView({\n    menuElement: menuRef.current,\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    itemRefs: itemRefs,\n    scrollIntoView: scrollIntoView,\n    getItemNodeFromIndex: getItemNodeFromIndex\n  });\n\n  // Sets cleanup for the keysSoFar callback, debounded after 500ms.\n  useEffect(function () {\n    // init the clean function here as we need access to dispatch.\n    clearTimeoutRef.current = debounce(function (outerDispatch) {\n      outerDispatch({\n        type: FunctionSetInputValue$1,\n        inputValue: ''\n      });\n    }, 500);\n\n    // Cancel any pending debounced calls on mount\n    return function () {\n      clearTimeoutRef.current.cancel();\n    };\n  }, []);\n\n  // Invokes the keysSoFar callback set up above.\n  useEffect(function () {\n    if (!inputValue) {\n      return;\n    }\n    clearTimeoutRef.current(dispatch);\n  }, [dispatch, inputValue]);\n  useControlPropsValidator({\n    isInitialMount: isInitialMountRef.current,\n    props: props,\n    state: state\n  });\n  useEffect(function () {\n    if (isInitialMountRef.current) {\n      return;\n    }\n    previousResultCountRef.current = items.length;\n  });\n  // Add mouse/touch events to document.\n  var mouseAndTouchTrackersRef = useMouseAndTouchTracker(isOpen, [menuRef, toggleButtonRef], environment, function () {\n    dispatch({\n      type: ToggleButtonBlur\n    });\n  });\n  var setGetterPropCallInfo = useGetterPropsCalledChecker('getMenuProps', 'getToggleButtonProps');\n  // Make initial ref false.\n  useEffect(function () {\n    isInitialMountRef.current = false;\n    return function () {\n      isInitialMountRef.current = true;\n    };\n  }, []);\n  // Reset itemRefs on close.\n  useEffect(function () {\n    if (!isOpen) {\n      itemRefs.current = {};\n    }\n  }, [isOpen]);\n\n  // Event handler functions.\n  var toggleButtonKeyDownHandlers = useMemo(function () {\n    return {\n      ArrowDown: function ArrowDown(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownArrowDown,\n          getItemNodeFromIndex: getItemNodeFromIndex,\n          altKey: event.altKey\n        });\n      },\n      ArrowUp: function ArrowUp(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownArrowUp,\n          getItemNodeFromIndex: getItemNodeFromIndex,\n          altKey: event.altKey\n        });\n      },\n      Home: function Home(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownHome,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      End: function End(event) {\n        event.preventDefault();\n        dispatch({\n          type: ToggleButtonKeyDownEnd,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      Escape: function Escape() {\n        if (latest.current.state.isOpen) {\n          dispatch({\n            type: ToggleButtonKeyDownEscape\n          });\n        }\n      },\n      Enter: function Enter(event) {\n        event.preventDefault();\n        dispatch({\n          type: latest.current.state.isOpen ? ToggleButtonKeyDownEnter : ToggleButtonClick$1\n        });\n      },\n      PageUp: function PageUp(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: ToggleButtonKeyDownPageUp,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      },\n      PageDown: function PageDown(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: ToggleButtonKeyDownPageDown,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      },\n      ' ': function _(event) {\n        event.preventDefault();\n        var currentState = latest.current.state;\n        if (!currentState.isOpen) {\n          dispatch({\n            type: ToggleButtonClick$1\n          });\n          return;\n        }\n        if (currentState.inputValue) {\n          dispatch({\n            type: ToggleButtonKeyDownCharacter,\n            key: ' ',\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        } else {\n          dispatch({\n            type: ToggleButtonKeyDownSpaceButton\n          });\n        }\n      }\n    };\n  }, [dispatch, getItemNodeFromIndex, latest]);\n\n  // Action functions.\n  var toggleMenu = useCallback(function () {\n    dispatch({\n      type: FunctionToggleMenu$1\n    });\n  }, [dispatch]);\n  var closeMenu = useCallback(function () {\n    dispatch({\n      type: FunctionCloseMenu$1\n    });\n  }, [dispatch]);\n  var openMenu = useCallback(function () {\n    dispatch({\n      type: FunctionOpenMenu$1\n    });\n  }, [dispatch]);\n  var setHighlightedIndex = useCallback(function (newHighlightedIndex) {\n    dispatch({\n      type: FunctionSetHighlightedIndex$1,\n      highlightedIndex: newHighlightedIndex\n    });\n  }, [dispatch]);\n  var selectItem = useCallback(function (newSelectedItem) {\n    dispatch({\n      type: FunctionSelectItem$1,\n      selectedItem: newSelectedItem\n    });\n  }, [dispatch]);\n  var reset = useCallback(function () {\n    dispatch({\n      type: FunctionReset$2\n    });\n  }, [dispatch]);\n  var setInputValue = useCallback(function (newInputValue) {\n    dispatch({\n      type: FunctionSetInputValue$1,\n      inputValue: newInputValue\n    });\n  }, [dispatch]);\n  // Getter functions.\n  var getLabelProps = useCallback(function (labelProps) {\n    return _extends({\n      id: elementIds.labelId,\n      htmlFor: elementIds.toggleButtonId\n    }, labelProps);\n  }, [elementIds]);\n  var getMenuProps = useCallback(function (_temp, _temp2) {\n    var _extends2;\n    var _ref = _temp === void 0 ? {} : _temp,\n      onMouseLeave = _ref.onMouseLeave,\n      _ref$refKey = _ref.refKey,\n      refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey;\n      _ref.onKeyDown;\n      _ref.onBlur;\n      var ref = _ref.ref,\n      rest = _objectWithoutPropertiesLoose(_ref, _excluded$2);\n    var _ref2 = _temp2 === void 0 ? {} : _temp2,\n      _ref2$suppressRefErro = _ref2.suppressRefError,\n      suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;\n    var menuHandleMouseLeave = function menuHandleMouseLeave() {\n      dispatch({\n        type: MenuMouseLeave$1\n      });\n    };\n    setGetterPropCallInfo('getMenuProps', suppressRefError, refKey, menuRef);\n    return _extends((_extends2 = {}, _extends2[refKey] = handleRefs(ref, function (menuNode) {\n      menuRef.current = menuNode;\n    }), _extends2.id = elementIds.menuId, _extends2.role = 'listbox', _extends2['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends2.onMouseLeave = callAllEventHandlers(onMouseLeave, menuHandleMouseLeave), _extends2), rest);\n  }, [dispatch, setGetterPropCallInfo, elementIds]);\n  var getToggleButtonProps = useCallback(function (_temp3, _temp4) {\n    var _extends3;\n    var _ref3 = _temp3 === void 0 ? {} : _temp3,\n      onBlur = _ref3.onBlur,\n      onClick = _ref3.onClick;\n      _ref3.onPress;\n      var onKeyDown = _ref3.onKeyDown,\n      _ref3$refKey = _ref3.refKey,\n      refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey,\n      ref = _ref3.ref,\n      rest = _objectWithoutPropertiesLoose(_ref3, _excluded2$2);\n    var _ref4 = _temp4 === void 0 ? {} : _temp4,\n      _ref4$suppressRefErro = _ref4.suppressRefError,\n      suppressRefError = _ref4$suppressRefErro === void 0 ? false : _ref4$suppressRefErro;\n    var latestState = latest.current.state;\n    var toggleButtonHandleClick = function toggleButtonHandleClick() {\n      dispatch({\n        type: ToggleButtonClick$1\n      });\n    };\n    var toggleButtonHandleBlur = function toggleButtonHandleBlur() {\n      if (latestState.isOpen && !mouseAndTouchTrackersRef.current.isMouseDown) {\n        dispatch({\n          type: ToggleButtonBlur\n        });\n      }\n    };\n    var toggleButtonHandleKeyDown = function toggleButtonHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && toggleButtonKeyDownHandlers[key]) {\n        toggleButtonKeyDownHandlers[key](event);\n      } else if (isAcceptedCharacterKey(key)) {\n        dispatch({\n          type: ToggleButtonKeyDownCharacter,\n          key: key,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      }\n    };\n    var toggleProps = _extends((_extends3 = {}, _extends3[refKey] = handleRefs(ref, function (toggleButtonNode) {\n      toggleButtonRef.current = toggleButtonNode;\n    }), _extends3['aria-activedescendant'] = latestState.isOpen && latestState.highlightedIndex > -1 ? elementIds.getItemId(latestState.highlightedIndex) : '', _extends3['aria-controls'] = elementIds.menuId, _extends3['aria-expanded'] = latest.current.state.isOpen, _extends3['aria-haspopup'] = 'listbox', _extends3['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends3.id = elementIds.toggleButtonId, _extends3.role = 'combobox', _extends3.tabIndex = 0, _extends3.onBlur = callAllEventHandlers(onBlur, toggleButtonHandleBlur), _extends3), rest);\n    if (!rest.disabled) {\n      /* istanbul ignore if (react-native) */\n      {\n        toggleProps.onClick = callAllEventHandlers(onClick, toggleButtonHandleClick);\n        toggleProps.onKeyDown = callAllEventHandlers(onKeyDown, toggleButtonHandleKeyDown);\n      }\n    }\n    setGetterPropCallInfo('getToggleButtonProps', suppressRefError, refKey, toggleButtonRef);\n    return toggleProps;\n  }, [latest, elementIds, setGetterPropCallInfo, dispatch, mouseAndTouchTrackersRef, toggleButtonKeyDownHandlers, getItemNodeFromIndex]);\n  var getItemProps = useCallback(function (_temp5) {\n    var _extends4;\n    var _ref5 = _temp5 === void 0 ? {} : _temp5,\n      itemProp = _ref5.item,\n      indexProp = _ref5.index,\n      onMouseMove = _ref5.onMouseMove,\n      onClick = _ref5.onClick;\n      _ref5.onPress;\n      var _ref5$refKey = _ref5.refKey,\n      refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey,\n      ref = _ref5.ref,\n      disabled = _ref5.disabled,\n      rest = _objectWithoutPropertiesLoose(_ref5, _excluded3$1);\n    var _latest$current = latest.current,\n      latestState = _latest$current.state,\n      latestProps = _latest$current.props;\n    var _getItemAndIndex = getItemAndIndex(itemProp, indexProp, latestProps.items, 'Pass either item or index to getItemProps!'),\n      item = _getItemAndIndex[0],\n      index = _getItemAndIndex[1];\n    var itemHandleMouseMove = function itemHandleMouseMove() {\n      if (index === latestState.highlightedIndex) {\n        return;\n      }\n      shouldScrollRef.current = false;\n      dispatch({\n        type: ItemMouseMove$1,\n        index: index,\n        disabled: disabled\n      });\n    };\n    var itemHandleClick = function itemHandleClick() {\n      dispatch({\n        type: ItemClick$1,\n        index: index\n      });\n    };\n    var itemProps = _extends((_extends4 = {\n      disabled: disabled,\n      role: 'option',\n      'aria-selected': \"\" + (item === selectedItem),\n      id: elementIds.getItemId(index)\n    }, _extends4[refKey] = handleRefs(ref, function (itemNode) {\n      if (itemNode) {\n        itemRefs.current[elementIds.getItemId(index)] = itemNode;\n      }\n    }), _extends4), rest);\n    if (!disabled) {\n      /* istanbul ignore next (react-native) */\n      {\n        itemProps.onClick = callAllEventHandlers(onClick, itemHandleClick);\n      }\n    }\n    itemProps.onMouseMove = callAllEventHandlers(onMouseMove, itemHandleMouseMove);\n    return itemProps;\n  }, [latest, selectedItem, elementIds, shouldScrollRef, dispatch]);\n  return {\n    // prop getters.\n    getToggleButtonProps: getToggleButtonProps,\n    getLabelProps: getLabelProps,\n    getMenuProps: getMenuProps,\n    getItemProps: getItemProps,\n    // actions.\n    toggleMenu: toggleMenu,\n    openMenu: openMenu,\n    closeMenu: closeMenu,\n    setHighlightedIndex: setHighlightedIndex,\n    selectItem: selectItem,\n    reset: reset,\n    setInputValue: setInputValue,\n    // state.\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    selectedItem: selectedItem,\n    inputValue: inputValue\n  };\n}\n\nvar InputKeyDownArrowDown = process.env.NODE_ENV !== \"production\" ? '__input_keydown_arrow_down__' : 0;\nvar InputKeyDownArrowUp = process.env.NODE_ENV !== \"production\" ? '__input_keydown_arrow_up__' : 1;\nvar InputKeyDownEscape = process.env.NODE_ENV !== \"production\" ? '__input_keydown_escape__' : 2;\nvar InputKeyDownHome = process.env.NODE_ENV !== \"production\" ? '__input_keydown_home__' : 3;\nvar InputKeyDownEnd = process.env.NODE_ENV !== \"production\" ? '__input_keydown_end__' : 4;\nvar InputKeyDownPageUp = process.env.NODE_ENV !== \"production\" ? '__input_keydown_page_up__' : 5;\nvar InputKeyDownPageDown = process.env.NODE_ENV !== \"production\" ? '__input_keydown_page_down__' : 6;\nvar InputKeyDownEnter = process.env.NODE_ENV !== \"production\" ? '__input_keydown_enter__' : 7;\nvar InputChange = process.env.NODE_ENV !== \"production\" ? '__input_change__' : 8;\nvar InputBlur = process.env.NODE_ENV !== \"production\" ? '__input_blur__' : 9;\nvar InputFocus = process.env.NODE_ENV !== \"production\" ? '__input_focus__' : 10;\nvar MenuMouseLeave = process.env.NODE_ENV !== \"production\" ? '__menu_mouse_leave__' : 11;\nvar ItemMouseMove = process.env.NODE_ENV !== \"production\" ? '__item_mouse_move__' : 12;\nvar ItemClick = process.env.NODE_ENV !== \"production\" ? '__item_click__' : 13;\nvar ToggleButtonClick = process.env.NODE_ENV !== \"production\" ? '__togglebutton_click__' : 14;\nvar FunctionToggleMenu = process.env.NODE_ENV !== \"production\" ? '__function_toggle_menu__' : 15;\nvar FunctionOpenMenu = process.env.NODE_ENV !== \"production\" ? '__function_open_menu__' : 16;\nvar FunctionCloseMenu = process.env.NODE_ENV !== \"production\" ? '__function_close_menu__' : 17;\nvar FunctionSetHighlightedIndex = process.env.NODE_ENV !== \"production\" ? '__function_set_highlighted_index__' : 18;\nvar FunctionSelectItem = process.env.NODE_ENV !== \"production\" ? '__function_select_item__' : 19;\nvar FunctionSetInputValue = process.env.NODE_ENV !== \"production\" ? '__function_set_input_value__' : 20;\nvar FunctionReset$1 = process.env.NODE_ENV !== \"production\" ? '__function_reset__' : 21;\nvar ControlledPropUpdatedSelectedItem = process.env.NODE_ENV !== \"production\" ? '__controlled_prop_updated_selected_item__' : 22;\n\nvar stateChangeTypes$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  InputKeyDownArrowDown: InputKeyDownArrowDown,\n  InputKeyDownArrowUp: InputKeyDownArrowUp,\n  InputKeyDownEscape: InputKeyDownEscape,\n  InputKeyDownHome: InputKeyDownHome,\n  InputKeyDownEnd: InputKeyDownEnd,\n  InputKeyDownPageUp: InputKeyDownPageUp,\n  InputKeyDownPageDown: InputKeyDownPageDown,\n  InputKeyDownEnter: InputKeyDownEnter,\n  InputChange: InputChange,\n  InputBlur: InputBlur,\n  InputFocus: InputFocus,\n  MenuMouseLeave: MenuMouseLeave,\n  ItemMouseMove: ItemMouseMove,\n  ItemClick: ItemClick,\n  ToggleButtonClick: ToggleButtonClick,\n  FunctionToggleMenu: FunctionToggleMenu,\n  FunctionOpenMenu: FunctionOpenMenu,\n  FunctionCloseMenu: FunctionCloseMenu,\n  FunctionSetHighlightedIndex: FunctionSetHighlightedIndex,\n  FunctionSelectItem: FunctionSelectItem,\n  FunctionSetInputValue: FunctionSetInputValue,\n  FunctionReset: FunctionReset$1,\n  ControlledPropUpdatedSelectedItem: ControlledPropUpdatedSelectedItem\n});\n\nfunction getInitialState$1(props) {\n  var initialState = getInitialState$2(props);\n  var selectedItem = initialState.selectedItem;\n  var inputValue = initialState.inputValue;\n  if (inputValue === '' && selectedItem && props.defaultInputValue === undefined && props.initialInputValue === undefined && props.inputValue === undefined) {\n    inputValue = props.itemToString(selectedItem);\n  }\n  return _extends({}, initialState, {\n    inputValue: inputValue\n  });\n}\nvar propTypes$1 = {\n  items: PropTypes.array.isRequired,\n  itemToString: PropTypes.func,\n  selectedItemChanged: PropTypes.func,\n  getA11yStatusMessage: PropTypes.func,\n  getA11ySelectionMessage: PropTypes.func,\n  highlightedIndex: PropTypes.number,\n  defaultHighlightedIndex: PropTypes.number,\n  initialHighlightedIndex: PropTypes.number,\n  isOpen: PropTypes.bool,\n  defaultIsOpen: PropTypes.bool,\n  initialIsOpen: PropTypes.bool,\n  selectedItem: PropTypes.any,\n  initialSelectedItem: PropTypes.any,\n  defaultSelectedItem: PropTypes.any,\n  inputValue: PropTypes.string,\n  defaultInputValue: PropTypes.string,\n  initialInputValue: PropTypes.string,\n  id: PropTypes.string,\n  labelId: PropTypes.string,\n  menuId: PropTypes.string,\n  getItemId: PropTypes.func,\n  inputId: PropTypes.string,\n  toggleButtonId: PropTypes.string,\n  stateReducer: PropTypes.func,\n  onSelectedItemChange: PropTypes.func,\n  onHighlightedIndexChange: PropTypes.func,\n  onStateChange: PropTypes.func,\n  onIsOpenChange: PropTypes.func,\n  onInputValueChange: PropTypes.func,\n  environment: PropTypes.shape({\n    addEventListener: PropTypes.func,\n    removeEventListener: PropTypes.func,\n    document: PropTypes.shape({\n      getElementById: PropTypes.func,\n      activeElement: PropTypes.any,\n      body: PropTypes.any\n    })\n  })\n};\n\n/**\n * The useCombobox version of useControlledReducer, which also\n * checks if the controlled prop selectedItem changed between\n * renders. If so, it will also update inputValue with its\n * string equivalent. It uses the common useEnhancedReducer to\n * compute the rest of the state.\n *\n * @param {Function} reducer Reducer function from downshift.\n * @param {Object} initialState Initial state of the hook.\n * @param {Object} props The hook props.\n * @returns {Array} An array with the state and an action dispatcher.\n */\nfunction useControlledReducer(reducer, initialState, props) {\n  var previousSelectedItemRef = useRef();\n  var _useEnhancedReducer = useEnhancedReducer(reducer, initialState, props),\n    state = _useEnhancedReducer[0],\n    dispatch = _useEnhancedReducer[1];\n\n  // ToDo: if needed, make same approach as selectedItemChanged from Downshift.\n  useEffect(function () {\n    if (!isControlledProp(props, 'selectedItem')) {\n      return;\n    }\n    if (props.selectedItemChanged(previousSelectedItemRef.current, props.selectedItem)) {\n      dispatch({\n        type: ControlledPropUpdatedSelectedItem,\n        inputValue: props.itemToString(props.selectedItem)\n      });\n    }\n    previousSelectedItemRef.current = state.selectedItem === previousSelectedItemRef.current ? props.selectedItem : state.selectedItem;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [state.selectedItem, props.selectedItem]);\n  return [getState(state, props), dispatch];\n}\n\n// eslint-disable-next-line import/no-mutable-exports\nvar validatePropTypes$1 = noop;\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n  validatePropTypes$1 = function validatePropTypes(options, caller) {\n    PropTypes.checkPropTypes(propTypes$1, options, 'prop', caller.name);\n  };\n}\nvar defaultProps$1 = _extends({}, defaultProps$3, {\n  selectedItemChanged: function selectedItemChanged(prevItem, item) {\n    return prevItem !== item;\n  },\n  getA11yStatusMessage: getA11yStatusMessage$1\n});\n\n/* eslint-disable complexity */\nfunction downshiftUseComboboxReducer(state, action) {\n  var _props$items;\n  var type = action.type,\n    props = action.props,\n    altKey = action.altKey;\n  var changes;\n  switch (type) {\n    case ItemClick:\n      changes = {\n        isOpen: getDefaultValue$1(props, 'isOpen'),\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        selectedItem: props.items[action.index],\n        inputValue: props.itemToString(props.items[action.index])\n      };\n      break;\n    case InputKeyDownArrowDown:\n      if (state.isOpen) {\n        changes = {\n          highlightedIndex: getNextWrappingIndex(1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, true)\n        };\n      } else {\n        changes = {\n          highlightedIndex: altKey && state.selectedItem == null ? -1 : getHighlightedIndexOnOpen(props, state, 1, action.getItemNodeFromIndex),\n          isOpen: props.items.length >= 0\n        };\n      }\n      break;\n    case InputKeyDownArrowUp:\n      if (state.isOpen) {\n        if (altKey) {\n          changes = getChangesOnSelection(props, state.highlightedIndex);\n        } else {\n          changes = {\n            highlightedIndex: getNextWrappingIndex(-1, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, true)\n          };\n        }\n      } else {\n        changes = {\n          highlightedIndex: getHighlightedIndexOnOpen(props, state, -1, action.getItemNodeFromIndex),\n          isOpen: props.items.length >= 0\n        };\n      }\n      break;\n    case InputKeyDownEnter:\n      changes = getChangesOnSelection(props, state.highlightedIndex);\n      break;\n    case InputKeyDownEscape:\n      changes = _extends({\n        isOpen: false,\n        highlightedIndex: -1\n      }, !state.isOpen && {\n        selectedItem: null,\n        inputValue: ''\n      });\n      break;\n    case InputKeyDownPageUp:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(-10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputKeyDownPageDown:\n      changes = {\n        highlightedIndex: getNextWrappingIndex(10, state.highlightedIndex, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputKeyDownHome:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(1, 0, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputKeyDownEnd:\n      changes = {\n        highlightedIndex: getNextNonDisabledIndex(-1, props.items.length - 1, props.items.length, action.getItemNodeFromIndex, false)\n      };\n      break;\n    case InputBlur:\n      changes = _extends({\n        isOpen: false,\n        highlightedIndex: -1\n      }, state.highlightedIndex >= 0 && ((_props$items = props.items) == null ? void 0 : _props$items.length) && action.selectItem && {\n        selectedItem: props.items[state.highlightedIndex],\n        inputValue: props.itemToString(props.items[state.highlightedIndex])\n      });\n      break;\n    case InputChange:\n      changes = {\n        isOpen: true,\n        highlightedIndex: getDefaultValue$1(props, 'highlightedIndex'),\n        inputValue: action.inputValue\n      };\n      break;\n    case InputFocus:\n      changes = {\n        isOpen: true,\n        highlightedIndex: getHighlightedIndexOnOpen(props, state, 0)\n      };\n      break;\n    case FunctionSelectItem:\n      changes = {\n        selectedItem: action.selectedItem,\n        inputValue: props.itemToString(action.selectedItem)\n      };\n      break;\n    case ControlledPropUpdatedSelectedItem:\n      changes = {\n        inputValue: action.inputValue\n      };\n      break;\n    default:\n      return downshiftCommonReducer(state, action, stateChangeTypes$1);\n  }\n  return _extends({}, state, changes);\n}\n/* eslint-enable complexity */\n\nvar _excluded$1 = [\"onMouseLeave\", \"refKey\", \"ref\"],\n  _excluded2$1 = [\"item\", \"index\", \"refKey\", \"ref\", \"onMouseMove\", \"onMouseDown\", \"onClick\", \"onPress\", \"disabled\"],\n  _excluded3 = [\"onClick\", \"onPress\", \"refKey\", \"ref\"],\n  _excluded4 = [\"onKeyDown\", \"onChange\", \"onInput\", \"onFocus\", \"onBlur\", \"onChangeText\", \"refKey\", \"ref\"];\nuseCombobox.stateChangeTypes = stateChangeTypes$1;\nfunction useCombobox(userProps) {\n  if (userProps === void 0) {\n    userProps = {};\n  }\n  validatePropTypes$1(userProps, useCombobox);\n  // Props defaults and destructuring.\n  var props = _extends({}, defaultProps$1, userProps);\n  var initialIsOpen = props.initialIsOpen,\n    defaultIsOpen = props.defaultIsOpen,\n    items = props.items,\n    scrollIntoView = props.scrollIntoView,\n    environment = props.environment,\n    getA11yStatusMessage = props.getA11yStatusMessage,\n    getA11ySelectionMessage = props.getA11ySelectionMessage,\n    itemToString = props.itemToString;\n  // Initial state depending on controlled props.\n  var initialState = getInitialState$1(props);\n  var _useControlledReducer = useControlledReducer(downshiftUseComboboxReducer, initialState, props),\n    state = _useControlledReducer[0],\n    dispatch = _useControlledReducer[1];\n  var isOpen = state.isOpen,\n    highlightedIndex = state.highlightedIndex,\n    selectedItem = state.selectedItem,\n    inputValue = state.inputValue;\n\n  // Element refs.\n  var menuRef = useRef(null);\n  var itemRefs = useRef({});\n  var inputRef = useRef(null);\n  var toggleButtonRef = useRef(null);\n  var isInitialMountRef = useRef(true);\n  // prevent id re-generation between renders.\n  var elementIds = useElementIds(props);\n  // used to keep track of how many items we had on previous cycle.\n  var previousResultCountRef = useRef();\n  // utility callback to get item element.\n  var latest = useLatestRef({\n    state: state,\n    props: props\n  });\n  var getItemNodeFromIndex = useCallback(function (index) {\n    return itemRefs.current[elementIds.getItemId(index)];\n  }, [elementIds]);\n\n  // Effects.\n  // Sets a11y status message on changes in state.\n  useA11yMessageSetter(getA11yStatusMessage, [isOpen, highlightedIndex, inputValue, items], _extends({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Sets a11y status message on changes in selectedItem.\n  useA11yMessageSetter(getA11ySelectionMessage, [selectedItem], _extends({\n    isInitialMount: isInitialMountRef.current,\n    previousResultCount: previousResultCountRef.current,\n    items: items,\n    environment: environment,\n    itemToString: itemToString\n  }, state));\n  // Scroll on highlighted item if change comes from keyboard.\n  var shouldScrollRef = useScrollIntoView({\n    menuElement: menuRef.current,\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    itemRefs: itemRefs,\n    scrollIntoView: scrollIntoView,\n    getItemNodeFromIndex: getItemNodeFromIndex\n  });\n  useControlPropsValidator({\n    isInitialMount: isInitialMountRef.current,\n    props: props,\n    state: state\n  });\n  // Focus the input on first render if required.\n  useEffect(function () {\n    var focusOnOpen = initialIsOpen || defaultIsOpen || isOpen;\n    if (focusOnOpen && inputRef.current) {\n      inputRef.current.focus();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  useEffect(function () {\n    if (isInitialMountRef.current) {\n      return;\n    }\n    previousResultCountRef.current = items.length;\n  });\n  // Add mouse/touch events to document.\n  var mouseAndTouchTrackersRef = useMouseAndTouchTracker(isOpen, [inputRef, menuRef, toggleButtonRef], environment, function () {\n    dispatch({\n      type: InputBlur,\n      selectItem: false\n    });\n  });\n  var setGetterPropCallInfo = useGetterPropsCalledChecker('getInputProps', 'getMenuProps');\n  // Make initial ref false.\n  useEffect(function () {\n    isInitialMountRef.current = false;\n    return function () {\n      isInitialMountRef.current = true;\n    };\n  }, []);\n  // Reset itemRefs on close.\n  useEffect(function () {\n    var _environment$document;\n    if (!isOpen) {\n      itemRefs.current = {};\n    } else if (((_environment$document = environment.document) == null ? void 0 : _environment$document.activeElement) !== inputRef.current) {\n      var _inputRef$current;\n      inputRef == null || (_inputRef$current = inputRef.current) == null ? void 0 : _inputRef$current.focus();\n    }\n  }, [isOpen, environment]);\n\n  /* Event handler functions */\n  var inputKeyDownHandlers = useMemo(function () {\n    return {\n      ArrowDown: function ArrowDown(event) {\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownArrowDown,\n          altKey: event.altKey,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      ArrowUp: function ArrowUp(event) {\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownArrowUp,\n          altKey: event.altKey,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      Home: function Home(event) {\n        if (!latest.current.state.isOpen) {\n          return;\n        }\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownHome,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      End: function End(event) {\n        if (!latest.current.state.isOpen) {\n          return;\n        }\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownEnd,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      Escape: function Escape(event) {\n        var latestState = latest.current.state;\n        if (latestState.isOpen || latestState.inputValue || latestState.selectedItem || latestState.highlightedIndex > -1) {\n          event.preventDefault();\n          dispatch({\n            type: InputKeyDownEscape\n          });\n        }\n      },\n      Enter: function Enter(event) {\n        var latestState = latest.current.state;\n        // if closed or no highlighted index, do nothing.\n        if (!latestState.isOpen || event.which === 229 // if IME composing, wait for next Enter keydown event.\n        ) {\n          return;\n        }\n        event.preventDefault();\n        dispatch({\n          type: InputKeyDownEnter,\n          getItemNodeFromIndex: getItemNodeFromIndex\n        });\n      },\n      PageUp: function PageUp(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: InputKeyDownPageUp,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      },\n      PageDown: function PageDown(event) {\n        if (latest.current.state.isOpen) {\n          event.preventDefault();\n          dispatch({\n            type: InputKeyDownPageDown,\n            getItemNodeFromIndex: getItemNodeFromIndex\n          });\n        }\n      }\n    };\n  }, [dispatch, latest, getItemNodeFromIndex]);\n\n  // Getter props.\n  var getLabelProps = useCallback(function (labelProps) {\n    return _extends({\n      id: elementIds.labelId,\n      htmlFor: elementIds.inputId\n    }, labelProps);\n  }, [elementIds]);\n  var getMenuProps = useCallback(function (_temp, _temp2) {\n    var _extends2;\n    var _ref = _temp === void 0 ? {} : _temp,\n      onMouseLeave = _ref.onMouseLeave,\n      _ref$refKey = _ref.refKey,\n      refKey = _ref$refKey === void 0 ? 'ref' : _ref$refKey,\n      ref = _ref.ref,\n      rest = _objectWithoutPropertiesLoose(_ref, _excluded$1);\n    var _ref2 = _temp2 === void 0 ? {} : _temp2,\n      _ref2$suppressRefErro = _ref2.suppressRefError,\n      suppressRefError = _ref2$suppressRefErro === void 0 ? false : _ref2$suppressRefErro;\n    setGetterPropCallInfo('getMenuProps', suppressRefError, refKey, menuRef);\n    return _extends((_extends2 = {}, _extends2[refKey] = handleRefs(ref, function (menuNode) {\n      menuRef.current = menuNode;\n    }), _extends2.id = elementIds.menuId, _extends2.role = 'listbox', _extends2['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends2.onMouseLeave = callAllEventHandlers(onMouseLeave, function () {\n      dispatch({\n        type: MenuMouseLeave\n      });\n    }), _extends2), rest);\n  }, [dispatch, setGetterPropCallInfo, elementIds]);\n  var getItemProps = useCallback(function (_temp3) {\n    var _extends3, _ref4;\n    var _ref3 = _temp3 === void 0 ? {} : _temp3,\n      itemProp = _ref3.item,\n      indexProp = _ref3.index,\n      _ref3$refKey = _ref3.refKey,\n      refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey,\n      ref = _ref3.ref,\n      onMouseMove = _ref3.onMouseMove,\n      onMouseDown = _ref3.onMouseDown,\n      onClick = _ref3.onClick;\n      _ref3.onPress;\n      var disabled = _ref3.disabled,\n      rest = _objectWithoutPropertiesLoose(_ref3, _excluded2$1);\n    var _latest$current = latest.current,\n      latestProps = _latest$current.props,\n      latestState = _latest$current.state;\n    var _getItemAndIndex = getItemAndIndex(itemProp, indexProp, latestProps.items, 'Pass either item or index to getItemProps!'),\n      index = _getItemAndIndex[1];\n    var onSelectKey = 'onClick';\n    var customClickHandler = onClick;\n    var itemHandleMouseMove = function itemHandleMouseMove() {\n      if (index === latestState.highlightedIndex) {\n        return;\n      }\n      shouldScrollRef.current = false;\n      dispatch({\n        type: ItemMouseMove,\n        index: index,\n        disabled: disabled\n      });\n    };\n    var itemHandleClick = function itemHandleClick() {\n      dispatch({\n        type: ItemClick,\n        index: index\n      });\n    };\n    var itemHandleMouseDown = function itemHandleMouseDown(e) {\n      return e.preventDefault();\n    };\n    return _extends((_extends3 = {}, _extends3[refKey] = handleRefs(ref, function (itemNode) {\n      if (itemNode) {\n        itemRefs.current[elementIds.getItemId(index)] = itemNode;\n      }\n    }), _extends3.disabled = disabled, _extends3.role = 'option', _extends3['aria-selected'] = \"\" + (index === latestState.highlightedIndex), _extends3.id = elementIds.getItemId(index), _extends3), !disabled && (_ref4 = {}, _ref4[onSelectKey] = callAllEventHandlers(customClickHandler, itemHandleClick), _ref4), {\n      onMouseMove: callAllEventHandlers(onMouseMove, itemHandleMouseMove),\n      onMouseDown: callAllEventHandlers(onMouseDown, itemHandleMouseDown)\n    }, rest);\n  }, [dispatch, latest, shouldScrollRef, elementIds]);\n  var getToggleButtonProps = useCallback(function (_temp4) {\n    var _extends4;\n    var _ref5 = _temp4 === void 0 ? {} : _temp4,\n      onClick = _ref5.onClick;\n      _ref5.onPress;\n      var _ref5$refKey = _ref5.refKey,\n      refKey = _ref5$refKey === void 0 ? 'ref' : _ref5$refKey,\n      ref = _ref5.ref,\n      rest = _objectWithoutPropertiesLoose(_ref5, _excluded3);\n    var latestState = latest.current.state;\n    var toggleButtonHandleClick = function toggleButtonHandleClick() {\n      dispatch({\n        type: ToggleButtonClick\n      });\n    };\n    return _extends((_extends4 = {}, _extends4[refKey] = handleRefs(ref, function (toggleButtonNode) {\n      toggleButtonRef.current = toggleButtonNode;\n    }), _extends4['aria-controls'] = elementIds.menuId, _extends4['aria-expanded'] = latestState.isOpen, _extends4.id = elementIds.toggleButtonId, _extends4.tabIndex = -1, _extends4), !rest.disabled && _extends({}, {\n      onClick: callAllEventHandlers(onClick, toggleButtonHandleClick)\n    }), rest);\n  }, [dispatch, latest, elementIds]);\n  var getInputProps = useCallback(function (_temp5, _temp6) {\n    var _extends5;\n    var _ref6 = _temp5 === void 0 ? {} : _temp5,\n      onKeyDown = _ref6.onKeyDown,\n      onChange = _ref6.onChange,\n      onInput = _ref6.onInput,\n      onFocus = _ref6.onFocus,\n      onBlur = _ref6.onBlur;\n      _ref6.onChangeText;\n      var _ref6$refKey = _ref6.refKey,\n      refKey = _ref6$refKey === void 0 ? 'ref' : _ref6$refKey,\n      ref = _ref6.ref,\n      rest = _objectWithoutPropertiesLoose(_ref6, _excluded4);\n    var _ref7 = _temp6 === void 0 ? {} : _temp6,\n      _ref7$suppressRefErro = _ref7.suppressRefError,\n      suppressRefError = _ref7$suppressRefErro === void 0 ? false : _ref7$suppressRefErro;\n    setGetterPropCallInfo('getInputProps', suppressRefError, refKey, inputRef);\n    var latestState = latest.current.state;\n    var inputHandleKeyDown = function inputHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && inputKeyDownHandlers[key]) {\n        inputKeyDownHandlers[key](event);\n      }\n    };\n    var inputHandleChange = function inputHandleChange(event) {\n      dispatch({\n        type: InputChange,\n        inputValue: event.target.value\n      });\n    };\n    var inputHandleBlur = function inputHandleBlur(event) {\n      /* istanbul ignore else */\n      if (latestState.isOpen && !mouseAndTouchTrackersRef.current.isMouseDown) {\n        var isBlurByTabChange = event.relatedTarget === null && environment.document.activeElement !== environment.document.body;\n        dispatch({\n          type: InputBlur,\n          selectItem: !isBlurByTabChange\n        });\n      }\n    };\n    var inputHandleFocus = function inputHandleFocus() {\n      if (!latestState.isOpen) {\n        dispatch({\n          type: InputFocus\n        });\n      }\n    };\n\n    /* istanbul ignore next (preact) */\n    var onChangeKey = 'onChange';\n    var eventHandlers = {};\n    if (!rest.disabled) {\n      var _eventHandlers;\n      eventHandlers = (_eventHandlers = {}, _eventHandlers[onChangeKey] = callAllEventHandlers(onChange, onInput, inputHandleChange), _eventHandlers.onKeyDown = callAllEventHandlers(onKeyDown, inputHandleKeyDown), _eventHandlers.onBlur = callAllEventHandlers(onBlur, inputHandleBlur), _eventHandlers.onFocus = callAllEventHandlers(onFocus, inputHandleFocus), _eventHandlers);\n    }\n    return _extends((_extends5 = {}, _extends5[refKey] = handleRefs(ref, function (inputNode) {\n      inputRef.current = inputNode;\n    }), _extends5['aria-activedescendant'] = latestState.isOpen && latestState.highlightedIndex > -1 ? elementIds.getItemId(latestState.highlightedIndex) : '', _extends5['aria-autocomplete'] = 'list', _extends5['aria-controls'] = elementIds.menuId, _extends5['aria-expanded'] = latestState.isOpen, _extends5['aria-labelledby'] = rest && rest['aria-label'] ? undefined : \"\" + elementIds.labelId, _extends5.autoComplete = 'off', _extends5.id = elementIds.inputId, _extends5.role = 'combobox', _extends5.value = latestState.inputValue, _extends5), eventHandlers, rest);\n  }, [setGetterPropCallInfo, latest, elementIds, inputKeyDownHandlers, dispatch, mouseAndTouchTrackersRef, environment]);\n\n  // returns\n  var toggleMenu = useCallback(function () {\n    dispatch({\n      type: FunctionToggleMenu\n    });\n  }, [dispatch]);\n  var closeMenu = useCallback(function () {\n    dispatch({\n      type: FunctionCloseMenu\n    });\n  }, [dispatch]);\n  var openMenu = useCallback(function () {\n    dispatch({\n      type: FunctionOpenMenu\n    });\n  }, [dispatch]);\n  var setHighlightedIndex = useCallback(function (newHighlightedIndex) {\n    dispatch({\n      type: FunctionSetHighlightedIndex,\n      highlightedIndex: newHighlightedIndex\n    });\n  }, [dispatch]);\n  var selectItem = useCallback(function (newSelectedItem) {\n    dispatch({\n      type: FunctionSelectItem,\n      selectedItem: newSelectedItem\n    });\n  }, [dispatch]);\n  var setInputValue = useCallback(function (newInputValue) {\n    dispatch({\n      type: FunctionSetInputValue,\n      inputValue: newInputValue\n    });\n  }, [dispatch]);\n  var reset = useCallback(function () {\n    dispatch({\n      type: FunctionReset$1\n    });\n  }, [dispatch]);\n  return {\n    // prop getters.\n    getItemProps: getItemProps,\n    getLabelProps: getLabelProps,\n    getMenuProps: getMenuProps,\n    getInputProps: getInputProps,\n    getToggleButtonProps: getToggleButtonProps,\n    // actions.\n    toggleMenu: toggleMenu,\n    openMenu: openMenu,\n    closeMenu: closeMenu,\n    setHighlightedIndex: setHighlightedIndex,\n    setInputValue: setInputValue,\n    selectItem: selectItem,\n    reset: reset,\n    // state.\n    highlightedIndex: highlightedIndex,\n    isOpen: isOpen,\n    selectedItem: selectedItem,\n    inputValue: inputValue\n  };\n}\n\nvar defaultStateValues = {\n  activeIndex: -1,\n  selectedItems: []\n};\n\n/**\n * Returns the initial value for a state key in the following order:\n * 1. controlled prop, 2. initial prop, 3. default prop, 4. default\n * value from Downshift.\n *\n * @param {Object} props Props passed to the hook.\n * @param {string} propKey Props key to generate the value for.\n * @returns {any} The initial value for that prop.\n */\nfunction getInitialValue(props, propKey) {\n  return getInitialValue$1(props, propKey, defaultStateValues);\n}\n\n/**\n * Returns the default value for a state key in the following order:\n * 1. controlled prop, 2. default prop, 3. default value from Downshift.\n *\n * @param {Object} props Props passed to the hook.\n * @param {string} propKey Props key to generate the value for.\n * @returns {any} The initial value for that prop.\n */\nfunction getDefaultValue(props, propKey) {\n  return getDefaultValue$1(props, propKey, defaultStateValues);\n}\n\n/**\n * Gets the initial state based on the provided props. It uses initial, default\n * and controlled props related to state in order to compute the initial value.\n *\n * @param {Object} props Props passed to the hook.\n * @returns {Object} The initial state.\n */\nfunction getInitialState(props) {\n  var activeIndex = getInitialValue(props, 'activeIndex');\n  var selectedItems = getInitialValue(props, 'selectedItems');\n  return {\n    activeIndex: activeIndex,\n    selectedItems: selectedItems\n  };\n}\n\n/**\n * Returns true if dropdown keydown operation is permitted. Should not be\n * allowed on keydown with modifier keys (ctrl, alt, shift, meta), on\n * input element with text content that is either highlighted or selection\n * cursor is not at the starting position.\n *\n * @param {KeyboardEvent} event The event from keydown.\n * @returns {boolean} Whether the operation is allowed.\n */\nfunction isKeyDownOperationPermitted(event) {\n  if (event.shiftKey || event.metaKey || event.ctrlKey || event.altKey) {\n    return false;\n  }\n  var element = event.target;\n  if (element instanceof HTMLInputElement &&\n  // if element is a text input\n  element.value !== '' && (\n  // and we have text in it\n  // and cursor is either not at the start or is currently highlighting text.\n  element.selectionStart !== 0 || element.selectionEnd !== 0)) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * Returns a message to be added to aria-live region when item is removed.\n *\n * @param {Object} selectionParameters Parameters required to build the message.\n * @returns {string} The a11y message.\n */\nfunction getA11yRemovalMessage(selectionParameters) {\n  var removedSelectedItem = selectionParameters.removedSelectedItem,\n    itemToStringLocal = selectionParameters.itemToString;\n  return itemToStringLocal(removedSelectedItem) + \" has been removed.\";\n}\nvar propTypes = {\n  selectedItems: PropTypes.array,\n  initialSelectedItems: PropTypes.array,\n  defaultSelectedItems: PropTypes.array,\n  itemToString: PropTypes.func,\n  getA11yRemovalMessage: PropTypes.func,\n  stateReducer: PropTypes.func,\n  activeIndex: PropTypes.number,\n  initialActiveIndex: PropTypes.number,\n  defaultActiveIndex: PropTypes.number,\n  onActiveIndexChange: PropTypes.func,\n  onSelectedItemsChange: PropTypes.func,\n  keyNavigationNext: PropTypes.string,\n  keyNavigationPrevious: PropTypes.string,\n  environment: PropTypes.shape({\n    addEventListener: PropTypes.func,\n    removeEventListener: PropTypes.func,\n    document: PropTypes.shape({\n      getElementById: PropTypes.func,\n      activeElement: PropTypes.any,\n      body: PropTypes.any\n    })\n  })\n};\nvar defaultProps = {\n  itemToString: defaultProps$3.itemToString,\n  stateReducer: defaultProps$3.stateReducer,\n  environment: defaultProps$3.environment,\n  getA11yRemovalMessage: getA11yRemovalMessage,\n  keyNavigationNext: 'ArrowRight',\n  keyNavigationPrevious: 'ArrowLeft'\n};\n\n// eslint-disable-next-line import/no-mutable-exports\nvar validatePropTypes = noop;\n/* istanbul ignore next */\nif (process.env.NODE_ENV !== 'production') {\n  validatePropTypes = function validatePropTypes(options, caller) {\n    PropTypes.checkPropTypes(propTypes, options, 'prop', caller.name);\n  };\n}\n\nvar SelectedItemClick = process.env.NODE_ENV !== \"production\" ? '__selected_item_click__' : 0;\nvar SelectedItemKeyDownDelete = process.env.NODE_ENV !== \"production\" ? '__selected_item_keydown_delete__' : 1;\nvar SelectedItemKeyDownBackspace = process.env.NODE_ENV !== \"production\" ? '__selected_item_keydown_backspace__' : 2;\nvar SelectedItemKeyDownNavigationNext = process.env.NODE_ENV !== \"production\" ? '__selected_item_keydown_navigation_next__' : 3;\nvar SelectedItemKeyDownNavigationPrevious = process.env.NODE_ENV !== \"production\" ? '__selected_item_keydown_navigation_previous__' : 4;\nvar DropdownKeyDownNavigationPrevious = process.env.NODE_ENV !== \"production\" ? '__dropdown_keydown_navigation_previous__' : 5;\nvar DropdownKeyDownBackspace = process.env.NODE_ENV !== \"production\" ? '__dropdown_keydown_backspace__' : 6;\nvar DropdownClick = process.env.NODE_ENV !== \"production\" ? '__dropdown_click__' : 7;\nvar FunctionAddSelectedItem = process.env.NODE_ENV !== \"production\" ? '__function_add_selected_item__' : 8;\nvar FunctionRemoveSelectedItem = process.env.NODE_ENV !== \"production\" ? '__function_remove_selected_item__' : 9;\nvar FunctionSetSelectedItems = process.env.NODE_ENV !== \"production\" ? '__function_set_selected_items__' : 10;\nvar FunctionSetActiveIndex = process.env.NODE_ENV !== \"production\" ? '__function_set_active_index__' : 11;\nvar FunctionReset = process.env.NODE_ENV !== \"production\" ? '__function_reset__' : 12;\n\nvar stateChangeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  SelectedItemClick: SelectedItemClick,\n  SelectedItemKeyDownDelete: SelectedItemKeyDownDelete,\n  SelectedItemKeyDownBackspace: SelectedItemKeyDownBackspace,\n  SelectedItemKeyDownNavigationNext: SelectedItemKeyDownNavigationNext,\n  SelectedItemKeyDownNavigationPrevious: SelectedItemKeyDownNavigationPrevious,\n  DropdownKeyDownNavigationPrevious: DropdownKeyDownNavigationPrevious,\n  DropdownKeyDownBackspace: DropdownKeyDownBackspace,\n  DropdownClick: DropdownClick,\n  FunctionAddSelectedItem: FunctionAddSelectedItem,\n  FunctionRemoveSelectedItem: FunctionRemoveSelectedItem,\n  FunctionSetSelectedItems: FunctionSetSelectedItems,\n  FunctionSetActiveIndex: FunctionSetActiveIndex,\n  FunctionReset: FunctionReset\n});\n\n/* eslint-disable complexity */\nfunction downshiftMultipleSelectionReducer(state, action) {\n  var type = action.type,\n    index = action.index,\n    props = action.props,\n    selectedItem = action.selectedItem;\n  var activeIndex = state.activeIndex,\n    selectedItems = state.selectedItems;\n  var changes;\n  switch (type) {\n    case SelectedItemClick:\n      changes = {\n        activeIndex: index\n      };\n      break;\n    case SelectedItemKeyDownNavigationPrevious:\n      changes = {\n        activeIndex: activeIndex - 1 < 0 ? 0 : activeIndex - 1\n      };\n      break;\n    case SelectedItemKeyDownNavigationNext:\n      changes = {\n        activeIndex: activeIndex + 1 >= selectedItems.length ? -1 : activeIndex + 1\n      };\n      break;\n    case SelectedItemKeyDownBackspace:\n    case SelectedItemKeyDownDelete:\n      {\n        if (activeIndex < 0) {\n          break;\n        }\n        var newActiveIndex = activeIndex;\n        if (selectedItems.length === 1) {\n          newActiveIndex = -1;\n        } else if (activeIndex === selectedItems.length - 1) {\n          newActiveIndex = selectedItems.length - 2;\n        }\n        changes = _extends({\n          selectedItems: [].concat(selectedItems.slice(0, activeIndex), selectedItems.slice(activeIndex + 1))\n        }, {\n          activeIndex: newActiveIndex\n        });\n        break;\n      }\n    case DropdownKeyDownNavigationPrevious:\n      changes = {\n        activeIndex: selectedItems.length - 1\n      };\n      break;\n    case DropdownKeyDownBackspace:\n      changes = {\n        selectedItems: selectedItems.slice(0, selectedItems.length - 1)\n      };\n      break;\n    case FunctionAddSelectedItem:\n      changes = {\n        selectedItems: [].concat(selectedItems, [selectedItem])\n      };\n      break;\n    case DropdownClick:\n      changes = {\n        activeIndex: -1\n      };\n      break;\n    case FunctionRemoveSelectedItem:\n      {\n        var _newActiveIndex = activeIndex;\n        var selectedItemIndex = selectedItems.indexOf(selectedItem);\n        if (selectedItemIndex < 0) {\n          break;\n        }\n        if (selectedItems.length === 1) {\n          _newActiveIndex = -1;\n        } else if (selectedItemIndex === selectedItems.length - 1) {\n          _newActiveIndex = selectedItems.length - 2;\n        }\n        changes = {\n          selectedItems: [].concat(selectedItems.slice(0, selectedItemIndex), selectedItems.slice(selectedItemIndex + 1)),\n          activeIndex: _newActiveIndex\n        };\n        break;\n      }\n    case FunctionSetSelectedItems:\n      {\n        var newSelectedItems = action.selectedItems;\n        changes = {\n          selectedItems: newSelectedItems\n        };\n        break;\n      }\n    case FunctionSetActiveIndex:\n      {\n        var _newActiveIndex2 = action.activeIndex;\n        changes = {\n          activeIndex: _newActiveIndex2\n        };\n        break;\n      }\n    case FunctionReset:\n      changes = {\n        activeIndex: getDefaultValue(props, 'activeIndex'),\n        selectedItems: getDefaultValue(props, 'selectedItems')\n      };\n      break;\n    default:\n      throw new Error('Reducer called without proper action type.');\n  }\n  return _extends({}, state, changes);\n}\n\nvar _excluded = [\"refKey\", \"ref\", \"onClick\", \"onKeyDown\", \"selectedItem\", \"index\"],\n  _excluded2 = [\"refKey\", \"ref\", \"onKeyDown\", \"onClick\", \"preventKeyAction\"];\nuseMultipleSelection.stateChangeTypes = stateChangeTypes;\nfunction useMultipleSelection(userProps) {\n  if (userProps === void 0) {\n    userProps = {};\n  }\n  validatePropTypes(userProps, useMultipleSelection);\n  // Props defaults and destructuring.\n  var props = _extends({}, defaultProps, userProps);\n  var getA11yRemovalMessage = props.getA11yRemovalMessage,\n    itemToString = props.itemToString,\n    environment = props.environment,\n    keyNavigationNext = props.keyNavigationNext,\n    keyNavigationPrevious = props.keyNavigationPrevious;\n\n  // Reducer init.\n  var _useControlledReducer = useControlledReducer$1(downshiftMultipleSelectionReducer, getInitialState(props), props),\n    state = _useControlledReducer[0],\n    dispatch = _useControlledReducer[1];\n  var activeIndex = state.activeIndex,\n    selectedItems = state.selectedItems;\n\n  // Refs.\n  var isInitialMountRef = useRef(true);\n  var dropdownRef = useRef(null);\n  var previousSelectedItemsRef = useRef(selectedItems);\n  var selectedItemRefs = useRef();\n  selectedItemRefs.current = [];\n  var latest = useLatestRef({\n    state: state,\n    props: props\n  });\n\n  // Effects.\n  /* Sets a11y status message on changes in selectedItem. */\n  useEffect(function () {\n    if (isInitialMountRef.current || false) {\n      return;\n    }\n    if (selectedItems.length < previousSelectedItemsRef.current.length) {\n      var removedSelectedItem = previousSelectedItemsRef.current.find(function (item) {\n        return selectedItems.indexOf(item) < 0;\n      });\n      setStatus(getA11yRemovalMessage({\n        itemToString: itemToString,\n        resultCount: selectedItems.length,\n        removedSelectedItem: removedSelectedItem,\n        activeIndex: activeIndex,\n        activeSelectedItem: selectedItems[activeIndex]\n      }), environment.document);\n    }\n    previousSelectedItemsRef.current = selectedItems;\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedItems.length]);\n  // Sets focus on active item.\n  useEffect(function () {\n    if (isInitialMountRef.current) {\n      return;\n    }\n    if (activeIndex === -1 && dropdownRef.current) {\n      dropdownRef.current.focus();\n    } else if (selectedItemRefs.current[activeIndex]) {\n      selectedItemRefs.current[activeIndex].focus();\n    }\n  }, [activeIndex]);\n  useControlPropsValidator({\n    isInitialMount: isInitialMountRef.current,\n    props: props,\n    state: state\n  });\n  var setGetterPropCallInfo = useGetterPropsCalledChecker('getDropdownProps');\n  // Make initial ref false.\n  useEffect(function () {\n    isInitialMountRef.current = false;\n    return function () {\n      isInitialMountRef.current = true;\n    };\n  }, []);\n\n  // Event handler functions.\n  var selectedItemKeyDownHandlers = useMemo(function () {\n    var _ref;\n    return _ref = {}, _ref[keyNavigationPrevious] = function () {\n      dispatch({\n        type: SelectedItemKeyDownNavigationPrevious\n      });\n    }, _ref[keyNavigationNext] = function () {\n      dispatch({\n        type: SelectedItemKeyDownNavigationNext\n      });\n    }, _ref.Delete = function Delete() {\n      dispatch({\n        type: SelectedItemKeyDownDelete\n      });\n    }, _ref.Backspace = function Backspace() {\n      dispatch({\n        type: SelectedItemKeyDownBackspace\n      });\n    }, _ref;\n  }, [dispatch, keyNavigationNext, keyNavigationPrevious]);\n  var dropdownKeyDownHandlers = useMemo(function () {\n    var _ref2;\n    return _ref2 = {}, _ref2[keyNavigationPrevious] = function (event) {\n      if (isKeyDownOperationPermitted(event)) {\n        dispatch({\n          type: DropdownKeyDownNavigationPrevious\n        });\n      }\n    }, _ref2.Backspace = function Backspace(event) {\n      if (isKeyDownOperationPermitted(event)) {\n        dispatch({\n          type: DropdownKeyDownBackspace\n        });\n      }\n    }, _ref2;\n  }, [dispatch, keyNavigationPrevious]);\n\n  // Getter props.\n  var getSelectedItemProps = useCallback(function (_temp) {\n    var _extends2;\n    var _ref3 = _temp === void 0 ? {} : _temp,\n      _ref3$refKey = _ref3.refKey,\n      refKey = _ref3$refKey === void 0 ? 'ref' : _ref3$refKey,\n      ref = _ref3.ref,\n      onClick = _ref3.onClick,\n      onKeyDown = _ref3.onKeyDown,\n      selectedItemProp = _ref3.selectedItem,\n      indexProp = _ref3.index,\n      rest = _objectWithoutPropertiesLoose(_ref3, _excluded);\n    var latestState = latest.current.state;\n    var _getItemAndIndex = getItemAndIndex(selectedItemProp, indexProp, latestState.selectedItems, 'Pass either item or index to getSelectedItemProps!'),\n      index = _getItemAndIndex[1];\n    var isFocusable = index > -1 && index === latestState.activeIndex;\n    var selectedItemHandleClick = function selectedItemHandleClick() {\n      dispatch({\n        type: SelectedItemClick,\n        index: index\n      });\n    };\n    var selectedItemHandleKeyDown = function selectedItemHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && selectedItemKeyDownHandlers[key]) {\n        selectedItemKeyDownHandlers[key](event);\n      }\n    };\n    return _extends((_extends2 = {}, _extends2[refKey] = handleRefs(ref, function (selectedItemNode) {\n      if (selectedItemNode) {\n        selectedItemRefs.current.push(selectedItemNode);\n      }\n    }), _extends2.tabIndex = isFocusable ? 0 : -1, _extends2.onClick = callAllEventHandlers(onClick, selectedItemHandleClick), _extends2.onKeyDown = callAllEventHandlers(onKeyDown, selectedItemHandleKeyDown), _extends2), rest);\n  }, [dispatch, latest, selectedItemKeyDownHandlers]);\n  var getDropdownProps = useCallback(function (_temp2, _temp3) {\n    var _extends3;\n    var _ref4 = _temp2 === void 0 ? {} : _temp2,\n      _ref4$refKey = _ref4.refKey,\n      refKey = _ref4$refKey === void 0 ? 'ref' : _ref4$refKey,\n      ref = _ref4.ref,\n      onKeyDown = _ref4.onKeyDown,\n      onClick = _ref4.onClick,\n      _ref4$preventKeyActio = _ref4.preventKeyAction,\n      preventKeyAction = _ref4$preventKeyActio === void 0 ? false : _ref4$preventKeyActio,\n      rest = _objectWithoutPropertiesLoose(_ref4, _excluded2);\n    var _ref5 = _temp3 === void 0 ? {} : _temp3,\n      _ref5$suppressRefErro = _ref5.suppressRefError,\n      suppressRefError = _ref5$suppressRefErro === void 0 ? false : _ref5$suppressRefErro;\n    setGetterPropCallInfo('getDropdownProps', suppressRefError, refKey, dropdownRef);\n    var dropdownHandleKeyDown = function dropdownHandleKeyDown(event) {\n      var key = normalizeArrowKey(event);\n      if (key && dropdownKeyDownHandlers[key]) {\n        dropdownKeyDownHandlers[key](event);\n      }\n    };\n    var dropdownHandleClick = function dropdownHandleClick() {\n      dispatch({\n        type: DropdownClick\n      });\n    };\n    return _extends((_extends3 = {}, _extends3[refKey] = handleRefs(ref, function (dropdownNode) {\n      if (dropdownNode) {\n        dropdownRef.current = dropdownNode;\n      }\n    }), _extends3), !preventKeyAction && {\n      onKeyDown: callAllEventHandlers(onKeyDown, dropdownHandleKeyDown),\n      onClick: callAllEventHandlers(onClick, dropdownHandleClick)\n    }, rest);\n  }, [dispatch, dropdownKeyDownHandlers, setGetterPropCallInfo]);\n\n  // returns\n  var addSelectedItem = useCallback(function (selectedItem) {\n    dispatch({\n      type: FunctionAddSelectedItem,\n      selectedItem: selectedItem\n    });\n  }, [dispatch]);\n  var removeSelectedItem = useCallback(function (selectedItem) {\n    dispatch({\n      type: FunctionRemoveSelectedItem,\n      selectedItem: selectedItem\n    });\n  }, [dispatch]);\n  var setSelectedItems = useCallback(function (newSelectedItems) {\n    dispatch({\n      type: FunctionSetSelectedItems,\n      selectedItems: newSelectedItems\n    });\n  }, [dispatch]);\n  var setActiveIndex = useCallback(function (newActiveIndex) {\n    dispatch({\n      type: FunctionSetActiveIndex,\n      activeIndex: newActiveIndex\n    });\n  }, [dispatch]);\n  var reset = useCallback(function () {\n    dispatch({\n      type: FunctionReset\n    });\n  }, [dispatch]);\n  return {\n    getSelectedItemProps: getSelectedItemProps,\n    getDropdownProps: getDropdownProps,\n    addSelectedItem: addSelectedItem,\n    removeSelectedItem: removeSelectedItem,\n    setSelectedItems: setSelectedItems,\n    setActiveIndex: setActiveIndex,\n    reset: reset,\n    selectedItems: selectedItems,\n    activeIndex: activeIndex\n  };\n}\n\nexport { Downshift$1 as default, resetIdCounter, useCombobox, useMultipleSelection, useSelect };\n"], "names": [], "mappings": ";;;;;;;AAqXI;AArXJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,IAAI,YAAY;AAEhB;;;;;;;CAOC,GACD,SAAS,OAAO,EAAE;IAChB,OAAO,OAAO,OAAO,aAAa,KAAK;AACzC;AACA,SAAS,QAAQ;AAEjB;;;;CAIC,GACD,SAAS,eAAe,IAAI,EAAE,QAAQ;IACpC,IAAI,CAAC,MAAM;QACT;IACF;IACA,IAAI,UAAU,CAAA,GAAA,kMAAA,CAAA,UAAO,AAAD,EAAE,MAAM;QAC1B,UAAU;QACV,OAAO;QACP,YAAY;IACd;IACA,QAAQ,OAAO,CAAC,SAAU,IAAI;QAC5B,IAAI,KAAK,KAAK,EAAE,EACd,MAAM,KAAK,GAAG,EACd,OAAO,KAAK,IAAI;QAClB,GAAG,SAAS,GAAG;QACf,GAAG,UAAU,GAAG;IAClB;AACF;AAEA;;;;;CAKC,GACD,SAAS,iBAAiB,MAAM,EAAE,KAAK,EAAE,WAAW;IAClD,IAAI,SAAS,WAAW,SAAS,iBAAiB,YAAY,IAAI,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC;IACzG,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,EAAE,EAAE,IAAI;IACxB,IAAI;IACJ,SAAS;QACP,IAAI,WAAW;YACb,aAAa;QACf;IACF;IACA,SAAS;QACP,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAC9B;QACA;QACA,YAAY,WAAW;YACrB,YAAY;YACZ,GAAG,KAAK,CAAC,KAAK,GAAG;QACnB,GAAG;IACL;IACA,QAAQ,MAAM,GAAG;IACjB,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS;IACP,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,MAAM,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;QAC5F,GAAG,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;IAC/B;IACA,OAAO,SAAU,KAAK;QACpB,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,IAAI,QAAQ,GAAG,QAAQ,OAAO,QAAS;YACjH,IAAI,CAAC,QAAQ,EAAE,GAAG,SAAS,CAAC,MAAM;QACpC;QACA,OAAO,IAAI,IAAI,CAAC,SAAU,EAAE;YAC1B,IAAI,IAAI;gBACN,GAAG,KAAK,CAAC,KAAK,GAAG;oBAAC;iBAAM,CAAC,MAAM,CAAC;YAClC;YACA,OAAO,MAAM,uBAAuB,IAAI,MAAM,cAAc,CAAC,kBAAkB,MAAM,WAAW,CAAC,uBAAuB;QAC1H;IACF;AACF;AACA,SAAS;IACP,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;QAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;IAChC;IACA,OAAO,SAAU,IAAI;QACnB,KAAK,OAAO,CAAC,SAAU,GAAG;YACxB,IAAI,OAAO,QAAQ,YAAY;gBAC7B,IAAI;YACN,OAAO,IAAI,KAAK;gBACd,IAAI,OAAO,GAAG;YAChB;QACF;IACF;AACF;AAEA;;;CAGC,GACD,SAAS;IACP,OAAO,OAAO;AAChB;AAEA;;CAEC,GACD,SAAS;IACP,YAAY;AACd;AAEA;;;;;;;CAOC,GACD,SAAS,uBAAuB,KAAK;IACnC,IAAI,SAAS,MAAM,MAAM,EACvB,cAAc,MAAM,WAAW,EAC/B,sBAAsB,MAAM,mBAAmB;IACjD,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IACA,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,IAAI,gBAAgB,qBAAqB;QACvC,OAAO,cAAc,YAAY,CAAC,gBAAgB,IAAI,QAAQ,OAAO,IAAI;IAC3E;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,YAAY,GAAG,EAAE,YAAY;IACpC,MAAM,MAAM,OAAO,CAAC,OAAO,iCAAiC,GAAE,GAAG,CAAC,EAAE,GAAG;IACvE,IAAI,CAAC,OAAO,cAAc;QACxB,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEA;;;CAGC,GACD,SAAS,aAAa,OAAO;IAE3B,+BAA+B;IAC/B,OAAO,OAAO,QAAQ,IAAI,KAAK;AACjC;AAEA;;;CAGC,GACD,SAAS,gBAAgB,OAAO;IAC9B,OAAO,QAAQ,KAAK;AACtB;AAEA;;;;;CAKC,GACD,SAAS,aAAa,MAAM,EAAE,QAAQ;IACpC,sCAAsC;IACtC,QAAQ,KAAK,CAAC,oBAAoB,WAAW,yBAAyB,SAAS;AACjF;AACA,IAAI,YAAY;IAAC;IAAoB;IAAc;IAAU;IAAgB;CAAO;AACpF;;;CAGC,GACD,SAAS,UAAU,KAAK;IACtB,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,IAAI,SAAS,CAAC;IACd,UAAU,OAAO,CAAC,SAAU,CAAC;QAC3B,IAAI,MAAM,cAAc,CAAC,IAAI;YAC3B,MAAM,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;QACtB;IACF;IACA,OAAO;AACT;AAEA;;;;;;;;;;CAUC,GACD,SAAS,SAAS,KAAK,EAAE,KAAK;IAC5B,OAAO,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC,SAAU,SAAS,EAAE,GAAG;QACvD,SAAS,CAAC,IAAI,GAAG,iBAAiB,OAAO,OAAO,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;QACvE,OAAO;IACT,GAAG,CAAC;AACN;AAEA;;;;;;;;CAQC,GACD,SAAS,iBAAiB,KAAK,EAAE,GAAG;IAClC,OAAO,KAAK,CAAC,IAAI,KAAK;AACxB;AAEA;;;;CAIC,GACD,SAAS,kBAAkB,KAAK;IAC9B,IAAI,MAAM,MAAM,GAAG,EACjB,UAAU,MAAM,OAAO;IACzB,6BAA6B,GAC7B,IAAI,WAAW,MAAM,WAAW,MAAM,IAAI,OAAO,CAAC,aAAa,GAAG;QAChE,OAAO,UAAU;IACnB;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,cAAc,GAAG;IACxB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS;AACjD;AAEA;;;;;;;;;;CAUC,GACD,SAAS,qBAAqB,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,oBAAoB,EAAE,QAAQ;IAC5F,IAAI,aAAa,KAAK,GAAG;QACvB,WAAW;IACb;IACA,IAAI,cAAc,GAAG;QACnB,OAAO,CAAC;IACV;IACA,IAAI,iBAAiB,YAAY;IACjC,IAAI,OAAO,cAAc,YAAY,YAAY,KAAK,aAAa,WAAW;QAC5E,YAAY,aAAa,IAAI,CAAC,IAAI,iBAAiB;IACrD;IACA,IAAI,WAAW,YAAY;IAC3B,IAAI,WAAW,GAAG;QAChB,WAAW,WAAW,iBAAiB;IACzC,OAAO,IAAI,WAAW,gBAAgB;QACpC,WAAW,WAAW,IAAI;IAC5B;IACA,IAAI,sBAAsB,wBAAwB,YAAY,UAAU,WAAW,sBAAsB;IACzG,IAAI,wBAAwB,CAAC,GAAG;QAC9B,OAAO,aAAa,YAAY,CAAC,IAAI;IACvC;IACA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,wBAAwB,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,oBAAoB,EAAE,QAAQ;IAC/F,IAAI,qBAAqB,qBAAqB;IAC9C,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,YAAY,CAAC,aAAa;QACvE,OAAO;IACT;IACA,IAAI,aAAa,GAAG;QAClB,IAAK,IAAI,QAAQ,YAAY,GAAG,QAAQ,WAAW,QAAS;YAC1D,IAAI,CAAC,qBAAqB,OAAO,YAAY,CAAC,aAAa;gBACzD,OAAO;YACT;QACF;IACF,OAAO;QACL,IAAK,IAAI,SAAS,YAAY,GAAG,UAAU,GAAG,SAAU;YACtD,IAAI,CAAC,qBAAqB,QAAQ,YAAY,CAAC,aAAa;gBAC1D,OAAO;YACT;QACF;IACF;IACA,IAAI,UAAU;QACZ,OAAO,aAAa,IAAI,wBAAwB,GAAG,GAAG,WAAW,sBAAsB,SAAS,wBAAwB,CAAC,GAAG,YAAY,GAAG,WAAW,sBAAsB;IAC9K;IACA,OAAO,CAAC;AACV;AAEA;;;;;;;;;CASC,GACD,SAAS,sBAAsB,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,kBAAkB;IACvF,IAAI,uBAAuB,KAAK,GAAG;QACjC,qBAAqB;IACvB;IACA,OAAO,kBAAkB,IAAI,CAAC,SAAU,WAAW;QACjD,OAAO,eAAe,CAAC,iBAAiB,aAAa,QAAQ,gBAAgB,sBAAsB,iBAAiB,aAAa,YAAY,QAAQ,CAAC,aAAa,EAAE,YAAY;IACnL;AACF;AAEA,qDAAqD;AACrD,IAAI,8BAA8B;AAClC,wBAAwB,GACxB,wCAA2C;IACzC,8BAA8B,SAAS,4BAA4B,KAAK,EAAE,SAAS,EAAE,SAAS;QAC5F,IAAI,qBAAqB;QACzB,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,SAAU,OAAO;YAC1C,IAAI,SAAS,CAAC,QAAQ,KAAK,aAAa,SAAS,CAAC,QAAQ,KAAK,WAAW;gBACxE,sCAAsC;gBACtC,QAAQ,KAAK,CAAC,8DAA8D,UAAU,4BAA4B;YACpH,OAAO,IAAI,SAAS,CAAC,QAAQ,KAAK,aAAa,SAAS,CAAC,QAAQ,KAAK,WAAW;gBAC/E,sCAAsC;gBACtC,QAAQ,KAAK,CAAC,gEAAgE,UAAU,0BAA0B;YACpH;QACF;IACF;AACF;AAEA,IAAI,gBAAgB,SAAS,SAAU,YAAY;IACjD,aAAa,cAAc,WAAW,GAAG;AAC3C,GAAG;AAEH;;;CAGC,GACD,SAAS,UAAU,MAAM,EAAE,YAAY;IACrC,IAAI,MAAM,aAAa;IACvB,IAAI,CAAC,QAAQ;QACX;IACF;IACA,IAAI,WAAW,GAAG;IAClB,cAAc;AAChB;AAEA;;;;CAIC,GACD,SAAS,aAAa,YAAY;IAChC,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,IAAI,YAAY,aAAa,cAAc,CAAC;IAC5C,IAAI,WAAW;QACb,OAAO;IACT;IACA,YAAY,aAAa,aAAa,CAAC;IACvC,UAAU,YAAY,CAAC,MAAM;IAC7B,UAAU,YAAY,CAAC,QAAQ;IAC/B,UAAU,YAAY,CAAC,aAAa;IACpC,UAAU,YAAY,CAAC,iBAAiB;IACxC,OAAO,MAAM,CAAC,UAAU,KAAK,EAAE;QAC7B,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,SAAS;QACT,UAAU;QACV,OAAO;IACT;IACA,aAAa,IAAI,CAAC,WAAW,CAAC;IAC9B,OAAO;AACT;AAEA,IAAI,UAAU,uCAAwC;AACtD,IAAI,UAAU,uCAAwC;AACtD,IAAI,iBAAiB,uCAAwC;AAC7D,IAAI,iBAAiB,uCAAwC;AAC7D,IAAI,mBAAmB,uCAAwC;AAC/D,IAAI,gBAAgB,uCAAwC;AAC5D,IAAI,eAAe,uCAAwC;AAC3D,IAAI,cAAc,uCAAwC;AAC1D,IAAI,aAAa,uCAAwC;AACzD,IAAI,YAAY,uCAAwC;AACxD,IAAI,YAAY,uCAAwC;AACxD,IAAI,cAAc,uCAAwC;AAC1D,IAAI,qBAAqB,uCAAwC;AACjE,IAAI,cAAc,uCAAwC;AAC1D,IAAI,aAAa,uCAAwC;AACzD,IAAI,oCAAoC,uCAAwC;AAChF,IAAI,WAAW,uCAAwC;AAEvD,IAAI,qBAAqB,WAAW,GAAE,OAAO,MAAM,CAAC;IAClD,WAAW;IACX,SAAS;IACT,SAAS;IACT,gBAAgB;IAChB,gBAAgB;IAChB,kBAAkB;IAClB,eAAe;IACf,cAAc;IACd,aAAa;IACb,YAAY;IACZ,WAAW;IACX,WAAW;IACX,aAAa;IACb,oBAAoB;IACpB,aAAa;IACb,YAAY;IACZ,mCAAmC;IACnC,UAAU;AACZ;AAEA,IAAI,cAAc;IAAC;IAAU;CAAM,EACjC,eAAe;IAAC;IAAW;IAAW;IAAa;IAAW;CAAS,EACvE,eAAe;IAAC;IAAa;IAAU;IAAY;IAAW;CAAe,EAC7E,eAAe;IAAC;IAAU;CAAM,EAChC,aAAa;IAAC;IAAe;IAAe;IAAW;IAAW;IAAS;CAAO;AACpF,IAAI,YAAY,WAAW,GAAE;IAC3B,IAAI,YAAY,WAAW,GAAE,SAAU,UAAU;QAC/C,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW;QAC1B,SAAS,UAAU,MAAM;YACvB,IAAI;YACJ,QAAQ,WAAW,IAAI,CAAC,IAAI,EAAE,WAAW,IAAI;YAC7C,2CAA2C;YAC3C,kEAAkE;YAClE,8EAA8E;YAC9E,MAAM,EAAE,GAAG,MAAM,KAAK,CAAC,EAAE,IAAI,eAAe;YAC5C,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,MAAM,IAAI,MAAM,EAAE,GAAG;YAChD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,OAAO,IAAI,MAAM,EAAE,GAAG;YAClD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,OAAO,IAAI,MAAM,EAAE,GAAG;YAClD,MAAM,SAAS,GAAG,MAAM,KAAK,CAAC,SAAS,IAAI,SAAU,KAAK;gBACxD,OAAO,MAAM,EAAE,GAAG,WAAW;YAC/B;YACA,MAAM,KAAK,GAAG;YACd,MAAM,KAAK,GAAG,EAAE;YAChB,0CAA0C;YAC1C,uDAAuD;YACvD,iDAAiD;YACjD,iDAAiD;YACjD,iCAAiC;YACjC,MAAM,SAAS,GAAG;YAClB,MAAM,mBAAmB,GAAG;YAC5B,MAAM,UAAU,GAAG,EAAE;YACrB;;;OAGC,GACD,MAAM,kBAAkB,GAAG,SAAU,EAAE,EAAE,IAAI;gBAC3C,IAAI,KAAK,WAAW;oBAClB,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,SAAU,CAAC;wBACpD,OAAO,MAAM;oBACf;oBACA;gBACF,GAAG;gBACH,MAAM,UAAU,CAAC,IAAI,CAAC;YACxB;YACA,MAAM,YAAY,GAAG,SAAU,KAAK;gBAClC,MAAM,SAAS,GAAG;YACpB;YACA,MAAM,cAAc,GAAG;gBACrB,MAAM,SAAS,GAAG;YACpB;YACA,MAAM,mBAAmB,GAAG,SAAU,gBAAgB,EAAE,eAAe;gBACrE,IAAI,qBAAqB,KAAK,GAAG;oBAC/B,mBAAmB,MAAM,KAAK,CAAC,uBAAuB;gBACxD;gBACA,IAAI,oBAAoB,KAAK,GAAG;oBAC9B,kBAAkB,CAAC;gBACrB;gBACA,kBAAkB,UAAU;gBAC5B,MAAM,gBAAgB,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;oBAC9B,kBAAkB;gBACpB,GAAG;YACL;YACA,MAAM,cAAc,GAAG,SAAU,EAAE;gBACjC,MAAM,gBAAgB,CAAC;oBACrB,cAAc;oBACd,YAAY;oBACZ,kBAAkB,MAAM,KAAK,CAAC,uBAAuB;oBACrD,QAAQ,MAAM,KAAK,CAAC,aAAa;gBACnC,GAAG;YACL;YACA,MAAM,UAAU,GAAG,SAAU,IAAI,EAAE,eAAe,EAAE,EAAE;gBACpD,kBAAkB,UAAU;gBAC5B,MAAM,gBAAgB,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;oBAC9B,QAAQ,MAAM,KAAK,CAAC,aAAa;oBACjC,kBAAkB,MAAM,KAAK,CAAC,uBAAuB;oBACrD,cAAc;oBACd,YAAY,MAAM,KAAK,CAAC,YAAY,CAAC;gBACvC,GAAG,kBAAkB;YACvB;YACA,MAAM,iBAAiB,GAAG,SAAU,SAAS,EAAE,eAAe,EAAE,EAAE;gBAChE,IAAI,OAAO,MAAM,KAAK,CAAC,UAAU;gBACjC,IAAI,QAAQ,MAAM;oBAChB;gBACF;gBACA,MAAM,UAAU,CAAC,MAAM,iBAAiB;YAC1C;YACA,MAAM,qBAAqB,GAAG,SAAU,eAAe,EAAE,EAAE;gBACzD,OAAO,MAAM,iBAAiB,CAAC,MAAM,QAAQ,GAAG,gBAAgB,EAAE,iBAAiB;YACrF;YACA,iDAAiD;YACjD,8CAA8C;YAC9C,qDAAqD;YACrD,4CAA4C;YAC5C,gEAAgE;YAChE,EAAE;YACF,qDAAqD;YACrD,2BAA2B;YAC3B,MAAM,gBAAgB,GAAG,SAAU,UAAU,EAAE,EAAE;gBAC/C,IAAI,gBAAgB;gBACpB,IAAI,mBAAmB,CAAC;gBACxB,IAAI,uBAAuB,OAAO,eAAe;gBAEjD,kEAAkE;gBAClE,iEAAiE;gBACjE,gEAAgE;gBAChE,kCAAkC;gBAClC,0EAA0E;gBAC1E,IAAI,CAAC,wBAAwB,WAAW,cAAc,CAAC,eAAe;oBACpE,MAAM,KAAK,CAAC,kBAAkB,CAAC,WAAW,UAAU,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,kBAAkB,IAAI;gBACjG;gBACA,OAAO,MAAM,QAAQ,CAAC,SAAU,KAAK;oBACnC,QAAQ,MAAM,QAAQ,CAAC;oBACvB,IAAI,gBAAgB,uBAAuB,WAAW,SAAS;oBAE/D,kEAAkE;oBAClE,gBAAgB,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO;oBAEhD,sEAAsE;oBACtE,2BAA2B;oBAC3B,wEAAwE;oBACxE,iBAAiB,cAAc,cAAc,CAAC;oBAC9C,+DAA+D;oBAC/D,IAAI,YAAY,CAAC;oBACjB,iFAAiF;oBACjF,mFAAmF;oBACnF,iCAAiC;oBACjC,IAAI,kBAAkB,cAAc,YAAY,KAAK,MAAM,YAAY,EAAE;wBACvE,cAAc,cAAc,YAAY;oBAC1C;oBACA,cAAc,IAAI,GAAG,cAAc,IAAI,IAAI;oBAC3C,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,SAAU,GAAG;wBAC9C,sDAAsD;wBACtD,oBAAoB;wBACpB,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE;4BACrC,gBAAgB,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI;wBAC5C;wBACA,8CAA8C;wBAC9C,0DAA0D;wBAC1D,sEAAsE;wBACtE,4DAA4D;wBAC5D,iEAAiE;wBACjE,uEAAuE;wBACvE,IAAI,QAAQ,QAAQ;4BAClB;wBACF;wBACA,aAAa,CAAC,IAAI;wBAClB,qEAAqE;wBACrE,IAAI,CAAC,iBAAiB,MAAM,KAAK,EAAE,MAAM;4BACvC,SAAS,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI;wBACrC;oBACF;oBAEA,+EAA+E;oBAC/E,gFAAgF;oBAChF,IAAI,wBAAwB,cAAc,cAAc,CAAC,eAAe;wBACtE,MAAM,KAAK,CAAC,kBAAkB,CAAC,cAAc,UAAU,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,kBAAkB,IAAI;oBACpG;oBACA,OAAO;gBACT,GAAG;oBACD,gDAAgD;oBAChD,OAAO;oBAEP,wDAAwD;oBACxD,6CAA6C;oBAC7C,IAAI,uBAAuB,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG;oBAClE,IAAI,sBAAsB;wBACxB,MAAM,KAAK,CAAC,aAAa,CAAC,kBAAkB,MAAM,kBAAkB;oBACtE;oBACA,IAAI,gBAAgB;wBAClB,MAAM,KAAK,CAAC,QAAQ,CAAC,WAAW,YAAY,EAAE,MAAM,kBAAkB;oBACxE;oBACA,IAAI,gBAAgB,WAAW;wBAC7B,MAAM,KAAK,CAAC,QAAQ,CAAC,aAAa,MAAM,kBAAkB;oBAC5D;oBACA,iEAAiE;oBACjE,iDAAiD;oBACjD,MAAM,KAAK,CAAC,YAAY,CAAC,kBAAkB,MAAM,kBAAkB;gBACrE;YACF;YACA,iCAAiC;YACjC,MAAM,OAAO,GAAG,SAAU,IAAI;gBAC5B,OAAO,MAAM,SAAS,GAAG;YAC3B;YACA,MAAM,YAAY,GAAG,SAAU,KAAK,EAAE,MAAM;gBAC1C,IAAI;gBACJ,IAAI,OAAO,UAAU,KAAK,IAAI,CAAC,IAAI,OACjC,cAAc,KAAK,MAAM,EACzB,SAAS,gBAAgB,KAAK,IAAI,QAAQ,aAC1C,MAAM,KAAK,GAAG,EACd,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,MAAM;gBAC7C,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,wBAAwB,MAAM,gBAAgB,EAC9C,mBAAmB,0BAA0B,KAAK,IAAI,QAAQ;gBAChE,+EAA+E;gBAC/E,gEAAgE;gBAChE,MAAM,YAAY,CAAC,MAAM,GAAG;gBAC5B,MAAM,YAAY,CAAC,MAAM,GAAG;gBAC5B,MAAM,YAAY,CAAC,gBAAgB,GAAG;gBACtC,IAAI,iBAAiB,MAAM,QAAQ,IACjC,SAAS,eAAe,MAAM;gBAChC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,WAAW,KAAK,MAAM,OAAO,GAAG,UAAU,IAAI,GAAG,YAAY,SAAS,CAAC,gBAAgB,GAAG,QAAQ,SAAS,CAAC,gBAAgB,GAAG,WAAW,SAAS,CAAC,YAAY,GAAG,SAAS,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,kBAAkB,GAAG,MAAM,OAAO,EAAE,SAAS,GAAG;YAClT;YACA,iCAAiC;YACjC,MAAM,eAAe,GAAG;gBACtB,WAAW,SAAS,UAAU,KAAK;oBACjC,IAAI,SAAS,IAAI;oBACjB,MAAM,cAAc;oBACpB,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,EAAE;wBAC1B,IAAI,SAAS,MAAM,QAAQ,GAAG,IAAI;wBAClC,IAAI,CAAC,oBAAoB,CAAC,QAAQ;4BAChC,MAAM;wBACR;oBACF,OAAO;wBACL,IAAI,CAAC,gBAAgB,CAAC;4BACpB,QAAQ;4BACR,MAAM;wBACR,GAAG;4BACD,IAAI,YAAY,OAAO,YAAY;4BACnC,IAAI,YAAY,GAAG;gCACjB,IAAI,kBAAkB,OAAO,QAAQ,IACnC,mBAAmB,gBAAgB,gBAAgB;gCACrD,IAAI,uBAAuB,qBAAqB,GAAG,kBAAkB,WAAW,SAAU,KAAK;oCAC7F,OAAO,OAAO,oBAAoB,CAAC;gCACrC;gCACA,OAAO,mBAAmB,CAAC,sBAAsB;oCAC/C,MAAM;gCACR;4BACF;wBACF;oBACF;gBACF;gBACA,SAAS,SAAS,QAAQ,KAAK;oBAC7B,IAAI,SAAS,IAAI;oBACjB,MAAM,cAAc;oBACpB,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,EAAE;wBAC1B,IAAI,SAAS,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC;wBACpC,IAAI,CAAC,oBAAoB,CAAC,QAAQ;4BAChC,MAAM;wBACR;oBACF,OAAO;wBACL,IAAI,CAAC,gBAAgB,CAAC;4BACpB,QAAQ;4BACR,MAAM;wBACR,GAAG;4BACD,IAAI,YAAY,OAAO,YAAY;4BACnC,IAAI,YAAY,GAAG;gCACjB,IAAI,kBAAkB,OAAO,QAAQ,IACnC,mBAAmB,gBAAgB,gBAAgB;gCACrD,IAAI,uBAAuB,qBAAqB,CAAC,GAAG,kBAAkB,WAAW,SAAU,KAAK;oCAC9F,OAAO,OAAO,oBAAoB,CAAC;gCACrC;gCACA,OAAO,mBAAmB,CAAC,sBAAsB;oCAC/C,MAAM;gCACR;4BACF;wBACF;oBACF;gBACF;gBACA,OAAO,SAAS,MAAM,KAAK;oBACzB,IAAI,MAAM,KAAK,KAAK,KAAK;wBACvB;oBACF;oBACA,IAAI,kBAAkB,IAAI,CAAC,QAAQ,IACjC,SAAS,gBAAgB,MAAM,EAC/B,mBAAmB,gBAAgB,gBAAgB;oBACrD,IAAI,UAAU,oBAAoB,MAAM;wBACtC,MAAM,cAAc;wBACpB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB;wBACvC,IAAI,WAAW,IAAI,CAAC,oBAAoB,CAAC;wBACzC,IAAI,QAAQ,QAAQ,YAAY,SAAS,YAAY,CAAC,aAAa;4BACjE;wBACF;wBACA,IAAI,CAAC,qBAAqB,CAAC;4BACzB,MAAM;wBACR;oBACF;gBACF;gBACA,QAAQ,SAAS,OAAO,KAAK;oBAC3B,MAAM,cAAc;oBACpB,IAAI,CAAC,KAAK,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;wBAClB,MAAM;oBACR,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI;wBACvB,cAAc;wBACd,YAAY;oBACd;gBACF;YACF;YACA,mCAAmC;YACnC,MAAM,qBAAqB,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,eAAe,EAAE;gBAChE,KAAK,SAAS,EAAE,KAAK;oBACnB,MAAM,cAAc;oBACpB,IAAI,CAAC,UAAU,CAAC;wBACd,MAAM;oBACR;gBACF;YACF;YACA,MAAM,oBAAoB,GAAG,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,eAAe,EAAE;gBAC/D,MAAM,SAAS,KAAK,KAAK;oBACvB,IAAI,SAAS,IAAI;oBACjB,IAAI,kBAAkB,IAAI,CAAC,QAAQ,IACjC,SAAS,gBAAgB,MAAM;oBACjC,IAAI,CAAC,QAAQ;wBACX;oBACF;oBACA,MAAM,cAAc;oBACpB,IAAI,YAAY,IAAI,CAAC,YAAY;oBACjC,IAAI,aAAa,KAAK,CAAC,QAAQ;wBAC7B;oBACF;oBAEA,sEAAsE;oBACtE,IAAI,sBAAsB,wBAAwB,GAAG,GAAG,WAAW,SAAU,KAAK;wBAChF,OAAO,OAAO,oBAAoB,CAAC;oBACrC,GAAG;oBACH,IAAI,CAAC,mBAAmB,CAAC,qBAAqB;wBAC5C,MAAM;oBACR;gBACF;gBACA,KAAK,SAAS,IAAI,KAAK;oBACrB,IAAI,SAAS,IAAI;oBACjB,IAAI,kBAAkB,IAAI,CAAC,QAAQ,IACjC,SAAS,gBAAgB,MAAM;oBACjC,IAAI,CAAC,QAAQ;wBACX;oBACF;oBACA,MAAM,cAAc;oBACpB,IAAI,YAAY,IAAI,CAAC,YAAY;oBACjC,IAAI,aAAa,KAAK,CAAC,QAAQ;wBAC7B;oBACF;oBAEA,6EAA6E;oBAC7E,IAAI,sBAAsB,wBAAwB,CAAC,GAAG,YAAY,GAAG,WAAW,SAAU,KAAK;wBAC7F,OAAO,OAAO,oBAAoB,CAAC;oBACrC,GAAG;oBACH,IAAI,CAAC,mBAAmB,CAAC,qBAAqB;wBAC5C,MAAM;oBACR;gBACF;YACF;YACA,MAAM,oBAAoB,GAAG,SAAU,MAAM;gBAC3C,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,UAAU,MAAM,OAAO;gBACvB,MAAM,OAAO;gBACb,IAAI,YAAY,MAAM,SAAS,EAC/B,UAAU,MAAM,OAAO,EACvB,SAAS,MAAM,MAAM,EACrB,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;gBAC9C,IAAI,kBAAkB,MAAM,QAAQ,IAClC,SAAS,gBAAgB,MAAM;gBACjC,IAAI,uBAAuB;oBACzB,SAAS,qBAAqB,SAAS,MAAM,iBAAiB;oBAC9D,WAAW,qBAAqB,WAAW,MAAM,mBAAmB;oBACpE,SAAS,qBAAqB,SAAS,MAAM,iBAAiB;oBAC9D,QAAQ,qBAAqB,QAAQ,MAAM,gBAAgB;gBAC7D;gBACA,IAAI,gBAAgB,KAAK,QAAQ,GAAG,CAAC,IAAI;gBACzC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;oBACd,MAAM;oBACN,MAAM;oBACN,cAAc,SAAS,eAAe;oBACtC,iBAAiB;oBACjB,eAAe;gBACjB,GAAG,eAAe;YACpB;YACA,MAAM,iBAAiB,GAAG,SAAU,KAAK;gBACvC,+CAA+C;gBAC/C,MAAM,cAAc;YACtB;YACA,MAAM,mBAAmB,GAAG,SAAU,KAAK;gBACzC,IAAI,MAAM,kBAAkB;gBAC5B,IAAI,MAAM,qBAAqB,CAAC,IAAI,EAAE;oBACpC,MAAM,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,UAAsB,AAAD,EAAE,QAAQ;gBACvE;YACF;YACA,MAAM,iBAAiB,GAAG,SAAU,KAAK;gBACvC,MAAM,cAAc;gBACpB,+CAA+C;gBAC/C,4CAA4C;gBAC5C,mDAAmD,GACnD,IAAI,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,KAAK,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE;oBAC5F,MAAM,MAAM,CAAC,KAAK;gBACpB;gBACA,yFAAyF;gBACzF,6FAA6F;gBAC7F,uFAAuF;gBACvF,uCAAqC;;gBAIrC,OAAO;oBACL,0EAA0E;oBAC1E,MAAM,kBAAkB,CAAC;wBACvB,OAAO,MAAM,UAAU,CAAC;4BACtB,MAAM;wBACR;oBACF;gBACF;YACF;YACA,MAAM,gBAAgB,GAAG,SAAU,KAAK;gBACtC,IAAI,aAAa,MAAM,MAAM,EAAE,2DAA2D;gBAC1F,sHAAsH;gBACtH,MAAM,kBAAkB,CAAC;oBACvB,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,IAAI,QAAQ,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,KAAK,MAAM,OAAO,KAAK,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,KAAK,WAAW,oFAAoF;sBAC7S;wBACA,MAAM,KAAK,CAAC;4BACV,MAAM;wBACR;oBACF;gBACF;YACF;YACA,sCAAsC;YACtC,qCAAqC;YACrC,MAAM,aAAa,GAAG,SAAU,KAAK;gBACnC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;oBACd,SAAS,MAAM,OAAO;oBACtB,IAAI,MAAM,OAAO;gBACnB,GAAG;YACL;YACA,qCAAqC;YACrC,qCAAqC;YACrC,MAAM,aAAa,GAAG,SAAU,MAAM;gBACpC,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,YAAY,MAAM,SAAS,EAC3B,SAAS,MAAM,MAAM,EACrB,WAAW,MAAM,QAAQ,EACzB,UAAU,MAAM,OAAO;gBACvB,MAAM,YAAY;gBAClB,IAAI,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;gBAClD,IAAI;gBACJ,IAAI,gBAAgB,CAAC;gBAErB,iCAAiC,GACjC;oBACE,cAAc;gBAChB;gBACA,IAAI,kBAAkB,MAAM,QAAQ,IAClC,aAAa,gBAAgB,UAAU,EACvC,SAAS,gBAAgB,MAAM,EAC/B,mBAAmB,gBAAgB,gBAAgB;gBACrD,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAClB,IAAI;oBACJ,gBAAgB,CAAC,iBAAiB,CAAC,GAAG,cAAc,CAAC,YAAY,GAAG,qBAAqB,UAAU,SAAS,MAAM,iBAAiB,GAAG,eAAe,SAAS,GAAG,qBAAqB,WAAW,MAAM,kBAAkB,GAAG,eAAe,MAAM,GAAG,qBAAqB,QAAQ,MAAM,eAAe,GAAG,cAAc;gBACzT;gBACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;oBACd,qBAAqB;oBACrB,yBAAyB,UAAU,OAAO,qBAAqB,YAAY,oBAAoB,IAAI,MAAM,SAAS,CAAC,oBAAoB;oBACvI,iBAAiB,SAAS,MAAM,MAAM,GAAG;oBACzC,mBAAmB,QAAQ,IAAI,CAAC,aAAa,GAAG,YAAY,MAAM,OAAO;oBACzE,2GAA2G;oBAC3G,8EAA8E;oBAC9E,cAAc;oBACd,OAAO;oBACP,IAAI,MAAM,OAAO;gBACnB,GAAG,eAAe;YACpB;YACA,MAAM,kBAAkB,GAAG,SAAU,KAAK;gBACxC,IAAI,MAAM,kBAAkB;gBAC5B,IAAI,OAAO,MAAM,oBAAoB,CAAC,IAAI,EAAE;oBAC1C,MAAM,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,UAAsB,AAAD,EAAE,QAAQ;gBACtE;YACF;YACA,MAAM,iBAAiB,GAAG,SAAU,KAAK;gBACvC,MAAM,gBAAgB,CAAC;oBACrB,MAAM;oBACN,QAAQ;oBACR,YAAY,MAAM,MAAM,CAAC,KAAK;oBAC9B,kBAAkB,MAAM,KAAK,CAAC,uBAAuB;gBACvD;YACF;YACA,MAAM,eAAe,GAAG;gBACtB,0HAA0H;gBAC1H,MAAM,kBAAkB,CAAC;oBACvB,IAAI,0BAA0B,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,IAAI,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa;oBAC7U,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,yBAAyB;wBAClD,MAAM,KAAK,CAAC;4BACV,MAAM;wBACR;oBACF;gBACF;YACF;YACA,qCAAqC;YACrC,oCAAoC;YACpC,MAAM,OAAO,GAAG,SAAU,IAAI;gBAC5B,MAAM,SAAS,GAAG;YACpB;YACA,MAAM,YAAY,GAAG,SAAU,MAAM,EAAE,MAAM;gBAC3C,IAAI;gBACJ,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,eAAe,MAAM,MAAM,EAC3B,SAAS,iBAAiB,KAAK,IAAI,QAAQ,cAC3C,MAAM,MAAM,GAAG,EACf,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;gBAC/C,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,wBAAwB,MAAM,gBAAgB,EAC9C,mBAAmB,0BAA0B,KAAK,IAAI,QAAQ;gBAChE,MAAM,YAAY,CAAC,MAAM,GAAG;gBAC5B,MAAM,YAAY,CAAC,MAAM,GAAG;gBAC5B,MAAM,YAAY,CAAC,gBAAgB,GAAG;gBACtC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,WAAW,KAAK,MAAM,OAAO,GAAG,UAAU,IAAI,GAAG,WAAW,SAAS,CAAC,kBAAkB,GAAG,SAAS,KAAK,CAAC,aAAa,GAAG,OAAO,MAAM,OAAO,EAAE,UAAU,EAAE,GAAG,MAAM,MAAM,EAAE,SAAS,GAAG;YAChP;YACA,oCAAoC;YACpC,oCAAoC;YACpC,MAAM,YAAY,GAAG,SAAU,MAAM;gBACnC,IAAI;gBACJ,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,cAAc,MAAM,WAAW,EAC/B,cAAc,MAAM,WAAW,EAC/B,UAAU,MAAM,OAAO;gBACvB,MAAM,OAAO;gBACb,IAAI,QAAQ,MAAM,KAAK,EACvB,aAAa,MAAM,IAAI,EACvB,OAAO,eAAe,KAAK,IAAI,6EAA8E,aAAa,gBAAgB,UAAU,YACpJ,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;gBAC9C,IAAI,UAAU,WAAW;oBACvB,MAAM,KAAK,CAAC,IAAI,CAAC;oBACjB,QAAQ,MAAM,KAAK,CAAC,OAAO,CAAC;gBAC9B,OAAO;oBACL,MAAM,KAAK,CAAC,MAAM,GAAG;gBACvB;gBACA,IAAI,cAAc;gBAClB,IAAI,qBAAqB;gBACzB,IAAI,uBAAuB,CAAC,wBAAwB;oBAClD,0DAA0D;oBAC1D,gEAAgE;oBAChE,4DAA4D;oBAC5D,aAAa,qBAAqB,aAAa;wBAC7C,IAAI,UAAU,MAAM,QAAQ,GAAG,gBAAgB,EAAE;4BAC/C;wBACF;wBACA,MAAM,mBAAmB,CAAC,OAAO;4BAC/B,MAAM;wBACR;wBAEA,6DAA6D;wBAC7D,6DAA6D;wBAC7D,8DAA8D;wBAC9D,SAAS;wBACT,MAAM,cAAc,GAAG;wBACvB,MAAM,kBAAkB,CAAC;4BACvB,OAAO,MAAM,cAAc,GAAG;wBAChC,GAAG;oBACL;oBACA,aAAa,qBAAqB,aAAa,SAAU,KAAK;wBAC5D,qDAAqD;wBACrD,8DAA8D;wBAC9D,mCAAmC;wBACnC,MAAM,cAAc;oBACtB;gBACF,GAAG,qBAAqB,CAAC,YAAY,GAAG,qBAAqB,oBAAoB;oBAC/E,MAAM,iBAAiB,CAAC,OAAO;wBAC7B,MAAM;oBACR;gBACF,IAAI,qBAAqB;gBAEzB,2DAA2D;gBAC3D,qDAAqD;gBACrD,IAAI,gBAAgB,KAAK,QAAQ,GAAG;oBAClC,aAAa,qBAAqB,WAAW;gBAC/C,IAAI;gBACJ,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;oBACd,IAAI,MAAM,SAAS,CAAC;oBACpB,MAAM;oBACN,iBAAiB,MAAM,QAAQ,GAAG,gBAAgB,KAAK;gBACzD,GAAG,eAAe;YACpB;YACA,oCAAoC;YACpC,MAAM,UAAU,GAAG;gBACjB,MAAM,KAAK,GAAG,EAAE;YAClB;YACA,MAAM,KAAK,GAAG,SAAU,eAAe,EAAE,EAAE;gBACzC,IAAI,oBAAoB,KAAK,GAAG;oBAC9B,kBAAkB,CAAC;gBACrB;gBACA,kBAAkB,UAAU;gBAC5B,MAAM,gBAAgB,CAAC,SAAU,KAAK;oBACpC,IAAI,eAAe,MAAM,YAAY;oBACrC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;wBACd,QAAQ,MAAM,KAAK,CAAC,aAAa;wBACjC,kBAAkB,MAAM,KAAK,CAAC,uBAAuB;wBACrD,YAAY,MAAM,KAAK,CAAC,YAAY,CAAC;oBACvC,GAAG;gBACL,GAAG;YACL;YACA,MAAM,UAAU,GAAG,SAAU,eAAe,EAAE,EAAE;gBAC9C,IAAI,oBAAoB,KAAK,GAAG;oBAC9B,kBAAkB,CAAC;gBACrB;gBACA,kBAAkB,UAAU;gBAC5B,MAAM,gBAAgB,CAAC,SAAU,KAAK;oBACpC,IAAI,SAAS,MAAM,MAAM;oBACzB,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;wBACd,QAAQ,CAAC;oBACX,GAAG,UAAU;wBACX,kBAAkB,MAAM,KAAK,CAAC,uBAAuB;oBACvD,GAAG;gBACL,GAAG;oBACD,IAAI,kBAAkB,MAAM,QAAQ,IAClC,SAAS,gBAAgB,MAAM,EAC/B,mBAAmB,gBAAgB,gBAAgB;oBACrD,IAAI,QAAQ;wBACV,IAAI,MAAM,YAAY,KAAK,KAAK,OAAO,qBAAqB,UAAU;4BACpE,MAAM,mBAAmB,CAAC,kBAAkB;wBAC9C;oBACF;oBACA,OAAO;gBACT;YACF;YACA,MAAM,QAAQ,GAAG,SAAU,EAAE;gBAC3B,MAAM,gBAAgB,CAAC;oBACrB,QAAQ;gBACV,GAAG;YACL;YACA,MAAM,SAAS,GAAG,SAAU,EAAE;gBAC5B,MAAM,gBAAgB,CAAC;oBACrB,QAAQ;gBACV,GAAG;YACL;YACA,MAAM,YAAY,GAAG,SAAS;gBAC5B,IAAI,QAAQ,MAAM,QAAQ;gBAC1B,IAAI,OAAO,MAAM,KAAK,CAAC,MAAM,gBAAgB,CAAC;gBAC9C,IAAI,cAAc,MAAM,YAAY;gBACpC,IAAI,SAAS,MAAM,KAAK,CAAC,oBAAoB,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;oBACrD,cAAc,MAAM,KAAK,CAAC,YAAY;oBACtC,qBAAqB,MAAM,mBAAmB;oBAC9C,aAAa;oBACb,iBAAiB;gBACnB,GAAG;gBACH,MAAM,mBAAmB,GAAG;gBAC5B,UAAU,QAAQ,MAAM,KAAK,CAAC,WAAW,CAAC,QAAQ;YACpD,GAAG;YACH,IAAI,cAAc,MAAM,KAAK,EAC3B,0BAA0B,YAAY,uBAAuB,EAC7D,wBAAwB,YAAY,uBAAuB,EAC3D,oBAAoB,0BAA0B,KAAK,IAAI,0BAA0B,uBACjF,gBAAgB,YAAY,aAAa,EACzC,wBAAwB,YAAY,aAAa,EACjD,UAAU,0BAA0B,KAAK,IAAI,gBAAgB,uBAC7D,wBAAwB,YAAY,iBAAiB,EACrD,cAAc,0BAA0B,KAAK,IAAI,KAAK,uBACtD,wBAAwB,YAAY,mBAAmB,EACvD,gBAAgB,0BAA0B,KAAK,IAAI,OAAO;YAC5D,IAAI,SAAS,MAAM,QAAQ,CAAC;gBAC1B,kBAAkB;gBAClB,QAAQ;gBACR,YAAY;gBACZ,cAAc;YAChB;YACA,IAAI,OAAO,YAAY,IAAI,QAAQ,MAAM,KAAK,CAAC,iBAAiB,KAAK,WAAW;gBAC9E,OAAO,UAAU,GAAG,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO,YAAY;YAClE;YACA,MAAM,KAAK,GAAG;YACd,OAAO;QACT;QACA,IAAI,SAAS,UAAU,SAAS;QAChC;;KAEC,GACD,OAAO,qBAAqB,GAAG,SAAS;YACtC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,SAAU,EAAE;gBAClC,aAAa;YACf;YACA,IAAI,CAAC,UAAU,GAAG,EAAE;QACtB;QAWA,OAAO,QAAQ,GAAG,SAAS,WAAW,YAAY;YAChD,IAAI,iBAAiB,KAAK,GAAG;gBAC3B,eAAe,IAAI,CAAC,KAAK;YAC3B;YACA,OAAO,SAAS,cAAc,IAAI,CAAC,KAAK;QAC1C;QACA,OAAO,YAAY,GAAG,SAAS;YAC7B,0DAA0D;YAC1D,sBAAsB;YACtB,4BAA4B;YAC5B,yBAAyB;YACzB,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM;YACjC,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM;gBAC1B,YAAY,IAAI,CAAC,SAAS;YAC5B,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,WAAW;gBAC7C,YAAY,IAAI,CAAC,KAAK,CAAC,SAAS;YAClC;YACA,OAAO;QACT;QACA,OAAO,oBAAoB,GAAG,SAAS,qBAAqB,KAAK;YAC/D,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC;QACvE;QACA,OAAO,6BAA6B,GAAG,SAAS;YAC9C,uCAAuC,GACvC;gBACE,IAAI,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,GAAG,gBAAgB;gBACrE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,SAAS;YAChD;QACF;QACA,OAAO,oBAAoB,GAAG,SAAS,qBAAqB,MAAM,EAAE,eAAe;YACjF,IAAI,SAAS,IAAI;YACjB,IAAI,YAAY,IAAI,CAAC,YAAY;YACjC,IAAI,kBAAkB,IAAI,CAAC,QAAQ,IACjC,mBAAmB,gBAAgB,gBAAgB;YACrD,IAAI,YAAY,GAAG;gBACjB,IAAI,uBAAuB,qBAAqB,QAAQ,kBAAkB,WAAW,SAAU,KAAK;oBAClG,OAAO,OAAO,oBAAoB,CAAC;gBACrC;gBACA,IAAI,CAAC,mBAAmB,CAAC,sBAAsB;YACjD;QACF;QACA,OAAO,kBAAkB,GAAG,SAAS;YACnC,IAAI,kBAAkB,IAAI,CAAC,QAAQ,IACjC,mBAAmB,gBAAgB,gBAAgB,EACnD,aAAa,gBAAgB,UAAU,EACvC,eAAe,gBAAgB,YAAY,EAC3C,SAAS,gBAAgB,MAAM;YACjC,IAAI,eAAe,IAAI,CAAC,KAAK,CAAC,YAAY;YAC1C,IAAI,KAAK,IAAI,CAAC,EAAE;YAChB,IAAI,eAAe,IAAI,CAAC,YAAY,EAClC,uBAAuB,IAAI,CAAC,oBAAoB,EAChD,gBAAgB,IAAI,CAAC,aAAa,EAClC,eAAe,IAAI,CAAC,YAAY,EAChC,gBAAgB,IAAI,CAAC,aAAa,EAClC,eAAe,IAAI,CAAC,YAAY,EAChC,WAAW,IAAI,CAAC,QAAQ,EACxB,YAAY,IAAI,CAAC,SAAS,EAC1B,aAAa,IAAI,CAAC,UAAU,EAC5B,aAAa,IAAI,CAAC,UAAU,EAC5B,oBAAoB,IAAI,CAAC,iBAAiB,EAC1C,wBAAwB,IAAI,CAAC,qBAAqB,EAClD,sBAAsB,IAAI,CAAC,mBAAmB,EAC9C,iBAAiB,IAAI,CAAC,cAAc,EACpC,aAAa,IAAI,CAAC,UAAU,EAC5B,QAAQ,IAAI,CAAC,KAAK,EAClB,eAAe,IAAI,CAAC,YAAY,EAChC,iBAAiB,IAAI,CAAC,cAAc,EACpC,WAAW,IAAI,CAAC,gBAAgB;YAClC,OAAO;gBACL,eAAe;gBACf,cAAc;gBACd,sBAAsB;gBACtB,eAAe;gBACf,cAAc;gBACd,eAAe;gBACf,cAAc;gBACd,UAAU;gBACV,OAAO;gBACP,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ,YAAY;gBACZ,mBAAmB;gBACnB,uBAAuB;gBACvB,qBAAqB;gBACrB,gBAAgB;gBAChB,YAAY;gBACZ,cAAc;gBACd,gBAAgB;gBAChB,UAAU;gBACV,QAAQ;gBACR,cAAc;gBACd,UAAU;gBACV,IAAI;gBACJ,QAAQ;gBACR,kBAAkB;gBAClB,YAAY;gBACZ,QAAQ;gBACR,cAAc;YAChB;QACF;QACA,OAAO,iBAAiB,GAAG,SAAS;YAClC,IAAI,SAAS,IAAI;YACjB,qCAAqC,GACrC,IAAI,oDAAyB,gBAAgB,CAAC,SAAS,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;gBACtH,oCAAoC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY;YACvE;YAEA,qCAAqC,GACrC;gBACE,4EAA4E;gBAC5E,kFAAkF;gBAClF,yFAAyF;gBACzF,uGAAuG;gBACvG,2BAA2B;gBAC3B,IAAI,cAAc,SAAS;oBACzB,OAAO,WAAW,GAAG;gBACvB;gBACA,IAAI,YAAY,SAAS,UAAU,KAAK;oBACtC,OAAO,WAAW,GAAG;oBACrB,wEAAwE;oBACxE,wCAAwC;oBACxC,IAAI,yBAAyB,sBAAsB,MAAM,MAAM,EAAE;wBAAC,OAAO,SAAS;wBAAE,OAAO,SAAS;qBAAC,EAAE,OAAO,KAAK,CAAC,WAAW;oBAC/H,IAAI,CAAC,0BAA0B,OAAO,QAAQ,GAAG,MAAM,EAAE;wBACvD,OAAO,KAAK,CAAC;4BACX,MAAM;wBACR,GAAG;4BACD,OAAO,OAAO,KAAK,CAAC,YAAY,CAAC,OAAO,kBAAkB;wBAC5D;oBACF;gBACF;gBACA,+EAA+E;gBAC/E,+EAA+E;gBAC/E,kCAAkC;gBAClC,4FAA4F;gBAC5F,wEAAwE;gBACxE,iCAAiC;gBACjC,IAAI,eAAe,SAAS;oBAC1B,OAAO,WAAW,GAAG;gBACvB;gBACA,IAAI,cAAc,SAAS;oBACzB,OAAO,WAAW,GAAG;gBACvB;gBACA,IAAI,aAAa,SAAS,WAAW,KAAK;oBACxC,IAAI,yBAAyB,sBAAsB,MAAM,MAAM,EAAE;wBAAC,OAAO,SAAS;wBAAE,OAAO,SAAS;qBAAC,EAAE,OAAO,KAAK,CAAC,WAAW,EAAE;oBACjI,IAAI,CAAC,OAAO,WAAW,IAAI,CAAC,0BAA0B,OAAO,QAAQ,GAAG,MAAM,EAAE;wBAC9E,OAAO,KAAK,CAAC;4BACX,MAAM;wBACR,GAAG;4BACD,OAAO,OAAO,KAAK,CAAC,YAAY,CAAC,OAAO,kBAAkB;wBAC5D;oBACF;gBACF;gBACA,IAAI,cAAc,IAAI,CAAC,KAAK,CAAC,WAAW;gBACxC,YAAY,gBAAgB,CAAC,aAAa;gBAC1C,YAAY,gBAAgB,CAAC,WAAW;gBACxC,YAAY,gBAAgB,CAAC,cAAc;gBAC3C,YAAY,gBAAgB,CAAC,aAAa;gBAC1C,YAAY,gBAAgB,CAAC,YAAY;gBACzC,IAAI,CAAC,OAAO,GAAG;oBACb,OAAO,qBAAqB;oBAC5B,OAAO,YAAY,CAAC,MAAM;oBAC1B,YAAY,mBAAmB,CAAC,aAAa;oBAC7C,YAAY,mBAAmB,CAAC,WAAW;oBAC3C,YAAY,mBAAmB,CAAC,cAAc;oBAC9C,YAAY,mBAAmB,CAAC,aAAa;oBAC7C,YAAY,mBAAmB,CAAC,YAAY;gBAC9C;YACF;QACF;QACA,OAAO,YAAY,GAAG,SAAS,aAAa,SAAS,EAAE,SAAS;YAC9D,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,YAAY,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,EACnF,0BAA0B,OAAO,gBAAgB;YACnD,IAAI,SAAS,UAAU,gBAAgB,KAAK,YAAY,YAAY,WAClE,uBAAuB,OAAO,gBAAgB;YAChD,IAAI,iBAAiB,2BAA2B,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,MAAM;YAC3F,IAAI,uBAAuB,4BAA4B;YACvD,OAAO,kBAAkB;QAC3B;QACA,OAAO,kBAAkB,GAAG,SAAS,mBAAmB,SAAS,EAAE,SAAS;YAC1E,wCAA2C;gBACzC,4BAA4B,IAAI,CAAC,KAAK,EAAE,WAAW,IAAI,CAAC,KAAK;gBAC7D,qCAAqC,GACrC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;oBACnE,oCAAoC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY;gBACvE;YACF;YACA,IAAI,iBAAiB,IAAI,CAAC,KAAK,EAAE,mBAAmB,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,UAAU,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG;gBACnI,IAAI,CAAC,gBAAgB,CAAC;oBACpB,MAAM;oBACN,YAAY,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY;gBAC7D;YACF;YACA,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,YAAY;gBACnE,IAAI,CAAC,6BAA6B;YACpC;YAEA,uCAAuC,GACvC;gBACE,IAAI,CAAC,YAAY;YACnB;QACF;QACA,OAAO,oBAAoB,GAAG,SAAS;YACrC,IAAI,CAAC,OAAO,IAAI,qBAAqB;QACvC;QACA,OAAO,MAAM,GAAG,SAAS;YACvB,IAAI,WAAW,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YAChD,mEAAmE;YACnE,kEAAkE;YAClE,0BAA0B;YAC1B,IAAI,CAAC,UAAU;YACf,sEAAsE;YACtE,6DAA6D;YAC7D,kEAAkE;YAClE,4BAA4B;YAC5B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;YAC3B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;YAC3B,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG;YACrC,2CAA2C;YAC3C,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;YAC3B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;YAC3B,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG;YACrC,4CAA4C;YAC5C,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;YAC5B,0CAA0C;YAC1C,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;YAC5B,IAAI,UAAU,YAAY,SAAS,IAAI,CAAC,kBAAkB;YAC1D,IAAI,CAAC,SAAS;gBACZ,OAAO;YACT;YACA,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBAC3D,IAAI,oDAAyB,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;oBAChH,oCAAoC,SAAS,IAAI,CAAC,YAAY;gBAChE;gBACA,OAAO;YACT,OAAO,IAAI,aAAa,UAAU;gBAChC,qDAAqD;gBACrD,qCAAqC;gBACrC,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,IAAI,CAAC,YAAY,CAAC,gBAAgB;YAC9E;YAEA,wBAAwB,GACxB,wCAA2C;gBACzC,qDAAqD;gBACrD,mDAAmD;gBAEnD,MAAM,IAAI,MAAM;YAClB;YAEA,wBAAwB,GACxB,OAAO;QACT;QACA,OAAO;IACT,EAAE,6JAAA,CAAA,YAAS;IACX,UAAU,YAAY,GAAG;QACvB,yBAAyB;QACzB,eAAe;QACf,sBAAsB;QACtB,cAAc,SAAS,aAAa,CAAC;YACnC,IAAI,KAAK,MAAM;gBACb,OAAO;YACT;YACA,IAAI,oDAAyB,gBAAgB,cAAc,MAAM,CAAC,EAAE,cAAc,CAAC,aAAa;gBAC9F,sCAAsC;gBACtC,QAAQ,IAAI,CAAC,8MAA8M,+BAA+B;YAC5P;YACA,OAAO,OAAO;QAChB;QACA,eAAe;QACf,oBAAoB;QACpB,cAAc;QACd,UAAU;QACV,UAAU;QACV,cAAc;QACd,qBAAqB,SAAS,oBAAoB,QAAQ,EAAE,IAAI;YAC9D,OAAO,aAAa;QACtB;QACA,aAAa,8BAA8B,GAC3C,OAAO,WAAW,cAAc,CAAC,IAAI;QACrC,cAAc,SAAS,aAAa,KAAK,EAAE,UAAU;YACnD,OAAO;QACT;QACA,kBAAkB;QAClB,gBAAgB;IAClB;IACA,UAAU,gBAAgB,GAAG;IAC7B,OAAO;AACT;AACA,uCAAwC,UAAU,SAAS,GAAG;IAC5D,UAAU,yIAAA,CAAA,UAAS,CAAC,IAAI;IACxB,yBAAyB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACzC,eAAe,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B,yBAAyB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACzC,qBAAqB,yIAAA,CAAA,UAAS,CAAC,GAAG;IAClC,mBAAmB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACnC,eAAe,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B,sBAAsB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACpC,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B,UAAU,yIAAA,CAAA,UAAS,CAAC,IAAI;IACxB,UAAU,yIAAA,CAAA,UAAS,CAAC,IAAI;IACxB,eAAe,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B,oBAAoB,yIAAA,CAAA,UAAS,CAAC,IAAI;IAClC,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B,qBAAqB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACnC,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B,WAAW,yIAAA,CAAA,UAAS,CAAC,MAAM;IAC3B,IAAI,yIAAA,CAAA,UAAS,CAAC,MAAM;IACpB,aAAa,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QAC3B,kBAAkB,yIAAA,CAAA,UAAS,CAAC,IAAI;QAChC,qBAAqB,yIAAA,CAAA,UAAS,CAAC,IAAI;QACnC,UAAU,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YACxB,gBAAgB,yIAAA,CAAA,UAAS,CAAC,IAAI;YAC9B,eAAe,yIAAA,CAAA,UAAS,CAAC,GAAG;YAC5B,MAAM,yIAAA,CAAA,UAAS,CAAC,GAAG;QACrB;IACF;IACA,kBAAkB,yIAAA,CAAA,UAAS,CAAC,IAAI;IAChC,gBAAgB,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC9B,sDAAsD;IACtD,oDAAoD;IACpD,6CAA6C,GAC7C,cAAc,yIAAA,CAAA,UAAS,CAAC,GAAG;IAC3B,QAAQ,yIAAA,CAAA,UAAS,CAAC,IAAI;IACtB,YAAY,yIAAA,CAAA,UAAS,CAAC,MAAM;IAC5B,kBAAkB,yIAAA,CAAA,UAAS,CAAC,MAAM;IAClC,SAAS,yIAAA,CAAA,UAAS,CAAC,MAAM;IACzB,SAAS,yIAAA,CAAA,UAAS,CAAC,MAAM;IACzB,QAAQ,yIAAA,CAAA,UAAS,CAAC,MAAM;IACxB,WAAW,yIAAA,CAAA,UAAS,CAAC,IAAI;AAE3B;AACA,IAAI,cAAc;AAClB,SAAS,oCAAoC,IAAI,EAAE,MAAM;IACvD,IAAI,SAAS,OAAO,MAAM;IAC1B,IAAI,CAAC,MAAM;QACT,sCAAsC;QACtC,QAAQ,KAAK,CAAC,+BAA+B,SAAS;IACxD;AACF;AACA,SAAS,oCAAoC,OAAO,EAAE,MAAM;IAC1D,IAAI,SAAS,OAAO,MAAM;IAC1B,IAAI,kBAAkB,WAAW;IACjC,IAAI,cAAc,CAAC,aAAa;IAChC,IAAI,eAAe,CAAC,mBAAmB,CAAC,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,UAAU;QAC7D,sCAAsC;QACtC,QAAQ,KAAK,CAAC;IAChB,OAAO,IAAI,CAAC,eAAe,iBAAiB;QAC1C,sCAAsC;QACtC,QAAQ,KAAK,CAAC,6GAA6G,SAAS;IACtI;IACA,IAAI,CAAC,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,YAAY,CAAC,gBAAgB,QAAQ,CAAC,OAAO,EAAE;QAC/D,sCAAsC;QACtC,QAAQ,KAAK,CAAC,8CAA8C,SAAS;IACvE;AACF;AAEA,IAAI,cAAc;IAAC;IAAkB;IAAoB;IAAS;CAAc;AAChF,IAAI,6BAA6B;IAC/B,kBAAkB,CAAC;IACnB,QAAQ;IACR,cAAc;IACd,YAAY;AACd;AACA,SAAS,kBAAkB,MAAM,EAAE,KAAK,EAAE,QAAQ;IAChD,IAAI,QAAQ,OAAO,KAAK,EACtB,OAAO,OAAO,IAAI;IACpB,IAAI,UAAU,CAAC;IACf,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,SAAU,GAAG;QACtC,sBAAsB,KAAK,QAAQ,OAAO;QAC1C,IAAI,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;YAChC,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;QAC9B;IACF;IACA,IAAI,MAAM,aAAa,IAAI,OAAO,IAAI,CAAC,SAAS,MAAM,EAAE;QACtD,MAAM,aAAa,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;YAC3B,MAAM;QACR,GAAG;IACL;AACF;AACA,SAAS,sBAAsB,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ;IACzD,IAAI,QAAQ,OAAO,KAAK,EACtB,OAAO,OAAO,IAAI;IACpB,IAAI,UAAU,OAAO,iBAAiB,OAAO;IAC7C,IAAI,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,KAAK,aAAa,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;QACjF,KAAK,CAAC,QAAQ,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;YACtB,MAAM;QACR,GAAG;IACL;AACF;AAEA;;;;;;CAMC,GACD,SAAS,aAAa,CAAC,EAAE,CAAC;IACxB,OAAO,EAAE,OAAO;AAClB;AAEA;;;;;CAKC,GACD,SAAS,wBAAwB,mBAAmB;IAClD,IAAI,eAAe,oBAAoB,YAAY,EACjD,oBAAoB,oBAAoB,YAAY;IACtD,OAAO,eAAe,kBAAkB,gBAAgB,wBAAwB;AAClF;AAEA;;CAEC,GACD,IAAI,mBAAmB,SAAS,SAAU,cAAc,EAAE,SAAQ;IAChE,UAAU,kBAAkB;AAC9B,GAAG;AAEH,uBAAuB;AACvB,IAAI,4BAA4B,OAAO,WAAW,eAAe,OAAO,OAAO,QAAQ,KAAK,eAAe,OAAO,OAAO,QAAQ,CAAC,aAAa,KAAK,cAAc,6JAAA,CAAA,kBAAe,GAAG,6JAAA,CAAA,YAAS;AAC7L,SAAS,cAAc,IAAI;IACzB,IAAI,UAAU,KAAK,EAAE,EACnB,KAAK,YAAY,KAAK,IAAI,eAAe,eAAe,SACxD,UAAU,KAAK,OAAO,EACtB,SAAS,KAAK,MAAM,EACpB,YAAY,KAAK,SAAS,EAC1B,iBAAiB,KAAK,cAAc,EACpC,UAAU,KAAK,OAAO;IACxB,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QACzB,SAAS,WAAW,KAAK;QACzB,QAAQ,UAAU,KAAK;QACvB,WAAW;mDAAa,SAAU,KAAK;gBACrC,OAAO,KAAK,WAAW;YACzB;;QACA,gBAAgB,kBAAkB,KAAK;QACvC,SAAS,WAAW,KAAK;IAC3B;IACA,OAAO,cAAc,OAAO;AAC9B;AACA,SAAS,gBAAgB,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY;IAC/D,IAAI,MAAM;IACV,IAAI,aAAa,WAAW;QAC1B,IAAI,cAAc,WAAW;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,KAAK,CAAC,UAAU;QACvB,QAAQ;IACV,OAAO;QACL,QAAQ,cAAc,YAAY,MAAM,OAAO,CAAC,YAAY;QAC5D,OAAO;IACT;IACA,OAAO;QAAC;QAAM;KAAM;AACtB;AACA,SAAS,aAAa,IAAI;IACxB,OAAO,OAAO,OAAO,QAAQ;AAC/B;AACA,SAAS,uBAAuB,GAAG;IACjC,OAAO,UAAU,IAAI,CAAC;AACxB;AACA,SAAS,iBAAiB,MAAM;IAC9B,OAAO,KAAK,OAAO,KAAK,CAAC,GAAG,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;AAC9D;AACA,SAAS,aAAa,GAAG;IACvB,IAAI,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACjB,4EAA4E;IAC5E,6EAA6E;IAC7E,sEAAsE;IACtE,iEAAiE;IACjE,2EAA2E;IAC3E,IAAI,OAAO,GAAG;IACd,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,mBAAmB,OAAO,EAAE,YAAY,EAAE,KAAK;IACtD,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACxB,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACrB,IAAI,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,SAAU,KAAK,EAAE,MAAM;YACvD,UAAU,OAAO,GAAG;YACpB,QAAQ,SAAS,OAAO,OAAO,KAAK;YACpC,IAAI,UAAU,QAAQ,OAAO;YAC7B,IAAI,WAAW,OAAO,KAAK,CAAC,YAAY,CAAC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,QAAQ;gBACnE,SAAS;YACX;YACA,OAAO;QACT;0DAAG;QAAC;KAAQ;IACZ,IAAI,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,eAC5C,QAAQ,WAAW,CAAC,EAAE,EACtB,WAAW,WAAW,CAAC,EAAE;IAC3B,IAAI,WAAW,aAAa;IAC5B,IAAI,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,SAAU,MAAM;YAClD,OAAO,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;gBACvB,OAAO,SAAS,OAAO;YACzB,GAAG;QACL;4DAAG;QAAC;KAAS;IACb,IAAI,SAAS,UAAU,OAAO;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,UAAU,aAAa,OAAO,IAAI,aAAa,OAAO,KAAK,OAAO;gBACpE,kBAAkB,QAAQ,SAAS,aAAa,OAAO,EAAE,OAAO,KAAK,GAAG;YAC1E;YACA,aAAa,OAAO,GAAG;QACzB;uCAAG;QAAC;QAAO;QAAO;KAAO;IACzB,OAAO;QAAC;QAAO;KAAkB;AACnC;AAEA;;;;;;;;CAQC,GACD,SAAS,uBAAuB,OAAO,EAAE,YAAY,EAAE,KAAK;IAC1D,IAAI,sBAAsB,mBAAmB,SAAS,cAAc,QAClE,QAAQ,mBAAmB,CAAC,EAAE,EAC9B,WAAW,mBAAmB,CAAC,EAAE;IACnC,OAAO;QAAC,SAAS,OAAO;QAAQ;KAAS;AAC3C;AACA,IAAI,iBAAiB;IACnB,cAAc;IACd,cAAc;IACd,yBAAyB;IACzB,gBAAgB;IAChB,aAAa,8BAA8B,GAC3C,OAAO,WAAW,cAAc,CAAC,IAAI;AACvC;AACA,SAAS,kBAAkB,KAAK,EAAE,OAAO,EAAE,kBAAkB;IAC3D,IAAI,uBAAuB,KAAK,GAAG;QACjC,qBAAqB;IACvB;IACA,IAAI,eAAe,KAAK,CAAC,YAAY,iBAAiB,SAAS;IAC/D,IAAI,iBAAiB,WAAW;QAC9B,OAAO;IACT;IACA,OAAO,kBAAkB,CAAC,QAAQ;AACpC;AACA,SAAS,kBAAkB,KAAK,EAAE,OAAO,EAAE,kBAAkB;IAC3D,IAAI,uBAAuB,KAAK,GAAG;QACjC,qBAAqB;IACvB;IACA,IAAI,QAAQ,KAAK,CAAC,QAAQ;IAC1B,IAAI,UAAU,WAAW;QACvB,OAAO;IACT;IACA,IAAI,eAAe,KAAK,CAAC,YAAY,iBAAiB,SAAS;IAC/D,IAAI,iBAAiB,WAAW;QAC9B,OAAO;IACT;IACA,OAAO,kBAAkB,OAAO,SAAS;AAC3C;AACA,SAAS,kBAAkB,KAAK;IAC9B,IAAI,eAAe,kBAAkB,OAAO;IAC5C,IAAI,SAAS,kBAAkB,OAAO;IACtC,IAAI,mBAAmB,kBAAkB,OAAO;IAChD,IAAI,aAAa,kBAAkB,OAAO;IAC1C,OAAO;QACL,kBAAkB,mBAAmB,KAAK,gBAAgB,SAAS,MAAM,KAAK,CAAC,OAAO,CAAC,gBAAgB;QACvG,QAAQ;QACR,cAAc;QACd,YAAY;IACd;AACF;AACA,SAAS,0BAA0B,KAAK,EAAE,KAAK,EAAE,MAAM;IACrD,IAAI,QAAQ,MAAM,KAAK,EACrB,0BAA0B,MAAM,uBAAuB,EACvD,0BAA0B,MAAM,uBAAuB;IACzD,IAAI,eAAe,MAAM,YAAY,EACnC,mBAAmB,MAAM,gBAAgB;IAC3C,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,OAAO,CAAC;IACV;IAEA,qFAAqF;IACrF,IAAI,4BAA4B,aAAa,qBAAqB,yBAAyB;QACzF,OAAO;IACT;IACA,IAAI,4BAA4B,WAAW;QACzC,OAAO;IACT;IACA,IAAI,cAAc;QAChB,OAAO,MAAM,OAAO,CAAC;IACvB;IACA,IAAI,WAAW,GAAG;QAChB,OAAO,CAAC;IACV;IACA,OAAO,SAAS,IAAI,MAAM,MAAM,GAAG,IAAI;AACzC;AAEA;;;;;;;;CAQC,GACD,SAAS,wBAAwB,MAAM,EAAE,oBAAoB,EAAE,WAAW,EAAE,UAAU;IACpF,IAAI,2BAA2B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QACpC,aAAa;QACb,aAAa;IACf;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,IAAI,CAAC,eAAe,OAAO,KAAK,IAAI,YAAY,gBAAgB,KAAK,MAAM;gBACzE;YACF;YAEA,iFAAiF;YACjF,sBAAsB;YACtB,IAAI,cAAc,SAAS;gBACzB,yBAAyB,OAAO,CAAC,WAAW,GAAG;YACjD;YACA,IAAI,YAAY,SAAS,UAAU,KAAK;gBACtC,yBAAyB,OAAO,CAAC,WAAW,GAAG;gBAC/C,IAAI,UAAU,CAAC,sBAAsB,MAAM,MAAM,EAAE,qBAAqB,GAAG;mEAAC,SAAU,GAAG;wBACvF,OAAO,IAAI,OAAO;oBACpB;mEAAI,cAAc;oBAChB;gBACF;YACF;YACA,IAAI,eAAe,SAAS;gBAC1B,yBAAyB,OAAO,CAAC,WAAW,GAAG;YACjD;YACA,IAAI,cAAc,SAAS;gBACzB,yBAAyB,OAAO,CAAC,WAAW,GAAG;YACjD;YACA,IAAI,aAAa,SAAS,WAAW,KAAK;gBACxC,IAAI,UAAU,CAAC,yBAAyB,OAAO,CAAC,WAAW,IAAI,CAAC,sBAAsB,MAAM,MAAM,EAAE,qBAAqB,GAAG;oEAAC,SAAU,GAAG;wBACxI,OAAO,IAAI,OAAO;oBACpB;oEAAI,aAAa,QAAQ;oBACvB;gBACF;YACF;YACA,YAAY,gBAAgB,CAAC,aAAa;YAC1C,YAAY,gBAAgB,CAAC,WAAW;YACxC,YAAY,gBAAgB,CAAC,cAAc;YAC3C,YAAY,gBAAgB,CAAC,aAAa;YAC1C,YAAY,gBAAgB,CAAC,YAAY;YAEzC,6CAA6C;YAC7C,OAAO,SAAS;gBACd,YAAY,mBAAmB,CAAC,aAAa;gBAC7C,YAAY,mBAAmB,CAAC,WAAW;gBAC3C,YAAY,mBAAmB,CAAC,cAAc;gBAC9C,YAAY,mBAAmB,CAAC,aAAa;gBAC7C,YAAY,mBAAmB,CAAC,YAAY;YAC9C;QACA,uDAAuD;QACzD;4CAAG;QAAC;QAAQ;KAAY;IACxB,OAAO;AACT;AAEA,wBAAwB,GACxB,qDAAqD;AACrD,IAAI,8BAA8B,SAAS;IACzC,OAAO;AACT;AACA;;;;;CAKC,GACD,wBAAwB,GACxB,wCAA2C;IACzC,8BAA8B,SAAS;QACrC,IAAI,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QAC/B,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,WAAW,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YAC3F,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAClC;QACA,IAAI,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,SAAS,MAAM;wEAAC,SAAU,GAAG,EAAE,OAAO;gBACtE,GAAG,CAAC,QAAQ,GAAG,CAAC;gBAChB,OAAO;YACT;uEAAG,CAAC;QACJ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qDAAE;gBACR,OAAO,IAAI,CAAC,qBAAqB,OAAO,EAAE,OAAO;6DAAC,SAAU,OAAO;wBACjE,IAAI,eAAe,qBAAqB,OAAO,CAAC,QAAQ;wBACxD,IAAI,kBAAkB,OAAO,EAAE;4BAC7B,IAAI,CAAC,OAAO,IAAI,CAAC,cAAc,MAAM,EAAE;gCACrC,sCAAsC;gCACtC,QAAQ,KAAK,CAAC,uCAAuC,UAAU;gCAC/D;4BACF;wBACF;wBACA,IAAI,mBAAmB,aAAa,gBAAgB,EAClD,SAAS,aAAa,MAAM,EAC5B,aAAa,aAAa,UAAU;wBACtC,IAAI,CAAC,CAAC,cAAc,CAAC,WAAW,OAAO,KAAK,CAAC,kBAAkB;4BAC7D,sCAAsC;4BACtC,QAAQ,KAAK,CAAC,+BAA+B,SAAS,aAAa,UAAU;wBAC/E;oBACF;;gBACA,kBAAkB,OAAO,GAAG;YAC9B;;QACA,IAAI,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8EAAE,SAAU,OAAO,EAAE,gBAAgB,EAAE,MAAM,EAAE,UAAU;gBAC7F,qBAAqB,OAAO,CAAC,QAAQ,GAAG;oBACtC,kBAAkB;oBAClB,QAAQ;oBACR,YAAY;gBACd;YACF;6EAAG,EAAE;QACL,OAAO;IACT;AACF;AACA,SAAS,qBAAqB,cAAc,EAAE,eAAe,EAAE,KAAK;IAClE,IAAI,iBAAiB,MAAM,cAAc,EACvC,mBAAmB,MAAM,gBAAgB,EACzC,QAAQ,MAAM,KAAK,EACnB,cAAc,MAAM,WAAW,EAC/B,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;IAC9C,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,kBAAkB,OAAO;gBAC3B;YACF;YACA;kDAAiB;oBACf,OAAO,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;wBAC7B,kBAAkB;wBAClB,iBAAiB,KAAK,CAAC,iBAAiB;wBACxC,aAAa,MAAM,MAAM;oBAC3B,GAAG;gBACL;iDAAG,YAAY,QAAQ;QACvB,uDAAuD;QACzD;yCAAG;AACL;AACA,SAAS,kBAAkB,KAAK;IAC9B,IAAI,mBAAmB,MAAM,gBAAgB,EAC3C,SAAS,MAAM,MAAM,EACrB,WAAW,MAAM,QAAQ,EACzB,uBAAuB,MAAM,oBAAoB,EACjD,cAAc,MAAM,WAAW,EAC/B,qBAAqB,MAAM,cAAc;IAC3C,4CAA4C;IAC5C,IAAI,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,4DAA4D;IAC5D;uDAA0B;YACxB,IAAI,mBAAmB,KAAK,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;gBAC5E;YACF;YACA,IAAI,gBAAgB,OAAO,KAAK,OAAO;gBACrC,gBAAgB,OAAO,GAAG;YAC5B,OAAO;gBACL,mBAAmB,qBAAqB,mBAAmB;YAC7D;QACA,uDAAuD;QACzD;sDAAG;QAAC;KAAiB;IACrB,OAAO;AACT;AAEA,qDAAqD;AACrD,IAAI,2BAA2B;AAC/B,wBAAwB,GACxB,wCAA2C;IACzC,2BAA2B,SAAS,yBAAyB,KAAK;QAChE,IAAI,iBAAiB,MAAM,cAAc,EACvC,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,KAAK;QACrB,2EAA2E;QAC3E,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kDAAE;gBACR,IAAI,gBAAgB;oBAClB;gBACF;gBACA,4BAA4B,OAAO,aAAa,OAAO,EAAE;gBACzD,aAAa,OAAO,GAAG;YACzB;iDAAG;YAAC;YAAO;YAAO;SAAe;IACnC;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,sBAAsB,KAAK,EAAE,gBAAgB,EAAE,UAAU;IAChE,IAAI;IACJ,IAAI,eAAe,KAAK,GAAG;QACzB,aAAa;IACf;IACA,IAAI,eAAe,CAAC,CAAC,eAAe,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,aAAa,MAAM,KAAK,oBAAoB;IAChH,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACd,QAAQ;QACR,kBAAkB,CAAC;IACrB,GAAG,gBAAgB,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC1B,cAAc,MAAM,KAAK,CAAC,iBAAiB;QAC3C,QAAQ,kBAAkB,OAAO;QACjC,kBAAkB,kBAAkB,OAAO;IAC7C,GAAG,cAAc;QACf,YAAY,MAAM,YAAY,CAAC,MAAM,KAAK,CAAC,iBAAiB;IAC9D;AACF;AAEA,SAAS,uBAAuB,KAAK,EAAE,MAAM,EAAE,gBAAgB;IAC7D,IAAI,OAAO,OAAO,IAAI,EACpB,QAAQ,OAAO,KAAK;IACtB,IAAI;IACJ,OAAQ;QACN,KAAK,iBAAiB,aAAa;YACjC,UAAU;gBACR,kBAAkB,OAAO,QAAQ,GAAG,CAAC,IAAI,OAAO,KAAK;YACvD;YACA;QACF,KAAK,iBAAiB,cAAc;YAClC,UAAU;gBACR,kBAAkB,CAAC;YACrB;YACA;QACF,KAAK,iBAAiB,iBAAiB;QACvC,KAAK,iBAAiB,kBAAkB;YACtC,UAAU;gBACR,QAAQ,CAAC,MAAM,MAAM;gBACrB,kBAAkB,MAAM,MAAM,GAAG,CAAC,IAAI,0BAA0B,OAAO,OAAO;YAChF;YACA;QACF,KAAK,iBAAiB,gBAAgB;YACpC,UAAU;gBACR,QAAQ;gBACR,kBAAkB,0BAA0B,OAAO,OAAO;YAC5D;YACA;QACF,KAAK,iBAAiB,iBAAiB;YACrC,UAAU;gBACR,QAAQ;YACV;YACA;QACF,KAAK,iBAAiB,2BAA2B;YAC/C,UAAU;gBACR,kBAAkB,OAAO,gBAAgB;YAC3C;YACA;QACF,KAAK,iBAAiB,qBAAqB;YACzC,UAAU;gBACR,YAAY,OAAO,UAAU;YAC/B;YACA;QACF,KAAK,iBAAiB,aAAa;YACjC,UAAU;gBACR,kBAAkB,kBAAkB,OAAO;gBAC3C,QAAQ,kBAAkB,OAAO;gBACjC,cAAc,kBAAkB,OAAO;gBACvC,YAAY,kBAAkB,OAAO;YACvC;YACA;QACF;YACE,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;AAC7B;AACA,4BAA4B,GAE5B,SAAS,2BAA2B,EAAE;IAClC,IAAI,YAAY,GAAG,SAAS,EAAE,mBAAmB,GAAG,gBAAgB,EAAE,QAAQ,GAAG,KAAK,EAAE,eAAe,GAAG,YAAY,EAAE,uBAAuB,GAAG,oBAAoB;IACtK,IAAI,sBAAsB,UAAU,WAAW;IAC/C,IAAK,IAAI,QAAQ,GAAG,QAAQ,MAAM,MAAM,EAAE,QAAS;QAC/C,gGAAgG;QAChG,IAAI,cAAc,CAAC,QAAQ,mBAAmB,CAAC,UAAU,MAAM,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI,MAAM,MAAM;QAC5F,IAAI,OAAO,KAAK,CAAC,YAAY;QAC7B,IAAI,SAAS,aACT,aAAa,MAAM,WAAW,GAAG,UAAU,CAAC,sBAAsB;YAClE,IAAI,UAAU,qBAAqB;YACnC,IAAI,CAAC,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,YAAY,CAAC,WAAW,GAAG;gBACvF,OAAO;YACX;QACJ;IACJ;IACA,OAAO;AACX;AACA,IAAI,cAAc;IACd,OAAO,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC,UAAU;IACjC,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B,sBAAsB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACpC,yBAAyB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACvC,kBAAkB,yIAAA,CAAA,UAAS,CAAC,MAAM;IAClC,yBAAyB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACzC,yBAAyB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACzC,QAAQ,yIAAA,CAAA,UAAS,CAAC,IAAI;IACtB,eAAe,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B,eAAe,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B,cAAc,yIAAA,CAAA,UAAS,CAAC,GAAG;IAC3B,qBAAqB,yIAAA,CAAA,UAAS,CAAC,GAAG;IAClC,qBAAqB,yIAAA,CAAA,UAAS,CAAC,GAAG;IAClC,IAAI,yIAAA,CAAA,UAAS,CAAC,MAAM;IACpB,SAAS,yIAAA,CAAA,UAAS,CAAC,MAAM;IACzB,QAAQ,yIAAA,CAAA,UAAS,CAAC,MAAM;IACxB,WAAW,yIAAA,CAAA,UAAS,CAAC,IAAI;IACzB,gBAAgB,yIAAA,CAAA,UAAS,CAAC,MAAM;IAChC,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B,sBAAsB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACpC,0BAA0B,yIAAA,CAAA,UAAS,CAAC,IAAI;IACxC,eAAe,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B,gBAAgB,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC9B,aAAa,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QACzB,kBAAkB,yIAAA,CAAA,UAAS,CAAC,IAAI;QAChC,qBAAqB,yIAAA,CAAA,UAAS,CAAC,IAAI;QACnC,UAAU,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YACtB,gBAAgB,yIAAA,CAAA,UAAS,CAAC,IAAI;YAC9B,eAAe,yIAAA,CAAA,UAAS,CAAC,GAAG;YAC5B,MAAM,yIAAA,CAAA,UAAS,CAAC,GAAG;QACvB;IACJ;AACJ;AACA;;;;;;;CAOC,GACD,SAAS,qBAAqB,EAAE;IAC5B,IAAI,SAAS,GAAG,MAAM,EAAE,cAAc,GAAG,WAAW,EAAE,sBAAsB,GAAG,mBAAmB;IAClG,IAAI,CAAC,QAAQ;QACT,OAAO;IACX;IACA,IAAI,CAAC,aAAa;QACd,OAAO;IACX;IACA,IAAI,gBAAgB,qBAAqB;QACrC,OAAO,GAAG,MAAM,CAAC,aAAa,WAAW,MAAM,CAAC,gBAAgB,IAAI,QAAQ,SAAS;IACzF;IACA,OAAO;AACX;AACA,IAAI,iBAAiB,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,iBAAiB;IAAE,sBAAsB;AAAqB;AACzG,qDAAqD;AACrD,IAAI,sBAAsB;AAC1B,wBAAwB,GACxB,wCAA2C;IACvC,sBAAsB,SAAU,OAAO,EAAE,MAAM;QAC3C,yIAAA,CAAA,UAAS,CAAC,cAAc,CAAC,aAAa,SAAS,QAAQ,OAAO,IAAI;IACtE;AACJ;AAEA,IAAI,sBAAsB,uCAAwC;AAClE,IAAI,+BAA+B,uCAAwC;AAC3E,IAAI,6BAA6B,uCAAwC;AACzE,IAAI,+BAA+B,uCAAwC;AAC3E,IAAI,4BAA4B,uCAAwC;AACxE,IAAI,0BAA0B,uCAAwC;AACtE,IAAI,yBAAyB,uCAAwC;AACrE,IAAI,2BAA2B,uCAAwC;AACvE,IAAI,iCAAiC,uCAAwC;AAC7E,IAAI,4BAA4B,uCAAwC;AACxE,IAAI,8BAA8B,uCAAwC;AAC1E,IAAI,mBAAmB,uCAAwC;AAC/D,IAAI,mBAAmB,uCAAwC;AAC/D,IAAI,kBAAkB,uCAAwC;AAC9D,IAAI,cAAc,uCAAwC;AAC1D,IAAI,uBAAuB,uCAAwC;AACnE,IAAI,qBAAqB,uCAAwC;AACjE,IAAI,sBAAsB,uCAAwC;AAClE,IAAI,gCAAgC,uCAAwC;AAC5E,IAAI,uBAAuB,uCAAwC;AACnE,IAAI,0BAA0B,uCAAwC;AACtE,IAAI,kBAAkB,uCAAwC;AAE9D,IAAI,qBAAqB,WAAW,GAAE,OAAO,MAAM,CAAC;IAClD,WAAW;IACX,mBAAmB;IACnB,8BAA8B;IAC9B,4BAA4B;IAC5B,8BAA8B;IAC9B,2BAA2B;IAC3B,yBAAyB;IACzB,wBAAwB;IACxB,0BAA0B;IAC1B,gCAAgC;IAChC,2BAA2B;IAC3B,6BAA6B;IAC7B,kBAAkB;IAClB,gBAAgB;IAChB,eAAe;IACf,WAAW;IACX,oBAAoB;IACpB,kBAAkB;IAClB,mBAAmB;IACnB,6BAA6B;IAC7B,oBAAoB;IACpB,uBAAuB;IACvB,eAAe;AACjB;AAEA,6BAA6B,GAC7B,SAAS,uBAAuB,KAAK,EAAE,MAAM;IAC3C,IAAI;IACJ,IAAI,OAAO,OAAO,IAAI,EACpB,QAAQ,OAAO,KAAK,EACpB,SAAS,OAAO,MAAM;IACxB,IAAI;IACJ,OAAQ;QACN,KAAK;YACH,UAAU;gBACR,QAAQ,kBAAkB,OAAO;gBACjC,kBAAkB,kBAAkB,OAAO;gBAC3C,cAAc,MAAM,KAAK,CAAC,OAAO,KAAK,CAAC;YACzC;YACA;QACF,KAAK;YACH;gBACE,IAAI,gBAAgB,OAAO,GAAG;gBAC9B,IAAI,aAAa,KAAK,MAAM,UAAU,GAAG;gBACzC,IAAI,uBAAuB,CAAC,MAAM,MAAM,IAAI,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,MAAM,YAAY,IAAI,MAAM,gBAAgB;gBACjI,IAAI,mBAAmB,2BAA2B;oBAChD,WAAW;oBACX,kBAAkB;oBAClB,OAAO,MAAM,KAAK;oBAClB,cAAc,MAAM,YAAY;oBAChC,sBAAsB,OAAO,oBAAoB;gBACnD;gBACA,UAAU;oBACR,YAAY;oBACZ,kBAAkB;oBAClB,QAAQ;gBACV;YACF;YACA;QACF,KAAK;YACH;gBACE,IAAI,oBAAoB,MAAM,MAAM,GAAG,qBAAqB,GAAG,MAAM,gBAAgB,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE,SAAS,UAAU,MAAM,YAAY,IAAI,OAAO,CAAC,IAAI,0BAA0B,OAAO,OAAO;gBACrO,UAAU;oBACR,kBAAkB;oBAClB,QAAQ;gBACV;YACF;YACA;QACF,KAAK;YACH,IAAI,MAAM,MAAM,IAAI,QAAQ;gBAC1B,UAAU,sBAAsB,OAAO,MAAM,gBAAgB,EAAE;YACjE,OAAO;gBACL,IAAI,qBAAqB,MAAM,MAAM,GAAG,qBAAqB,CAAC,GAAG,MAAM,gBAAgB,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE,SAAS,0BAA0B,OAAO,OAAO,CAAC;gBAC5L,UAAU;oBACR,kBAAkB;oBAClB,QAAQ;gBACV;YACF;YACA;QACF,oCAAoC;QACpC,KAAK;QACL,KAAK;YACH,UAAU,sBAAsB,OAAO,MAAM,gBAAgB,EAAE;YAC/D;QACF,KAAK;YACH,UAAU;gBACR,kBAAkB,wBAAwB,GAAG,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE;gBACjG,QAAQ;YACV;YACA;QACF,KAAK;YACH,UAAU;gBACR,kBAAkB,wBAAwB,CAAC,GAAG,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE;gBACvH,QAAQ;YACV;YACA;QACF,KAAK;YACH,UAAU;gBACR,kBAAkB,qBAAqB,CAAC,IAAI,MAAM,gBAAgB,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE;YACvH;YACA;QACF,KAAK;YACH,UAAU;gBACR,kBAAkB,qBAAqB,IAAI,MAAM,gBAAgB,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE;YACtH;YACA;QACF,KAAK;YACH,UAAU;gBACR,QAAQ;gBACR,kBAAkB,CAAC;YACrB;YACA;QACF,KAAK;YACH,UAAU,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;gBACjB,QAAQ;gBACR,kBAAkB,CAAC;YACrB,GAAG,MAAM,gBAAgB,IAAI,KAAK,CAAC,CAAC,eAAe,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,aAAa,MAAM,KAAK;gBACzG,cAAc,MAAM,KAAK,CAAC,MAAM,gBAAgB,CAAC;YACnD;YACA;QACF,KAAK;YACH,UAAU;gBACR,cAAc,OAAO,YAAY;YACnC;YACA;QACF;YACE,OAAO,uBAAuB,OAAO,QAAQ;IACjD;IACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;AAC7B;AACA,4BAA4B,GAE5B,IAAI,cAAc;IAAC;IAAgB;IAAU;IAAa;IAAU;CAAM,EACxE,eAAe;IAAC;IAAU;IAAW;IAAW;IAAa;IAAU;CAAM,EAC7E,eAAe;IAAC;IAAQ;IAAS;IAAe;IAAW;IAAW;IAAU;IAAO;CAAW;AACpG,UAAU,gBAAgB,GAAG;AAC7B,SAAS,UAAU,SAAS;IAC1B,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY,CAAC;IACf;IACA,oBAAoB,WAAW;IAC/B,oCAAoC;IACpC,IAAI,QAAQ,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,gBAAgB;IACzC,IAAI,QAAQ,MAAM,KAAK,EACrB,iBAAiB,MAAM,cAAc,EACrC,cAAc,MAAM,WAAW,EAC/B,eAAe,MAAM,YAAY,EACjC,0BAA0B,MAAM,uBAAuB,EACvD,uBAAuB,MAAM,oBAAoB;IACnD,+CAA+C;IAC/C,IAAI,eAAe,kBAAkB;IACrC,IAAI,wBAAwB,uBAAuB,wBAAwB,cAAc,QACvF,QAAQ,qBAAqB,CAAC,EAAE,EAChC,WAAW,qBAAqB,CAAC,EAAE;IACrC,IAAI,SAAS,MAAM,MAAM,EACvB,mBAAmB,MAAM,gBAAgB,EACzC,eAAe,MAAM,YAAY,EACjC,aAAa,MAAM,UAAU;IAE/B,eAAe;IACf,IAAI,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,IAAI,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACrB,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,CAAC;IACvB,mEAAmE;IACnE,IAAI,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,4CAA4C;IAC5C,IAAI,aAAa,cAAc;IAC/B,iEAAiE;IACjE,IAAI,yBAAyB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAClC,IAAI,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,wCAAwC;IACxC,IAAI,SAAS,aAAa;QACxB,OAAO;QACP,OAAO;IACT;IAEA,cAAc;IACd,IAAI,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,SAAU,KAAK;YACpD,OAAO,SAAS,OAAO,CAAC,WAAW,SAAS,CAAC,OAAO;QACtD;sDAAG;QAAC;KAAW;IAEf,WAAW;IACX,gDAAgD;IAChD,qBAAqB,sBAAsB;QAAC;QAAQ;QAAkB;QAAY;KAAM,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACjG,gBAAgB,kBAAkB,OAAO;QACzC,qBAAqB,uBAAuB,OAAO;QACnD,OAAO;QACP,aAAa;QACb,cAAc;IAChB,GAAG;IACH,uDAAuD;IACvD,qBAAqB,yBAAyB;QAAC;KAAa,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACrE,gBAAgB,kBAAkB,OAAO;QACzC,qBAAqB,uBAAuB,OAAO;QACnD,OAAO;QACP,aAAa;QACb,cAAc;IAChB,GAAG;IACH,4DAA4D;IAC5D,IAAI,kBAAkB,kBAAkB;QACtC,aAAa,QAAQ,OAAO;QAC5B,kBAAkB;QAClB,QAAQ;QACR,UAAU;QACV,gBAAgB;QAChB,sBAAsB;IACxB;IAEA,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,8DAA8D;YAC9D,gBAAgB,OAAO,GAAG;uCAAS,SAAU,aAAa;oBACxD,cAAc;wBACZ,MAAM;wBACN,YAAY;oBACd;gBACF;sCAAG;YAEH,8CAA8C;YAC9C;uCAAO;oBACL,gBAAgB,OAAO,CAAC,MAAM;gBAChC;;QACF;8BAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,YAAY;gBACf;YACF;YACA,gBAAgB,OAAO,CAAC;QAC1B;8BAAG;QAAC;QAAU;KAAW;IACzB,yBAAyB;QACvB,gBAAgB,kBAAkB,OAAO;QACzC,OAAO;QACP,OAAO;IACT;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,kBAAkB,OAAO,EAAE;gBAC7B;YACF;YACA,uBAAuB,OAAO,GAAG,MAAM,MAAM;QAC/C;;IACA,sCAAsC;IACtC,IAAI,2BAA2B,wBAAwB,QAAQ;QAAC;QAAS;KAAgB,EAAE;uEAAa;YACtG,SAAS;gBACP,MAAM;YACR;QACF;;IACA,IAAI,wBAAwB,4BAA4B,gBAAgB;IACxE,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,kBAAkB,OAAO,GAAG;YAC5B;uCAAO;oBACL,kBAAkB,OAAO,GAAG;gBAC9B;;QACF;8BAAG,EAAE;IACL,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,QAAQ;gBACX,SAAS,OAAO,GAAG,CAAC;YACtB;QACF;8BAAG;QAAC;KAAO;IAEX,2BAA2B;IAC3B,IAAI,8BAA8B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0DAAE;YACxC,OAAO;gBACL,WAAW,SAAS,UAAU,KAAK;oBACjC,MAAM,cAAc;oBACpB,SAAS;wBACP,MAAM;wBACN,sBAAsB;wBACtB,QAAQ,MAAM,MAAM;oBACtB;gBACF;gBACA,SAAS,SAAS,QAAQ,KAAK;oBAC7B,MAAM,cAAc;oBACpB,SAAS;wBACP,MAAM;wBACN,sBAAsB;wBACtB,QAAQ,MAAM,MAAM;oBACtB;gBACF;gBACA,MAAM,SAAS,KAAK,KAAK;oBACvB,MAAM,cAAc;oBACpB,SAAS;wBACP,MAAM;wBACN,sBAAsB;oBACxB;gBACF;gBACA,KAAK,SAAS,IAAI,KAAK;oBACrB,MAAM,cAAc;oBACpB,SAAS;wBACP,MAAM;wBACN,sBAAsB;oBACxB;gBACF;gBACA,QAAQ,SAAS;oBACf,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE;wBAC/B,SAAS;4BACP,MAAM;wBACR;oBACF;gBACF;gBACA,OAAO,SAAS,MAAM,KAAK;oBACzB,MAAM,cAAc;oBACpB,SAAS;wBACP,MAAM,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,2BAA2B;oBACjE;gBACF;gBACA,QAAQ,SAAS,OAAO,KAAK;oBAC3B,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE;wBAC/B,MAAM,cAAc;wBACpB,SAAS;4BACP,MAAM;4BACN,sBAAsB;wBACxB;oBACF;gBACF;gBACA,UAAU,SAAS,SAAS,KAAK;oBAC/B,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE;wBAC/B,MAAM,cAAc;wBACpB,SAAS;4BACP,MAAM;4BACN,sBAAsB;wBACxB;oBACF;gBACF;gBACA,KAAK,SAAS,EAAE,KAAK;oBACnB,MAAM,cAAc;oBACpB,IAAI,eAAe,OAAO,OAAO,CAAC,KAAK;oBACvC,IAAI,CAAC,aAAa,MAAM,EAAE;wBACxB,SAAS;4BACP,MAAM;wBACR;wBACA;oBACF;oBACA,IAAI,aAAa,UAAU,EAAE;wBAC3B,SAAS;4BACP,MAAM;4BACN,KAAK;4BACL,sBAAsB;wBACxB;oBACF,OAAO;wBACL,SAAS;4BACP,MAAM;wBACR;oBACF;gBACF;YACF;QACF;yDAAG;QAAC;QAAU;QAAsB;KAAO;IAE3C,oBAAoB;IACpB,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YAC3B,SAAS;gBACP,MAAM;YACR;QACF;4CAAG;QAAC;KAAS;IACb,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YAC1B,SAAS;gBACP,MAAM;YACR;QACF;2CAAG;QAAC;KAAS;IACb,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE;YACzB,SAAS;gBACP,MAAM;YACR;QACF;0CAAG;QAAC;KAAS;IACb,IAAI,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,SAAU,mBAAmB;YACjE,SAAS;gBACP,MAAM;gBACN,kBAAkB;YACpB;QACF;qDAAG;QAAC;KAAS;IACb,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,SAAU,eAAe;YACpD,SAAS;gBACP,MAAM;gBACN,cAAc;YAChB;QACF;4CAAG;QAAC;KAAS;IACb,IAAI,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wCAAE;YACtB,SAAS;gBACP,MAAM;YACR;QACF;uCAAG;QAAC;KAAS;IACb,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,SAAU,aAAa;YACrD,SAAS;gBACP,MAAM;gBACN,YAAY;YACd;QACF;+CAAG;QAAC;KAAS;IACb,oBAAoB;IACpB,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,SAAU,UAAU;YAClD,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;gBACd,IAAI,WAAW,OAAO;gBACtB,SAAS,WAAW,cAAc;YACpC,GAAG;QACL;+CAAG;QAAC;KAAW;IACf,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,SAAU,KAAK,EAAE,MAAM;YACpD,IAAI;YACJ,IAAI,OAAO,UAAU,KAAK,IAAI,CAAC,IAAI,OACjC,eAAe,KAAK,YAAY,EAChC,cAAc,KAAK,MAAM,EACzB,SAAS,gBAAgB,KAAK,IAAI,QAAQ;YAC1C,KAAK,SAAS;YACd,KAAK,MAAM;YACX,IAAI,MAAM,KAAK,GAAG,EAClB,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,MAAM;YAC7C,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,wBAAwB,MAAM,gBAAgB,EAC9C,mBAAmB,0BAA0B,KAAK,IAAI,QAAQ;YAChE,IAAI,uBAAuB,SAAS;gBAClC,SAAS;oBACP,MAAM;gBACR;YACF;YACA,sBAAsB,gBAAgB,kBAAkB,QAAQ;YAChE,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,WAAW;uDAAK,SAAU,QAAQ;oBACrF,QAAQ,OAAO,GAAG;gBACpB;uDAAI,UAAU,EAAE,GAAG,WAAW,MAAM,EAAE,UAAU,IAAI,GAAG,WAAW,SAAS,CAAC,kBAAkB,GAAG,QAAQ,IAAI,CAAC,aAAa,GAAG,YAAY,KAAK,WAAW,OAAO,EAAE,UAAU,YAAY,GAAG,qBAAqB,cAAc,uBAAuB,SAAS,GAAG;QACpQ;8CAAG;QAAC;QAAU;QAAuB;KAAW;IAChD,IAAI,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,SAAU,MAAM,EAAE,MAAM;YAC7D,IAAI;YACJ,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,SAAS,MAAM,MAAM,EACrB,UAAU,MAAM,OAAO;YACvB,MAAM,OAAO;YACb,IAAI,YAAY,MAAM,SAAS,EAC/B,eAAe,MAAM,MAAM,EAC3B,SAAS,iBAAiB,KAAK,IAAI,QAAQ,cAC3C,MAAM,MAAM,GAAG,EACf,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;YAC9C,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,wBAAwB,MAAM,gBAAgB,EAC9C,mBAAmB,0BAA0B,KAAK,IAAI,QAAQ;YAChE,IAAI,cAAc,OAAO,OAAO,CAAC,KAAK;YACtC,IAAI,0BAA0B,SAAS;gBACrC,SAAS;oBACP,MAAM;gBACR;YACF;YACA,IAAI,yBAAyB,SAAS;gBACpC,IAAI,YAAY,MAAM,IAAI,CAAC,yBAAyB,OAAO,CAAC,WAAW,EAAE;oBACvE,SAAS;wBACP,MAAM;oBACR;gBACF;YACF;YACA,IAAI,4BAA4B,SAAS,0BAA0B,KAAK;gBACtE,IAAI,MAAM,kBAAkB;gBAC5B,IAAI,OAAO,2BAA2B,CAAC,IAAI,EAAE;oBAC3C,2BAA2B,CAAC,IAAI,CAAC;gBACnC,OAAO,IAAI,uBAAuB,MAAM;oBACtC,SAAS;wBACP,MAAM;wBACN,KAAK;wBACL,sBAAsB;oBACxB;gBACF;YACF;YACA,IAAI,cAAc,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,WAAW;2EAAK,SAAU,gBAAgB;oBACxG,gBAAgB,OAAO,GAAG;gBAC5B;2EAAI,SAAS,CAAC,wBAAwB,GAAG,YAAY,MAAM,IAAI,YAAY,gBAAgB,GAAG,CAAC,IAAI,WAAW,SAAS,CAAC,YAAY,gBAAgB,IAAI,IAAI,SAAS,CAAC,gBAAgB,GAAG,WAAW,MAAM,EAAE,SAAS,CAAC,gBAAgB,GAAG,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,gBAAgB,GAAG,WAAW,SAAS,CAAC,kBAAkB,GAAG,QAAQ,IAAI,CAAC,aAAa,GAAG,YAAY,KAAK,WAAW,OAAO,EAAE,UAAU,EAAE,GAAG,WAAW,cAAc,EAAE,UAAU,IAAI,GAAG,YAAY,UAAU,QAAQ,GAAG,GAAG,UAAU,MAAM,GAAG,qBAAqB,QAAQ,yBAAyB,SAAS,GAAG;YACnkB,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAClB,qCAAqC,GACrC;oBACE,YAAY,OAAO,GAAG,qBAAqB,SAAS;oBACpD,YAAY,SAAS,GAAG,qBAAqB,WAAW;gBAC1D;YACF;YACA,sBAAsB,wBAAwB,kBAAkB,QAAQ;YACxE,OAAO;QACT;sDAAG;QAAC;QAAQ;QAAY;QAAuB;QAAU;QAA0B;QAA6B;KAAqB;IACrI,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,SAAU,MAAM;YAC7C,IAAI;YACJ,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,WAAW,MAAM,IAAI,EACrB,YAAY,MAAM,KAAK,EACvB,cAAc,MAAM,WAAW,EAC/B,UAAU,MAAM,OAAO;YACvB,MAAM,OAAO;YACb,IAAI,eAAe,MAAM,MAAM,EAC/B,SAAS,iBAAiB,KAAK,IAAI,QAAQ,cAC3C,MAAM,MAAM,GAAG,EACf,WAAW,MAAM,QAAQ,EACzB,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;YAC9C,IAAI,kBAAkB,OAAO,OAAO,EAClC,cAAc,gBAAgB,KAAK,EACnC,cAAc,gBAAgB,KAAK;YACrC,IAAI,mBAAmB,gBAAgB,UAAU,WAAW,YAAY,KAAK,EAAE,+CAC7E,OAAO,gBAAgB,CAAC,EAAE,EAC1B,QAAQ,gBAAgB,CAAC,EAAE;YAC7B,IAAI,sBAAsB,SAAS;gBACjC,IAAI,UAAU,YAAY,gBAAgB,EAAE;oBAC1C;gBACF;gBACA,gBAAgB,OAAO,GAAG;gBAC1B,SAAS;oBACP,MAAM;oBACN,OAAO;oBACP,UAAU;gBACZ;YACF;YACA,IAAI,kBAAkB,SAAS;gBAC7B,SAAS;oBACP,MAAM;oBACN,OAAO;gBACT;YACF;YACA,IAAI,YAAY,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,YAAY;gBACpC,UAAU;gBACV,MAAM;gBACN,iBAAiB,KAAK,CAAC,SAAS,YAAY;gBAC5C,IAAI,WAAW,SAAS,CAAC;YAC3B,GAAG,SAAS,CAAC,OAAO,GAAG,WAAW;iEAAK,SAAU,QAAQ;oBACvD,IAAI,UAAU;wBACZ,SAAS,OAAO,CAAC,WAAW,SAAS,CAAC,OAAO,GAAG;oBAClD;gBACF;iEAAI,SAAS,GAAG;YAChB,IAAI,CAAC,UAAU;gBACb,uCAAuC,GACvC;oBACE,UAAU,OAAO,GAAG,qBAAqB,SAAS;gBACpD;YACF;YACA,UAAU,WAAW,GAAG,qBAAqB,aAAa;YAC1D,OAAO;QACT;8CAAG;QAAC;QAAQ;QAAc;QAAY;QAAiB;KAAS;IAChE,OAAO;QACL,gBAAgB;QAChB,sBAAsB;QACtB,eAAe;QACf,cAAc;QACd,cAAc;QACd,WAAW;QACX,YAAY;QACZ,UAAU;QACV,WAAW;QACX,qBAAqB;QACrB,YAAY;QACZ,OAAO;QACP,eAAe;QACf,SAAS;QACT,kBAAkB;QAClB,QAAQ;QACR,cAAc;QACd,YAAY;IACd;AACF;AAEA,IAAI,wBAAwB,uCAAwC;AACpE,IAAI,sBAAsB,uCAAwC;AAClE,IAAI,qBAAqB,uCAAwC;AACjE,IAAI,mBAAmB,uCAAwC;AAC/D,IAAI,kBAAkB,uCAAwC;AAC9D,IAAI,qBAAqB,uCAAwC;AACjE,IAAI,uBAAuB,uCAAwC;AACnE,IAAI,oBAAoB,uCAAwC;AAChE,IAAI,cAAc,uCAAwC;AAC1D,IAAI,YAAY,uCAAwC;AACxD,IAAI,aAAa,uCAAwC;AACzD,IAAI,iBAAiB,uCAAwC;AAC7D,IAAI,gBAAgB,uCAAwC;AAC5D,IAAI,YAAY,uCAAwC;AACxD,IAAI,oBAAoB,uCAAwC;AAChE,IAAI,qBAAqB,uCAAwC;AACjE,IAAI,mBAAmB,uCAAwC;AAC/D,IAAI,oBAAoB,uCAAwC;AAChE,IAAI,8BAA8B,uCAAwC;AAC1E,IAAI,qBAAqB,uCAAwC;AACjE,IAAI,wBAAwB,uCAAwC;AACpE,IAAI,kBAAkB,uCAAwC;AAC9D,IAAI,oCAAoC,uCAAwC;AAEhF,IAAI,qBAAqB,WAAW,GAAE,OAAO,MAAM,CAAC;IAClD,WAAW;IACX,uBAAuB;IACvB,qBAAqB;IACrB,oBAAoB;IACpB,kBAAkB;IAClB,iBAAiB;IACjB,oBAAoB;IACpB,sBAAsB;IACtB,mBAAmB;IACnB,aAAa;IACb,WAAW;IACX,YAAY;IACZ,gBAAgB;IAChB,eAAe;IACf,WAAW;IACX,mBAAmB;IACnB,oBAAoB;IACpB,kBAAkB;IAClB,mBAAmB;IACnB,6BAA6B;IAC7B,oBAAoB;IACpB,uBAAuB;IACvB,eAAe;IACf,mCAAmC;AACrC;AAEA,SAAS,kBAAkB,KAAK;IAC9B,IAAI,eAAe,kBAAkB;IACrC,IAAI,eAAe,aAAa,YAAY;IAC5C,IAAI,aAAa,aAAa,UAAU;IACxC,IAAI,eAAe,MAAM,gBAAgB,MAAM,iBAAiB,KAAK,aAAa,MAAM,iBAAiB,KAAK,aAAa,MAAM,UAAU,KAAK,WAAW;QACzJ,aAAa,MAAM,YAAY,CAAC;IAClC;IACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,cAAc;QAChC,YAAY;IACd;AACF;AACA,IAAI,cAAc;IAChB,OAAO,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC,UAAU;IACjC,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B,qBAAqB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACnC,sBAAsB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACpC,yBAAyB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACvC,kBAAkB,yIAAA,CAAA,UAAS,CAAC,MAAM;IAClC,yBAAyB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACzC,yBAAyB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACzC,QAAQ,yIAAA,CAAA,UAAS,CAAC,IAAI;IACtB,eAAe,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B,eAAe,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B,cAAc,yIAAA,CAAA,UAAS,CAAC,GAAG;IAC3B,qBAAqB,yIAAA,CAAA,UAAS,CAAC,GAAG;IAClC,qBAAqB,yIAAA,CAAA,UAAS,CAAC,GAAG;IAClC,YAAY,yIAAA,CAAA,UAAS,CAAC,MAAM;IAC5B,mBAAmB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACnC,mBAAmB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACnC,IAAI,yIAAA,CAAA,UAAS,CAAC,MAAM;IACpB,SAAS,yIAAA,CAAA,UAAS,CAAC,MAAM;IACzB,QAAQ,yIAAA,CAAA,UAAS,CAAC,MAAM;IACxB,WAAW,yIAAA,CAAA,UAAS,CAAC,IAAI;IACzB,SAAS,yIAAA,CAAA,UAAS,CAAC,MAAM;IACzB,gBAAgB,yIAAA,CAAA,UAAS,CAAC,MAAM;IAChC,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B,sBAAsB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACpC,0BAA0B,yIAAA,CAAA,UAAS,CAAC,IAAI;IACxC,eAAe,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC7B,gBAAgB,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC9B,oBAAoB,yIAAA,CAAA,UAAS,CAAC,IAAI;IAClC,aAAa,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QAC3B,kBAAkB,yIAAA,CAAA,UAAS,CAAC,IAAI;QAChC,qBAAqB,yIAAA,CAAA,UAAS,CAAC,IAAI;QACnC,UAAU,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YACxB,gBAAgB,yIAAA,CAAA,UAAS,CAAC,IAAI;YAC9B,eAAe,yIAAA,CAAA,UAAS,CAAC,GAAG;YAC5B,MAAM,yIAAA,CAAA,UAAS,CAAC,GAAG;QACrB;IACF;AACF;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,qBAAqB,OAAO,EAAE,YAAY,EAAE,KAAK;IACxD,IAAI,0BAA0B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACnC,IAAI,sBAAsB,mBAAmB,SAAS,cAAc,QAClE,QAAQ,mBAAmB,CAAC,EAAE,EAC9B,WAAW,mBAAmB,CAAC,EAAE;IAEnC,6EAA6E;IAC7E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,CAAC,iBAAiB,OAAO,iBAAiB;gBAC5C;YACF;YACA,IAAI,MAAM,mBAAmB,CAAC,wBAAwB,OAAO,EAAE,MAAM,YAAY,GAAG;gBAClF,SAAS;oBACP,MAAM;oBACN,YAAY,MAAM,YAAY,CAAC,MAAM,YAAY;gBACnD;YACF;YACA,wBAAwB,OAAO,GAAG,MAAM,YAAY,KAAK,wBAAwB,OAAO,GAAG,MAAM,YAAY,GAAG,MAAM,YAAY;QAClI,uDAAuD;QACzD;yCAAG;QAAC,MAAM,YAAY;QAAE,MAAM,YAAY;KAAC;IAC3C,OAAO;QAAC,SAAS,OAAO;QAAQ;KAAS;AAC3C;AAEA,qDAAqD;AACrD,IAAI,sBAAsB;AAC1B,wBAAwB,GACxB,wCAA2C;IACzC,sBAAsB,SAAS,kBAAkB,OAAO,EAAE,MAAM;QAC9D,yIAAA,CAAA,UAAS,CAAC,cAAc,CAAC,aAAa,SAAS,QAAQ,OAAO,IAAI;IACpE;AACF;AACA,IAAI,iBAAiB,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,gBAAgB;IAChD,qBAAqB,SAAS,oBAAoB,QAAQ,EAAE,IAAI;QAC9D,OAAO,aAAa;IACtB;IACA,sBAAsB;AACxB;AAEA,6BAA6B,GAC7B,SAAS,4BAA4B,KAAK,EAAE,MAAM;IAChD,IAAI;IACJ,IAAI,OAAO,OAAO,IAAI,EACpB,QAAQ,OAAO,KAAK,EACpB,SAAS,OAAO,MAAM;IACxB,IAAI;IACJ,OAAQ;QACN,KAAK;YACH,UAAU;gBACR,QAAQ,kBAAkB,OAAO;gBACjC,kBAAkB,kBAAkB,OAAO;gBAC3C,cAAc,MAAM,KAAK,CAAC,OAAO,KAAK,CAAC;gBACvC,YAAY,MAAM,YAAY,CAAC,MAAM,KAAK,CAAC,OAAO,KAAK,CAAC;YAC1D;YACA;QACF,KAAK;YACH,IAAI,MAAM,MAAM,EAAE;gBAChB,UAAU;oBACR,kBAAkB,qBAAqB,GAAG,MAAM,gBAAgB,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE;gBACrH;YACF,OAAO;gBACL,UAAU;oBACR,kBAAkB,UAAU,MAAM,YAAY,IAAI,OAAO,CAAC,IAAI,0BAA0B,OAAO,OAAO,GAAG,OAAO,oBAAoB;oBACpI,QAAQ,MAAM,KAAK,CAAC,MAAM,IAAI;gBAChC;YACF;YACA;QACF,KAAK;YACH,IAAI,MAAM,MAAM,EAAE;gBAChB,IAAI,QAAQ;oBACV,UAAU,sBAAsB,OAAO,MAAM,gBAAgB;gBAC/D,OAAO;oBACL,UAAU;wBACR,kBAAkB,qBAAqB,CAAC,GAAG,MAAM,gBAAgB,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE;oBACtH;gBACF;YACF,OAAO;gBACL,UAAU;oBACR,kBAAkB,0BAA0B,OAAO,OAAO,CAAC,GAAG,OAAO,oBAAoB;oBACzF,QAAQ,MAAM,KAAK,CAAC,MAAM,IAAI;gBAChC;YACF;YACA;QACF,KAAK;YACH,UAAU,sBAAsB,OAAO,MAAM,gBAAgB;YAC7D;QACF,KAAK;YACH,UAAU,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;gBACjB,QAAQ;gBACR,kBAAkB,CAAC;YACrB,GAAG,CAAC,MAAM,MAAM,IAAI;gBAClB,cAAc;gBACd,YAAY;YACd;YACA;QACF,KAAK;YACH,UAAU;gBACR,kBAAkB,qBAAqB,CAAC,IAAI,MAAM,gBAAgB,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE;YACvH;YACA;QACF,KAAK;YACH,UAAU;gBACR,kBAAkB,qBAAqB,IAAI,MAAM,gBAAgB,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE;YACtH;YACA;QACF,KAAK;YACH,UAAU;gBACR,kBAAkB,wBAAwB,GAAG,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE;YACnG;YACA;QACF,KAAK;YACH,UAAU;gBACR,kBAAkB,wBAAwB,CAAC,GAAG,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,oBAAoB,EAAE;YACzH;YACA;QACF,KAAK;YACH,UAAU,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;gBACjB,QAAQ;gBACR,kBAAkB,CAAC;YACrB,GAAG,MAAM,gBAAgB,IAAI,KAAK,CAAC,CAAC,eAAe,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,aAAa,MAAM,KAAK,OAAO,UAAU,IAAI;gBAC9H,cAAc,MAAM,KAAK,CAAC,MAAM,gBAAgB,CAAC;gBACjD,YAAY,MAAM,YAAY,CAAC,MAAM,KAAK,CAAC,MAAM,gBAAgB,CAAC;YACpE;YACA;QACF,KAAK;YACH,UAAU;gBACR,QAAQ;gBACR,kBAAkB,kBAAkB,OAAO;gBAC3C,YAAY,OAAO,UAAU;YAC/B;YACA;QACF,KAAK;YACH,UAAU;gBACR,QAAQ;gBACR,kBAAkB,0BAA0B,OAAO,OAAO;YAC5D;YACA;QACF,KAAK;YACH,UAAU;gBACR,cAAc,OAAO,YAAY;gBACjC,YAAY,MAAM,YAAY,CAAC,OAAO,YAAY;YACpD;YACA;QACF,KAAK;YACH,UAAU;gBACR,YAAY,OAAO,UAAU;YAC/B;YACA;QACF;YACE,OAAO,uBAAuB,OAAO,QAAQ;IACjD;IACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;AAC7B;AACA,4BAA4B,GAE5B,IAAI,cAAc;IAAC;IAAgB;IAAU;CAAM,EACjD,eAAe;IAAC;IAAQ;IAAS;IAAU;IAAO;IAAe;IAAe;IAAW;IAAW;CAAW,EACjH,aAAa;IAAC;IAAW;IAAW;IAAU;CAAM,EACpD,aAAa;IAAC;IAAa;IAAY;IAAW;IAAW;IAAU;IAAgB;IAAU;CAAM;AACzG,YAAY,gBAAgB,GAAG;AAC/B,SAAS,YAAY,SAAS;IAC5B,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY,CAAC;IACf;IACA,oBAAoB,WAAW;IAC/B,oCAAoC;IACpC,IAAI,QAAQ,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,gBAAgB;IACzC,IAAI,gBAAgB,MAAM,aAAa,EACrC,gBAAgB,MAAM,aAAa,EACnC,QAAQ,MAAM,KAAK,EACnB,iBAAiB,MAAM,cAAc,EACrC,cAAc,MAAM,WAAW,EAC/B,uBAAuB,MAAM,oBAAoB,EACjD,0BAA0B,MAAM,uBAAuB,EACvD,eAAe,MAAM,YAAY;IACnC,+CAA+C;IAC/C,IAAI,eAAe,kBAAkB;IACrC,IAAI,wBAAwB,qBAAqB,6BAA6B,cAAc,QAC1F,QAAQ,qBAAqB,CAAC,EAAE,EAChC,WAAW,qBAAqB,CAAC,EAAE;IACrC,IAAI,SAAS,MAAM,MAAM,EACvB,mBAAmB,MAAM,gBAAgB,EACzC,eAAe,MAAM,YAAY,EACjC,aAAa,MAAM,UAAU;IAE/B,gBAAgB;IAChB,IAAI,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACrB,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,CAAC;IACvB,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACtB,IAAI,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,IAAI,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,4CAA4C;IAC5C,IAAI,aAAa,cAAc;IAC/B,iEAAiE;IACjE,IAAI,yBAAyB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAClC,wCAAwC;IACxC,IAAI,SAAS,aAAa;QACxB,OAAO;QACP,OAAO;IACT;IACA,IAAI,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,SAAU,KAAK;YACpD,OAAO,SAAS,OAAO,CAAC,WAAW,SAAS,CAAC,OAAO;QACtD;wDAAG;QAAC;KAAW;IAEf,WAAW;IACX,gDAAgD;IAChD,qBAAqB,sBAAsB;QAAC;QAAQ;QAAkB;QAAY;KAAM,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACjG,gBAAgB,kBAAkB,OAAO;QACzC,qBAAqB,uBAAuB,OAAO;QACnD,OAAO;QACP,aAAa;QACb,cAAc;IAChB,GAAG;IACH,uDAAuD;IACvD,qBAAqB,yBAAyB;QAAC;KAAa,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACrE,gBAAgB,kBAAkB,OAAO;QACzC,qBAAqB,uBAAuB,OAAO;QACnD,OAAO;QACP,aAAa;QACb,cAAc;IAChB,GAAG;IACH,4DAA4D;IAC5D,IAAI,kBAAkB,kBAAkB;QACtC,aAAa,QAAQ,OAAO;QAC5B,kBAAkB;QAClB,QAAQ;QACR,UAAU;QACV,gBAAgB;QAChB,sBAAsB;IACxB;IACA,yBAAyB;QACvB,gBAAgB,kBAAkB,OAAO;QACzC,OAAO;QACP,OAAO;IACT;IACA,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,cAAc,iBAAiB,iBAAiB;YACpD,IAAI,eAAe,SAAS,OAAO,EAAE;gBACnC,SAAS,OAAO,CAAC,KAAK;YACxB;QACA,uDAAuD;QACzD;gCAAG,EAAE;IACL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,kBAAkB,OAAO,EAAE;gBAC7B;YACF;YACA,uBAAuB,OAAO,GAAG,MAAM,MAAM;QAC/C;;IACA,sCAAsC;IACtC,IAAI,2BAA2B,wBAAwB,QAAQ;QAAC;QAAU;QAAS;KAAgB,EAAE;yEAAa;YAChH,SAAS;gBACP,MAAM;gBACN,YAAY;YACd;QACF;;IACA,IAAI,wBAAwB,4BAA4B,iBAAiB;IACzE,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,kBAAkB,OAAO,GAAG;YAC5B;yCAAO;oBACL,kBAAkB,OAAO,GAAG;gBAC9B;;QACF;gCAAG,EAAE;IACL,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI;YACJ,IAAI,CAAC,QAAQ;gBACX,SAAS,OAAO,GAAG,CAAC;YACtB,OAAO,IAAI,CAAC,CAAC,wBAAwB,YAAY,QAAQ,KAAK,OAAO,KAAK,IAAI,sBAAsB,aAAa,MAAM,SAAS,OAAO,EAAE;gBACvI,IAAI;gBACJ,YAAY,QAAQ,CAAC,oBAAoB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK;YACvG;QACF;gCAAG;QAAC;QAAQ;KAAY;IAExB,2BAA2B,GAC3B,IAAI,uBAAuB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE;YACjC,OAAO;gBACL,WAAW,SAAS,UAAU,KAAK;oBACjC,MAAM,cAAc;oBACpB,SAAS;wBACP,MAAM;wBACN,QAAQ,MAAM,MAAM;wBACpB,sBAAsB;oBACxB;gBACF;gBACA,SAAS,SAAS,QAAQ,KAAK;oBAC7B,MAAM,cAAc;oBACpB,SAAS;wBACP,MAAM;wBACN,QAAQ,MAAM,MAAM;wBACpB,sBAAsB;oBACxB;gBACF;gBACA,MAAM,SAAS,KAAK,KAAK;oBACvB,IAAI,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE;wBAChC;oBACF;oBACA,MAAM,cAAc;oBACpB,SAAS;wBACP,MAAM;wBACN,sBAAsB;oBACxB;gBACF;gBACA,KAAK,SAAS,IAAI,KAAK;oBACrB,IAAI,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE;wBAChC;oBACF;oBACA,MAAM,cAAc;oBACpB,SAAS;wBACP,MAAM;wBACN,sBAAsB;oBACxB;gBACF;gBACA,QAAQ,SAAS,OAAO,KAAK;oBAC3B,IAAI,cAAc,OAAO,OAAO,CAAC,KAAK;oBACtC,IAAI,YAAY,MAAM,IAAI,YAAY,UAAU,IAAI,YAAY,YAAY,IAAI,YAAY,gBAAgB,GAAG,CAAC,GAAG;wBACjH,MAAM,cAAc;wBACpB,SAAS;4BACP,MAAM;wBACR;oBACF;gBACF;gBACA,OAAO,SAAS,MAAM,KAAK;oBACzB,IAAI,cAAc,OAAO,OAAO,CAAC,KAAK;oBACtC,iDAAiD;oBACjD,IAAI,CAAC,YAAY,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,uDAAuD;sBACpG;wBACA;oBACF;oBACA,MAAM,cAAc;oBACpB,SAAS;wBACP,MAAM;wBACN,sBAAsB;oBACxB;gBACF;gBACA,QAAQ,SAAS,OAAO,KAAK;oBAC3B,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE;wBAC/B,MAAM,cAAc;wBACpB,SAAS;4BACP,MAAM;4BACN,sBAAsB;wBACxB;oBACF;gBACF;gBACA,UAAU,SAAS,SAAS,KAAK;oBAC/B,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE;wBAC/B,MAAM,cAAc;wBACpB,SAAS;4BACP,MAAM;4BACN,sBAAsB;wBACxB;oBACF;gBACF;YACF;QACF;oDAAG;QAAC;QAAU;QAAQ;KAAqB;IAE3C,gBAAgB;IAChB,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,SAAU,UAAU;YAClD,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;gBACd,IAAI,WAAW,OAAO;gBACtB,SAAS,WAAW,OAAO;YAC7B,GAAG;QACL;iDAAG;QAAC;KAAW;IACf,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,SAAU,KAAK,EAAE,MAAM;YACpD,IAAI;YACJ,IAAI,OAAO,UAAU,KAAK,IAAI,CAAC,IAAI,OACjC,eAAe,KAAK,YAAY,EAChC,cAAc,KAAK,MAAM,EACzB,SAAS,gBAAgB,KAAK,IAAI,QAAQ,aAC1C,MAAM,KAAK,GAAG,EACd,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,MAAM;YAC7C,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,wBAAwB,MAAM,gBAAgB,EAC9C,mBAAmB,0BAA0B,KAAK,IAAI,QAAQ;YAChE,sBAAsB,gBAAgB,kBAAkB,QAAQ;YAChE,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,WAAW;yDAAK,SAAU,QAAQ;oBACrF,QAAQ,OAAO,GAAG;gBACpB;yDAAI,UAAU,EAAE,GAAG,WAAW,MAAM,EAAE,UAAU,IAAI,GAAG,WAAW,SAAS,CAAC,kBAAkB,GAAG,QAAQ,IAAI,CAAC,aAAa,GAAG,YAAY,KAAK,WAAW,OAAO,EAAE,UAAU,YAAY,GAAG,qBAAqB;yDAAc;oBAC7N,SAAS;wBACP,MAAM;oBACR;gBACF;yDAAI,SAAS,GAAG;QAClB;gDAAG;QAAC;QAAU;QAAuB;KAAW;IAChD,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,SAAU,MAAM;YAC7C,IAAI,WAAW;YACf,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,WAAW,MAAM,IAAI,EACrB,YAAY,MAAM,KAAK,EACvB,eAAe,MAAM,MAAM,EAC3B,SAAS,iBAAiB,KAAK,IAAI,QAAQ,cAC3C,MAAM,MAAM,GAAG,EACf,cAAc,MAAM,WAAW,EAC/B,cAAc,MAAM,WAAW,EAC/B,UAAU,MAAM,OAAO;YACvB,MAAM,OAAO;YACb,IAAI,WAAW,MAAM,QAAQ,EAC7B,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;YAC9C,IAAI,kBAAkB,OAAO,OAAO,EAClC,cAAc,gBAAgB,KAAK,EACnC,cAAc,gBAAgB,KAAK;YACrC,IAAI,mBAAmB,gBAAgB,UAAU,WAAW,YAAY,KAAK,EAAE,+CAC7E,QAAQ,gBAAgB,CAAC,EAAE;YAC7B,IAAI,cAAc;YAClB,IAAI,qBAAqB;YACzB,IAAI,sBAAsB,SAAS;gBACjC,IAAI,UAAU,YAAY,gBAAgB,EAAE;oBAC1C;gBACF;gBACA,gBAAgB,OAAO,GAAG;gBAC1B,SAAS;oBACP,MAAM;oBACN,OAAO;oBACP,UAAU;gBACZ;YACF;YACA,IAAI,kBAAkB,SAAS;gBAC7B,SAAS;oBACP,MAAM;oBACN,OAAO;gBACT;YACF;YACA,IAAI,sBAAsB,SAAS,oBAAoB,CAAC;gBACtD,OAAO,EAAE,cAAc;YACzB;YACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,WAAW;yDAAK,SAAU,QAAQ;oBACrF,IAAI,UAAU;wBACZ,SAAS,OAAO,CAAC,WAAW,SAAS,CAAC,OAAO,GAAG;oBAClD;gBACF;yDAAI,UAAU,QAAQ,GAAG,UAAU,UAAU,IAAI,GAAG,UAAU,SAAS,CAAC,gBAAgB,GAAG,KAAK,CAAC,UAAU,YAAY,gBAAgB,GAAG,UAAU,EAAE,GAAG,WAAW,SAAS,CAAC,QAAQ,SAAS,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,YAAY,GAAG,qBAAqB,oBAAoB,kBAAkB,KAAK,GAAG;gBAClT,aAAa,qBAAqB,aAAa;gBAC/C,aAAa,qBAAqB,aAAa;YACjD,GAAG;QACL;gDAAG;QAAC;QAAU;QAAQ;QAAiB;KAAW;IAClD,IAAI,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,SAAU,MAAM;YACrD,IAAI;YACJ,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,UAAU,MAAM,OAAO;YACvB,MAAM,OAAO;YACb,IAAI,eAAe,MAAM,MAAM,EAC/B,SAAS,iBAAiB,KAAK,IAAI,QAAQ,cAC3C,MAAM,MAAM,GAAG,EACf,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;YAC9C,IAAI,cAAc,OAAO,OAAO,CAAC,KAAK;YACtC,IAAI,0BAA0B,SAAS;gBACrC,SAAS;oBACP,MAAM;gBACR;YACF;YACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,WAAW;iEAAK,SAAU,gBAAgB;oBAC7F,gBAAgB,OAAO,GAAG;gBAC5B;iEAAI,SAAS,CAAC,gBAAgB,GAAG,WAAW,MAAM,EAAE,SAAS,CAAC,gBAAgB,GAAG,YAAY,MAAM,EAAE,UAAU,EAAE,GAAG,WAAW,cAAc,EAAE,UAAU,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG;gBACjN,SAAS,qBAAqB,SAAS;YACzC,IAAI;QACN;wDAAG;QAAC;QAAU;QAAQ;KAAW;IACjC,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,SAAU,MAAM,EAAE,MAAM;YACtD,IAAI;YACJ,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,YAAY,MAAM,SAAS,EAC3B,WAAW,MAAM,QAAQ,EACzB,UAAU,MAAM,OAAO,EACvB,UAAU,MAAM,OAAO,EACvB,SAAS,MAAM,MAAM;YACrB,MAAM,YAAY;YAClB,IAAI,eAAe,MAAM,MAAM,EAC/B,SAAS,iBAAiB,KAAK,IAAI,QAAQ,cAC3C,MAAM,MAAM,GAAG,EACf,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;YAC9C,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,wBAAwB,MAAM,gBAAgB,EAC9C,mBAAmB,0BAA0B,KAAK,IAAI,QAAQ;YAChE,sBAAsB,iBAAiB,kBAAkB,QAAQ;YACjE,IAAI,cAAc,OAAO,OAAO,CAAC,KAAK;YACtC,IAAI,qBAAqB,SAAS,mBAAmB,KAAK;gBACxD,IAAI,MAAM,kBAAkB;gBAC5B,IAAI,OAAO,oBAAoB,CAAC,IAAI,EAAE;oBACpC,oBAAoB,CAAC,IAAI,CAAC;gBAC5B;YACF;YACA,IAAI,oBAAoB,SAAS,kBAAkB,KAAK;gBACtD,SAAS;oBACP,MAAM;oBACN,YAAY,MAAM,MAAM,CAAC,KAAK;gBAChC;YACF;YACA,IAAI,kBAAkB,SAAS,gBAAgB,KAAK;gBAClD,wBAAwB,GACxB,IAAI,YAAY,MAAM,IAAI,CAAC,yBAAyB,OAAO,CAAC,WAAW,EAAE;oBACvE,IAAI,oBAAoB,MAAM,aAAa,KAAK,QAAQ,YAAY,QAAQ,CAAC,aAAa,KAAK,YAAY,QAAQ,CAAC,IAAI;oBACxH,SAAS;wBACP,MAAM;wBACN,YAAY,CAAC;oBACf;gBACF;YACF;YACA,IAAI,mBAAmB,SAAS;gBAC9B,IAAI,CAAC,YAAY,MAAM,EAAE;oBACvB,SAAS;wBACP,MAAM;oBACR;gBACF;YACF;YAEA,iCAAiC,GACjC,IAAI,cAAc;YAClB,IAAI,gBAAgB,CAAC;YACrB,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAClB,IAAI;gBACJ,gBAAgB,CAAC,iBAAiB,CAAC,GAAG,cAAc,CAAC,YAAY,GAAG,qBAAqB,UAAU,SAAS,oBAAoB,eAAe,SAAS,GAAG,qBAAqB,WAAW,qBAAqB,eAAe,MAAM,GAAG,qBAAqB,QAAQ,kBAAkB,eAAe,OAAO,GAAG,qBAAqB,SAAS,mBAAmB,cAAc;YACjX;YACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,WAAW;0DAAK,SAAU,SAAS;oBACtF,SAAS,OAAO,GAAG;gBACrB;0DAAI,SAAS,CAAC,wBAAwB,GAAG,YAAY,MAAM,IAAI,YAAY,gBAAgB,GAAG,CAAC,IAAI,WAAW,SAAS,CAAC,YAAY,gBAAgB,IAAI,IAAI,SAAS,CAAC,oBAAoB,GAAG,QAAQ,SAAS,CAAC,gBAAgB,GAAG,WAAW,MAAM,EAAE,SAAS,CAAC,gBAAgB,GAAG,YAAY,MAAM,EAAE,SAAS,CAAC,kBAAkB,GAAG,QAAQ,IAAI,CAAC,aAAa,GAAG,YAAY,KAAK,WAAW,OAAO,EAAE,UAAU,YAAY,GAAG,OAAO,UAAU,EAAE,GAAG,WAAW,OAAO,EAAE,UAAU,IAAI,GAAG,YAAY,UAAU,KAAK,GAAG,YAAY,UAAU,EAAE,SAAS,GAAG,eAAe;QAC9iB;iDAAG;QAAC;QAAuB;QAAQ;QAAY;QAAsB;QAAU;QAA0B;KAAY;IAErH,UAAU;IACV,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC3B,SAAS;gBACP,MAAM;YACR;QACF;8CAAG;QAAC;KAAS;IACb,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YAC1B,SAAS;gBACP,MAAM;YACR;QACF;6CAAG;QAAC;KAAS;IACb,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YACzB,SAAS;gBACP,MAAM;YACR;QACF;4CAAG;QAAC;KAAS;IACb,IAAI,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,SAAU,mBAAmB;YACjE,SAAS;gBACP,MAAM;gBACN,kBAAkB;YACpB;QACF;uDAAG;QAAC;KAAS;IACb,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,SAAU,eAAe;YACpD,SAAS;gBACP,MAAM;gBACN,cAAc;YAChB;QACF;8CAAG;QAAC;KAAS;IACb,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,SAAU,aAAa;YACrD,SAAS;gBACP,MAAM;gBACN,YAAY;YACd;QACF;iDAAG;QAAC;KAAS;IACb,IAAI,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE;YACtB,SAAS;gBACP,MAAM;YACR;QACF;yCAAG;QAAC;KAAS;IACb,OAAO;QACL,gBAAgB;QAChB,cAAc;QACd,eAAe;QACf,cAAc;QACd,eAAe;QACf,sBAAsB;QACtB,WAAW;QACX,YAAY;QACZ,UAAU;QACV,WAAW;QACX,qBAAqB;QACrB,eAAe;QACf,YAAY;QACZ,OAAO;QACP,SAAS;QACT,kBAAkB;QAClB,QAAQ;QACR,cAAc;QACd,YAAY;IACd;AACF;AAEA,IAAI,qBAAqB;IACvB,aAAa,CAAC;IACd,eAAe,EAAE;AACnB;AAEA;;;;;;;;CAQC,GACD,SAAS,gBAAgB,KAAK,EAAE,OAAO;IACrC,OAAO,kBAAkB,OAAO,SAAS;AAC3C;AAEA;;;;;;;CAOC,GACD,SAAS,gBAAgB,KAAK,EAAE,OAAO;IACrC,OAAO,kBAAkB,OAAO,SAAS;AAC3C;AAEA;;;;;;CAMC,GACD,SAAS,gBAAgB,KAAK;IAC5B,IAAI,cAAc,gBAAgB,OAAO;IACzC,IAAI,gBAAgB,gBAAgB,OAAO;IAC3C,OAAO;QACL,aAAa;QACb,eAAe;IACjB;AACF;AAEA;;;;;;;;CAQC,GACD,SAAS,4BAA4B,KAAK;IACxC,IAAI,MAAM,QAAQ,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE;QACpE,OAAO;IACT;IACA,IAAI,UAAU,MAAM,MAAM;IAC1B,IAAI,mBAAmB,oBACvB,6BAA6B;IAC7B,QAAQ,KAAK,KAAK,MAAM,CACxB,yBAAyB;IACzB,2EAA2E;IAC3E,QAAQ,cAAc,KAAK,KAAK,QAAQ,YAAY,KAAK,CAAC,GAAG;QAC3D,OAAO;IACT;IACA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,sBAAsB,mBAAmB;IAChD,IAAI,sBAAsB,oBAAoB,mBAAmB,EAC/D,oBAAoB,oBAAoB,YAAY;IACtD,OAAO,kBAAkB,uBAAuB;AAClD;AACA,IAAI,YAAY;IACd,eAAe,yIAAA,CAAA,UAAS,CAAC,KAAK;IAC9B,sBAAsB,yIAAA,CAAA,UAAS,CAAC,KAAK;IACrC,sBAAsB,yIAAA,CAAA,UAAS,CAAC,KAAK;IACrC,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B,uBAAuB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACrC,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;IAC5B,aAAa,yIAAA,CAAA,UAAS,CAAC,MAAM;IAC7B,oBAAoB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACpC,oBAAoB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACpC,qBAAqB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACnC,uBAAuB,yIAAA,CAAA,UAAS,CAAC,IAAI;IACrC,mBAAmB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACnC,uBAAuB,yIAAA,CAAA,UAAS,CAAC,MAAM;IACvC,aAAa,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QAC3B,kBAAkB,yIAAA,CAAA,UAAS,CAAC,IAAI;QAChC,qBAAqB,yIAAA,CAAA,UAAS,CAAC,IAAI;QACnC,UAAU,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YACxB,gBAAgB,yIAAA,CAAA,UAAS,CAAC,IAAI;YAC9B,eAAe,yIAAA,CAAA,UAAS,CAAC,GAAG;YAC5B,MAAM,yIAAA,CAAA,UAAS,CAAC,GAAG;QACrB;IACF;AACF;AACA,IAAI,eAAe;IACjB,cAAc,eAAe,YAAY;IACzC,cAAc,eAAe,YAAY;IACzC,aAAa,eAAe,WAAW;IACvC,uBAAuB;IACvB,mBAAmB;IACnB,uBAAuB;AACzB;AAEA,qDAAqD;AACrD,IAAI,oBAAoB;AACxB,wBAAwB,GACxB,wCAA2C;IACzC,oBAAoB,SAAS,kBAAkB,OAAO,EAAE,MAAM;QAC5D,yIAAA,CAAA,UAAS,CAAC,cAAc,CAAC,WAAW,SAAS,QAAQ,OAAO,IAAI;IAClE;AACF;AAEA,IAAI,oBAAoB,uCAAwC;AAChE,IAAI,4BAA4B,uCAAwC;AACxE,IAAI,+BAA+B,uCAAwC;AAC3E,IAAI,oCAAoC,uCAAwC;AAChF,IAAI,wCAAwC,uCAAwC;AACpF,IAAI,oCAAoC,uCAAwC;AAChF,IAAI,2BAA2B,uCAAwC;AACvE,IAAI,gBAAgB,uCAAwC;AAC5D,IAAI,0BAA0B,uCAAwC;AACtE,IAAI,6BAA6B,uCAAwC;AACzE,IAAI,2BAA2B,uCAAwC;AACvE,IAAI,yBAAyB,uCAAwC;AACrE,IAAI,gBAAgB,uCAAwC;AAE5D,IAAI,mBAAmB,WAAW,GAAE,OAAO,MAAM,CAAC;IAChD,WAAW;IACX,mBAAmB;IACnB,2BAA2B;IAC3B,8BAA8B;IAC9B,mCAAmC;IACnC,uCAAuC;IACvC,mCAAmC;IACnC,0BAA0B;IAC1B,eAAe;IACf,yBAAyB;IACzB,4BAA4B;IAC5B,0BAA0B;IAC1B,wBAAwB;IACxB,eAAe;AACjB;AAEA,6BAA6B,GAC7B,SAAS,kCAAkC,KAAK,EAAE,MAAM;IACtD,IAAI,OAAO,OAAO,IAAI,EACpB,QAAQ,OAAO,KAAK,EACpB,QAAQ,OAAO,KAAK,EACpB,eAAe,OAAO,YAAY;IACpC,IAAI,cAAc,MAAM,WAAW,EACjC,gBAAgB,MAAM,aAAa;IACrC,IAAI;IACJ,OAAQ;QACN,KAAK;YACH,UAAU;gBACR,aAAa;YACf;YACA;QACF,KAAK;YACH,UAAU;gBACR,aAAa,cAAc,IAAI,IAAI,IAAI,cAAc;YACvD;YACA;QACF,KAAK;YACH,UAAU;gBACR,aAAa,cAAc,KAAK,cAAc,MAAM,GAAG,CAAC,IAAI,cAAc;YAC5E;YACA;QACF,KAAK;QACL,KAAK;YACH;gBACE,IAAI,cAAc,GAAG;oBACnB;gBACF;gBACA,IAAI,iBAAiB;gBACrB,IAAI,cAAc,MAAM,KAAK,GAAG;oBAC9B,iBAAiB,CAAC;gBACpB,OAAO,IAAI,gBAAgB,cAAc,MAAM,GAAG,GAAG;oBACnD,iBAAiB,cAAc,MAAM,GAAG;gBAC1C;gBACA,UAAU,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;oBACjB,eAAe,EAAE,CAAC,MAAM,CAAC,cAAc,KAAK,CAAC,GAAG,cAAc,cAAc,KAAK,CAAC,cAAc;gBAClG,GAAG;oBACD,aAAa;gBACf;gBACA;YACF;QACF,KAAK;YACH,UAAU;gBACR,aAAa,cAAc,MAAM,GAAG;YACtC;YACA;QACF,KAAK;YACH,UAAU;gBACR,eAAe,cAAc,KAAK,CAAC,GAAG,cAAc,MAAM,GAAG;YAC/D;YACA;QACF,KAAK;YACH,UAAU;gBACR,eAAe,EAAE,CAAC,MAAM,CAAC,eAAe;oBAAC;iBAAa;YACxD;YACA;QACF,KAAK;YACH,UAAU;gBACR,aAAa,CAAC;YAChB;YACA;QACF,KAAK;YACH;gBACE,IAAI,kBAAkB;gBACtB,IAAI,oBAAoB,cAAc,OAAO,CAAC;gBAC9C,IAAI,oBAAoB,GAAG;oBACzB;gBACF;gBACA,IAAI,cAAc,MAAM,KAAK,GAAG;oBAC9B,kBAAkB,CAAC;gBACrB,OAAO,IAAI,sBAAsB,cAAc,MAAM,GAAG,GAAG;oBACzD,kBAAkB,cAAc,MAAM,GAAG;gBAC3C;gBACA,UAAU;oBACR,eAAe,EAAE,CAAC,MAAM,CAAC,cAAc,KAAK,CAAC,GAAG,oBAAoB,cAAc,KAAK,CAAC,oBAAoB;oBAC5G,aAAa;gBACf;gBACA;YACF;QACF,KAAK;YACH;gBACE,IAAI,mBAAmB,OAAO,aAAa;gBAC3C,UAAU;oBACR,eAAe;gBACjB;gBACA;YACF;QACF,KAAK;YACH;gBACE,IAAI,mBAAmB,OAAO,WAAW;gBACzC,UAAU;oBACR,aAAa;gBACf;gBACA;YACF;QACF,KAAK;YACH,UAAU;gBACR,aAAa,gBAAgB,OAAO;gBACpC,eAAe,gBAAgB,OAAO;YACxC;YACA;QACF;YACE,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;AAC7B;AAEA,IAAI,YAAY;IAAC;IAAU;IAAO;IAAW;IAAa;IAAgB;CAAQ,EAChF,aAAa;IAAC;IAAU;IAAO;IAAa;IAAW;CAAmB;AAC5E,qBAAqB,gBAAgB,GAAG;AACxC,SAAS,qBAAqB,SAAS;IACrC,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY,CAAC;IACf;IACA,kBAAkB,WAAW;IAC7B,oCAAoC;IACpC,IAAI,QAAQ,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,cAAc;IACvC,IAAI,wBAAwB,MAAM,qBAAqB,EACrD,eAAe,MAAM,YAAY,EACjC,cAAc,MAAM,WAAW,EAC/B,oBAAoB,MAAM,iBAAiB,EAC3C,wBAAwB,MAAM,qBAAqB;IAErD,gBAAgB;IAChB,IAAI,wBAAwB,uBAAuB,mCAAmC,gBAAgB,QAAQ,QAC5G,QAAQ,qBAAqB,CAAC,EAAE,EAChC,WAAW,qBAAqB,CAAC,EAAE;IACrC,IAAI,cAAc,MAAM,WAAW,EACjC,gBAAgB,MAAM,aAAa;IAErC,QAAQ;IACR,IAAI,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,IAAI,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,IAAI,2BAA2B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACtC,IAAI,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAC5B,iBAAiB,OAAO,GAAG,EAAE;IAC7B,IAAI,SAAS,aAAa;QACxB,OAAO;QACP,OAAO;IACT;IAEA,WAAW;IACX,wDAAwD,GACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,kBAAkB,OAAO,IAAI,OAAO;gBACtC;YACF;YACA,IAAI,cAAc,MAAM,GAAG,yBAAyB,OAAO,CAAC,MAAM,EAAE;gBAClE,IAAI,sBAAsB,yBAAyB,OAAO,CAAC,IAAI;0EAAC,SAAU,IAAI;wBAC5E,OAAO,cAAc,OAAO,CAAC,QAAQ;oBACvC;;gBACA,UAAU,sBAAsB;oBAC9B,cAAc;oBACd,aAAa,cAAc,MAAM;oBACjC,qBAAqB;oBACrB,aAAa;oBACb,oBAAoB,aAAa,CAAC,YAAY;gBAChD,IAAI,YAAY,QAAQ;YAC1B;YACA,yBAAyB,OAAO,GAAG;QAEnC,uDAAuD;QACzD;yCAAG;QAAC,cAAc,MAAM;KAAC;IACzB,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,kBAAkB,OAAO,EAAE;gBAC7B;YACF;YACA,IAAI,gBAAgB,CAAC,KAAK,YAAY,OAAO,EAAE;gBAC7C,YAAY,OAAO,CAAC,KAAK;YAC3B,OAAO,IAAI,iBAAiB,OAAO,CAAC,YAAY,EAAE;gBAChD,iBAAiB,OAAO,CAAC,YAAY,CAAC,KAAK;YAC7C;QACF;yCAAG;QAAC;KAAY;IAChB,yBAAyB;QACvB,gBAAgB,kBAAkB,OAAO;QACzC,OAAO;QACP,OAAO;IACT;IACA,IAAI,wBAAwB,4BAA4B;IACxD,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,kBAAkB,OAAO,GAAG;YAC5B;kDAAO;oBACL,kBAAkB,OAAO,GAAG;gBAC9B;;QACF;yCAAG,EAAE;IAEL,2BAA2B;IAC3B,IAAI,8BAA8B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qEAAE;YACxC,IAAI;YACJ,OAAO,OAAO,CAAC,GAAG,IAAI,CAAC,sBAAsB;6EAAG;oBAC9C,SAAS;wBACP,MAAM;oBACR;gBACF;6EAAG,IAAI,CAAC,kBAAkB;6EAAG;oBAC3B,SAAS;wBACP,MAAM;oBACR;gBACF;6EAAG,KAAK,MAAM,GAAG,SAAS;gBACxB,SAAS;oBACP,MAAM;gBACR;YACF,GAAG,KAAK,SAAS,GAAG,SAAS;gBAC3B,SAAS;oBACP,MAAM;gBACR;YACF,GAAG;QACL;oEAAG;QAAC;QAAU;QAAmB;KAAsB;IACvD,IAAI,0BAA0B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iEAAE;YACpC,IAAI;YACJ,OAAO,QAAQ,CAAC,GAAG,KAAK,CAAC,sBAAsB;yEAAG,SAAU,KAAK;oBAC/D,IAAI,4BAA4B,QAAQ;wBACtC,SAAS;4BACP,MAAM;wBACR;oBACF;gBACF;yEAAG,MAAM,SAAS,GAAG,SAAS,UAAU,KAAK;gBAC3C,IAAI,4BAA4B,QAAQ;oBACtC,SAAS;wBACP,MAAM;oBACR;gBACF;YACF,GAAG;QACL;gEAAG;QAAC;QAAU;KAAsB;IAEpC,gBAAgB;IAChB,IAAI,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAAE,SAAU,KAAK;YACpD,IAAI;YACJ,IAAI,QAAQ,UAAU,KAAK,IAAI,CAAC,IAAI,OAClC,eAAe,MAAM,MAAM,EAC3B,SAAS,iBAAiB,KAAK,IAAI,QAAQ,cAC3C,MAAM,MAAM,GAAG,EACf,UAAU,MAAM,OAAO,EACvB,YAAY,MAAM,SAAS,EAC3B,mBAAmB,MAAM,YAAY,EACrC,YAAY,MAAM,KAAK,EACvB,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;YAC9C,IAAI,cAAc,OAAO,OAAO,CAAC,KAAK;YACtC,IAAI,mBAAmB,gBAAgB,kBAAkB,WAAW,YAAY,aAAa,EAAE,uDAC7F,QAAQ,gBAAgB,CAAC,EAAE;YAC7B,IAAI,cAAc,QAAQ,CAAC,KAAK,UAAU,YAAY,WAAW;YACjE,IAAI,0BAA0B,SAAS;gBACrC,SAAS;oBACP,MAAM;oBACN,OAAO;gBACT;YACF;YACA,IAAI,4BAA4B,SAAS,0BAA0B,KAAK;gBACtE,IAAI,MAAM,kBAAkB;gBAC5B,IAAI,OAAO,2BAA2B,CAAC,IAAI,EAAE;oBAC3C,2BAA2B,CAAC,IAAI,CAAC;gBACnC;YACF;YACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,WAAW;0EAAK,SAAU,gBAAgB;oBAC7F,IAAI,kBAAkB;wBACpB,iBAAiB,OAAO,CAAC,IAAI,CAAC;oBAChC;gBACF;0EAAI,UAAU,QAAQ,GAAG,cAAc,IAAI,CAAC,GAAG,UAAU,OAAO,GAAG,qBAAqB,SAAS,0BAA0B,UAAU,SAAS,GAAG,qBAAqB,WAAW,4BAA4B,SAAS,GAAG;QAC3N;iEAAG;QAAC;QAAU;QAAQ;KAA4B;IAClD,IAAI,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,SAAU,MAAM,EAAE,MAAM;YACzD,IAAI;YACJ,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,eAAe,MAAM,MAAM,EAC3B,SAAS,iBAAiB,KAAK,IAAI,QAAQ,cAC3C,MAAM,MAAM,GAAG,EACf,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,wBAAwB,MAAM,gBAAgB,EAC9C,mBAAmB,0BAA0B,KAAK,IAAI,QAAQ,uBAC9D,OAAO,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;YAC9C,IAAI,QAAQ,WAAW,KAAK,IAAI,CAAC,IAAI,QACnC,wBAAwB,MAAM,gBAAgB,EAC9C,mBAAmB,0BAA0B,KAAK,IAAI,QAAQ;YAChE,sBAAsB,oBAAoB,kBAAkB,QAAQ;YACpE,IAAI,wBAAwB,SAAS,sBAAsB,KAAK;gBAC9D,IAAI,MAAM,kBAAkB;gBAC5B,IAAI,OAAO,uBAAuB,CAAC,IAAI,EAAE;oBACvC,uBAAuB,CAAC,IAAI,CAAC;gBAC/B;YACF;YACA,IAAI,sBAAsB,SAAS;gBACjC,SAAS;oBACP,MAAM;gBACR;YACF;YACA,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,OAAO,GAAG,WAAW;sEAAK,SAAU,YAAY;oBACzF,IAAI,cAAc;wBAChB,YAAY,OAAO,GAAG;oBACxB;gBACF;sEAAI,SAAS,GAAG,CAAC,oBAAoB;gBACnC,WAAW,qBAAqB,WAAW;gBAC3C,SAAS,qBAAqB,SAAS;YACzC,GAAG;QACL;6DAAG;QAAC;QAAU;QAAyB;KAAsB;IAE7D,UAAU;IACV,IAAI,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,SAAU,YAAY;YACtD,SAAS;gBACP,MAAM;gBACN,cAAc;YAChB;QACF;4DAAG;QAAC;KAAS;IACb,IAAI,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE,SAAU,YAAY;YACzD,SAAS;gBACP,MAAM;gBACN,cAAc;YAChB;QACF;+DAAG;QAAC;KAAS;IACb,IAAI,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,SAAU,gBAAgB;YAC3D,SAAS;gBACP,MAAM;gBACN,eAAe;YACjB;QACF;6DAAG;QAAC;KAAS;IACb,IAAI,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,SAAU,cAAc;YACvD,SAAS;gBACP,MAAM;gBACN,aAAa;YACf;QACF;2DAAG;QAAC;KAAS;IACb,IAAI,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YACtB,SAAS;gBACP,MAAM;YACR;QACF;kDAAG;QAAC;KAAS;IACb,OAAO;QACL,sBAAsB;QACtB,kBAAkB;QAClB,iBAAiB;QACjB,oBAAoB;QACpB,kBAAkB;QAClB,gBAAgB;QAChB,OAAO;QACP,eAAe;QACf,aAAa;IACf;AACF", "ignoreList": [0], "debugId": null}}]}