{"/api/auth/login/route": "/api/auth/login", "/api/auth/signup/route": "/api/auth/signup", "/api/auth/signin/route": "/api/auth/signin", "/api/auth/register/route": "/api/auth/register", "/api/certificates/route": "/api/certificates", "/api/chapters/[id]/route": "/api/chapters/[id]", "/api/class-enrollments/[id]/route": "/api/class-enrollments/[id]", "/api/chapters/route": "/api/chapters", "/api/class-enrollments/route": "/api/class-enrollments", "/api/classes/[id]/route": "/api/classes/[id]", "/api/classes/route": "/api/classes", "/api/courses/[id]/route": "/api/courses/[id]", "/api/enrollments/[id]/route": "/api/enrollments/[id]", "/api/courses/route": "/api/courses", "/api/enrollments/route": "/api/enrollments", "/api/institutions/[id]/route": "/api/institutions/[id]", "/api/institutions/route": "/api/institutions", "/api/modules/[id]/route": "/api/modules/[id]", "/api/modules/route": "/api/modules", "/api/questions/[id]/route": "/api/questions/[id]", "/api/questions/route": "/api/questions", "/api/quizzes/[id]/route": "/api/quizzes/[id]", "/api/quizzes/route": "/api/quizzes", "/api/reports/route": "/api/reports", "/api/subscriptions/route": "/api/subscriptions", "/api/upload/route": "/api/upload", "/api/users/[id]/route": "/api/users/[id]", "/api/users/route": "/api/users", "/favicon.ico/route": "/favicon.ico", "/icon.png/route": "/icon.png", "/_not-found/page": "/_not-found", "/page": "/", "/auth/sign-in/[[...sign-in]]/page": "/auth/sign-in/[[...sign-in]]", "/auth/sign-up/[[...sign-up]]/page": "/auth/sign-up/[[...sign-up]]", "/(students-page)/courses/page": "/courses", "/(students-page)/my-courses/page": "/my-courses", "/(course-view)/my-courses/[courseId]/exam/page": "/my-courses/[courseId]/exam", "/(course-view)/my-courses/[courseId]/page": "/my-courses/[courseId]", "/(course-view)/my-courses/[courseId]/exam/results/page": "/my-courses/[courseId]/exam/results", "/dashboard/kanban/page": "/dashboard/kanban", "/dashboard/page": "/dashboard", "/dashboard/product/[productId]/page": "/dashboard/product/[productId]", "/dashboard/profile/[[...profile]]/page": "/dashboard/profile/[[...profile]]", "/dashboard/product/page": "/dashboard/product", "/dashboard/admin/institutions/[id]/page": "/dashboard/admin/institutions/[id]", "/dashboard/admin/institutions/new/page": "/dashboard/admin/institutions/new", "/dashboard/admin/institutions/page": "/dashboard/admin/institutions", "/dashboard/admin/subscriptions/page": "/dashboard/admin/subscriptions", "/dashboard/admin/users/[id]/page": "/dashboard/admin/users/[id]", "/dashboard/admin/page": "/dashboard/admin", "/dashboard/admin/users/new/page": "/dashboard/admin/users/new", "/dashboard/admin/users/page": "/dashboard/admin/users", "/dashboard/overview/@area_stats/page": "/dashboard/overview", "/dashboard/overview/@bar_stats/page": "/dashboard/overview", "/dashboard/overview/@pie_stats/page": "/dashboard/overview", "/dashboard/overview/@sales/page": "/dashboard/overview", "/dashboard/student/courses/[id]/page": "/dashboard/student/courses/[id]", "/dashboard/student/certificates/page": "/dashboard/student/certificates", "/dashboard/student/courses/page": "/dashboard/student/courses", "/dashboard/student/page": "/dashboard/student", "/dashboard/student/progress/page": "/dashboard/student/progress", "/dashboard/teacher/classes/[id]/courses/page": "/dashboard/teacher/classes/[id]/courses", "/dashboard/teacher/classes/[id]/students/page": "/dashboard/teacher/classes/[id]/students", "/dashboard/teacher/classes/[id]/page": "/dashboard/teacher/classes/[id]", "/dashboard/teacher/classes/page": "/dashboard/teacher/classes", "/dashboard/teacher/classes/new/page": "/dashboard/teacher/classes/new", "/dashboard/teacher/courses/[id]/page": "/dashboard/teacher/courses/[id]", "/dashboard/teacher/courses/page": "/dashboard/teacher/courses", "/dashboard/teacher/courses/generate/page": "/dashboard/teacher/courses/generate", "/dashboard/teacher/reports/page": "/dashboard/teacher/reports", "/dashboard/teacher/page": "/dashboard/teacher", "/dashboard/teacher/courses/new/page": "/dashboard/teacher/courses/new"}