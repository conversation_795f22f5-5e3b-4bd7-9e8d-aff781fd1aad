{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalComposerContext.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { createContext, useContext } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\nconst LexicalComposerContext = /*#__PURE__*/createContext(null);\nfunction createLexicalComposerContext(parent, theme) {\n  let parentContext = null;\n  if (parent != null) {\n    parentContext = parent[1];\n  }\n  function getTheme() {\n    if (theme != null) {\n      return theme;\n    }\n    return parentContext != null ? parentContext.getTheme() : null;\n  }\n  return {\n    getTheme\n  };\n}\nfunction useLexicalComposerContext() {\n  const composerContext = useContext(LexicalComposerContext);\n  if (composerContext == null) {\n    {\n      formatDevErrorMessage(`LexicalComposerContext.useLexicalComposerContext: cannot find a LexicalComposerContext`);\n    }\n  }\n  return composerContext;\n}\n\nexport { LexicalComposerContext, createLexicalComposerContext, useLexicalComposerContext };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;AAED;;AAEA;;;;;;CAMC,GAED,qEAAqE;AAErE,SAAS,sBAAsB,OAAO;IACpC,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,yBAAyB,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;AAC1D,SAAS,6BAA6B,MAAM,EAAE,KAAK;IACjD,IAAI,gBAAgB;IACpB,IAAI,UAAU,MAAM;QAClB,gBAAgB,MAAM,CAAC,EAAE;IAC3B;IACA,SAAS;QACP,IAAI,SAAS,MAAM;YACjB,OAAO;QACT;QACA,OAAO,iBAAiB,OAAO,cAAc,QAAQ,KAAK;IAC5D;IACA,OAAO;QACL;IACF;AACF;AACA,SAAS;IACP,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IACnC,IAAI,mBAAmB,MAAM;QAC3B;YACE,sBAAsB,CAAC,sFAAsF,CAAC;QAChH;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalHistoryPlugin.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { createEmptyHistoryState, registerHistory } from '@lexical/history';\nexport { createEmptyHistoryState } from '@lexical/history';\nimport { useMemo, useEffect } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction useHistory(editor, externalHistoryState, delay = 1000) {\n  const historyState = useMemo(() => externalHistoryState || createEmptyHistoryState(), [externalHistoryState]);\n  useEffect(() => {\n    return registerHistory(editor, historyState, delay);\n  }, [delay, editor, historyState]);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction HistoryPlugin({\n  delay,\n  externalHistoryState\n}) {\n  const [editor] = useLexicalComposerContext();\n  useHistory(editor, externalHistoryState, delay);\n  return null;\n}\n\nexport { HistoryPlugin };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;AAEA;;;;;AAEA;;;;;;CAMC,GAED,SAAS,WAAW,MAAM,EAAE,oBAAoB,EAAE,QAAQ,IAAI;IAC5D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE,IAAM,wBAAwB,CAAA,GAAA,iKAAA,CAAA,0BAAuB,AAAD;2CAAK;QAAC;KAAqB;IAC5G,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,OAAO,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,cAAc;QAC/C;+BAAG;QAAC;QAAO;QAAQ;KAAa;AAClC;AAEA;;;;;;CAMC,GAED,SAAS,cAAc,EACrB,KAAK,EACL,oBAAoB,EACrB;IACC,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,WAAW,QAAQ,sBAAsB;IACzC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalContentEditable.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { useLayoutEffect, useEffect, forwardRef, useState, useCallback, useMemo } from 'react';\nimport { jsx, jsxs, Fragment } from 'react/jsx-runtime';\nimport { $canShowPlaceholderCurry } from '@lexical/text';\nimport { mergeRegister } from '@lexical/utils';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? useLayoutEffect : useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n// Source: https://github.com/gregberge/react-merge-refs/blob/main/src/index.tsx\n\nfunction mergeRefs(...refs) {\n  return value => {\n    refs.forEach(ref => {\n      if (typeof ref === 'function') {\n        ref(value);\n      } else if (ref != null) {\n        ref.current = value;\n      }\n    });\n  };\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction ContentEditableElementImpl({\n  editor,\n  ariaActiveDescendant,\n  ariaAutoComplete,\n  ariaControls,\n  ariaDescribedBy,\n  ariaErrorMessage,\n  ariaExpanded,\n  ariaInvalid,\n  ariaLabel,\n  ariaLabelledBy,\n  ariaMultiline,\n  ariaOwns,\n  ariaRequired,\n  autoCapitalize,\n  className,\n  id,\n  role = 'textbox',\n  spellCheck = true,\n  style,\n  tabIndex,\n  'data-testid': testid,\n  ...rest\n}, ref) {\n  const [isEditable, setEditable] = useState(editor.isEditable());\n  const handleRef = useCallback(rootElement => {\n    // defaultView is required for a root element.\n    // In multi-window setups, the defaultView may not exist at certain points.\n    if (rootElement && rootElement.ownerDocument && rootElement.ownerDocument.defaultView) {\n      editor.setRootElement(rootElement);\n    } else {\n      editor.setRootElement(null);\n    }\n  }, [editor]);\n  const mergedRefs = useMemo(() => mergeRefs(ref, handleRef), [handleRef, ref]);\n  useLayoutEffectImpl(() => {\n    setEditable(editor.isEditable());\n    return editor.registerEditableListener(currentIsEditable => {\n      setEditable(currentIsEditable);\n    });\n  }, [editor]);\n  return /*#__PURE__*/jsx(\"div\", {\n    \"aria-activedescendant\": isEditable ? ariaActiveDescendant : undefined,\n    \"aria-autocomplete\": isEditable ? ariaAutoComplete : 'none',\n    \"aria-controls\": isEditable ? ariaControls : undefined,\n    \"aria-describedby\": ariaDescribedBy\n    // for compat, only override aria-errormessage if ariaErrorMessage is defined\n    ,\n    ...(ariaErrorMessage != null ? {\n      'aria-errormessage': ariaErrorMessage\n    } : {}),\n    \"aria-expanded\": isEditable && role === 'combobox' ? !!ariaExpanded : undefined\n    // for compat, only override aria-invalid if ariaInvalid is defined\n    ,\n    ...(ariaInvalid != null ? {\n      'aria-invalid': ariaInvalid\n    } : {}),\n    \"aria-label\": ariaLabel,\n    \"aria-labelledby\": ariaLabelledBy,\n    \"aria-multiline\": ariaMultiline,\n    \"aria-owns\": isEditable ? ariaOwns : undefined,\n    \"aria-readonly\": isEditable ? undefined : true,\n    \"aria-required\": ariaRequired,\n    autoCapitalize: autoCapitalize,\n    className: className,\n    contentEditable: isEditable,\n    \"data-testid\": testid,\n    id: id,\n    ref: mergedRefs,\n    role: role,\n    spellCheck: spellCheck,\n    style: style,\n    tabIndex: tabIndex,\n    ...rest\n  });\n}\nconst ContentEditableElement = /*#__PURE__*/forwardRef(ContentEditableElementImpl);\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction canShowPlaceholderFromCurrentEditorState(editor) {\n  const currentCanShowPlaceholder = editor.getEditorState().read($canShowPlaceholderCurry(editor.isComposing()));\n  return currentCanShowPlaceholder;\n}\nfunction useCanShowPlaceholder(editor) {\n  const [canShowPlaceholder, setCanShowPlaceholder] = useState(() => canShowPlaceholderFromCurrentEditorState(editor));\n  useLayoutEffectImpl(() => {\n    function resetCanShowPlaceholder() {\n      const currentCanShowPlaceholder = canShowPlaceholderFromCurrentEditorState(editor);\n      setCanShowPlaceholder(currentCanShowPlaceholder);\n    }\n    resetCanShowPlaceholder();\n    return mergeRegister(editor.registerUpdateListener(() => {\n      resetCanShowPlaceholder();\n    }), editor.registerEditableListener(() => {\n      resetCanShowPlaceholder();\n    }));\n  }, [editor]);\n  return canShowPlaceholder;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n/**\n * @deprecated This type has been renamed to `ContentEditableProps` to provide a clearer and more descriptive name.\n * For backward compatibility, this type is still exported as `Props`, but it is recommended to migrate to using `ContentEditableProps` instead.\n *\n * @note This alias is maintained for compatibility purposes but may be removed in future versions.\n * Please update your codebase to use `ContentEditableProps` to ensure long-term maintainability.\n */\n\nconst ContentEditable = /*#__PURE__*/forwardRef(ContentEditableImpl);\nfunction ContentEditableImpl(props, ref) {\n  const {\n    placeholder,\n    ...rest\n  } = props;\n  const [editor] = useLexicalComposerContext();\n  return /*#__PURE__*/jsxs(Fragment, {\n    children: [/*#__PURE__*/jsx(ContentEditableElement, {\n      editor: editor,\n      ...rest,\n      ref: ref\n    }), placeholder != null && /*#__PURE__*/jsx(Placeholder, {\n      editor: editor,\n      content: placeholder\n    })]\n  });\n}\nfunction Placeholder({\n  content,\n  editor\n}) {\n  const showPlaceholder = useCanShowPlaceholder(editor);\n  const [isEditable, setEditable] = useState(editor.isEditable());\n  useLayoutEffect(() => {\n    setEditable(editor.isEditable());\n    return editor.registerEditableListener(currentIsEditable => {\n      setEditable(currentIsEditable);\n    });\n  }, [editor]);\n  if (!showPlaceholder) {\n    return null;\n  }\n  let placeholder = null;\n  if (typeof content === 'function') {\n    placeholder = content(isEditable);\n  } else if (content !== null) {\n    placeholder = content;\n  }\n  if (placeholder === null) {\n    return null;\n  }\n  return /*#__PURE__*/jsx(\"div\", {\n    \"aria-hidden\": true,\n    children: placeholder\n  });\n}\n\nexport { ContentEditable, ContentEditableElement };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;AAED;AACA;AACA;AACA;AACA;;;;;;AAEA;;;;;;CAMC,GAED,MAAM,cAAc,OAAO,WAAW,eAAe,OAAO,OAAO,QAAQ,KAAK,eAAe,OAAO,OAAO,QAAQ,CAAC,aAAa,KAAK;AAExI;;;;;;CAMC,GAGD,sDAAsD;AACtD,wCAAwC;AACxC,+CAA+C;AAC/C,MAAM,sBAAsB,cAAc,6JAAA,CAAA,kBAAe,GAAG,6JAAA,CAAA,YAAS;AAErE;;;;;;CAMC,GACD,gFAAgF;AAEhF,SAAS,UAAU,GAAG,IAAI;IACxB,OAAO,CAAA;QACL,KAAK,OAAO,CAAC,CAAA;YACX,IAAI,OAAO,QAAQ,YAAY;gBAC7B,IAAI;YACN,OAAO,IAAI,OAAO,MAAM;gBACtB,IAAI,OAAO,GAAG;YAChB;QACF;IACF;AACF;AAEA;;;;;;CAMC,GAED,SAAS,2BAA2B,EAClC,MAAM,EACN,oBAAoB,EACpB,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,YAAY,EACZ,WAAW,EACX,SAAS,EACT,cAAc,EACd,aAAa,EACb,QAAQ,EACR,YAAY,EACZ,cAAc,EACd,SAAS,EACT,EAAE,EACF,OAAO,SAAS,EAChB,aAAa,IAAI,EACjB,KAAK,EACL,QAAQ,EACR,eAAe,MAAM,EACrB,GAAG,MACJ,EAAE,GAAG;IACJ,MAAM,CAAC,YAAY,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,UAAU;IAC5D,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAA;YAC5B,8CAA8C;YAC9C,2EAA2E;YAC3E,IAAI,eAAe,YAAY,aAAa,IAAI,YAAY,aAAa,CAAC,WAAW,EAAE;gBACrF,OAAO,cAAc,CAAC;YACxB,OAAO;gBACL,OAAO,cAAc,CAAC;YACxB;QACF;4DAAG;QAAC;KAAO;IACX,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0DAAE,IAAM,UAAU,KAAK;yDAAY;QAAC;QAAW;KAAI;IAC5E;0DAAoB;YAClB,YAAY,OAAO,UAAU;YAC7B,OAAO,OAAO,wBAAwB;kEAAC,CAAA;oBACrC,YAAY;gBACd;;QACF;yDAAG;QAAC;KAAO;IACX,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAC7B,yBAAyB,aAAa,uBAAuB;QAC7D,qBAAqB,aAAa,mBAAmB;QACrD,iBAAiB,aAAa,eAAe;QAC7C,oBAAoB;QAGpB,GAAI,oBAAoB,OAAO;YAC7B,qBAAqB;QACvB,IAAI,CAAC,CAAC;QACN,iBAAiB,cAAc,SAAS,aAAa,CAAC,CAAC,eAAe;QAGtE,GAAI,eAAe,OAAO;YACxB,gBAAgB;QAClB,IAAI,CAAC,CAAC;QACN,cAAc;QACd,mBAAmB;QACnB,kBAAkB;QAClB,aAAa,aAAa,WAAW;QACrC,iBAAiB,aAAa,YAAY;QAC1C,iBAAiB;QACjB,gBAAgB;QAChB,WAAW;QACX,iBAAiB;QACjB,eAAe;QACf,IAAI;QACJ,KAAK;QACL,MAAM;QACN,YAAY;QACZ,OAAO;QACP,UAAU;QACV,GAAG,IAAI;IACT;AACF;AACA,MAAM,yBAAyB,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AAEvD;;;;;;CAMC,GAED,SAAS,yCAAyC,MAAM;IACtD,MAAM,4BAA4B,OAAO,cAAc,GAAG,IAAI,CAAC,CAAA,GAAA,2JAAA,CAAA,2BAAwB,AAAD,EAAE,OAAO,WAAW;IAC1G,OAAO;AACT;AACA,SAAS,sBAAsB,MAAM;IACnC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;0CAAE,IAAM,yCAAyC;;IAC5G;qDAAoB;YAClB,SAAS;gBACP,MAAM,4BAA4B,yCAAyC;gBAC3E,sBAAsB;YACxB;YACA;YACA,OAAO,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,sBAAsB;6DAAC;oBACjD;gBACF;6DAAI,OAAO,wBAAwB;6DAAC;oBAClC;gBACF;;QACF;oDAAG;QAAC;KAAO;IACX,OAAO;AACT;AAEA;;;;;;CAMC,GAGD;;;;;;CAMC,GAED,MAAM,kBAAkB,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AAChD,SAAS,oBAAoB,KAAK,EAAE,GAAG;IACrC,MAAM,EACJ,WAAW,EACX,GAAG,MACJ,GAAG;IACJ,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,OAAI,AAAD,EAAE,sKAAA,CAAA,WAAQ,EAAE;QACjC,UAAU;YAAC,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,wBAAwB;gBAClD,QAAQ;gBACR,GAAG,IAAI;gBACP,KAAK;YACP;YAAI,eAAe,QAAQ,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,aAAa;gBACvD,QAAQ;gBACR,SAAS;YACX;SAAG;IACL;AACF;AACA,SAAS,YAAY,EACnB,OAAO,EACP,MAAM,EACP;IACC,MAAM,kBAAkB,sBAAsB;IAC9C,MAAM,CAAC,YAAY,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,UAAU;IAC5D,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;uCAAE;YACd,YAAY,OAAO,UAAU;YAC7B,OAAO,OAAO,wBAAwB;+CAAC,CAAA;oBACrC,YAAY;gBACd;;QACF;sCAAG;QAAC;KAAO;IACX,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IACA,IAAI,cAAc;IAClB,IAAI,OAAO,YAAY,YAAY;QACjC,cAAc,QAAQ;IACxB,OAAO,IAAI,YAAY,MAAM;QAC3B,cAAc;IAChB;IACA,IAAI,gBAAgB,MAAM;QACxB,OAAO;IACT;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAC7B,eAAe;QACf,UAAU;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/useLexicalEditable.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { useLayoutEffect, useEffect, useMemo, useState, useRef } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? useLayoutEffect : useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Shortcut to Lexical subscriptions when values are used for render.\n * @param subscription - The function to create the {@link LexicalSubscription}. This function's identity must be stable (e.g. defined at module scope or with useCallback).\n */\nfunction useLexicalSubscription(subscription) {\n  const [editor] = useLexicalComposerContext();\n  const initializedSubscription = useMemo(() => subscription(editor), [editor, subscription]);\n  const [value, setValue] = useState(() => initializedSubscription.initialValueFn());\n  const valueRef = useRef(value);\n  useLayoutEffectImpl(() => {\n    const {\n      initialValueFn,\n      subscribe\n    } = initializedSubscription;\n    const currentValue = initialValueFn();\n    if (valueRef.current !== currentValue) {\n      valueRef.current = currentValue;\n      setValue(currentValue);\n    }\n    return subscribe(newValue => {\n      valueRef.current = newValue;\n      setValue(newValue);\n    });\n  }, [initializedSubscription, subscription]);\n  return value;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction subscription(editor) {\n  return {\n    initialValueFn: () => editor.isEditable(),\n    subscribe: callback => {\n      return editor.registerEditableListener(callback);\n    }\n  };\n}\n\n/**\n * Get the current value for {@link LexicalEditor.isEditable}\n * using {@link useLexicalSubscription}.\n * You should prefer this over manually observing the value with\n * {@link LexicalEditor.registerEditableListener},\n * which is a bit tricky to do correctly, particularly when using\n * React StrictMode (the default for development) or concurrency.\n */\nfunction useLexicalEditable() {\n  return useLexicalSubscription(subscription);\n}\n\nexport { useLexicalEditable };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;;;AAEA;;;;;;CAMC,GAED,MAAM,cAAc,OAAO,WAAW,eAAe,OAAO,OAAO,QAAQ,KAAK,eAAe,OAAO,OAAO,QAAQ,CAAC,aAAa,KAAK;AAExI;;;;;;CAMC,GAGD,sDAAsD;AACtD,wCAAwC;AACxC,+CAA+C;AAC/C,MAAM,sBAAsB,cAAc,6JAAA,CAAA,kBAAe,GAAG,6JAAA,CAAA,YAAS;AAErE;;;;;;CAMC,GAED;;;CAGC,GACD,SAAS,uBAAuB,YAAY;IAC1C,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mEAAE,IAAM,aAAa;kEAAS;QAAC;QAAQ;KAAa;IAC1F,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;2CAAE,IAAM,wBAAwB,cAAc;;IAC/E,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB;sDAAoB;YAClB,MAAM,EACJ,cAAc,EACd,SAAS,EACV,GAAG;YACJ,MAAM,eAAe;YACrB,IAAI,SAAS,OAAO,KAAK,cAAc;gBACrC,SAAS,OAAO,GAAG;gBACnB,SAAS;YACX;YACA,OAAO;8DAAU,CAAA;oBACf,SAAS,OAAO,GAAG;oBACnB,SAAS;gBACX;;QACF;qDAAG;QAAC;QAAyB;KAAa;IAC1C,OAAO;AACT;AAEA;;;;;;CAMC,GAED,SAAS,aAAa,MAAM;IAC1B,OAAO;QACL,gBAAgB,IAAM,OAAO,UAAU;QACvC,WAAW,CAAA;YACT,OAAO,OAAO,wBAAwB,CAAC;QACzC;IACF;AACF;AAEA;;;;;;;CAOC,GACD,SAAS;IACP,OAAO,uBAAuB;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalRichTextPlugin.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { useLexicalEditable } from '@lexical/react/useLexicalEditable';\nimport { $canShowPlaceholderCurry } from '@lexical/text';\nimport { mergeRegister } from '@lexical/utils';\nimport { useLayoutEffect, useEffect, useState, useMemo, Suspense } from 'react';\nimport { flushSync, createPortal } from 'react-dom';\nimport { jsx, jsxs, Fragment } from 'react/jsx-runtime';\nimport { registerDragonSupport } from '@lexical/dragon';\nimport { registerRichText } from '@lexical/rich-text';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? useLayoutEffect : useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction canShowPlaceholderFromCurrentEditorState(editor) {\n  const currentCanShowPlaceholder = editor.getEditorState().read($canShowPlaceholderCurry(editor.isComposing()));\n  return currentCanShowPlaceholder;\n}\nfunction useCanShowPlaceholder(editor) {\n  const [canShowPlaceholder, setCanShowPlaceholder] = useState(() => canShowPlaceholderFromCurrentEditorState(editor));\n  useLayoutEffectImpl(() => {\n    function resetCanShowPlaceholder() {\n      const currentCanShowPlaceholder = canShowPlaceholderFromCurrentEditorState(editor);\n      setCanShowPlaceholder(currentCanShowPlaceholder);\n    }\n    resetCanShowPlaceholder();\n    return mergeRegister(editor.registerUpdateListener(() => {\n      resetCanShowPlaceholder();\n    }), editor.registerEditableListener(() => {\n      resetCanShowPlaceholder();\n    }));\n  }, [editor]);\n  return canShowPlaceholder;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction useDecorators(editor, ErrorBoundary) {\n  const [decorators, setDecorators] = useState(() => editor.getDecorators());\n\n  // Subscribe to changes\n  useLayoutEffectImpl(() => {\n    return editor.registerDecoratorListener(nextDecorators => {\n      flushSync(() => {\n        setDecorators(nextDecorators);\n      });\n    });\n  }, [editor]);\n  useEffect(() => {\n    // If the content editable mounts before the subscription is added, then\n    // nothing will be rendered on initial pass. We can get around that by\n    // ensuring that we set the value.\n    setDecorators(editor.getDecorators());\n  }, [editor]);\n\n  // Return decorators defined as React Portals\n  return useMemo(() => {\n    const decoratedPortals = [];\n    const decoratorKeys = Object.keys(decorators);\n    for (let i = 0; i < decoratorKeys.length; i++) {\n      const nodeKey = decoratorKeys[i];\n      const reactDecorator = /*#__PURE__*/jsx(ErrorBoundary, {\n        onError: e => editor._onError(e),\n        children: /*#__PURE__*/jsx(Suspense, {\n          fallback: null,\n          children: decorators[nodeKey]\n        })\n      });\n      const element = editor.getElementByKey(nodeKey);\n      if (element !== null) {\n        decoratedPortals.push(/*#__PURE__*/createPortal(reactDecorator, element, nodeKey));\n      }\n    }\n    return decoratedPortals;\n  }, [ErrorBoundary, decorators, editor]);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction useRichTextSetup(editor) {\n  useLayoutEffectImpl(() => {\n    return mergeRegister(registerRichText(editor), registerDragonSupport(editor));\n\n    // We only do this for init\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [editor]);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction RichTextPlugin({\n  contentEditable,\n  // TODO Remove. This property is now part of ContentEditable\n  placeholder = null,\n  ErrorBoundary\n}) {\n  const [editor] = useLexicalComposerContext();\n  const decorators = useDecorators(editor, ErrorBoundary);\n  useRichTextSetup(editor);\n  return /*#__PURE__*/jsxs(Fragment, {\n    children: [contentEditable, /*#__PURE__*/jsx(Placeholder, {\n      content: placeholder\n    }), decorators]\n  });\n}\n\n// TODO remove\nfunction Placeholder({\n  content\n}) {\n  const [editor] = useLexicalComposerContext();\n  const showPlaceholder = useCanShowPlaceholder(editor);\n  const editable = useLexicalEditable();\n  if (!showPlaceholder) {\n    return null;\n  }\n  if (typeof content === 'function') {\n    return content(editable);\n  } else {\n    return content;\n  }\n}\n\nexport { RichTextPlugin };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA;;;;;;CAMC,GAED,MAAM,cAAc,OAAO,WAAW,eAAe,OAAO,OAAO,QAAQ,KAAK,eAAe,OAAO,OAAO,QAAQ,CAAC,aAAa,KAAK;AAExI;;;;;;CAMC,GAGD,sDAAsD;AACtD,wCAAwC;AACxC,+CAA+C;AAC/C,MAAM,sBAAsB,cAAc,6JAAA,CAAA,kBAAe,GAAG,6JAAA,CAAA,YAAS;AAErE;;;;;;CAMC,GAED,SAAS,yCAAyC,MAAM;IACtD,MAAM,4BAA4B,OAAO,cAAc,GAAG,IAAI,CAAC,CAAA,GAAA,2JAAA,CAAA,2BAAwB,AAAD,EAAE,OAAO,WAAW;IAC1G,OAAO;AACT;AACA,SAAS,sBAAsB,MAAM;IACnC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;0CAAE,IAAM,yCAAyC;;IAC5G;qDAAoB;YAClB,SAAS;gBACP,MAAM,4BAA4B,yCAAyC;gBAC3E,sBAAsB;YACxB;YACA;YACA,OAAO,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,sBAAsB;6DAAC;oBACjD;gBACF;6DAAI,OAAO,wBAAwB;6DAAC;oBAClC;gBACF;;QACF;oDAAG;QAAC;KAAO;IACX,OAAO;AACT;AAEA;;;;;;CAMC,GAED,SAAS,cAAc,MAAM,EAAE,aAAa;IAC1C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;kCAAE,IAAM,OAAO,aAAa;;IAEvE,uBAAuB;IACvB;6CAAoB;YAClB,OAAO,OAAO,yBAAyB;qDAAC,CAAA;oBACtC,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD;6DAAE;4BACR,cAAc;wBAChB;;gBACF;;QACF;4CAAG;QAAC;KAAO;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,wEAAwE;YACxE,sEAAsE;YACtE,kCAAkC;YAClC,cAAc,OAAO,aAAa;QACpC;kCAAG;QAAC;KAAO;IAEX,6CAA6C;IAC7C,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iCAAE;YACb,MAAM,mBAAmB,EAAE;YAC3B,MAAM,gBAAgB,OAAO,IAAI,CAAC;YAClC,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;gBAC7C,MAAM,UAAU,aAAa,CAAC,EAAE;gBAChC,MAAM,iBAAiB,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,eAAe;oBACrD,OAAO;gEAAE,CAAA,IAAK,OAAO,QAAQ,CAAC;;oBAC9B,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,6JAAA,CAAA,WAAQ,EAAE;wBACnC,UAAU;wBACV,UAAU,UAAU,CAAC,QAAQ;oBAC/B;gBACF;gBACA,MAAM,UAAU,OAAO,eAAe,CAAC;gBACvC,IAAI,YAAY,MAAM;oBACpB,iBAAiB,IAAI,CAAC,WAAW,GAAE,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,SAAS;gBAC3E;YACF;YACA,OAAO;QACT;gCAAG;QAAC;QAAe;QAAY;KAAO;AACxC;AAEA;;;;;;CAMC,GAED,SAAS,iBAAiB,MAAM;IAC9B;gDAAoB;YAClB,OAAO,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,CAAA,GAAA,+JAAA,CAAA,wBAAqB,AAAD,EAAE;QAErE,2BAA2B;QAC3B,uDAAuD;QACzD;+CAAG;QAAC;KAAO;AACb;AAEA;;;;;;CAMC,GAED,SAAS,eAAe,EACtB,eAAe,EACf,4DAA4D;AAC5D,cAAc,IAAI,EAClB,aAAa,EACd;IACC,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,MAAM,aAAa,cAAc,QAAQ;IACzC,iBAAiB;IACjB,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,OAAI,AAAD,EAAE,sKAAA,CAAA,WAAQ,EAAE;QACjC,UAAU;YAAC;YAAiB,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,aAAa;gBACxD,SAAS;YACX;YAAI;SAAW;IACjB;AACF;AAEA,cAAc;AACd,SAAS,YAAY,EACnB,OAAO,EACR;IACC,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,MAAM,kBAAkB,sBAAsB;IAC9C,MAAM,WAAW,CAAA,GAAA,mKAAA,CAAA,qBAAkB,AAAD;IAClC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IACA,IAAI,OAAO,YAAY,YAAY;QACjC,OAAO,QAAQ;IACjB,OAAO;QACL,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalErrorBoundary.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport * as React from 'react';\nimport { jsx } from 'react/jsx-runtime';\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  _setPrototypeOf(subClass, superClass);\n}\n\nvar changedArray = function changedArray(a, b) {\n  if (a === void 0) {\n    a = [];\n  }\n\n  if (b === void 0) {\n    b = [];\n  }\n\n  return a.length !== b.length || a.some(function (item, index) {\n    return !Object.is(item, b[index]);\n  });\n};\n\nvar initialState = {\n  error: null\n};\n\nvar ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ErrorBoundary, _React$Component);\n\n  function ErrorBoundary() {\n    var _this;\n\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(_args)) || this;\n    _this.state = initialState;\n\n    _this.resetErrorBoundary = function () {\n      var _this$props;\n\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      _this.props.onReset == null ? void 0 : (_this$props = _this.props).onReset.apply(_this$props, args);\n\n      _this.reset();\n    };\n\n    return _this;\n  }\n\n  ErrorBoundary.getDerivedStateFromError = function getDerivedStateFromError(error) {\n    return {\n      error: error\n    };\n  };\n\n  var _proto = ErrorBoundary.prototype;\n\n  _proto.reset = function reset() {\n    this.setState(initialState);\n  };\n\n  _proto.componentDidCatch = function componentDidCatch(error, info) {\n    var _this$props$onError, _this$props2;\n\n    (_this$props$onError = (_this$props2 = this.props).onError) == null ? void 0 : _this$props$onError.call(_this$props2, error, info);\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n    var error = this.state.error;\n    var resetKeys = this.props.resetKeys; // There's an edge case where if the thing that triggered the error\n    // happens to *also* be in the resetKeys array, we'd end up resetting\n    // the error boundary immediately. This would likely trigger a second\n    // error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call\n    // of cDU after the error is set\n\n    if (error !== null && prevState.error !== null && changedArray(prevProps.resetKeys, resetKeys)) {\n      var _this$props$onResetKe, _this$props3;\n\n      (_this$props$onResetKe = (_this$props3 = this.props).onResetKeysChange) == null ? void 0 : _this$props$onResetKe.call(_this$props3, prevProps.resetKeys, resetKeys);\n      this.reset();\n    }\n  };\n\n  _proto.render = function render() {\n    var error = this.state.error;\n    var _this$props4 = this.props,\n        fallbackRender = _this$props4.fallbackRender,\n        FallbackComponent = _this$props4.FallbackComponent,\n        fallback = _this$props4.fallback;\n\n    if (error !== null) {\n      var _props = {\n        error: error,\n        resetErrorBoundary: this.resetErrorBoundary\n      };\n\n      if ( /*#__PURE__*/React.isValidElement(fallback)) {\n        return fallback;\n      } else if (typeof fallbackRender === 'function') {\n        return fallbackRender(_props);\n      } else if (FallbackComponent) {\n        return /*#__PURE__*/React.createElement(FallbackComponent, _props);\n      } else {\n        throw new Error('react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop');\n      }\n    }\n\n    return this.props.children;\n  };\n\n  return ErrorBoundary;\n}(React.Component);\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction LexicalErrorBoundary({\n  children,\n  onError\n}) {\n  return /*#__PURE__*/jsx(ErrorBoundary, {\n    fallback: /*#__PURE__*/jsx(\"div\", {\n      style: {\n        border: '1px solid #f00',\n        color: '#f00',\n        padding: '8px'\n      },\n      children: \"An error was thrown.\"\n    }),\n    onError: onError,\n    children: children\n  });\n}\n\nexport { LexicalErrorBoundary };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;;;AAEA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC3B,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QACpG,EAAE,SAAS,GAAG;QACd,OAAO;IACT;IACA,OAAO,gBAAgB,GAAG;AAC5B;AAEA,SAAS,eAAe,QAAQ,EAAE,UAAU;IAC1C,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IACvD,SAAS,SAAS,CAAC,WAAW,GAAG;IACjC,gBAAgB,UAAU;AAC5B;AAEA,IAAI,eAAe,SAAS,aAAa,CAAC,EAAE,CAAC;IAC3C,IAAI,MAAM,KAAK,GAAG;QAChB,IAAI,EAAE;IACR;IAEA,IAAI,MAAM,KAAK,GAAG;QAChB,IAAI,EAAE;IACR;IAEA,OAAO,EAAE,MAAM,KAAK,EAAE,MAAM,IAAI,EAAE,IAAI,CAAC,SAAU,IAAI,EAAE,KAAK;QAC1D,OAAO,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM;IAClC;AACF;AAEA,IAAI,eAAe;IACjB,OAAO;AACT;AAEA,IAAI,gBAAgB,WAAW,GAAE,SAAU,gBAAgB;IACzD,eAAe,eAAe;IAE9B,SAAS;QACP,IAAI;QAEJ,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,QAAQ,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACxF,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAC/B;QAEA,QAAQ,iBAAiB,IAAI,CAAC,KAAK,CAAC,kBAAkB;YAAC,IAAI;SAAC,CAAC,MAAM,CAAC,WAAW,IAAI;QACnF,MAAM,KAAK,GAAG;QAEd,MAAM,kBAAkB,GAAG;YACzB,IAAI;YAEJ,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;gBAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;YAChC;YAEA,MAAM,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC,cAAc,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,aAAa;YAE9F,MAAM,KAAK;QACb;QAEA,OAAO;IACT;IAEA,cAAc,wBAAwB,GAAG,SAAS,yBAAyB,KAAK;QAC9E,OAAO;YACL,OAAO;QACT;IACF;IAEA,IAAI,SAAS,cAAc,SAAS;IAEpC,OAAO,KAAK,GAAG,SAAS;QACtB,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEA,OAAO,iBAAiB,GAAG,SAAS,kBAAkB,KAAK,EAAE,IAAI;QAC/D,IAAI,qBAAqB;QAEzB,CAAC,sBAAsB,CAAC,eAAe,IAAI,CAAC,KAAK,EAAE,OAAO,KAAK,OAAO,KAAK,IAAI,oBAAoB,IAAI,CAAC,cAAc,OAAO;IAC/H;IAEA,OAAO,kBAAkB,GAAG,SAAS,mBAAmB,SAAS,EAAE,SAAS;QAC1E,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK;QAC5B,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,mEAAmE;QACzG,qEAAqE;QACrE,qEAAqE;QACrE,sBAAsB;QACtB,sEAAsE;QACtE,gCAAgC;QAEhC,IAAI,UAAU,QAAQ,UAAU,KAAK,KAAK,QAAQ,aAAa,UAAU,SAAS,EAAE,YAAY;YAC9F,IAAI,uBAAuB;YAE3B,CAAC,wBAAwB,CAAC,eAAe,IAAI,CAAC,KAAK,EAAE,iBAAiB,KAAK,OAAO,KAAK,IAAI,sBAAsB,IAAI,CAAC,cAAc,UAAU,SAAS,EAAE;YACzJ,IAAI,CAAC,KAAK;QACZ;IACF;IAEA,OAAO,MAAM,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK;QAC5B,IAAI,eAAe,IAAI,CAAC,KAAK,EACzB,iBAAiB,aAAa,cAAc,EAC5C,oBAAoB,aAAa,iBAAiB,EAClD,WAAW,aAAa,QAAQ;QAEpC,IAAI,UAAU,MAAM;YAClB,IAAI,SAAS;gBACX,OAAO;gBACP,oBAAoB,IAAI,CAAC,kBAAkB;YAC7C;YAEA,IAAK,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,iBAAoB,AAAD,EAAE,WAAW;gBAChD,OAAO;YACT,OAAO,IAAI,OAAO,mBAAmB,YAAY;gBAC/C,OAAO,eAAe;YACxB,OAAO,IAAI,mBAAmB;gBAC5B,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,mBAAmB;YAC7D,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAEA,OAAO;AACT,EAAE,6JAAA,CAAA,YAAe;AAEjB;;;;;;CAMC,GAED,SAAS,qBAAqB,EAC5B,QAAQ,EACR,OAAO,EACR;IACC,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,eAAe;QACrC,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,OAAO;YAChC,OAAO;gBACL,QAAQ;gBACR,OAAO;gBACP,SAAS;YACX;YACA,UAAU;QACZ;QACA,SAAS;QACT,UAAU;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalTabIndentationPlugin.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { mergeRegister, $getNearestBlockElementAncestorOrThrow, $filter } from '@lexical/utils';\nimport { KEY_TAB_COMMAND, $getSelection, $isRangeSelection, OUTDENT_CONTENT_COMMAND, INDENT_CONTENT_COMMAND, INSERT_TAB_COMMAND, COMMAND_PRIORITY_EDITOR, COMMAND_PRIORITY_CRITICAL, $isBlockElementNode, $createRangeSelection, $normalizeSelection__EXPERIMENTAL } from 'lexical';\nimport { useEffect } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction $indentOverTab(selection) {\n  // const handled = new Set();\n  const nodes = selection.getNodes();\n  const canIndentBlockNodes = $filter(nodes, node => {\n    if ($isBlockElementNode(node) && node.canIndent()) {\n      return node;\n    }\n    return null;\n  });\n  // 1. If selection spans across canIndent block nodes: indent\n  if (canIndentBlockNodes.length > 0) {\n    return true;\n  }\n  // 2. If first (anchor/focus) is at block start: indent\n  const anchor = selection.anchor;\n  const focus = selection.focus;\n  const first = focus.isBefore(anchor) ? focus : anchor;\n  const firstNode = first.getNode();\n  const firstBlock = $getNearestBlockElementAncestorOrThrow(firstNode);\n  if (firstBlock.canIndent()) {\n    const firstBlockKey = firstBlock.getKey();\n    let selectionAtStart = $createRangeSelection();\n    selectionAtStart.anchor.set(firstBlockKey, 0, 'element');\n    selectionAtStart.focus.set(firstBlockKey, 0, 'element');\n    selectionAtStart = $normalizeSelection__EXPERIMENTAL(selectionAtStart);\n    if (selectionAtStart.anchor.is(first)) {\n      return true;\n    }\n  }\n  // 3. Else: tab\n  return false;\n}\nfunction registerTabIndentation(editor, maxIndent) {\n  return mergeRegister(editor.registerCommand(KEY_TAB_COMMAND, event => {\n    const selection = $getSelection();\n    if (!$isRangeSelection(selection)) {\n      return false;\n    }\n    event.preventDefault();\n    const command = $indentOverTab(selection) ? event.shiftKey ? OUTDENT_CONTENT_COMMAND : INDENT_CONTENT_COMMAND : INSERT_TAB_COMMAND;\n    return editor.dispatchCommand(command, undefined);\n  }, COMMAND_PRIORITY_EDITOR), editor.registerCommand(INDENT_CONTENT_COMMAND, () => {\n    if (maxIndent == null) {\n      return false;\n    }\n    const selection = $getSelection();\n    if (!$isRangeSelection(selection)) {\n      return false;\n    }\n    const indents = selection.getNodes().map(node => $getNearestBlockElementAncestorOrThrow(node).getIndent());\n    return Math.max(...indents) + 1 >= maxIndent;\n  }, COMMAND_PRIORITY_CRITICAL));\n}\n\n/**\n * This plugin adds the ability to indent content using the tab key. Generally, we don't\n * recommend using this plugin as it could negatively affect accessibility for keyboard\n * users, causing focus to become trapped within the editor.\n */\nfunction TabIndentationPlugin({\n  maxIndent\n}) {\n  const [editor] = useLexicalComposerContext();\n  useEffect(() => {\n    return registerTabIndentation(editor, maxIndent);\n  }, [editor, maxIndent]);\n  return null;\n}\n\nexport { TabIndentationPlugin, registerTabIndentation };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;AAED;AACA;AACA;AACA;;;;;AAEA;;;;;;CAMC,GAED,SAAS,eAAe,SAAS;IAC/B,6BAA6B;IAC7B,MAAM,QAAQ,UAAU,QAAQ;IAChC,MAAM,sBAAsB,CAAA,GAAA,6KAAA,CAAA,UAAO,AAAD,EAAE,OAAO,CAAA;QACzC,IAAI,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,KAAK,SAAS,IAAI;YACjD,OAAO;QACT;QACA,OAAO;IACT;IACA,6DAA6D;IAC7D,IAAI,oBAAoB,MAAM,GAAG,GAAG;QAClC,OAAO;IACT;IACA,uDAAuD;IACvD,MAAM,SAAS,UAAU,MAAM;IAC/B,MAAM,QAAQ,UAAU,KAAK;IAC7B,MAAM,QAAQ,MAAM,QAAQ,CAAC,UAAU,QAAQ;IAC/C,MAAM,YAAY,MAAM,OAAO;IAC/B,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,yCAAsC,AAAD,EAAE;IAC1D,IAAI,WAAW,SAAS,IAAI;QAC1B,MAAM,gBAAgB,WAAW,MAAM;QACvC,IAAI,mBAAmB,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD;QAC3C,iBAAiB,MAAM,CAAC,GAAG,CAAC,eAAe,GAAG;QAC9C,iBAAiB,KAAK,CAAC,GAAG,CAAC,eAAe,GAAG;QAC7C,mBAAmB,CAAA,GAAA,6IAAA,CAAA,oCAAiC,AAAD,EAAE;QACrD,IAAI,iBAAiB,MAAM,CAAC,EAAE,CAAC,QAAQ;YACrC,OAAO;QACT;IACF;IACA,eAAe;IACf,OAAO;AACT;AACA,SAAS,uBAAuB,MAAM,EAAE,SAAS;IAC/C,OAAO,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,eAAe,CAAC,6IAAA,CAAA,kBAAe,EAAE,CAAA;QAC3D,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD;QAC9B,IAAI,CAAC,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;YACjC,OAAO;QACT;QACA,MAAM,cAAc;QACpB,MAAM,UAAU,eAAe,aAAa,MAAM,QAAQ,GAAG,6IAAA,CAAA,0BAAuB,GAAG,6IAAA,CAAA,yBAAsB,GAAG,6IAAA,CAAA,qBAAkB;QAClI,OAAO,OAAO,eAAe,CAAC,SAAS;IACzC,GAAG,6IAAA,CAAA,0BAAuB,GAAG,OAAO,eAAe,CAAC,6IAAA,CAAA,yBAAsB,EAAE;QAC1E,IAAI,aAAa,MAAM;YACrB,OAAO;QACT;QACA,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD;QAC9B,IAAI,CAAC,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;YACjC,OAAO;QACT;QACA,MAAM,UAAU,UAAU,QAAQ,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAA,GAAA,6KAAA,CAAA,yCAAsC,AAAD,EAAE,MAAM,SAAS;QACvG,OAAO,KAAK,GAAG,IAAI,WAAW,KAAK;IACrC,GAAG,6IAAA,CAAA,4BAAyB;AAC9B;AAEA;;;;CAIC,GACD,SAAS,qBAAqB,EAC5B,SAAS,EACV;IACC,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,OAAO,uBAAuB,QAAQ;QACxC;yCAAG;QAAC;QAAQ;KAAU;IACtB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalCheckListPlugin.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { registerCheckList } from '@lexical/list';\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { useEffect } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction CheckListPlugin() {\n  const [editor] = useLexicalComposerContext();\n  useEffect(() => {\n    return registerCheckList(editor);\n  }, [editor]);\n  return null;\n}\n\nexport { CheckListPlugin };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;AACA;;;;AAEA;;;;;;CAMC,GAED,SAAS;IACP,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,OAAO,CAAA,GAAA,2JAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B;oCAAG;QAAC;KAAO;IACX,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalListPlugin.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { registerList, ListNode, ListItemNode, registerListStrictIndentTransform } from '@lexical/list';\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { useEffect } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction useList(editor) {\n  useEffect(() => {\n    return registerList(editor);\n  }, [editor]);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction ListPlugin({\n  hasStrictIndent = false\n}) {\n  const [editor] = useLexicalComposerContext();\n  useEffect(() => {\n    if (!editor.hasNodes([ListNode, ListItemNode])) {\n      throw new Error('ListPlugin: ListNode and/or ListItemNode not registered on editor');\n    }\n  }, [editor]);\n  useEffect(() => {\n    if (!hasStrictIndent) {\n      return;\n    }\n    return registerListStrictIndentTransform(editor);\n  }, [editor, hasStrictIndent]);\n  useList(editor);\n  return null;\n}\n\nexport { ListPlugin };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;AACA;;;;AAEA;;;;;;CAMC,GAED,SAAS,QAAQ,MAAM;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,OAAO,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE;QACtB;4BAAG;QAAC;KAAO;AACb;AAEA;;;;;;CAMC,GAED,SAAS,WAAW,EAClB,kBAAkB,KAAK,EACxB;IACC,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,OAAO,QAAQ,CAAC;gBAAC,2JAAA,CAAA,WAAQ;gBAAE,2JAAA,CAAA,eAAY;aAAC,GAAG;gBAC9C,MAAM,IAAI,MAAM;YAClB;QACF;+BAAG;QAAC;KAAO;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB;YACF;YACA,OAAO,CAAA,GAAA,2JAAA,CAAA,oCAAiC,AAAD,EAAE;QAC3C;+BAAG;QAAC;QAAQ;KAAgB;IAC5B,QAAQ;IACR,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/useLexicalNodeSelection.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { $getSelection, $isNodeSelection, $createNodeSelection, $setSelection, $getNodeByKey } from 'lexical';\nimport { useState, useEffect, useCallback } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n/**\n * A helper function to determine if a specific node is selected in a Lexical editor.\n *\n * @param {LexicalEditor} editor - The LexicalEditor instance.\n * @param {NodeKey} key - The key of the node to check.\n * @returns {boolean} Whether the node is selected.\n */\n\nfunction isNodeSelected(editor, key) {\n  return editor.getEditorState().read(() => {\n    const node = $getNodeByKey(key);\n    if (node === null) {\n      return false; // Node doesn't exist, so it's not selected.\n    }\n    return node.isSelected(); // Check if the node is selected.\n  });\n}\n\n/**\n * A custom hook to manage the selection state of a specific node in a Lexical editor.\n *\n * This hook provides utilities to:\n * - Check if a node is selected.\n * - Update its selection state.\n * - Clear the selection.\n *\n * @param {NodeKey} key - The key of the node to track selection for.\n * @returns {[boolean, (selected: boolean) => void, () => void]} A tuple containing:\n * - `isSelected` (boolean): Whether the node is currently selected.\n * - `setSelected` (function): A function to set the selection state of the node.\n * - `clearSelected` (function): A function to clear the selection of the node.\n *\n */\n\nfunction useLexicalNodeSelection(key) {\n  const [editor] = useLexicalComposerContext();\n\n  // State to track whether the node is currently selected.\n  const [isSelected, setIsSelected] = useState(() => isNodeSelected(editor, key));\n  useEffect(() => {\n    let isMounted = true;\n    const unregister = editor.registerUpdateListener(() => {\n      if (isMounted) {\n        setIsSelected(isNodeSelected(editor, key));\n      }\n    });\n    return () => {\n      isMounted = false; // Prevent updates after component unmount.\n      unregister();\n    };\n  }, [editor, key]);\n  const setSelected = useCallback(selected => {\n    editor.update(() => {\n      let selection = $getSelection();\n      if (!$isNodeSelection(selection)) {\n        selection = $createNodeSelection();\n        $setSelection(selection);\n      }\n      if ($isNodeSelection(selection)) {\n        if (selected) {\n          selection.add(key);\n        } else {\n          selection.delete(key);\n        }\n      }\n    });\n  }, [editor, key]);\n  const clearSelected = useCallback(() => {\n    editor.update(() => {\n      const selection = $getSelection();\n      if ($isNodeSelection(selection)) {\n        selection.clear();\n      }\n    });\n  }, [editor]);\n  return [isSelected, setSelected, clearSelected];\n}\n\nexport { useLexicalNodeSelection };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;AACA;;;;AAEA;;;;;;CAMC,GAGD;;;;;;CAMC,GAED,SAAS,eAAe,MAAM,EAAE,GAAG;IACjC,OAAO,OAAO,cAAc,GAAG,IAAI,CAAC;QAClC,MAAM,OAAO,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD,EAAE;QAC3B,IAAI,SAAS,MAAM;YACjB,OAAO,OAAO,4CAA4C;QAC5D;QACA,OAAO,KAAK,UAAU,IAAI,iCAAiC;IAC7D;AACF;AAEA;;;;;;;;;;;;;;CAcC,GAED,SAAS,wBAAwB,GAAG;IAClC,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IAEzC,yDAAyD;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;4CAAE,IAAM,eAAe,QAAQ;;IAC1E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,IAAI,YAAY;YAChB,MAAM,aAAa,OAAO,sBAAsB;gEAAC;oBAC/C,IAAI,WAAW;wBACb,cAAc,eAAe,QAAQ;oBACvC;gBACF;;YACA;qDAAO;oBACL,YAAY,OAAO,2CAA2C;oBAC9D;gBACF;;QACF;4CAAG;QAAC;QAAQ;KAAI;IAChB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAA;YAC9B,OAAO,MAAM;oEAAC;oBACZ,IAAI,YAAY,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD;oBAC5B,IAAI,CAAC,CAAA,GAAA,6IAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY;wBAChC,YAAY,CAAA,GAAA,6IAAA,CAAA,uBAAoB,AAAD;wBAC/B,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD,EAAE;oBAChB;oBACA,IAAI,CAAA,GAAA,6IAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY;wBAC/B,IAAI,UAAU;4BACZ,UAAU,GAAG,CAAC;wBAChB,OAAO;4BACL,UAAU,MAAM,CAAC;wBACnB;oBACF;gBACF;;QACF;2DAAG;QAAC;QAAQ;KAAI;IAChB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YAChC,OAAO,MAAM;sEAAC;oBACZ,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD;oBAC9B,IAAI,CAAA,GAAA,6IAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY;wBAC/B,UAAU,KAAK;oBACjB;gBACF;;QACF;6DAAG;QAAC;KAAO;IACX,OAAO;QAAC;QAAY;QAAa;KAAc;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalHorizontalRuleNode.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { useLexicalNodeSelection } from '@lexical/react/useLexicalNodeSelection';\nimport { addClassNamesToElement, mergeRegister, removeClassNamesFromElement } from '@lexical/utils';\nimport { createCommand, DecoratorNode, $applyNodeReplacement, CLICK_COMMAND, COMMAND_PRIORITY_LOW } from 'lexical';\nimport { useEffect } from 'react';\nimport { jsx } from 'react/jsx-runtime';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst INSERT_HORIZONTAL_RULE_COMMAND = createCommand('INSERT_HORIZONTAL_RULE_COMMAND');\nfunction HorizontalRuleComponent({\n  nodeKey\n}) {\n  const [editor] = useLexicalComposerContext();\n  const [isSelected, setSelected, clearSelection] = useLexicalNodeSelection(nodeKey);\n  useEffect(() => {\n    return mergeRegister(editor.registerCommand(CLICK_COMMAND, event => {\n      const hrElem = editor.getElementByKey(nodeKey);\n      if (event.target === hrElem) {\n        if (!event.shiftKey) {\n          clearSelection();\n        }\n        setSelected(!isSelected);\n        return true;\n      }\n      return false;\n    }, COMMAND_PRIORITY_LOW));\n  }, [clearSelection, editor, isSelected, nodeKey, setSelected]);\n  useEffect(() => {\n    const hrElem = editor.getElementByKey(nodeKey);\n    const isSelectedClassName = editor._config.theme.hrSelected ?? 'selected';\n    if (hrElem !== null) {\n      if (isSelected) {\n        addClassNamesToElement(hrElem, isSelectedClassName);\n      } else {\n        removeClassNamesFromElement(hrElem, isSelectedClassName);\n      }\n    }\n  }, [editor, isSelected, nodeKey]);\n  return null;\n}\nclass HorizontalRuleNode extends DecoratorNode {\n  static getType() {\n    return 'horizontalrule';\n  }\n  static clone(node) {\n    return new HorizontalRuleNode(node.__key);\n  }\n  static importJSON(serializedNode) {\n    return $createHorizontalRuleNode().updateFromJSON(serializedNode);\n  }\n  static importDOM() {\n    return {\n      hr: () => ({\n        conversion: $convertHorizontalRuleElement,\n        priority: 0\n      })\n    };\n  }\n  exportDOM() {\n    return {\n      element: document.createElement('hr')\n    };\n  }\n  createDOM(config) {\n    const element = document.createElement('hr');\n    addClassNamesToElement(element, config.theme.hr);\n    return element;\n  }\n  getTextContent() {\n    return '\\n';\n  }\n  isInline() {\n    return false;\n  }\n  updateDOM() {\n    return false;\n  }\n  decorate() {\n    return /*#__PURE__*/jsx(HorizontalRuleComponent, {\n      nodeKey: this.__key\n    });\n  }\n}\nfunction $convertHorizontalRuleElement() {\n  return {\n    node: $createHorizontalRuleNode()\n  };\n}\nfunction $createHorizontalRuleNode() {\n  return $applyNodeReplacement(new HorizontalRuleNode());\n}\nfunction $isHorizontalRuleNode(node) {\n  return node instanceof HorizontalRuleNode;\n}\n\nexport { $createHorizontalRuleNode, $isHorizontalRuleNode, HorizontalRuleNode, INSERT_HORIZONTAL_RULE_COMMAND };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;;AAED;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA;;;;;;CAMC,GAED,MAAM,iCAAiC,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD,EAAE;AACrD,SAAS,wBAAwB,EAC/B,OAAO,EACR;IACC,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,MAAM,CAAC,YAAY,aAAa,eAAe,GAAG,CAAA,GAAA,wKAAA,CAAA,0BAAuB,AAAD,EAAE;IAC1E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,OAAO,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,eAAe,CAAC,6IAAA,CAAA,gBAAa;qDAAE,CAAA;oBACzD,MAAM,SAAS,OAAO,eAAe,CAAC;oBACtC,IAAI,MAAM,MAAM,KAAK,QAAQ;wBAC3B,IAAI,CAAC,MAAM,QAAQ,EAAE;4BACnB;wBACF;wBACA,YAAY,CAAC;wBACb,OAAO;oBACT;oBACA,OAAO;gBACT;oDAAG,6IAAA,CAAA,uBAAoB;QACzB;4CAAG;QAAC;QAAgB;QAAQ;QAAY;QAAS;KAAY;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,MAAM,SAAS,OAAO,eAAe,CAAC;YACtC,MAAM,sBAAsB,OAAO,OAAO,CAAC,KAAK,CAAC,UAAU,IAAI;YAC/D,IAAI,WAAW,MAAM;gBACnB,IAAI,YAAY;oBACd,CAAA,GAAA,6KAAA,CAAA,yBAAsB,AAAD,EAAE,QAAQ;gBACjC,OAAO;oBACL,CAAA,GAAA,6KAAA,CAAA,8BAA2B,AAAD,EAAE,QAAQ;gBACtC;YACF;QACF;4CAAG;QAAC;QAAQ;QAAY;KAAQ;IAChC,OAAO;AACT;AACA,MAAM,2BAA2B,6IAAA,CAAA,gBAAa;IAC5C,OAAO,UAAU;QACf,OAAO;IACT;IACA,OAAO,MAAM,IAAI,EAAE;QACjB,OAAO,IAAI,mBAAmB,KAAK,KAAK;IAC1C;IACA,OAAO,WAAW,cAAc,EAAE;QAChC,OAAO,4BAA4B,cAAc,CAAC;IACpD;IACA,OAAO,YAAY;QACjB,OAAO;YACL,IAAI,IAAM,CAAC;oBACT,YAAY;oBACZ,UAAU;gBACZ,CAAC;QACH;IACF;IACA,YAAY;QACV,OAAO;YACL,SAAS,SAAS,aAAa,CAAC;QAClC;IACF;IACA,UAAU,MAAM,EAAE;QAChB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,CAAA,GAAA,6KAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS,OAAO,KAAK,CAAC,EAAE;QAC/C,OAAO;IACT;IACA,iBAAiB;QACf,OAAO;IACT;IACA,WAAW;QACT,OAAO;IACT;IACA,YAAY;QACV,OAAO;IACT;IACA,WAAW;QACT,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yBAAyB;YAC/C,SAAS,IAAI,CAAC,KAAK;QACrB;IACF;AACF;AACA,SAAS;IACP,OAAO;QACL,MAAM;IACR;AACF;AACA,SAAS;IACP,OAAO,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI;AACnC;AACA,SAAS,sBAAsB,IAAI;IACjC,OAAO,gBAAgB;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalHorizontalRulePlugin.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { INSERT_HORIZONTAL_RULE_COMMAND, $createHorizontalRuleNode } from '@lexical/react/LexicalHorizontalRuleNode';\nimport { $insertNodeToNearestRoot } from '@lexical/utils';\nimport { $getSelection, $isRangeSelection, COMMAND_PRIORITY_EDITOR } from 'lexical';\nimport { useEffect } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction HorizontalRulePlugin() {\n  const [editor] = useLexicalComposerContext();\n  useEffect(() => {\n    return editor.registerCommand(INSERT_HORIZONTAL_RULE_COMMAND, type => {\n      const selection = $getSelection();\n      if (!$isRangeSelection(selection)) {\n        return false;\n      }\n      const focusNode = selection.focus.getNode();\n      if (focusNode !== null) {\n        const horizontalRuleNode = $createHorizontalRuleNode();\n        $insertNodeToNearestRoot(horizontalRuleNode);\n      }\n      return true;\n    }, COMMAND_PRIORITY_EDITOR);\n  }, [editor]);\n  return null;\n}\n\nexport { HorizontalRulePlugin };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;AACA;AACA;AACA;;;;;;AAEA;;;;;;CAMC,GAED,SAAS;IACP,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,OAAO,OAAO,eAAe,CAAC,0KAAA,CAAA,iCAA8B;kDAAE,CAAA;oBAC5D,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD;oBAC9B,IAAI,CAAC,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;wBACjC,OAAO;oBACT;oBACA,MAAM,YAAY,UAAU,KAAK,CAAC,OAAO;oBACzC,IAAI,cAAc,MAAM;wBACtB,MAAM,qBAAqB,CAAA,GAAA,0KAAA,CAAA,4BAAyB,AAAD;wBACnD,CAAA,GAAA,6KAAA,CAAA,2BAAwB,AAAD,EAAE;oBAC3B;oBACA,OAAO;gBACT;iDAAG,6IAAA,CAAA,0BAAuB;QAC5B;yCAAG;QAAC;KAAO;IACX,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { TRANSFORMERS, registerMarkdownShortcuts } from '@lexical/markdown';\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { HorizontalRuleNode, $isHorizontalRuleNode, $createHorizontalRuleNode } from '@lexical/react/LexicalHorizontalRuleNode';\nimport { useEffect } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst HR = {\n  dependencies: [HorizontalRuleNode],\n  export: node => {\n    return $isHorizontalRuleNode(node) ? '***' : null;\n  },\n  regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n  replace: (parentNode, _1, _2, isImport) => {\n    const line = $createHorizontalRuleNode();\n\n    // TODO: Get rid of isImport flag\n    if (isImport || parentNode.getNextSibling() != null) {\n      parentNode.replace(line);\n    } else {\n      parentNode.insertBefore(line);\n    }\n    line.selectNext();\n  },\n  type: 'element'\n};\nconst DEFAULT_TRANSFORMERS = [HR, ...TRANSFORMERS];\nfunction MarkdownShortcutPlugin({\n  transformers = DEFAULT_TRANSFORMERS\n}) {\n  const [editor] = useLexicalComposerContext();\n  useEffect(() => {\n    return registerMarkdownShortcuts(editor, transformers);\n  }, [editor, transformers]);\n  return null;\n}\n\nexport { DEFAULT_TRANSFORMERS, MarkdownShortcutPlugin };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;AAED;AACA;AACA;AACA;;;;;AAEA;;;;;;CAMC,GAED,MAAM,KAAK;IACT,cAAc;QAAC,0KAAA,CAAA,qBAAkB;KAAC;IAClC,QAAQ,CAAA;QACN,OAAO,CAAA,GAAA,0KAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ,QAAQ;IAC/C;IACA,QAAQ;IACR,SAAS,CAAC,YAAY,IAAI,IAAI;QAC5B,MAAM,OAAO,CAAA,GAAA,0KAAA,CAAA,4BAAyB,AAAD;QAErC,iCAAiC;QACjC,IAAI,YAAY,WAAW,cAAc,MAAM,MAAM;YACnD,WAAW,OAAO,CAAC;QACrB,OAAO;YACL,WAAW,YAAY,CAAC;QAC1B;QACA,KAAK,UAAU;IACjB;IACA,MAAM;AACR;AACA,MAAM,uBAAuB;IAAC;OAAO,mKAAA,CAAA,eAAY;CAAC;AAClD,SAAS,uBAAuB,EAC9B,eAAe,oBAAoB,EACpC;IACC,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,OAAO,CAAA,GAAA,mKAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ;QAC3C;2CAAG;QAAC;QAAQ;KAAa;IACzB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalLinkPlugin.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { LinkNode, TOGGLE_LINK_COMMAND, $toggleLink } from '@lexical/link';\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { mergeRegister, objectKlassEquals } from '@lexical/utils';\nimport { COMMAND_PRIORITY_LOW, PASTE_COMMAND, $getSelection, $isRangeSelection, $isElementNode } from 'lexical';\nimport { useEffect } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction LinkPlugin({\n  validateUrl,\n  attributes\n}) {\n  const [editor] = useLexicalComposerContext();\n  useEffect(() => {\n    if (!editor.hasNodes([LinkNode])) {\n      throw new Error('LinkPlugin: LinkNode not registered on editor');\n    }\n    return mergeRegister(editor.registerCommand(TOGGLE_LINK_COMMAND, payload => {\n      if (payload === null) {\n        $toggleLink(payload);\n        return true;\n      } else if (typeof payload === 'string') {\n        if (validateUrl === undefined || validateUrl(payload)) {\n          $toggleLink(payload, attributes);\n          return true;\n        }\n        return false;\n      } else {\n        const {\n          url,\n          target,\n          rel,\n          title\n        } = payload;\n        $toggleLink(url, {\n          ...attributes,\n          rel,\n          target,\n          title\n        });\n        return true;\n      }\n    }, COMMAND_PRIORITY_LOW), validateUrl !== undefined ? editor.registerCommand(PASTE_COMMAND, event => {\n      const selection = $getSelection();\n      if (!$isRangeSelection(selection) || selection.isCollapsed() || !objectKlassEquals(event, ClipboardEvent)) {\n        return false;\n      }\n      if (event.clipboardData === null) {\n        return false;\n      }\n      const clipboardText = event.clipboardData.getData('text');\n      if (!validateUrl(clipboardText)) {\n        return false;\n      }\n      // If we select nodes that are elements then avoid applying the link.\n      if (!selection.getNodes().some(node => $isElementNode(node))) {\n        editor.dispatchCommand(TOGGLE_LINK_COMMAND, {\n          ...attributes,\n          url: clipboardText\n        });\n        event.preventDefault();\n        return true;\n      }\n      return false;\n    }, COMMAND_PRIORITY_LOW) : () => {\n      // Don't paste arbitrary text as a link when there's no validate function\n    });\n  }, [editor, validateUrl, attributes]);\n  return null;\n}\n\nexport { LinkPlugin };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;AACA;AACA;AACA;;;;;;AAEA;;;;;;CAMC,GAED,SAAS,WAAW,EAClB,WAAW,EACX,UAAU,EACX;IACC,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,OAAO,QAAQ,CAAC;gBAAC,2JAAA,CAAA,WAAQ;aAAC,GAAG;gBAChC,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,eAAe,CAAC,2JAAA,CAAA,sBAAmB;wCAAE,CAAA;oBAC/D,IAAI,YAAY,MAAM;wBACpB,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE;wBACZ,OAAO;oBACT,OAAO,IAAI,OAAO,YAAY,UAAU;wBACtC,IAAI,gBAAgB,aAAa,YAAY,UAAU;4BACrD,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,SAAS;4BACrB,OAAO;wBACT;wBACA,OAAO;oBACT,OAAO;wBACL,MAAM,EACJ,GAAG,EACH,MAAM,EACN,GAAG,EACH,KAAK,EACN,GAAG;wBACJ,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,KAAK;4BACf,GAAG,UAAU;4BACb;4BACA;4BACA;wBACF;wBACA,OAAO;oBACT;gBACF;uCAAG,6IAAA,CAAA,uBAAoB,GAAG,gBAAgB,YAAY,OAAO,eAAe,CAAC,6IAAA,CAAA,gBAAa;wCAAE,CAAA;oBAC1F,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD;oBAC9B,IAAI,CAAC,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc,UAAU,WAAW,MAAM,CAAC,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,iBAAiB;wBACzG,OAAO;oBACT;oBACA,IAAI,MAAM,aAAa,KAAK,MAAM;wBAChC,OAAO;oBACT;oBACA,MAAM,gBAAgB,MAAM,aAAa,CAAC,OAAO,CAAC;oBAClD,IAAI,CAAC,YAAY,gBAAgB;wBAC/B,OAAO;oBACT;oBACA,qEAAqE;oBACrE,IAAI,CAAC,UAAU,QAAQ,GAAG,IAAI;gDAAC,CAAA,OAAQ,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;gDAAQ;wBAC5D,OAAO,eAAe,CAAC,2JAAA,CAAA,sBAAmB,EAAE;4BAC1C,GAAG,UAAU;4BACb,KAAK;wBACP;wBACA,MAAM,cAAc;wBACpB,OAAO;oBACT;oBACA,OAAO;gBACT;uCAAG,6IAAA,CAAA,uBAAoB;wCAAI;gBACzB,yEAAyE;gBAC3E;;QACF;+BAAG;QAAC;QAAQ;QAAa;KAAW;IACpC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1491, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalAutoLinkPlugin.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { AutoLinkNode, $isAutoLinkNode, $isLinkNode, TOGGLE_LINK_COMMAND, $createAutoLinkNode } from '@lexical/link';\nimport { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { mergeRegister } from '@lexical/utils';\nimport { TextNode, $getSelection, $isRangeSelection, COMMAND_PRIORITY_LOW, $isTextNode, $isElementNode, $isLineBreakNode, $createTextNode, $isNodeSelection } from 'lexical';\nimport { useEffect } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\nfunction createLinkMatcherWithRegExp(regExp, urlTransformer = text => text) {\n  return text => {\n    const match = regExp.exec(text);\n    if (match === null) {\n      return null;\n    }\n    return {\n      index: match.index,\n      length: match[0].length,\n      text: match[0],\n      url: urlTransformer(match[0])\n    };\n  };\n}\nfunction findFirstMatch(text, matchers) {\n  for (let i = 0; i < matchers.length; i++) {\n    const match = matchers[i](text);\n    if (match) {\n      return match;\n    }\n  }\n  return null;\n}\nconst PUNCTUATION_OR_SPACE = /[.,;\\s]/;\nfunction isSeparator(char) {\n  return PUNCTUATION_OR_SPACE.test(char);\n}\nfunction endsWithSeparator(textContent) {\n  return isSeparator(textContent[textContent.length - 1]);\n}\nfunction startsWithSeparator(textContent) {\n  return isSeparator(textContent[0]);\n}\n\n/**\n * Check if the text content starts with a fullstop followed by a top-level domain.\n * Meaning if the text content can be a beginning of a top level domain.\n * @param textContent\n * @param isEmail\n * @returns boolean\n */\nfunction startsWithTLD(textContent, isEmail) {\n  if (isEmail) {\n    return /^\\.[a-zA-Z]{2,}/.test(textContent);\n  } else {\n    return /^\\.[a-zA-Z0-9]{1,}/.test(textContent);\n  }\n}\nfunction isPreviousNodeValid(node) {\n  let previousNode = node.getPreviousSibling();\n  if ($isElementNode(previousNode)) {\n    previousNode = previousNode.getLastDescendant();\n  }\n  return previousNode === null || $isLineBreakNode(previousNode) || $isTextNode(previousNode) && endsWithSeparator(previousNode.getTextContent());\n}\nfunction isNextNodeValid(node) {\n  let nextNode = node.getNextSibling();\n  if ($isElementNode(nextNode)) {\n    nextNode = nextNode.getFirstDescendant();\n  }\n  return nextNode === null || $isLineBreakNode(nextNode) || $isTextNode(nextNode) && startsWithSeparator(nextNode.getTextContent());\n}\nfunction isContentAroundIsValid(matchStart, matchEnd, text, nodes) {\n  const contentBeforeIsValid = matchStart > 0 ? isSeparator(text[matchStart - 1]) : isPreviousNodeValid(nodes[0]);\n  if (!contentBeforeIsValid) {\n    return false;\n  }\n  const contentAfterIsValid = matchEnd < text.length ? isSeparator(text[matchEnd]) : isNextNodeValid(nodes[nodes.length - 1]);\n  return contentAfterIsValid;\n}\nfunction extractMatchingNodes(nodes, startIndex, endIndex) {\n  const unmodifiedBeforeNodes = [];\n  const matchingNodes = [];\n  const unmodifiedAfterNodes = [];\n  let matchingOffset = 0;\n  let currentOffset = 0;\n  const currentNodes = [...nodes];\n  while (currentNodes.length > 0) {\n    const currentNode = currentNodes[0];\n    const currentNodeText = currentNode.getTextContent();\n    const currentNodeLength = currentNodeText.length;\n    const currentNodeStart = currentOffset;\n    const currentNodeEnd = currentOffset + currentNodeLength;\n    if (currentNodeEnd <= startIndex) {\n      unmodifiedBeforeNodes.push(currentNode);\n      matchingOffset += currentNodeLength;\n    } else if (currentNodeStart >= endIndex) {\n      unmodifiedAfterNodes.push(currentNode);\n    } else {\n      matchingNodes.push(currentNode);\n    }\n    currentOffset += currentNodeLength;\n    currentNodes.shift();\n  }\n  return [matchingOffset, unmodifiedBeforeNodes, matchingNodes, unmodifiedAfterNodes];\n}\nfunction $createAutoLinkNode_(nodes, startIndex, endIndex, match) {\n  const linkNode = $createAutoLinkNode(match.url, match.attributes);\n  if (nodes.length === 1) {\n    let remainingTextNode = nodes[0];\n    let linkTextNode;\n    if (startIndex === 0) {\n      [linkTextNode, remainingTextNode] = remainingTextNode.splitText(endIndex);\n    } else {\n      [, linkTextNode, remainingTextNode] = remainingTextNode.splitText(startIndex, endIndex);\n    }\n    const textNode = $createTextNode(match.text);\n    textNode.setFormat(linkTextNode.getFormat());\n    textNode.setDetail(linkTextNode.getDetail());\n    textNode.setStyle(linkTextNode.getStyle());\n    linkNode.append(textNode);\n    linkTextNode.replace(linkNode);\n    return remainingTextNode;\n  } else if (nodes.length > 1) {\n    const firstTextNode = nodes[0];\n    let offset = firstTextNode.getTextContent().length;\n    let firstLinkTextNode;\n    if (startIndex === 0) {\n      firstLinkTextNode = firstTextNode;\n    } else {\n      [, firstLinkTextNode] = firstTextNode.splitText(startIndex);\n    }\n    const linkNodes = [];\n    let remainingTextNode;\n    for (let i = 1; i < nodes.length; i++) {\n      const currentNode = nodes[i];\n      const currentNodeText = currentNode.getTextContent();\n      const currentNodeLength = currentNodeText.length;\n      const currentNodeStart = offset;\n      const currentNodeEnd = offset + currentNodeLength;\n      if (currentNodeStart < endIndex) {\n        if (currentNodeEnd <= endIndex) {\n          linkNodes.push(currentNode);\n        } else {\n          const [linkTextNode, endNode] = currentNode.splitText(endIndex - currentNodeStart);\n          linkNodes.push(linkTextNode);\n          remainingTextNode = endNode;\n        }\n      }\n      offset += currentNodeLength;\n    }\n    const selection = $getSelection();\n    const selectedTextNode = selection ? selection.getNodes().find($isTextNode) : undefined;\n    const textNode = $createTextNode(firstLinkTextNode.getTextContent());\n    textNode.setFormat(firstLinkTextNode.getFormat());\n    textNode.setDetail(firstLinkTextNode.getDetail());\n    textNode.setStyle(firstLinkTextNode.getStyle());\n    linkNode.append(textNode, ...linkNodes);\n    // it does not preserve caret position if caret was at the first text node\n    // so we need to restore caret position\n    if (selectedTextNode && selectedTextNode === firstLinkTextNode) {\n      if ($isRangeSelection(selection)) {\n        textNode.select(selection.anchor.offset, selection.focus.offset);\n      } else if ($isNodeSelection(selection)) {\n        textNode.select(0, textNode.getTextContent().length);\n      }\n    }\n    firstLinkTextNode.replace(linkNode);\n    return remainingTextNode;\n  }\n  return undefined;\n}\nfunction $handleLinkCreation(nodes, matchers, onChange) {\n  let currentNodes = [...nodes];\n  const initialText = currentNodes.map(node => node.getTextContent()).join('');\n  let text = initialText;\n  let match;\n  let invalidMatchEnd = 0;\n  while ((match = findFirstMatch(text, matchers)) && match !== null) {\n    const matchStart = match.index;\n    const matchLength = match.length;\n    const matchEnd = matchStart + matchLength;\n    const isValid = isContentAroundIsValid(invalidMatchEnd + matchStart, invalidMatchEnd + matchEnd, initialText, currentNodes);\n    if (isValid) {\n      const [matchingOffset,, matchingNodes, unmodifiedAfterNodes] = extractMatchingNodes(currentNodes, invalidMatchEnd + matchStart, invalidMatchEnd + matchEnd);\n      const actualMatchStart = invalidMatchEnd + matchStart - matchingOffset;\n      const actualMatchEnd = invalidMatchEnd + matchEnd - matchingOffset;\n      const remainingTextNode = $createAutoLinkNode_(matchingNodes, actualMatchStart, actualMatchEnd, match);\n      currentNodes = remainingTextNode ? [remainingTextNode, ...unmodifiedAfterNodes] : unmodifiedAfterNodes;\n      onChange(match.url, null);\n      invalidMatchEnd = 0;\n    } else {\n      invalidMatchEnd += matchEnd;\n    }\n    text = text.substring(matchEnd);\n  }\n}\nfunction handleLinkEdit(linkNode, matchers, onChange) {\n  // Check children are simple text\n  const children = linkNode.getChildren();\n  const childrenLength = children.length;\n  for (let i = 0; i < childrenLength; i++) {\n    const child = children[i];\n    if (!$isTextNode(child) || !child.isSimpleText()) {\n      replaceWithChildren(linkNode);\n      onChange(null, linkNode.getURL());\n      return;\n    }\n  }\n\n  // Check text content fully matches\n  const text = linkNode.getTextContent();\n  const match = findFirstMatch(text, matchers);\n  if (match === null || match.text !== text) {\n    replaceWithChildren(linkNode);\n    onChange(null, linkNode.getURL());\n    return;\n  }\n\n  // Check neighbors\n  if (!isPreviousNodeValid(linkNode) || !isNextNodeValid(linkNode)) {\n    replaceWithChildren(linkNode);\n    onChange(null, linkNode.getURL());\n    return;\n  }\n  const url = linkNode.getURL();\n  if (url !== match.url) {\n    linkNode.setURL(match.url);\n    onChange(match.url, url);\n  }\n  if (match.attributes) {\n    const rel = linkNode.getRel();\n    if (rel !== match.attributes.rel) {\n      linkNode.setRel(match.attributes.rel || null);\n      onChange(match.attributes.rel || null, rel);\n    }\n    const target = linkNode.getTarget();\n    if (target !== match.attributes.target) {\n      linkNode.setTarget(match.attributes.target || null);\n      onChange(match.attributes.target || null, target);\n    }\n  }\n}\n\n// Bad neighbors are edits in neighbor nodes that make AutoLinks incompatible.\n// Given the creation preconditions, these can only be simple text nodes.\nfunction handleBadNeighbors(textNode, matchers, onChange) {\n  const previousSibling = textNode.getPreviousSibling();\n  const nextSibling = textNode.getNextSibling();\n  const text = textNode.getTextContent();\n  if ($isAutoLinkNode(previousSibling) && !previousSibling.getIsUnlinked() && (!startsWithSeparator(text) || startsWithTLD(text, previousSibling.isEmailURI()))) {\n    previousSibling.append(textNode);\n    handleLinkEdit(previousSibling, matchers, onChange);\n    onChange(null, previousSibling.getURL());\n  }\n  if ($isAutoLinkNode(nextSibling) && !nextSibling.getIsUnlinked() && !endsWithSeparator(text)) {\n    replaceWithChildren(nextSibling);\n    handleLinkEdit(nextSibling, matchers, onChange);\n    onChange(null, nextSibling.getURL());\n  }\n}\nfunction replaceWithChildren(node) {\n  const children = node.getChildren();\n  const childrenLength = children.length;\n  for (let j = childrenLength - 1; j >= 0; j--) {\n    node.insertAfter(children[j]);\n  }\n  node.remove();\n  return children.map(child => child.getLatest());\n}\nfunction getTextNodesToMatch(textNode) {\n  // check if next siblings are simple text nodes till a node contains a space separator\n  const textNodesToMatch = [textNode];\n  let nextSibling = textNode.getNextSibling();\n  while (nextSibling !== null && $isTextNode(nextSibling) && nextSibling.isSimpleText()) {\n    textNodesToMatch.push(nextSibling);\n    if (/[\\s]/.test(nextSibling.getTextContent())) {\n      break;\n    }\n    nextSibling = nextSibling.getNextSibling();\n  }\n  return textNodesToMatch;\n}\nfunction useAutoLink(editor, matchers, onChange) {\n  useEffect(() => {\n    if (!editor.hasNodes([AutoLinkNode])) {\n      {\n        formatDevErrorMessage(`LexicalAutoLinkPlugin: AutoLinkNode not registered on editor`);\n      }\n    }\n    const onChangeWrapped = (url, prevUrl) => {\n      if (onChange) {\n        onChange(url, prevUrl);\n      }\n    };\n    return mergeRegister(editor.registerNodeTransform(TextNode, textNode => {\n      const parent = textNode.getParentOrThrow();\n      const previous = textNode.getPreviousSibling();\n      if ($isAutoLinkNode(parent) && !parent.getIsUnlinked()) {\n        handleLinkEdit(parent, matchers, onChangeWrapped);\n      } else if (!$isLinkNode(parent)) {\n        if (textNode.isSimpleText() && (startsWithSeparator(textNode.getTextContent()) || !$isAutoLinkNode(previous))) {\n          const textNodesToMatch = getTextNodesToMatch(textNode);\n          $handleLinkCreation(textNodesToMatch, matchers, onChangeWrapped);\n        }\n        handleBadNeighbors(textNode, matchers, onChangeWrapped);\n      }\n    }), editor.registerCommand(TOGGLE_LINK_COMMAND, payload => {\n      const selection = $getSelection();\n      if (payload !== null || !$isRangeSelection(selection)) {\n        return false;\n      }\n      const nodes = selection.extract();\n      nodes.forEach(node => {\n        const parent = node.getParent();\n        if ($isAutoLinkNode(parent)) {\n          // invert the value\n          parent.setIsUnlinked(!parent.getIsUnlinked());\n          parent.markDirty();\n        }\n      });\n      return false;\n    }, COMMAND_PRIORITY_LOW));\n  }, [editor, matchers, onChange]);\n}\nfunction AutoLinkPlugin({\n  matchers,\n  onChange\n}) {\n  const [editor] = useLexicalComposerContext();\n  useAutoLink(editor, matchers, onChange);\n  return null;\n}\n\nexport { AutoLinkPlugin, createLinkMatcherWithRegExp };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;AAED;AACA;AACA;AACA;AACA;;;;;;AAEA;;;;;;CAMC,GAED,qEAAqE;AAErE,SAAS,sBAAsB,OAAO;IACpC,MAAM,IAAI,MAAM;AAClB;AAEA,SAAS,4BAA4B,MAAM,EAAE,iBAAiB,CAAA,OAAQ,IAAI;IACxE,OAAO,CAAA;QACL,MAAM,QAAQ,OAAO,IAAI,CAAC;QAC1B,IAAI,UAAU,MAAM;YAClB,OAAO;QACT;QACA,OAAO;YACL,OAAO,MAAM,KAAK;YAClB,QAAQ,KAAK,CAAC,EAAE,CAAC,MAAM;YACvB,MAAM,KAAK,CAAC,EAAE;YACd,KAAK,eAAe,KAAK,CAAC,EAAE;QAC9B;IACF;AACF;AACA,SAAS,eAAe,IAAI,EAAE,QAAQ;IACpC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,MAAM,QAAQ,QAAQ,CAAC,EAAE,CAAC;QAC1B,IAAI,OAAO;YACT,OAAO;QACT;IACF;IACA,OAAO;AACT;AACA,MAAM,uBAAuB;AAC7B,SAAS,YAAY,IAAI;IACvB,OAAO,qBAAqB,IAAI,CAAC;AACnC;AACA,SAAS,kBAAkB,WAAW;IACpC,OAAO,YAAY,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;AACxD;AACA,SAAS,oBAAoB,WAAW;IACtC,OAAO,YAAY,WAAW,CAAC,EAAE;AACnC;AAEA;;;;;;CAMC,GACD,SAAS,cAAc,WAAW,EAAE,OAAO;IACzC,IAAI,SAAS;QACX,OAAO,kBAAkB,IAAI,CAAC;IAChC,OAAO;QACL,OAAO,qBAAqB,IAAI,CAAC;IACnC;AACF;AACA,SAAS,oBAAoB,IAAI;IAC/B,IAAI,eAAe,KAAK,kBAAkB;IAC1C,IAAI,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,eAAe;QAChC,eAAe,aAAa,iBAAiB;IAC/C;IACA,OAAO,iBAAiB,QAAQ,CAAA,GAAA,6IAAA,CAAA,mBAAgB,AAAD,EAAE,iBAAiB,CAAA,GAAA,6IAAA,CAAA,cAAW,AAAD,EAAE,iBAAiB,kBAAkB,aAAa,cAAc;AAC9I;AACA,SAAS,gBAAgB,IAAI;IAC3B,IAAI,WAAW,KAAK,cAAc;IAClC,IAAI,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;QAC5B,WAAW,SAAS,kBAAkB;IACxC;IACA,OAAO,aAAa,QAAQ,CAAA,GAAA,6IAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa,CAAA,GAAA,6IAAA,CAAA,cAAW,AAAD,EAAE,aAAa,oBAAoB,SAAS,cAAc;AAChI;AACA,SAAS,uBAAuB,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK;IAC/D,MAAM,uBAAuB,aAAa,IAAI,YAAY,IAAI,CAAC,aAAa,EAAE,IAAI,oBAAoB,KAAK,CAAC,EAAE;IAC9G,IAAI,CAAC,sBAAsB;QACzB,OAAO;IACT;IACA,MAAM,sBAAsB,WAAW,KAAK,MAAM,GAAG,YAAY,IAAI,CAAC,SAAS,IAAI,gBAAgB,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;IAC1H,OAAO;AACT;AACA,SAAS,qBAAqB,KAAK,EAAE,UAAU,EAAE,QAAQ;IACvD,MAAM,wBAAwB,EAAE;IAChC,MAAM,gBAAgB,EAAE;IACxB,MAAM,uBAAuB,EAAE;IAC/B,IAAI,iBAAiB;IACrB,IAAI,gBAAgB;IACpB,MAAM,eAAe;WAAI;KAAM;IAC/B,MAAO,aAAa,MAAM,GAAG,EAAG;QAC9B,MAAM,cAAc,YAAY,CAAC,EAAE;QACnC,MAAM,kBAAkB,YAAY,cAAc;QAClD,MAAM,oBAAoB,gBAAgB,MAAM;QAChD,MAAM,mBAAmB;QACzB,MAAM,iBAAiB,gBAAgB;QACvC,IAAI,kBAAkB,YAAY;YAChC,sBAAsB,IAAI,CAAC;YAC3B,kBAAkB;QACpB,OAAO,IAAI,oBAAoB,UAAU;YACvC,qBAAqB,IAAI,CAAC;QAC5B,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QACA,iBAAiB;QACjB,aAAa,KAAK;IACpB;IACA,OAAO;QAAC;QAAgB;QAAuB;QAAe;KAAqB;AACrF;AACA,SAAS,qBAAqB,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK;IAC9D,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,GAAG,EAAE,MAAM,UAAU;IAChE,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,IAAI,oBAAoB,KAAK,CAAC,EAAE;QAChC,IAAI;QACJ,IAAI,eAAe,GAAG;YACpB,CAAC,cAAc,kBAAkB,GAAG,kBAAkB,SAAS,CAAC;QAClE,OAAO;YACL,GAAG,cAAc,kBAAkB,GAAG,kBAAkB,SAAS,CAAC,YAAY;QAChF;QACA,MAAM,WAAW,CAAA,GAAA,6IAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,IAAI;QAC3C,SAAS,SAAS,CAAC,aAAa,SAAS;QACzC,SAAS,SAAS,CAAC,aAAa,SAAS;QACzC,SAAS,QAAQ,CAAC,aAAa,QAAQ;QACvC,SAAS,MAAM,CAAC;QAChB,aAAa,OAAO,CAAC;QACrB,OAAO;IACT,OAAO,IAAI,MAAM,MAAM,GAAG,GAAG;QAC3B,MAAM,gBAAgB,KAAK,CAAC,EAAE;QAC9B,IAAI,SAAS,cAAc,cAAc,GAAG,MAAM;QAClD,IAAI;QACJ,IAAI,eAAe,GAAG;YACpB,oBAAoB;QACtB,OAAO;YACL,GAAG,kBAAkB,GAAG,cAAc,SAAS,CAAC;QAClD;QACA,MAAM,YAAY,EAAE;QACpB,IAAI;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,cAAc,KAAK,CAAC,EAAE;YAC5B,MAAM,kBAAkB,YAAY,cAAc;YAClD,MAAM,oBAAoB,gBAAgB,MAAM;YAChD,MAAM,mBAAmB;YACzB,MAAM,iBAAiB,SAAS;YAChC,IAAI,mBAAmB,UAAU;gBAC/B,IAAI,kBAAkB,UAAU;oBAC9B,UAAU,IAAI,CAAC;gBACjB,OAAO;oBACL,MAAM,CAAC,cAAc,QAAQ,GAAG,YAAY,SAAS,CAAC,WAAW;oBACjE,UAAU,IAAI,CAAC;oBACf,oBAAoB;gBACtB;YACF;YACA,UAAU;QACZ;QACA,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD;QAC9B,MAAM,mBAAmB,YAAY,UAAU,QAAQ,GAAG,IAAI,CAAC,6IAAA,CAAA,cAAW,IAAI;QAC9E,MAAM,WAAW,CAAA,GAAA,6IAAA,CAAA,kBAAe,AAAD,EAAE,kBAAkB,cAAc;QACjE,SAAS,SAAS,CAAC,kBAAkB,SAAS;QAC9C,SAAS,SAAS,CAAC,kBAAkB,SAAS;QAC9C,SAAS,QAAQ,CAAC,kBAAkB,QAAQ;QAC5C,SAAS,MAAM,CAAC,aAAa;QAC7B,0EAA0E;QAC1E,uCAAuC;QACvC,IAAI,oBAAoB,qBAAqB,mBAAmB;YAC9D,IAAI,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;gBAChC,SAAS,MAAM,CAAC,UAAU,MAAM,CAAC,MAAM,EAAE,UAAU,KAAK,CAAC,MAAM;YACjE,OAAO,IAAI,CAAA,GAAA,6IAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY;gBACtC,SAAS,MAAM,CAAC,GAAG,SAAS,cAAc,GAAG,MAAM;YACrD;QACF;QACA,kBAAkB,OAAO,CAAC;QAC1B,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,KAAK,EAAE,QAAQ,EAAE,QAAQ;IACpD,IAAI,eAAe;WAAI;KAAM;IAC7B,MAAM,cAAc,aAAa,GAAG,CAAC,CAAA,OAAQ,KAAK,cAAc,IAAI,IAAI,CAAC;IACzE,IAAI,OAAO;IACX,IAAI;IACJ,IAAI,kBAAkB;IACtB,MAAO,CAAC,QAAQ,eAAe,MAAM,SAAS,KAAK,UAAU,KAAM;QACjE,MAAM,aAAa,MAAM,KAAK;QAC9B,MAAM,cAAc,MAAM,MAAM;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,UAAU,uBAAuB,kBAAkB,YAAY,kBAAkB,UAAU,aAAa;QAC9G,IAAI,SAAS;YACX,MAAM,CAAC,kBAAiB,eAAe,qBAAqB,GAAG,qBAAqB,cAAc,kBAAkB,YAAY,kBAAkB;YAClJ,MAAM,mBAAmB,kBAAkB,aAAa;YACxD,MAAM,iBAAiB,kBAAkB,WAAW;YACpD,MAAM,oBAAoB,qBAAqB,eAAe,kBAAkB,gBAAgB;YAChG,eAAe,oBAAoB;gBAAC;mBAAsB;aAAqB,GAAG;YAClF,SAAS,MAAM,GAAG,EAAE;YACpB,kBAAkB;QACpB,OAAO;YACL,mBAAmB;QACrB;QACA,OAAO,KAAK,SAAS,CAAC;IACxB;AACF;AACA,SAAS,eAAe,QAAQ,EAAE,QAAQ,EAAE,QAAQ;IAClD,iCAAiC;IACjC,MAAM,WAAW,SAAS,WAAW;IACrC,MAAM,iBAAiB,SAAS,MAAM;IACtC,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,IAAK;QACvC,MAAM,QAAQ,QAAQ,CAAC,EAAE;QACzB,IAAI,CAAC,CAAA,GAAA,6IAAA,CAAA,cAAW,AAAD,EAAE,UAAU,CAAC,MAAM,YAAY,IAAI;YAChD,oBAAoB;YACpB,SAAS,MAAM,SAAS,MAAM;YAC9B;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,OAAO,SAAS,cAAc;IACpC,MAAM,QAAQ,eAAe,MAAM;IACnC,IAAI,UAAU,QAAQ,MAAM,IAAI,KAAK,MAAM;QACzC,oBAAoB;QACpB,SAAS,MAAM,SAAS,MAAM;QAC9B;IACF;IAEA,kBAAkB;IAClB,IAAI,CAAC,oBAAoB,aAAa,CAAC,gBAAgB,WAAW;QAChE,oBAAoB;QACpB,SAAS,MAAM,SAAS,MAAM;QAC9B;IACF;IACA,MAAM,MAAM,SAAS,MAAM;IAC3B,IAAI,QAAQ,MAAM,GAAG,EAAE;QACrB,SAAS,MAAM,CAAC,MAAM,GAAG;QACzB,SAAS,MAAM,GAAG,EAAE;IACtB;IACA,IAAI,MAAM,UAAU,EAAE;QACpB,MAAM,MAAM,SAAS,MAAM;QAC3B,IAAI,QAAQ,MAAM,UAAU,CAAC,GAAG,EAAE;YAChC,SAAS,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,IAAI;YACxC,SAAS,MAAM,UAAU,CAAC,GAAG,IAAI,MAAM;QACzC;QACA,MAAM,SAAS,SAAS,SAAS;QACjC,IAAI,WAAW,MAAM,UAAU,CAAC,MAAM,EAAE;YACtC,SAAS,SAAS,CAAC,MAAM,UAAU,CAAC,MAAM,IAAI;YAC9C,SAAS,MAAM,UAAU,CAAC,MAAM,IAAI,MAAM;QAC5C;IACF;AACF;AAEA,8EAA8E;AAC9E,yEAAyE;AACzE,SAAS,mBAAmB,QAAQ,EAAE,QAAQ,EAAE,QAAQ;IACtD,MAAM,kBAAkB,SAAS,kBAAkB;IACnD,MAAM,cAAc,SAAS,cAAc;IAC3C,MAAM,OAAO,SAAS,cAAc;IACpC,IAAI,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,oBAAoB,CAAC,gBAAgB,aAAa,MAAM,CAAC,CAAC,oBAAoB,SAAS,cAAc,MAAM,gBAAgB,UAAU,GAAG,GAAG;QAC7J,gBAAgB,MAAM,CAAC;QACvB,eAAe,iBAAiB,UAAU;QAC1C,SAAS,MAAM,gBAAgB,MAAM;IACvC;IACA,IAAI,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,CAAC,YAAY,aAAa,MAAM,CAAC,kBAAkB,OAAO;QAC5F,oBAAoB;QACpB,eAAe,aAAa,UAAU;QACtC,SAAS,MAAM,YAAY,MAAM;IACnC;AACF;AACA,SAAS,oBAAoB,IAAI;IAC/B,MAAM,WAAW,KAAK,WAAW;IACjC,MAAM,iBAAiB,SAAS,MAAM;IACtC,IAAK,IAAI,IAAI,iBAAiB,GAAG,KAAK,GAAG,IAAK;QAC5C,KAAK,WAAW,CAAC,QAAQ,CAAC,EAAE;IAC9B;IACA,KAAK,MAAM;IACX,OAAO,SAAS,GAAG,CAAC,CAAA,QAAS,MAAM,SAAS;AAC9C;AACA,SAAS,oBAAoB,QAAQ;IACnC,sFAAsF;IACtF,MAAM,mBAAmB;QAAC;KAAS;IACnC,IAAI,cAAc,SAAS,cAAc;IACzC,MAAO,gBAAgB,QAAQ,CAAA,GAAA,6IAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,YAAY,YAAY,GAAI;QACrF,iBAAiB,IAAI,CAAC;QACtB,IAAI,OAAO,IAAI,CAAC,YAAY,cAAc,KAAK;YAC7C;QACF;QACA,cAAc,YAAY,cAAc;IAC1C;IACA,OAAO;AACT;AACA,SAAS,YAAY,MAAM,EAAE,QAAQ,EAAE,QAAQ;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,OAAO,QAAQ,CAAC;gBAAC,2JAAA,CAAA,eAAY;aAAC,GAAG;gBACpC;oBACE,sBAAsB,CAAC,4DAA4D,CAAC;gBACtF;YACF;YACA,MAAM;yDAAkB,CAAC,KAAK;oBAC5B,IAAI,UAAU;wBACZ,SAAS,KAAK;oBAChB;gBACF;;YACA,OAAO,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,qBAAqB,CAAC,6IAAA,CAAA,WAAQ;yCAAE,CAAA;oBAC1D,MAAM,SAAS,SAAS,gBAAgB;oBACxC,MAAM,WAAW,SAAS,kBAAkB;oBAC5C,IAAI,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,WAAW,CAAC,OAAO,aAAa,IAAI;wBACtD,eAAe,QAAQ,UAAU;oBACnC,OAAO,IAAI,CAAC,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,SAAS;wBAC/B,IAAI,SAAS,YAAY,MAAM,CAAC,oBAAoB,SAAS,cAAc,OAAO,CAAC,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,GAAG;4BAC7G,MAAM,mBAAmB,oBAAoB;4BAC7C,oBAAoB,kBAAkB,UAAU;wBAClD;wBACA,mBAAmB,UAAU,UAAU;oBACzC;gBACF;yCAAI,OAAO,eAAe,CAAC,2JAAA,CAAA,sBAAmB;yCAAE,CAAA;oBAC9C,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,gBAAa,AAAD;oBAC9B,IAAI,YAAY,QAAQ,CAAC,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;wBACrD,OAAO;oBACT;oBACA,MAAM,QAAQ,UAAU,OAAO;oBAC/B,MAAM,OAAO;iDAAC,CAAA;4BACZ,MAAM,SAAS,KAAK,SAAS;4BAC7B,IAAI,CAAA,GAAA,2JAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;gCAC3B,mBAAmB;gCACnB,OAAO,aAAa,CAAC,CAAC,OAAO,aAAa;gCAC1C,OAAO,SAAS;4BAClB;wBACF;;oBACA,OAAO;gBACT;wCAAG,6IAAA,CAAA,uBAAoB;QACzB;gCAAG;QAAC;QAAQ;QAAU;KAAS;AACjC;AACA,SAAS,eAAe,EACtB,QAAQ,EACR,QAAQ,EACT;IACC,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,uKAAA,CAAA,4BAAyB,AAAD;IACzC,YAAY,QAAQ,UAAU;IAC9B,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalCollaborationContext.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { createContext, useContext } from 'react';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst entries = [['Cat', 'rgb(125, 50, 0)'], ['<PERSON>', 'rgb(100, 0, 0)'], ['Rabbit', 'rgb(150, 0, 0)'], ['<PERSON>', 'rgb(200, 0, 0)'], ['<PERSON>', 'rgb(200, 75, 0)'], ['Hedgehog', 'rgb(0, 75, 0)'], ['Pigeon', 'rgb(0, 125, 0)'], ['Squirrel', 'rgb(75, 100, 0)'], ['Bear', 'rgb(125, 100, 0)'], ['<PERSON>', 'rgb(0, 0, 150)'], ['<PERSON><PERSON>', 'rgb(0, 0, 200)'], ['Zebra', 'rgb(0, 0, 250)'], ['<PERSON>', 'rgb(0, 100, 150)'], ['Owl', 'rgb(0, 100, 100)'], ['Gull', 'rgb(100, 0, 100)'], ['Squid', 'rgb(150, 0, 150)']];\nconst randomEntry = entries[Math.floor(Math.random() * entries.length)];\nconst CollaborationContext = /*#__PURE__*/createContext({\n  clientID: 0,\n  color: randomEntry[1],\n  isCollabActive: false,\n  name: randomEntry[0],\n  yjsDocMap: new Map()\n});\nfunction useCollaborationContext(username, color) {\n  const collabContext = useContext(CollaborationContext);\n  if (username != null) {\n    collabContext.name = username;\n  }\n  if (color != null) {\n    collabContext.color = color;\n  }\n  return collabContext;\n}\n\nexport { CollaborationContext, useCollaborationContext };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;AAED;;AAEA;;;;;;CAMC,GAED,MAAM,UAAU;IAAC;QAAC;QAAO;KAAkB;IAAE;QAAC;QAAO;KAAiB;IAAE;QAAC;QAAU;KAAiB;IAAE;QAAC;QAAQ;KAAiB;IAAE;QAAC;QAAO;KAAkB;IAAE;QAAC;QAAY;KAAgB;IAAE;QAAC;QAAU;KAAiB;IAAE;QAAC;QAAY;KAAkB;IAAE;QAAC;QAAQ;KAAmB;IAAE;QAAC;QAAS;KAAiB;IAAE;QAAC;QAAW;KAAiB;IAAE;QAAC;QAAS;KAAiB;IAAE;QAAC;QAAQ;KAAmB;IAAE;QAAC;QAAO;KAAmB;IAAE;QAAC;QAAQ;KAAmB;IAAE;QAAC;QAAS;KAAmB;CAAC;AAC1e,MAAM,cAAc,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;AACvE,MAAM,uBAAuB,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;IACtD,UAAU;IACV,OAAO,WAAW,CAAC,EAAE;IACrB,gBAAgB;IAChB,MAAM,WAAW,CAAC,EAAE;IACpB,WAAW,IAAI;AACjB;AACA,SAAS,wBAAwB,QAAQ,EAAE,KAAK;IAC9C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IACjC,IAAI,YAAY,MAAM;QACpB,cAAc,IAAI,GAAG;IACvB;IACA,IAAI,SAAS,MAAM;QACjB,cAAc,KAAK,GAAG;IACxB;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40lexical/react/LexicalNestedComposer.dev.mjs"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport { useCollaborationContext } from '@lexical/react/LexicalCollaborationContext';\nimport { LexicalComposerContext, createLexicalComposerContext } from '@lexical/react/LexicalComposerContext';\nimport { createSharedNodeState, getRegisteredNode, getStaticNodeConfig } from 'lexical';\nimport { useRef, useContext, useMemo, useEffect } from 'react';\nimport { jsx } from 'react/jsx-runtime';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/*@__INLINE__*/\nfunction warnOnlyOnce(message) {\n  {\n    let run = false;\n    return () => {\n      if (!run) {\n        console.warn(message);\n      }\n      run = true;\n    };\n  }\n}\n\nfunction getTransformSetFromKlass(klass) {\n  const transforms = new Set();\n  const {\n    ownNodeConfig\n  } = getStaticNodeConfig(klass);\n  const transform = klass.transform();\n  if (ownNodeConfig) {\n    const $transform = ownNodeConfig.$transform;\n    if ($transform) {\n      transforms.add($transform);\n    }\n  }\n  if (transform) {\n    transforms.add(transform);\n  }\n  return transforms;\n}\nconst initialNodesWarning = warnOnlyOnce(`LexicalNestedComposer initialNodes is deprecated and will be removed in v0.32.0, it has never worked correctly.\\nYou can configure your editor's nodes with createEditor({nodes: [], parentEditor: $getEditor()})`);\nconst explicitNamespaceWarning = warnOnlyOnce(`LexicalNestedComposer initialEditor should explicitly initialize its namespace when the node configuration differs from the parentEditor. For backwards compatibility, the namespace will be initialized from parentEditor until v0.32.0, but this has always had incorrect copy/paste behavior when the configuration differed.\\nYou can configure your editor's namespace with createEditor({namespace: 'nested-editor-namespace', nodes: [], parentEditor: $getEditor()}).`);\nfunction LexicalNestedComposer({\n  initialEditor,\n  children,\n  initialNodes,\n  initialTheme,\n  skipCollabChecks,\n  skipEditableListener\n}) {\n  const wasCollabPreviouslyReadyRef = useRef(false);\n  const parentContext = useContext(LexicalComposerContext);\n  if (parentContext == null) {\n    {\n      formatDevErrorMessage(`Unexpected parent context null on a nested composer`);\n    }\n  }\n  const [parentEditor, {\n    getTheme: getParentTheme\n  }] = parentContext;\n  const composerContext = useMemo(() => {\n    const composerTheme = initialTheme || getParentTheme() || undefined;\n    const context = createLexicalComposerContext(parentContext, composerTheme);\n    if (composerTheme !== undefined) {\n      initialEditor._config.theme = composerTheme;\n    }\n    initialEditor._parentEditor = initialEditor._parentEditor || parentEditor;\n    const createEditorArgs = initialEditor._createEditorArgs;\n    const explicitNamespace = createEditorArgs && createEditorArgs.namespace;\n    if (!initialNodes) {\n      if (!(createEditorArgs && createEditorArgs.nodes)) {\n        const parentNodes = initialEditor._nodes = new Map(parentEditor._nodes);\n        if (!explicitNamespace) {\n          // This is the only safe situation to inherit the parent's namespace\n          initialEditor._config.namespace = parentEditor._config.namespace;\n        }\n        for (const [type, entry] of parentNodes) {\n          initialEditor._nodes.set(type, {\n            exportDOM: entry.exportDOM,\n            klass: entry.klass,\n            replace: entry.replace,\n            replaceWithKlass: entry.replaceWithKlass,\n            sharedNodeState: createSharedNodeState(entry.klass),\n            transforms: getTransformSetFromKlass(entry.klass)\n          });\n        }\n      } else if (!explicitNamespace) {\n        explicitNamespaceWarning();\n        initialEditor._config.namespace = parentEditor._config.namespace;\n      }\n    } else {\n      initialNodesWarning();\n      if (!explicitNamespace) {\n        explicitNamespaceWarning();\n        initialEditor._config.namespace = parentEditor._config.namespace;\n      }\n      for (let klass of initialNodes) {\n        let replace = null;\n        let replaceWithKlass = null;\n        if (typeof klass !== 'function') {\n          const options = klass;\n          klass = options.replace;\n          replace = options.with;\n          replaceWithKlass = options.withKlass || null;\n        }\n        const registeredKlass = getRegisteredNode(initialEditor, klass.getType());\n        initialEditor._nodes.set(klass.getType(), {\n          exportDOM: registeredKlass ? registeredKlass.exportDOM : undefined,\n          klass,\n          replace,\n          replaceWithKlass,\n          sharedNodeState: createSharedNodeState(klass),\n          transforms: getTransformSetFromKlass(klass)\n        });\n      }\n    }\n    return [initialEditor, context];\n  },\n  // We only do this for init\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n\n  // If collaboration is enabled, make sure we don't render the children until the collaboration subdocument is ready.\n  const {\n    isCollabActive,\n    yjsDocMap\n  } = useCollaborationContext();\n  const isCollabReady = skipCollabChecks || wasCollabPreviouslyReadyRef.current || yjsDocMap.has(initialEditor.getKey());\n  useEffect(() => {\n    if (isCollabReady) {\n      wasCollabPreviouslyReadyRef.current = true;\n    }\n  }, [isCollabReady]);\n\n  // Update `isEditable` state of nested editor in response to the same change on parent editor.\n  useEffect(() => {\n    if (!skipEditableListener) {\n      const editableListener = editable => initialEditor.setEditable(editable);\n      editableListener(parentEditor.isEditable());\n      return parentEditor.registerEditableListener(editableListener);\n    }\n  }, [initialEditor, parentEditor, skipEditableListener]);\n  return /*#__PURE__*/jsx(LexicalComposerContext.Provider, {\n    value: composerContext,\n    children: !isCollabActive || isCollabReady ? children : null\n  });\n}\n\nexport { LexicalNestedComposer };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AACA;AACA;AACA;AACA;;;;;;AAEA;;;;;;CAMC,GAED,qEAAqE;AAErE,SAAS,sBAAsB,OAAO;IACpC,MAAM,IAAI,MAAM;AAClB;AAEA;;;;;;CAMC,GAED,aAAa,GACb,SAAS,aAAa,OAAO;IAC3B;QACE,IAAI,MAAM;QACV,OAAO;YACL,IAAI,CAAC,KAAK;gBACR,QAAQ,IAAI,CAAC;YACf;YACA,MAAM;QACR;IACF;AACF;AAEA,SAAS,yBAAyB,KAAK;IACrC,MAAM,aAAa,IAAI;IACvB,MAAM,EACJ,aAAa,EACd,GAAG,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD,EAAE;IACxB,MAAM,YAAY,MAAM,SAAS;IACjC,IAAI,eAAe;QACjB,MAAM,aAAa,cAAc,UAAU;QAC3C,IAAI,YAAY;YACd,WAAW,GAAG,CAAC;QACjB;IACF;IACA,IAAI,WAAW;QACb,WAAW,GAAG,CAAC;IACjB;IACA,OAAO;AACT;AACA,MAAM,sBAAsB,aAAa,CAAC,iNAAiN,CAAC;AAC5P,MAAM,2BAA2B,aAAa,CAAC,6cAA6c,CAAC;AAC7f,SAAS,sBAAsB,EAC7B,aAAa,EACb,QAAQ,EACR,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,oBAAoB,EACrB;IACC,MAAM,8BAA8B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,uKAAA,CAAA,yBAAsB;IACvD,IAAI,iBAAiB,MAAM;QACzB;YACE,sBAAsB,CAAC,mDAAmD,CAAC;QAC7E;IACF;IACA,MAAM,CAAC,cAAc,EACnB,UAAU,cAAc,EACzB,CAAC,GAAG;IACL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0DAAE;YAC9B,MAAM,gBAAgB,gBAAgB,oBAAoB;YAC1D,MAAM,UAAU,CAAA,GAAA,uKAAA,CAAA,+BAA4B,AAAD,EAAE,eAAe;YAC5D,IAAI,kBAAkB,WAAW;gBAC/B,cAAc,OAAO,CAAC,KAAK,GAAG;YAChC;YACA,cAAc,aAAa,GAAG,cAAc,aAAa,IAAI;YAC7D,MAAM,mBAAmB,cAAc,iBAAiB;YACxD,MAAM,oBAAoB,oBAAoB,iBAAiB,SAAS;YACxE,IAAI,CAAC,cAAc;gBACjB,IAAI,CAAC,CAAC,oBAAoB,iBAAiB,KAAK,GAAG;oBACjD,MAAM,cAAc,cAAc,MAAM,GAAG,IAAI,IAAI,aAAa,MAAM;oBACtE,IAAI,CAAC,mBAAmB;wBACtB,oEAAoE;wBACpE,cAAc,OAAO,CAAC,SAAS,GAAG,aAAa,OAAO,CAAC,SAAS;oBAClE;oBACA,KAAK,MAAM,CAAC,MAAM,MAAM,IAAI,YAAa;wBACvC,cAAc,MAAM,CAAC,GAAG,CAAC,MAAM;4BAC7B,WAAW,MAAM,SAAS;4BAC1B,OAAO,MAAM,KAAK;4BAClB,SAAS,MAAM,OAAO;4BACtB,kBAAkB,MAAM,gBAAgB;4BACxC,iBAAiB,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,KAAK;4BAClD,YAAY,yBAAyB,MAAM,KAAK;wBAClD;oBACF;gBACF,OAAO,IAAI,CAAC,mBAAmB;oBAC7B;oBACA,cAAc,OAAO,CAAC,SAAS,GAAG,aAAa,OAAO,CAAC,SAAS;gBAClE;YACF,OAAO;gBACL;gBACA,IAAI,CAAC,mBAAmB;oBACtB;oBACA,cAAc,OAAO,CAAC,SAAS,GAAG,aAAa,OAAO,CAAC,SAAS;gBAClE;gBACA,KAAK,IAAI,SAAS,aAAc;oBAC9B,IAAI,UAAU;oBACd,IAAI,mBAAmB;oBACvB,IAAI,OAAO,UAAU,YAAY;wBAC/B,MAAM,UAAU;wBAChB,QAAQ,QAAQ,OAAO;wBACvB,UAAU,QAAQ,IAAI;wBACtB,mBAAmB,QAAQ,SAAS,IAAI;oBAC1C;oBACA,MAAM,kBAAkB,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe,MAAM,OAAO;oBACtE,cAAc,MAAM,CAAC,GAAG,CAAC,MAAM,OAAO,IAAI;wBACxC,WAAW,kBAAkB,gBAAgB,SAAS,GAAG;wBACzD;wBACA;wBACA;wBACA,iBAAiB,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD,EAAE;wBACvC,YAAY,yBAAyB;oBACvC;gBACF;YACF;YACA,OAAO;gBAAC;gBAAe;aAAQ;QACjC;yDACA,2BAA2B;IAC3B,uDAAuD;IACvD,EAAE;IAEF,oHAAoH;IACpH,MAAM,EACJ,cAAc,EACd,SAAS,EACV,GAAG,CAAA,GAAA,4KAAA,CAAA,0BAAuB,AAAD;IAC1B,MAAM,gBAAgB,oBAAoB,4BAA4B,OAAO,IAAI,UAAU,GAAG,CAAC,cAAc,MAAM;IACnH,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,eAAe;gBACjB,4BAA4B,OAAO,GAAG;YACxC;QACF;0CAAG;QAAC;KAAc;IAElB,8FAA8F;IAC9F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,CAAC,sBAAsB;gBACzB,MAAM;wEAAmB,CAAA,WAAY,cAAc,WAAW,CAAC;;gBAC/D,iBAAiB,aAAa,UAAU;gBACxC,OAAO,aAAa,wBAAwB,CAAC;YAC/C;QACF;0CAAG;QAAC;QAAe;QAAc;KAAqB;IACtD,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,uKAAA,CAAA,yBAAsB,CAAC,QAAQ,EAAE;QACvD,OAAO;QACP,UAAU,CAAC,kBAAkB,gBAAgB,WAAW;IAC1D;AACF", "ignoreList": [0], "debugId": null}}]}