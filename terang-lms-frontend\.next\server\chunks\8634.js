try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c9a625f4-389d-40f0-a3a9-64d691e3bde0",e._sentryDebugIdIdentifier="sentry-dbid-c9a625f4-389d-40f0-a3a9-64d691e3bde0")}catch(e){}"use strict";exports.id=8634,exports.ids=[8634],exports.modules={2457:(e,t,r)=>{r.d(t,{Bc:()=>o,ZI:()=>l,k$:()=>d,m_:()=>i});var a=r(91754);r(93491);var n=r(99830),s=r(82233);function o({delayDuration:e=0,...t}){return(0,a.jsx)(n.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t,"data-sentry-element":"TooltipPrimitive.Provider","data-sentry-component":"TooltipProvider","data-sentry-source-file":"tooltip.tsx"})}function i({...e}){return(0,a.jsx)(o,{"data-sentry-element":"TooltipProvider","data-sentry-component":"Tooltip","data-sentry-source-file":"tooltip.tsx",children:(0,a.jsx)(n.bL,{"data-slot":"tooltip",...e,"data-sentry-element":"TooltipPrimitive.Root","data-sentry-source-file":"tooltip.tsx"})})}function d({...e}){return(0,a.jsx)(n.l9,{"data-slot":"tooltip-trigger",...e,"data-sentry-element":"TooltipPrimitive.Trigger","data-sentry-component":"TooltipTrigger","data-sentry-source-file":"tooltip.tsx"})}function l({className:e,sideOffset:t=0,children:r,...o}){return(0,a.jsx)(n.ZL,{"data-sentry-element":"TooltipPrimitive.Portal","data-sentry-component":"TooltipContent","data-sentry-source-file":"tooltip.tsx",children:(0,a.jsxs)(n.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,s.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...o,"data-sentry-element":"TooltipPrimitive.Content","data-sentry-source-file":"tooltip.tsx",children:[r,(0,a.jsx)(n.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]","data-sentry-element":"TooltipPrimitive.Arrow","data-sentry-source-file":"tooltip.tsx"})]})})}},3033:(e,t,r)=>{r.d(t,{default:()=>b});var a=r(91754);r(93491);var n=r(84436),s=r(56682),o=r(92681),i=r(86778),d=r(21372),l=r(48071);function c(){let e=(0,d.useRouter)(),{user:t,signOut:r}=(0,l.A)();return t?(0,a.jsxs)(o.rI,{"data-sentry-element":"DropdownMenu","data-sentry-component":"UserNav","data-sentry-source-file":"user-nav.tsx",children:[(0,a.jsx)(o.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"user-nav.tsx",children:(0,a.jsx)(s.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full cursor-pointer","data-sentry-element":"Button","data-sentry-source-file":"user-nav.tsx",children:(0,a.jsx)(i.M,{"data-sentry-element":"UserAvatarProfile","data-sentry-source-file":"user-nav.tsx"})})}),(0,a.jsxs)(o.SQ,{className:"w-56",align:"end",sideOffset:10,forceMount:!0,"data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"user-nav.tsx",children:[(0,a.jsx)(o.lp,{className:"font-normal","data-sentry-element":"DropdownMenuLabel","data-sentry-source-file":"user-nav.tsx",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("p",{className:"text-sm leading-none font-medium",children:t.name}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs leading-none",children:t.email})]})}),(0,a.jsx)(o.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"user-nav.tsx"}),(0,a.jsxs)(o.I,{"data-sentry-element":"DropdownMenuGroup","data-sentry-source-file":"user-nav.tsx",children:[(0,a.jsx)(o._2,{onClick:()=>e.push("/dashboard/profile"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"user-nav.tsx",children:"Profile"}),(0,a.jsx)(o._2,{"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"user-nav.tsx",children:"Billing"})]}),(0,a.jsx)(o.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"user-nav.tsx"}),(0,a.jsx)(o._2,{onClick:r,"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"user-nav.tsx",children:"Sign Out"})]})]}):null}var u=r(22928),m=r(85650),p=r(39547),f=r(85629);function b(){let e=(0,d.useRouter)();return(0,a.jsxs)("header",{className:"flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12","data-sentry-component":"Header","data-sentry-source-file":"header.tsx",children:[(0,a.jsx)("div",{className:"flex items-center gap-2 px-4",children:(0,a.jsx)(n.x2,{className:"-ml-1","data-sentry-element":"SidebarTrigger","data-sentry-source-file":"header.tsx"})}),(0,a.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,a.jsxs)(o.rI,{"data-sentry-element":"DropdownMenu","data-sentry-source-file":"header.tsx",children:[(0,a.jsx)(o.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"header.tsx",children:(0,a.jsxs)(s.$,{variant:"ghost",size:"icon",className:"relative h-8 w-8 rounded-full cursor-pointer","data-sentry-element":"Button","data-sentry-source-file":"header.tsx",children:[(0,a.jsx)(u.A,{className:"h-4 w-4","data-sentry-element":"User","data-sentry-source-file":"header.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Profile Menu"})]})}),(0,a.jsxs)(o.SQ,{className:"w-56",align:"end",sideOffset:10,"data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"header.tsx",children:[(0,a.jsx)(o.lp,{"data-sentry-element":"DropdownMenuLabel","data-sentry-source-file":"header.tsx",children:"Profile & Settings"}),(0,a.jsx)(o.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"header.tsx"}),(0,a.jsxs)(o.I,{"data-sentry-element":"DropdownMenuGroup","data-sentry-source-file":"header.tsx",children:[(0,a.jsxs)(o._2,{onClick:()=>e.push("/dashboard/profile"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"header.tsx",children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"User","data-sentry-source-file":"header.tsx"}),"View Profile"]}),(0,a.jsxs)(o._2,{onClick:()=>e.push("/dashboard/settings"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"header.tsx",children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Settings","data-sentry-source-file":"header.tsx"}),"Settings"]}),(0,a.jsxs)(o._2,{onClick:()=>e.push("/dashboard/help"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"header.tsx",children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4","data-sentry-element":"HelpCircle","data-sentry-source-file":"header.tsx"}),"Help & Support"]}),(0,a.jsxs)(o._2,{onClick:()=>e.push("/dashboard/documentation"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"header.tsx",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4","data-sentry-element":"FileText","data-sentry-source-file":"header.tsx"}),"Documentation"]})]})]})]}),(0,a.jsx)(c,{"data-sentry-element":"UserNav","data-sentry-source-file":"header.tsx"})]})]})}},4590:(e,t,r)=>{r.d(t,{SidebarInset:()=>n,SidebarProvider:()=>s});var a=r(1472);(0,a.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","Sidebar"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarContent"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarFooter"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarGroup"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarGroupAction"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarGroupContent"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarGroupLabel"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarHeader"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarInput");let n=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarInset");(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenu"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuAction"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuBadge"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuButton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuItem"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuSkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuSub"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuSubButton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarMenuSubItem");let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarRail"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarSeparator"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","SidebarTrigger"),(0,a.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\sidebar.tsx","useSidebar")},5434:(e,t,r)=>{r.d(t,{default:()=>a});let a=(0,r(1472).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\kbar\\index.tsx","default")},7346:(e,t,r)=>{r.d(t,{default:()=>m});var a=r(91754),n=r(93400),s=r(97969),o=r(21372),i=r(93491);let d=i.forwardRef(({action:e,active:t,currentRootActionId:r},n)=>{let s=i.useMemo(()=>{if(!r)return e.ancestors;let t=e.ancestors.findIndex(e=>e.id===r);return e.ancestors.slice(t+1)},[e.ancestors,r]);return(0,a.jsxs)("div",{ref:n,className:"relative z-10 flex cursor-pointer items-center justify-between px-4 py-3",children:[t&&(0,a.jsx)("div",{id:"kbar-result-item",className:"border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4"}),(0,a.jsxs)("div",{className:"relative z-10 flex items-center gap-2",children:[e.icon&&e.icon,(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{children:[s.length>0&&s.map(e=>(0,a.jsxs)(i.Fragment,{children:[(0,a.jsx)("span",{className:"text-muted-foreground mr-2",children:e.name}),(0,a.jsx)("span",{className:"mr-2",children:"›"})]},e.id)),(0,a.jsx)("span",{children:e.name})]}),e.subtitle&&(0,a.jsx)("span",{className:"text-muted-foreground text-sm",children:e.subtitle})]})]}),e.shortcut?.length?(0,a.jsx)("div",{className:"relative z-10 grid grid-flow-col gap-1",children:e.shortcut.map((e,t)=>(0,a.jsx)("kbd",{className:"bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium",children:e},e+t))}):null]})});function l(){let{results:e,rootActionId:t}=(0,s.useMatches)();return(0,a.jsx)(s.KBarResults,{items:e,onRender:({item:e,active:r})=>"string"==typeof e?(0,a.jsx)("div",{className:"text-primary-foreground px-4 py-2 text-sm uppercase opacity-50",children:e}):(0,a.jsx)(d,{action:e,active:r,currentRootActionId:t??""}),"data-sentry-element":"KBarResults","data-sentry-component":"RenderResults","data-sentry-source-file":"render-result.tsx"})}d.displayName="KBarResultItem";var c=r(73233);let u=()=>{let{theme:e,setTheme:t}=(0,c.D)();(0,s.useRegisterActions)([{id:"toggleTheme",name:"Toggle Theme",shortcut:["t","t"],section:"Theme",perform:()=>{t("light"===e?"dark":"light")}},{id:"setLightTheme",name:"Set Light Theme",section:"Theme",perform:()=>t("light")},{id:"setDarkTheme",name:"Set Dark Theme",section:"Theme",perform:()=>t("dark")}],[e])};function m({children:e}){let t=(0,o.useRouter)(),r=(0,i.useMemo)(()=>{let e=e=>{t.push(e)};return n.Cn.flatMap(t=>{let r="#"!==t.url?{id:`${t.title.toLowerCase()}Action`,name:t.title,shortcut:t.shortcut,keywords:t.title.toLowerCase(),section:"Navigation",subtitle:`Go to ${t.title}`,perform:()=>e(t.url)}:null,a=t.items?.map(r=>({id:`${r.title.toLowerCase()}Action`,name:r.title,shortcut:r.shortcut,keywords:r.title.toLowerCase(),section:t.title,subtitle:`Go to ${r.title}`,perform:()=>e(r.url)}))??[];return r?[r,...a]:a})},[t]);return(0,a.jsx)(s.KBarProvider,{actions:r,"data-sentry-element":"KBarProvider","data-sentry-component":"KBar","data-sentry-source-file":"index.tsx",children:(0,a.jsx)(p,{"data-sentry-element":"KBarComponent","data-sentry-source-file":"index.tsx",children:e})})}let p=({children:e})=>(u(),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.KBarPortal,{"data-sentry-element":"KBarPortal","data-sentry-source-file":"index.tsx",children:(0,a.jsx)(s.KBarPositioner,{className:"bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm","data-sentry-element":"KBarPositioner","data-sentry-source-file":"index.tsx",children:(0,a.jsxs)(s.KBarAnimator,{className:"bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg","data-sentry-element":"KBarAnimator","data-sentry-source-file":"index.tsx",children:[(0,a.jsx)("div",{className:"bg-card border-border sticky top-0 z-10 border-b",children:(0,a.jsx)(s.KBarSearch,{className:"bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden","data-sentry-element":"KBarSearch","data-sentry-source-file":"index.tsx"})}),(0,a.jsx)("div",{className:"max-h-[400px]",children:(0,a.jsx)(l,{"data-sentry-element":"RenderResults","data-sentry-source-file":"index.tsx"})})]})})}),e]}))},21444:(e,t,r)=>{r.d(t,{default:()=>eh});var a=r(91754),n=r(7361);function s({...e}){return(0,a.jsx)(n.bL,{"data-slot":"collapsible",...e,"data-sentry-element":"CollapsiblePrimitive.Root","data-sentry-component":"Collapsible","data-sentry-source-file":"collapsible.tsx"})}function o({...e}){return(0,a.jsx)(n.R6,{"data-slot":"collapsible-trigger",...e,"data-sentry-element":"CollapsiblePrimitive.CollapsibleTrigger","data-sentry-component":"CollapsibleTrigger","data-sentry-source-file":"collapsible.tsx"})}function i({...e}){return(0,a.jsx)(n.Ke,{"data-slot":"collapsible-content",...e,"data-sentry-element":"CollapsiblePrimitive.CollapsibleContent","data-sentry-component":"CollapsibleContent","data-sentry-source-file":"collapsible.tsx"})}var d=r(92681),l=r(84436),c=r(86778);let u=[{title:"Dashboard",url:"/dashboard/admin",icon:"dashboard",isActive:!1,shortcut:["d","d"],items:[]},{title:"Institutions",url:"/dashboard/admin/institutions",icon:"building",isActive:!1,shortcut:["i","i"],items:[{title:"All Institutions",url:"/dashboard/admin/institutions",icon:"building"},{title:"Add Institution",url:"/dashboard/admin/institutions/new",icon:"plus"}]},{title:"Users",url:"/dashboard/admin/users",icon:"users",isActive:!1,shortcut:["u","u"],items:[{title:"All Users",url:"/dashboard/admin/users",icon:"users"},{title:"Add User",url:"/dashboard/admin/users/new",icon:"userPlus"}]},{title:"Subscriptions",url:"/dashboard/admin/subscriptions",icon:"creditCard",isActive:!1,shortcut:["s","s"],items:[]},{title:"Analytics",url:"/dashboard/admin/analytics",icon:"barChart",isActive:!1,shortcut:["a","a"],items:[]},{title:"Account",url:"#",icon:"billing",isActive:!1,items:[{title:"Profile",url:"/dashboard/profile",icon:"userPen",shortcut:["p","p"]},{title:"Settings",url:"/dashboard/settings",icon:"settings"}]}],m=[{title:"Dashboard",url:"/dashboard/teacher",icon:"dashboard",isActive:!1,shortcut:["d","d"],items:[]},{title:"Courses",url:"/dashboard/teacher/courses",icon:"bookOpen",isActive:!1,shortcut:["o","o"],items:[{title:"My Courses",url:"/dashboard/teacher/courses",icon:"bookOpen"},{title:"Create Course",url:"/dashboard/teacher/courses/new",icon:"plus"}]}],p=[{title:"Dashboard",url:"/dashboard/student",icon:"dashboard",isActive:!1,shortcut:["d","d"],items:[]},{title:"My Courses",url:"/dashboard/student/courses",icon:"bookOpen",isActive:!1,shortcut:["c","c"],items:[]},{title:"Progress",url:"/dashboard/student/progress",icon:"trendingUp",isActive:!1,shortcut:["p","p"],items:[]},{title:"Certificates",url:"/dashboard/student/certificates",icon:"award",isActive:!1,shortcut:["e","e"],items:[]},{title:"Account",url:"#",icon:"billing",isActive:!1,items:[{title:"Profile",url:"/dashboard/profile",icon:"userPen",shortcut:["r","r"]},{title:"Settings",url:"/dashboard/settings",icon:"settings"}]}],f=e=>{switch(e){case"super_admin":return u;case"teacher":return m;case"student":return p;default:return[]}};var b=r(76328),h=r(93400),x=r(93491),g=r(96196),v=r(44331),y=r(52068),A=r(16041),j=r.n(A),w=r(15854),C=r(21372),S=r(66500),N=r(80294),M=r(93049),D=r(31619),I=r(73136),k=r(88373),E=r(62364),R=r(37980),G=r(93626),T=r(95625),_=r(85629),P=r(65289),L=r(54950),U=r(41051),F=r(7018),z=r(85650),K=r(73473),B=r(57e3),W=r(41939),O=r(2804),$=r(22928),q=r(94065),H=r(39547),X=r(33489),Z=r(33649),V=r(89438),Q=r(66863),Y=r(53636),J=r(3671),ee=r(87435),et=r(84795),er=r(5574),ea=r(19845),en=r(84386),es=r(41867),eo=r(63890),ei=r(19698),ed=r(77406),el=r(69622),ec=r(38192),eu=r(11607);let em={dashboard:S.A,logo:N.A,login:M.A,close:D.A,product:I.A,spinner:k.A,kanban:E.A,chevronLeft:R.A,chevronRight:v.A,trash:G.A,employee:T.A,post:_.A,page:P.A,userPen:L.A,user2:U.A,media:F.A,settings:z.A,billing:K.A,ellipsis:B.A,add:W.A,warning:O.A,user:$.A,arrowRight:q.A,help:H.A,pizza:X.A,sun:Z.A,moon:V.A,laptop:Q.A,github:Y.A,twitter:J.A,check:ee.A,building:g.A,users:et.A,userPlus:er.A,userCheck:ea.A,creditCard:K.A,barChart:en.A,bookOpen:es.A,book:eo.A,bot:ei.A,trendingUp:ed.A,award:el.A,plus:W.A,school:es.A,certificate:el.A,enrollment:er.A,searchList:ec.A,graduationCap:eu.A};var ep=r(36990);let ef=e=>{switch(e){case"teacher":return{label:"Teacher",icon:ep.A,bgColor:"bg-blue-600"};case"student":return{label:"Student",icon:es.A,bgColor:"bg-green-600"};case"super_admin":return{label:"Institution Manager",icon:g.A,bgColor:"bg-purple-600"};default:return{label:"User",icon:es.A,bgColor:"bg-gray-600"}}};function eb(){let[e,t]=x.useState(b.qs.getUser());if(x.useEffect(()=>{t(b.qs.getUser())},[]),!e)return null;let r=ef(e.role),n=r.icon;return(0,a.jsx)(l.wZ,{"data-sentry-element":"SidebarMenu","data-sentry-component":"RoleIndicator","data-sentry-source-file":"role-indicator.tsx",children:(0,a.jsx)(l.FX,{"data-sentry-element":"SidebarMenuItem","data-sentry-source-file":"role-indicator.tsx",children:(0,a.jsxs)(l.Uj,{size:"lg",className:"cursor-default hover:bg-transparent","data-sentry-element":"SidebarMenuButton","data-sentry-source-file":"role-indicator.tsx",children:[(0,a.jsx)("div",{className:`${r.bgColor} text-white flex aspect-square size-8 items-center justify-center rounded-lg`,children:(0,a.jsx)(n,{className:"size-4","data-sentry-element":"Icon","data-sentry-source-file":"role-indicator.tsx"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-0.5 leading-none",children:[(0,a.jsx)("span",{className:"font-semibold",children:e.name}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Account Type: ",r.label]})]})]})})})}function eh(){let e=(0,C.usePathname)(),{isOpen:t}=function(){let[e,t]=(0,x.useState)(!1);return{isOpen:e}}(),r=(0,C.useRouter)(),{state:n}=(0,l.cL)(),[u,m]=x.useState([]);x.useEffect(()=>{let t=b.qs.getUser();t?"student"===t.role?m((0,h.iI)(e)):m(f(t.role)):m((0,h.iI)(e))},[e]);let p=u.length>0?u:(0,h.iI)(e);return(0,a.jsxs)(l.Bx,{collapsible:"icon","data-sentry-element":"Sidebar","data-sentry-component":"AppSidebar","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsxs)(l.Gh,{className:"p-4","data-sentry-element":"SidebarHeader","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)(j(),{href:"/","data-sentry-element":"Link","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsx)(w.default,{src:"/assets/logo-iai.png",alt:"IAI Logo",width:"collapsed"===n?48:160,height:"collapsed"===n?48:60,className:"object-contain transition-all duration-200 cursor-pointer hover:opacity-80",priority:!0,"data-sentry-element":"Image","data-sentry-source-file":"app-sidebar.tsx"})})}),(0,a.jsx)("div",{className:`mt-3 ${"collapsed"===n?"hidden":"block"}`,children:(0,a.jsx)(eb,{"data-sentry-element":"RoleIndicator","data-sentry-source-file":"app-sidebar.tsx"})})]}),(0,a.jsx)(l.Yv,{className:"overflow-x-hidden","data-sentry-element":"SidebarContent","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsxs)(l.Cn,{"data-sentry-element":"SidebarGroup","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(l.jj,{"data-sentry-element":"SidebarGroupLabel","data-sentry-source-file":"app-sidebar.tsx",children:"Overview"}),(0,a.jsx)(l.wZ,{className:"gap-0","data-sentry-element":"SidebarMenu","data-sentry-source-file":"app-sidebar.tsx",children:p.map(t=>{let r=t.icon?em[t.icon]:em.logo;return t?.items&&t?.items?.length>0?(0,a.jsx)(s,{asChild:!0,defaultOpen:t.isActive,className:"group/collapsible",children:(0,a.jsxs)(l.FX,{children:[(0,a.jsx)(o,{asChild:!0,children:(0,a.jsxs)(l.Uj,{tooltip:t.title,isActive:t.items?.some(t=>e===t.url),className:"py-3 px-4 text-base font-medium h-auto",children:[t.icon&&(0,a.jsx)(r,{}),(0,a.jsx)("span",{children:t.title}),(0,a.jsx)(v.A,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),(0,a.jsx)(i,{children:(0,a.jsx)(l.q9,{children:t.items?.map(t=>(0,a.jsx)(l.Fg,{children:(0,a.jsx)(l.Cp,{asChild:!0,isActive:e===t.url,className:"py-2 px-6 text-sm font-medium",children:(0,a.jsx)(j(),{href:t.url,children:(0,a.jsx)("span",{children:t.title})})})},t.title))})})]})},t.title):(0,a.jsx)(l.FX,{children:(0,a.jsx)(l.Uj,{asChild:!0,tooltip:t.title,isActive:e===t.url,className:"py-3 px-4 text-base font-medium h-auto",children:(0,a.jsxs)(j(),{href:t.url,children:[(0,a.jsx)(r,{}),(0,a.jsx)("span",{children:t.title})]})})},t.title)})})]})}),(0,a.jsx)(l.CG,{"data-sentry-element":"SidebarFooter","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsx)(l.wZ,{"data-sentry-element":"SidebarMenu","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsx)(l.FX,{"data-sentry-element":"SidebarMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsxs)(d.rI,{"data-sentry-element":"DropdownMenu","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(d.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsxs)(l.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground py-3 px-4 text-base font-medium h-auto","data-sentry-element":"SidebarMenuButton","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(c.M,{className:"h-8 w-8 rounded-lg",showInfo:!0,"data-sentry-element":"UserAvatarProfile","data-sentry-source-file":"app-sidebar.tsx"}),(0,a.jsx)(em.chevronRight,{className:"ml-auto size-4","data-sentry-element":"Icons.chevronRight","data-sentry-source-file":"app-sidebar.tsx"})]})}),(0,a.jsxs)(d.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:"bottom",align:"end",sideOffset:4,"data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(d.lp,{className:"p-0 font-normal","data-sentry-element":"DropdownMenuLabel","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsx)("div",{className:"px-1 py-1.5",children:(0,a.jsx)(c.M,{className:"h-8 w-8 rounded-lg",showInfo:!0,"data-sentry-element":"UserAvatarProfile","data-sentry-source-file":"app-sidebar.tsx"})})}),(0,a.jsx)(d.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"app-sidebar.tsx"}),(0,a.jsxs)(d.I,{"data-sentry-element":"DropdownMenuGroup","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsxs)(d._2,{onClick:()=>r.push("/dashboard/profile"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(em.user,{className:"mr-2 h-4 w-4","data-sentry-element":"Icons.user","data-sentry-source-file":"app-sidebar.tsx"}),"Profile"]}),(0,a.jsxs)(d._2,{"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(em.billing,{className:"mr-2 h-4 w-4","data-sentry-element":"Icons.billing","data-sentry-source-file":"app-sidebar.tsx"}),"Billing"]}),(0,a.jsxs)(d._2,{"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(em.help,{className:"mr-2 h-4 w-4","data-sentry-element":"Icons.help","data-sentry-source-file":"app-sidebar.tsx"}),"Notifications"]})]}),(0,a.jsx)(d.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"app-sidebar.tsx"}),(0,a.jsxs)(d._2,{onClick:()=>{b.qs.removeUser(),window.location.href="/auth/sign-in"},"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4","data-sentry-element":"LogOut","data-sentry-source-file":"app-sidebar.tsx"}),(0,a.jsx)("span",{children:"Logout"})]})]})]})})})}),(0,a.jsx)(l.jM,{"data-sentry-element":"SidebarRail","data-sentry-source-file":"app-sidebar.tsx"})]})}g.A},45188:(e,t,r)=>{r.d(t,{default:()=>n});var a=r(1472);(0,a.registerClientReference)(function(){throw Error("Attempted to call company() from the server but company is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\app-sidebar.tsx","company");let n=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\app-sidebar.tsx","default")},48071:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(93491),n=r(76328),s=r(21372);function o(){let[e,t]=(0,a.useState)(null),[r,o]=(0,a.useState)(!0),i=(0,s.useRouter)();return{user:e,loading:r,signOut:()=>{n.qs.removeUser(),t(null),i.push("/auth/sign-in")}}}},59672:(e,t,r)=>{r.d(t,{p:()=>s});var a=r(91754);r(93491);var n=r(82233);function s({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},63457:(e,t,r)=>{r.d(t,{Avatar:()=>o,AvatarFallback:()=>d,AvatarImage:()=>i});var a=r(91754);r(93491);var n=r(17112),s=r(82233);function o({className:e,...t}){return(0,a.jsx)(n.bL,{"data-slot":"avatar",className:(0,s.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t,"data-sentry-element":"AvatarPrimitive.Root","data-sentry-component":"Avatar","data-sentry-source-file":"avatar.tsx"})}function i({className:e,...t}){return(0,a.jsx)(n._V,{"data-slot":"avatar-image",className:(0,s.cn)("aspect-square size-full",e),...t,"data-sentry-element":"AvatarPrimitive.Image","data-sentry-component":"AvatarImage","data-sentry-source-file":"avatar.tsx"})}function d({className:e,...t}){return(0,a.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,s.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t,"data-sentry-element":"AvatarPrimitive.Fallback","data-sentry-component":"AvatarFallback","data-sentry-source-file":"avatar.tsx"})}},67999:(e,t,r)=>{r.d(t,{default:()=>a});let a=(0,r(1472).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\layout\\header.tsx","default")},70067:(e,t,r)=>{r.d(t,{E:()=>s});var a=r(91754),n=r(82233);function s({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,n.cn)("bg-accent animate-pulse rounded-md",e),...t,"data-sentry-component":"Skeleton","data-sentry-source-file":"skeleton.tsx"})}},76328:(e,t,r)=>{r.d(t,{WG:()=>n,cl:()=>o,qs:()=>a});let a={setUser:e=>{},getUser:()=>null,removeUser:()=>{},isAuthenticated:()=>null!==a.getUser(),hasRole:e=>{let t=a.getUser();return t?.role===e},isSuperAdmin:()=>a.hasRole("super_admin"),isTeacher:()=>a.hasRole("teacher"),isStudent:()=>a.hasRole("student")},n=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},s=()=>{let e=a.getUser();return e||null},o=e=>{let t=s();return t&&t.role===e?t:null}},84436:(e,t,r)=>{r.d(t,{Bx:()=>w,Yv:()=>I,CG:()=>D,Cn:()=>k,jj:()=>E,Gh:()=>M,SidebarInset:()=>N,wZ:()=>R,Uj:()=>_,FX:()=>G,q9:()=>P,Cp:()=>U,Fg:()=>L,SidebarProvider:()=>j,jM:()=>S,x2:()=>C,cL:()=>A});var a=r(91754),n=r(93491),s=r(16435),o=r(25758),i=r(78855),d=r(82233),l=r(56682);r(59672),r(92501);var c=r(18227),u=r(31619);function m({...e}){return(0,a.jsx)(c.bL,{"data-slot":"sheet",...e,"data-sentry-element":"SheetPrimitive.Root","data-sentry-component":"Sheet","data-sentry-source-file":"sheet.tsx"})}function p({...e}){return(0,a.jsx)(c.ZL,{"data-slot":"sheet-portal",...e,"data-sentry-element":"SheetPrimitive.Portal","data-sentry-component":"SheetPortal","data-sentry-source-file":"sheet.tsx"})}function f({className:e,...t}){return(0,a.jsx)(c.hJ,{"data-slot":"sheet-overlay",className:(0,d.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"SheetPrimitive.Overlay","data-sentry-component":"SheetOverlay","data-sentry-source-file":"sheet.tsx"})}function b({className:e,children:t,side:r="right",...n}){return(0,a.jsxs)(p,{"data-sentry-element":"SheetPortal","data-sentry-component":"SheetContent","data-sentry-source-file":"sheet.tsx",children:[(0,a.jsx)(f,{"data-sentry-element":"SheetOverlay","data-sentry-source-file":"sheet.tsx"}),(0,a.jsxs)(c.UC,{"data-slot":"sheet-content",className:(0,d.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...n,"data-sentry-element":"SheetPrimitive.Content","data-sentry-source-file":"sheet.tsx",children:[t,(0,a.jsxs)(c.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none","data-sentry-element":"SheetPrimitive.Close","data-sentry-source-file":"sheet.tsx",children:[(0,a.jsx)(u.A,{className:"size-4","data-sentry-element":"XIcon","data-sentry-source-file":"sheet.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sheet-header",className:(0,d.cn)("flex flex-col gap-1.5 p-4",e),...t,"data-sentry-component":"SheetHeader","data-sentry-source-file":"sheet.tsx"})}function x({className:e,...t}){return(0,a.jsx)(c.hE,{"data-slot":"sheet-title",className:(0,d.cn)("text-foreground font-semibold",e),...t,"data-sentry-element":"SheetPrimitive.Title","data-sentry-component":"SheetTitle","data-sentry-source-file":"sheet.tsx"})}function g({className:e,...t}){return(0,a.jsx)(c.VY,{"data-slot":"sheet-description",className:(0,d.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"SheetPrimitive.Description","data-sentry-component":"SheetDescription","data-sentry-source-file":"sheet.tsx"})}r(70067);var v=r(2457);let y=n.createContext(null);function A(){let e=n.useContext(y);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function j({defaultOpen:e=!0,open:t,onOpenChange:r,className:s,style:o,children:i,...l}){let c=function(){let[e,t]=n.useState(void 0);return n.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),r=()=>{t(window.innerWidth<768)};return e.addEventListener("change",r),t(window.innerWidth<768),()=>e.removeEventListener("change",r)},[]),!!e}(),[u,m]=n.useState(!1),[p,f]=n.useState(e),b=t??p,h=n.useCallback(e=>{let t="function"==typeof e?e(b):e;r?r(t):f(t),document.cookie=`sidebar_state=${t}; path=/; max-age=604800`},[r,b]),x=n.useCallback(()=>c?m(e=>!e):h(e=>!e),[c,h,m]);n.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),x())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[x]);let g=b?"expanded":"collapsed",A=n.useMemo(()=>({state:g,open:b,setOpen:h,isMobile:c,openMobile:u,setOpenMobile:m,toggleSidebar:x}),[g,b,h,c,u,m,x]);return(0,a.jsx)(y.Provider,{value:A,"data-sentry-element":"SidebarContext.Provider","data-sentry-component":"SidebarProvider","data-sentry-source-file":"sidebar.tsx",children:(0,a.jsx)(v.Bc,{delayDuration:0,"data-sentry-element":"TooltipProvider","data-sentry-source-file":"sidebar.tsx",children:(0,a.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...o},className:(0,d.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",s),...l,children:i})})})}function w({side:e="left",variant:t="sidebar",collapsible:r="offcanvas",className:n,children:s,...o}){let{isMobile:i,state:l,openMobile:c,setOpenMobile:u}=A();return"none"===r?(0,a.jsx)("div",{"data-slot":"sidebar",className:(0,d.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n),...o,children:s}):i?(0,a.jsx)(m,{open:c,onOpenChange:u,...o,children:(0,a.jsxs)(b,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:[(0,a.jsxs)(h,{className:"sr-only",children:[(0,a.jsx)(x,{children:"Sidebar"}),(0,a.jsx)(g,{children:"Displays the mobile sidebar."})]}),(0,a.jsx)("div",{className:"flex h-full w-full flex-col",children:s})]})}):(0,a.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":"collapsed"===l?r:"","data-variant":t,"data-side":e,"data-slot":"sidebar","data-sentry-component":"Sidebar","data-sentry-source-file":"sidebar.tsx",children:[(0,a.jsx)("div",{"data-slot":"sidebar-gap",className:(0,d.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,a.jsx)("div",{"data-slot":"sidebar-container",className:(0,d.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...o,children:(0,a.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:s})})]})}function C({className:e,onClick:t,...r}){let{toggleSidebar:n}=A();return(0,a.jsxs)(l.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,d.cn)("size-7",e),onClick:e=>{t?.(e),n()},...r,"data-sentry-element":"Button","data-sentry-component":"SidebarTrigger","data-sentry-source-file":"sidebar.tsx",children:[(0,a.jsx)(i.A,{"data-sentry-element":"PanelLeftIcon","data-sentry-source-file":"sidebar.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function S({className:e,...t}){let{toggleSidebar:r}=A();return(0,a.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:(0,d.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t,"data-sentry-component":"SidebarRail","data-sentry-source-file":"sidebar.tsx"})}function N({className:e,...t}){return(0,a.jsx)("main",{"data-slot":"sidebar-inset",className:(0,d.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t,"data-sentry-component":"SidebarInset","data-sentry-source-file":"sidebar.tsx"})}function M({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,d.cn)("flex flex-col gap-2 p-2",e),...t,"data-sentry-component":"SidebarHeader","data-sentry-source-file":"sidebar.tsx"})}function D({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,d.cn)("flex flex-col gap-2 p-2",e),...t,"data-sentry-component":"SidebarFooter","data-sentry-source-file":"sidebar.tsx"})}function I({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,d.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t,"data-sentry-component":"SidebarContent","data-sentry-source-file":"sidebar.tsx"})}function k({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,d.cn)("relative flex w-full min-w-0 flex-col p-2",e),...t,"data-sentry-component":"SidebarGroup","data-sentry-source-file":"sidebar.tsx"})}function E({className:e,asChild:t=!1,...r}){let n=t?s.DX:"div";return(0,a.jsx)(n,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,d.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...r,"data-sentry-element":"Comp","data-sentry-component":"SidebarGroupLabel","data-sentry-source-file":"sidebar.tsx"})}function R({className:e,...t}){return(0,a.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,d.cn)("flex w-full min-w-0 flex-col gap-1",e),...t,"data-sentry-component":"SidebarMenu","data-sentry-source-file":"sidebar.tsx"})}function G({className:e,...t}){return(0,a.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,d.cn)("group/menu-item relative",e),...t,"data-sentry-component":"SidebarMenuItem","data-sentry-source-file":"sidebar.tsx"})}let T=(0,o.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 cursor-pointer",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function _({asChild:e=!1,isActive:t=!1,variant:r="default",size:n="default",tooltip:o,className:i,...l}){let c=e?s.DX:"button",{isMobile:u,state:m}=A(),p=(0,a.jsx)(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":n,"data-active":t,className:(0,d.cn)(T({variant:r,size:n}),i),...l});return o?("string"==typeof o&&(o={children:o}),(0,a.jsxs)(v.m_,{"data-sentry-element":"Tooltip","data-sentry-component":"SidebarMenuButton","data-sentry-source-file":"sidebar.tsx",children:[(0,a.jsx)(v.k$,{asChild:!0,"data-sentry-element":"TooltipTrigger","data-sentry-source-file":"sidebar.tsx",children:p}),(0,a.jsx)(v.ZI,{side:"right",align:"center",hidden:"collapsed"!==m||u,...o,"data-sentry-element":"TooltipContent","data-sentry-source-file":"sidebar.tsx"})]})):p}function P({className:e,...t}){return(0,a.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,d.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t,"data-sentry-component":"SidebarMenuSub","data-sentry-source-file":"sidebar.tsx"})}function L({className:e,...t}){return(0,a.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,d.cn)("group/menu-sub-item relative",e),...t,"data-sentry-component":"SidebarMenuSubItem","data-sentry-source-file":"sidebar.tsx"})}function U({asChild:e=!1,size:t="md",isActive:r=!1,className:n,...o}){let i=e?s.DX:"a";return(0,a.jsx)(i,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":t,"data-active":r,className:(0,d.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===t&&"text-xs","md"===t&&"text-sm","group-data-[collapsible=icon]:hidden",n),...o,"data-sentry-element":"Comp","data-sentry-component":"SidebarMenuSubButton","data-sentry-source-file":"sidebar.tsx"})}},86778:(e,t,r)=>{r.d(t,{M:()=>o});var a=r(91754),n=r(63457),s=r(48071);function o({className:e,showInfo:t=!1}){let{user:r}=(0,s.A)();return r?(0,a.jsxs)("div",{className:"flex items-center gap-2","data-sentry-component":"UserAvatarProfile","data-sentry-source-file":"user-avatar-profile.tsx",children:[(0,a.jsxs)(n.Avatar,{className:e,"data-sentry-element":"Avatar","data-sentry-source-file":"user-avatar-profile.tsx",children:[(0,a.jsx)(n.AvatarImage,{src:`https://ui-avatars.com/api/?name=${r.name}&background=random`,alt:r.name,"data-sentry-element":"AvatarImage","data-sentry-source-file":"user-avatar-profile.tsx"}),(0,a.jsx)(n.AvatarFallback,{className:"rounded-lg","data-sentry-element":"AvatarFallback","data-sentry-source-file":"user-avatar-profile.tsx",children:r.name?.slice(0,2)?.toUpperCase()||"U"})]}),t&&(0,a.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,a.jsx)("span",{className:"truncate font-semibold",children:r.name}),(0,a.jsx)("span",{className:"truncate text-xs",children:r.email})]})]}):(0,a.jsx)(n.Avatar,{className:e,children:(0,a.jsx)(n.AvatarFallback,{children:"G"})})}},92501:(e,t,r)=>{r.d(t,{Separator:()=>i});var a=r(91754),n=r(93491),s=r(6002),o=r(82233);let i=n.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...n},i)=>(0,a.jsx)(s.b,{ref:i,decorative:r,orientation:t,className:(0,o.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...n}));i.displayName=s.b.displayName},92681:(e,t,r)=>{r.d(t,{I:()=>c,SQ:()=>l,_2:()=>u,hO:()=>m,lp:()=>p,mB:()=>f,rI:()=>i,ty:()=>d});var a=r(91754);r(93491);var n=r(20809),s=r(87435),o=r(82233);function i({...e}){return(0,a.jsx)(n.bL,{"data-slot":"dropdown-menu",...e,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function d({...e}){return(0,a.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...e,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function l({className:e,sideOffset:t=4,...r}){return(0,a.jsx)(n.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,a.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c({...e}){return(0,a.jsx)(n.YJ,{"data-slot":"dropdown-menu-group",...e,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u({className:e,inset:t,variant:r="default",...s}){return(0,a.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground focus:[&_svg:not([class*='text-'])]:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function m({className:e,children:t,checked:r,...i}){return(0,a.jsxs)(n.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),checked:r,...i,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,a.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,a.jsx)(s.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),t]})}function p({className:e,inset:t,...r}){return(0,a.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function f({className:e,...t}){return(0,a.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",e),...t,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},93400:(e,t,r)=>{r.d(t,{Cn:()=>n,iI:()=>a});let a=e=>[{title:"Available Courses",url:"/courses",icon:"searchList",isActive:e.startsWith("/courses"),shortcut:["a","c"],items:[]},{title:"My Courses",url:"/my-courses",icon:"graduationCap",isActive:e.startsWith("/my-courses"),shortcut:["m","c"],items:[]}],n=a("")}};
//# sourceMappingURL=8634.js.map