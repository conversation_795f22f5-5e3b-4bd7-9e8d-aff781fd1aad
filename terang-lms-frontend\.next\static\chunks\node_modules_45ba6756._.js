(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@lezer/lr/dist/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ContextTracker": (()=>ContextTracker),
    "ExternalTokenizer": (()=>ExternalTokenizer),
    "InputStream": (()=>InputStream),
    "LRParser": (()=>LRParser),
    "LocalTokenGroup": (()=>LocalTokenGroup),
    "Stack": (()=>Stack)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/common/dist/index.js [app-client] (ecmascript)");
;
/**
A parse stack. These are used internally by the parser to track
parsing progress. They also provide some properties and methods
that external code such as a tokenizer can use to get information
about the parse state.
*/ class Stack {
    /**
    @internal
    */ constructor(/**
    The parse that this stack is part of @internal
    */ p, /**
    Holds state, input pos, buffer index triplets for all but the
    top state @internal
    */ stack, /**
    The current parse state @internal
    */ state, // The position at which the next reduce should take place. This
    // can be less than `this.pos` when skipped expressions have been
    // added to the stack (which should be moved outside of the next
    // reduction)
    /**
    @internal
    */ reducePos, /**
    The input position up to which this stack has parsed.
    */ pos, /**
    The dynamic score of the stack, including dynamic precedence
    and error-recovery penalties
    @internal
    */ score, // The output buffer. Holds (type, start, end, size) quads
    // representing nodes created by the parser, where `size` is
    // amount of buffer array entries covered by this node.
    /**
    @internal
    */ buffer, // The base offset of the buffer. When stacks are split, the split
    // instance shared the buffer history with its parent up to
    // `bufferBase`, which is the absolute offset (including the
    // offset of previous splits) into the buffer at which this stack
    // starts writing.
    /**
    @internal
    */ bufferBase, /**
    @internal
    */ curContext, /**
    @internal
    */ lookAhead = 0, // A parent stack from which this was split off, if any. This is
    // set up so that it always points to a stack that has some
    // additional buffer content, never to a stack with an equal
    // `bufferBase`.
    /**
    @internal
    */ parent){
        this.p = p;
        this.stack = stack;
        this.state = state;
        this.reducePos = reducePos;
        this.pos = pos;
        this.score = score;
        this.buffer = buffer;
        this.bufferBase = bufferBase;
        this.curContext = curContext;
        this.lookAhead = lookAhead;
        this.parent = parent;
    }
    /**
    @internal
    */ toString() {
        return `[${this.stack.filter((_, i)=>i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? "!" + this.score : ""}`;
    }
    // Start an empty stack
    /**
    @internal
    */ static start(p, state, pos = 0) {
        let cx = p.parser.context;
        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);
    }
    /**
    The stack's current [context](#lr.ContextTracker) value, if
    any. Its type will depend on the context tracker's type
    parameter, or it will be `null` if there is no context
    tracker.
    */ get context() {
        return this.curContext ? this.curContext.context : null;
    }
    // Push a state onto the stack, tracking its start position as well
    // as the buffer base at that point.
    /**
    @internal
    */ pushState(state, start) {
        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);
        this.state = state;
    }
    // Apply a reduce action
    /**
    @internal
    */ reduce(action) {
        var _a;
        let depth = action >> 19 /* Action.ReduceDepthShift */ , type = action & 65535 /* Action.ValueMask */ ;
        let { parser } = this.p;
        let lookaheadRecord = this.reducePos < this.pos - 25 /* Lookahead.Margin */ ;
        if (lookaheadRecord) this.setLookAhead(this.pos);
        let dPrec = parser.dynamicPrecedence(type);
        if (dPrec) this.score += dPrec;
        if (depth == 0) {
            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);
            // Zero-depth reductions are a special case—they add stuff to
            // the stack without popping anything off.
            if (type < parser.minRepeatTerm) this.storeNode(type, this.reducePos, this.reducePos, lookaheadRecord ? 8 : 4, true);
            this.reduceContext(type, this.reducePos);
            return;
        }
        // Find the base index into `this.stack`, content after which will
        // be dropped. Note that with `StayFlag` reductions we need to
        // consume two extra frames (the dummy parent node for the skipped
        // expression and the state that we'll be staying in, which should
        // be moved to `this.state`).
        let base = this.stack.length - (depth - 1) * 3 - (action & 262144 /* Action.StayFlag */  ? 6 : 0);
        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;
        // This is a kludge to try and detect overly deep left-associative
        // trees, which will not increase the parse stack depth and thus
        // won't be caught by the regular stack-depth limit check.
        if (size >= 2000 /* Recover.MinBigReduction */  && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {
            if (start == this.p.lastBigReductionStart) {
                this.p.bigReductionCount++;
                this.p.lastBigReductionSize = size;
            } else if (this.p.lastBigReductionSize < size) {
                this.p.bigReductionCount = 1;
                this.p.lastBigReductionStart = start;
                this.p.lastBigReductionSize = size;
            }
        }
        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;
        // Store normal terms or `R -> R R` repeat reductions
        if (type < parser.minRepeatTerm || action & 131072 /* Action.RepeatFlag */ ) {
            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */ ) ? this.pos : this.reducePos;
            this.storeNode(type, start, pos, count + 4, true);
        }
        if (action & 262144 /* Action.StayFlag */ ) {
            this.state = this.stack[base];
        } else {
            let baseStateID = this.stack[base - 3];
            this.state = parser.getGoto(baseStateID, type, true);
        }
        while(this.stack.length > base)this.stack.pop();
        this.reduceContext(type, start);
    }
    // Shift a value into the buffer
    /**
    @internal
    */ storeNode(term, start, end, size = 4, mustSink = false) {
        if (term == 0 /* Term.Err */  && (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {
            // Try to omit/merge adjacent error nodes
            let cur = this, top = this.buffer.length;
            if (top == 0 && cur.parent) {
                top = cur.bufferBase - cur.parent.bufferBase;
                cur = cur.parent;
            }
            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */  && cur.buffer[top - 1] > -1) {
                if (start == end) return;
                if (cur.buffer[top - 2] >= start) {
                    cur.buffer[top - 2] = end;
                    return;
                }
            }
        }
        if (!mustSink || this.pos == end) {
            this.buffer.push(term, start, end, size);
        } else {
            let index = this.buffer.length;
            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */ ) {
                let mustMove = false;
                for(let scan = index; scan > 0 && this.buffer[scan - 2] > end; scan -= 4){
                    if (this.buffer[scan - 1] >= 0) {
                        mustMove = true;
                        break;
                    }
                }
                if (mustMove) while(index > 0 && this.buffer[index - 2] > end){
                    // Move this record forward
                    this.buffer[index] = this.buffer[index - 4];
                    this.buffer[index + 1] = this.buffer[index - 3];
                    this.buffer[index + 2] = this.buffer[index - 2];
                    this.buffer[index + 3] = this.buffer[index - 1];
                    index -= 4;
                    if (size > 4) size -= 4;
                }
            }
            this.buffer[index] = term;
            this.buffer[index + 1] = start;
            this.buffer[index + 2] = end;
            this.buffer[index + 3] = size;
        }
    }
    // Apply a shift action
    /**
    @internal
    */ shift(action, type, start, end) {
        if (action & 131072 /* Action.GotoFlag */ ) {
            this.pushState(action & 65535 /* Action.ValueMask */ , this.pos);
        } else if ((action & 262144 /* Action.StayFlag */ ) == 0) {
            let nextState = action, { parser } = this.p;
            if (end > this.pos || type <= parser.maxNode) {
                this.pos = end;
                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */ )) this.reducePos = end;
            }
            this.pushState(nextState, start);
            this.shiftContext(type, start);
            if (type <= parser.maxNode) this.buffer.push(type, start, end, 4);
        } else {
            this.pos = end;
            this.shiftContext(type, start);
            if (type <= this.p.parser.maxNode) this.buffer.push(type, start, end, 4);
        }
    }
    // Apply an action
    /**
    @internal
    */ apply(action, next, nextStart, nextEnd) {
        if (action & 65536 /* Action.ReduceFlag */ ) this.reduce(action);
        else this.shift(action, next, nextStart, nextEnd);
    }
    // Add a prebuilt (reused) node into the buffer.
    /**
    @internal
    */ useNode(value, next) {
        let index = this.p.reused.length - 1;
        if (index < 0 || this.p.reused[index] != value) {
            this.p.reused.push(value);
            index++;
        }
        let start = this.pos;
        this.reducePos = this.pos = start + value.length;
        this.pushState(next, start);
        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */ );
        if (this.curContext) this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));
    }
    // Split the stack. Due to the buffer sharing and the fact
    // that `this.stack` tends to stay quite shallow, this isn't very
    // expensive.
    /**
    @internal
    */ split() {
        let parent = this;
        let off = parent.buffer.length;
        // Because the top of the buffer (after this.pos) may be mutated
        // to reorder reductions and skipped tokens, and shared buffers
        // should be immutable, this copies any outstanding skipped tokens
        // to the new buffer, and puts the base pointer before them.
        while(off > 0 && parent.buffer[off - 2] > parent.reducePos)off -= 4;
        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;
        // Make sure parent points to an actual parent with content, if there is such a parent.
        while(parent && base == parent.bufferBase)parent = parent.parent;
        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);
    }
    // Try to recover from an error by 'deleting' (ignoring) one token.
    /**
    @internal
    */ recoverByDelete(next, nextEnd) {
        let isNode = next <= this.p.parser.maxNode;
        if (isNode) this.storeNode(next, this.pos, nextEnd, 4);
        this.storeNode(0 /* Term.Err */ , this.pos, nextEnd, isNode ? 8 : 4);
        this.pos = this.reducePos = nextEnd;
        this.score -= 190 /* Recover.Delete */ ;
    }
    /**
    Check if the given term would be able to be shifted (optionally
    after some reductions) on this stack. This can be useful for
    external tokenizers that want to make sure they only provide a
    given token when it applies.
    */ canShift(term) {
        for(let sim = new SimulatedStack(this);;){
            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */ ) || this.p.parser.hasAction(sim.state, term);
            if (action == 0) return false;
            if ((action & 65536 /* Action.ReduceFlag */ ) == 0) return true;
            sim.reduce(action);
        }
    }
    // Apply up to Recover.MaxNext recovery actions that conceptually
    // inserts some missing token or rule.
    /**
    @internal
    */ recoverByInsert(next) {
        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */ ) return [];
        let nextStates = this.p.parser.nextStates(this.state);
        if (nextStates.length > 4 /* Recover.MaxNext */  << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */ ) {
            let best = [];
            for(let i = 0, s; i < nextStates.length; i += 2){
                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next)) best.push(nextStates[i], s);
            }
            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */ ) for(let i = 0; best.length < 4 /* Recover.MaxNext */  << 1 && i < nextStates.length; i += 2){
                let s = nextStates[i + 1];
                if (!best.some((v, i)=>i & 1 && v == s)) best.push(nextStates[i], s);
            }
            nextStates = best;
        }
        let result = [];
        for(let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */ ; i += 2){
            let s = nextStates[i + 1];
            if (s == this.state) continue;
            let stack = this.split();
            stack.pushState(s, this.pos);
            stack.storeNode(0 /* Term.Err */ , stack.pos, stack.pos, 4, true);
            stack.shiftContext(nextStates[i], this.pos);
            stack.reducePos = this.pos;
            stack.score -= 200 /* Recover.Insert */ ;
            result.push(stack);
        }
        return result;
    }
    // Force a reduce, if possible. Return false if that can't
    // be done.
    /**
    @internal
    */ forceReduce() {
        let { parser } = this.p;
        let reduce = parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */ );
        if ((reduce & 65536 /* Action.ReduceFlag */ ) == 0) return false;
        if (!parser.validAction(this.state, reduce)) {
            let depth = reduce >> 19 /* Action.ReduceDepthShift */ , term = reduce & 65535 /* Action.ValueMask */ ;
            let target = this.stack.length - depth * 3;
            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {
                let backup = this.findForcedReduction();
                if (backup == null) return false;
                reduce = backup;
            }
            this.storeNode(0 /* Term.Err */ , this.pos, this.pos, 4, true);
            this.score -= 100 /* Recover.Reduce */ ;
        }
        this.reducePos = this.pos;
        this.reduce(reduce);
        return true;
    }
    /**
    Try to scan through the automaton to find some kind of reduction
    that can be applied. Used when the regular ForcedReduce field
    isn't a valid action. @internal
    */ findForcedReduction() {
        let { parser } = this.p, seen = [];
        let explore = (state, depth)=>{
            if (seen.includes(state)) return;
            seen.push(state);
            return parser.allActions(state, (action)=>{
                if (action & (262144 /* Action.StayFlag */  | 131072 /* Action.GotoFlag */ )) ;
                else if (action & 65536 /* Action.ReduceFlag */ ) {
                    let rDepth = (action >> 19 /* Action.ReduceDepthShift */ ) - depth;
                    if (rDepth > 1) {
                        let term = action & 65535 /* Action.ValueMask */ , target = this.stack.length - rDepth * 3;
                        if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0) return rDepth << 19 /* Action.ReduceDepthShift */  | 65536 /* Action.ReduceFlag */  | term;
                    }
                } else {
                    let found = explore(action, depth + 1);
                    if (found != null) return found;
                }
            });
        };
        return explore(this.state, 0);
    }
    /**
    @internal
    */ forceAll() {
        while(!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */ )){
            if (!this.forceReduce()) {
                this.storeNode(0 /* Term.Err */ , this.pos, this.pos, 4, true);
                break;
            }
        }
        return this;
    }
    /**
    Check whether this state has no further actions (assumed to be a direct descendant of the
    top state, since any other states must be able to continue
    somehow). @internal
    */ get deadEnd() {
        if (this.stack.length != 3) return false;
        let { parser } = this.p;
        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */ )] == 65535 /* Seq.End */  && !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */ );
    }
    /**
    Restart the stack (put it back in its start state). Only safe
    when this.stack.length == 3 (state is directly below the top
    state). @internal
    */ restart() {
        this.storeNode(0 /* Term.Err */ , this.pos, this.pos, 4, true);
        this.state = this.stack[0];
        this.stack.length = 0;
    }
    /**
    @internal
    */ sameState(other) {
        if (this.state != other.state || this.stack.length != other.stack.length) return false;
        for(let i = 0; i < this.stack.length; i += 3)if (this.stack[i] != other.stack[i]) return false;
        return true;
    }
    /**
    Get the parser used by this stack.
    */ get parser() {
        return this.p.parser;
    }
    /**
    Test whether a given dialect (by numeric ID, as exported from
    the terms file) is enabled.
    */ dialectEnabled(dialectID) {
        return this.p.parser.dialect.flags[dialectID];
    }
    shiftContext(term, start) {
        if (this.curContext) this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));
    }
    reduceContext(term, start) {
        if (this.curContext) this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));
    }
    /**
    @internal
    */ emitContext() {
        let last = this.buffer.length - 1;
        if (last < 0 || this.buffer[last] != -3) this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);
    }
    /**
    @internal
    */ emitLookAhead() {
        let last = this.buffer.length - 1;
        if (last < 0 || this.buffer[last] != -4) this.buffer.push(this.lookAhead, this.pos, this.pos, -4);
    }
    updateContext(context) {
        if (context != this.curContext.context) {
            let newCx = new StackContext(this.curContext.tracker, context);
            if (newCx.hash != this.curContext.hash) this.emitContext();
            this.curContext = newCx;
        }
    }
    /**
    @internal
    */ setLookAhead(lookAhead) {
        if (lookAhead > this.lookAhead) {
            this.emitLookAhead();
            this.lookAhead = lookAhead;
        }
    }
    /**
    @internal
    */ close() {
        if (this.curContext && this.curContext.tracker.strict) this.emitContext();
        if (this.lookAhead > 0) this.emitLookAhead();
    }
}
class StackContext {
    constructor(tracker, context){
        this.tracker = tracker;
        this.context = context;
        this.hash = tracker.strict ? tracker.hash(context) : 0;
    }
}
// Used to cheaply run some reductions to scan ahead without mutating
// an entire stack
class SimulatedStack {
    constructor(start){
        this.start = start;
        this.state = start.state;
        this.stack = start.stack;
        this.base = this.stack.length;
    }
    reduce(action) {
        let term = action & 65535 /* Action.ValueMask */ , depth = action >> 19 /* Action.ReduceDepthShift */ ;
        if (depth == 0) {
            if (this.stack == this.start.stack) this.stack = this.stack.slice();
            this.stack.push(this.state, 0, 0);
            this.base += 3;
        } else {
            this.base -= (depth - 1) * 3;
        }
        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);
        this.state = goto;
    }
}
// This is given to `Tree.build` to build a buffer, and encapsulates
// the parent-stack-walking necessary to read the nodes.
class StackBufferCursor {
    constructor(stack, pos, index){
        this.stack = stack;
        this.pos = pos;
        this.index = index;
        this.buffer = stack.buffer;
        if (this.index == 0) this.maybeNext();
    }
    static create(stack, pos = stack.bufferBase + stack.buffer.length) {
        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);
    }
    maybeNext() {
        let next = this.stack.parent;
        if (next != null) {
            this.index = this.stack.bufferBase - next.bufferBase;
            this.stack = next;
            this.buffer = next.buffer;
        }
    }
    get id() {
        return this.buffer[this.index - 4];
    }
    get start() {
        return this.buffer[this.index - 3];
    }
    get end() {
        return this.buffer[this.index - 2];
    }
    get size() {
        return this.buffer[this.index - 1];
    }
    next() {
        this.index -= 4;
        this.pos -= 4;
        if (this.index == 0) this.maybeNext();
    }
    fork() {
        return new StackBufferCursor(this.stack, this.pos, this.index);
    }
}
// See lezer-generator/src/encode.ts for comments about the encoding
// used here
function decodeArray(input, Type = Uint16Array) {
    if (typeof input != "string") return input;
    let array = null;
    for(let pos = 0, out = 0; pos < input.length;){
        let value = 0;
        for(;;){
            let next = input.charCodeAt(pos++), stop = false;
            if (next == 126 /* Encode.BigValCode */ ) {
                value = 65535 /* Encode.BigVal */ ;
                break;
            }
            if (next >= 92 /* Encode.Gap2 */ ) next--;
            if (next >= 34 /* Encode.Gap1 */ ) next--;
            let digit = next - 32 /* Encode.Start */ ;
            if (digit >= 46 /* Encode.Base */ ) {
                digit -= 46 /* Encode.Base */ ;
                stop = true;
            }
            value += digit;
            if (stop) break;
            value *= 46 /* Encode.Base */ ;
        }
        if (array) array[out++] = value;
        else array = new Type(value);
    }
    return array;
}
class CachedToken {
    constructor(){
        this.start = -1;
        this.value = -1;
        this.end = -1;
        this.extended = -1;
        this.lookAhead = 0;
        this.mask = 0;
        this.context = 0;
    }
}
const nullToken = new CachedToken;
/**
[Tokenizers](#lr.ExternalTokenizer) interact with the input
through this interface. It presents the input as a stream of
characters, tracking lookahead and hiding the complexity of
[ranges](#common.Parser.parse^ranges) from tokenizer code.
*/ class InputStream {
    /**
    @internal
    */ constructor(/**
    @internal
    */ input, /**
    @internal
    */ ranges){
        this.input = input;
        this.ranges = ranges;
        /**
        @internal
        */ this.chunk = "";
        /**
        @internal
        */ this.chunkOff = 0;
        /**
        Backup chunk
        */ this.chunk2 = "";
        this.chunk2Pos = 0;
        /**
        The character code of the next code unit in the input, or -1
        when the stream is at the end of the input.
        */ this.next = -1;
        /**
        @internal
        */ this.token = nullToken;
        this.rangeIndex = 0;
        this.pos = this.chunkPos = ranges[0].from;
        this.range = ranges[0];
        this.end = ranges[ranges.length - 1].to;
        this.readNext();
    }
    /**
    @internal
    */ resolveOffset(offset, assoc) {
        let range = this.range, index = this.rangeIndex;
        let pos = this.pos + offset;
        while(pos < range.from){
            if (!index) return null;
            let next = this.ranges[--index];
            pos -= range.from - next.to;
            range = next;
        }
        while(assoc < 0 ? pos > range.to : pos >= range.to){
            if (index == this.ranges.length - 1) return null;
            let next = this.ranges[++index];
            pos += next.from - range.to;
            range = next;
        }
        return pos;
    }
    /**
    @internal
    */ clipPos(pos) {
        if (pos >= this.range.from && pos < this.range.to) return pos;
        for (let range of this.ranges)if (range.to > pos) return Math.max(pos, range.from);
        return this.end;
    }
    /**
    Look at a code unit near the stream position. `.peek(0)` equals
    `.next`, `.peek(-1)` gives you the previous character, and so
    on.
    
    Note that looking around during tokenizing creates dependencies
    on potentially far-away content, which may reduce the
    effectiveness incremental parsing—when looking forward—or even
    cause invalid reparses when looking backward more than 25 code
    units, since the library does not track lookbehind.
    */ peek(offset) {
        let idx = this.chunkOff + offset, pos, result;
        if (idx >= 0 && idx < this.chunk.length) {
            pos = this.pos + offset;
            result = this.chunk.charCodeAt(idx);
        } else {
            let resolved = this.resolveOffset(offset, 1);
            if (resolved == null) return -1;
            pos = resolved;
            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {
                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);
            } else {
                let i = this.rangeIndex, range = this.range;
                while(range.to <= pos)range = this.ranges[++i];
                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);
                if (pos + this.chunk2.length > range.to) this.chunk2 = this.chunk2.slice(0, range.to - pos);
                result = this.chunk2.charCodeAt(0);
            }
        }
        if (pos >= this.token.lookAhead) this.token.lookAhead = pos + 1;
        return result;
    }
    /**
    Accept a token. By default, the end of the token is set to the
    current stream position, but you can pass an offset (relative to
    the stream position) to change that.
    */ acceptToken(token, endOffset = 0) {
        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;
        if (end == null || end < this.token.start) throw new RangeError("Token end out of bounds");
        this.token.value = token;
        this.token.end = end;
    }
    /**
    Accept a token ending at a specific given position.
    */ acceptTokenTo(token, endPos) {
        this.token.value = token;
        this.token.end = endPos;
    }
    getChunk() {
        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {
            let { chunk, chunkPos } = this;
            this.chunk = this.chunk2;
            this.chunkPos = this.chunk2Pos;
            this.chunk2 = chunk;
            this.chunk2Pos = chunkPos;
            this.chunkOff = this.pos - this.chunkPos;
        } else {
            this.chunk2 = this.chunk;
            this.chunk2Pos = this.chunkPos;
            let nextChunk = this.input.chunk(this.pos);
            let end = this.pos + nextChunk.length;
            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;
            this.chunkPos = this.pos;
            this.chunkOff = 0;
        }
    }
    readNext() {
        if (this.chunkOff >= this.chunk.length) {
            this.getChunk();
            if (this.chunkOff == this.chunk.length) return this.next = -1;
        }
        return this.next = this.chunk.charCodeAt(this.chunkOff);
    }
    /**
    Move the stream forward N (defaults to 1) code units. Returns
    the new value of [`next`](#lr.InputStream.next).
    */ advance(n = 1) {
        this.chunkOff += n;
        while(this.pos + n >= this.range.to){
            if (this.rangeIndex == this.ranges.length - 1) return this.setDone();
            n -= this.range.to - this.pos;
            this.range = this.ranges[++this.rangeIndex];
            this.pos = this.range.from;
        }
        this.pos += n;
        if (this.pos >= this.token.lookAhead) this.token.lookAhead = this.pos + 1;
        return this.readNext();
    }
    setDone() {
        this.pos = this.chunkPos = this.end;
        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];
        this.chunk = "";
        return this.next = -1;
    }
    /**
    @internal
    */ reset(pos, token) {
        if (token) {
            this.token = token;
            token.start = pos;
            token.lookAhead = pos + 1;
            token.value = token.extended = -1;
        } else {
            this.token = nullToken;
        }
        if (this.pos != pos) {
            this.pos = pos;
            if (pos == this.end) {
                this.setDone();
                return this;
            }
            while(pos < this.range.from)this.range = this.ranges[--this.rangeIndex];
            while(pos >= this.range.to)this.range = this.ranges[++this.rangeIndex];
            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {
                this.chunkOff = pos - this.chunkPos;
            } else {
                this.chunk = "";
                this.chunkOff = 0;
            }
            this.readNext();
        }
        return this;
    }
    /**
    @internal
    */ read(from, to) {
        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length) return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);
        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length) return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);
        if (from >= this.range.from && to <= this.range.to) return this.input.read(from, to);
        let result = "";
        for (let r of this.ranges){
            if (r.from >= to) break;
            if (r.to > from) result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));
        }
        return result;
    }
}
/**
@internal
*/ class TokenGroup {
    constructor(data, id){
        this.data = data;
        this.id = id;
    }
    token(input, stack) {
        let { parser } = stack.p;
        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);
    }
}
TokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;
/**
@hide
*/ class LocalTokenGroup {
    constructor(data, precTable, elseToken){
        this.precTable = precTable;
        this.elseToken = elseToken;
        this.data = typeof data == "string" ? decodeArray(data) : data;
    }
    token(input, stack) {
        let start = input.pos, skipped = 0;
        for(;;){
            let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);
            readToken(this.data, input, stack, 0, this.data, this.precTable);
            if (input.token.value > -1) break;
            if (this.elseToken == null) return;
            if (!atEof) skipped++;
            if (nextPos == null) break;
            input.reset(nextPos, input.token);
        }
        if (skipped) {
            input.reset(start, input.token);
            input.acceptToken(this.elseToken, skipped);
        }
    }
}
LocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;
/**
`@external tokens` declarations in the grammar should resolve to
an instance of this class.
*/ class ExternalTokenizer {
    /**
    Create a tokenizer. The first argument is the function that,
    given an input stream, scans for the types of tokens it
    recognizes at the stream's position, and calls
    [`acceptToken`](#lr.InputStream.acceptToken) when it finds
    one.
    */ constructor(/**
    @internal
    */ token, options = {}){
        this.token = token;
        this.contextual = !!options.contextual;
        this.fallback = !!options.fallback;
        this.extend = !!options.extend;
    }
}
// Tokenizer data is stored a big uint16 array containing, for each
// state:
//
//  - A group bitmask, indicating what token groups are reachable from
//    this state, so that paths that can only lead to tokens not in
//    any of the current groups can be cut off early.
//
//  - The position of the end of the state's sequence of accepting
//    tokens
//
//  - The number of outgoing edges for the state
//
//  - The accepting tokens, as (token id, group mask) pairs
//
//  - The outgoing edges, as (start character, end character, state
//    index) triples, with end character being exclusive
//
// This function interprets that data, running through a stream as
// long as new states with the a matching group mask can be reached,
// and updating `input.token` when it matches a token.
function readToken(data, input, stack, group, precTable, precOffset) {
    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;
    scan: for(;;){
        if ((groupMask & data[state]) == 0) break;
        let accEnd = data[state + 1];
        // Check whether this state can lead to a token in the current group
        // Accept tokens in this state, possibly overwriting
        // lower-precedence / shorter tokens
        for(let i = state + 3; i < accEnd; i += 2)if ((data[i + 1] & groupMask) > 0) {
            let term = data[i];
            if (dialect.allows(term) && (input.token.value == -1 || input.token.value == term || overrides(term, input.token.value, precTable, precOffset))) {
                input.acceptToken(term);
                break;
            }
        }
        let next = input.next, low = 0, high = data[state + 2];
        // Special case for EOF
        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */ ) {
            state = data[accEnd + high * 3 - 1];
            continue scan;
        }
        // Do a binary search on the state's edges
        for(; low < high;){
            let mid = low + high >> 1;
            let index = accEnd + mid + (mid << 1);
            let from = data[index], to = data[index + 1] || 0x10000;
            if (next < from) high = mid;
            else if (next >= to) low = mid + 1;
            else {
                state = data[index + 2];
                input.advance();
                continue scan;
            }
        }
        break;
    }
}
function findOffset(data, start, term) {
    for(let i = start, next; (next = data[i]) != 65535 /* Seq.End */ ; i++)if (next == term) return i - start;
    return -1;
}
function overrides(token, prev, tableData, tableOffset) {
    let iPrev = findOffset(tableData, tableOffset, prev);
    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;
}
// Environment variable used to control console output
const verbose = typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] != "undefined" && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env && /\bparse\b/.test(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.LOG);
let stackIDs = null;
function cutAt(tree, pos, side) {
    let cursor = tree.cursor(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IterMode"].IncludeAnonymous);
    cursor.moveTo(pos);
    for(;;){
        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos))) for(;;){
            if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError) return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Lookahead.Margin */ )) : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Lookahead.Margin */ ));
            if (side < 0 ? cursor.prevSibling() : cursor.nextSibling()) break;
            if (!cursor.parent()) return side < 0 ? 0 : tree.length;
        }
    }
}
class FragmentCursor {
    constructor(fragments, nodeSet){
        this.fragments = fragments;
        this.nodeSet = nodeSet;
        this.i = 0;
        this.fragment = null;
        this.safeFrom = -1;
        this.safeTo = -1;
        this.trees = [];
        this.start = [];
        this.index = [];
        this.nextFragment();
    }
    nextFragment() {
        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];
        if (fr) {
            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;
            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;
            while(this.trees.length){
                this.trees.pop();
                this.start.pop();
                this.index.pop();
            }
            this.trees.push(fr.tree);
            this.start.push(-fr.offset);
            this.index.push(0);
            this.nextStart = this.safeFrom;
        } else {
            this.nextStart = 1e9;
        }
    }
    // `pos` must be >= any previously given `pos` for this cursor
    nodeAt(pos) {
        if (pos < this.nextStart) return null;
        while(this.fragment && this.safeTo <= pos)this.nextFragment();
        if (!this.fragment) return null;
        for(;;){
            let last = this.trees.length - 1;
            if (last < 0) {
                this.nextFragment();
                return null;
            }
            let top = this.trees[last], index = this.index[last];
            if (index == top.children.length) {
                this.trees.pop();
                this.start.pop();
                this.index.pop();
                continue;
            }
            let next = top.children[index];
            let start = this.start[last] + top.positions[index];
            if (start > pos) {
                this.nextStart = start;
                return null;
            }
            if (next instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tree"]) {
                if (start == pos) {
                    if (start < this.safeFrom) return null;
                    let end = start + next.length;
                    if (end <= this.safeTo) {
                        let lookAhead = next.prop(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeProp"].lookAhead);
                        if (!lookAhead || end + lookAhead < this.fragment.to) return next;
                    }
                }
                this.index[last]++;
                if (start + next.length >= Math.max(this.safeFrom, pos)) {
                    this.trees.push(next);
                    this.start.push(start);
                    this.index.push(0);
                }
            } else {
                this.index[last]++;
                this.nextStart = start + next.length;
            }
        }
    }
}
class TokenCache {
    constructor(parser, stream){
        this.stream = stream;
        this.tokens = [];
        this.mainToken = null;
        this.actions = [];
        this.tokens = parser.tokenizers.map((_)=>new CachedToken);
    }
    getActions(stack) {
        let actionIndex = 0;
        let main = null;
        let { parser } = stack.p, { tokenizers } = parser;
        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */ );
        let context = stack.curContext ? stack.curContext.hash : 0;
        let lookAhead = 0;
        for(let i = 0; i < tokenizers.length; i++){
            if ((1 << i & mask) == 0) continue;
            let tokenizer = tokenizers[i], token = this.tokens[i];
            if (main && !tokenizer.fallback) continue;
            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {
                this.updateCachedToken(token, tokenizer, stack);
                token.mask = mask;
                token.context = context;
            }
            if (token.lookAhead > token.end + 25 /* Lookahead.Margin */ ) lookAhead = Math.max(token.lookAhead, lookAhead);
            if (token.value != 0 /* Term.Err */ ) {
                let startIndex = actionIndex;
                if (token.extended > -1) actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);
                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);
                if (!tokenizer.extend) {
                    main = token;
                    if (actionIndex > startIndex) break;
                }
            }
        }
        while(this.actions.length > actionIndex)this.actions.pop();
        if (lookAhead) stack.setLookAhead(lookAhead);
        if (!main && stack.pos == this.stream.end) {
            main = new CachedToken;
            main.value = stack.p.parser.eofTerm;
            main.start = main.end = stack.pos;
            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);
        }
        this.mainToken = main;
        return this.actions;
    }
    getMainToken(stack) {
        if (this.mainToken) return this.mainToken;
        let main = new CachedToken, { pos, p } = stack;
        main.start = pos;
        main.end = Math.min(pos + 1, p.stream.end);
        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */ ;
        return main;
    }
    updateCachedToken(token, tokenizer, stack) {
        let start = this.stream.clipPos(stack.pos);
        tokenizer.token(this.stream.reset(start, token), stack);
        if (token.value > -1) {
            let { parser } = stack.p;
            for(let i = 0; i < parser.specialized.length; i++)if (parser.specialized[i] == token.value) {
                let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);
                if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {
                    if ((result & 1) == 0 /* Specialize.Specialize */ ) token.value = result >> 1;
                    else token.extended = result >> 1;
                    break;
                }
            }
        } else {
            token.value = 0 /* Term.Err */ ;
            token.end = this.stream.clipPos(start + 1);
        }
    }
    putAction(action, token, end, index) {
        // Don't add duplicate actions
        for(let i = 0; i < index; i += 3)if (this.actions[i] == action) return index;
        this.actions[index++] = action;
        this.actions[index++] = token;
        this.actions[index++] = end;
        return index;
    }
    addActions(stack, token, end, index) {
        let { state } = stack, { parser } = stack.p, { data } = parser;
        for(let set = 0; set < 2; set++){
            for(let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */  : 1 /* ParseState.Actions */ );; i += 3){
                if (data[i] == 65535 /* Seq.End */ ) {
                    if (data[i + 1] == 1 /* Seq.Next */ ) {
                        i = pair(data, i + 2);
                    } else {
                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */ ) index = this.putAction(pair(data, i + 2), token, end, index);
                        break;
                    }
                }
                if (data[i] == token) index = this.putAction(pair(data, i + 1), token, end, index);
            }
        }
        return index;
    }
}
class Parse {
    constructor(parser, input, fragments, ranges){
        this.parser = parser;
        this.input = input;
        this.ranges = ranges;
        this.recovering = 0;
        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧
        this.minStackPos = 0;
        this.reused = [];
        this.stoppedAt = null;
        this.lastBigReductionStart = -1;
        this.lastBigReductionSize = 0;
        this.bigReductionCount = 0;
        this.stream = new InputStream(input, ranges);
        this.tokens = new TokenCache(parser, this.stream);
        this.topTerm = parser.top[1];
        let { from } = ranges[0];
        this.stacks = [
            Stack.start(this, parser.top[0], from)
        ];
        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4 ? new FragmentCursor(fragments, parser.nodeSet) : null;
    }
    get parsedPos() {
        return this.minStackPos;
    }
    // Move the parser forward. This will process all parse stacks at
    // `this.pos` and try to advance them to a further position. If no
    // stack for such a position is found, it'll start error-recovery.
    //
    // When the parse is finished, this will return a syntax tree. When
    // not, it returns `null`.
    advance() {
        let stacks = this.stacks, pos = this.minStackPos;
        // This will hold stacks beyond `pos`.
        let newStacks = this.stacks = [];
        let stopped, stoppedTokens;
        // If a large amount of reductions happened with the same start
        // position, force the stack out of that production in order to
        // avoid creating a tree too deep to recurse through.
        // (This is an ugly kludge, because unfortunately there is no
        // straightforward, cheap way to check for this happening, due to
        // the history of reductions only being available in an
        // expensive-to-access format in the stack buffers.)
        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */  && stacks.length == 1) {
            let [s] = stacks;
            while(s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart){}
            this.bigReductionCount = this.lastBigReductionSize = 0;
        }
        // Keep advancing any stacks at `pos` until they either move
        // forward or can't be advanced. Gather stacks that can't be
        // advanced further in `stopped`.
        for(let i = 0; i < stacks.length; i++){
            let stack = stacks[i];
            for(;;){
                this.tokens.mainToken = null;
                if (stack.pos > pos) {
                    newStacks.push(stack);
                } else if (this.advanceStack(stack, newStacks, stacks)) {
                    continue;
                } else {
                    if (!stopped) {
                        stopped = [];
                        stoppedTokens = [];
                    }
                    stopped.push(stack);
                    let tok = this.tokens.getMainToken(stack);
                    stoppedTokens.push(tok.value, tok.end);
                }
                break;
            }
        }
        if (!newStacks.length) {
            let finished = stopped && findFinished(stopped);
            if (finished) {
                if (verbose) console.log("Finish with " + this.stackID(finished));
                return this.stackToTree(finished);
            }
            if (this.parser.strict) {
                if (verbose && stopped) console.log("Stuck with token " + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : "none"));
                throw new SyntaxError("No parse at " + pos);
            }
            if (!this.recovering) this.recovering = 5 /* Rec.Distance */ ;
        }
        if (this.recovering && stopped) {
            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0] : this.runRecovery(stopped, stoppedTokens, newStacks);
            if (finished) {
                if (verbose) console.log("Force-finish " + this.stackID(finished));
                return this.stackToTree(finished.forceAll());
            }
        }
        if (this.recovering) {
            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */ ;
            if (newStacks.length > maxRemaining) {
                newStacks.sort((a, b)=>b.score - a.score);
                while(newStacks.length > maxRemaining)newStacks.pop();
            }
            if (newStacks.some((s)=>s.reducePos > pos)) this.recovering--;
        } else if (newStacks.length > 1) {
            // Prune stacks that are in the same state, or that have been
            // running without splitting for a while, to avoid getting stuck
            // with multiple successful stacks running endlessly on.
            outer: for(let i = 0; i < newStacks.length - 1; i++){
                let stack = newStacks[i];
                for(let j = i + 1; j < newStacks.length; j++){
                    let other = newStacks[j];
                    if (stack.sameState(other) || stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */  && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */ ) {
                        if ((stack.score - other.score || stack.buffer.length - other.buffer.length) > 0) {
                            newStacks.splice(j--, 1);
                        } else {
                            newStacks.splice(i--, 1);
                            continue outer;
                        }
                    }
                }
            }
            if (newStacks.length > 12 /* Rec.MaxStackCount */ ) newStacks.splice(12 /* Rec.MaxStackCount */ , newStacks.length - 12 /* Rec.MaxStackCount */ );
        }
        this.minStackPos = newStacks[0].pos;
        for(let i = 1; i < newStacks.length; i++)if (newStacks[i].pos < this.minStackPos) this.minStackPos = newStacks[i].pos;
        return null;
    }
    stopAt(pos) {
        if (this.stoppedAt != null && this.stoppedAt < pos) throw new RangeError("Can't move stoppedAt forward");
        this.stoppedAt = pos;
    }
    // Returns an updated version of the given stack, or null if the
    // stack can't advance normally. When `split` and `stacks` are
    // given, stacks split off by ambiguous operations will be pushed to
    // `split`, or added to `stacks` if they move `pos` forward.
    advanceStack(stack, stacks, split) {
        let start = stack.pos, { parser } = this;
        let base = verbose ? this.stackID(stack) + " -> " : "";
        if (this.stoppedAt != null && start > this.stoppedAt) return stack.forceReduce() ? stack : null;
        if (this.fragments) {
            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;
            for(let cached = this.fragments.nodeAt(start); cached;){
                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;
                if (match > -1 && cached.length && (!strictCx || (cached.prop(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeProp"].contextHash) || 0) == cxHash)) {
                    stack.useNode(cached, match);
                    if (verbose) console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);
                    return true;
                }
                if (!(cached instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tree"]) || cached.children.length == 0 || cached.positions[0] > 0) break;
                let inner = cached.children[0];
                if (inner instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tree"] && cached.positions[0] == 0) cached = inner;
                else break;
            }
        }
        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */ );
        if (defaultReduce > 0) {
            stack.reduce(defaultReduce);
            if (verbose) console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */ )})`);
            return true;
        }
        if (stack.stack.length >= 8400 /* Rec.CutDepth */ ) {
            while(stack.stack.length > 6000 /* Rec.CutTo */  && stack.forceReduce()){}
        }
        let actions = this.tokens.getActions(stack);
        for(let i = 0; i < actions.length;){
            let action = actions[i++], term = actions[i++], end = actions[i++];
            let last = i == actions.length || !split;
            let localStack = last ? stack : stack.split();
            let main = this.tokens.mainToken;
            localStack.apply(action, term, main ? main.start : localStack.pos, end);
            if (verbose) console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */ ) == 0 ? "shift" : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */ )}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? "" : ", split"})`);
            if (last) return true;
            else if (localStack.pos > start) stacks.push(localStack);
            else split.push(localStack);
        }
        return false;
    }
    // Advance a given stack forward as far as it will go. Returns the
    // (possibly updated) stack if it got stuck, or null if it moved
    // forward and was given to `pushStackDedup`.
    advanceFully(stack, newStacks) {
        let pos = stack.pos;
        for(;;){
            if (!this.advanceStack(stack, null, null)) return false;
            if (stack.pos > pos) {
                pushStackDedup(stack, newStacks);
                return true;
            }
        }
    }
    runRecovery(stacks, tokens, newStacks) {
        let finished = null, restarted = false;
        for(let i = 0; i < stacks.length; i++){
            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];
            let base = verbose ? this.stackID(stack) + " -> " : "";
            if (stack.deadEnd) {
                if (restarted) continue;
                restarted = true;
                stack.restart();
                if (verbose) console.log(base + this.stackID(stack) + " (restarted)");
                let done = this.advanceFully(stack, newStacks);
                if (done) continue;
            }
            let force = stack.split(), forceBase = base;
            for(let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */ ; j++){
                if (verbose) console.log(forceBase + this.stackID(force) + " (via force-reduce)");
                let done = this.advanceFully(force, newStacks);
                if (done) break;
                if (verbose) forceBase = this.stackID(force) + " -> ";
            }
            for (let insert of stack.recoverByInsert(token)){
                if (verbose) console.log(base + this.stackID(insert) + " (via recover-insert)");
                this.advanceFully(insert, newStacks);
            }
            if (this.stream.end > stack.pos) {
                if (tokenEnd == stack.pos) {
                    tokenEnd++;
                    token = 0 /* Term.Err */ ;
                }
                stack.recoverByDelete(token, tokenEnd);
                if (verbose) console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);
                pushStackDedup(stack, newStacks);
            } else if (!finished || finished.score < stack.score) {
                finished = stack;
            }
        }
        return finished;
    }
    // Convert the stack's buffer to a syntax tree.
    stackToTree(stack) {
        stack.close();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tree"].build({
            buffer: StackBufferCursor.create(stack),
            nodeSet: this.parser.nodeSet,
            topID: this.topTerm,
            maxBufferLength: this.parser.bufferLength,
            reused: this.reused,
            start: this.ranges[0].from,
            length: stack.pos - this.ranges[0].from,
            minRepeatType: this.parser.minRepeatTerm
        });
    }
    stackID(stack) {
        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);
        if (!id) stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));
        return id + stack;
    }
}
function pushStackDedup(stack, newStacks) {
    for(let i = 0; i < newStacks.length; i++){
        let other = newStacks[i];
        if (other.pos == stack.pos && other.sameState(stack)) {
            if (newStacks[i].score < stack.score) newStacks[i] = stack;
            return;
        }
    }
    newStacks.push(stack);
}
class Dialect {
    constructor(source, flags, disabled){
        this.source = source;
        this.flags = flags;
        this.disabled = disabled;
    }
    allows(term) {
        return !this.disabled || this.disabled[term] == 0;
    }
}
const id = (x)=>x;
/**
Context trackers are used to track stateful context (such as
indentation in the Python grammar, or parent elements in the XML
grammar) needed by external tokenizers. You declare them in a
grammar file as `@context exportName from "module"`.

Context values should be immutable, and can be updated (replaced)
on shift or reduce actions.

The export used in a `@context` declaration should be of this
type.
*/ class ContextTracker {
    /**
    Define a context tracker.
    */ constructor(spec){
        this.start = spec.start;
        this.shift = spec.shift || id;
        this.reduce = spec.reduce || id;
        this.reuse = spec.reuse || id;
        this.hash = spec.hash || (()=>0);
        this.strict = spec.strict !== false;
    }
}
/**
Holds the parse tables for a given grammar, as generated by
`lezer-generator`, and provides [methods](#common.Parser) to parse
content with.
*/ class LRParser extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Parser"] {
    /**
    @internal
    */ constructor(spec){
        super();
        /**
        @internal
        */ this.wrappers = [];
        if (spec.version != 14 /* File.Version */ ) throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */ })`);
        let nodeNames = spec.nodeNames.split(" ");
        this.minRepeatTerm = nodeNames.length;
        for(let i = 0; i < spec.repeatNodeCount; i++)nodeNames.push("");
        let topTerms = Object.keys(spec.topRules).map((r)=>spec.topRules[r][1]);
        let nodeProps = [];
        for(let i = 0; i < nodeNames.length; i++)nodeProps.push([]);
        function setProp(nodeID, prop, value) {
            nodeProps[nodeID].push([
                prop,
                prop.deserialize(String(value))
            ]);
        }
        if (spec.nodeProps) for (let propSpec of spec.nodeProps){
            let prop = propSpec[0];
            if (typeof prop == "string") prop = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeProp"][prop];
            for(let i = 1; i < propSpec.length;){
                let next = propSpec[i++];
                if (next >= 0) {
                    setProp(next, prop, propSpec[i++]);
                } else {
                    let value = propSpec[i + -next];
                    for(let j = -next; j > 0; j--)setProp(propSpec[i++], prop, value);
                    i++;
                }
            }
        }
        this.nodeSet = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeSet"](nodeNames.map((name, i)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeType"].define({
                name: i >= this.minRepeatTerm ? undefined : name,
                id: i,
                props: nodeProps[i],
                top: topTerms.indexOf(i) > -1,
                error: i == 0,
                skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1
            })));
        if (spec.propSources) this.nodeSet = this.nodeSet.extend(...spec.propSources);
        this.strict = false;
        this.bufferLength = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DefaultBufferLength"];
        let tokenArray = decodeArray(spec.tokenData);
        this.context = spec.context;
        this.specializerSpecs = spec.specialized || [];
        this.specialized = new Uint16Array(this.specializerSpecs.length);
        for(let i = 0; i < this.specializerSpecs.length; i++)this.specialized[i] = this.specializerSpecs[i].term;
        this.specializers = this.specializerSpecs.map(getSpecializer);
        this.states = decodeArray(spec.states, Uint32Array);
        this.data = decodeArray(spec.stateData);
        this.goto = decodeArray(spec.goto);
        this.maxTerm = spec.maxTerm;
        this.tokenizers = spec.tokenizers.map((value)=>typeof value == "number" ? new TokenGroup(tokenArray, value) : value);
        this.topRules = spec.topRules;
        this.dialects = spec.dialects || {};
        this.dynamicPrecedences = spec.dynamicPrecedences || null;
        this.tokenPrecTable = spec.tokenPrec;
        this.termNames = spec.termNames || null;
        this.maxNode = this.nodeSet.types.length - 1;
        this.dialect = this.parseDialect();
        this.top = this.topRules[Object.keys(this.topRules)[0]];
    }
    createParse(input, fragments, ranges) {
        let parse = new Parse(this, input, fragments, ranges);
        for (let w of this.wrappers)parse = w(parse, input, fragments, ranges);
        return parse;
    }
    /**
    Get a goto table entry @internal
    */ getGoto(state, term, loose = false) {
        let table = this.goto;
        if (term >= table[0]) return -1;
        for(let pos = table[term + 1];;){
            let groupTag = table[pos++], last = groupTag & 1;
            let target = table[pos++];
            if (last && loose) return target;
            for(let end = pos + (groupTag >> 1); pos < end; pos++)if (table[pos] == state) return target;
            if (last) return -1;
        }
    }
    /**
    Check if this state has an action for a given terminal @internal
    */ hasAction(state, terminal) {
        let data = this.data;
        for(let set = 0; set < 2; set++){
            for(let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */  : 1 /* ParseState.Actions */ ), next;; i += 3){
                if ((next = data[i]) == 65535 /* Seq.End */ ) {
                    if (data[i + 1] == 1 /* Seq.Next */ ) next = data[i = pair(data, i + 2)];
                    else if (data[i + 1] == 2 /* Seq.Other */ ) return pair(data, i + 2);
                    else break;
                }
                if (next == terminal || next == 0 /* Term.Err */ ) return pair(data, i + 1);
            }
        }
        return 0;
    }
    /**
    @internal
    */ stateSlot(state, slot) {
        return this.states[state * 6 /* ParseState.Size */  + slot];
    }
    /**
    @internal
    */ stateFlag(state, flag) {
        return (this.stateSlot(state, 0 /* ParseState.Flags */ ) & flag) > 0;
    }
    /**
    @internal
    */ validAction(state, action) {
        return !!this.allActions(state, (a)=>a == action ? true : null);
    }
    /**
    @internal
    */ allActions(state, action) {
        let deflt = this.stateSlot(state, 4 /* ParseState.DefaultReduce */ );
        let result = deflt ? action(deflt) : undefined;
        for(let i = this.stateSlot(state, 1 /* ParseState.Actions */ ); result == null; i += 3){
            if (this.data[i] == 65535 /* Seq.End */ ) {
                if (this.data[i + 1] == 1 /* Seq.Next */ ) i = pair(this.data, i + 2);
                else break;
            }
            result = action(pair(this.data, i + 1));
        }
        return result;
    }
    /**
    Get the states that can follow this one through shift actions or
    goto jumps. @internal
    */ nextStates(state) {
        let result = [];
        for(let i = this.stateSlot(state, 1 /* ParseState.Actions */ );; i += 3){
            if (this.data[i] == 65535 /* Seq.End */ ) {
                if (this.data[i + 1] == 1 /* Seq.Next */ ) i = pair(this.data, i + 2);
                else break;
            }
            if ((this.data[i + 2] & 65536 /* Action.ReduceFlag */  >> 16) == 0) {
                let value = this.data[i + 1];
                if (!result.some((v, i)=>i & 1 && v == value)) result.push(this.data[i], value);
            }
        }
        return result;
    }
    /**
    Configure the parser. Returns a new parser instance that has the
    given settings modified. Settings not provided in `config` are
    kept from the original parser.
    */ configure(config) {
        // Hideous reflection-based kludge to make it easy to create a
        // slightly modified copy of a parser.
        let copy = Object.assign(Object.create(LRParser.prototype), this);
        if (config.props) copy.nodeSet = this.nodeSet.extend(...config.props);
        if (config.top) {
            let info = this.topRules[config.top];
            if (!info) throw new RangeError(`Invalid top rule name ${config.top}`);
            copy.top = info;
        }
        if (config.tokenizers) copy.tokenizers = this.tokenizers.map((t)=>{
            let found = config.tokenizers.find((r)=>r.from == t);
            return found ? found.to : t;
        });
        if (config.specializers) {
            copy.specializers = this.specializers.slice();
            copy.specializerSpecs = this.specializerSpecs.map((s, i)=>{
                let found = config.specializers.find((r)=>r.from == s.external);
                if (!found) return s;
                let spec = Object.assign(Object.assign({}, s), {
                    external: found.to
                });
                copy.specializers[i] = getSpecializer(spec);
                return spec;
            });
        }
        if (config.contextTracker) copy.context = config.contextTracker;
        if (config.dialect) copy.dialect = this.parseDialect(config.dialect);
        if (config.strict != null) copy.strict = config.strict;
        if (config.wrap) copy.wrappers = copy.wrappers.concat(config.wrap);
        if (config.bufferLength != null) copy.bufferLength = config.bufferLength;
        return copy;
    }
    /**
    Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)
    are registered for this parser.
    */ hasWrappers() {
        return this.wrappers.length > 0;
    }
    /**
    Returns the name associated with a given term. This will only
    work for all terms when the parser was generated with the
    `--names` option. By default, only the names of tagged terms are
    stored.
    */ getName(term) {
        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);
    }
    /**
    The eof term id is always allocated directly after the node
    types. @internal
    */ get eofTerm() {
        return this.maxNode + 1;
    }
    /**
    The type of top node produced by the parser.
    */ get topNode() {
        return this.nodeSet.types[this.top[1]];
    }
    /**
    @internal
    */ dynamicPrecedence(term) {
        let prec = this.dynamicPrecedences;
        return prec == null ? 0 : prec[term] || 0;
    }
    /**
    @internal
    */ parseDialect(dialect) {
        let values = Object.keys(this.dialects), flags = values.map(()=>false);
        if (dialect) for (let part of dialect.split(" ")){
            let id = values.indexOf(part);
            if (id >= 0) flags[id] = true;
        }
        let disabled = null;
        for(let i = 0; i < values.length; i++)if (!flags[i]) {
            for(let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */ ;)(disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;
        }
        return new Dialect(dialect, flags, disabled);
    }
    /**
    Used by the output of the parser generator. Not available to
    user code. @hide
    */ static deserialize(spec) {
        return new LRParser(spec);
    }
}
function pair(data, off) {
    return data[off] | data[off + 1] << 16;
}
function findFinished(stacks) {
    let best = null;
    for (let stack of stacks){
        let stopped = stack.p.stoppedAt;
        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) && stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */ ) && (!best || best.score < stack.score)) best = stack;
    }
    return best;
}
function getSpecializer(spec) {
    if (spec.external) {
        let mask = spec.extend ? 1 /* Specialize.Extend */  : 0 /* Specialize.Specialize */ ;
        return (value, stack)=>spec.external(value, stack) << 1 | mask;
    }
    return spec.get;
}
;
}}),
"[project]/node_modules/@lezer/html/dist/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "configureNesting": (()=>configureNesting),
    "parser": (()=>parser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/lr/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/highlight/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/common/dist/index.js [app-client] (ecmascript)");
;
;
;
// This file was generated by lezer-generator. You probably shouldn't edit it.
const scriptText = 54, StartCloseScriptTag = 1, styleText = 55, StartCloseStyleTag = 2, textareaText = 56, StartCloseTextareaTag = 3, EndTag = 4, SelfClosingEndTag = 5, StartTag = 6, StartScriptTag = 7, StartStyleTag = 8, StartTextareaTag = 9, StartSelfClosingTag = 10, StartCloseTag = 11, NoMatchStartCloseTag = 12, MismatchedStartCloseTag = 13, missingCloseTag = 57, IncompleteCloseTag = 14, commentContent$1 = 58, Element = 20, TagName = 22, Attribute = 23, AttributeName = 24, AttributeValue = 26, UnquotedAttributeValue = 27, ScriptText = 28, StyleText = 31, TextareaText = 34, OpenTag = 36, CloseTag = 37, Dialect_noMatch = 0, Dialect_selfClosing = 1;
/* Hand-written tokenizers for HTML. */ const selfClosers = {
    area: true,
    base: true,
    br: true,
    col: true,
    command: true,
    embed: true,
    frame: true,
    hr: true,
    img: true,
    input: true,
    keygen: true,
    link: true,
    meta: true,
    param: true,
    source: true,
    track: true,
    wbr: true,
    menuitem: true
};
const implicitlyClosed = {
    dd: true,
    li: true,
    optgroup: true,
    option: true,
    p: true,
    rp: true,
    rt: true,
    tbody: true,
    td: true,
    tfoot: true,
    th: true,
    tr: true
};
const closeOnOpen = {
    dd: {
        dd: true,
        dt: true
    },
    dt: {
        dd: true,
        dt: true
    },
    li: {
        li: true
    },
    option: {
        option: true,
        optgroup: true
    },
    optgroup: {
        optgroup: true
    },
    p: {
        address: true,
        article: true,
        aside: true,
        blockquote: true,
        dir: true,
        div: true,
        dl: true,
        fieldset: true,
        footer: true,
        form: true,
        h1: true,
        h2: true,
        h3: true,
        h4: true,
        h5: true,
        h6: true,
        header: true,
        hgroup: true,
        hr: true,
        menu: true,
        nav: true,
        ol: true,
        p: true,
        pre: true,
        section: true,
        table: true,
        ul: true
    },
    rp: {
        rp: true,
        rt: true
    },
    rt: {
        rp: true,
        rt: true
    },
    tbody: {
        tbody: true,
        tfoot: true
    },
    td: {
        td: true,
        th: true
    },
    tfoot: {
        tbody: true
    },
    th: {
        td: true,
        th: true
    },
    thead: {
        tbody: true,
        tfoot: true
    },
    tr: {
        tr: true
    }
};
function nameChar(ch) {
    return ch == 45 || ch == 46 || ch == 58 || ch >= 65 && ch <= 90 || ch == 95 || ch >= 97 && ch <= 122 || ch >= 161;
}
function isSpace(ch) {
    return ch == 9 || ch == 10 || ch == 13 || ch == 32;
}
let cachedName = null, cachedInput = null, cachedPos = 0;
function tagNameAfter(input, offset) {
    let pos = input.pos + offset;
    if (cachedPos == pos && cachedInput == input) return cachedName;
    let next = input.peek(offset);
    while(isSpace(next))next = input.peek(++offset);
    let name = "";
    for(;;){
        if (!nameChar(next)) break;
        name += String.fromCharCode(next);
        next = input.peek(++offset);
    }
    // Undefined to signal there's a <? or <!, null for just missing
    cachedInput = input;
    cachedPos = pos;
    return cachedName = name ? name.toLowerCase() : next == question || next == bang ? undefined : null;
}
const lessThan = 60, greaterThan = 62, slash = 47, question = 63, bang = 33, dash = 45;
function ElementContext(name, parent) {
    this.name = name;
    this.parent = parent;
}
const startTagTerms = [
    StartTag,
    StartSelfClosingTag,
    StartScriptTag,
    StartStyleTag,
    StartTextareaTag
];
const elementContext = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContextTracker"]({
    start: null,
    shift (context, term, stack, input) {
        return startTagTerms.indexOf(term) > -1 ? new ElementContext(tagNameAfter(input, 1) || "", context) : context;
    },
    reduce (context, term) {
        return term == Element && context ? context.parent : context;
    },
    reuse (context, node, stack, input) {
        let type = node.type.id;
        return type == StartTag || type == OpenTag ? new ElementContext(tagNameAfter(input, 1) || "", context) : context;
    },
    strict: false
});
const tagStart = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"]((input, stack)=>{
    if (input.next != lessThan) {
        // End of file, close any open tags
        if (input.next < 0 && stack.context) input.acceptToken(missingCloseTag);
        return;
    }
    input.advance();
    let close = input.next == slash;
    if (close) input.advance();
    let name = tagNameAfter(input, 0);
    if (name === undefined) return;
    if (!name) return input.acceptToken(close ? IncompleteCloseTag : StartTag);
    let parent = stack.context ? stack.context.name : null;
    if (close) {
        if (name == parent) return input.acceptToken(StartCloseTag);
        if (parent && implicitlyClosed[parent]) return input.acceptToken(missingCloseTag, -2);
        if (stack.dialectEnabled(Dialect_noMatch)) return input.acceptToken(NoMatchStartCloseTag);
        for(let cx = stack.context; cx; cx = cx.parent)if (cx.name == name) return;
        input.acceptToken(MismatchedStartCloseTag);
    } else {
        if (name == "script") return input.acceptToken(StartScriptTag);
        if (name == "style") return input.acceptToken(StartStyleTag);
        if (name == "textarea") return input.acceptToken(StartTextareaTag);
        if (selfClosers.hasOwnProperty(name)) return input.acceptToken(StartSelfClosingTag);
        if (parent && closeOnOpen[parent] && closeOnOpen[parent][name]) input.acceptToken(missingCloseTag, -1);
        else input.acceptToken(StartTag);
    }
}, {
    contextual: true
});
const commentContent = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"]((input)=>{
    for(let dashes = 0, i = 0;; i++){
        if (input.next < 0) {
            if (i) input.acceptToken(commentContent$1);
            break;
        }
        if (input.next == dash) {
            dashes++;
        } else if (input.next == greaterThan && dashes >= 2) {
            if (i >= 3) input.acceptToken(commentContent$1, -2);
            break;
        } else {
            dashes = 0;
        }
        input.advance();
    }
});
function inForeignElement(context) {
    for(; context; context = context.parent)if (context.name == "svg" || context.name == "math") return true;
    return false;
}
const endTag = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"]((input, stack)=>{
    if (input.next == slash && input.peek(1) == greaterThan) {
        let selfClosing = stack.dialectEnabled(Dialect_selfClosing) || inForeignElement(stack.context);
        input.acceptToken(selfClosing ? SelfClosingEndTag : EndTag, 2);
    } else if (input.next == greaterThan) {
        input.acceptToken(EndTag, 1);
    }
});
function contentTokenizer(tag, textToken, endToken) {
    let lastState = 2 + tag.length;
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"]((input)=>{
        // state means:
        // - 0 nothing matched
        // - 1 '<' matched
        // - 2 '</' + possibly whitespace matched
        // - 3-(1+tag.length) part of the tag matched
        // - lastState whole tag + possibly whitespace matched
        for(let state = 0, matchedLen = 0, i = 0;; i++){
            if (input.next < 0) {
                if (i) input.acceptToken(textToken);
                break;
            }
            if (state == 0 && input.next == lessThan || state == 1 && input.next == slash || state >= 2 && state < lastState && input.next == tag.charCodeAt(state - 2)) {
                state++;
                matchedLen++;
            } else if ((state == 2 || state == lastState) && isSpace(input.next)) {
                matchedLen++;
            } else if (state == lastState && input.next == greaterThan) {
                if (i > matchedLen) input.acceptToken(textToken, -matchedLen);
                else input.acceptToken(endToken, -(matchedLen - 2));
                break;
            } else if ((input.next == 10 /* '\n' */  || input.next == 13 /* '\r' */ ) && i) {
                input.acceptToken(textToken, 1);
                break;
            } else {
                state = matchedLen = 0;
            }
            input.advance();
        }
    });
}
const scriptTokens = contentTokenizer("script", scriptText, StartCloseScriptTag);
const styleTokens = contentTokenizer("style", styleText, StartCloseStyleTag);
const textareaTokens = contentTokenizer("textarea", textareaText, StartCloseTextareaTag);
const htmlHighlighting = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["styleTags"])({
    "Text RawText": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].content,
    "StartTag StartCloseTag SelfClosingEndTag EndTag": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].angleBracket,
    TagName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].tagName,
    "MismatchedCloseTag/TagName": [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].tagName,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].invalid
    ],
    AttributeName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].attributeName,
    "AttributeValue UnquotedAttributeValue": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].attributeValue,
    Is: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definitionOperator,
    "EntityReference CharacterReference": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].character,
    Comment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].blockComment,
    ProcessingInst: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].processingInstruction,
    DoctypeDecl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].documentMeta
});
// This file was generated by lezer-generator. You probably shouldn't edit it.
const parser = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LRParser"].deserialize({
    version: 14,
    states: ",xOVO!rOOO!WQ#tO'#CqO!]Q#tO'#CzO!bQ#tO'#C}O!gQ#tO'#DQO!lQ#tO'#DSO!qOaO'#CpO!|ObO'#CpO#XOdO'#CpO$eO!rO'#CpOOO`'#Cp'#CpO$lO$fO'#DTO$tQ#tO'#DVO$yQ#tO'#DWOOO`'#Dk'#DkOOO`'#DY'#DYQVO!rOOO%OQ&rO,59]O%ZQ&rO,59fO%fQ&rO,59iO%qQ&rO,59lO%|Q&rO,59nOOOa'#D^'#D^O&XOaO'#CxO&dOaO,59[OOOb'#D_'#D_O&lObO'#C{O&wObO,59[OOOd'#D`'#D`O'POdO'#DOO'[OdO,59[OOO`'#Da'#DaO'dO!rO,59[O'kQ#tO'#DROOO`,59[,59[OOOp'#Db'#DbO'pO$fO,59oOOO`,59o,59oO'xQ#|O,59qO'}Q#|O,59rOOO`-E7W-E7WO(SQ&rO'#CsOOQW'#DZ'#DZO(bQ&rO1G.wOOOa1G.w1G.wOOO`1G/Y1G/YO(mQ&rO1G/QOOOb1G/Q1G/QO(xQ&rO1G/TOOOd1G/T1G/TO)TQ&rO1G/WOOO`1G/W1G/WO)`Q&rO1G/YOOOa-E7[-E7[O)kQ#tO'#CyOOO`1G.v1G.vOOOb-E7]-E7]O)pQ#tO'#C|OOOd-E7^-E7^O)uQ#tO'#DPOOO`-E7_-E7_O)zQ#|O,59mOOOp-E7`-E7`OOO`1G/Z1G/ZOOO`1G/]1G/]OOO`1G/^1G/^O*PQ,UO,59_OOQW-E7X-E7XOOOa7+$c7+$cOOO`7+$t7+$tOOOb7+$l7+$lOOOd7+$o7+$oOOO`7+$r7+$rO*[Q#|O,59eO*aQ#|O,59hO*fQ#|O,59kOOO`1G/X1G/XO*kO7[O'#CvO*|OMhO'#CvOOQW1G.y1G.yOOO`1G/P1G/POOO`1G/S1G/SOOO`1G/V1G/VOOOO'#D['#D[O+_O7[O,59bOOQW,59b,59bOOOO'#D]'#D]O+pOMhO,59bOOOO-E7Y-E7YOOQW1G.|1G.|OOOO-E7Z-E7Z",
    stateData: ",]~O!^OS~OUSOVPOWQOXROYTO[]O][O^^O`^Oa^Ob^Oc^Ox^O{_O!dZO~OfaO~OfbO~OfcO~OfdO~OfeO~O!WfOPlP!ZlP~O!XiOQoP!ZoP~O!YlORrP!ZrP~OUSOVPOWQOXROYTOZqO[]O][O^^O`^Oa^Ob^Oc^Ox^O!dZO~O!ZrO~P#dO![sO!euO~OfvO~OfwO~OS|OT}OhyO~OS!POT}OhyO~OS!ROT}OhyO~OS!TOT}OhyO~OS}OT}OhyO~O!WfOPlX!ZlX~OP!WO!Z!XO~O!XiOQoX!ZoX~OQ!ZO!Z!XO~O!YlORrX!ZrX~OR!]O!Z!XO~O!Z!XO~P#dOf!_O~O![sO!e!aO~OS!bO~OS!cO~Oi!dOSgXTgXhgX~OS!fOT!gOhyO~OS!hOT!gOhyO~OS!iOT!gOhyO~OS!jOT!gOhyO~OS!gOT!gOhyO~Of!kO~Of!lO~Of!mO~OS!nO~Ok!qO!`!oO!b!pO~OS!rO~OS!sO~OS!tO~Oa!uOb!uOc!uO!`!wO!a!uO~Oa!xOb!xOc!xO!b!wO!c!xO~Oa!uOb!uOc!uO!`!{O!a!uO~Oa!xOb!xOc!xO!b!{O!c!xO~OT~bac!dx{!d~",
    goto: "%p!`PPPPPPPPPPPPPPPPPPPP!a!gP!mPP!yP!|#P#S#Y#]#`#f#i#l#r#x!aP!a!aP$O$U$l$r$x%O%U%[%bPPPPPPPP%hX^OX`pXUOX`pezabcde{!O!Q!S!UR!q!dRhUR!XhXVOX`pRkVR!XkXWOX`pRnWR!XnXXOX`pQrXR!XpXYOX`pQ`ORx`Q{aQ!ObQ!QcQ!SdQ!UeZ!e{!O!Q!S!UQ!v!oR!z!vQ!y!pR!|!yQgUR!VgQjVR!YjQmWR![mQpXR!^pQtZR!`tS_O`ToXp",
    nodeNames: "⚠ StartCloseTag StartCloseTag StartCloseTag EndTag SelfClosingEndTag StartTag StartTag StartTag StartTag StartTag StartCloseTag StartCloseTag StartCloseTag IncompleteCloseTag Document Text EntityReference CharacterReference InvalidEntity Element OpenTag TagName Attribute AttributeName Is AttributeValue UnquotedAttributeValue ScriptText CloseTag OpenTag StyleText CloseTag OpenTag TextareaText CloseTag OpenTag CloseTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag CloseTag DoctypeDecl",
    maxTerm: 67,
    context: elementContext,
    nodeProps: [
        [
            "closedBy",
            -10,
            1,
            2,
            3,
            7,
            8,
            9,
            10,
            11,
            12,
            13,
            "EndTag",
            6,
            "EndTag SelfClosingEndTag",
            -4,
            21,
            30,
            33,
            36,
            "CloseTag"
        ],
        [
            "openedBy",
            4,
            "StartTag StartCloseTag",
            5,
            "StartTag",
            -4,
            29,
            32,
            35,
            37,
            "OpenTag"
        ],
        [
            "group",
            -9,
            14,
            17,
            18,
            19,
            20,
            39,
            40,
            41,
            42,
            "Entity",
            16,
            "Entity TextContent",
            -3,
            28,
            31,
            34,
            "TextContent Entity"
        ],
        [
            "isolate",
            -11,
            21,
            29,
            30,
            32,
            33,
            35,
            36,
            37,
            38,
            41,
            42,
            "ltr",
            -3,
            26,
            27,
            39,
            ""
        ]
    ],
    propSources: [
        htmlHighlighting
    ],
    skippedNodes: [
        0
    ],
    repeatNodeCount: 9,
    tokenData: "!<p!aR!YOX$qXY,QYZ,QZ[$q[]&X]^,Q^p$qpq,Qqr-_rs3_sv-_vw3}wxHYx}-_}!OH{!O!P-_!P!Q$q!Q![-_![!]Mz!]!^-_!^!_!$S!_!`!;x!`!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4U-_4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!Z$|c`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr$qrs&}sv$qvw+Pwx(tx!^$q!^!_*V!_!a&X!a#S$q#S#T&X#T;'S$q;'S;=`+z<%lO$q!R&bX`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&Xq'UV`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}P'pT`POv'kw!^'k!_;'S'k;'S;=`(P<%lO'kP(SP;=`<%l'kp([S!cpOv(Vx;'S(V;'S;=`(h<%lO(Vp(kP;=`<%l(Vq(qP;=`<%l&}a({W`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t`)jT!a`Or)esv)ew;'S)e;'S;=`)y<%lO)e`)|P;=`<%l)ea*SP;=`<%l(t!Q*^V!a`!cpOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!Q*vP;=`<%l*V!R*|P;=`<%l&XW+UYkWOX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+PW+wP;=`<%l+P!Z+}P;=`<%l$q!a,]``P!a`!cp!^^OX&XXY,QYZ,QZ]&X]^,Q^p&Xpq,Qqr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!_-ljhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q[/ebhSkWOX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+PS0rXhSqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0mS1bP;=`<%l0m[1hP;=`<%l/^!V1vchS`P!a`!cpOq&Xqr1krs&}sv1kvw0mwx(tx!P1k!P!Q&X!Q!^1k!^!_*V!_!a&X!a#s1k#s$f&X$f;'S1k;'S;=`3R<%l?Ah1k?Ah?BY&X?BY?Mn1k?MnO&X!V3UP;=`<%l1k!_3[P;=`<%l-_!Z3hV!`h`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}!_4WihSkWc!ROX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst>]tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^/^!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!Z5zbkWOX5uXZ7SZ[5u[^7S^p5uqr5urs7Sst+Ptw5uwx7Sx!]5u!]!^7w!^!a7S!a#S5u#S#T7S#T;'S5u;'S;=`8n<%lO5u!R7VVOp7Sqs7St!]7S!]!^7l!^;'S7S;'S;=`7q<%lO7S!R7qOa!R!R7tP;=`<%l7S!Z8OYkWa!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!Z8qP;=`<%l5u!_8{ihSkWOX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst/^tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^:j!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!_:sbhSkWa!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!V<QchSOp7Sqr;{rs7Sst0mtw;{wx7Sx!P;{!P!Q7S!Q!];{!]!^=]!^!a7S!a#s;{#s$f7S$f;'S;{;'S;=`>P<%l?Ah;{?Ah?BY7S?BY?Mn;{?MnO7S!V=dXhSa!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!V>SP;=`<%l;{!_>YP;=`<%l8t!_>dhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^/^!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!Z@TakWOX@OXZAYZ[@O[^AY^p@Oqr@OrsAYsw@OwxAYx!]@O!]!^Az!^!aAY!a#S@O#S#TAY#T;'S@O;'S;=`Bq<%lO@O!RA]UOpAYq!]AY!]!^Ao!^;'SAY;'S;=`At<%lOAY!RAtOb!R!RAwP;=`<%lAY!ZBRYkWb!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!ZBtP;=`<%l@O!_COhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^Dj!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!_DsbhSkWb!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!VFQbhSOpAYqrE{rsAYswE{wxAYx!PE{!P!QAY!Q!]E{!]!^GY!^!aAY!a#sE{#s$fAY$f;'SE{;'S;=`G|<%l?AhE{?Ah?BYAY?BY?MnE{?MnOAY!VGaXhSb!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!VHPP;=`<%lE{!_HVP;=`<%lBw!ZHcW!bx`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t!aIYlhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OKQ!O!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!aK_khS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!`&X!`!aMS!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!TM_X`P!a`!cp!eQOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!aNZ!ZhSfQ`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OMz!O!PMz!P!Q$q!Q![Mz![!]Mz!]!^-_!^!_*V!_!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f$}-_$}%OMz%O%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4UMz4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Je-_$Je$JgMz$Jg$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!a!$PP;=`<%lMz!R!$ZY!a`!cpOq*Vqr!$yrs(Vsv*Vwx)ex!a*V!a!b!4t!b;'S*V;'S;=`*s<%lO*V!R!%Q]!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!%y!O!f*V!f!g!']!g#W*V#W#X!0`#X;'S*V;'S;=`*s<%lO*V!R!&QX!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!&m!O;'S*V;'S;=`*s<%lO*V!R!&vV!a`!cp!dPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!'dX!a`!cpOr*Vrs(Vsv*Vwx)ex!q*V!q!r!(P!r;'S*V;'S;=`*s<%lO*V!R!(WX!a`!cpOr*Vrs(Vsv*Vwx)ex!e*V!e!f!(s!f;'S*V;'S;=`*s<%lO*V!R!(zX!a`!cpOr*Vrs(Vsv*Vwx)ex!v*V!v!w!)g!w;'S*V;'S;=`*s<%lO*V!R!)nX!a`!cpOr*Vrs(Vsv*Vwx)ex!{*V!{!|!*Z!|;'S*V;'S;=`*s<%lO*V!R!*bX!a`!cpOr*Vrs(Vsv*Vwx)ex!r*V!r!s!*}!s;'S*V;'S;=`*s<%lO*V!R!+UX!a`!cpOr*Vrs(Vsv*Vwx)ex!g*V!g!h!+q!h;'S*V;'S;=`*s<%lO*V!R!+xY!a`!cpOr!+qrs!,hsv!+qvw!-Swx!.[x!`!+q!`!a!/j!a;'S!+q;'S;=`!0Y<%lO!+qq!,mV!cpOv!,hvx!-Sx!`!,h!`!a!-q!a;'S!,h;'S;=`!.U<%lO!,hP!-VTO!`!-S!`!a!-f!a;'S!-S;'S;=`!-k<%lO!-SP!-kO{PP!-nP;=`<%l!-Sq!-xS!cp{POv(Vx;'S(V;'S;=`(h<%lO(Vq!.XP;=`<%l!,ha!.aX!a`Or!.[rs!-Ssv!.[vw!-Sw!`!.[!`!a!.|!a;'S!.[;'S;=`!/d<%lO!.[a!/TT!a`{POr)esv)ew;'S)e;'S;=`)y<%lO)ea!/gP;=`<%l!.[!R!/sV!a`!cp{POr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!0]P;=`<%l!+q!R!0gX!a`!cpOr*Vrs(Vsv*Vwx)ex#c*V#c#d!1S#d;'S*V;'S;=`*s<%lO*V!R!1ZX!a`!cpOr*Vrs(Vsv*Vwx)ex#V*V#V#W!1v#W;'S*V;'S;=`*s<%lO*V!R!1}X!a`!cpOr*Vrs(Vsv*Vwx)ex#h*V#h#i!2j#i;'S*V;'S;=`*s<%lO*V!R!2qX!a`!cpOr*Vrs(Vsv*Vwx)ex#m*V#m#n!3^#n;'S*V;'S;=`*s<%lO*V!R!3eX!a`!cpOr*Vrs(Vsv*Vwx)ex#d*V#d#e!4Q#e;'S*V;'S;=`*s<%lO*V!R!4XX!a`!cpOr*Vrs(Vsv*Vwx)ex#X*V#X#Y!+q#Y;'S*V;'S;=`*s<%lO*V!R!4{Y!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!a!4t!a!b!:]!b;'S!4t;'S;=`!;r<%lO!4tq!5pV!cpOv!5kvx!6Vx!a!5k!a!b!7W!b;'S!5k;'S;=`!8V<%lO!5kP!6YTO!a!6V!a!b!6i!b;'S!6V;'S;=`!7Q<%lO!6VP!6lTO!`!6V!`!a!6{!a;'S!6V;'S;=`!7Q<%lO!6VP!7QOxPP!7TP;=`<%l!6Vq!7]V!cpOv!5kvx!6Vx!`!5k!`!a!7r!a;'S!5k;'S;=`!8V<%lO!5kq!7yS!cpxPOv(Vx;'S(V;'S;=`(h<%lO(Vq!8YP;=`<%l!5ka!8bX!a`Or!8]rs!6Vsv!8]vw!6Vw!a!8]!a!b!8}!b;'S!8];'S;=`!:V<%lO!8]a!9SX!a`Or!8]rs!6Vsv!8]vw!6Vw!`!8]!`!a!9o!a;'S!8];'S;=`!:V<%lO!8]a!9vT!a`xPOr)esv)ew;'S)e;'S;=`)y<%lO)ea!:YP;=`<%l!8]!R!:dY!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!`!4t!`!a!;S!a;'S!4t;'S;=`!;r<%lO!4t!R!;]V!a`!cpxPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!;uP;=`<%l!4t!V!<TXiS`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X",
    tokenizers: [
        scriptTokens,
        styleTokens,
        textareaTokens,
        endTag,
        tagStart,
        commentContent,
        0,
        1,
        2,
        3,
        4,
        5
    ],
    topRules: {
        "Document": [
            0,
            15
        ]
    },
    dialects: {
        noMatch: 0,
        selfClosing: 509
    },
    tokenPrec: 511
});
function getAttrs(openTag, input) {
    let attrs = Object.create(null);
    for (let att of openTag.getChildren(Attribute)){
        let name = att.getChild(AttributeName), value = att.getChild(AttributeValue) || att.getChild(UnquotedAttributeValue);
        if (name) attrs[input.read(name.from, name.to)] = !value ? "" : value.type.id == AttributeValue ? input.read(value.from + 1, value.to - 1) : input.read(value.from, value.to);
    }
    return attrs;
}
function findTagName(openTag, input) {
    let tagNameNode = openTag.getChild(TagName);
    return tagNameNode ? input.read(tagNameNode.from, tagNameNode.to) : " ";
}
function maybeNest(node, input, tags) {
    let attrs;
    for (let tag of tags){
        if (!tag.attrs || tag.attrs(attrs || (attrs = getAttrs(node.node.parent.firstChild, input)))) return {
            parser: tag.parser
        };
    }
    return null;
}
// tags?: {
//   tag: string,
//   attrs?: ({[attr: string]: string}) => boolean,
//   parser: Parser
// }[]
// attributes?: {
//   name: string,
//   tagName?: string,
//   parser: Parser
// }[]
function configureNesting(tags = [], attributes = []) {
    let script = [], style = [], textarea = [], other = [];
    for (let tag of tags){
        let array = tag.tag == "script" ? script : tag.tag == "style" ? style : tag.tag == "textarea" ? textarea : other;
        array.push(tag);
    }
    let attrs = attributes.length ? Object.create(null) : null;
    for (let attr of attributes)(attrs[attr.name] || (attrs[attr.name] = [])).push(attr);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseMixed"])((node, input)=>{
        let id = node.type.id;
        if (id == ScriptText) return maybeNest(node, input, script);
        if (id == StyleText) return maybeNest(node, input, style);
        if (id == TextareaText) return maybeNest(node, input, textarea);
        if (id == Element && other.length) {
            let n = node.node, open = n.firstChild, tagName = open && findTagName(open, input), attrs;
            if (tagName) for (let tag of other){
                if (tag.tag == tagName && (!tag.attrs || tag.attrs(attrs || (attrs = getAttrs(open, input))))) {
                    let close = n.lastChild;
                    let to = close.type.id == CloseTag ? close.from : n.to;
                    if (to > open.to) return {
                        parser: tag.parser,
                        overlay: [
                            {
                                from: open.to,
                                to
                            }
                        ]
                    };
                }
            }
        }
        if (attrs && id == Attribute) {
            let n = node.node, nameNode;
            if (nameNode = n.firstChild) {
                let matches = attrs[input.read(nameNode.from, nameNode.to)];
                if (matches) for (let attr of matches){
                    if (attr.tagName && attr.tagName != findTagName(n.parent, input)) continue;
                    let value = n.lastChild;
                    if (value.type.id == AttributeValue) {
                        let from = value.from + 1;
                        let last = value.lastChild, to = value.to - (last && last.isError ? 0 : 1);
                        if (to > from) return {
                            parser: attr.parser,
                            overlay: [
                                {
                                    from,
                                    to
                                }
                            ]
                        };
                    } else if (value.type.id == UnquotedAttributeValue) {
                        return {
                            parser: attr.parser,
                            overlay: [
                                {
                                    from: value.from,
                                    to: value.to
                                }
                            ]
                        };
                    }
                }
            }
        }
        return null;
    });
}
;
}}),
"[project]/node_modules/@lezer/css/dist/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "parser": (()=>parser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/lr/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/highlight/dist/index.js [app-client] (ecmascript)");
;
;
// This file was generated by lezer-generator. You probably shouldn't edit it.
const descendantOp = 122, Unit = 1, identifier = 123, callee = 124, VariableName = 2, queryIdentifier = 125, queryVariableName = 3, QueryCallee = 4;
/* Hand-written tokenizers for CSS tokens that can't be
   expressed by Lezer's built-in tokenizer. */ const space = [
    9,
    10,
    11,
    12,
    13,
    32,
    133,
    160,
    5760,
    8192,
    8193,
    8194,
    8195,
    8196,
    8197,
    8198,
    8199,
    8200,
    8201,
    8202,
    8232,
    8233,
    8239,
    8287,
    12288
];
const colon = 58, parenL = 40, underscore = 95, bracketL = 91, dash = 45, period = 46, hash = 35, percent = 37, ampersand = 38, backslash = 92, newline = 10, asterisk = 42;
function isAlpha(ch) {
    return ch >= 65 && ch <= 90 || ch >= 97 && ch <= 122 || ch >= 161;
}
function isDigit(ch) {
    return ch >= 48 && ch <= 57;
}
function isHex(ch) {
    return isDigit(ch) || ch >= 97 && ch <= 102 || ch >= 65 && ch <= 70;
}
const identifierTokens = (id, varName, callee)=>(input, stack)=>{
        for(let inside = false, dashes = 0, i = 0;; i++){
            let { next } = input;
            if (isAlpha(next) || next == dash || next == underscore || inside && isDigit(next)) {
                if (!inside && (next != dash || i > 0)) inside = true;
                if (dashes === i && next == dash) dashes++;
                input.advance();
            } else if (next == backslash && input.peek(1) != newline) {
                input.advance();
                if (isHex(input.next)) {
                    do {
                        input.advance();
                    }while (isHex(input.next))
                    if (input.next == 32) input.advance();
                } else if (input.next > -1) {
                    input.advance();
                }
                inside = true;
            } else {
                if (inside) input.acceptToken(dashes == 2 && stack.canShift(VariableName) ? varName : next == parenL ? callee : id);
                break;
            }
        }
    };
const identifiers = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"](identifierTokens(identifier, VariableName, callee));
const queryIdentifiers = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"](identifierTokens(queryIdentifier, queryVariableName, QueryCallee));
const descendant = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"]((input)=>{
    if (space.includes(input.peek(-1))) {
        let { next } = input;
        if (isAlpha(next) || next == underscore || next == hash || next == period || next == asterisk || next == bracketL || next == colon && isAlpha(input.peek(1)) || next == dash || next == ampersand) input.acceptToken(descendantOp);
    }
});
const unitToken = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"]((input)=>{
    if (!space.includes(input.peek(-1))) {
        let { next } = input;
        if (next == percent) {
            input.advance();
            input.acceptToken(Unit);
        }
        if (isAlpha(next)) {
            do {
                input.advance();
            }while (isAlpha(input.next) || isDigit(input.next))
            input.acceptToken(Unit);
        }
    }
});
const cssHighlighting = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["styleTags"])({
    "AtKeyword import charset namespace keyframes media supports": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definitionKeyword,
    "from to selector": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].keyword,
    NamespaceName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].namespace,
    KeyframeName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].labelName,
    KeyframeRangeName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].operatorKeyword,
    TagName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].tagName,
    ClassName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].className,
    PseudoClassName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].constant(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].className),
    IdName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].labelName,
    "FeatureName PropertyName": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].propertyName,
    AttributeName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].attributeName,
    NumberLiteral: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].number,
    KeywordQuery: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].keyword,
    UnaryQueryOp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].operatorKeyword,
    "CallTag ValueName": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].atom,
    VariableName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].variableName,
    Callee: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].operatorKeyword,
    Unit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].unit,
    "UniversalSelector NestingSelector": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definitionOperator,
    "MatchOp CompareOp": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].compareOperator,
    "ChildOp SiblingOp, LogicOp": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].logicOperator,
    BinOp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].arithmeticOperator,
    Important: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].modifier,
    Comment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].blockComment,
    ColorLiteral: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].color,
    "ParenthesizedContent StringLiteral": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].string,
    ":": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].punctuation,
    "PseudoOp #": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].derefOperator,
    "; ,": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].separator,
    "( )": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].paren,
    "[ ]": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].squareBracket,
    "{ }": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].brace
});
// This file was generated by lezer-generator. You probably shouldn't edit it.
const spec_callee = {
    __proto__: null,
    lang: 38,
    "nth-child": 38,
    "nth-last-child": 38,
    "nth-of-type": 38,
    "nth-last-of-type": 38,
    dir: 38,
    "host-context": 38,
    if: 84,
    url: 124,
    "url-prefix": 124,
    domain: 124,
    regexp: 124
};
const spec_queryIdentifier = {
    __proto__: null,
    or: 98,
    and: 98,
    not: 106,
    only: 106,
    layer: 170
};
const spec_QueryCallee = {
    __proto__: null,
    selector: 112,
    layer: 166
};
const spec_AtKeyword = {
    __proto__: null,
    "@import": 162,
    "@media": 174,
    "@charset": 178,
    "@namespace": 182,
    "@keyframes": 188,
    "@supports": 200,
    "@scope": 204
};
const spec_identifier = {
    __proto__: null,
    to: 207
};
const parser = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LRParser"].deserialize({
    version: 14,
    states: "EbQYQdOOO#qQdOOP#xO`OOOOQP'#Cf'#CfOOQP'#Ce'#CeO#}QdO'#ChO$nQaO'#CcO$xQdO'#CkO%TQdO'#DpO%YQdO'#DrO%_QdO'#DuO%_QdO'#DxOOQP'#FV'#FVO&eQhO'#EhOOQS'#FU'#FUOOQS'#Ek'#EkQYQdOOO&lQdO'#EOO&PQhO'#EUO&lQdO'#EWO'aQdO'#EYO'lQdO'#E]O'tQhO'#EcO(VQdO'#EeO(bQaO'#CfO)VQ`O'#D{O)[Q`O'#F`O)gQdO'#F`QOQ`OOP)qO&jO'#CaPOOO)C@t)C@tOOQP'#Cj'#CjOOQP,59S,59SO#}QdO,59SO)|QdO,59VO%TQdO,5:[O%YQdO,5:^O%_QdO,5:aO%_QdO,5:cO%_QdO,5:dO%_QdO'#ErO*XQ`O,58}O*aQdO'#DzOOQS,58},58}OOQP'#Cn'#CnOOQO'#Dn'#DnOOQP,59V,59VO*hQ`O,59VO*mQ`O,59VOOQP'#Dq'#DqOOQP,5:[,5:[OOQO'#Ds'#DsO*rQpO,5:^O+]QaO,5:aO+sQaO,5:dOOQW'#DZ'#DZO,ZQhO'#DdO,xQhO'#FaO'tQhO'#DbO-WQ`O'#DhOOQW'#F['#F[O-]Q`O,5;SO-eQ`O'#DeOOQS-E8i-E8iOOQ['#Cs'#CsO-jQdO'#CtO.QQdO'#CzO.hQdO'#C}O/OQ!pO'#DPO1RQ!jO,5:jOOQO'#DU'#DUO*mQ`O'#DTO1cQ!nO'#FXO3`Q`O'#DVO3eQ`O'#DkOOQ['#FX'#FXO-`Q`O,5:pO3jQ!bO,5:rOOQS'#E['#E[O3rQ`O,5:tO3wQdO,5:tOOQO'#E_'#E_O4PQ`O,5:wO4UQhO,5:}O%_QdO'#DgOOQS,5;P,5;PO-eQ`O,5;PO4^QdO,5;PO4fQdO,5:gO4vQdO'#EtO5TQ`O,5;zO5TQ`O,5;zPOOO'#Ej'#EjP5`O&jO,58{POOO,58{,58{OOQP1G.n1G.nOOQP1G.q1G.qO*hQ`O1G.qO*mQ`O1G.qOOQP1G/v1G/vO5kQpO1G/xO5sQaO1G/{O6ZQaO1G/}O6qQaO1G0OO7XQaO,5;^OOQO-E8p-E8pOOQS1G.i1G.iO7cQ`O,5:fO7hQdO'#DoO7oQdO'#CrOOQP1G/x1G/xO&lQdO1G/xO7vQ!jO'#DZO8UQ!bO,59vO8^QhO,5:OOOQO'#F]'#F]O8XQ!bO,59zO'tQhO,59xO8fQhO'#EvO8sQ`O,5;{O9OQhO,59|O9uQhO'#DiOOQW,5:S,5:SOOQS1G0n1G0nOOQW,5:P,5:PO9|Q!fO'#FYOOQS'#FY'#FYOOQS'#Em'#EmO;^QdO,59`OOQ[,59`,59`O;tQdO,59fOOQ[,59f,59fO<[QdO,59iOOQ[,59i,59iOOQ[,59k,59kO&lQdO,59mO<rQhO'#EQOOQW'#EQ'#EQO=WQ`O1G0UO1[QhO1G0UOOQ[,59o,59oO'tQhO'#DXOOQ[,59q,59qO=]Q#tO,5:VOOQS1G0[1G0[OOQS1G0^1G0^OOQS1G0`1G0`O=hQ`O1G0`O=mQdO'#E`OOQS1G0c1G0cOOQS1G0i1G0iO=xQaO,5:RO-`Q`O1G0kOOQS1G0k1G0kO-eQ`O1G0kO>PQ!fO1G0ROOQO1G0R1G0ROOQO,5;`,5;`O>gQdO,5;`OOQO-E8r-E8rO>tQ`O1G1fPOOO-E8h-E8hPOOO1G.g1G.gOOQP7+$]7+$]OOQP7+%d7+%dO&lQdO7+%dOOQS1G0Q1G0QO?PQaO'#F_O?ZQ`O,5:ZO?`Q!fO'#ElO@^QdO'#FWO@hQ`O,59^O@mQ!bO7+%dO&lQdO1G/bO@uQhO1G/fOOQW1G/j1G/jOOQW1G/d1G/dOAWQhO,5;bOOQO-E8t-E8tOAfQhO'#DZOAtQhO'#F^OBPQ`O'#F^OBUQ`O,5:TOOQS-E8k-E8kOOQ[1G.z1G.zOOQ[1G/Q1G/QOOQ[1G/T1G/TOOQ[1G/X1G/XOBZQdO,5:lOOQS7+%p7+%pOB`Q`O7+%pOBeQhO'#DYOBmQ`O,59sO'tQhO,59sOOQ[1G/q1G/qOBuQ`O1G/qOOQS7+%z7+%zOBzQbO'#DPOOQO'#Eb'#EbOCYQ`O'#EaOOQO'#Ea'#EaOCeQ`O'#EwOCmQdO,5:zOOQS,5:z,5:zOOQ[1G/m1G/mOOQS7+&V7+&VO-`Q`O7+&VOCxQ!fO'#EsO&lQdO'#EsOEPQdO7+%mOOQO7+%m7+%mOOQO1G0z1G0zOEdQ!bO<<IOOElQdO'#EqOEvQ`O,5;yOOQP1G/u1G/uOOQS-E8j-E8jOFOQdO'#EpOFYQ`O,5;rOOQ]1G.x1G.xOOQP<<IO<<IOOFbQdO7+$|OOQO'#D]'#D]OFiQ!bO7+%QOFqQhO'#EoOF{Q`O,5;xO&lQdO,5;xOOQW1G/o1G/oOOQO'#ES'#ESOGTQ`O1G0WOOQS<<I[<<I[O&lQdO,59tOGnQhO1G/_OOQ[1G/_1G/_OGuQ`O1G/_OOQW-E8l-E8lOOQ[7+%]7+%]OOQO,5:{,5:{O=pQdO'#ExOCeQ`O,5;cOOQS,5;c,5;cOOQS-E8u-E8uOOQS1G0f1G0fOOQS<<Iq<<IqOG}Q!fO,5;_OOQS-E8q-E8qOOQO<<IX<<IXOOQPAN>jAN>jOIUQaO,5;]OOQO-E8o-E8oOI`QdO,5;[OOQO-E8n-E8nOOQW<<Hh<<HhOOQW<<Hl<<HlOIjQhO<<HlOI{QhO,5;ZOJWQ`O,5;ZOOQO-E8m-E8mOJ]QdO1G1dOBZQdO'#EuOJgQ`O7+%rOOQW7+%r7+%rOJoQ!bO1G/`OOQ[7+$y7+$yOJzQhO7+$yPKRQ`O'#EnOOQO,5;d,5;dOOQO-E8v-E8vOOQS1G0}1G0}OKWQ`OAN>WO&lQdO1G0uOK]Q`O7+'OOOQO,5;a,5;aOOQO-E8s-E8sOOQW<<I^<<I^OOQ[<<He<<HePOQW,5;Y,5;YOOQWG23rG23rOKeQdO7+&a",
    stateData: "Kx~O#sOS#tQQ~OW[OZ[O]TO`VOaVOi]OjWOmXO!jYO!mZO!saO!ybO!{cO!}dO#QeO#WfO#YgO#oRO~OQiOW[OZ[O]TO`VOaVOi]OjWOmXO!jYO!mZO!saO!ybO!{cO!}dO#QeO#WfO#YgO#ohO~O#m$SP~P!dO#tmO~O#ooO~O]qO`rOarOjsOmtO!juO!mwO#nvO~OpzO!^xO~P$SOc!QO#o|O#p}O~O#o!RO~O#o!TO~OW[OZ[O]TO`VOaVOjWOmXO!jYO!mZO#oRO~OS!]Oe!YO!V![O!Y!`O#q!XOp$TP~Ok$TP~P&POQ!jOe!cOm!dOp!eOr!mOt!mOz!kO!`!lO#o!bO#p!hO#}!fO~Ot!qO!`!lO#o!pO~Ot!sO#o!sO~OS!]Oe!YO!V![O!Y!`O#q!XO~Oe!vOpzO#Z!xO~O]YX`YX`!pXaYXjYXmYXpYX!^YX!jYX!mYX#nYX~O`!zO~Ok!{O#m$SXo$SX~O#m$SXo$SX~P!dO#u#OO#v#OO#w#QO~Oc#UO#o|O#p}O~OpzO!^xO~Oo$SP~P!dOe#`O~Oe#aO~Ol#bO!h#cO~O]qO`rOarOjsOmtO~Op!ia!^!ia!j!ia!m!ia#n!iad!ia~P*zOp!la!^!la!j!la!m!la#n!lad!la~P*zOR#gOS!]Oe!YOr#gOt#gO!V![O!Y!`O#q#dO#}!fO~O!R#iO!^#jOk$TXp$TX~Oe#mO~Ok#oOpzO~Oe!vO~O]#rO`#rOd#uOi#rOj#rOk#rO~P&lO]#rO`#rOi#rOj#rOk#rOl#wO~P&lO]#rO`#rOi#rOj#rOk#rOo#yO~P&lOP#zOSsXesXksXvsX!VsX!YsX!usX!wsX#qsX!TsXQsX]sX`sXdsXisXjsXmsXpsXrsXtsXzsX!`sX#osX#psX#}sXlsXosX!^sX!qsX#msX~Ov#{O!u#|O!w#}Ok$TP~P'tOe#aOS#{Xk#{Xv#{X!V#{X!Y#{X!u#{X!w#{X#q#{XQ#{X]#{X`#{Xd#{Xi#{Xj#{Xm#{Xp#{Xr#{Xt#{Xz#{X!`#{X#o#{X#p#{X#}#{Xl#{Xo#{X!^#{X!q#{X#m#{X~Oe$RO~Oe$TO~Ok$VOv#{O~Ok$WO~Ot$XO!`!lO~Op$YO~OpzO!R#iO~OpzO#Z$`O~O!q$bOk!oa#m!oao!oa~P&lOk#hX#m#hXo#hX~P!dOk!{O#m$Sao$Sa~O#u#OO#v#OO#w$hO~Ol$jO!h$kO~Op!ii!^!ii!j!ii!m!ii#n!iid!ii~P*zOp!ki!^!ki!j!ki!m!ki#n!kid!ki~P*zOp!li!^!li!j!li!m!li#n!lid!li~P*zOp#fa!^#fa~P$SOo$lO~Od$RP~P%_Od#zP~P&lO`!PXd}X!R}X!T!PX~O`$sO!T$tO~Od$uO!R#iO~Ok#jXp#jX!^#jX~P'tO!^#jOk$Tap$Ta~O!R#iOk!Uap!Ua!^!Uad!Ua`!Ua~OS!]Oe!YO!V![O!Y!`O#q$yO~Od$QP~P9dOv#{OQ#|X]#|X`#|Xd#|Xe#|Xi#|Xj#|Xk#|Xm#|Xp#|Xr#|Xt#|Xz#|X!`#|X#o#|X#p#|X#}#|Xl#|Xo#|X~O]#rO`#rOd%OOi#rOj#rOk#rO~P&lO]#rO`#rOi#rOj#rOk#rOl%PO~P&lO]#rO`#rOi#rOj#rOk#rOo%QO~P&lOe%SOS!tXk!tX!V!tX!Y!tX#q!tX~Ok%TO~Od%YOt%ZO!a%ZO~Ok%[O~Oo%cO#o%^O#}%]O~Od%dO~P$SOv#{O!^%hO!q%jOk!oi#m!oio!oi~P&lOk#ha#m#hao#ha~P!dOk!{O#m$Sio$Si~O!^%mOd$RX~P$SOd%oO~Ov#{OQ#`Xd#`Xe#`Xm#`Xp#`Xr#`Xt#`Xz#`X!^#`X!`#`X#o#`X#p#`X#}#`X~O!^%qOd#zX~P&lOd%sO~Ol%tOv#{O~OR#gOr#gOt#gO#q%vO#}!fO~O!R#iOk#jap#ja!^#ja~O`!PXd}X!R}X!^}X~O!R#iO!^%xOd$QX~O`%zO~Od%{O~O#o%|O~Ok&OO~O`&PO!R#iO~Od&ROk&QO~Od&UO~OP#zOpsX!^sXdsX~O#}%]Op#TX!^#TX~OpzO!^&WO~Oo&[O#o%^O#}%]O~Ov#{OQ#gXe#gXk#gXm#gXp#gXr#gXt#gXz#gX!^#gX!`#gX!q#gX#m#gX#o#gX#p#gX#}#gXo#gX~O!^%hO!q&`Ok!oq#m!oqo!oq~P&lOl&aOv#{O~Od#eX!^#eX~P%_O!^%mOd$Ra~Od#dX!^#dX~P&lO!^%qOd#za~Od&fO~P&lOd&gO!T&hO~Od#cX!^#cX~P9dO!^%xOd$Qa~O]&mOd&oO~OS#bae#ba!V#ba!Y#ba#q#ba~Od&qO~PG]Od&qOk&rO~Ov#{OQ#gae#gak#gam#gap#gar#gat#gaz#ga!^#ga!`#ga!q#ga#m#ga#o#ga#p#ga#}#gao#ga~Od#ea!^#ea~P$SOd#da!^#da~P&lOR#gOr#gOt#gO#q%vO#}%]O~O!R#iOd#ca!^#ca~O`&xO~O!^%xOd$Qi~P&lO]&mOd&|O~Ov#{Od|ik|i~Od&}O~PG]Ok'OO~Od'PO~O!^%xOd$Qq~Od#cq!^#cq~P&lO#s!a#t#}]#}v!m~",
    goto: "2h$UPPPPP$VP$YP$c$uP$cP%X$cPP%_PPP%e%o%oPPPPP%oPP%oP&]P%oP%o'W%oP't'w'}'}(^'}P'}P'}P'}'}P(m'}(yP(|PP)p)v$c)|$c*SP$cP$c$cP*Y*{+YP$YP+aP+dP$YP$YP$YP+j$YP+m+p+s+z$YP$YPP$YP,P,V,f,|-[-b-l-r-x.O.U.`.f.l.rPPPPPPPPPPP.x/R/w/z0|P1U1u2O2R2U2[RnQ_^OP`kz!{$dq[OPYZ`kuvwxz!v!{#`$d%mqSOPYZ`kuvwxz!v!{#`$d%mQpTR#RqQ!OVR#SrQ#S!QS$Q!i!jR$i#U!V!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'Q!U!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QU#g!Y$t&hU%`$Y%b&WR&V%_!V!iac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QR$S!kQ%W$RR&S%Xk!^]bf!Y![!g#i#j#m$P$R%X%xQ#e!YQ${#mQ%w$tQ&j%xR&w&hQ!ygQ#p!`Q$^!xR%f$`R#n!]!U!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QQ!qdR$X!rQ!PVR#TrQ#S!PR$i#TQ!SWR#VsQ!UXR#WtQ{UQ!wgQ#^yQ#o!_Q$U!nQ$[!uQ$_!yQ%e$^Q&Y%aQ&]%fR&v&XSjPzQ!}kQ$c!{R%k$dZiPkz!{$dR$P!gQ%}%SR&z&mR!rdR!teR$Z!tS%a$Y%bR&t&WV%_$Y%b&WQ#PmR$g#PQ`OSkPzU!a`k$dR$d!{Q$p#aY%p$p%u&d&l'QQ%u$sQ&d%qQ&l%zR'Q&xQ#t!cQ#v!dQ#x!eV$}#t#v#xQ%X$RR&T%XQ%y$zS&k%y&yR&y&lQ%r$pR&e%rQ%n$mR&c%nQyUR#]yQ%i$aR&_%iQ!|jS$e!|$fR$f!}Q&n%}R&{&nQ#k!ZR$x#kQ%b$YR&Z%bQ&X%aR&u&X__OP`kz!{$d^UOP`kz!{$dQ!VYQ!WZQ#XuQ#YvQ#ZwQ#[xQ$]!vQ$m#`R&b%mR$q#aQ!gaQ!oc[#q!c!d!e#t#v#xQ$a!zd$o#a$p$s%q%u%z&d&l&x'QQ$r#cQ%R#{S%g$a%iQ%l$kQ&^%hR&p&P]#s!c!d!e#t#v#xW!Z]b!g$PQ!ufQ#f!YQ#l![Q$v#iQ$w#jQ$z#mS%V$R%XR&i%xQ#h!YQ%w$tR&w&hR$|#mR$n#`QlPR#_zQ!_]Q!nbQ$O!gR%U$P",
    nodeNames: "⚠ Unit VariableName VariableName QueryCallee Comment StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector ClassSelector . ClassName PseudoClassSelector : :: PseudoClassName PseudoClassName ) ( ArgList ValueName ParenthesizedValue AtKeyword # ; ] [ BracketedValue } { BracedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp CallExpression Callee IfExpression if ArgList IfBranch KeywordQuery FeatureQuery FeatureName BinaryQuery LogicOp ComparisonQuery CompareOp UnaryQuery UnaryQueryOp ParenthesizedQuery SelectorQuery selector ParenthesizedSelector CallQuery ArgList , CallLiteral CallTag ParenthesizedContent PseudoClassName ArgList IdSelector IdName AttributeSelector AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp Block Declaration PropertyName Important ImportStatement import Layer layer LayerName layer MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList KeyframeSelector KeyframeRangeName SupportsStatement supports ScopeStatement scope to AtRule Styles",
    maxTerm: 143,
    nodeProps: [
        [
            "isolate",
            -2,
            5,
            36,
            ""
        ],
        [
            "openedBy",
            20,
            "(",
            28,
            "[",
            31,
            "{"
        ],
        [
            "closedBy",
            21,
            ")",
            29,
            "]",
            32,
            "}"
        ]
    ],
    propSources: [
        cssHighlighting
    ],
    skippedNodes: [
        0,
        5,
        106
    ],
    repeatNodeCount: 15,
    tokenData: "JQ~R!YOX$qX^%i^p$qpq%iqr({rs-ust/itu6Wuv$qvw7Qwx7cxy9Qyz9cz{9h{|:R|}>t}!O?V!O!P?t!P!Q@]!Q![AU![!]BP!]!^B{!^!_C^!_!`DY!`!aDm!a!b$q!b!cEn!c!}$q!}#OG{#O#P$q#P#QH^#Q#R6W#R#o$q#o#pHo#p#q6W#q#rIQ#r#sIc#s#y$q#y#z%i#z$f$q$f$g%i$g#BY$q#BY#BZ%i#BZ$IS$q$IS$I_%i$I_$I|$q$I|$JO%i$JO$JT$q$JT$JU%i$JU$KV$q$KV$KW%i$KW&FU$q&FU&FV%i&FV;'S$q;'S;=`Iz<%lO$q`$tSOy%Qz;'S%Q;'S;=`%c<%lO%Q`%VS!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Q`%fP;=`<%l%Q~%nh#s~OX%QX^'Y^p%Qpq'Yqy%Qz#y%Q#y#z'Y#z$f%Q$f$g'Y$g#BY%Q#BY#BZ'Y#BZ$IS%Q$IS$I_'Y$I_$I|%Q$I|$JO'Y$JO$JT%Q$JT$JU'Y$JU$KV%Q$KV$KW'Y$KW&FU%Q&FU&FV'Y&FV;'S%Q;'S;=`%c<%lO%Q~'ah#s~!a`OX%QX^'Y^p%Qpq'Yqy%Qz#y%Q#y#z'Y#z$f%Q$f$g'Y$g#BY%Q#BY#BZ'Y#BZ$IS%Q$IS$I_'Y$I_$I|%Q$I|$JO'Y$JO$JT%Q$JT$JU'Y$JU$KV%Q$KV$KW'Y$KW&FU%Q&FU&FV'Y&FV;'S%Q;'S;=`%c<%lO%Qj)OUOy%Qz#]%Q#]#^)b#^;'S%Q;'S;=`%c<%lO%Qj)gU!a`Oy%Qz#a%Q#a#b)y#b;'S%Q;'S;=`%c<%lO%Qj*OU!a`Oy%Qz#d%Q#d#e*b#e;'S%Q;'S;=`%c<%lO%Qj*gU!a`Oy%Qz#c%Q#c#d*y#d;'S%Q;'S;=`%c<%lO%Qj+OU!a`Oy%Qz#f%Q#f#g+b#g;'S%Q;'S;=`%c<%lO%Qj+gU!a`Oy%Qz#h%Q#h#i+y#i;'S%Q;'S;=`%c<%lO%Qj,OU!a`Oy%Qz#T%Q#T#U,b#U;'S%Q;'S;=`%c<%lO%Qj,gU!a`Oy%Qz#b%Q#b#c,y#c;'S%Q;'S;=`%c<%lO%Qj-OU!a`Oy%Qz#h%Q#h#i-b#i;'S%Q;'S;=`%c<%lO%Qj-iS!qY!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Q~-xWOY-uZr-urs.bs#O-u#O#P.g#P;'S-u;'S;=`/c<%lO-u~.gOt~~.jRO;'S-u;'S;=`.s;=`O-u~.vXOY-uZr-urs.bs#O-u#O#P.g#P;'S-u;'S;=`/c;=`<%l-u<%lO-u~/fP;=`<%l-uj/nYjYOy%Qz!Q%Q!Q![0^![!c%Q!c!i0^!i#T%Q#T#Z0^#Z;'S%Q;'S;=`%c<%lO%Qj0cY!a`Oy%Qz!Q%Q!Q![1R![!c%Q!c!i1R!i#T%Q#T#Z1R#Z;'S%Q;'S;=`%c<%lO%Qj1WY!a`Oy%Qz!Q%Q!Q![1v![!c%Q!c!i1v!i#T%Q#T#Z1v#Z;'S%Q;'S;=`%c<%lO%Qj1}YrY!a`Oy%Qz!Q%Q!Q![2m![!c%Q!c!i2m!i#T%Q#T#Z2m#Z;'S%Q;'S;=`%c<%lO%Qj2tYrY!a`Oy%Qz!Q%Q!Q![3d![!c%Q!c!i3d!i#T%Q#T#Z3d#Z;'S%Q;'S;=`%c<%lO%Qj3iY!a`Oy%Qz!Q%Q!Q![4X![!c%Q!c!i4X!i#T%Q#T#Z4X#Z;'S%Q;'S;=`%c<%lO%Qj4`YrY!a`Oy%Qz!Q%Q!Q![5O![!c%Q!c!i5O!i#T%Q#T#Z5O#Z;'S%Q;'S;=`%c<%lO%Qj5TY!a`Oy%Qz!Q%Q!Q![5s![!c%Q!c!i5s!i#T%Q#T#Z5s#Z;'S%Q;'S;=`%c<%lO%Qj5zSrY!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Qd6ZUOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Qd6tS!hS!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Qb7VSZQOy%Qz;'S%Q;'S;=`%c<%lO%Q~7fWOY7cZw7cwx.bx#O7c#O#P8O#P;'S7c;'S;=`8z<%lO7c~8RRO;'S7c;'S;=`8[;=`O7c~8_XOY7cZw7cwx.bx#O7c#O#P8O#P;'S7c;'S;=`8z;=`<%l7c<%lO7c~8}P;=`<%l7cj9VSeYOy%Qz;'S%Q;'S;=`%c<%lO%Q~9hOd~n9oUWQvWOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Qj:YWvW!mQOy%Qz!O%Q!O!P:r!P!Q%Q!Q![=w![;'S%Q;'S;=`%c<%lO%Qj:wU!a`Oy%Qz!Q%Q!Q![;Z![;'S%Q;'S;=`%c<%lO%Qj;bY!a`#}YOy%Qz!Q%Q!Q![;Z![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%Qj<VY!a`Oy%Qz{%Q{|<u|}%Q}!O<u!O!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj<zU!a`Oy%Qz!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj=eU!a`#}YOy%Qz!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj>O[!a`#}YOy%Qz!O%Q!O!P;Z!P!Q%Q!Q![=w![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%Qj>yS!^YOy%Qz;'S%Q;'S;=`%c<%lO%Qj?[WvWOy%Qz!O%Q!O!P:r!P!Q%Q!Q![=w![;'S%Q;'S;=`%c<%lO%Qj?yU]YOy%Qz!Q%Q!Q![;Z![;'S%Q;'S;=`%c<%lO%Q~@bTvWOy%Qz{@q{;'S%Q;'S;=`%c<%lO%Q~@xS!a`#t~Oy%Qz;'S%Q;'S;=`%c<%lO%QjAZ[#}YOy%Qz!O%Q!O!P;Z!P!Q%Q!Q![=w![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%QjBUU`YOy%Qz![%Q![!]Bh!];'S%Q;'S;=`%c<%lO%QbBoSaQ!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QjCQSkYOy%Qz;'S%Q;'S;=`%c<%lO%QhCcU!TWOy%Qz!_%Q!_!`Cu!`;'S%Q;'S;=`%c<%lO%QhC|S!TW!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QlDaS!TW!hSOy%Qz;'S%Q;'S;=`%c<%lO%QjDtV!jQ!TWOy%Qz!_%Q!_!`Cu!`!aEZ!a;'S%Q;'S;=`%c<%lO%QbEbS!jQ!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QjEqYOy%Qz}%Q}!OFa!O!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjFfW!a`Oy%Qz!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjGV[iY!a`Oy%Qz}%Q}!OGO!O!Q%Q!Q![GO![!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjHQSmYOy%Qz;'S%Q;'S;=`%c<%lO%QnHcSl^Oy%Qz;'S%Q;'S;=`%c<%lO%QjHtSpYOy%Qz;'S%Q;'S;=`%c<%lO%QjIVSoYOy%Qz;'S%Q;'S;=`%c<%lO%QfIhU!mQOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Q`I}P;=`<%l$q",
    tokenizers: [
        descendant,
        unitToken,
        identifiers,
        queryIdentifiers,
        1,
        2,
        3,
        4,
        new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LocalTokenGroup"]("m~RRYZ[z{a~~g~aO#v~~dP!P!Qg~lO#w~~", 28, 129)
    ],
    topRules: {
        "StyleSheet": [
            0,
            6
        ],
        "Styles": [
            1,
            105
        ]
    },
    specialized: [
        {
            term: 124,
            get: (value)=>spec_callee[value] || -1
        },
        {
            term: 125,
            get: (value)=>spec_queryIdentifier[value] || -1
        },
        {
            term: 4,
            get: (value)=>spec_QueryCallee[value] || -1
        },
        {
            term: 25,
            get: (value)=>spec_AtKeyword[value] || -1
        },
        {
            term: 123,
            get: (value)=>spec_identifier[value] || -1
        }
    ],
    tokenPrec: 1963
});
;
}}),
"[project]/node_modules/@codemirror/lang-css/dist/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "css": (()=>css),
    "cssCompletionSource": (()=>cssCompletionSource),
    "cssLanguage": (()=>cssLanguage),
    "defineCSSCompletionSource": (()=>defineCSSCompletionSource)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$css$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/css/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codemirror/language/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/common/dist/index.js [app-client] (ecmascript)");
;
;
;
let _properties = null;
function properties() {
    if (!_properties && typeof document == "object" && document.body) {
        let { style } = document.body, names = [], seen = new Set;
        for(let prop in style)if (prop != "cssText" && prop != "cssFloat") {
            if (typeof style[prop] == "string") {
                if (/[A-Z]/.test(prop)) prop = prop.replace(/[A-Z]/g, (ch)=>"-" + ch.toLowerCase());
                if (!seen.has(prop)) {
                    names.push(prop);
                    seen.add(prop);
                }
            }
        }
        _properties = names.sort().map((name)=>({
                type: "property",
                label: name,
                apply: name + ": "
            }));
    }
    return _properties || [];
}
const pseudoClasses = /*@__PURE__*/ [
    "active",
    "after",
    "any-link",
    "autofill",
    "backdrop",
    "before",
    "checked",
    "cue",
    "default",
    "defined",
    "disabled",
    "empty",
    "enabled",
    "file-selector-button",
    "first",
    "first-child",
    "first-letter",
    "first-line",
    "first-of-type",
    "focus",
    "focus-visible",
    "focus-within",
    "fullscreen",
    "has",
    "host",
    "host-context",
    "hover",
    "in-range",
    "indeterminate",
    "invalid",
    "is",
    "lang",
    "last-child",
    "last-of-type",
    "left",
    "link",
    "marker",
    "modal",
    "not",
    "nth-child",
    "nth-last-child",
    "nth-last-of-type",
    "nth-of-type",
    "only-child",
    "only-of-type",
    "optional",
    "out-of-range",
    "part",
    "placeholder",
    "placeholder-shown",
    "read-only",
    "read-write",
    "required",
    "right",
    "root",
    "scope",
    "selection",
    "slotted",
    "target",
    "target-text",
    "valid",
    "visited",
    "where"
].map((name)=>({
        type: "class",
        label: name
    }));
const values = /*@__PURE__*/ [
    "above",
    "absolute",
    "activeborder",
    "additive",
    "activecaption",
    "after-white-space",
    "ahead",
    "alias",
    "all",
    "all-scroll",
    "alphabetic",
    "alternate",
    "always",
    "antialiased",
    "appworkspace",
    "asterisks",
    "attr",
    "auto",
    "auto-flow",
    "avoid",
    "avoid-column",
    "avoid-page",
    "avoid-region",
    "axis-pan",
    "background",
    "backwards",
    "baseline",
    "below",
    "bidi-override",
    "blink",
    "block",
    "block-axis",
    "bold",
    "bolder",
    "border",
    "border-box",
    "both",
    "bottom",
    "break",
    "break-all",
    "break-word",
    "bullets",
    "button",
    "button-bevel",
    "buttonface",
    "buttonhighlight",
    "buttonshadow",
    "buttontext",
    "calc",
    "capitalize",
    "caps-lock-indicator",
    "caption",
    "captiontext",
    "caret",
    "cell",
    "center",
    "checkbox",
    "circle",
    "cjk-decimal",
    "clear",
    "clip",
    "close-quote",
    "col-resize",
    "collapse",
    "color",
    "color-burn",
    "color-dodge",
    "column",
    "column-reverse",
    "compact",
    "condensed",
    "contain",
    "content",
    "contents",
    "content-box",
    "context-menu",
    "continuous",
    "copy",
    "counter",
    "counters",
    "cover",
    "crop",
    "cross",
    "crosshair",
    "currentcolor",
    "cursive",
    "cyclic",
    "darken",
    "dashed",
    "decimal",
    "decimal-leading-zero",
    "default",
    "default-button",
    "dense",
    "destination-atop",
    "destination-in",
    "destination-out",
    "destination-over",
    "difference",
    "disc",
    "discard",
    "disclosure-closed",
    "disclosure-open",
    "document",
    "dot-dash",
    "dot-dot-dash",
    "dotted",
    "double",
    "down",
    "e-resize",
    "ease",
    "ease-in",
    "ease-in-out",
    "ease-out",
    "element",
    "ellipse",
    "ellipsis",
    "embed",
    "end",
    "ethiopic-abegede-gez",
    "ethiopic-halehame-aa-er",
    "ethiopic-halehame-gez",
    "ew-resize",
    "exclusion",
    "expanded",
    "extends",
    "extra-condensed",
    "extra-expanded",
    "fantasy",
    "fast",
    "fill",
    "fill-box",
    "fixed",
    "flat",
    "flex",
    "flex-end",
    "flex-start",
    "footnotes",
    "forwards",
    "from",
    "geometricPrecision",
    "graytext",
    "grid",
    "groove",
    "hand",
    "hard-light",
    "help",
    "hidden",
    "hide",
    "higher",
    "highlight",
    "highlighttext",
    "horizontal",
    "hsl",
    "hsla",
    "hue",
    "icon",
    "ignore",
    "inactiveborder",
    "inactivecaption",
    "inactivecaptiontext",
    "infinite",
    "infobackground",
    "infotext",
    "inherit",
    "initial",
    "inline",
    "inline-axis",
    "inline-block",
    "inline-flex",
    "inline-grid",
    "inline-table",
    "inset",
    "inside",
    "intrinsic",
    "invert",
    "italic",
    "justify",
    "keep-all",
    "landscape",
    "large",
    "larger",
    "left",
    "level",
    "lighter",
    "lighten",
    "line-through",
    "linear",
    "linear-gradient",
    "lines",
    "list-item",
    "listbox",
    "listitem",
    "local",
    "logical",
    "loud",
    "lower",
    "lower-hexadecimal",
    "lower-latin",
    "lower-norwegian",
    "lowercase",
    "ltr",
    "luminosity",
    "manipulation",
    "match",
    "matrix",
    "matrix3d",
    "medium",
    "menu",
    "menutext",
    "message-box",
    "middle",
    "min-intrinsic",
    "mix",
    "monospace",
    "move",
    "multiple",
    "multiple_mask_images",
    "multiply",
    "n-resize",
    "narrower",
    "ne-resize",
    "nesw-resize",
    "no-close-quote",
    "no-drop",
    "no-open-quote",
    "no-repeat",
    "none",
    "normal",
    "not-allowed",
    "nowrap",
    "ns-resize",
    "numbers",
    "numeric",
    "nw-resize",
    "nwse-resize",
    "oblique",
    "opacity",
    "open-quote",
    "optimizeLegibility",
    "optimizeSpeed",
    "outset",
    "outside",
    "outside-shape",
    "overlay",
    "overline",
    "padding",
    "padding-box",
    "painted",
    "page",
    "paused",
    "perspective",
    "pinch-zoom",
    "plus-darker",
    "plus-lighter",
    "pointer",
    "polygon",
    "portrait",
    "pre",
    "pre-line",
    "pre-wrap",
    "preserve-3d",
    "progress",
    "push-button",
    "radial-gradient",
    "radio",
    "read-only",
    "read-write",
    "read-write-plaintext-only",
    "rectangle",
    "region",
    "relative",
    "repeat",
    "repeating-linear-gradient",
    "repeating-radial-gradient",
    "repeat-x",
    "repeat-y",
    "reset",
    "reverse",
    "rgb",
    "rgba",
    "ridge",
    "right",
    "rotate",
    "rotate3d",
    "rotateX",
    "rotateY",
    "rotateZ",
    "round",
    "row",
    "row-resize",
    "row-reverse",
    "rtl",
    "run-in",
    "running",
    "s-resize",
    "sans-serif",
    "saturation",
    "scale",
    "scale3d",
    "scaleX",
    "scaleY",
    "scaleZ",
    "screen",
    "scroll",
    "scrollbar",
    "scroll-position",
    "se-resize",
    "self-start",
    "self-end",
    "semi-condensed",
    "semi-expanded",
    "separate",
    "serif",
    "show",
    "single",
    "skew",
    "skewX",
    "skewY",
    "skip-white-space",
    "slide",
    "slider-horizontal",
    "slider-vertical",
    "sliderthumb-horizontal",
    "sliderthumb-vertical",
    "slow",
    "small",
    "small-caps",
    "small-caption",
    "smaller",
    "soft-light",
    "solid",
    "source-atop",
    "source-in",
    "source-out",
    "source-over",
    "space",
    "space-around",
    "space-between",
    "space-evenly",
    "spell-out",
    "square",
    "start",
    "static",
    "status-bar",
    "stretch",
    "stroke",
    "stroke-box",
    "sub",
    "subpixel-antialiased",
    "svg_masks",
    "super",
    "sw-resize",
    "symbolic",
    "symbols",
    "system-ui",
    "table",
    "table-caption",
    "table-cell",
    "table-column",
    "table-column-group",
    "table-footer-group",
    "table-header-group",
    "table-row",
    "table-row-group",
    "text",
    "text-bottom",
    "text-top",
    "textarea",
    "textfield",
    "thick",
    "thin",
    "threeddarkshadow",
    "threedface",
    "threedhighlight",
    "threedlightshadow",
    "threedshadow",
    "to",
    "top",
    "transform",
    "translate",
    "translate3d",
    "translateX",
    "translateY",
    "translateZ",
    "transparent",
    "ultra-condensed",
    "ultra-expanded",
    "underline",
    "unidirectional-pan",
    "unset",
    "up",
    "upper-latin",
    "uppercase",
    "url",
    "var",
    "vertical",
    "vertical-text",
    "view-box",
    "visible",
    "visibleFill",
    "visiblePainted",
    "visibleStroke",
    "visual",
    "w-resize",
    "wait",
    "wave",
    "wider",
    "window",
    "windowframe",
    "windowtext",
    "words",
    "wrap",
    "wrap-reverse",
    "x-large",
    "x-small",
    "xor",
    "xx-large",
    "xx-small"
].map((name)=>({
        type: "keyword",
        label: name
    })).concat(/*@__PURE__*/ [
    "aliceblue",
    "antiquewhite",
    "aqua",
    "aquamarine",
    "azure",
    "beige",
    "bisque",
    "black",
    "blanchedalmond",
    "blue",
    "blueviolet",
    "brown",
    "burlywood",
    "cadetblue",
    "chartreuse",
    "chocolate",
    "coral",
    "cornflowerblue",
    "cornsilk",
    "crimson",
    "cyan",
    "darkblue",
    "darkcyan",
    "darkgoldenrod",
    "darkgray",
    "darkgreen",
    "darkkhaki",
    "darkmagenta",
    "darkolivegreen",
    "darkorange",
    "darkorchid",
    "darkred",
    "darksalmon",
    "darkseagreen",
    "darkslateblue",
    "darkslategray",
    "darkturquoise",
    "darkviolet",
    "deeppink",
    "deepskyblue",
    "dimgray",
    "dodgerblue",
    "firebrick",
    "floralwhite",
    "forestgreen",
    "fuchsia",
    "gainsboro",
    "ghostwhite",
    "gold",
    "goldenrod",
    "gray",
    "grey",
    "green",
    "greenyellow",
    "honeydew",
    "hotpink",
    "indianred",
    "indigo",
    "ivory",
    "khaki",
    "lavender",
    "lavenderblush",
    "lawngreen",
    "lemonchiffon",
    "lightblue",
    "lightcoral",
    "lightcyan",
    "lightgoldenrodyellow",
    "lightgray",
    "lightgreen",
    "lightpink",
    "lightsalmon",
    "lightseagreen",
    "lightskyblue",
    "lightslategray",
    "lightsteelblue",
    "lightyellow",
    "lime",
    "limegreen",
    "linen",
    "magenta",
    "maroon",
    "mediumaquamarine",
    "mediumblue",
    "mediumorchid",
    "mediumpurple",
    "mediumseagreen",
    "mediumslateblue",
    "mediumspringgreen",
    "mediumturquoise",
    "mediumvioletred",
    "midnightblue",
    "mintcream",
    "mistyrose",
    "moccasin",
    "navajowhite",
    "navy",
    "oldlace",
    "olive",
    "olivedrab",
    "orange",
    "orangered",
    "orchid",
    "palegoldenrod",
    "palegreen",
    "paleturquoise",
    "palevioletred",
    "papayawhip",
    "peachpuff",
    "peru",
    "pink",
    "plum",
    "powderblue",
    "purple",
    "rebeccapurple",
    "red",
    "rosybrown",
    "royalblue",
    "saddlebrown",
    "salmon",
    "sandybrown",
    "seagreen",
    "seashell",
    "sienna",
    "silver",
    "skyblue",
    "slateblue",
    "slategray",
    "snow",
    "springgreen",
    "steelblue",
    "tan",
    "teal",
    "thistle",
    "tomato",
    "turquoise",
    "violet",
    "wheat",
    "white",
    "whitesmoke",
    "yellow",
    "yellowgreen"
].map((name)=>({
        type: "constant",
        label: name
    })));
const tags = /*@__PURE__*/ [
    "a",
    "abbr",
    "address",
    "article",
    "aside",
    "b",
    "bdi",
    "bdo",
    "blockquote",
    "body",
    "br",
    "button",
    "canvas",
    "caption",
    "cite",
    "code",
    "col",
    "colgroup",
    "dd",
    "del",
    "details",
    "dfn",
    "dialog",
    "div",
    "dl",
    "dt",
    "em",
    "figcaption",
    "figure",
    "footer",
    "form",
    "header",
    "hgroup",
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "hr",
    "html",
    "i",
    "iframe",
    "img",
    "input",
    "ins",
    "kbd",
    "label",
    "legend",
    "li",
    "main",
    "meter",
    "nav",
    "ol",
    "output",
    "p",
    "pre",
    "ruby",
    "section",
    "select",
    "small",
    "source",
    "span",
    "strong",
    "sub",
    "summary",
    "sup",
    "table",
    "tbody",
    "td",
    "template",
    "textarea",
    "tfoot",
    "th",
    "thead",
    "tr",
    "u",
    "ul"
].map((name)=>({
        type: "type",
        label: name
    }));
const atRules = /*@__PURE__*/ [
    "@charset",
    "@color-profile",
    "@container",
    "@counter-style",
    "@font-face",
    "@font-feature-values",
    "@font-palette-values",
    "@import",
    "@keyframes",
    "@layer",
    "@media",
    "@namespace",
    "@page",
    "@position-try",
    "@property",
    "@scope",
    "@starting-style",
    "@supports",
    "@view-transition"
].map((label)=>({
        type: "keyword",
        label
    }));
const identifier = /^(\w[\w-]*|-\w[\w-]*|)$/, variable = /^-(-[\w-]*)?$/;
function isVarArg(node, doc) {
    var _a;
    if (node.name == "(" || node.type.isError) node = node.parent || node;
    if (node.name != "ArgList") return false;
    let callee = (_a = node.parent) === null || _a === void 0 ? void 0 : _a.firstChild;
    if ((callee === null || callee === void 0 ? void 0 : callee.name) != "Callee") return false;
    return doc.sliceString(callee.from, callee.to) == "var";
}
const VariablesByNode = /*@__PURE__*/ new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeWeakMap"]();
const declSelector = [
    "Declaration"
];
function astTop(node) {
    for(let cur = node;;){
        if (cur.type.isTop) return cur;
        if (!(cur = cur.parent)) return node;
    }
}
function variableNames(doc, node, isVariable) {
    if (node.to - node.from > 4096) {
        let known = VariablesByNode.get(node);
        if (known) return known;
        let result = [], seen = new Set, cursor = node.cursor(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IterMode"].IncludeAnonymous);
        if (cursor.firstChild()) do {
            for (let option of variableNames(doc, cursor.node, isVariable))if (!seen.has(option.label)) {
                seen.add(option.label);
                result.push(option);
            }
        }while (cursor.nextSibling())
        VariablesByNode.set(node, result);
        return result;
    } else {
        let result = [], seen = new Set;
        node.cursor().iterate((node)=>{
            var _a;
            if (isVariable(node) && node.matchContext(declSelector) && ((_a = node.node.nextSibling) === null || _a === void 0 ? void 0 : _a.name) == ":") {
                let name = doc.sliceString(node.from, node.to);
                if (!seen.has(name)) {
                    seen.add(name);
                    result.push({
                        label: name,
                        type: "variable"
                    });
                }
            }
        });
        return result;
    }
}
/**
Create a completion source for a CSS dialect, providing a
predicate for determining what kind of syntax node can act as a
completable variable. This is used by language modes like Sass and
Less to reuse this package's completion logic.
*/ const defineCSSCompletionSource = (isVariable)=>(context)=>{
        let { state, pos } = context, node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["syntaxTree"])(state).resolveInner(pos, -1);
        let isDash = node.type.isError && node.from == node.to - 1 && state.doc.sliceString(node.from, node.to) == "-";
        if (node.name == "PropertyName" || (isDash || node.name == "TagName") && /^(Block|Styles)$/.test(node.resolve(node.to).name)) return {
            from: node.from,
            options: properties(),
            validFor: identifier
        };
        if (node.name == "ValueName") return {
            from: node.from,
            options: values,
            validFor: identifier
        };
        if (node.name == "PseudoClassName") return {
            from: node.from,
            options: pseudoClasses,
            validFor: identifier
        };
        if (isVariable(node) || (context.explicit || isDash) && isVarArg(node, state.doc)) return {
            from: isVariable(node) || isDash ? node.from : pos,
            options: variableNames(state.doc, astTop(node), isVariable),
            validFor: variable
        };
        if (node.name == "TagName") {
            for(let { parent } = node; parent; parent = parent.parent)if (parent.name == "Block") return {
                from: node.from,
                options: properties(),
                validFor: identifier
            };
            return {
                from: node.from,
                options: tags,
                validFor: identifier
            };
        }
        if (node.name == "AtKeyword") return {
            from: node.from,
            options: atRules,
            validFor: identifier
        };
        if (!context.explicit) return null;
        let above = node.resolve(pos), before = above.childBefore(pos);
        if (before && before.name == ":" && above.name == "PseudoClassSelector") return {
            from: pos,
            options: pseudoClasses,
            validFor: identifier
        };
        if (before && before.name == ":" && above.name == "Declaration" || above.name == "ArgList") return {
            from: pos,
            options: values,
            validFor: identifier
        };
        if (above.name == "Block" || above.name == "Styles") return {
            from: pos,
            options: properties(),
            validFor: identifier
        };
        return null;
    };
/**
CSS property, variable, and value keyword completion source.
*/ const cssCompletionSource = /*@__PURE__*/ defineCSSCompletionSource((n)=>n.name == "VariableName");
/**
A language provider based on the [Lezer CSS
parser](https://github.com/lezer-parser/css), extended with
highlighting and indentation information.
*/ const cssLanguage = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LRLanguage"].define({
    name: "css",
    parser: /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$css$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parser"].configure({
        props: [
            /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["indentNodeProp"].add({
                Declaration: /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["continuedIndent"])()
            }),
            /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["foldNodeProp"].add({
                "Block KeyframeList": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["foldInside"]
            })
        ]
    }),
    languageData: {
        commentTokens: {
            block: {
                open: "/*",
                close: "*/"
            }
        },
        indentOnInput: /^\s*\}$/,
        wordChars: "-"
    }
});
/**
Language support for CSS.
*/ function css() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LanguageSupport"](cssLanguage, cssLanguage.data.of({
        autocomplete: cssCompletionSource
    }));
}
;
}}),
"[project]/node_modules/@lezer/javascript/dist/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "parser": (()=>parser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/lr/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/highlight/dist/index.js [app-client] (ecmascript)");
;
;
// This file was generated by lezer-generator. You probably shouldn't edit it.
const noSemi = 316, noSemiType = 317, incdec = 1, incdecPrefix = 2, questionDot = 3, JSXStartTag = 4, insertSemi = 318, spaces = 320, newline = 321, LineComment = 5, BlockComment = 6, Dialect_jsx = 0;
/* Hand-written tokenizers for JavaScript tokens that can't be
   expressed by lezer's built-in tokenizer. */ const space = [
    9,
    10,
    11,
    12,
    13,
    32,
    133,
    160,
    5760,
    8192,
    8193,
    8194,
    8195,
    8196,
    8197,
    8198,
    8199,
    8200,
    8201,
    8202,
    8232,
    8233,
    8239,
    8287,
    12288
];
const braceR = 125, semicolon = 59, slash = 47, star = 42, plus = 43, minus = 45, lt = 60, comma = 44, question = 63, dot = 46, bracketL = 91;
const trackNewline = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContextTracker"]({
    start: false,
    shift (context, term) {
        return term == LineComment || term == BlockComment || term == spaces ? context : term == newline;
    },
    strict: false
});
const insertSemicolon = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"]((input, stack)=>{
    let { next } = input;
    if (next == braceR || next == -1 || stack.context) input.acceptToken(insertSemi);
}, {
    contextual: true,
    fallback: true
});
const noSemicolon = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"]((input, stack)=>{
    let { next } = input, after;
    if (space.indexOf(next) > -1) return;
    if (next == slash && ((after = input.peek(1)) == slash || after == star)) return;
    if (next != braceR && next != semicolon && next != -1 && !stack.context) input.acceptToken(noSemi);
}, {
    contextual: true
});
const noSemicolonType = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"]((input, stack)=>{
    if (input.next == bracketL && !stack.context) input.acceptToken(noSemiType);
}, {
    contextual: true
});
const operatorToken = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"]((input, stack)=>{
    let { next } = input;
    if (next == plus || next == minus) {
        input.advance();
        if (next == input.next) {
            input.advance();
            let mayPostfix = !stack.context && stack.canShift(incdec);
            input.acceptToken(mayPostfix ? incdec : incdecPrefix);
        }
    } else if (next == question && input.peek(1) == dot) {
        input.advance();
        input.advance();
        if (input.next < 48 || input.next > 57) input.acceptToken(questionDot);
    }
}, {
    contextual: true
});
function identifierChar(ch, start) {
    return ch >= 65 && ch <= 90 || ch >= 97 && ch <= 122 || ch == 95 || ch >= 192 || !start && ch >= 48 && ch <= 57;
}
const jsx = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExternalTokenizer"]((input, stack)=>{
    if (input.next != lt || !stack.dialectEnabled(Dialect_jsx)) return;
    input.advance();
    if (input.next == slash) return;
    // Scan for an identifier followed by a comma or 'extends', don't
    // treat this as a start tag if present.
    let back = 0;
    while(space.indexOf(input.next) > -1){
        input.advance();
        back++;
    }
    if (identifierChar(input.next, true)) {
        input.advance();
        back++;
        while(identifierChar(input.next, false)){
            input.advance();
            back++;
        }
        while(space.indexOf(input.next) > -1){
            input.advance();
            back++;
        }
        if (input.next == comma) return;
        for(let i = 0;; i++){
            if (i == 7) {
                if (!identifierChar(input.next, true)) return;
                break;
            }
            if (input.next != "extends".charCodeAt(i)) break;
            input.advance();
            back++;
        }
    }
    input.acceptToken(JSXStartTag, -back);
});
const jsHighlight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["styleTags"])({
    "get set async static": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].modifier,
    "for while do if else switch try catch finally return throw break continue default case defer": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].controlKeyword,
    "in of await yield void typeof delete instanceof as satisfies": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].operatorKeyword,
    "let var const using function class extends": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definitionKeyword,
    "import export from": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].moduleKeyword,
    "with debugger new": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].keyword,
    TemplateString: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].special(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].string),
    super: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].atom,
    BooleanLiteral: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].bool,
    this: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].self,
    null: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].null,
    Star: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].modifier,
    VariableName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].variableName,
    "CallExpression/VariableName TaggedTemplateExpression/VariableName": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].function(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].variableName),
    VariableDefinition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definition(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].variableName),
    Label: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].labelName,
    PropertyName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].propertyName,
    PrivatePropertyName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].special(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].propertyName),
    "CallExpression/MemberExpression/PropertyName": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].function(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].propertyName),
    "FunctionDeclaration/VariableDefinition": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].function(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definition(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].variableName)),
    "ClassDeclaration/VariableDefinition": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definition(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].className),
    "NewExpression/VariableName": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].className,
    PropertyDefinition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definition(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].propertyName),
    PrivatePropertyDefinition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definition(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].special(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].propertyName)),
    UpdateOp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].updateOperator,
    "LineComment Hashbang": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].lineComment,
    BlockComment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].blockComment,
    Number: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].number,
    String: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].string,
    Escape: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].escape,
    ArithOp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].arithmeticOperator,
    LogicOp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].logicOperator,
    BitOp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].bitwiseOperator,
    CompareOp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].compareOperator,
    RegExp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].regexp,
    Equals: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definitionOperator,
    Arrow: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].function(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].punctuation),
    ": Spread": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].punctuation,
    "( )": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].paren,
    "[ ]": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].squareBracket,
    "{ }": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].brace,
    "InterpolationStart InterpolationEnd": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].special(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].brace),
    ".": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].derefOperator,
    ", ;": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].separator,
    "@": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].meta,
    TypeName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].typeName,
    TypeDefinition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definition(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].typeName),
    "type enum interface implements namespace module declare": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].definitionKeyword,
    "abstract global Privacy readonly override": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].modifier,
    "is keyof unique infer asserts": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].operatorKeyword,
    JSXAttributeValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].attributeValue,
    JSXText: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].content,
    "JSXStartTag JSXStartCloseTag JSXSelfCloseEndTag JSXEndTag": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].angleBracket,
    "JSXIdentifier JSXNameSpacedName": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].tagName,
    "JSXAttribute/JSXIdentifier JSXAttribute/JSXNameSpacedName": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].attributeName,
    "JSXBuiltin/JSXIdentifier": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].standard(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$highlight$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tags"].tagName)
});
// This file was generated by lezer-generator. You probably shouldn't edit it.
const spec_identifier = {
    __proto__: null,
    export: 20,
    as: 25,
    from: 33,
    default: 36,
    async: 41,
    function: 42,
    in: 52,
    out: 55,
    const: 56,
    extends: 60,
    this: 64,
    true: 72,
    false: 72,
    null: 84,
    void: 88,
    typeof: 92,
    super: 108,
    new: 142,
    delete: 154,
    yield: 163,
    await: 167,
    class: 172,
    public: 235,
    private: 235,
    protected: 235,
    readonly: 237,
    instanceof: 256,
    satisfies: 259,
    import: 292,
    keyof: 349,
    unique: 353,
    infer: 359,
    asserts: 395,
    is: 397,
    abstract: 417,
    implements: 419,
    type: 421,
    let: 424,
    var: 426,
    using: 429,
    interface: 435,
    enum: 439,
    namespace: 445,
    module: 447,
    declare: 451,
    global: 455,
    defer: 471,
    for: 476,
    of: 485,
    while: 488,
    with: 492,
    do: 496,
    if: 500,
    else: 502,
    switch: 506,
    case: 512,
    try: 518,
    catch: 522,
    finally: 526,
    return: 530,
    throw: 534,
    break: 538,
    continue: 542,
    debugger: 546
};
const spec_word = {
    __proto__: null,
    async: 129,
    get: 131,
    set: 133,
    declare: 195,
    public: 197,
    private: 197,
    protected: 197,
    static: 199,
    abstract: 201,
    override: 203,
    readonly: 209,
    accessor: 211,
    new: 401
};
const spec_LessThan = {
    __proto__: null,
    "<": 193
};
const parser = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LRParser"].deserialize({
    version: 14,
    states: "$FjQ%TQlOOO%[QlOOO'_QpOOP(lO`OOO*zQ!0MxO'#CiO+RO#tO'#CjO+aO&jO'#CjO+oO#@ItO'#DaO.QQlO'#DgO.bQlO'#DrO%[QlO'#DzO0fQlO'#ESOOQ!0Lf'#E['#E[O1PQ`O'#EXOOQO'#Ep'#EpOOQO'#Il'#IlO1XQ`O'#GsO1dQ`O'#EoO1iQ`O'#EoO3hQ!0MxO'#JrO6[Q!0MxO'#JsO6uQ`O'#F]O6zQ,UO'#FtOOQ!0Lf'#Ff'#FfO7VO7dO'#FfO9XQMhO'#F|O9`Q`O'#F{OOQ!0Lf'#Js'#JsOOQ!0Lb'#Jr'#JrO9eQ`O'#GwOOQ['#K_'#K_O9pQ`O'#IYO9uQ!0LrO'#IZOOQ['#J`'#J`OOQ['#I_'#I_Q`QlOOQ`QlOOO9}Q!L^O'#DvO:UQlO'#EOO:]QlO'#EQO9kQ`O'#GsO:dQMhO'#CoO:rQ`O'#EnO:}Q`O'#EyO;hQMhO'#FeO;xQ`O'#GsOOQO'#K`'#K`O;}Q`O'#K`O<]Q`O'#G{O<]Q`O'#G|O<]Q`O'#HOO9kQ`O'#HRO=SQ`O'#HUO>kQ`O'#CeO>{Q`O'#HcO?TQ`O'#HiO?TQ`O'#HkO`QlO'#HmO?TQ`O'#HoO?TQ`O'#HrO?YQ`O'#HxO?_Q!0LsO'#IOO%[QlO'#IQO?jQ!0LsO'#ISO?uQ!0LsO'#IUO9uQ!0LrO'#IWO@QQ!0MxO'#CiOASQpO'#DlQOQ`OOO%[QlO'#EQOAjQ`O'#ETO:dQMhO'#EnOAuQ`O'#EnOBQQ!bO'#FeOOQ['#Cg'#CgOOQ!0Lb'#Dq'#DqOOQ!0Lb'#Jv'#JvO%[QlO'#JvOOQO'#Jy'#JyOOQO'#Ih'#IhOCQQpO'#EgOOQ!0Lb'#Ef'#EfOOQ!0Lb'#J}'#J}OC|Q!0MSO'#EgODWQpO'#EWOOQO'#Jx'#JxODlQpO'#JyOEyQpO'#EWODWQpO'#EgPFWO&2DjO'#CbPOOO)CD})CD}OOOO'#I`'#I`OFcO#tO,59UOOQ!0Lh,59U,59UOOOO'#Ia'#IaOFqO&jO,59UOGPQ!L^O'#DcOOOO'#Ic'#IcOGWO#@ItO,59{OOQ!0Lf,59{,59{OGfQlO'#IdOGyQ`O'#JtOIxQ!fO'#JtO+}QlO'#JtOJPQ`O,5:ROJgQ`O'#EpOJtQ`O'#KTOKPQ`O'#KSOKPQ`O'#KSOKXQ`O,5;^OK^Q`O'#KROOQ!0Ln,5:^,5:^OKeQlO,5:^OMcQ!0MxO,5:fONSQ`O,5:nONmQ!0LrO'#KQONtQ`O'#KPO9eQ`O'#KPO! YQ`O'#KPO! bQ`O,5;]O! gQ`O'#KPO!#lQ!fO'#JsOOQ!0Lh'#Ci'#CiO%[QlO'#ESO!$[Q!fO,5:sOOQS'#Jz'#JzOOQO-E<j-E<jO9kQ`O,5=_O!$rQ`O,5=_O!$wQlO,5;ZO!&zQMhO'#EkO!(eQ`O,5;ZO!(jQlO'#DyO!(tQpO,5;dO!(|QpO,5;dO%[QlO,5;dOOQ['#FT'#FTOOQ['#FV'#FVO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eOOQ['#FZ'#FZO!)[QlO,5;tOOQ!0Lf,5;y,5;yOOQ!0Lf,5;z,5;zOOQ!0Lf,5;|,5;|O%[QlO'#IpO!+_Q!0LrO,5<iO%[QlO,5;eO!&zQMhO,5;eO!+|QMhO,5;eO!-nQMhO'#E^O%[QlO,5;wOOQ!0Lf,5;{,5;{O!-uQ,UO'#FjO!.rQ,UO'#KXO!.^Q,UO'#KXO!.yQ,UO'#KXOOQO'#KX'#KXO!/_Q,UO,5<SOOOW,5<`,5<`O!/pQlO'#FvOOOW'#Io'#IoO7VO7dO,5<QO!/wQ,UO'#FxOOQ!0Lf,5<Q,5<QO!0hQ$IUO'#CyOOQ!0Lh'#C}'#C}O!0{O#@ItO'#DRO!1iQMjO,5<eO!1pQ`O,5<hO!3]Q(CWO'#GXO!3jQ`O'#GYO!3oQ`O'#GYO!5_Q(CWO'#G^O!6dQpO'#GbOOQO'#Gn'#GnO!,TQMhO'#GmOOQO'#Gp'#GpO!,TQMhO'#GoO!7VQ$IUO'#JlOOQ!0Lh'#Jl'#JlO!7aQ`O'#JkO!7oQ`O'#JjO!7wQ`O'#CuOOQ!0Lh'#C{'#C{O!8YQ`O'#C}OOQ!0Lh'#DV'#DVOOQ!0Lh'#DX'#DXO!8_Q`O,5<eO1SQ`O'#DZO!,TQMhO'#GPO!,TQMhO'#GRO!8gQ`O'#GTO!8lQ`O'#GUO!3oQ`O'#G[O!,TQMhO'#GaO<]Q`O'#JkO!8qQ`O'#EqO!9`Q`O,5<gOOQ!0Lb'#Cr'#CrO!9hQ`O'#ErO!:bQpO'#EsOOQ!0Lb'#KR'#KRO!:iQ!0LrO'#KaO9uQ!0LrO,5=cO`QlO,5>tOOQ['#Jh'#JhOOQ[,5>u,5>uOOQ[-E<]-E<]O!<hQ!0MxO,5:bO!:]QpO,5:`O!?RQ!0MxO,5:jO%[QlO,5:jO!AiQ!0MxO,5:lOOQO,5@z,5@zO!BYQMhO,5=_O!BhQ!0LrO'#JiO9`Q`O'#JiO!ByQ!0LrO,59ZO!CUQpO,59ZO!C^QMhO,59ZO:dQMhO,59ZO!CiQ`O,5;ZO!CqQ`O'#HbO!DVQ`O'#KdO%[QlO,5;}O!:]QpO,5<PO!D_Q`O,5=zO!DdQ`O,5=zO!DiQ`O,5=zO!DwQ`O,5=zO9uQ!0LrO,5=zO<]Q`O,5=jOOQO'#Cy'#CyO!EOQpO,5=gO!EWQMhO,5=hO!EcQ`O,5=jO!EhQ!bO,5=mO!EpQ`O'#K`O?YQ`O'#HWO9kQ`O'#HYO!EuQ`O'#HYO:dQMhO'#H[O!EzQ`O'#H[OOQ[,5=p,5=pO!FPQ`O'#H]O!FbQ`O'#CoO!FgQ`O,59PO!FqQ`O,59PO!HvQlO,59POOQ[,59P,59PO!IWQ!0LrO,59PO%[QlO,59PO!KcQlO'#HeOOQ['#Hf'#HfOOQ['#Hg'#HgO`QlO,5=}O!KyQ`O,5=}O`QlO,5>TO`QlO,5>VO!LOQ`O,5>XO`QlO,5>ZO!LTQ`O,5>^O!LYQlO,5>dOOQ[,5>j,5>jO%[QlO,5>jO9uQ!0LrO,5>lOOQ[,5>n,5>nO#!dQ`O,5>nOOQ[,5>p,5>pO#!dQ`O,5>pOOQ[,5>r,5>rO##QQpO'#D_O%[QlO'#JvO##sQpO'#JvO##}QpO'#DmO#$`QpO'#DmO#&qQlO'#DmO#&xQ`O'#JuO#'QQ`O,5:WO#'VQ`O'#EtO#'eQ`O'#KUO#'mQ`O,5;_O#'rQpO'#DmO#(PQpO'#EVOOQ!0Lf,5:o,5:oO%[QlO,5:oO#(WQ`O,5:oO?YQ`O,5;YO!CUQpO,5;YO!C^QMhO,5;YO:dQMhO,5;YO#(`Q`O,5@bO#(eQ07dO,5:sOOQO-E<f-E<fO#)kQ!0MSO,5;RODWQpO,5:rO#)uQpO,5:rODWQpO,5;RO!ByQ!0LrO,5:rOOQ!0Lb'#Ej'#EjOOQO,5;R,5;RO%[QlO,5;RO#*SQ!0LrO,5;RO#*_Q!0LrO,5;RO!CUQpO,5:rOOQO,5;X,5;XO#*mQ!0LrO,5;RPOOO'#I^'#I^P#+RO&2DjO,58|POOO,58|,58|OOOO-E<^-E<^OOQ!0Lh1G.p1G.pOOOO-E<_-E<_OOOO,59},59}O#+^Q!bO,59}OOOO-E<a-E<aOOQ!0Lf1G/g1G/gO#+cQ!fO,5?OO+}QlO,5?OOOQO,5?U,5?UO#+mQlO'#IdOOQO-E<b-E<bO#+zQ`O,5@`O#,SQ!fO,5@`O#,ZQ`O,5@nOOQ!0Lf1G/m1G/mO%[QlO,5@oO#,cQ`O'#IjOOQO-E<h-E<hO#,ZQ`O,5@nOOQ!0Lb1G0x1G0xOOQ!0Ln1G/x1G/xOOQ!0Ln1G0Y1G0YO%[QlO,5@lO#,wQ!0LrO,5@lO#-YQ!0LrO,5@lO#-aQ`O,5@kO9eQ`O,5@kO#-iQ`O,5@kO#-wQ`O'#ImO#-aQ`O,5@kOOQ!0Lb1G0w1G0wO!(tQpO,5:uO!)PQpO,5:uOOQS,5:w,5:wO#.iQdO,5:wO#.qQMhO1G2yO9kQ`O1G2yOOQ!0Lf1G0u1G0uO#/PQ!0MxO1G0uO#0UQ!0MvO,5;VOOQ!0Lh'#GW'#GWO#0rQ!0MzO'#JlO!$wQlO1G0uO#2}Q!fO'#JwO%[QlO'#JwO#3XQ`O,5:eOOQ!0Lh'#D_'#D_OOQ!0Lf1G1O1G1OO%[QlO1G1OOOQ!0Lf1G1f1G1fO#3^Q`O1G1OO#5rQ!0MxO1G1PO#5yQ!0MxO1G1PO#8aQ!0MxO1G1PO#8hQ!0MxO1G1PO#;OQ!0MxO1G1PO#=fQ!0MxO1G1PO#=mQ!0MxO1G1PO#=tQ!0MxO1G1PO#@[Q!0MxO1G1PO#@cQ!0MxO1G1PO#BpQ?MtO'#CiO#DkQ?MtO1G1`O#DrQ?MtO'#JsO#EVQ!0MxO,5?[OOQ!0Lb-E<n-E<nO#GdQ!0MxO1G1PO#HaQ!0MzO1G1POOQ!0Lf1G1P1G1PO#IdQMjO'#J|O#InQ`O,5:xO#IsQ!0MxO1G1cO#JgQ,UO,5<WO#JoQ,UO,5<XO#JwQ,UO'#FoO#K`Q`O'#FnOOQO'#KY'#KYOOQO'#In'#InO#KeQ,UO1G1nOOQ!0Lf1G1n1G1nOOOW1G1y1G1yO#KvQ?MtO'#JrO#LQQ`O,5<bO!)[QlO,5<bOOOW-E<m-E<mOOQ!0Lf1G1l1G1lO#LVQpO'#KXOOQ!0Lf,5<d,5<dO#L_QpO,5<dO#LdQMhO'#DTOOOO'#Ib'#IbO#LkO#@ItO,59mOOQ!0Lh,59m,59mO%[QlO1G2PO!8lQ`O'#IrO#LvQ`O,5<zOOQ!0Lh,5<w,5<wO!,TQMhO'#IuO#MdQMjO,5=XO!,TQMhO'#IwO#NVQMjO,5=ZO!&zQMhO,5=]OOQO1G2S1G2SO#NaQ!dO'#CrO#NtQ(CWO'#ErO$ yQpO'#GbO$!aQ!dO,5<sO$!hQ`O'#K[O9eQ`O'#K[O$!vQ`O,5<uO!,TQMhO,5<tO$!{Q`O'#GZO$#^Q`O,5<tO$#cQ!dO'#GWO$#pQ!dO'#K]O$#zQ`O'#K]O!&zQMhO'#K]O$$PQ`O,5<xO$$UQlO'#JvO$$`QpO'#GcO#$`QpO'#GcO$$qQ`O'#GgO!3oQ`O'#GkO$$vQ!0LrO'#ItO$%RQpO,5<|OOQ!0Lp,5<|,5<|O$%YQpO'#GcO$%gQpO'#GdO$%xQpO'#GdO$%}QMjO,5=XO$&_QMjO,5=ZOOQ!0Lh,5=^,5=^O!,TQMhO,5@VO!,TQMhO,5@VO$&oQ`O'#IyO$'TQ`O,5@UO$']Q`O,59aOOQ!0Lh,59i,59iO$'bQ`O,5@VO$(bQ$IYO,59uOOQ!0Lh'#Jp'#JpO$)TQMjO,5<kO$)vQMjO,5<mO@zQ`O,5<oOOQ!0Lh,5<p,5<pO$*QQ`O,5<vO$*VQMjO,5<{O$*gQ`O'#KPO!$wQlO1G2RO$*lQ`O1G2RO9eQ`O'#KSO9eQ`O'#EtO%[QlO'#EtO9eQ`O'#I{O$*qQ!0LrO,5@{OOQ[1G2}1G2}OOQ[1G4`1G4`OOQ!0Lf1G/|1G/|OOQ!0Lf1G/z1G/zO$,sQ!0MxO1G0UOOQ[1G2y1G2yO!&zQMhO1G2yO%[QlO1G2yO#.tQ`O1G2yO$.wQMhO'#EkOOQ!0Lb,5@T,5@TO$/UQ!0LrO,5@TOOQ[1G.u1G.uO!ByQ!0LrO1G.uO!CUQpO1G.uO!C^QMhO1G.uO$/gQ`O1G0uO$/lQ`O'#CiO$/wQ`O'#KeO$0PQ`O,5=|O$0UQ`O'#KeO$0ZQ`O'#KeO$0iQ`O'#JRO$0wQ`O,5AOO$1PQ!fO1G1iOOQ!0Lf1G1k1G1kO9kQ`O1G3fO@zQ`O1G3fO$1WQ`O1G3fO$1]Q`O1G3fO!DiQ`O1G3fO9uQ!0LrO1G3fOOQ[1G3f1G3fO!EcQ`O1G3UO!&zQMhO1G3RO$1bQ`O1G3ROOQ[1G3S1G3SO!&zQMhO1G3SO$1gQ`O1G3SO$1oQpO'#HQOOQ[1G3U1G3UO!6_QpO'#I}O!EhQ!bO1G3XOOQ[1G3X1G3XOOQ[,5=r,5=rO$1wQMhO,5=tO9kQ`O,5=tO$$qQ`O,5=vO9`Q`O,5=vO!CUQpO,5=vO!C^QMhO,5=vO:dQMhO,5=vO$2VQ`O'#KcO$2bQ`O,5=wOOQ[1G.k1G.kO$2gQ!0LrO1G.kO@zQ`O1G.kO$2rQ`O1G.kO9uQ!0LrO1G.kO$4zQ!fO,5AQO$5XQ`O,5AQO9eQ`O,5AQO$5dQlO,5>PO$5kQ`O,5>POOQ[1G3i1G3iO`QlO1G3iOOQ[1G3o1G3oOOQ[1G3q1G3qO?TQ`O1G3sO$5pQlO1G3uO$9tQlO'#HtOOQ[1G3x1G3xO$:RQ`O'#HzO?YQ`O'#H|OOQ[1G4O1G4OO$:ZQlO1G4OO9uQ!0LrO1G4UOOQ[1G4W1G4WOOQ!0Lb'#G_'#G_O9uQ!0LrO1G4YO9uQ!0LrO1G4[O$>bQ`O,5@bO!)[QlO,5;`O9eQ`O,5;`O?YQ`O,5:XO!)[QlO,5:XO!CUQpO,5:XO$>gQ?MtO,5:XOOQO,5;`,5;`O$>qQpO'#IeO$?XQ`O,5@aOOQ!0Lf1G/r1G/rO$?aQpO'#IkO$?kQ`O,5@pOOQ!0Lb1G0y1G0yO#$`QpO,5:XOOQO'#Ig'#IgO$?sQpO,5:qOOQ!0Ln,5:q,5:qO#(ZQ`O1G0ZOOQ!0Lf1G0Z1G0ZO%[QlO1G0ZOOQ!0Lf1G0t1G0tO?YQ`O1G0tO!CUQpO1G0tO!C^QMhO1G0tOOQ!0Lb1G5|1G5|O!ByQ!0LrO1G0^OOQO1G0m1G0mO%[QlO1G0mO$?zQ!0LrO1G0mO$@VQ!0LrO1G0mO!CUQpO1G0^ODWQpO1G0^O$@eQ!0LrO1G0mOOQO1G0^1G0^O$@yQ!0MxO1G0mPOOO-E<[-E<[POOO1G.h1G.hOOOO1G/i1G/iO$ATQ!bO,5<iO$A]Q!fO1G4jOOQO1G4p1G4pO%[QlO,5?OO$AgQ`O1G5zO$AoQ`O1G6YO$AwQ!fO1G6ZO9eQ`O,5?UO$BRQ!0MxO1G6WO%[QlO1G6WO$BcQ!0LrO1G6WO$BtQ`O1G6VO$BtQ`O1G6VO9eQ`O1G6VO$B|Q`O,5?XO9eQ`O,5?XOOQO,5?X,5?XO$CbQ`O,5?XO$*gQ`O,5?XOOQO-E<k-E<kOOQS1G0a1G0aOOQS1G0c1G0cO#.lQ`O1G0cOOQ[7+(e7+(eO!&zQMhO7+(eO%[QlO7+(eO$CpQ`O7+(eO$C{QMhO7+(eO$DZQ!0MzO,5=XO$FfQ!0MzO,5=ZO$HqQ!0MzO,5=XO$KSQ!0MzO,5=ZO$MeQ!0MzO,59uO% jQ!0MzO,5<kO%#uQ!0MzO,5<mO%&QQ!0MzO,5<{OOQ!0Lf7+&a7+&aO%(cQ!0MxO7+&aO%)VQlO'#IfO%)dQ`O,5@cO%)lQ!fO,5@cOOQ!0Lf1G0P1G0PO%)vQ`O7+&jOOQ!0Lf7+&j7+&jO%){Q?MtO,5:fO%[QlO7+&zO%*VQ?MtO,5:bO%*dQ?MtO,5:jO%*nQ?MtO,5:lO%*xQMhO'#IiO%+SQ`O,5@hOOQ!0Lh1G0d1G0dOOQO1G1r1G1rOOQO1G1s1G1sO%+[Q!jO,5<ZO!)[QlO,5<YOOQO-E<l-E<lOOQ!0Lf7+'Y7+'YOOOW7+'e7+'eOOOW1G1|1G1|O%+gQ`O1G1|OOQ!0Lf1G2O1G2OOOOO,59o,59oO%+lQ!dO,59oOOOO-E<`-E<`OOQ!0Lh1G/X1G/XO%+sQ!0MxO7+'kOOQ!0Lh,5?^,5?^O%,gQMhO1G2fP%,nQ`O'#IrPOQ!0Lh-E<p-E<pO%-[QMjO,5?aOOQ!0Lh-E<s-E<sO%-}QMjO,5?cOOQ!0Lh-E<u-E<uO%.XQ!dO1G2wO%.`Q!dO'#CrO%.vQMhO'#KSO$$UQlO'#JvOOQ!0Lh1G2_1G2_O%.}Q`O'#IqO%/cQ`O,5@vO%/cQ`O,5@vO%/kQ`O,5@vO%/vQ`O,5@vOOQO1G2a1G2aO%0UQMjO1G2`O!,TQMhO1G2`O%0fQ(CWO'#IsO%0sQ`O,5@wO!&zQMhO,5@wO%0{Q!dO,5@wOOQ!0Lh1G2d1G2dO%3]Q!fO'#CiO%3gQ`O,5=POOQ!0Lb,5<},5<}O%3oQpO,5<}OOQ!0Lb,5=O,5=OOCwQ`O,5<}O%3zQpO,5<}OOQ!0Lb,5=R,5=RO$*gQ`O,5=VOOQO,5?`,5?`OOQO-E<r-E<rOOQ!0Lp1G2h1G2hO#$`QpO,5<}O$$UQlO,5=PO%4YQ`O,5=OO%4eQpO,5=OO!,TQMhO'#IuO%5_QMjO1G2sO!,TQMhO'#IwO%6QQMjO1G2uO%6[QMjO1G5qO%6fQMjO1G5qOOQO,5?e,5?eOOQO-E<w-E<wOOQO1G.{1G.{O!,TQMhO1G5qO!,TQMhO1G5qO!:]QpO,59wO%[QlO,59wOOQ!0Lh,5<j,5<jO%6sQ`O1G2ZO!,TQMhO1G2bO%6xQ!0MxO7+'mOOQ!0Lf7+'m7+'mO!$wQlO7+'mO%7lQ`O,5;`OOQ!0Lb,5?g,5?gOOQ!0Lb-E<y-E<yO%7qQ!dO'#K^O#(ZQ`O7+(eO4UQ!fO7+(eO$CsQ`O7+(eO%7{Q!0MvO'#CiO%8`Q!0MvO,5=SO%9QQ`O,5=SO%9YQ`O,5=SOOQ!0Lb1G5o1G5oOOQ[7+$a7+$aO!ByQ!0LrO7+$aO!CUQpO7+$aO!$wQlO7+&aO%9_Q`O'#JQO%9vQ`O,5APOOQO1G3h1G3hO9kQ`O,5APO%9vQ`O,5APO%:OQ`O,5APOOQO,5?m,5?mOOQO-E=P-E=POOQ!0Lf7+'T7+'TO%:TQ`O7+)QO9uQ!0LrO7+)QO9kQ`O7+)QO@zQ`O7+)QO%:YQ`O7+)QOOQ[7+)Q7+)QOOQ[7+(p7+(pO%:_Q!0MvO7+(mO!&zQMhO7+(mO!E^Q`O7+(nOOQ[7+(n7+(nO!&zQMhO7+(nO%:iQ`O'#KbO%:tQ`O,5=lOOQO,5?i,5?iOOQO-E<{-E<{OOQ[7+(s7+(sO%<WQpO'#HZOOQ[1G3`1G3`O!&zQMhO1G3`O%[QlO1G3`O%<_Q`O1G3`O%<jQMhO1G3`O9uQ!0LrO1G3bO$$qQ`O1G3bO9`Q`O1G3bO!CUQpO1G3bO!C^QMhO1G3bO%<xQ`O'#JPO%=^Q`O,5@}O%=fQpO,5@}OOQ!0Lb1G3c1G3cOOQ[7+$V7+$VO@zQ`O7+$VO9uQ!0LrO7+$VO%=qQ`O7+$VO%[QlO1G6lO%[QlO1G6mO%=vQ!0LrO1G6lO%>QQlO1G3kO%>XQ`O1G3kO%>^QlO1G3kOOQ[7+)T7+)TO9uQ!0LrO7+)_O`QlO7+)aOOQ['#Kh'#KhOOQ['#JS'#JSO%>eQlO,5>`OOQ[,5>`,5>`O%[QlO'#HuO%>rQ`O'#HwOOQ[,5>f,5>fO9eQ`O,5>fOOQ[,5>h,5>hOOQ[7+)j7+)jOOQ[7+)p7+)pOOQ[7+)t7+)tOOQ[7+)v7+)vO%>wQpO1G5|O%?cQ?MtO1G0zO%?mQ`O1G0zOOQO1G/s1G/sO%?xQ?MtO1G/sO?YQ`O1G/sO!)[QlO'#DmOOQO,5?P,5?POOQO-E<c-E<cOOQO,5?V,5?VOOQO-E<i-E<iO!CUQpO1G/sOOQO-E<e-E<eOOQ!0Ln1G0]1G0]OOQ!0Lf7+%u7+%uO#(ZQ`O7+%uOOQ!0Lf7+&`7+&`O?YQ`O7+&`O!CUQpO7+&`OOQO7+%x7+%xO$@yQ!0MxO7+&XOOQO7+&X7+&XO%[QlO7+&XO%@SQ!0LrO7+&XO!ByQ!0LrO7+%xO!CUQpO7+%xO%@_Q!0LrO7+&XO%@mQ!0MxO7++rO%[QlO7++rO%@}Q`O7++qO%@}Q`O7++qOOQO1G4s1G4sO9eQ`O1G4sO%AVQ`O1G4sOOQS7+%}7+%}O#(ZQ`O<<LPO4UQ!fO<<LPO%AeQ`O<<LPOOQ[<<LP<<LPO!&zQMhO<<LPO%[QlO<<LPO%AmQ`O<<LPO%AxQ!0MzO,5?aO%DTQ!0MzO,5?cO%F`Q!0MzO1G2`O%HqQ!0MzO1G2sO%J|Q!0MzO1G2uO%MXQ!fO,5?QO%[QlO,5?QOOQO-E<d-E<dO%McQ`O1G5}OOQ!0Lf<<JU<<JUO%MkQ?MtO1G0uO& rQ?MtO1G1PO& yQ?MtO1G1PO&#zQ?MtO1G1PO&$RQ?MtO1G1PO&&SQ?MtO1G1PO&(TQ?MtO1G1PO&([Q?MtO1G1PO&(cQ?MtO1G1PO&*dQ?MtO1G1PO&*kQ?MtO1G1PO&*rQ!0MxO<<JfO&,jQ?MtO1G1PO&-gQ?MvO1G1PO&.jQ?MvO'#JlO&0pQ?MtO1G1cO&0}Q?MtO1G0UO&1XQMjO,5?TOOQO-E<g-E<gO!)[QlO'#FqOOQO'#KZ'#KZOOQO1G1u1G1uO&1cQ`O1G1tO&1hQ?MtO,5?[OOOW7+'h7+'hOOOO1G/Z1G/ZO&1rQ!dO1G4xOOQ!0Lh7+(Q7+(QP!&zQMhO,5?^O!,TQMhO7+(cO&1yQ`O,5?]O9eQ`O,5?]OOQO-E<o-E<oO&2XQ`O1G6bO&2XQ`O1G6bO&2aQ`O1G6bO&2lQMjO7+'zO&2|Q!dO,5?_O&3WQ`O,5?_O!&zQMhO,5?_OOQO-E<q-E<qO&3]Q!dO1G6cO&3gQ`O1G6cO&3oQ`O1G2kO!&zQMhO1G2kOOQ!0Lb1G2i1G2iOOQ!0Lb1G2j1G2jO%3oQpO1G2iO!CUQpO1G2iOCwQ`O1G2iOOQ!0Lb1G2q1G2qO&3tQpO1G2iO&4SQ`O1G2kO$*gQ`O1G2jOCwQ`O1G2jO$$UQlO1G2kO&4[Q`O1G2jO&5OQMjO,5?aOOQ!0Lh-E<t-E<tO&5qQMjO,5?cOOQ!0Lh-E<v-E<vO!,TQMhO7++]O&5{QMjO7++]O&6VQMjO7++]OOQ!0Lh1G/c1G/cO&6dQ`O1G/cOOQ!0Lh7+'u7+'uO&6iQMjO7+'|O&6yQ!0MxO<<KXOOQ!0Lf<<KX<<KXO&7mQ`O1G0zO!&zQMhO'#IzO&7rQ`O,5@xO&9tQ!fO<<LPO!&zQMhO1G2nO&9{Q!0LrO1G2nOOQ[<<G{<<G{O!ByQ!0LrO<<G{O&:^Q!0MxO<<I{OOQ!0Lf<<I{<<I{OOQO,5?l,5?lO&;QQ`O,5?lO&;VQ`O,5?lOOQO-E=O-E=OO&;eQ`O1G6kO&;eQ`O1G6kO9kQ`O1G6kO@zQ`O<<LlOOQ[<<Ll<<LlO&;mQ`O<<LlO9uQ!0LrO<<LlO9kQ`O<<LlOOQ[<<LX<<LXO%:_Q!0MvO<<LXOOQ[<<LY<<LYO!E^Q`O<<LYO&;rQpO'#I|O&;}Q`O,5@|O!)[QlO,5@|OOQ[1G3W1G3WOOQO'#JO'#JOO9uQ!0LrO'#JOO&<VQpO,5=uOOQ[,5=u,5=uO&<^QpO'#EgO&<eQpO'#GeO&<jQ`O7+(zO&<oQ`O7+(zOOQ[7+(z7+(zO!&zQMhO7+(zO%[QlO7+(zO&<wQ`O7+(zOOQ[7+(|7+(|O9uQ!0LrO7+(|O$$qQ`O7+(|O9`Q`O7+(|O!CUQpO7+(|O&=SQ`O,5?kOOQO-E<}-E<}OOQO'#H^'#H^O&=_Q`O1G6iO9uQ!0LrO<<GqOOQ[<<Gq<<GqO@zQ`O<<GqO&=gQ`O7+,WO&=lQ`O7+,XO%[QlO7+,WO%[QlO7+,XOOQ[7+)V7+)VO&=qQ`O7+)VO&=vQlO7+)VO&=}Q`O7+)VOOQ[<<Ly<<LyOOQ[<<L{<<L{OOQ[-E=Q-E=QOOQ[1G3z1G3zO&>SQ`O,5>aOOQ[,5>c,5>cO&>XQ`O1G4QO9eQ`O7+&fO!)[QlO7+&fOOQO7+%_7+%_O&>^Q?MtO1G6ZO?YQ`O7+%_OOQ!0Lf<<Ia<<IaOOQ!0Lf<<Iz<<IzO?YQ`O<<IzOOQO<<Is<<IsO$@yQ!0MxO<<IsO%[QlO<<IsOOQO<<Id<<IdO!ByQ!0LrO<<IdO&>hQ!0LrO<<IsO&>sQ!0MxO<= ^O&?TQ`O<= ]OOQO7+*_7+*_O9eQ`O7+*_OOQ[ANAkANAkO&?]Q!fOANAkO!&zQMhOANAkO#(ZQ`OANAkO4UQ!fOANAkO&?dQ`OANAkO%[QlOANAkO&?lQ!0MzO7+'zO&A}Q!0MzO,5?aO&DYQ!0MzO,5?cO&FeQ!0MzO7+'|O&HvQ!fO1G4lO&IQQ?MtO7+&aO&KUQ?MvO,5=XO&M]Q?MvO,5=ZO&MmQ?MvO,5=XO&M}Q?MvO,5=ZO&N_Q?MvO,59uO'!eQ?MvO,5<kO'$hQ?MvO,5<mO'&|Q?MvO,5<{O'(rQ?MtO7+'kO')PQ?MtO7+'mO')^Q`O,5<]OOQO7+'`7+'`OOQ!0Lh7+*d7+*dO')cQMjO<<K}OOQO1G4w1G4wO')jQ`O1G4wO')uQ`O1G4wO'*TQ`O7++|O'*TQ`O7++|O!&zQMhO1G4yO'*]Q!dO1G4yO'*gQ`O7++}O'*oQ`O7+(VO'*zQ!dO7+(VOOQ!0Lb7+(T7+(TOOQ!0Lb7+(U7+(UO!CUQpO7+(TOCwQ`O7+(TO'+UQ`O7+(VO!&zQMhO7+(VO$*gQ`O7+(UO'+ZQ`O7+(VOCwQ`O7+(UO'+cQMjO<<NwO!,TQMhO<<NwOOQ!0Lh7+$}7+$}O'+mQ!dO,5?fOOQO-E<x-E<xO'+wQ!0MvO7+(YO!&zQMhO7+(YOOQ[AN=gAN=gO9kQ`O1G5WOOQO1G5W1G5WO',XQ`O1G5WO',^Q`O7+,VO',^Q`O7+,VO9uQ!0LrOANBWO@zQ`OANBWOOQ[ANBWANBWO',fQ`OANBWOOQ[ANAsANAsOOQ[ANAtANAtO',kQ`O,5?hOOQO-E<z-E<zO',vQ?MtO1G6hOOQO,5?j,5?jOOQO-E<|-E<|OOQ[1G3a1G3aO'-QQ`O,5=POOQ[<<Lf<<LfO!&zQMhO<<LfO&<jQ`O<<LfO'-VQ`O<<LfO%[QlO<<LfOOQ[<<Lh<<LhO9uQ!0LrO<<LhO$$qQ`O<<LhO9`Q`O<<LhO'-_QpO1G5VO'-jQ`O7+,TOOQ[AN=]AN=]O9uQ!0LrOAN=]OOQ[<= r<= rOOQ[<= s<= sO'-rQ`O<= rO'-wQ`O<= sOOQ[<<Lq<<LqO'-|Q`O<<LqO'.RQlO<<LqOOQ[1G3{1G3{O?YQ`O7+)lO'.YQ`O<<JQO'.eQ?MtO<<JQOOQO<<Hy<<HyOOQ!0LfAN?fAN?fOOQOAN?_AN?_O$@yQ!0MxOAN?_OOQOAN?OAN?OO%[QlOAN?_OOQO<<My<<MyOOQ[G27VG27VO!&zQMhOG27VO#(ZQ`OG27VO'.oQ!fOG27VO4UQ!fOG27VO'.vQ`OG27VO'/OQ?MtO<<JfO'/]Q?MvO1G2`O'1RQ?MvO,5?aO'3UQ?MvO,5?cO'5XQ?MvO1G2sO'7[Q?MvO1G2uO'9_Q?MtO<<KXO'9lQ?MtO<<I{OOQO1G1w1G1wO!,TQMhOANAiOOQO7+*c7+*cO'9yQ`O7+*cO':UQ`O<= hO':^Q!dO7+*eOOQ!0Lb<<Kq<<KqO$*gQ`O<<KqOCwQ`O<<KqO':hQ`O<<KqO!&zQMhO<<KqOOQ!0Lb<<Ko<<KoO!CUQpO<<KoO':sQ!dO<<KqOOQ!0Lb<<Kp<<KpO':}Q`O<<KqO!&zQMhO<<KqO$*gQ`O<<KpO';SQMjOANDcO';^Q!0MvO<<KtOOQO7+*r7+*rO9kQ`O7+*rO';nQ`O<= qOOQ[G27rG27rO9uQ!0LrOG27rO@zQ`OG27rO!)[QlO1G5SO';vQ`O7+,SO'<OQ`O1G2kO&<jQ`OANBQOOQ[ANBQANBQO!&zQMhOANBQO'<TQ`OANBQOOQ[ANBSANBSO9uQ!0LrOANBSO$$qQ`OANBSOOQO'#H_'#H_OOQO7+*q7+*qOOQ[G22wG22wOOQ[ANE^ANE^OOQ[ANE_ANE_OOQ[ANB]ANB]O'<]Q`OANB]OOQ[<<MW<<MWO!)[QlOAN?lOOQOG24yG24yO$@yQ!0MxOG24yO#(ZQ`OLD,qOOQ[LD,qLD,qO!&zQMhOLD,qO'<bQ!fOLD,qO'<iQ?MvO7+'zO'>_Q?MvO,5?aO'@bQ?MvO,5?cO'BeQ?MvO7+'|O'DZQMjOG27TOOQO<<M}<<M}OOQ!0LbANA]ANA]O$*gQ`OANA]OCwQ`OANA]O'DkQ!dOANA]OOQ!0LbANAZANAZO'DrQ`OANA]O!&zQMhOANA]O'D}Q!dOANA]OOQ!0LbANA[ANA[OOQO<<N^<<N^OOQ[LD-^LD-^O9uQ!0LrOLD-^O'EXQ?MtO7+*nOOQO'#Gf'#GfOOQ[G27lG27lO&<jQ`OG27lO!&zQMhOG27lOOQ[G27nG27nO9uQ!0LrOG27nOOQ[G27wG27wO'EcQ?MtOG25WOOQOLD*eLD*eOOQ[!$(!]!$(!]O#(ZQ`O!$(!]O!&zQMhO!$(!]O'EmQ!0MzOG27TOOQ!0LbG26wG26wO$*gQ`OG26wO'HOQ`OG26wOCwQ`OG26wO'HZQ!dOG26wO!&zQMhOG26wOOQ[!$(!x!$(!xOOQ[LD-WLD-WO&<jQ`OLD-WOOQ[LD-YLD-YOOQ[!)9Ew!)9EwO#(ZQ`O!)9EwOOQ!0LbLD,cLD,cO$*gQ`OLD,cOCwQ`OLD,cO'HbQ`OLD,cO'HmQ!dOLD,cOOQ[!$(!r!$(!rOOQ[!.K;c!.K;cO'HtQ?MvOG27TOOQ!0Lb!$( }!$( }O$*gQ`O!$( }OCwQ`O!$( }O'JjQ`O!$( }OOQ!0Lb!)9Ei!)9EiO$*gQ`O!)9EiOCwQ`O!)9EiOOQ!0Lb!.K;T!.K;TO$*gQ`O!.K;TOOQ!0Lb!4/0o!4/0oO!)[QlO'#DzO1PQ`O'#EXO'JuQ!fO'#JrO'J|Q!L^O'#DvO'KTQlO'#EOO'K[Q!fO'#CiO'MrQ!fO'#CiO!)[QlO'#EQO'NSQlO,5;ZO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO,5;eO!)[QlO'#IpO(!VQ`O,5<iO!)[QlO,5;eO(!_QMhO,5;eO(#xQMhO,5;eO!)[QlO,5;wO!&zQMhO'#GmO(!_QMhO'#GmO!&zQMhO'#GoO(!_QMhO'#GoO1SQ`O'#DZO1SQ`O'#DZO!&zQMhO'#GPO(!_QMhO'#GPO!&zQMhO'#GRO(!_QMhO'#GRO!&zQMhO'#GaO(!_QMhO'#GaO!)[QlO,5:jO($PQpO'#D_O($ZQpO'#JvO!)[QlO,5@oO'NSQlO1G0uO($eQ?MtO'#CiO!)[QlO1G2PO!&zQMhO'#IuO(!_QMhO'#IuO!&zQMhO'#IwO(!_QMhO'#IwO($oQ!dO'#CrO!&zQMhO,5<tO(!_QMhO,5<tO'NSQlO1G2RO!)[QlO7+&zO!&zQMhO1G2`O(!_QMhO1G2`O!&zQMhO'#IuO(!_QMhO'#IuO!&zQMhO'#IwO(!_QMhO'#IwO!&zQMhO1G2bO(!_QMhO1G2bO'NSQlO7+'mO'NSQlO7+&aO!&zQMhOANAiO(!_QMhOANAiO(%SQ`O'#EoO(%XQ`O'#EoO(%aQ`O'#F]O(%fQ`O'#EyO(%kQ`O'#KTO(%vQ`O'#KRO(&RQ`O,5;ZO(&WQMjO,5<eO(&_Q`O'#GYO(&dQ`O'#GYO(&iQ`O,5<eO(&qQ`O,5<gO(&yQ`O,5;ZO('RQ?MtO1G1`O('YQ`O,5<tO('_Q`O,5<tO('dQ`O,5<vO('iQ`O,5<vO('nQ`O1G2RO('sQ`O1G0uO('xQMjO<<K}O((PQMjO<<K}O((WQMhO'#F|O9`Q`O'#F{OAuQ`O'#EnO!)[QlO,5;tO!3oQ`O'#GYO!3oQ`O'#GYO!3oQ`O'#G[O!3oQ`O'#G[O!,TQMhO7+(cO!,TQMhO7+(cO%.XQ!dO1G2wO%.XQ!dO1G2wO!&zQMhO,5=]O!&zQMhO,5=]",
    stateData: "()^~O'|OS'}OSTOS(ORQ~OPYOQYOSfOY!VOaqOdzOeyOl!POpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_XO!iuO!lZO!oYO!pYO!qYO!svO!uwO!xxO!|]O$W|O$niO%h}O%j!QO%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO%y!UO&W!WO&^!XO&`!YO&b!ZO&d![O&g!]O&m!^O&s!_O&u!`O&w!aO&y!bO&{!cO(TSO(VTO(YUO(aVO(o[O~OWtO~P`OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(T!dO(VTO(YUO(aVO(o[O~Oa!wOs!nO!S!oO!b!yO!c!vO!d!vO!|<SO#T!pO#U!pO#V!xO#W!pO#X!pO#[!zO#]!zO(U!lO(VTO(YUO(e!mO(o!sO~O(O!{O~OP]XR]X[]Xa]Xj]Xr]X!Q]X!S]X!]]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X'z]X(a]X(r]X(y]X(z]X~O!g%RX~P(qO_!}O(V#PO(W!}O(X#PO~O_#QO(X#PO(Y#PO(Z#QO~Ox#SO!U#TO(b#TO(c#VO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(T<WO(VTO(YUO(aVO(o[O~O![#ZO!]#WO!Y(hP!Y(vP~P+}O!^#cO~P`OPYOQYOSfOd!jOe!iOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(VTO(YUO(aVO(o[O~Op#mO![#iO!|]O#i#lO#j#iO(T<XO!k(sP~P.iO!l#oO(T#nO~O!x#sO!|]O%h#tO~O#k#uO~O!g#vO#k#uO~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!]$_O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(aVO(r$YO(y#|O(z#}O~Oa(fX'z(fX'w(fX!k(fX!Y(fX!_(fX%i(fX!g(fX~P1qO#S$dO#`$eO$Q$eOP(gXR(gX[(gXj(gXr(gX!Q(gX!S(gX!](gX!l(gX!p(gX#R(gX#n(gX#o(gX#p(gX#q(gX#r(gX#s(gX#t(gX#u(gX#v(gX#x(gX#z(gX#{(gX(a(gX(r(gX(y(gX(z(gX!_(gX%i(gX~Oa(gX'z(gX'w(gX!Y(gX!k(gXv(gX!g(gX~P4UO#`$eO~O$]$hO$_$gO$f$mO~OSfO!_$nO$i$oO$k$qO~Oh%VOj%dOk%dOp%WOr%XOs$tOt$tOz%YO|%ZO!O%]O!S${O!_$|O!i%bO!l$xO#j%cO$W%`O$t%^O$v%_O$y%aO(T$sO(VTO(YUO(a$uO(y$}O(z%POg(^P~Ol%[O~P7eO!l%eO~O!S%hO!_%iO(T%gO~O!g%mO~Oa%nO'z%nO~O!Q%rO~P%[O(U!lO~P%[O%n%vO~P%[Oh%VO!l%eO(T%gO(U!lO~Oe%}O!l%eO(T%gO~Oj$RO~O!_&PO(T%gO(U!lO(VTO(YUO`)WP~O!Q&SO!l&RO%j&VO&T&WO~P;SO!x#sO~O%s&YO!S)SX!_)SX(T)SX~O(T&ZO~Ol!PO!u&`O%j!QO%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO~Od&eOe&dO!x&bO%h&cO%{&aO~P<bOd&hOeyOl!PO!_&gO!u&`O!xxO!|]O%h}O%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO%y!UO~Ob&kO#`&nO%j&iO(U!lO~P=gO!l&oO!u&sO~O!l#oO~O!_XO~Oa%nO'x&{O'z%nO~Oa%nO'x'OO'z%nO~Oa%nO'x'QO'z%nO~O'w]X!Y]Xv]X!k]X&[]X!_]X%i]X!g]X~P(qO!b'_O!c'WO!d'WO(U!lO(VTO(YUO~Os'UO!S'TO!['XO(e'SO!^(iP!^(xP~P@nOn'bO!_'`O(T%gO~Oe'gO!l%eO(T%gO~O!Q&SO!l&RO~Os!nO!S!oO!|<SO#T!pO#U!pO#W!pO#X!pO(U!lO(VTO(YUO(e!mO(o!sO~O!b'mO!c'lO!d'lO#V!pO#['nO#]'nO~PBYOa%nOh%VO!g#vO!l%eO'z%nO(r'pO~O!p'tO#`'rO~PChOs!nO!S!oO(VTO(YUO(e!mO(o!sO~O!_XOs(mX!S(mX!b(mX!c(mX!d(mX!|(mX#T(mX#U(mX#V(mX#W(mX#X(mX#[(mX#](mX(U(mX(V(mX(Y(mX(e(mX(o(mX~O!c'lO!d'lO(U!lO~PDWO(P'xO(Q'xO(R'zO~O_!}O(V'|O(W!}O(X'|O~O_#QO(X'|O(Y'|O(Z#QO~Ov(OO~P%[Ox#SO!U#TO(b#TO(c(RO~O![(TO!Y'WX!Y'^X!]'WX!]'^X~P+}O!](VO!Y(hX~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!](VO!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(aVO(r$YO(y#|O(z#}O~O!Y(hX~PHRO!Y([O~O!Y(uX!](uX!g(uX!k(uX(r(uX~O#`(uX#k#dX!^(uX~PJUO#`(]O!Y(wX!](wX~O!](^O!Y(vX~O!Y(aO~O#`$eO~PJUO!^(bO~P`OR#zO!Q#yO!S#{O!l#xO(aVOP!na[!naj!nar!na!]!na!p!na#R!na#n!na#o!na#p!na#q!na#r!na#s!na#t!na#u!na#v!na#x!na#z!na#{!na(r!na(y!na(z!na~Oa!na'z!na'w!na!Y!na!k!nav!na!_!na%i!na!g!na~PKlO!k(cO~O!g#vO#`(dO(r'pO!](tXa(tX'z(tX~O!k(tX~PNXO!S%hO!_%iO!|]O#i(iO#j(hO(T%gO~O!](jO!k(sX~O!k(lO~O!S%hO!_%iO#j(hO(T%gO~OP(gXR(gX[(gXj(gXr(gX!Q(gX!S(gX!](gX!l(gX!p(gX#R(gX#n(gX#o(gX#p(gX#q(gX#r(gX#s(gX#t(gX#u(gX#v(gX#x(gX#z(gX#{(gX(a(gX(r(gX(y(gX(z(gX~O!g#vO!k(gX~P! uOR(nO!Q(mO!l#xO#S$dO!|!{a!S!{a~O!x!{a%h!{a!_!{a#i!{a#j!{a(T!{a~P!#vO!x(rO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_XO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(T!dO(VTO(YUO(aVO(o[O~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<pO!S${O!_$|O!i>SO!l$xO#j<vO$W%`O$t<rO$v<tO$y%aO(T(vO(VTO(YUO(a$uO(y$}O(z%PO~O#k(xO~O![(zO!k(kP~P%[O(e(|O(o[O~O!S)OO!l#xO(e(|O(o[O~OP<ROQ<ROSfOd>OOe!iOpkOr<ROskOtkOzkO|<RO!O<RO!SWO!WkO!XkO!_!eO!i<UO!lZO!o<RO!p<RO!q<RO!s<VO!u<YO!x!hO$W!kO$n=|O(T)]O(VTO(YUO(aVO(o[O~O!]$_Oa$qa'z$qa'w$qa!k$qa!Y$qa!_$qa%i$qa!g$qa~Ol)dO~P!&zOh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O%]O!S${O!_$|O!i%bO!l$xO#j%cO$W%`O$t%^O$v%_O$y%aO(T(vO(VTO(YUO(a$uO(y$}O(z%PO~Og(pP~P!,TO!Q)iO!g)hO!_$^X$Z$^X$]$^X$_$^X$f$^X~O!g)hO!_({X$Z({X$]({X$_({X$f({X~O!Q)iO~P!.^O!Q)iO!_({X$Z({X$]({X$_({X$f({X~O!_)kO$Z)oO$])jO$_)jO$f)pO~O![)sO~P!)[O$]$hO$_$gO$f)wO~On$zX!Q$zX#S$zX'y$zX(y$zX(z$zX~OgmXg$zXnmX!]mX#`mX~P!0SOx)yO(b)zO(c)|O~On*VO!Q*OO'y*PO(y$}O(z%PO~Og)}O~P!1WOg*WO~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<pO!S*YO!_*ZO!i>SO!l$xO#j<vO$W%`O$t<rO$v<tO$y%aO(VTO(YUO(a$uO(y$}O(z%PO~O![*^O(T*XO!k)OP~P!1uO#k*`O~O!l*aO~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<pO!S${O!_$|O!i>SO!l$xO#j<vO$W%`O$t<rO$v<tO$y%aO(T*cO(VTO(YUO(a$uO(y$}O(z%PO~O![*fO!Y)PP~P!3tOr*rOs!nO!S*hO!b*pO!c*jO!d*jO!l*aO#[*qO%`*lO(U!lO(VTO(YUO(e!mO~O!^*oO~P!5iO#S$dOn(`X!Q(`X'y(`X(y(`X(z(`X!](`X#`(`X~Og(`X$O(`X~P!6kOn*wO#`*vOg(_X!](_X~O!]*xOg(^X~Oj%dOk%dOl%dO(T&ZOg(^P~Os*{O~Og)}O(T&ZO~O!l+RO~O(T(vO~Op+VO!S%hO![#iO!_%iO!|]O#i#lO#j#iO(T%gO!k(sP~O!g#vO#k+WO~O!S%hO![+YO!](^O!_%iO(T%gO!Y(vP~Os'[O!S+[O![+ZO(VTO(YUO(e(|O~O!^(xP~P!9|O!]+]Oa)TX'z)TX~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(aVO(r$YO(y#|O(z#}O~Oa!ja!]!ja'z!ja'w!ja!Y!ja!k!jav!ja!_!ja%i!ja!g!ja~P!:tOR#zO!Q#yO!S#{O!l#xO(aVOP!ra[!raj!rar!ra!]!ra!p!ra#R!ra#n!ra#o!ra#p!ra#q!ra#r!ra#s!ra#t!ra#u!ra#v!ra#x!ra#z!ra#{!ra(r!ra(y!ra(z!ra~Oa!ra'z!ra'w!ra!Y!ra!k!rav!ra!_!ra%i!ra!g!ra~P!=[OR#zO!Q#yO!S#{O!l#xO(aVOP!ta[!taj!tar!ta!]!ta!p!ta#R!ta#n!ta#o!ta#p!ta#q!ta#r!ta#s!ta#t!ta#u!ta#v!ta#x!ta#z!ta#{!ta(r!ta(y!ta(z!ta~Oa!ta'z!ta'w!ta!Y!ta!k!tav!ta!_!ta%i!ta!g!ta~P!?rOh%VOn+fO!_'`O%i+eO~O!g+hOa(]X!_(]X'z(]X!](]X~Oa%nO!_XO'z%nO~Oh%VO!l%eO~Oh%VO!l%eO(T%gO~O!g#vO#k(xO~Ob+sO%j+tO(T+pO(VTO(YUO!^)XP~O!]+uO`)WX~O[+yO~O`+zO~O!_&PO(T%gO(U!lO`)WP~O%j+}O~P;SOh%VO#`,RO~Oh%VOn,UO!_$|O~O!_,WO~O!Q,YO!_XO~O%n%vO~O!x,_O~Oe,dO~Ob,eO(T#nO(VTO(YUO!^)VP~Oe%}O~O%j!QO(T&ZO~P=gO[,jO`,iO~OPYOQYOSfOdzOeyOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!iuO!lZO!oYO!pYO!qYO!svO!xxO!|]O$niO%h}O(VTO(YUO(aVO(o[O~O!_!eO!u!gO$W!kO(T!dO~P!FyO`,iOa%nO'z%nO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!x!hO$W!kO$niO(T!dO(VTO(YUO(aVO(o[O~Oa,oOl!OO!uwO%l!OO%m!OO%n!OO~P!IcO!l&oO~O&^,uO~O!_,wO~O&o,yO&q,zOP&laQ&laS&laY&laa&lad&lae&lal&lap&lar&las&lat&laz&la|&la!O&la!S&la!W&la!X&la!_&la!i&la!l&la!o&la!p&la!q&la!s&la!u&la!x&la!|&la$W&la$n&la%h&la%j&la%l&la%m&la%n&la%q&la%s&la%v&la%w&la%y&la&W&la&^&la&`&la&b&la&d&la&g&la&m&la&s&la&u&la&w&la&y&la&{&la'w&la(T&la(V&la(Y&la(a&la(o&la!^&la&e&lab&la&j&la~O(T-PO~Oh!eX!]!RX!^!RX!g!RX!g!eX!l!eX#`!RX~O!]!eX!^!eX~P#!iO!g-UO#`-TOh(jX!]#hX!^#hX!g(jX!l(jX~O!](jX!^(jX~P##[Oh%VO!g-WO!l%eO!]!aX!^!aX~Os!nO!S!oO(VTO(YUO(e!mO~OP<ROQ<ROSfOd>OOe!iOpkOr<ROskOtkOzkO|<RO!O<RO!SWO!WkO!XkO!_!eO!i<UO!lZO!o<RO!p<RO!q<RO!s<VO!u<YO!x!hO$W!kO$n=|O(VTO(YUO(aVO(o[O~O(T<}O~P#$qO!]-[O!^(iX~O!^-^O~O!g-UO#`-TO!]#hX!^#hX~O!]-_O!^(xX~O!^-aO~O!c-bO!d-bO(U!lO~P#$`O!^-eO~P'_On-hO!_'`O~O!Y-mO~Os!{a!b!{a!c!{a!d!{a#T!{a#U!{a#V!{a#W!{a#X!{a#[!{a#]!{a(U!{a(V!{a(Y!{a(e!{a(o!{a~P!#vO!p-rO#`-pO~PChO!c-tO!d-tO(U!lO~PDWOa%nO#`-pO'z%nO~Oa%nO!g#vO#`-pO'z%nO~Oa%nO!g#vO!p-rO#`-pO'z%nO(r'pO~O(P'xO(Q'xO(R-yO~Ov-zO~O!Y'Wa!]'Wa~P!:tO![.OO!Y'WX!]'WX~P%[O!](VO!Y(ha~O!Y(ha~PHRO!](^O!Y(va~O!S%hO![.SO!_%iO(T%gO!Y'^X!]'^X~O#`.UO!](ta!k(taa(ta'z(ta~O!g#vO~P#,wO!](jO!k(sa~O!S%hO!_%iO#j.YO(T%gO~Op._O!S%hO![.[O!_%iO!|]O#i.^O#j.[O(T%gO!]'aX!k'aX~OR.cO!l#xO~Oh%VOn.fO!_'`O%i.eO~Oa#ci!]#ci'z#ci'w#ci!Y#ci!k#civ#ci!_#ci%i#ci!g#ci~P!:tOn>YO!Q*OO'y*PO(y$}O(z%PO~O#k#_aa#_a#`#_a'z#_a!]#_a!k#_a!_#_a!Y#_a~P#/sO#k(`XP(`XR(`X[(`Xa(`Xj(`Xr(`X!S(`X!l(`X!p(`X#R(`X#n(`X#o(`X#p(`X#q(`X#r(`X#s(`X#t(`X#u(`X#v(`X#x(`X#z(`X#{(`X'z(`X(a(`X(r(`X!k(`X!Y(`X'w(`Xv(`X!_(`X%i(`X!g(`X~P!6kO!].sO!k(kX~P!:tO!k.vO~O!Y.xO~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O(aVO[#mia#mij#mir#mi!]#mi#R#mi#o#mi#p#mi#q#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'z#mi(r#mi(y#mi(z#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#n#mi~P#3cO#n$OO~P#3cOP$[OR#zOr$aO!Q#yO!S#{O!l#xO!p$[O#n$OO#o$PO#p$PO#q$PO(aVO[#mia#mij#mi!]#mi#R#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'z#mi(r#mi(y#mi(z#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#r#mi~P#6QO#r$QO~P#6QOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO(aVOa#mi!]#mi#x#mi#z#mi#{#mi'z#mi(r#mi(y#mi(z#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#v#mi~P#8oOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO(aVO(z#}Oa#mi!]#mi#z#mi#{#mi'z#mi(r#mi(y#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#x$UO~P#;VO#x#mi~P#;VO#v$SO~P#8oOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO(aVO(y#|O(z#}Oa#mi!]#mi#{#mi'z#mi(r#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#z#mi~P#={O#z$WO~P#={OP]XR]X[]Xj]Xr]X!Q]X!S]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(a]X(r]X(y]X(z]X!]]X!^]X~O$O]X~P#@jOP$[OR#zO[<jOj<_Or<hO!Q#yO!S#{O!l#xO!p$[O#R<_O#n<[O#o<]O#p<]O#q<]O#r<^O#s<_O#t<_O#u<iO#v<`O#x<bO#z<dO#{<eO(aVO(r$YO(y#|O(z#}O~O$O.zO~P#BwO#S$dO#`<kO$Q<kO$O(gX!^(gX~P! uOa'da!]'da'z'da'w'da!k'da!Y'dav'da!_'da%i'da!g'da~P!:tO[#mia#mij#mir#mi!]#mi#R#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'z#mi(r#mi'w#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O#n$OO#o$PO#p$PO#q$PO(aVO(y#mi(z#mi~P#EyOn>YO!Q*OO'y*PO(y$}O(z%POP#miR#mi!S#mi!l#mi!p#mi#n#mi#o#mi#p#mi#q#mi(a#mi~P#EyO!]/OOg(pX~P!1WOg/QO~Oa$Pi!]$Pi'z$Pi'w$Pi!Y$Pi!k$Piv$Pi!_$Pi%i$Pi!g$Pi~P!:tO$]/RO$_/RO~O$]/SO$_/SO~O!g)hO#`/TO!_$cX$Z$cX$]$cX$_$cX$f$cX~O![/UO~O!_)kO$Z/WO$])jO$_)jO$f/XO~O!]<fO!^(fX~P#BwO!^/YO~O!g)hO$f({X~O$f/[O~Ov/]O~P!&zOx)yO(b)zO(c/`O~O!S/cO~O(y$}On%aa!Q%aa'y%aa(z%aa!]%aa#`%aa~Og%aa$O%aa~P#L{O(z%POn%ca!Q%ca'y%ca(y%ca!]%ca#`%ca~Og%ca$O%ca~P#MnO!]fX!gfX!kfX!k$zX(rfX~P!0SO![/lO!](^O(T/kO!Y(vP!Y)PP~P!1uOr*rO!b*pO!c*jO!d*jO!l*aO#[*qO%`*lO(U!lO(VTO(YUO~Os<zO!S/mO![+ZO!^*oO(e<yO!^(xP~P$ XO!k/nO~P#/sO!]/oO!g#vO(r'pO!k)OX~O!k/tO~O!S%hO![*^O!_%iO(T%gO!k)OP~O#k/vO~O!Y$zX!]$zX!g%RX~P!0SO!]/wO!Y)PX~P#/sO!g/yO~O!Y/{O~OpkO(T/|O~P.iOh%VOr0RO!g#vO!l%eO(r'pO~O!g+hO~Oa%nO!]0VO'z%nO~O!^0XO~P!5iO!c0YO!d0YO(U!lO~P#$`Os!nO!S0ZO(VTO(YUO(e!mO~O#[0]O~Og%aa!]%aa#`%aa$O%aa~P!1WOg%ca!]%ca#`%ca$O%ca~P!1WOj%dOk%dOl%dO(T&ZOg'mX!]'mX~O!]*xOg(^a~Og0fO~On0hO#`0gOg(_a!](_a~OR0iO!Q0iO!S0jO#S$dOn}a'y}a(y}a(z}a!]}a#`}a~Og}a$O}a~P$'pO!Q*OO'y*POn$sa(y$sa(z$sa!]$sa#`$sa~Og$sa$O$sa~P$(lO!Q*OO'y*POn$ua(y$ua(z$ua!]$ua#`$ua~Og$ua$O$ua~P$)_O#k0mO~Og%Ta!]%Ta#`%Ta$O%Ta~P!1WO!g#vO~O#k0pO~O!]+]Oa)Ta'z)Ta~OR#zO!Q#yO!S#{O!l#xO(aVOP!ri[!rij!rir!ri!]!ri!p!ri#R!ri#n!ri#o!ri#p!ri#q!ri#r!ri#s!ri#t!ri#u!ri#v!ri#x!ri#z!ri#{!ri(r!ri(y!ri(z!ri~Oa!ri'z!ri'w!ri!Y!ri!k!riv!ri!_!ri%i!ri!g!ri~P$*|Oh%VOr%XOs$tOt$tOz%YO|%ZO!O<pO!S${O!_$|O!i>SO!l$xO#j<vO$W%`O$t<rO$v<tO$y%aO(VTO(YUO(a$uO(y$}O(z%PO~Op0yO%]0zO(T0xO~P$-dO!g+hOa(]a!_(]a'z(]a!](]a~O#k1QO~O[]X!]fX!^fX~O!]1RO!^)XX~O!^1TO~O[1UO~Ob1WO(T+pO(VTO(YUO~O!_&PO(T%gO`'uX!]'uX~O!]+uO`)Wa~O!k1ZO~P!:tO[1^O~O`1_O~O#`1dO~On1gO!_$|O~O(e(|O!^)UP~Oh%VOn1pO!_1mO%i1oO~O[1zO!]1xO!^)VX~O!^1{O~O`1}Oa%nO'z%nO~O(T#nO(VTO(YUO~O#S$dO#`$eO$Q$eOP(gXR(gX[(gXr(gX!Q(gX!S(gX!](gX!l(gX!p(gX#R(gX#n(gX#o(gX#p(gX#q(gX#r(gX#s(gX#t(gX#u(gX#v(gX#x(gX#z(gX#{(gX(a(gX(r(gX(y(gX(z(gX~Oj2QO&[2ROa(gX~P$2}Oj2QO#`$eO&[2RO~Oa2TO~P%[Oa2VO~O&e2YOP&ciQ&ciS&ciY&cia&cid&cie&cil&cip&cir&cis&cit&ciz&ci|&ci!O&ci!S&ci!W&ci!X&ci!_&ci!i&ci!l&ci!o&ci!p&ci!q&ci!s&ci!u&ci!x&ci!|&ci$W&ci$n&ci%h&ci%j&ci%l&ci%m&ci%n&ci%q&ci%s&ci%v&ci%w&ci%y&ci&W&ci&^&ci&`&ci&b&ci&d&ci&g&ci&m&ci&s&ci&u&ci&w&ci&y&ci&{&ci'w&ci(T&ci(V&ci(Y&ci(a&ci(o&ci!^&cib&ci&j&ci~Ob2`O!^2^O&j2_O~P`O!_XO!l2bO~O&q,zOP&liQ&liS&liY&lia&lid&lie&lil&lip&lir&lis&lit&liz&li|&li!O&li!S&li!W&li!X&li!_&li!i&li!l&li!o&li!p&li!q&li!s&li!u&li!x&li!|&li$W&li$n&li%h&li%j&li%l&li%m&li%n&li%q&li%s&li%v&li%w&li%y&li&W&li&^&li&`&li&b&li&d&li&g&li&m&li&s&li&u&li&w&li&y&li&{&li'w&li(T&li(V&li(Y&li(a&li(o&li!^&li&e&lib&li&j&li~O!Y2hO~O!]!aa!^!aa~P#BwOs!nO!S!oO![2nO(e!mO!]'XX!^'XX~P@nO!]-[O!^(ia~O!]'_X!^'_X~P!9|O!]-_O!^(xa~O!^2uO~P'_Oa%nO#`3OO'z%nO~Oa%nO!g#vO#`3OO'z%nO~Oa%nO!g#vO!p3SO#`3OO'z%nO(r'pO~Oa%nO'z%nO~P!:tO!]$_Ov$qa~O!Y'Wi!]'Wi~P!:tO!](VO!Y(hi~O!](^O!Y(vi~O!Y(wi!](wi~P!:tO!](ti!k(tia(ti'z(ti~P!:tO#`3UO!](ti!k(tia(ti'z(ti~O!](jO!k(si~O!S%hO!_%iO!|]O#i3ZO#j3YO(T%gO~O!S%hO!_%iO#j3YO(T%gO~On3bO!_'`O%i3aO~Oh%VOn3bO!_'`O%i3aO~O#k%aaP%aaR%aa[%aaa%aaj%aar%aa!S%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa'z%aa(a%aa(r%aa!k%aa!Y%aa'w%aav%aa!_%aa%i%aa!g%aa~P#L{O#k%caP%caR%ca[%caa%caj%car%ca!S%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca'z%ca(a%ca(r%ca!k%ca!Y%ca'w%cav%ca!_%ca%i%ca!g%ca~P#MnO#k%aaP%aaR%aa[%aaa%aaj%aar%aa!S%aa!]%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa'z%aa(a%aa(r%aa!k%aa!Y%aa'w%aa#`%aav%aa!_%aa%i%aa!g%aa~P#/sO#k%caP%caR%ca[%caa%caj%car%ca!S%ca!]%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca'z%ca(a%ca(r%ca!k%ca!Y%ca'w%ca#`%cav%ca!_%ca%i%ca!g%ca~P#/sO#k}aP}a[}aa}aj}ar}a!l}a!p}a#R}a#n}a#o}a#p}a#q}a#r}a#s}a#t}a#u}a#v}a#x}a#z}a#{}a'z}a(a}a(r}a!k}a!Y}a'w}av}a!_}a%i}a!g}a~P$'pO#k$saP$saR$sa[$saa$saj$sar$sa!S$sa!l$sa!p$sa#R$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#t$sa#u$sa#v$sa#x$sa#z$sa#{$sa'z$sa(a$sa(r$sa!k$sa!Y$sa'w$sav$sa!_$sa%i$sa!g$sa~P$(lO#k$uaP$uaR$ua[$uaa$uaj$uar$ua!S$ua!l$ua!p$ua#R$ua#n$ua#o$ua#p$ua#q$ua#r$ua#s$ua#t$ua#u$ua#v$ua#x$ua#z$ua#{$ua'z$ua(a$ua(r$ua!k$ua!Y$ua'w$uav$ua!_$ua%i$ua!g$ua~P$)_O#k%TaP%TaR%Ta[%Taa%Taj%Tar%Ta!S%Ta!]%Ta!l%Ta!p%Ta#R%Ta#n%Ta#o%Ta#p%Ta#q%Ta#r%Ta#s%Ta#t%Ta#u%Ta#v%Ta#x%Ta#z%Ta#{%Ta'z%Ta(a%Ta(r%Ta!k%Ta!Y%Ta'w%Ta#`%Tav%Ta!_%Ta%i%Ta!g%Ta~P#/sOa#cq!]#cq'z#cq'w#cq!Y#cq!k#cqv#cq!_#cq%i#cq!g#cq~P!:tO![3jO!]'YX!k'YX~P%[O!].sO!k(ka~O!].sO!k(ka~P!:tO!Y3mO~O$O!na!^!na~PKlO$O!ja!]!ja!^!ja~P#BwO$O!ra!^!ra~P!=[O$O!ta!^!ta~P!?rOg']X!]']X~P!,TO!]/OOg(pa~OSfO!_4RO$d4SO~O!^4WO~Ov4XO~P#/sOa$mq!]$mq'z$mq'w$mq!Y$mq!k$mqv$mq!_$mq%i$mq!g$mq~P!:tO!Y4ZO~P!&zO!S4[O~O!Q*OO'y*PO(z%POn'ia(y'ia!]'ia#`'ia~Og'ia$O'ia~P%,sO!Q*OO'y*POn'ka(y'ka(z'ka!]'ka#`'ka~Og'ka$O'ka~P%-fO(r$YO~P#/sO!YfX!Y$zX!]fX!]$zX!g%RX#`fX~P!0SO(T=TO~P!1uO!S%hO![4_O!_%iO(T%gO!]'eX!k'eX~O!]/oO!k)Oa~O!]/oO!g#vO!k)Oa~O!]/oO!g#vO(r'pO!k)Oa~Og$|i!]$|i#`$|i$O$|i~P!1WO![4gO!Y'gX!]'gX~P!3tO!]/wO!Y)Pa~O!]/wO!Y)Pa~P#/sOP]XR]X[]Xj]Xr]X!Q]X!S]X!Y]X!]]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(a]X(r]X(y]X(z]X~Oj%YX!g%YX~P%1VOj4lO!g#vO~Oh%VO!g#vO!l%eO~Oh%VOr4qO!l%eO(r'pO~Or4vO!g#vO(r'pO~Os!nO!S4wO(VTO(YUO(e!mO~O(y$}On%ai!Q%ai'y%ai(z%ai!]%ai#`%ai~Og%ai$O%ai~P%4vO(z%POn%ci!Q%ci'y%ci(y%ci!]%ci#`%ci~Og%ci$O%ci~P%5iOg(_i!](_i~P!1WO#`4}Og(_i!](_i~P!1WO!k5SO~Oa$oq!]$oq'z$oq'w$oq!Y$oq!k$oqv$oq!_$oq%i$oq!g$oq~P!:tO!Y5WO~O!]5XO!_)QX~P#/sOa$zX!_$zX%^]X'z$zX!]$zX~P!0SO%^5[OaoXnoX!QoX!_oX'yoX'zoX(yoX(zoX!]oX~Op5]O(T#nO~O%^5[O~Ob5cO%j5dO(T+pO(VTO(YUO!]'tX!^'tX~O!]1RO!^)Xa~O[5hO~O`5iO~O[5mO~Oa%nO'z%nO~P#/sO!]5rO#`5tO!^)UX~O!^5uO~Or5{Os!nO!S*hO!b!yO!c!vO!d!vO!|<SO#T!pO#U!pO#V!pO#W!pO#X!pO#[5zO#]!zO(U!lO(VTO(YUO(e!mO(o!sO~O!^5yO~P%:yOn6QO!_1mO%i6PO~Oh%VOn6QO!_1mO%i6PO~Ob6XO(T#nO(VTO(YUO!]'sX!^'sX~O!]1xO!^)Va~O(VTO(YUO(e6ZO~O`6_O~Oj6bO&[6cO~PNXO!k6dO~P%[Oa6fO~Oa6fO~P%[Ob2`O!^6kO&j2_O~P`O!g6mO~O!g6oOh(ji!](ji!^(ji!g(ji!l(jir(ji(r(ji~O!]#hi!^#hi~P#BwO#`6pO!]#hi!^#hi~O!]!ai!^!ai~P#BwOa%nO#`6yO'z%nO~Oa%nO!g#vO#`6yO'z%nO~O!](tq!k(tqa(tq'z(tq~P!:tO!](jO!k(sq~O!S%hO!_%iO#j7QO(T%gO~O!_'`O%i7TO~On7XO!_'`O%i7TO~O#k'iaP'iaR'ia['iaa'iaj'iar'ia!S'ia!l'ia!p'ia#R'ia#n'ia#o'ia#p'ia#q'ia#r'ia#s'ia#t'ia#u'ia#v'ia#x'ia#z'ia#{'ia'z'ia(a'ia(r'ia!k'ia!Y'ia'w'iav'ia!_'ia%i'ia!g'ia~P%,sO#k'kaP'kaR'ka['kaa'kaj'kar'ka!S'ka!l'ka!p'ka#R'ka#n'ka#o'ka#p'ka#q'ka#r'ka#s'ka#t'ka#u'ka#v'ka#x'ka#z'ka#{'ka'z'ka(a'ka(r'ka!k'ka!Y'ka'w'kav'ka!_'ka%i'ka!g'ka~P%-fO#k$|iP$|iR$|i[$|ia$|ij$|ir$|i!S$|i!]$|i!l$|i!p$|i#R$|i#n$|i#o$|i#p$|i#q$|i#r$|i#s$|i#t$|i#u$|i#v$|i#x$|i#z$|i#{$|i'z$|i(a$|i(r$|i!k$|i!Y$|i'w$|i#`$|iv$|i!_$|i%i$|i!g$|i~P#/sO#k%aiP%aiR%ai[%aia%aij%air%ai!S%ai!l%ai!p%ai#R%ai#n%ai#o%ai#p%ai#q%ai#r%ai#s%ai#t%ai#u%ai#v%ai#x%ai#z%ai#{%ai'z%ai(a%ai(r%ai!k%ai!Y%ai'w%aiv%ai!_%ai%i%ai!g%ai~P%4vO#k%ciP%ciR%ci[%cia%cij%cir%ci!S%ci!l%ci!p%ci#R%ci#n%ci#o%ci#p%ci#q%ci#r%ci#s%ci#t%ci#u%ci#v%ci#x%ci#z%ci#{%ci'z%ci(a%ci(r%ci!k%ci!Y%ci'w%civ%ci!_%ci%i%ci!g%ci~P%5iO!]'Ya!k'Ya~P!:tO!].sO!k(ki~O$O#ci!]#ci!^#ci~P#BwOP$[OR#zO!Q#yO!S#{O!l#xO!p$[O(aVO[#mij#mir#mi#R#mi#o#mi#p#mi#q#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(r#mi(y#mi(z#mi!]#mi!^#mi~O#n#mi~P%MxO#n<[O~P%MxOP$[OR#zOr<hO!Q#yO!S#{O!l#xO!p$[O#n<[O#o<]O#p<]O#q<]O(aVO[#mij#mi#R#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(r#mi(y#mi(z#mi!]#mi!^#mi~O#r#mi~P&!QO#r<^O~P&!QOP$[OR#zO[<jOj<_Or<hO!Q#yO!S#{O!l#xO!p$[O#R<_O#n<[O#o<]O#p<]O#q<]O#r<^O#s<_O#t<_O#u<iO(aVO#x#mi#z#mi#{#mi$O#mi(r#mi(y#mi(z#mi!]#mi!^#mi~O#v#mi~P&$YOP$[OR#zO[<jOj<_Or<hO!Q#yO!S#{O!l#xO!p$[O#R<_O#n<[O#o<]O#p<]O#q<]O#r<^O#s<_O#t<_O#u<iO#v<`O(aVO(z#}O#z#mi#{#mi$O#mi(r#mi(y#mi!]#mi!^#mi~O#x<bO~P&&ZO#x#mi~P&&ZO#v<`O~P&$YOP$[OR#zO[<jOj<_Or<hO!Q#yO!S#{O!l#xO!p$[O#R<_O#n<[O#o<]O#p<]O#q<]O#r<^O#s<_O#t<_O#u<iO#v<`O#x<bO(aVO(y#|O(z#}O#{#mi$O#mi(r#mi!]#mi!^#mi~O#z#mi~P&(jO#z<dO~P&(jOa#|y!]#|y'z#|y'w#|y!Y#|y!k#|yv#|y!_#|y%i#|y!g#|y~P!:tO[#mij#mir#mi#R#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(r#mi!]#mi!^#mi~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O#n<[O#o<]O#p<]O#q<]O(aVO(y#mi(z#mi~P&+fOn>ZO!Q*OO'y*PO(y$}O(z%POP#miR#mi!S#mi!l#mi!p#mi#n#mi#o#mi#p#mi#q#mi(a#mi~P&+fO#S$dOP(`XR(`X[(`Xj(`Xn(`Xr(`X!Q(`X!S(`X!l(`X!p(`X#R(`X#n(`X#o(`X#p(`X#q(`X#r(`X#s(`X#t(`X#u(`X#v(`X#x(`X#z(`X#{(`X$O(`X'y(`X(a(`X(r(`X(y(`X(z(`X!](`X!^(`X~O$O$Pi!]$Pi!^$Pi~P#BwO$O!ri!^!ri~P$*|Og']a!]']a~P!1WO!^7kO~O!]'da!^'da~P#BwO!Y7lO~P#/sO!g#vO(r'pO!]'ea!k'ea~O!]/oO!k)Oi~O!]/oO!g#vO!k)Oi~Og$|q!]$|q#`$|q$O$|q~P!1WO!Y'ga!]'ga~P#/sO!g7sO~O!]/wO!Y)Pi~P#/sO!]/wO!Y)Pi~O!Y7vO~Oh%VOr7{O!l%eO(r'pO~Oj7}O!g#vO~Or8QO!g#vO(r'pO~O!Q*OO'y*PO(z%POn'ja(y'ja!]'ja#`'ja~Og'ja$O'ja~P&4gO!Q*OO'y*POn'la(y'la(z'la!]'la#`'la~Og'la$O'la~P&5YOg(_q!](_q~P!1WO#`8SOg(_q!](_q~P!1WO!Y8TO~Og%Oq!]%Oq#`%Oq$O%Oq~P!1WOa$oy!]$oy'z$oy'w$oy!Y$oy!k$oyv$oy!_$oy%i$oy!g$oy~P!:tO!g6oO~O!]5XO!_)Qa~O!_'`OP$TaR$Ta[$Taj$Tar$Ta!Q$Ta!S$Ta!]$Ta!l$Ta!p$Ta#R$Ta#n$Ta#o$Ta#p$Ta#q$Ta#r$Ta#s$Ta#t$Ta#u$Ta#v$Ta#x$Ta#z$Ta#{$Ta(a$Ta(r$Ta(y$Ta(z$Ta~O%i7TO~P&7zO%^8XOa%[i!_%[i'z%[i!]%[i~Oa#cy!]#cy'z#cy'w#cy!Y#cy!k#cyv#cy!_#cy%i#cy!g#cy~P!:tO[8ZO~Ob8]O(T+pO(VTO(YUO~O!]1RO!^)Xi~O`8aO~O(e(|O!]'pX!^'pX~O!]5rO!^)Ua~O!^8kO~P%:yO(o!sO~P$%gO#[8lO~O!_1mO~O!_1mO%i8nO~On8qO!_1mO%i8nO~O[8vO!]'sa!^'sa~O!]1xO!^)Vi~O!k8zO~O!k8{O~O!k9OO~O!k9OO~P%[Oa9QO~O!g9RO~O!k9SO~O!](wi!^(wi~P#BwOa%nO#`9[O'z%nO~O!](ty!k(tya(ty'z(ty~P!:tO!](jO!k(sy~O%i9_O~P&7zO!_'`O%i9_O~O#k$|qP$|qR$|q[$|qa$|qj$|qr$|q!S$|q!]$|q!l$|q!p$|q#R$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#t$|q#u$|q#v$|q#x$|q#z$|q#{$|q'z$|q(a$|q(r$|q!k$|q!Y$|q'w$|q#`$|qv$|q!_$|q%i$|q!g$|q~P#/sO#k'jaP'jaR'ja['jaa'jaj'jar'ja!S'ja!l'ja!p'ja#R'ja#n'ja#o'ja#p'ja#q'ja#r'ja#s'ja#t'ja#u'ja#v'ja#x'ja#z'ja#{'ja'z'ja(a'ja(r'ja!k'ja!Y'ja'w'jav'ja!_'ja%i'ja!g'ja~P&4gO#k'laP'laR'la['laa'laj'lar'la!S'la!l'la!p'la#R'la#n'la#o'la#p'la#q'la#r'la#s'la#t'la#u'la#v'la#x'la#z'la#{'la'z'la(a'la(r'la!k'la!Y'la'w'lav'la!_'la%i'la!g'la~P&5YO#k%OqP%OqR%Oq[%Oqa%Oqj%Oqr%Oq!S%Oq!]%Oq!l%Oq!p%Oq#R%Oq#n%Oq#o%Oq#p%Oq#q%Oq#r%Oq#s%Oq#t%Oq#u%Oq#v%Oq#x%Oq#z%Oq#{%Oq'z%Oq(a%Oq(r%Oq!k%Oq!Y%Oq'w%Oq#`%Oqv%Oq!_%Oq%i%Oq!g%Oq~P#/sO!]'Yi!k'Yi~P!:tO$O#cq!]#cq!^#cq~P#BwO(y$}OP%aaR%aa[%aaj%aar%aa!S%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa$O%aa(a%aa(r%aa!]%aa!^%aa~On%aa!Q%aa'y%aa(z%aa~P&I_O(z%POP%caR%ca[%caj%car%ca!S%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca$O%ca(a%ca(r%ca!]%ca!^%ca~On%ca!Q%ca'y%ca(y%ca~P&KfOn>ZO!Q*OO'y*PO(z%PO~P&I_On>ZO!Q*OO'y*PO(y$}O~P&KfOR0iO!Q0iO!S0jO#S$dOP}a[}aj}an}ar}a!l}a!p}a#R}a#n}a#o}a#p}a#q}a#r}a#s}a#t}a#u}a#v}a#x}a#z}a#{}a$O}a'y}a(a}a(r}a(y}a(z}a!]}a!^}a~O!Q*OO'y*POP$saR$sa[$saj$san$sar$sa!S$sa!l$sa!p$sa#R$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#t$sa#u$sa#v$sa#x$sa#z$sa#{$sa$O$sa(a$sa(r$sa(y$sa(z$sa!]$sa!^$sa~O!Q*OO'y*POP$uaR$ua[$uaj$uan$uar$ua!S$ua!l$ua!p$ua#R$ua#n$ua#o$ua#p$ua#q$ua#r$ua#s$ua#t$ua#u$ua#v$ua#x$ua#z$ua#{$ua$O$ua(a$ua(r$ua(y$ua(z$ua!]$ua!^$ua~On>ZO!Q*OO'y*PO(y$}O(z%PO~OP%TaR%Ta[%Taj%Tar%Ta!S%Ta!l%Ta!p%Ta#R%Ta#n%Ta#o%Ta#p%Ta#q%Ta#r%Ta#s%Ta#t%Ta#u%Ta#v%Ta#x%Ta#z%Ta#{%Ta$O%Ta(a%Ta(r%Ta!]%Ta!^%Ta~P'&kO$O$mq!]$mq!^$mq~P#BwO$O$oq!]$oq!^$oq~P#BwO!^9lO~O$O9mO~P!1WO!g#vO!]'ei!k'ei~O!g#vO(r'pO!]'ei!k'ei~O!]/oO!k)Oq~O!Y'gi!]'gi~P#/sO!]/wO!Y)Pq~Or9tO!g#vO(r'pO~O[9vO!Y9uO~P#/sO!Y9uO~Oj9|O!g#vO~Og(_y!](_y~P!1WO!]'na!_'na~P#/sOa%[q!_%[q'z%[q!]%[q~P#/sO[:RO~O!]1RO!^)Xq~O`:VO~O#`:WO!]'pa!^'pa~O!]5rO!^)Ui~P#BwO!S:YO~O!_1mO%i:]O~O(VTO(YUO(e:bO~O!]1xO!^)Vq~O!k:eO~O!k:fO~O!k:gO~O!k:gO~P%[O#`:jO!]#hy!^#hy~O!]#hy!^#hy~P#BwO%i:oO~P&7zO!_'`O%i:oO~O$O#|y!]#|y!^#|y~P#BwOP$|iR$|i[$|ij$|ir$|i!S$|i!l$|i!p$|i#R$|i#n$|i#o$|i#p$|i#q$|i#r$|i#s$|i#t$|i#u$|i#v$|i#x$|i#z$|i#{$|i$O$|i(a$|i(r$|i!]$|i!^$|i~P'&kO!Q*OO'y*PO(z%POP'iaR'ia['iaj'ian'iar'ia!S'ia!l'ia!p'ia#R'ia#n'ia#o'ia#p'ia#q'ia#r'ia#s'ia#t'ia#u'ia#v'ia#x'ia#z'ia#{'ia$O'ia(a'ia(r'ia(y'ia!]'ia!^'ia~O!Q*OO'y*POP'kaR'ka['kaj'kan'kar'ka!S'ka!l'ka!p'ka#R'ka#n'ka#o'ka#p'ka#q'ka#r'ka#s'ka#t'ka#u'ka#v'ka#x'ka#z'ka#{'ka$O'ka(a'ka(r'ka(y'ka(z'ka!]'ka!^'ka~O(y$}OP%aiR%ai[%aij%ain%air%ai!Q%ai!S%ai!l%ai!p%ai#R%ai#n%ai#o%ai#p%ai#q%ai#r%ai#s%ai#t%ai#u%ai#v%ai#x%ai#z%ai#{%ai$O%ai'y%ai(a%ai(r%ai(z%ai!]%ai!^%ai~O(z%POP%ciR%ci[%cij%cin%cir%ci!Q%ci!S%ci!l%ci!p%ci#R%ci#n%ci#o%ci#p%ci#q%ci#r%ci#s%ci#t%ci#u%ci#v%ci#x%ci#z%ci#{%ci$O%ci'y%ci(a%ci(r%ci(y%ci!]%ci!^%ci~O$O$oy!]$oy!^$oy~P#BwO$O#cy!]#cy!^#cy~P#BwO!g#vO!]'eq!k'eq~O!]/oO!k)Oy~O!Y'gq!]'gq~P#/sOr:yO!g#vO(r'pO~O[:}O!Y:|O~P#/sO!Y:|O~Og(_!R!](_!R~P!1WOa%[y!_%[y'z%[y!]%[y~P#/sO!]1RO!^)Xy~O!]5rO!^)Uq~O(T;UO~O!_1mO%i;XO~O!k;[O~O%i;aO~P&7zOP$|qR$|q[$|qj$|qr$|q!S$|q!l$|q!p$|q#R$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#t$|q#u$|q#v$|q#x$|q#z$|q#{$|q$O$|q(a$|q(r$|q!]$|q!^$|q~P'&kO!Q*OO'y*PO(z%POP'jaR'ja['jaj'jan'jar'ja!S'ja!l'ja!p'ja#R'ja#n'ja#o'ja#p'ja#q'ja#r'ja#s'ja#t'ja#u'ja#v'ja#x'ja#z'ja#{'ja$O'ja(a'ja(r'ja(y'ja!]'ja!^'ja~O!Q*OO'y*POP'laR'la['laj'lan'lar'la!S'la!l'la!p'la#R'la#n'la#o'la#p'la#q'la#r'la#s'la#t'la#u'la#v'la#x'la#z'la#{'la$O'la(a'la(r'la(y'la(z'la!]'la!^'la~OP%OqR%Oq[%Oqj%Oqr%Oq!S%Oq!l%Oq!p%Oq#R%Oq#n%Oq#o%Oq#p%Oq#q%Oq#r%Oq#s%Oq#t%Oq#u%Oq#v%Oq#x%Oq#z%Oq#{%Oq$O%Oq(a%Oq(r%Oq!]%Oq!^%Oq~P'&kOg%e!Z!]%e!Z#`%e!Z$O%e!Z~P!1WO!Y;eO~P#/sOr;fO!g#vO(r'pO~O[;hO!Y;eO~P#/sO!]'pq!^'pq~P#BwO!]#h!Z!^#h!Z~P#BwO#k%e!ZP%e!ZR%e!Z[%e!Za%e!Zj%e!Zr%e!Z!S%e!Z!]%e!Z!l%e!Z!p%e!Z#R%e!Z#n%e!Z#o%e!Z#p%e!Z#q%e!Z#r%e!Z#s%e!Z#t%e!Z#u%e!Z#v%e!Z#x%e!Z#z%e!Z#{%e!Z'z%e!Z(a%e!Z(r%e!Z!k%e!Z!Y%e!Z'w%e!Z#`%e!Zv%e!Z!_%e!Z%i%e!Z!g%e!Z~P#/sOr;qO!g#vO(r'pO~O!Y;rO~P#/sOr;yO!g#vO(r'pO~O!Y;zO~P#/sOP%e!ZR%e!Z[%e!Zj%e!Zr%e!Z!S%e!Z!l%e!Z!p%e!Z#R%e!Z#n%e!Z#o%e!Z#p%e!Z#q%e!Z#r%e!Z#s%e!Z#t%e!Z#u%e!Z#v%e!Z#x%e!Z#z%e!Z#{%e!Z$O%e!Z(a%e!Z(r%e!Z!]%e!Z!^%e!Z~P'&kOr;}O!g#vO(r'pO~Ov(fX~P1qO!Q%rO~P!)[O(U!lO~P!)[O!YfX!]fX#`fX~P%1VOP]XR]X[]Xj]Xr]X!Q]X!S]X!]]X!]fX!l]X!p]X#R]X#S]X#`]X#`fX#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(a]X(r]X(y]X(z]X~O!gfX!k]X!kfX(rfX~P'KiOP<ROQ<ROSfOd>OOe!iOpkOr<ROskOtkOzkO|<RO!O<RO!SWO!WkO!XkO!_XO!i<UO!lZO!o<RO!p<RO!q<RO!s<VO!u<YO!x!hO$W!kO$n=|O(T)]O(VTO(YUO(aVO(o[O~O!]<fO!^$qa~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<qO!S${O!_$|O!i>TO!l$xO#j<wO$W%`O$t<sO$v<uO$y%aO(T(vO(VTO(YUO(a$uO(y$}O(z%PO~Ol)dO~P(!_Or!eX(r!eX~P#!iOr(jX(r(jX~P##[O!^]X!^fX~P'KiO!YfX!Y$zX!]fX!]$zX#`fX~P!0SO#k<ZO~O!g#vO#k<ZO~O#`<kO~Oj<_O~O#`<{O!](wX!^(wX~O#`<kO!](uX!^(uX~O#k<|O~Og=OO~P!1WO#k=UO~O#k=VO~Og=OO(T&ZO~O!g#vO#k=WO~O!g#vO#k<|O~O$O=XO~P#BwO#k=YO~O#k=ZO~O#k=`O~O#k=aO~O#k=bO~O#k=cO~O$O=dO~P!1WO$O=eO~P!1WOl=pO~P7eOk#S#T#U#W#X#[#i#j#u$n$t$v$y%]%^%h%i%j%q%s%v%w%y%{~(OT#o!X'|(U#ps#n#qr!Q'}$]'}(T$_(e~",
    goto: "$9V)]PPPPPP)^PP)aP)rP+W/]PPPP6mPP7TPP=QPPP@tPA^PA^PPPA^PCfPA^PA^PA^PCjPCoPD^PIWPPPI[PPPPI[L_PPPLeMVPI[PI[PP! eI[PPPI[PI[P!#lI[P!'S!(X!(bP!)U!)Y!)U!,gPPPPPPP!-W!(XPP!-h!/YP!2fI[I[!2k!5w!:e!:e!>dPPP!>lI[PPPPPPPPP!A{P!CYPPI[!DkPI[PI[I[I[I[I[PI[!E}P!IXP!L_P!Lc!Lm!Lq!LqP!IUP!Lu!LuP# {P#!PI[PI[#!V#%[CjA^PA^PA^A^P#&iA^A^#({A^#+sA^#.PA^A^#.o#1T#1T#1Y#1c#1T#1nPP#1TPA^#2WA^#6VA^A^6mPPP#:[PPP#:u#:uP#:uP#;]#:uPP#;cP#;YP#;Y#;v#;Y#<b#<h#<k)aP#<n)aP#<w#<w#<wP)aP)aP)aP)aPP)aP#<}#=QP#=Q)aP#=UP#=XP)aP)aP)aP)aP)aP)a)aPP#=_#=e#=p#=v#=|#>S#>Y#>h#>n#>x#?O#?Y#?`#?p#?v#@h#@z#AQ#AW#Af#A{#Cp#DO#DV#Eq#FP#Gq#HP#HV#H]#Hc#Hm#Hs#Hy#IT#Ig#ImPPPPPPPPPPP#IsPPPPPPP#Jh#Mu$ _$ f$ nPPP$'YP$'c$*[$0u$0x$0{$1z$1}$2U$2^P$2d$2gP$3T$3X$4P$5_$5d$5zPP$6P$6V$6Z$6^$6b$6f$7b$7y$8b$8f$8i$8l$8v$8y$8}$9RR!|RoqOXst!Z#d%m&r&t&u&w,r,w2Y2]Y!vQ'`-d1m5xQ%tvQ%|yQ&T|Q&j!VS'W!e-[Q'f!iS'l!r!yU*j$|*Z*nQ+n%}S+{&V&WQ,c&dQ-b'_Q-l'gQ-t'mQ0Y*pQ1`+}Q1w,dR<x<V%SdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*h+W+[,o,r,w-h-p.O.U.s.z/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_3O3U3j4w6Q6b6c6f6y8q9Q9[S#q]<S!r)_$Z$n'X)s-T-W/U2n4R5t6p:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>PU+O%]<p<qQ+s&PQ,e&gQ,l&oQ0v+fQ0{+hQ1W+tQ2P,jQ3^.fQ5]0zQ5c1RQ6X1xQ7V3bQ8]5dR9b7X'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*h+W+[+f,o,r,w-T-W-h-p.O.U.f.s.z/U/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_2n3O3U3b3j4R4w5t6Q6b6c6f6p6y7X8q9Q9[:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>P!S!nQ!r!v!y!z$|'W'_'`'l'm'n*j*n*p*q-[-b-d-t0Y0]1m5x5z%[$ti#v$b$c$d$x${%O%Q%^%_%c)y*R*T*V*Y*`*f*v*w+e+h,R,U.e/O/c/l/v/w/y0^0`0g0h0m1d1g1o3a4[4]4g4l4}5X5[6P7T7s7}8S8X8n9_9m9v9|:]:o:};X;a;h<i<j<l<m<n<o<r<s<t<u<v<w=P=Q=R=S=U=V=Y=Z=[=]=^=_=`=a=d=e=|>U>V>Y>ZQ&X|Q'U!eS'[%i-_Q+s&PQ,O&WQ,e&gQ0l+RQ1W+tQ1]+zQ2O,iQ2P,jQ5c1RQ5l1_Q6X1xQ6[1zQ6]1}Q8]5dQ8`5iQ8y6_Q:U8aQ:c8vQ;S:VR<z*ZrnOXst!V!Z#d%m&i&r&t&u&w,r,w2Y2]R,g&k&z^OPXYstuvwz!Z!`!g!j!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'b'r(V(](d(x(z)O)s)}*h+W+[+f,o,r,w-T-W-h-p.O.U.f.s.z/U/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_2n3O3U3b3j4R4w5t6Q6b6c6f6p6y7X8q9Q9[:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>O>P[#]WZ#W#Z'X(T!b%jm#h#i#l$x%e%h(^(h(i(j*Y*^*a+Y+Z+],n-U.S.Y.Z.[.^/l/o2b3Y3Z4_6o7QQ%wxQ%{yW&Q|&V&W+}Q&_!TQ'c!hQ'e!iQ(q#sS+m%|%}Q+q&PQ,^&bQ,b&dS-k'f'gQ.h(rQ1P+nQ1V+tQ1X+uQ1[+yQ1r,_S1v,c,dQ2z-lQ5b1RQ5f1UQ5k1^Q6W1wQ8[5dQ8_5hQ8c5mQ:Q8ZR;Q:R!U$zi$d%O%Q%^%_%c*R*T*`*v*w/O/v0^0`0g0h0m4]4}8S9m=|>U>V!^%yy!i!u%{%|%}'V'e'f'g'k'u*i+m+n-X-k-l-s0P0S1P2s2z3R4o4p4s7z9xQ+g%wQ,S&[Q,V&]Q,a&dQ.g(qQ1q,^U1u,b,c,dQ3c.hQ6R1rS6V1v1wQ8u6W#f>Q#v$b$c$x${)y*V*Y*f+e+h,R,U.e/c/l/w/y1d1g1o3a4[4g4l5X5[6P7T7s7}8X8n9_9v9|:]:o:};X;a;h<l<n<r<t<v=P=R=U=Y=[=^=`=d>Y>Zo>R<i<j<m<o<s<u<w=Q=S=V=Z=]=_=a=eW%Ti%V*x=|S&[!Q&iQ&]!RQ&^!SU*|%[%d=pR,Q&Y%]%Si#v$b$c$d$x${%O%Q%^%_%c)y*R*T*V*Y*`*f*v*w+e+h,R,U.e/O/c/l/v/w/y0^0`0g0h0m1d1g1o3a4[4]4g4l4}5X5[6P7T7s7}8S8X8n9_9m9v9|:]:o:};X;a;h<i<j<l<m<n<o<r<s<t<u<v<w=P=Q=R=S=U=V=Y=Z=[=]=^=_=`=a=d=e=|>U>V>Y>ZT)z$u){V+O%]<p<qW'[!e%i*Z-_S(}#y#zQ+b%rQ+x&SS.a(m(nQ1h,WQ5Q0iR8f5r'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*h+W+[+f,o,r,w-T-W-h-p.O.U.f.s.z/U/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_2n3O3U3b3j4R4w5t6Q6b6c6f6p6y7X8q9Q9[:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>P$i$^c#Y#e%q%s%u(S(Y(t(y)R)S)T)U)V)W)X)Y)Z)[)^)`)b)g)q+c+w-Y-w-|.R.T.r.u.y.{.|.}/a0n2i2l2|3T3i3n3o3p3q3r3s3t3u3v3w3x3y3z3}4O4V5U5`6r6x6}7^7_7h7i8h9U9Y9d9j9k:l;T;]<T=sT#TV#U'RkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*h+W+[+f,o,r,w-T-W-h-p.O.U.f.s.z/U/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_2n3O3U3b3j4R4w5t6Q6b6c6f6p6y7X8q9Q9[:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>PQ'Y!eR2o-[!W!nQ!e!r!v!y!z$|'W'_'`'l'm'n*Z*j*n*p*q-[-b-d-t0Y0]1m5x5zR1j,YnqOXst!Z#d%m&r&t&u&w,r,w2Y2]Q&y!^Q'v!xS(s#u<ZQ+k%zQ,[&_Q,]&aQ-i'dQ-v'oS.q(x<|S0o+W=WQ0}+lQ1l,ZQ2a,yQ2c,zQ2k-VQ2x-jQ2{-nS5V0p=bQ5^1OS5a1Q=cQ6q2mQ6u2yQ6z3QQ8Y5_Q9V6sQ9W6vQ9Z6{R:i9S$d$]c#Y#e%s%u(S(Y(t(y)R)S)T)U)V)W)X)Y)Z)[)^)`)b)g)q+c+w-Y-w-|.R.T.r.u.y.|.}/a0n2i2l2|3T3i3n3o3p3q3r3s3t3u3v3w3x3y3z3}4O4V5U5`6r6x6}7^7_7h7i8h9U9Y9d9j9k:l;T;]<T=sS(o#p'iQ)P#zS+a%q.{S.b(n(pR3[.c'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*h+W+[+f,o,r,w-T-W-h-p.O.U.f.s.z/U/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_2n3O3U3b3j4R4w5t6Q6b6c6f6p6y7X8q9Q9[:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>PS#q]<SQ&t!XQ&u!YQ&w![Q&x!]R2X,uQ'a!hQ+d%wQ-g'cS.d(q+gQ2v-fW3`.g.h0u0wQ6t2wW7R3]3_3c5ZU9^7S7U7WU:n9`9a9cS;_:m:pQ;m;`R;u;nU!wQ'`-dT5v1m5x!Q_OXZ`st!V!Z#d#h%e%m&i&k&r&t&u&w(j,r,w.Z2Y2]]!pQ!r'`-d1m5xT#q]<S%^{OPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&o&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*h+W+[+f,o,r,w-h-p.O.U.f.s.z/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_3O3U3b3j4w6Q6b6c6f6y7X8q9Q9[S(}#y#zS.a(m(n!s=i$Z$n'X)s-T-W/U2n4R5t6p:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>PU$fd)_,lS(p#p'iU*u%R(w3|U0k*}.m7dQ5Z0vQ7S3^Q9a7VR:p9bm!tQ!r!v!y!z'`'l'm'n-d-t1m5x5zQ't!uS(f#g2SS-r'k'wQ/r*]Q0P*iQ3S-uQ4c/sQ4o0RQ4p0SQ4u0[Q7o4^S7z4q4sS8O4v4xQ9o7pQ9s7vQ9x7{Q9}8QS:x9t9uS;d:y:|S;p;e;fS;x;q;rS;|;y;zR<P;}Q#wbQ's!uS(e#g2SS(g#m+VQ+X%fQ+i%xQ+o&OU-q'k't'wQ.V(fQ/q*]Q0Q*iQ0T*kQ0|+jQ1s,`S3P-r-uQ3X._S4b/r/sQ4k/}S4n0P0[Q4r0UQ6T1tQ6|3SQ7n4^Q7r4cU7y4o4u4xQ7|4tQ8s6US9n7o7pQ9r7vQ9z8OQ9{8PQ:`8tQ:v9oS:w9s9uQ;P9}Q;Z:aS;c:x:|S;o;d;eS;w;p;rS;{;x;zQ<O;|Q<Q<PQ=l=gQ=x=qR=y=rV!wQ'`-d%^aOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&o&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*h+W+[+f,o,r,w-h-p.O.U.f.s.z/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_3O3U3b3j4w6Q6b6c6f6y7X8q9Q9[S#wz!j!r=f$Z$n'X)s-T-W/U2n4R5t6p:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>PR=l>O%^bOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&o&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*h+W+[+f,o,r,w-h-p.O.U.f.s.z/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_3O3U3b3j4w6Q6b6c6f6y7X8q9Q9[Q%fj!^%xy!i!u%{%|%}'V'e'f'g'k'u*i+m+n-X-k-l-s0P0S1P2s2z3R4o4p4s7z9xS&Oz!jQ+j%yQ,`&dW1t,a,b,c,dU6U1u1v1wS8t6V6WQ:a8u!r=g$Z$n'X)s-T-W/U2n4R5t6p:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>PQ=q=}R=r>O%QeOPXYstuvw!Z!`!g!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&r&t&u&w&{'T'b'r(V(](d(x(z)O)}*h+W+[+f,o,r,w-h-p.O.U.f.s.z/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_3O3U3b3j4w6Q6b6c6f6y7X8q9Q9[Y#bWZ#W#Z(T!b%jm#h#i#l$x%e%h(^(h(i(j*Y*^*a+Y+Z+],n-U.S.Y.Z.[.^/l/o2b3Y3Z4_6o7QQ,m&o!p=h$Z$n)s-T-W/U2n4R5t6p:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>PR=k'XU']!e%i*ZR2q-_%SdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*h+W+[,o,r,w-h-p.O.U.s.z/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_3O3U3j4w6Q6b6c6f6y8q9Q9[!r)_$Z$n'X)s-T-W/U2n4R5t6p:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>PQ,l&oQ0v+fQ3^.fQ7V3bR9b7X!b$Tc#Y%q(S(Y(t(y)Z)[)`)g+w-w-|.R.T.r.u/a0n2|3T3i3y5U5`6x6}7^9Y:l<T!P<a)^)q-Y.{2i2l3n3w3x3}4V6r7_7h7i8h9U9d9j9k;T;]=s!f$Vc#Y%q(S(Y(t(y)W)X)Z)[)`)g+w-w-|.R.T.r.u/a0n2|3T3i3y5U5`6x6}7^9Y:l<T!T<c)^)q-Y.{2i2l3n3t3u3w3x3}4V6r7_7h7i8h9U9d9j9k;T;]=s!^$Zc#Y%q(S(Y(t(y)`)g+w-w-|.R.T.r.u/a0n2|3T3i3y5U5`6x6}7^9Y:l<TQ4]/jz>P)^)q-Y.{2i2l3n3}4V6r7_7h7i8h9U9d9j9k;T;]=sQ>U>WR>V>X'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*h+W+[+f,o,r,w-T-W-h-p.O.U.f.s.z/U/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_2n3O3U3b3j4R4w5t6Q6b6c6f6p6y7X8q9Q9[:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>PS$oh$pR4S/T'XgOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n$p%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*h+W+[+f,o,r,w-T-W-h-p.O.U.f.s.z/T/U/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_2n3O3U3b3j4R4w5t6Q6b6c6f6p6y7X8q9Q9[:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>PT$kf$qQ$ifS)j$l)nR)v$qT$jf$qT)l$l)n'XhOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n$p%m%t&R&k&n&o&r&t&u&w&{'T'X'b'r(T(V(](d(x(z)O)s)}*h+W+[+f,o,r,w-T-W-h-p.O.U.f.s.z/T/U/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_2n3O3U3b3j4R4w5t6Q6b6c6f6p6y7X8q9Q9[:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>PT$oh$pQ$rhR)u$p%^jOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%m%t&R&k&n&o&r&t&u&w&{'T'b'r(T(V(](d(x(z)O)}*h+W+[+f,o,r,w-h-p.O.U.f.s.z/m0Z0j0p1Q1p2Q2R2T2V2Y2]2_3O3U3b3j4w6Q6b6c6f6y7X8q9Q9[!s=}$Z$n'X)s-T-W/U2n4R5t6p:W:j<R<U<V<Y<Z<[<]<^<_<`<a<b<c<d<e<f<h<k<x<{<|=O=W=X=b=c>P#glOPXZst!Z!`!o#S#d#o#{$n%m&k&n&o&r&t&u&w&{'T'b)O)s*h+[+f,o,r,w-h.f/U/m0Z0j1p2Q2R2T2V2Y2]2_3b4R4w6Q6b6c6f7X8q9Q!U%Ri$d%O%Q%^%_%c*R*T*`*v*w/O/v0^0`0g0h0m4]4}8S9m=|>U>V#f(w#v$b$c$x${)y*V*Y*f+e+h,R,U.e/c/l/w/y1d1g1o3a4[4g4l5X5[6P7T7s7}8X8n9_9v9|:]:o:};X;a;h<l<n<r<t<v=P=R=U=Y=[=^=`=d>Y>ZQ+S%aQ/b*Oo3|<i<j<m<o<s<u<w=Q=S=V=Z=]=_=a=e!U$yi$d%O%Q%^%_%c*R*T*`*v*w/O/v0^0`0g0h0m4]4}8S9m=|>U>VQ*b$zU*k$|*Z*nQ+T%bQ0U*l#f=n#v$b$c$x${)y*V*Y*f+e+h,R,U.e/c/l/w/y1d1g1o3a4[4g4l5X5[6P7T7s7}8X8n9_9v9|:]:o:};X;a;h<l<n<r<t<v=P=R=U=Y=[=^=`=d>Y>Zn=o<i<j<m<o<s<u<w=Q=S=V=Z=]=_=a=eQ=t>QQ=u>RQ=v>SR=w>T!U%Ri$d%O%Q%^%_%c*R*T*`*v*w/O/v0^0`0g0h0m4]4}8S9m=|>U>V#f(w#v$b$c$x${)y*V*Y*f+e+h,R,U.e/c/l/w/y1d1g1o3a4[4g4l5X5[6P7T7s7}8X8n9_9v9|:]:o:};X;a;h<l<n<r<t<v=P=R=U=Y=[=^=`=d>Y>Zo3|<i<j<m<o<s<u<w=Q=S=V=Z=]=_=a=enoOXst!Z#d%m&r&t&u&w,r,w2Y2]S*e${*YQ-Q'OQ-R'QR4f/w%[%Si#v$b$c$d$x${%O%Q%^%_%c)y*R*T*V*Y*`*f*v*w+e+h,R,U.e/O/c/l/v/w/y0^0`0g0h0m1d1g1o3a4[4]4g4l4}5X5[6P7T7s7}8S8X8n9_9m9v9|:]:o:};X;a;h<i<j<l<m<n<o<r<s<t<u<v<w=P=Q=R=S=U=V=Y=Z=[=]=^=_=`=a=d=e=|>U>V>Y>ZQ,T&]Q1f,VQ5p1eR8e5qV*m$|*Z*nU*m$|*Z*nT5w1m5xS/}*h/mQ4t0ZT8P4w:YQ+i%xQ0T*kQ0|+jQ1s,`Q6T1tQ8s6UQ:`8tR;Z:a!U%Oi$d%O%Q%^%_%c*R*T*`*v*w/O/v0^0`0g0h0m4]4}8S9m=|>U>Vx*R$v)e*S*t+U/u0b0c4P4d5O5P5T7m8R:O:u=m=z={S0^*s0_#f<l#v$b$c$x${)y*V*Y*f+e+h,R,U.e/c/l/w/y1d1g1o3a4[4g4l5X5[6P7T7s7}8X8n9_9v9|:]:o:};X;a;h<l<n<r<t<v=P=R=U=Y=[=^=`=d>Y>Zn<m<i<j<m<o<s<u<w=Q=S=V=Z=]=_=a=e!d=P(u)c*[*d.i.l.p/^/j/z0t1c3f4Y4e4i5o7Y7]7t7w8U8W9q9y:P:z;O;b;g;s>W>X`=Q3{7`7c7g9e:q:t;vS=[.k3gT=]7b9h!U%Qi$d%O%Q%^%_%c*R*T*`*v*w/O/v0^0`0g0h0m4]4}8S9m=|>U>V|*T$v)e*U*s+U/f/u0b0c4P4d4y5O5P5T7m8R:O:u=m=z={S0`*t0a#f<n#v$b$c$x${)y*V*Y*f+e+h,R,U.e/c/l/w/y1d1g1o3a4[4g4l5X5[6P7T7s7}8X8n9_9v9|:]:o:};X;a;h<l<n<r<t<v=P=R=U=Y=[=^=`=d>Y>Zn<o<i<j<m<o<s<u<w=Q=S=V=Z=]=_=a=e!h=R(u)c*[*d.j.k.p/^/j/z0t1c3d3f4Y4e4i5o7Y7Z7]7t7w8U8W9q9y:P:z;O;b;g;s>W>Xd=S3{7a7b7g9e9f:q:r:t;vS=^.l3hT=_7c9irnOXst!V!Z#d%m&i&r&t&u&w,r,w2Y2]Q&f!UR,o&ornOXst!V!Z#d%m&i&r&t&u&w,r,w2Y2]R&f!UQ,X&^R1b,QsnOXst!V!Z#d%m&i&r&t&u&w,r,w2Y2]Q1n,^S6O1q1rU8m5|5}6RS:[8o8pS;V:Z:^Q;j;WR;t;kQ&m!VR,h&iR6[1zR:c8vW&Q|&V&W+}R1X+uQ&r!WR,r&sR,x&xT2Z,w2]R,|&yQ,{&yR2d,|Q'y!{R-x'ySsOtQ#dXT%ps#dQ#OTR'{#OQ#RUR'}#RQ){$uR/_){Q#UVR(Q#UQ#XWU(W#X(X.PQ(X#YR.P(YQ-]'YR2p-]Q.t(yS3k.t3lR3l.uQ-d'`R2t-dY!rQ'`-d1m5xR'j!rQ/P)eR4Q/PU#_W%h*YU(_#_(`.QQ(`#`R.Q(ZQ-`']R2r-`t`OXst!V!Z#d%m&i&k&r&t&u&w,r,w2Y2]S#hZ%eU#r`#h.ZR.Z(jQ(k#jQ.W(gW.`(k.W3V7OQ3V.XR7O3WQ)n$lR/V)nQ$phR)t$pQ$`cU)a$`-{<gQ-{<TR<g)qQ/p*]W4`/p4a7q9pU4a/q/r/sS7q4b4cR9p7r$e*Q$v(u)c)e*[*d*s*t+P+Q+U.k.l.n.o.p/^/f/h/j/u/z0b0c0t1c3d3e3f3{4P4Y4d4e4i4y4{5O5P5T5o7Y7Z7[7]7b7c7e7f7g7m7t7w8R8U8W9e9f9g9q9y:O:P:q:r:s:t:u:z;O;b;g;s;v=m=z={>W>XQ/x*dU4h/x4j7uQ4j/zR7u4iS*n$|*ZR0W*nx*S$v)e*s*t+U/u0b0c4P4d5O5P5T7m8R:O:u=m=z={!d.i(u)c*[*d.k.l.p/^/j/z0t1c3f4Y4e4i5o7Y7]7t7w8U8W9q9y:P:z;O;b;g;s>W>XU/g*S.i7`a7`3{7b7c7g9e:q:t;vQ0_*sQ3g.kU4z0_3g9hR9h7b|*U$v)e*s*t+U/f/u0b0c4P4d4y5O5P5T7m8R:O:u=m=z={!h.j(u)c*[*d.k.l.p/^/j/z0t1c3d3f4Y4e4i5o7Y7Z7]7t7w8U8W9q9y:P:z;O;b;g;s>W>XU/i*U.j7ae7a3{7b7c7g9e9f:q:r:t;vQ0a*tQ3h.lU4|0a3h9iR9i7cQ*y%UR0e*yQ5Y0tR8V5YQ+^%kR0s+^Q5s1hS8g5s:XR:X8hQ,Z&_R1k,ZQ5x1mR8j5xQ1y,eS6Y1y8wR8w6[Q1S+qW5e1S5g8^:SQ5g1VQ8^5fR:S8_Q+v&QR1Y+vQ2],wR6j2]YrOXst#dQ&v!ZQ+`%mQ,q&rQ,s&tQ,t&uQ,v&wQ2W,rS2Z,w2]R6i2YQ%opQ&z!_Q&}!aQ'P!bQ'R!cQ'q!uQ+_%lQ+k%zQ,P&XQ,g&mQ-O&|W-o'k's't'wQ-v'oQ0V*mQ0}+lQ1a,OS1|,h,kQ2e,}Q2f-QQ2g-RQ2{-nW2}-q-r-u-wQ5^1OQ5j1]Q5n1cQ6S1sQ6^2OQ6h2XU6w2|3P3SQ6z3QQ8Y5_Q8b5lQ8d5oQ8i5wQ8r6TQ8x6]S9X6x6|Q9Z6{Q:T8`Q:_8sQ:d8yQ:k9YQ;R:UQ;Y:`Q;^:lQ;i;SR;l;ZQ%zyQ'd!iQ'o!uU+l%{%|%}Q-V'VU-j'e'f'gS-n'k'uQ0O*iS1O+m+nQ2m-XS2y-k-lQ3Q-sS4m0P0SQ5_1PQ6s2sQ6v2zQ6{3RU7x4o4p4sQ9w7zR:{9xS$wi=|R*z%VU%Ui%V=|R0d*xQ$viS(u#v+hS)c$b$cQ)e$dQ*[$xS*d${*YQ*s%OQ*t%QQ+P%^Q+Q%_Q+U%cQ.k<lQ.l<nQ.n<rQ.o<tQ.p<vQ/^)yQ/f*RQ/h*TQ/j*VQ/u*`S/z*f/lQ0b*vQ0c*wl0t+e,U.e1g1o3a6P7T8n9_:]:o;X;aQ1c,RQ3d=PQ3e=RQ3f=US3{<i<jQ4P/OS4Y/c4[Q4d/vQ4e/wQ4i/yQ4y0^Q4{0`Q5O0gQ5P0hQ5T0mQ5o1dQ7Y=YQ7Z=[Q7[=^Q7]=`Q7b<mQ7c<oQ7e<sQ7f<uQ7g<wQ7m4]Q7t4gQ7w4lQ8R4}Q8U5XQ8W5[Q9e=VQ9f=QQ9g=SQ9q7sQ9y7}Q:O8SQ:P8XQ:q=ZQ:r=]Q:s=_Q:t=aQ:u9mQ:z9vQ;O9|Q;b=dQ;g:}Q;s;hQ;v=eQ=m=|Q=z>UQ={>VQ>W>YR>X>ZQ*}%]Q.m<pR7d<qnpOXst!Z#d%m&r&t&u&w,r,w2Y2]Q!fPS#fZ#oQ&|!`W'h!o*h0Z4wQ(P#SQ)Q#{Q)r$nS,k&k&nQ,p&oQ,}&{S-S'T/mQ-f'bQ.w)OQ/Z)sQ0q+[Q0w+fQ2U,oQ2w-hQ3_.fQ4U/UQ5R0jQ5}1pQ6`2QQ6a2RQ6e2TQ6g2VQ6l2_Q7W3bQ7j4RQ8p6QQ8|6bQ8}6cQ9P6fQ9c7XQ:^8qR:h9Q#[cOPXZst!Z!`!o#d#o#{%m&k&n&o&r&t&u&w&{'T'b)O*h+[+f,o,r,w-h.f/m0Z0j1p2Q2R2T2V2Y2]2_3b4w6Q6b6c6f7X8q9QQ#YWQ#eYQ%quQ%svS%uw!gS(S#W(VQ(Y#ZQ(t#uQ(y#xQ)R$OQ)S$PQ)T$QQ)U$RQ)V$SQ)W$TQ)X$UQ)Y$VQ)Z$WQ)[$XQ)^$ZQ)`$_Q)b$aQ)g$eW)q$n)s/U4RQ+c%tQ+w&RS-Y'X2nQ-w'rS-|(T.OQ.R(]Q.T(dQ.r(xQ.u(zQ.y<RQ.{<UQ.|<VQ.}<YQ/a)}Q0n+WQ2i-TQ2l-WQ2|-pQ3T.UQ3i.sQ3n<ZQ3o<[Q3p<]Q3q<^Q3r<_Q3s<`Q3t<aQ3u<bQ3v<cQ3w<dQ3x<eQ3y.zQ3z<hQ3}<kQ4O<xQ4V<fQ5U0pQ5`1QQ6r<{Q6x3OQ6}3UQ7^3jQ7_<|Q7h=OQ7i=WQ8h5tQ9U6pQ9Y6yQ9d=XQ9j=bQ9k=cQ:l9[Q;T:WQ;]:jQ<T#SR=s>PR#[WR'Z!el!tQ!r!v!y!z'`'l'm'n-d-t1m5x5zS'V!e-[U*i$|*Z*nS-X'W'_S0S*j*pQ0[*qQ2s-bQ4s0YR4x0]R({#xQ!fQT-c'`-d]!qQ!r'`-d1m5xQ#p]R'i<SR)f$dY!uQ'`-d1m5xQ'k!rS'u!v!yS'w!z5zS-s'l'mQ-u'nR3R-tT#kZ%eS#jZ%eS%km,nU(g#h#i#lS.X(h(iQ.](jQ0r+]Q3W.YU3X.Z.[.^S7P3Y3ZR9]7Qd#^W#W#Z%h(T(^*Y+Y.S/lr#gZm#h#i#l%e(h(i(j+].Y.Z.[.^3Y3Z7QS*]$x*aQ/s*^Q2S,nQ2j-UQ4^/oQ6n2bQ7p4_Q9T6oT=j'X+ZV#aW%h*YU#`W%h*YS(U#W(^U(Z#Z+Y/lS-Z'X+ZT-}(T.SV'^!e%i*ZQ$lfR)x$qT)m$l)nR4T/TT*_$x*aT*g${*YQ0u+eQ1e,UQ3].eQ5q1gQ5|1oQ7U3aQ8o6PQ9`7TQ:Z8nQ:m9_Q;W:]Q;`:oQ;k;XR;n;anqOXst!Z#d%m&r&t&u&w,r,w2Y2]Q&l!VR,g&itmOXst!U!V!Z#d%m&i&r&t&u&w,r,w2Y2]R,n&oT%lm,nR1i,WR,f&gQ&U|S+|&V&WR1[+}R+r&PT&p!W&sT&q!W&sT2[,w2]",
    nodeNames: "⚠ ArithOp ArithOp ?. JSXStartTag LineComment BlockComment Script Hashbang ExportDeclaration export Star as VariableName String Escape from ; default FunctionDeclaration async function VariableDefinition > < TypeParamList in out const TypeDefinition extends ThisType this LiteralType ArithOp Number BooleanLiteral TemplateType InterpolationEnd Interpolation InterpolationStart NullType null VoidType void TypeofType typeof MemberExpression . PropertyName [ TemplateString Escape Interpolation super RegExp ] ArrayExpression Spread , } { ObjectExpression Property async get set PropertyDefinition Block : NewTarget new NewExpression ) ( ArgList UnaryExpression delete LogicOp BitOp YieldExpression yield AwaitExpression await ParenthesizedExpression ClassExpression class ClassBody MethodDeclaration Decorator @ MemberExpression PrivatePropertyName CallExpression TypeArgList CompareOp < declare Privacy static abstract override PrivatePropertyDefinition PropertyDeclaration readonly accessor Optional TypeAnnotation Equals StaticBlock FunctionExpression ArrowFunction ParamList ParamList ArrayPattern ObjectPattern PatternProperty Privacy readonly Arrow MemberExpression BinaryExpression ArithOp ArithOp ArithOp ArithOp BitOp CompareOp instanceof satisfies CompareOp BitOp BitOp BitOp LogicOp LogicOp ConditionalExpression LogicOp LogicOp AssignmentExpression UpdateOp PostfixExpression CallExpression InstantiationExpression TaggedTemplateExpression DynamicImport import ImportMeta JSXElement JSXSelfCloseEndTag JSXSelfClosingTag JSXIdentifier JSXBuiltin JSXIdentifier JSXNamespacedName JSXMemberExpression JSXSpreadAttribute JSXAttribute JSXAttributeValue JSXEscape JSXEndTag JSXOpenTag JSXFragmentTag JSXText JSXEscape JSXStartCloseTag JSXCloseTag PrefixCast < ArrowFunction TypeParamList SequenceExpression InstantiationExpression KeyofType keyof UniqueType unique ImportType InferredType infer TypeName ParenthesizedType FunctionSignature ParamList NewSignature IndexedType TupleType Label ArrayType ReadonlyType ObjectType MethodType PropertyType IndexSignature PropertyDefinition CallSignature TypePredicate asserts is NewSignature new UnionType LogicOp IntersectionType LogicOp ConditionalType ParameterizedType ClassDeclaration abstract implements type VariableDeclaration let var using TypeAliasDeclaration InterfaceDeclaration interface EnumDeclaration enum EnumBody NamespaceDeclaration namespace module AmbientDeclaration declare GlobalDeclaration global ClassDeclaration ClassBody AmbientFunctionDeclaration ExportGroup VariableName VariableName ImportDeclaration defer ImportGroup ForStatement for ForSpec ForInSpec ForOfSpec of WhileStatement while WithStatement with DoStatement do IfStatement if else SwitchStatement switch SwitchBody CaseLabel case DefaultLabel TryStatement try CatchClause catch FinallyClause finally ReturnStatement return ThrowStatement throw BreakStatement break ContinueStatement continue DebuggerStatement debugger LabeledStatement ExpressionStatement SingleExpression SingleClassItem",
    maxTerm: 380,
    context: trackNewline,
    nodeProps: [
        [
            "isolate",
            -8,
            5,
            6,
            14,
            37,
            39,
            51,
            53,
            55,
            ""
        ],
        [
            "group",
            -26,
            9,
            17,
            19,
            68,
            207,
            211,
            215,
            216,
            218,
            221,
            224,
            234,
            237,
            243,
            245,
            247,
            249,
            252,
            258,
            264,
            266,
            268,
            270,
            272,
            274,
            275,
            "Statement",
            -34,
            13,
            14,
            32,
            35,
            36,
            42,
            51,
            54,
            55,
            57,
            62,
            70,
            72,
            76,
            80,
            82,
            84,
            85,
            110,
            111,
            120,
            121,
            136,
            139,
            141,
            142,
            143,
            144,
            145,
            147,
            148,
            167,
            169,
            171,
            "Expression",
            -23,
            31,
            33,
            37,
            41,
            43,
            45,
            173,
            175,
            177,
            178,
            180,
            181,
            182,
            184,
            185,
            186,
            188,
            189,
            190,
            201,
            203,
            205,
            206,
            "Type",
            -3,
            88,
            103,
            109,
            "ClassItem"
        ],
        [
            "openedBy",
            23,
            "<",
            38,
            "InterpolationStart",
            56,
            "[",
            60,
            "{",
            73,
            "(",
            160,
            "JSXStartCloseTag"
        ],
        [
            "closedBy",
            -2,
            24,
            168,
            ">",
            40,
            "InterpolationEnd",
            50,
            "]",
            61,
            "}",
            74,
            ")",
            165,
            "JSXEndTag"
        ]
    ],
    propSources: [
        jsHighlight
    ],
    skippedNodes: [
        0,
        5,
        6,
        278
    ],
    repeatNodeCount: 37,
    tokenData: "$Fq07[R!bOX%ZXY+gYZ-yZ[+g[]%Z]^.c^p%Zpq+gqr/mrs3cst:_tuEruvJSvwLkwx! Yxy!'iyz!(sz{!)}{|!,q|}!.O}!O!,q!O!P!/Y!P!Q!9j!Q!R#:O!R![#<_![!]#I_!]!^#Jk!^!_#Ku!_!`$![!`!a$$v!a!b$*T!b!c$,r!c!}Er!}#O$-|#O#P$/W#P#Q$4o#Q#R$5y#R#SEr#S#T$7W#T#o$8b#o#p$<r#p#q$=h#q#r$>x#r#s$@U#s$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$I|Er$I|$I}$Dk$I}$JO$Dk$JO$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr(n%d_$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&j&hT$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c&j&zP;=`<%l&c'|'U]$i&j(Z!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!b(SU(Z!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!b(iP;=`<%l'}'|(oP;=`<%l&}'[(y]$i&j(WpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(rp)wU(WpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)rp*^P;=`<%l)r'[*dP;=`<%l(r#S*nX(Wp(Z!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g#S+^P;=`<%l*g(n+dP;=`<%l%Z07[+rq$i&j(Wp(Z!b'|0/lOX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p$f%Z$f$g+g$g#BY%Z#BY#BZ+g#BZ$IS%Z$IS$I_+g$I_$JT%Z$JT$JU+g$JU$KV%Z$KV$KW+g$KW&FU%Z&FU&FV+g&FV;'S%Z;'S;=`+a<%l?HT%Z?HT?HU+g?HUO%Z07[.ST(X#S$i&j'}0/lO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c07[.n_$i&j(Wp(Z!b'}0/lOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)3p/x`$i&j!p),Q(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW1V`#v(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`2X!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW2d_#v(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At3l_(V':f$i&j(Z!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k(^4r_$i&j(Z!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k&z5vX$i&jOr5qrs6cs!^5q!^!_6y!_#o5q#o#p6y#p;'S5q;'S;=`7h<%lO5q&z6jT$d`$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c`6|TOr6yrs7]s;'S6y;'S;=`7b<%lO6y`7bO$d``7eP;=`<%l6y&z7kP;=`<%l5q(^7w]$d`$i&j(Z!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!r8uZ(Z!bOY8pYZ6yZr8prs9hsw8pwx6yx#O8p#O#P6y#P;'S8p;'S;=`:R<%lO8p!r9oU$d`(Z!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!r:UP;=`<%l8p(^:[P;=`<%l4k%9[:hh$i&j(Wp(Z!bOY%ZYZ&cZq%Zqr<Srs&}st%ZtuCruw%Zwx(rx!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr(r<__WS$i&j(Wp(Z!bOY<SYZ&cZr<Srs=^sw<Swx@nx!^<S!^!_Bm!_#O<S#O#P>`#P#o<S#o#pBm#p;'S<S;'S;=`Cl<%lO<S(Q=g]WS$i&j(Z!bOY=^YZ&cZw=^wx>`x!^=^!^!_?q!_#O=^#O#P>`#P#o=^#o#p?q#p;'S=^;'S;=`@h<%lO=^&n>gXWS$i&jOY>`YZ&cZ!^>`!^!_?S!_#o>`#o#p?S#p;'S>`;'S;=`?k<%lO>`S?XSWSOY?SZ;'S?S;'S;=`?e<%lO?SS?hP;=`<%l?S&n?nP;=`<%l>`!f?xWWS(Z!bOY?qZw?qwx?Sx#O?q#O#P?S#P;'S?q;'S;=`@b<%lO?q!f@eP;=`<%l?q(Q@kP;=`<%l=^'`@w]WS$i&j(WpOY@nYZ&cZr@nrs>`s!^@n!^!_Ap!_#O@n#O#P>`#P#o@n#o#pAp#p;'S@n;'S;=`Bg<%lO@ntAwWWS(WpOYApZrAprs?Ss#OAp#O#P?S#P;'SAp;'S;=`Ba<%lOAptBdP;=`<%lAp'`BjP;=`<%l@n#WBvYWS(Wp(Z!bOYBmZrBmrs?qswBmwxApx#OBm#O#P?S#P;'SBm;'S;=`Cf<%lOBm#WCiP;=`<%lBm(rCoP;=`<%l<S%9[C}i$i&j(o%1l(Wp(Z!bOY%ZYZ&cZr%Zrs&}st%ZtuCruw%Zwx(rx!Q%Z!Q![Cr![!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr%9[EoP;=`<%lCr07[FRk$i&j(Wp(Z!b$]#t(T,2j(e$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr+dHRk$i&j(Wp(Z!b$]#tOY%ZYZ&cZr%Zrs&}st%ZtuGvuw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Gv![!^%Z!^!_*g!_!c%Z!c!}Gv!}#O%Z#O#P&c#P#R%Z#R#SGv#S#T%Z#T#oGv#o#p*g#p$g%Z$g;'SGv;'S;=`Iv<%lOGv+dIyP;=`<%lGv07[JPP;=`<%lEr(KWJ_`$i&j(Wp(Z!b#p(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWKl_$i&j$Q(Ch(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,#xLva(z+JY$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sv%ZvwM{wx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWNW`$i&j#z(Ch(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At! c_(Y';W$i&j(WpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b'l!!i_$i&j(WpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b&z!#mX$i&jOw!#hwx6cx!^!#h!^!_!$Y!_#o!#h#o#p!$Y#p;'S!#h;'S;=`!$r<%lO!#h`!$]TOw!$Ywx7]x;'S!$Y;'S;=`!$l<%lO!$Y`!$oP;=`<%l!$Y&z!$uP;=`<%l!#h'l!%R]$d`$i&j(WpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r!Q!&PZ(WpOY!%zYZ!$YZr!%zrs!$Ysw!%zwx!&rx#O!%z#O#P!$Y#P;'S!%z;'S;=`!']<%lO!%z!Q!&yU$d`(WpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)r!Q!'`P;=`<%l!%z'l!'fP;=`<%l!!b/5|!'t_!l/.^$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#&U!)O_!k!Lf$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z-!n!*[b$i&j(Wp(Z!b(U%&f#q(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rxz%Zz{!+d{!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW!+o`$i&j(Wp(Z!b#n(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;x!,|`$i&j(Wp(Z!br+4YOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,$U!.Z_!]+Jf$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!/ec$i&j(Wp(Z!b!Q.2^OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!0p!P!Q%Z!Q![!3Y![!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!0ya$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!2O!P!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!2Z_![!L^$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!3eg$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!3Y![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S!3Y#S#X%Z#X#Y!4|#Y#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!5Vg$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx{%Z{|!6n|}%Z}!O!6n!O!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!6wc$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!8_c$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!9uf$i&j(Wp(Z!b#o(ChOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcxz!;Zz{#-}{!P!;Z!P!Q#/d!Q!^!;Z!^!_#(i!_!`#7S!`!a#8i!a!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z?O!;fb$i&j(Wp(Z!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z>^!<w`$i&j(Z!b!X7`OY!<nYZ&cZw!<nwx!=yx!P!<n!P!Q!Eq!Q!^!<n!^!_!Gr!_!}!<n!}#O!KS#O#P!Dy#P#o!<n#o#p!Gr#p;'S!<n;'S;=`!L]<%lO!<n<z!>Q^$i&j!X7`OY!=yYZ&cZ!P!=y!P!Q!>|!Q!^!=y!^!_!@c!_!}!=y!}#O!CW#O#P!Dy#P#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!?Td$i&j!X7`O!^&c!_#W&c#W#X!>|#X#Z&c#Z#[!>|#[#]&c#]#^!>|#^#a&c#a#b!>|#b#g&c#g#h!>|#h#i&c#i#j!>|#j#k!>|#k#m&c#m#n!>|#n#o&c#p;'S&c;'S;=`&w<%lO&c7`!@hX!X7`OY!@cZ!P!@c!P!Q!AT!Q!}!@c!}#O!Ar#O#P!Bq#P;'S!@c;'S;=`!CQ<%lO!@c7`!AYW!X7`#W#X!AT#Z#[!AT#]#^!AT#a#b!AT#g#h!AT#i#j!AT#j#k!AT#m#n!AT7`!AuVOY!ArZ#O!Ar#O#P!B[#P#Q!@c#Q;'S!Ar;'S;=`!Bk<%lO!Ar7`!B_SOY!ArZ;'S!Ar;'S;=`!Bk<%lO!Ar7`!BnP;=`<%l!Ar7`!BtSOY!@cZ;'S!@c;'S;=`!CQ<%lO!@c7`!CTP;=`<%l!@c<z!C][$i&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#O!CW#O#P!DR#P#Q!=y#Q#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DWX$i&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DvP;=`<%l!CW<z!EOX$i&jOY!=yYZ&cZ!^!=y!^!_!@c!_#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!EnP;=`<%l!=y>^!Ezl$i&j(Z!b!X7`OY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#W&}#W#X!Eq#X#Z&}#Z#[!Eq#[#]&}#]#^!Eq#^#a&}#a#b!Eq#b#g&}#g#h!Eq#h#i&}#i#j!Eq#j#k!Eq#k#m&}#m#n!Eq#n#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}8r!GyZ(Z!b!X7`OY!GrZw!Grwx!@cx!P!Gr!P!Q!Hl!Q!}!Gr!}#O!JU#O#P!Bq#P;'S!Gr;'S;=`!J|<%lO!Gr8r!Hse(Z!b!X7`OY'}Zw'}x#O'}#P#W'}#W#X!Hl#X#Z'}#Z#[!Hl#[#]'}#]#^!Hl#^#a'}#a#b!Hl#b#g'}#g#h!Hl#h#i'}#i#j!Hl#j#k!Hl#k#m'}#m#n!Hl#n;'S'};'S;=`(f<%lO'}8r!JZX(Z!bOY!JUZw!JUwx!Arx#O!JU#O#P!B[#P#Q!Gr#Q;'S!JU;'S;=`!Jv<%lO!JU8r!JyP;=`<%l!JU8r!KPP;=`<%l!Gr>^!KZ^$i&j(Z!bOY!KSYZ&cZw!KSwx!CWx!^!KS!^!_!JU!_#O!KS#O#P!DR#P#Q!<n#Q#o!KS#o#p!JU#p;'S!KS;'S;=`!LV<%lO!KS>^!LYP;=`<%l!KS>^!L`P;=`<%l!<n=l!Ll`$i&j(Wp!X7`OY!LcYZ&cZr!Lcrs!=ys!P!Lc!P!Q!Mn!Q!^!Lc!^!_# o!_!}!Lc!}#O#%P#O#P!Dy#P#o!Lc#o#p# o#p;'S!Lc;'S;=`#&Y<%lO!Lc=l!Mwl$i&j(Wp!X7`OY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#W(r#W#X!Mn#X#Z(r#Z#[!Mn#[#](r#]#^!Mn#^#a(r#a#b!Mn#b#g(r#g#h!Mn#h#i(r#i#j!Mn#j#k!Mn#k#m(r#m#n!Mn#n#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r8Q# vZ(Wp!X7`OY# oZr# ors!@cs!P# o!P!Q#!i!Q!}# o!}#O#$R#O#P!Bq#P;'S# o;'S;=`#$y<%lO# o8Q#!pe(Wp!X7`OY)rZr)rs#O)r#P#W)r#W#X#!i#X#Z)r#Z#[#!i#[#])r#]#^#!i#^#a)r#a#b#!i#b#g)r#g#h#!i#h#i)r#i#j#!i#j#k#!i#k#m)r#m#n#!i#n;'S)r;'S;=`*Z<%lO)r8Q#$WX(WpOY#$RZr#$Rrs!Ars#O#$R#O#P!B[#P#Q# o#Q;'S#$R;'S;=`#$s<%lO#$R8Q#$vP;=`<%l#$R8Q#$|P;=`<%l# o=l#%W^$i&j(WpOY#%PYZ&cZr#%Prs!CWs!^#%P!^!_#$R!_#O#%P#O#P!DR#P#Q!Lc#Q#o#%P#o#p#$R#p;'S#%P;'S;=`#&S<%lO#%P=l#&VP;=`<%l#%P=l#&]P;=`<%l!Lc?O#&kn$i&j(Wp(Z!b!X7`OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#W%Z#W#X#&`#X#Z%Z#Z#[#&`#[#]%Z#]#^#&`#^#a%Z#a#b#&`#b#g%Z#g#h#&`#h#i%Z#i#j#&`#j#k#&`#k#m%Z#m#n#&`#n#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z9d#(r](Wp(Z!b!X7`OY#(iZr#(irs!Grsw#(iwx# ox!P#(i!P!Q#)k!Q!}#(i!}#O#+`#O#P!Bq#P;'S#(i;'S;=`#,`<%lO#(i9d#)th(Wp(Z!b!X7`OY*gZr*grs'}sw*gwx)rx#O*g#P#W*g#W#X#)k#X#Z*g#Z#[#)k#[#]*g#]#^#)k#^#a*g#a#b#)k#b#g*g#g#h#)k#h#i*g#i#j#)k#j#k#)k#k#m*g#m#n#)k#n;'S*g;'S;=`+Z<%lO*g9d#+gZ(Wp(Z!bOY#+`Zr#+`rs!JUsw#+`wx#$Rx#O#+`#O#P!B[#P#Q#(i#Q;'S#+`;'S;=`#,Y<%lO#+`9d#,]P;=`<%l#+`9d#,cP;=`<%l#(i?O#,o`$i&j(Wp(Z!bOY#,fYZ&cZr#,frs!KSsw#,fwx#%Px!^#,f!^!_#+`!_#O#,f#O#P!DR#P#Q!;Z#Q#o#,f#o#p#+`#p;'S#,f;'S;=`#-q<%lO#,f?O#-tP;=`<%l#,f?O#-zP;=`<%l!;Z07[#.[b$i&j(Wp(Z!b(O0/l!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z07[#/o_$i&j(Wp(Z!bT0/lOY#/dYZ&cZr#/drs#0nsw#/dwx#4Ox!^#/d!^!_#5}!_#O#/d#O#P#1p#P#o#/d#o#p#5}#p;'S#/d;'S;=`#6|<%lO#/d06j#0w]$i&j(Z!bT0/lOY#0nYZ&cZw#0nwx#1px!^#0n!^!_#3R!_#O#0n#O#P#1p#P#o#0n#o#p#3R#p;'S#0n;'S;=`#3x<%lO#0n05W#1wX$i&jT0/lOY#1pYZ&cZ!^#1p!^!_#2d!_#o#1p#o#p#2d#p;'S#1p;'S;=`#2{<%lO#1p0/l#2iST0/lOY#2dZ;'S#2d;'S;=`#2u<%lO#2d0/l#2xP;=`<%l#2d05W#3OP;=`<%l#1p01O#3YW(Z!bT0/lOY#3RZw#3Rwx#2dx#O#3R#O#P#2d#P;'S#3R;'S;=`#3r<%lO#3R01O#3uP;=`<%l#3R06j#3{P;=`<%l#0n05x#4X]$i&j(WpT0/lOY#4OYZ&cZr#4Ors#1ps!^#4O!^!_#5Q!_#O#4O#O#P#1p#P#o#4O#o#p#5Q#p;'S#4O;'S;=`#5w<%lO#4O00^#5XW(WpT0/lOY#5QZr#5Qrs#2ds#O#5Q#O#P#2d#P;'S#5Q;'S;=`#5q<%lO#5Q00^#5tP;=`<%l#5Q05x#5zP;=`<%l#4O01p#6WY(Wp(Z!bT0/lOY#5}Zr#5}rs#3Rsw#5}wx#5Qx#O#5}#O#P#2d#P;'S#5};'S;=`#6v<%lO#5}01p#6yP;=`<%l#5}07[#7PP;=`<%l#/d)3h#7ab$i&j$Q(Ch(Wp(Z!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;ZAt#8vb$Z#t$i&j(Wp(Z!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z'Ad#:Zp$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#U%Z#U#V#?i#V#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#d#Bq#d#l%Z#l#m#Es#m#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#<jk$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#>j_$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#?rd$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#A]f$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Bzc$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Dbe$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#E|g$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Gpi$i&j(Wp(Z!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x#Il_!g$b$i&j$O)Lv(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)[#Jv_al$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f#LS^h#)`#R-<U(Wp(Z!b$n7`OY*gZr*grs'}sw*gwx)rx!P*g!P!Q#MO!Q!^*g!^!_#Mt!_!`$ f!`#O*g#P;'S*g;'S;=`+Z<%lO*g(n#MXX$k&j(Wp(Z!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El#M}Z#r(Ch(Wp(Z!bOY*gZr*grs'}sw*gwx)rx!_*g!_!`#Np!`#O*g#P;'S*g;'S;=`+Z<%lO*g(El#NyX$Q(Ch(Wp(Z!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El$ oX#s(Ch(Wp(Z!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g*)x$!ga#`*!Y$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`!a$#l!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(K[$#w_#k(Cl$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x$%Vag!*r#s(Ch$f#|$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`$&[!`!a$'f!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$&g_#s(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$'qa#r(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`!a$(v!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$)R`#r(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(Kd$*`a(r(Ct$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!a%Z!a!b$+e!b#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$+p`$i&j#{(Ch(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#`$,}_!|$Ip$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f$.X_!S0,v$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(n$/]Z$i&jO!^$0O!^!_$0f!_#i$0O#i#j$0k#j#l$0O#l#m$2^#m#o$0O#o#p$0f#p;'S$0O;'S;=`$4i<%lO$0O(n$0VT_#S$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c#S$0kO_#S(n$0p[$i&jO!Q&c!Q![$1f![!^&c!_!c&c!c!i$1f!i#T&c#T#Z$1f#Z#o&c#o#p$3|#p;'S&c;'S;=`&w<%lO&c(n$1kZ$i&jO!Q&c!Q![$2^![!^&c!_!c&c!c!i$2^!i#T&c#T#Z$2^#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$2cZ$i&jO!Q&c!Q![$3U![!^&c!_!c&c!c!i$3U!i#T&c#T#Z$3U#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$3ZZ$i&jO!Q&c!Q![$0O![!^&c!_!c&c!c!i$0O!i#T&c#T#Z$0O#Z#o&c#p;'S&c;'S;=`&w<%lO&c#S$4PR!Q![$4Y!c!i$4Y#T#Z$4Y#S$4]S!Q![$4Y!c!i$4Y#T#Z$4Y#q#r$0f(n$4lP;=`<%l$0O#1[$4z_!Y#)l$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$6U`#x(Ch$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;p$7c_$i&j(Wp(Z!b(a+4QOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$8qk$i&j(Wp(Z!b(T,2j$_#t(e$I[OY%ZYZ&cZr%Zrs&}st%Ztu$8buw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$8b![!^%Z!^!_*g!_!c%Z!c!}$8b!}#O%Z#O#P&c#P#R%Z#R#S$8b#S#T%Z#T#o$8b#o#p*g#p$g%Z$g;'S$8b;'S;=`$<l<%lO$8b+d$:qk$i&j(Wp(Z!b$_#tOY%ZYZ&cZr%Zrs&}st%Ztu$:fuw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$:f![!^%Z!^!_*g!_!c%Z!c!}$:f!}#O%Z#O#P&c#P#R%Z#R#S$:f#S#T%Z#T#o$:f#o#p*g#p$g%Z$g;'S$:f;'S;=`$<f<%lO$:f+d$<iP;=`<%l$:f07[$<oP;=`<%l$8b#Jf$<{X!_#Hb(Wp(Z!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g,#x$=sa(y+JY$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p#q$+e#q;'S%Z;'S;=`+a<%lO%Z)>v$?V_!^(CdvBr$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z?O$@a_!q7`$i&j(Wp(Z!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$Aq|$i&j(Wp(Z!b'|0/l$]#t(T,2j(e$I[OX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr07[$D|k$i&j(Wp(Z!b'}0/l$]#t(T,2j(e$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr",
    tokenizers: [
        noSemicolon,
        noSemicolonType,
        operatorToken,
        jsx,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        insertSemicolon,
        new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LocalTokenGroup"]("$S~RRtu[#O#Pg#S#T#|~_P#o#pb~gOx~~jVO#i!P#i#j!U#j#l!P#l#m!q#m;'S!P;'S;=`#v<%lO!P~!UO!U~~!XS!Q![!e!c!i!e#T#Z!e#o#p#Z~!hR!Q![!q!c!i!q#T#Z!q~!tR!Q![!}!c!i!}#T#Z!}~#QR!Q![!P!c!i!P#T#Z!P~#^R!Q![#g!c!i#g#T#Z#g~#jS!Q![#g!c!i#g#T#Z#g#q#r!P~#yP;=`<%l!P~$RO(c~~", 141, 340),
        new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$lr$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LocalTokenGroup"]("j~RQYZXz{^~^O(Q~~aP!P!Qd~iO(R~~", 25, 323)
    ],
    topRules: {
        "Script": [
            0,
            7
        ],
        "SingleExpression": [
            1,
            276
        ],
        "SingleClassItem": [
            2,
            277
        ]
    },
    dialects: {
        jsx: 0,
        ts: 15149
    },
    dynamicPrecedences: {
        "80": 1,
        "82": 1,
        "94": 1,
        "169": 1,
        "199": 1
    },
    specialized: [
        {
            term: 327,
            get: (value)=>spec_identifier[value] || -1
        },
        {
            term: 343,
            get: (value)=>spec_word[value] || -1
        },
        {
            term: 95,
            get: (value)=>spec_LessThan[value] || -1
        }
    ],
    tokenPrec: 15175
});
;
}}),
"[project]/node_modules/@codemirror/lang-javascript/dist/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "autoCloseTags": (()=>autoCloseTags),
    "completionPath": (()=>completionPath),
    "esLint": (()=>esLint),
    "javascript": (()=>javascript),
    "javascriptLanguage": (()=>javascriptLanguage),
    "jsxLanguage": (()=>jsxLanguage),
    "localCompletionSource": (()=>localCompletionSource),
    "scopeCompletionSource": (()=>scopeCompletionSource),
    "snippets": (()=>snippets),
    "tsxLanguage": (()=>tsxLanguage),
    "typescriptLanguage": (()=>typescriptLanguage),
    "typescriptSnippets": (()=>typescriptSnippets)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$javascript$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/javascript/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codemirror/language/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codemirror/state/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codemirror/view/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codemirror/autocomplete/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/common/dist/index.js [app-client] (ecmascript)");
;
;
;
;
;
;
/**
A collection of JavaScript-related
[snippets](https://codemirror.net/6/docs/ref/#autocomplete.snippet).
*/ const snippets = [
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("function ${name}(${params}) {\n\t${}\n}", {
        label: "function",
        detail: "definition",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("for (let ${index} = 0; ${index} < ${bound}; ${index}++) {\n\t${}\n}", {
        label: "for",
        detail: "loop",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("for (let ${name} of ${collection}) {\n\t${}\n}", {
        label: "for",
        detail: "of loop",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("do {\n\t${}\n} while (${})", {
        label: "do",
        detail: "loop",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("while (${}) {\n\t${}\n}", {
        label: "while",
        detail: "loop",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("try {\n\t${}\n} catch (${error}) {\n\t${}\n}", {
        label: "try",
        detail: "/ catch block",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("if (${}) {\n\t${}\n}", {
        label: "if",
        detail: "block",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("if (${}) {\n\t${}\n} else {\n\t${}\n}", {
        label: "if",
        detail: "/ else block",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("class ${name} {\n\tconstructor(${params}) {\n\t\t${}\n\t}\n}", {
        label: "class",
        detail: "definition",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("import {${names}} from \"${module}\"\n${}", {
        label: "import",
        detail: "named",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("import ${name} from \"${module}\"\n${}", {
        label: "import",
        detail: "default",
        type: "keyword"
    })
];
/**
A collection of snippet completions for TypeScript. Includes the
JavaScript [snippets](https://codemirror.net/6/docs/ref/#lang-javascript.snippets).
*/ const typescriptSnippets = /*@__PURE__*/ snippets.concat([
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("interface ${name} {\n\t${}\n}", {
        label: "interface",
        detail: "definition",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("type ${name} = ${type}", {
        label: "type",
        detail: "definition",
        type: "keyword"
    }),
    /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["snippetCompletion"])("enum ${name} {\n\t${}\n}", {
        label: "enum",
        detail: "definition",
        type: "keyword"
    })
]);
const cache = /*@__PURE__*/ new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeWeakMap"]();
const ScopeNodes = /*@__PURE__*/ new Set([
    "Script",
    "Block",
    "FunctionExpression",
    "FunctionDeclaration",
    "ArrowFunction",
    "MethodDeclaration",
    "ForStatement"
]);
function defID(type) {
    return (node, def)=>{
        let id = node.node.getChild("VariableDefinition");
        if (id) def(id, type);
        return true;
    };
}
const functionContext = [
    "FunctionDeclaration"
];
const gatherCompletions = {
    FunctionDeclaration: /*@__PURE__*/ defID("function"),
    ClassDeclaration: /*@__PURE__*/ defID("class"),
    ClassExpression: ()=>true,
    EnumDeclaration: /*@__PURE__*/ defID("constant"),
    TypeAliasDeclaration: /*@__PURE__*/ defID("type"),
    NamespaceDeclaration: /*@__PURE__*/ defID("namespace"),
    VariableDefinition (node, def) {
        if (!node.matchContext(functionContext)) def(node, "variable");
    },
    TypeDefinition (node, def) {
        def(node, "type");
    },
    __proto__: null
};
function getScope(doc, node) {
    let cached = cache.get(node);
    if (cached) return cached;
    let completions = [], top = true;
    function def(node, type) {
        let name = doc.sliceString(node.from, node.to);
        completions.push({
            label: name,
            type
        });
    }
    node.cursor(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$common$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IterMode"].IncludeAnonymous).iterate((node)=>{
        if (top) {
            top = false;
        } else if (node.name) {
            let gather = gatherCompletions[node.name];
            if (gather && gather(node, def) || ScopeNodes.has(node.name)) return false;
        } else if (node.to - node.from > 8192) {
            // Allow caching for bigger internal nodes
            for (let c of getScope(doc, node.node))completions.push(c);
            return false;
        }
    });
    cache.set(node, completions);
    return completions;
}
const Identifier = /^[\w$\xa1-\uffff][\w$\d\xa1-\uffff]*$/;
const dontComplete = [
    "TemplateString",
    "String",
    "RegExp",
    "LineComment",
    "BlockComment",
    "VariableDefinition",
    "TypeDefinition",
    "Label",
    "PropertyDefinition",
    "PropertyName",
    "PrivatePropertyDefinition",
    "PrivatePropertyName",
    "JSXText",
    "JSXAttributeValue",
    "JSXOpenTag",
    "JSXCloseTag",
    "JSXSelfClosingTag",
    ".",
    "?."
];
/**
Completion source that looks up locally defined names in
JavaScript code.
*/ function localCompletionSource(context) {
    let inner = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["syntaxTree"])(context.state).resolveInner(context.pos, -1);
    if (dontComplete.indexOf(inner.name) > -1) return null;
    let isWord = inner.name == "VariableName" || inner.to - inner.from < 20 && Identifier.test(context.state.sliceDoc(inner.from, inner.to));
    if (!isWord && !context.explicit) return null;
    let options = [];
    for(let pos = inner; pos; pos = pos.parent){
        if (ScopeNodes.has(pos.name)) options = options.concat(getScope(context.state.doc, pos));
    }
    return {
        options,
        from: isWord ? inner.from : context.pos,
        validFor: Identifier
    };
}
function pathFor(read, member, name) {
    var _a;
    let path = [];
    for(;;){
        let obj = member.firstChild, prop;
        if ((obj === null || obj === void 0 ? void 0 : obj.name) == "VariableName") {
            path.push(read(obj));
            return {
                path: path.reverse(),
                name
            };
        } else if ((obj === null || obj === void 0 ? void 0 : obj.name) == "MemberExpression" && ((_a = prop = obj.lastChild) === null || _a === void 0 ? void 0 : _a.name) == "PropertyName") {
            path.push(read(prop));
            member = obj;
        } else {
            return null;
        }
    }
}
/**
Helper function for defining JavaScript completion sources. It
returns the completable name and object path for a completion
context, or null if no name/property completion should happen at
that position. For example, when completing after `a.b.c` it will
return `{path: ["a", "b"], name: "c"}`. When completing after `x`
it will return `{path: [], name: "x"}`. When not in a property or
name, it will return null if `context.explicit` is false, and
`{path: [], name: ""}` otherwise.
*/ function completionPath(context) {
    let read = (node)=>context.state.doc.sliceString(node.from, node.to);
    let inner = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["syntaxTree"])(context.state).resolveInner(context.pos, -1);
    if (inner.name == "PropertyName") {
        return pathFor(read, inner.parent, read(inner));
    } else if ((inner.name == "." || inner.name == "?.") && inner.parent.name == "MemberExpression") {
        return pathFor(read, inner.parent, "");
    } else if (dontComplete.indexOf(inner.name) > -1) {
        return null;
    } else if (inner.name == "VariableName" || inner.to - inner.from < 20 && Identifier.test(read(inner))) {
        return {
            path: [],
            name: read(inner)
        };
    } else if (inner.name == "MemberExpression") {
        return pathFor(read, inner, "");
    } else {
        return context.explicit ? {
            path: [],
            name: ""
        } : null;
    }
}
function enumeratePropertyCompletions(obj, top) {
    let options = [], seen = new Set;
    for(let depth = 0;; depth++){
        for (let name of (Object.getOwnPropertyNames || Object.keys)(obj)){
            if (!/^[a-zA-Z_$\xaa-\uffdc][\w$\xaa-\uffdc]*$/.test(name) || seen.has(name)) continue;
            seen.add(name);
            let value;
            try {
                value = obj[name];
            } catch (_) {
                continue;
            }
            options.push({
                label: name,
                type: typeof value == "function" ? /^[A-Z]/.test(name) ? "class" : top ? "function" : "method" : top ? "variable" : "property",
                boost: -depth
            });
        }
        let next = Object.getPrototypeOf(obj);
        if (!next) return options;
        obj = next;
    }
}
/**
Defines a [completion source](https://codemirror.net/6/docs/ref/#autocomplete.CompletionSource) that
completes from the given scope object (for example `globalThis`).
Will enter properties of the object when completing properties on
a directly-named path.
*/ function scopeCompletionSource(scope) {
    let cache = new Map;
    return (context)=>{
        let path = completionPath(context);
        if (!path) return null;
        let target = scope;
        for (let step of path.path){
            target = target[step];
            if (!target) return null;
        }
        let options = cache.get(target);
        if (!options) cache.set(target, options = enumeratePropertyCompletions(target, !path.path.length));
        return {
            from: context.pos - path.name.length,
            options,
            validFor: Identifier
        };
    };
}
/**
A language provider based on the [Lezer JavaScript
parser](https://github.com/lezer-parser/javascript), extended with
highlighting and indentation information.
*/ const javascriptLanguage = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LRLanguage"].define({
    name: "javascript",
    parser: /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$javascript$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parser"].configure({
        props: [
            /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["indentNodeProp"].add({
                IfStatement: /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["continuedIndent"])({
                    except: /^\s*({|else\b)/
                }),
                TryStatement: /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["continuedIndent"])({
                    except: /^\s*({|catch\b|finally\b)/
                }),
                LabeledStatement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["flatIndent"],
                SwitchBody: (context)=>{
                    let after = context.textAfter, closed = /^\s*\}/.test(after), isCase = /^\s*(case|default)\b/.test(after);
                    return context.baseIndent + (closed ? 0 : isCase ? 1 : 2) * context.unit;
                },
                Block: /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delimitedIndent"])({
                    closing: "}"
                }),
                ArrowFunction: (cx)=>cx.baseIndent + cx.unit,
                "TemplateString BlockComment": ()=>null,
                "Statement Property": /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["continuedIndent"])({
                    except: /^\s*{/
                }),
                JSXElement (context) {
                    let closed = /^\s*<\//.test(context.textAfter);
                    return context.lineIndent(context.node.from) + (closed ? 0 : context.unit);
                },
                JSXEscape (context) {
                    let closed = /\s*\}/.test(context.textAfter);
                    return context.lineIndent(context.node.from) + (closed ? 0 : context.unit);
                },
                "JSXOpenTag JSXSelfClosingTag" (context) {
                    return context.column(context.node.from) + context.unit;
                }
            }),
            /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["foldNodeProp"].add({
                "Block ClassBody SwitchBody EnumBody ObjectExpression ArrayExpression ObjectType": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["foldInside"],
                BlockComment (tree) {
                    return {
                        from: tree.from + 2,
                        to: tree.to - 2
                    };
                }
            })
        ]
    }),
    languageData: {
        closeBrackets: {
            brackets: [
                "(",
                "[",
                "{",
                "'",
                '"',
                "`"
            ]
        },
        commentTokens: {
            line: "//",
            block: {
                open: "/*",
                close: "*/"
            }
        },
        indentOnInput: /^\s*(?:case |default:|\{|\}|<\/)$/,
        wordChars: "$"
    }
});
const jsxSublanguage = {
    test: (node)=>/^JSX/.test(node.name),
    facet: /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defineLanguageFacet"])({
        commentTokens: {
            block: {
                open: "{/*",
                close: "*/}"
            }
        }
    })
};
/**
A language provider for TypeScript.
*/ const typescriptLanguage = /*@__PURE__*/ javascriptLanguage.configure({
    dialect: "ts"
}, "typescript");
/**
Language provider for JSX.
*/ const jsxLanguage = /*@__PURE__*/ javascriptLanguage.configure({
    dialect: "jsx",
    props: [
        /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sublanguageProp"].add((n)=>n.isTop ? [
                jsxSublanguage
            ] : undefined)
    ]
});
/**
Language provider for JSX + TypeScript.
*/ const tsxLanguage = /*@__PURE__*/ javascriptLanguage.configure({
    dialect: "jsx ts",
    props: [
        /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sublanguageProp"].add((n)=>n.isTop ? [
                jsxSublanguage
            ] : undefined)
    ]
}, "typescript");
let kwCompletion = (name)=>({
        label: name,
        type: "keyword"
    });
const keywords = /*@__PURE__*/ "break case const continue default delete export extends false finally in instanceof let new return static super switch this throw true typeof var yield".split(" ").map(kwCompletion);
const typescriptKeywords = /*@__PURE__*/ keywords.concat(/*@__PURE__*/ [
    "declare",
    "implements",
    "private",
    "protected",
    "public"
].map(kwCompletion));
/**
JavaScript support. Includes [snippet](https://codemirror.net/6/docs/ref/#lang-javascript.snippets)
and local variable completion.
*/ function javascript(config = {}) {
    let lang = config.jsx ? config.typescript ? tsxLanguage : jsxLanguage : config.typescript ? typescriptLanguage : javascriptLanguage;
    let completions = config.typescript ? typescriptSnippets.concat(typescriptKeywords) : snippets.concat(keywords);
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LanguageSupport"](lang, [
        javascriptLanguage.data.of({
            autocomplete: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ifNotIn"])(dontComplete, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$autocomplete$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["completeFromList"])(completions))
        }),
        javascriptLanguage.data.of({
            autocomplete: localCompletionSource
        }),
        config.jsx ? autoCloseTags : []
    ]);
}
function findOpenTag(node) {
    for(;;){
        if (node.name == "JSXOpenTag" || node.name == "JSXSelfClosingTag" || node.name == "JSXFragmentTag") return node;
        if (node.name == "JSXEscape" || !node.parent) return null;
        node = node.parent;
    }
}
function elementName(doc, tree, max = doc.length) {
    for(let ch = tree === null || tree === void 0 ? void 0 : tree.firstChild; ch; ch = ch.nextSibling){
        if (ch.name == "JSXIdentifier" || ch.name == "JSXBuiltin" || ch.name == "JSXNamespacedName" || ch.name == "JSXMemberExpression") return doc.sliceString(ch.from, Math.min(ch.to, max));
    }
    return "";
}
const android = typeof navigator == "object" && /*@__PURE__*/ /Android\b/.test(navigator.userAgent);
/**
Extension that will automatically insert JSX close tags when a `>` or
`/` is typed.
*/ const autoCloseTags = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EditorView"].inputHandler.of((view, from, to, text, defaultInsert)=>{
    if ((android ? view.composing : view.compositionStarted) || view.state.readOnly || from != to || text != ">" && text != "/" || !javascriptLanguage.isActiveAt(view.state, from, -1)) return false;
    let base = defaultInsert(), { state } = base;
    let closeTags = state.changeByRange((range)=>{
        var _a;
        let { head } = range, around = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["syntaxTree"])(state).resolveInner(head - 1, -1), name;
        if (around.name == "JSXStartTag") around = around.parent;
        if (state.doc.sliceString(head - 1, head) != text || around.name == "JSXAttributeValue" && around.to > head) ;
        else if (text == ">" && around.name == "JSXFragmentTag") {
            return {
                range,
                changes: {
                    from: head,
                    insert: `</>`
                }
            };
        } else if (text == "/" && around.name == "JSXStartCloseTag") {
            let empty = around.parent, base = empty.parent;
            if (base && empty.from == head - 2 && ((name = elementName(state.doc, base.firstChild, head)) || ((_a = base.firstChild) === null || _a === void 0 ? void 0 : _a.name) == "JSXFragmentTag")) {
                let insert = `${name}>`;
                return {
                    range: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EditorSelection"].cursor(head + insert.length, -1),
                    changes: {
                        from: head,
                        insert
                    }
                };
            }
        } else if (text == ">") {
            let openTag = findOpenTag(around);
            if (openTag && openTag.name == "JSXOpenTag" && !/^\/?>|^<\//.test(state.doc.sliceString(head, head + 2)) && (name = elementName(state.doc, openTag, head))) return {
                range,
                changes: {
                    from: head,
                    insert: `</${name}>`
                }
            };
        }
        return {
            range
        };
    });
    if (closeTags.changes.empty) return false;
    view.dispatch([
        base,
        state.update(closeTags, {
            userEvent: "input.complete",
            scrollIntoView: true
        })
    ]);
    return true;
});
/**
Connects an [ESLint](https://eslint.org/) linter to CodeMirror's
[lint](https://codemirror.net/6/docs/ref/#lint) integration. `eslint` should be an instance of the
[`Linter`](https://eslint.org/docs/developer-guide/nodejs-api#linter)
class, and `config` an optional ESLint configuration. The return
value of this function can be passed to [`linter`](https://codemirror.net/6/docs/ref/#lint.linter)
to create a JavaScript linting extension.

Note that ESLint targets node, and is tricky to run in the
browser. The
[eslint-linter-browserify](https://github.com/UziTech/eslint-linter-browserify)
package may help with that (see
[example](https://github.com/UziTech/eslint-linter-browserify/blob/master/example/script.js)).
*/ function esLint(eslint, config) {
    if (!config) {
        config = {
            parserOptions: {
                ecmaVersion: 2019,
                sourceType: "module"
            },
            env: {
                browser: true,
                node: true,
                es6: true,
                es2015: true,
                es2017: true,
                es2020: true
            },
            rules: {}
        };
        eslint.getRules().forEach((desc, name)=>{
            var _a;
            if ((_a = desc.meta.docs) === null || _a === void 0 ? void 0 : _a.recommended) config.rules[name] = 2;
        });
    }
    return (view)=>{
        let { state } = view, found = [];
        for (let { from, to } of javascriptLanguage.findRegions(state)){
            let fromLine = state.doc.lineAt(from), offset = {
                line: fromLine.number - 1,
                col: from - fromLine.from,
                pos: from
            };
            for (let d of eslint.verify(state.sliceDoc(from, to), config))found.push(translateDiagnostic(d, state.doc, offset));
        }
        return found;
    };
}
function mapPos(line, col, doc, offset) {
    return doc.line(line + offset.line).from + col + (line == 1 ? offset.col - 1 : -1);
}
function translateDiagnostic(input, doc, offset) {
    let start = mapPos(input.line, input.column, doc, offset);
    let result = {
        from: start,
        to: input.endLine != null && input.endColumn != 1 ? mapPos(input.endLine, input.endColumn, doc, offset) : start,
        message: input.message,
        source: input.ruleId ? "eslint:" + input.ruleId : "eslint",
        severity: input.severity == 1 ? "warning" : "error"
    };
    if (input.fix) {
        let { range, text } = input.fix, from = range[0] + offset.pos - start, to = range[1] + offset.pos - start;
        result.actions = [
            {
                name: "fix",
                apply (view, start) {
                    view.dispatch({
                        changes: {
                            from: start + from,
                            to: start + to,
                            insert: text
                        },
                        scrollIntoView: true
                    });
                }
            }
        ];
    }
    return result;
}
;
}}),
"[project]/node_modules/@codemirror/lang-html/dist/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "autoCloseTags": (()=>autoCloseTags),
    "html": (()=>html),
    "htmlCompletionSource": (()=>htmlCompletionSource),
    "htmlCompletionSourceWith": (()=>htmlCompletionSourceWith),
    "htmlLanguage": (()=>htmlLanguage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$html$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lezer/html/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$css$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codemirror/lang-css/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$javascript$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codemirror/lang-javascript/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codemirror/view/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codemirror/state/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@codemirror/language/dist/index.js [app-client] (ecmascript)");
;
;
;
;
;
;
const Targets = [
    "_blank",
    "_self",
    "_top",
    "_parent"
];
const Charsets = [
    "ascii",
    "utf-8",
    "utf-16",
    "latin1",
    "latin1"
];
const Methods = [
    "get",
    "post",
    "put",
    "delete"
];
const Encs = [
    "application/x-www-form-urlencoded",
    "multipart/form-data",
    "text/plain"
];
const Bool = [
    "true",
    "false"
];
const S = {}; // Empty tag spec
const Tags = {
    a: {
        attrs: {
            href: null,
            ping: null,
            type: null,
            media: null,
            target: Targets,
            hreflang: null
        }
    },
    abbr: S,
    address: S,
    area: {
        attrs: {
            alt: null,
            coords: null,
            href: null,
            target: null,
            ping: null,
            media: null,
            hreflang: null,
            type: null,
            shape: [
                "default",
                "rect",
                "circle",
                "poly"
            ]
        }
    },
    article: S,
    aside: S,
    audio: {
        attrs: {
            src: null,
            mediagroup: null,
            crossorigin: [
                "anonymous",
                "use-credentials"
            ],
            preload: [
                "none",
                "metadata",
                "auto"
            ],
            autoplay: [
                "autoplay"
            ],
            loop: [
                "loop"
            ],
            controls: [
                "controls"
            ]
        }
    },
    b: S,
    base: {
        attrs: {
            href: null,
            target: Targets
        }
    },
    bdi: S,
    bdo: S,
    blockquote: {
        attrs: {
            cite: null
        }
    },
    body: S,
    br: S,
    button: {
        attrs: {
            form: null,
            formaction: null,
            name: null,
            value: null,
            autofocus: [
                "autofocus"
            ],
            disabled: [
                "autofocus"
            ],
            formenctype: Encs,
            formmethod: Methods,
            formnovalidate: [
                "novalidate"
            ],
            formtarget: Targets,
            type: [
                "submit",
                "reset",
                "button"
            ]
        }
    },
    canvas: {
        attrs: {
            width: null,
            height: null
        }
    },
    caption: S,
    center: S,
    cite: S,
    code: S,
    col: {
        attrs: {
            span: null
        }
    },
    colgroup: {
        attrs: {
            span: null
        }
    },
    command: {
        attrs: {
            type: [
                "command",
                "checkbox",
                "radio"
            ],
            label: null,
            icon: null,
            radiogroup: null,
            command: null,
            title: null,
            disabled: [
                "disabled"
            ],
            checked: [
                "checked"
            ]
        }
    },
    data: {
        attrs: {
            value: null
        }
    },
    datagrid: {
        attrs: {
            disabled: [
                "disabled"
            ],
            multiple: [
                "multiple"
            ]
        }
    },
    datalist: {
        attrs: {
            data: null
        }
    },
    dd: S,
    del: {
        attrs: {
            cite: null,
            datetime: null
        }
    },
    details: {
        attrs: {
            open: [
                "open"
            ]
        }
    },
    dfn: S,
    div: S,
    dl: S,
    dt: S,
    em: S,
    embed: {
        attrs: {
            src: null,
            type: null,
            width: null,
            height: null
        }
    },
    eventsource: {
        attrs: {
            src: null
        }
    },
    fieldset: {
        attrs: {
            disabled: [
                "disabled"
            ],
            form: null,
            name: null
        }
    },
    figcaption: S,
    figure: S,
    footer: S,
    form: {
        attrs: {
            action: null,
            name: null,
            "accept-charset": Charsets,
            autocomplete: [
                "on",
                "off"
            ],
            enctype: Encs,
            method: Methods,
            novalidate: [
                "novalidate"
            ],
            target: Targets
        }
    },
    h1: S,
    h2: S,
    h3: S,
    h4: S,
    h5: S,
    h6: S,
    head: {
        children: [
            "title",
            "base",
            "link",
            "style",
            "meta",
            "script",
            "noscript",
            "command"
        ]
    },
    header: S,
    hgroup: S,
    hr: S,
    html: {
        attrs: {
            manifest: null
        }
    },
    i: S,
    iframe: {
        attrs: {
            src: null,
            srcdoc: null,
            name: null,
            width: null,
            height: null,
            sandbox: [
                "allow-top-navigation",
                "allow-same-origin",
                "allow-forms",
                "allow-scripts"
            ],
            seamless: [
                "seamless"
            ]
        }
    },
    img: {
        attrs: {
            alt: null,
            src: null,
            ismap: null,
            usemap: null,
            width: null,
            height: null,
            crossorigin: [
                "anonymous",
                "use-credentials"
            ]
        }
    },
    input: {
        attrs: {
            alt: null,
            dirname: null,
            form: null,
            formaction: null,
            height: null,
            list: null,
            max: null,
            maxlength: null,
            min: null,
            name: null,
            pattern: null,
            placeholder: null,
            size: null,
            src: null,
            step: null,
            value: null,
            width: null,
            accept: [
                "audio/*",
                "video/*",
                "image/*"
            ],
            autocomplete: [
                "on",
                "off"
            ],
            autofocus: [
                "autofocus"
            ],
            checked: [
                "checked"
            ],
            disabled: [
                "disabled"
            ],
            formenctype: Encs,
            formmethod: Methods,
            formnovalidate: [
                "novalidate"
            ],
            formtarget: Targets,
            multiple: [
                "multiple"
            ],
            readonly: [
                "readonly"
            ],
            required: [
                "required"
            ],
            type: [
                "hidden",
                "text",
                "search",
                "tel",
                "url",
                "email",
                "password",
                "datetime",
                "date",
                "month",
                "week",
                "time",
                "datetime-local",
                "number",
                "range",
                "color",
                "checkbox",
                "radio",
                "file",
                "submit",
                "image",
                "reset",
                "button"
            ]
        }
    },
    ins: {
        attrs: {
            cite: null,
            datetime: null
        }
    },
    kbd: S,
    keygen: {
        attrs: {
            challenge: null,
            form: null,
            name: null,
            autofocus: [
                "autofocus"
            ],
            disabled: [
                "disabled"
            ],
            keytype: [
                "RSA"
            ]
        }
    },
    label: {
        attrs: {
            for: null,
            form: null
        }
    },
    legend: S,
    li: {
        attrs: {
            value: null
        }
    },
    link: {
        attrs: {
            href: null,
            type: null,
            hreflang: null,
            media: null,
            sizes: [
                "all",
                "16x16",
                "16x16 32x32",
                "16x16 32x32 64x64"
            ]
        }
    },
    map: {
        attrs: {
            name: null
        }
    },
    mark: S,
    menu: {
        attrs: {
            label: null,
            type: [
                "list",
                "context",
                "toolbar"
            ]
        }
    },
    meta: {
        attrs: {
            content: null,
            charset: Charsets,
            name: [
                "viewport",
                "application-name",
                "author",
                "description",
                "generator",
                "keywords"
            ],
            "http-equiv": [
                "content-language",
                "content-type",
                "default-style",
                "refresh"
            ]
        }
    },
    meter: {
        attrs: {
            value: null,
            min: null,
            low: null,
            high: null,
            max: null,
            optimum: null
        }
    },
    nav: S,
    noscript: S,
    object: {
        attrs: {
            data: null,
            type: null,
            name: null,
            usemap: null,
            form: null,
            width: null,
            height: null,
            typemustmatch: [
                "typemustmatch"
            ]
        }
    },
    ol: {
        attrs: {
            reversed: [
                "reversed"
            ],
            start: null,
            type: [
                "1",
                "a",
                "A",
                "i",
                "I"
            ]
        },
        children: [
            "li",
            "script",
            "template",
            "ul",
            "ol"
        ]
    },
    optgroup: {
        attrs: {
            disabled: [
                "disabled"
            ],
            label: null
        }
    },
    option: {
        attrs: {
            disabled: [
                "disabled"
            ],
            label: null,
            selected: [
                "selected"
            ],
            value: null
        }
    },
    output: {
        attrs: {
            for: null,
            form: null,
            name: null
        }
    },
    p: S,
    param: {
        attrs: {
            name: null,
            value: null
        }
    },
    pre: S,
    progress: {
        attrs: {
            value: null,
            max: null
        }
    },
    q: {
        attrs: {
            cite: null
        }
    },
    rp: S,
    rt: S,
    ruby: S,
    samp: S,
    script: {
        attrs: {
            type: [
                "text/javascript"
            ],
            src: null,
            async: [
                "async"
            ],
            defer: [
                "defer"
            ],
            charset: Charsets
        }
    },
    section: S,
    select: {
        attrs: {
            form: null,
            name: null,
            size: null,
            autofocus: [
                "autofocus"
            ],
            disabled: [
                "disabled"
            ],
            multiple: [
                "multiple"
            ]
        }
    },
    slot: {
        attrs: {
            name: null
        }
    },
    small: S,
    source: {
        attrs: {
            src: null,
            type: null,
            media: null
        }
    },
    span: S,
    strong: S,
    style: {
        attrs: {
            type: [
                "text/css"
            ],
            media: null,
            scoped: null
        }
    },
    sub: S,
    summary: S,
    sup: S,
    table: S,
    tbody: S,
    td: {
        attrs: {
            colspan: null,
            rowspan: null,
            headers: null
        }
    },
    template: S,
    textarea: {
        attrs: {
            dirname: null,
            form: null,
            maxlength: null,
            name: null,
            placeholder: null,
            rows: null,
            cols: null,
            autofocus: [
                "autofocus"
            ],
            disabled: [
                "disabled"
            ],
            readonly: [
                "readonly"
            ],
            required: [
                "required"
            ],
            wrap: [
                "soft",
                "hard"
            ]
        }
    },
    tfoot: S,
    th: {
        attrs: {
            colspan: null,
            rowspan: null,
            headers: null,
            scope: [
                "row",
                "col",
                "rowgroup",
                "colgroup"
            ]
        }
    },
    thead: S,
    time: {
        attrs: {
            datetime: null
        }
    },
    title: S,
    tr: S,
    track: {
        attrs: {
            src: null,
            label: null,
            default: null,
            kind: [
                "subtitles",
                "captions",
                "descriptions",
                "chapters",
                "metadata"
            ],
            srclang: null
        }
    },
    ul: {
        children: [
            "li",
            "script",
            "template",
            "ul",
            "ol"
        ]
    },
    var: S,
    video: {
        attrs: {
            src: null,
            poster: null,
            width: null,
            height: null,
            crossorigin: [
                "anonymous",
                "use-credentials"
            ],
            preload: [
                "auto",
                "metadata",
                "none"
            ],
            autoplay: [
                "autoplay"
            ],
            mediagroup: [
                "movie"
            ],
            muted: [
                "muted"
            ],
            controls: [
                "controls"
            ]
        }
    },
    wbr: S
};
const GlobalAttrs = {
    accesskey: null,
    class: null,
    contenteditable: Bool,
    contextmenu: null,
    dir: [
        "ltr",
        "rtl",
        "auto"
    ],
    draggable: [
        "true",
        "false",
        "auto"
    ],
    dropzone: [
        "copy",
        "move",
        "link",
        "string:",
        "file:"
    ],
    hidden: [
        "hidden"
    ],
    id: null,
    inert: [
        "inert"
    ],
    itemid: null,
    itemprop: null,
    itemref: null,
    itemscope: [
        "itemscope"
    ],
    itemtype: null,
    lang: [
        "ar",
        "bn",
        "de",
        "en-GB",
        "en-US",
        "es",
        "fr",
        "hi",
        "id",
        "ja",
        "pa",
        "pt",
        "ru",
        "tr",
        "zh"
    ],
    spellcheck: Bool,
    autocorrect: Bool,
    autocapitalize: Bool,
    style: null,
    tabindex: null,
    title: null,
    translate: [
        "yes",
        "no"
    ],
    rel: [
        "stylesheet",
        "alternate",
        "author",
        "bookmark",
        "help",
        "license",
        "next",
        "nofollow",
        "noreferrer",
        "prefetch",
        "prev",
        "search",
        "tag"
    ],
    role: /*@__PURE__*/ "alert application article banner button cell checkbox complementary contentinfo dialog document feed figure form grid gridcell heading img list listbox listitem main navigation region row rowgroup search switch tab table tabpanel textbox timer".split(" "),
    "aria-activedescendant": null,
    "aria-atomic": Bool,
    "aria-autocomplete": [
        "inline",
        "list",
        "both",
        "none"
    ],
    "aria-busy": Bool,
    "aria-checked": [
        "true",
        "false",
        "mixed",
        "undefined"
    ],
    "aria-controls": null,
    "aria-describedby": null,
    "aria-disabled": Bool,
    "aria-dropeffect": null,
    "aria-expanded": [
        "true",
        "false",
        "undefined"
    ],
    "aria-flowto": null,
    "aria-grabbed": [
        "true",
        "false",
        "undefined"
    ],
    "aria-haspopup": Bool,
    "aria-hidden": Bool,
    "aria-invalid": [
        "true",
        "false",
        "grammar",
        "spelling"
    ],
    "aria-label": null,
    "aria-labelledby": null,
    "aria-level": null,
    "aria-live": [
        "off",
        "polite",
        "assertive"
    ],
    "aria-multiline": Bool,
    "aria-multiselectable": Bool,
    "aria-owns": null,
    "aria-posinset": null,
    "aria-pressed": [
        "true",
        "false",
        "mixed",
        "undefined"
    ],
    "aria-readonly": Bool,
    "aria-relevant": null,
    "aria-required": Bool,
    "aria-selected": [
        "true",
        "false",
        "undefined"
    ],
    "aria-setsize": null,
    "aria-sort": [
        "ascending",
        "descending",
        "none",
        "other"
    ],
    "aria-valuemax": null,
    "aria-valuemin": null,
    "aria-valuenow": null,
    "aria-valuetext": null
};
const eventAttributes = /*@__PURE__*/ ("beforeunload copy cut dragstart dragover dragleave dragenter dragend " + "drag paste focus blur change click load mousedown mouseenter mouseleave " + "mouseup keydown keyup resize scroll unload").split(" ").map((n)=>"on" + n);
for (let a of eventAttributes)GlobalAttrs[a] = null;
class Schema {
    constructor(extraTags, extraAttrs){
        this.tags = {
            ...Tags,
            ...extraTags
        };
        this.globalAttrs = {
            ...GlobalAttrs,
            ...extraAttrs
        };
        this.allTags = Object.keys(this.tags);
        this.globalAttrNames = Object.keys(this.globalAttrs);
    }
}
Schema.default = /*@__PURE__*/ new Schema;
function elementName(doc, tree, max = doc.length) {
    if (!tree) return "";
    let tag = tree.firstChild;
    let name = tag && tag.getChild("TagName");
    return name ? doc.sliceString(name.from, Math.min(name.to, max)) : "";
}
function findParentElement(tree, skip = false) {
    for(; tree; tree = tree.parent)if (tree.name == "Element") {
        if (skip) skip = false;
        else return tree;
    }
    return null;
}
function allowedChildren(doc, tree, schema) {
    let parentInfo = schema.tags[elementName(doc, findParentElement(tree))];
    return (parentInfo === null || parentInfo === void 0 ? void 0 : parentInfo.children) || schema.allTags;
}
function openTags(doc, tree) {
    let open = [];
    for(let parent = findParentElement(tree); parent && !parent.type.isTop; parent = findParentElement(parent.parent)){
        let tagName = elementName(doc, parent);
        if (tagName && parent.lastChild.name == "CloseTag") break;
        if (tagName && open.indexOf(tagName) < 0 && (tree.name == "EndTag" || tree.from >= parent.firstChild.to)) open.push(tagName);
    }
    return open;
}
const identifier = /^[:\-\.\w\u00b7-\uffff]*$/;
function completeTag(state, schema, tree, from, to) {
    let end = /\s*>/.test(state.sliceDoc(to, to + 5)) ? "" : ">";
    let parent = findParentElement(tree, true);
    return {
        from,
        to,
        options: allowedChildren(state.doc, parent, schema).map((tagName)=>({
                label: tagName,
                type: "type"
            })).concat(openTags(state.doc, tree).map((tag, i)=>({
                label: "/" + tag,
                apply: "/" + tag + end,
                type: "type",
                boost: 99 - i
            }))),
        validFor: /^\/?[:\-\.\w\u00b7-\uffff]*$/
    };
}
function completeCloseTag(state, tree, from, to) {
    let end = /\s*>/.test(state.sliceDoc(to, to + 5)) ? "" : ">";
    return {
        from,
        to,
        options: openTags(state.doc, tree).map((tag, i)=>({
                label: tag,
                apply: tag + end,
                type: "type",
                boost: 99 - i
            })),
        validFor: identifier
    };
}
function completeStartTag(state, schema, tree, pos) {
    let options = [], level = 0;
    for (let tagName of allowedChildren(state.doc, tree, schema))options.push({
        label: "<" + tagName,
        type: "type"
    });
    for (let open of openTags(state.doc, tree))options.push({
        label: "</" + open + ">",
        type: "type",
        boost: 99 - level++
    });
    return {
        from: pos,
        to: pos,
        options,
        validFor: /^<\/?[:\-\.\w\u00b7-\uffff]*$/
    };
}
function completeAttrName(state, schema, tree, from, to) {
    let elt = findParentElement(tree), info = elt ? schema.tags[elementName(state.doc, elt)] : null;
    let localAttrs = info && info.attrs ? Object.keys(info.attrs) : [];
    let names = info && info.globalAttrs === false ? localAttrs : localAttrs.length ? localAttrs.concat(schema.globalAttrNames) : schema.globalAttrNames;
    return {
        from,
        to,
        options: names.map((attrName)=>({
                label: attrName,
                type: "property"
            })),
        validFor: identifier
    };
}
function completeAttrValue(state, schema, tree, from, to) {
    var _a;
    let nameNode = (_a = tree.parent) === null || _a === void 0 ? void 0 : _a.getChild("AttributeName");
    let options = [], token = undefined;
    if (nameNode) {
        let attrName = state.sliceDoc(nameNode.from, nameNode.to);
        let attrs = schema.globalAttrs[attrName];
        if (!attrs) {
            let elt = findParentElement(tree), info = elt ? schema.tags[elementName(state.doc, elt)] : null;
            attrs = (info === null || info === void 0 ? void 0 : info.attrs) && info.attrs[attrName];
        }
        if (attrs) {
            let base = state.sliceDoc(from, to).toLowerCase(), quoteStart = '"', quoteEnd = '"';
            if (/^['"]/.test(base)) {
                token = base[0] == '"' ? /^[^"]*$/ : /^[^']*$/;
                quoteStart = "";
                quoteEnd = state.sliceDoc(to, to + 1) == base[0] ? "" : base[0];
                base = base.slice(1);
                from++;
            } else {
                token = /^[^\s<>='"]*$/;
            }
            for (let value of attrs)options.push({
                label: value,
                apply: quoteStart + value + quoteEnd,
                type: "constant"
            });
        }
    }
    return {
        from,
        to,
        options,
        validFor: token
    };
}
function htmlCompletionFor(schema, context) {
    let { state, pos } = context, tree = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["syntaxTree"])(state).resolveInner(pos, -1), around = tree.resolve(pos);
    for(let scan = pos, before; around == tree && (before = tree.childBefore(scan));){
        let last = before.lastChild;
        if (!last || !last.type.isError || last.from < last.to) break;
        around = tree = before;
        scan = last.from;
    }
    if (tree.name == "TagName") {
        return tree.parent && /CloseTag$/.test(tree.parent.name) ? completeCloseTag(state, tree, tree.from, pos) : completeTag(state, schema, tree, tree.from, pos);
    } else if (tree.name == "StartTag") {
        return completeTag(state, schema, tree, pos, pos);
    } else if (tree.name == "StartCloseTag" || tree.name == "IncompleteCloseTag") {
        return completeCloseTag(state, tree, pos, pos);
    } else if (tree.name == "OpenTag" || tree.name == "SelfClosingTag" || tree.name == "AttributeName") {
        return completeAttrName(state, schema, tree, tree.name == "AttributeName" ? tree.from : pos, pos);
    } else if (tree.name == "Is" || tree.name == "AttributeValue" || tree.name == "UnquotedAttributeValue") {
        return completeAttrValue(state, schema, tree, tree.name == "Is" ? pos : tree.from, pos);
    } else if (context.explicit && (around.name == "Element" || around.name == "Text" || around.name == "Document")) {
        return completeStartTag(state, schema, tree, pos);
    } else {
        return null;
    }
}
/**
HTML tag completion. Opens and closes tags and attributes in a
context-aware way.
*/ function htmlCompletionSource(context) {
    return htmlCompletionFor(Schema.default, context);
}
/**
Create a completion source for HTML extended with additional tags
or attributes.
*/ function htmlCompletionSourceWith(config) {
    let { extraTags, extraGlobalAttributes: extraAttrs } = config;
    let schema = extraAttrs || extraTags ? new Schema(extraTags, extraAttrs) : Schema.default;
    return (context)=>htmlCompletionFor(schema, context);
}
const jsonParser = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$javascript$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["javascriptLanguage"].parser.configure({
    top: "SingleExpression"
});
const defaultNesting = [
    {
        tag: "script",
        attrs: (attrs)=>attrs.type == "text/typescript" || attrs.lang == "ts",
        parser: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$javascript$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["typescriptLanguage"].parser
    },
    {
        tag: "script",
        attrs: (attrs)=>attrs.type == "text/babel" || attrs.type == "text/jsx",
        parser: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$javascript$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxLanguage"].parser
    },
    {
        tag: "script",
        attrs: (attrs)=>attrs.type == "text/typescript-jsx",
        parser: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$javascript$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tsxLanguage"].parser
    },
    {
        tag: "script",
        attrs (attrs) {
            return /^(importmap|speculationrules|application\/(.+\+)?json)$/i.test(attrs.type);
        },
        parser: jsonParser
    },
    {
        tag: "script",
        attrs (attrs) {
            return !attrs.type || /^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i.test(attrs.type);
        },
        parser: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$javascript$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["javascriptLanguage"].parser
    },
    {
        tag: "style",
        attrs (attrs) {
            return (!attrs.lang || attrs.lang == "css") && (!attrs.type || /^(text\/)?(x-)?(stylesheet|css)$/i.test(attrs.type));
        },
        parser: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$css$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cssLanguage"].parser
    }
];
const defaultAttrs = /*@__PURE__*/ [
    {
        name: "style",
        parser: /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$css$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cssLanguage"].parser.configure({
            top: "Styles"
        })
    }
].concat(/*@__PURE__*/ eventAttributes.map((name)=>({
        name,
        parser: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$javascript$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["javascriptLanguage"].parser
    })));
const htmlPlain = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LRLanguage"].define({
    name: "html",
    parser: /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$html$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parser"].configure({
        props: [
            /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["indentNodeProp"].add({
                Element (context) {
                    let after = /^(\s*)(<\/)?/.exec(context.textAfter);
                    if (context.node.to <= context.pos + after[0].length) return context.continue();
                    return context.lineIndent(context.node.from) + (after[2] ? 0 : context.unit);
                },
                "OpenTag CloseTag SelfClosingTag" (context) {
                    return context.column(context.node.from) + context.unit;
                },
                Document (context) {
                    if (context.pos + /\s*/.exec(context.textAfter)[0].length < context.node.to) return context.continue();
                    let endElt = null, close;
                    for(let cur = context.node;;){
                        let last = cur.lastChild;
                        if (!last || last.name != "Element" || last.to != cur.to) break;
                        endElt = cur = last;
                    }
                    if (endElt && !((close = endElt.lastChild) && (close.name == "CloseTag" || close.name == "SelfClosingTag"))) return context.lineIndent(endElt.from) + context.unit;
                    return null;
                }
            }),
            /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["foldNodeProp"].add({
                Element (node) {
                    let first = node.firstChild, last = node.lastChild;
                    if (!first || first.name != "OpenTag") return null;
                    return {
                        from: first.to,
                        to: last.name == "CloseTag" ? last.from : node.to
                    };
                }
            }),
            /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bracketMatchingHandle"].add({
                "OpenTag CloseTag": (node)=>node.getChild("TagName")
            })
        ]
    }),
    languageData: {
        commentTokens: {
            block: {
                open: "<!--",
                close: "-->"
            }
        },
        indentOnInput: /^\s*<\/\w+\W$/,
        wordChars: "-_"
    }
});
/**
A language provider based on the [Lezer HTML
parser](https://github.com/lezer-parser/html), extended with the
JavaScript and CSS parsers to parse the content of `<script>` and
`<style>` tags.
*/ const htmlLanguage = /*@__PURE__*/ htmlPlain.configure({
    wrap: /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$html$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["configureNesting"])(defaultNesting, defaultAttrs)
});
/**
Language support for HTML, including
[`htmlCompletion`](https://codemirror.net/6/docs/ref/#lang-html.htmlCompletion) and JavaScript and
CSS support extensions.
*/ function html(config = {}) {
    let dialect = "", wrap;
    if (config.matchClosingTags === false) dialect = "noMatch";
    if (config.selfClosingTags === true) dialect = (dialect ? dialect + " " : "") + "selfClosing";
    if (config.nestedLanguages && config.nestedLanguages.length || config.nestedAttributes && config.nestedAttributes.length) wrap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lezer$2f$html$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["configureNesting"])((config.nestedLanguages || []).concat(defaultNesting), (config.nestedAttributes || []).concat(defaultAttrs));
    let lang = wrap ? htmlPlain.configure({
        wrap,
        dialect
    }) : dialect ? htmlLanguage.configure({
        dialect
    }) : htmlLanguage;
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LanguageSupport"](lang, [
        htmlLanguage.data.of({
            autocomplete: htmlCompletionSourceWith(config)
        }),
        config.autoCloseTags !== false ? autoCloseTags : [],
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$javascript$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["javascript"])().support,
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$lang$2d$css$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["css"])().support
    ]);
}
const selfClosers = /*@__PURE__*/ new Set(/*@__PURE__*/ "area base br col command embed frame hr img input keygen link meta param source track wbr menuitem".split(" "));
/**
Extension that will automatically insert close tags when a `>` or
`/` is typed.
*/ const autoCloseTags = /*@__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$view$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EditorView"].inputHandler.of((view, from, to, text, insertTransaction)=>{
    if (view.composing || view.state.readOnly || from != to || text != ">" && text != "/" || !htmlLanguage.isActiveAt(view.state, from, -1)) return false;
    let base = insertTransaction(), { state } = base;
    let closeTags = state.changeByRange((range)=>{
        var _a, _b, _c;
        let didType = state.doc.sliceString(range.from - 1, range.to) == text;
        let { head } = range, after = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$language$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["syntaxTree"])(state).resolveInner(head, -1), name;
        if (didType && text == ">" && after.name == "EndTag") {
            let tag = after.parent;
            if (((_b = (_a = tag.parent) === null || _a === void 0 ? void 0 : _a.lastChild) === null || _b === void 0 ? void 0 : _b.name) != "CloseTag" && (name = elementName(state.doc, tag.parent, head)) && !selfClosers.has(name)) {
                let to = head + (state.doc.sliceString(head, head + 1) === ">" ? 1 : 0);
                let insert = `</${name}>`;
                return {
                    range,
                    changes: {
                        from: head,
                        to,
                        insert
                    }
                };
            }
        } else if (didType && text == "/" && after.name == "IncompleteCloseTag") {
            let tag = after.parent;
            if (after.from == head - 2 && ((_c = tag.lastChild) === null || _c === void 0 ? void 0 : _c.name) != "CloseTag" && (name = elementName(state.doc, tag, head)) && !selfClosers.has(name)) {
                let to = head + (state.doc.sliceString(head, head + 1) === ">" ? 1 : 0);
                let insert = `${name}>`;
                return {
                    range: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$codemirror$2f$state$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EditorSelection"].cursor(head + insert.length, -1),
                    changes: {
                        from: head,
                        to,
                        insert
                    }
                };
            }
        }
        return {
            range
        };
    });
    if (closeTags.changes.empty) return false;
    view.dispatch([
        base,
        state.update(closeTags, {
            userEvent: "input.complete",
            scrollIntoView: true
        })
    ]);
    return true;
});
;
}}),
}]);

//# sourceMappingURL=node_modules_45ba6756._.js.map