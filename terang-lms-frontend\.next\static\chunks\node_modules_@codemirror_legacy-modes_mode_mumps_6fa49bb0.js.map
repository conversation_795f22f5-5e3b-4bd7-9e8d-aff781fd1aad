{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/mumps.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\", \"i\");\n}\n\nvar singleOperators = new RegExp(\"^[\\\\+\\\\-\\\\*/&#!_?\\\\\\\\<>=\\\\'\\\\[\\\\]]\");\nvar doubleOperators = new RegExp(\"^(('=)|(<=)|(>=)|('>)|('<)|([[)|(]])|(^$))\");\nvar singleDelimiters = new RegExp(\"^[\\\\.,:]\");\nvar brackets = new RegExp(\"[()]\");\nvar identifiers = new RegExp(\"^[%A-Za-z][A-Za-z0-9]*\");\nvar commandKeywords = [\"break\",\"close\",\"do\",\"else\",\"for\",\"goto\", \"halt\", \"hang\", \"if\", \"job\",\"kill\",\"lock\",\"merge\",\"new\",\"open\", \"quit\", \"read\", \"set\", \"tcommit\", \"trollback\", \"tstart\", \"use\", \"view\", \"write\", \"xecute\", \"b\",\"c\",\"d\",\"e\",\"f\",\"g\", \"h\", \"i\", \"j\",\"k\",\"l\",\"m\",\"n\",\"o\", \"q\", \"r\", \"s\", \"tc\", \"tro\", \"ts\", \"u\", \"v\", \"w\", \"x\"];\n// The following list includes intrinsic functions _and_ special variables\nvar intrinsicFuncsWords = [\"\\\\$ascii\", \"\\\\$char\", \"\\\\$data\", \"\\\\$ecode\", \"\\\\$estack\", \"\\\\$etrap\", \"\\\\$extract\", \"\\\\$find\", \"\\\\$fnumber\", \"\\\\$get\", \"\\\\$horolog\", \"\\\\$io\", \"\\\\$increment\", \"\\\\$job\", \"\\\\$justify\", \"\\\\$length\", \"\\\\$name\", \"\\\\$next\", \"\\\\$order\", \"\\\\$piece\", \"\\\\$qlength\", \"\\\\$qsubscript\", \"\\\\$query\", \"\\\\$quit\", \"\\\\$random\", \"\\\\$reverse\", \"\\\\$select\", \"\\\\$stack\", \"\\\\$test\", \"\\\\$text\", \"\\\\$translate\", \"\\\\$view\", \"\\\\$x\", \"\\\\$y\", \"\\\\$a\", \"\\\\$c\", \"\\\\$d\", \"\\\\$e\", \"\\\\$ec\", \"\\\\$es\", \"\\\\$et\", \"\\\\$f\", \"\\\\$fn\", \"\\\\$g\", \"\\\\$h\", \"\\\\$i\", \"\\\\$j\", \"\\\\$l\", \"\\\\$n\", \"\\\\$na\", \"\\\\$o\", \"\\\\$p\", \"\\\\$q\", \"\\\\$ql\", \"\\\\$qs\", \"\\\\$r\", \"\\\\$re\", \"\\\\$s\", \"\\\\$st\", \"\\\\$t\", \"\\\\$tr\", \"\\\\$v\", \"\\\\$z\"];\nvar intrinsicFuncs = wordRegexp(intrinsicFuncsWords);\nvar command = wordRegexp(commandKeywords);\n\nfunction tokenBase(stream, state) {\n  if (stream.sol()) {\n    state.label = true;\n    state.commandMode = 0;\n  }\n\n  // The <space> character has meaning in MUMPS. Ignoring consecutive\n  // spaces would interfere with interpreting whether the next non-space\n  // character belongs to the command or argument context.\n\n  // Examine each character and update a mode variable whose interpretation is:\n  //   >0 => command    0 => argument    <0 => command post-conditional\n  var ch = stream.peek();\n\n  if (ch == \" \" || ch == \"\\t\") { // Pre-process <space>\n    state.label = false;\n    if (state.commandMode == 0)\n      state.commandMode = 1;\n    else if ((state.commandMode < 0) || (state.commandMode == 2))\n      state.commandMode = 0;\n  } else if ((ch != \".\") && (state.commandMode > 0)) {\n    if (ch == \":\")\n      state.commandMode = -1;   // SIS - Command post-conditional\n    else\n      state.commandMode = 2;\n  }\n\n  // Do not color parameter list as line tag\n  if ((ch === \"(\") || (ch === \"\\u0009\"))\n    state.label = false;\n\n  // MUMPS comment starts with \";\"\n  if (ch === \";\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  // Number Literals // SIS/RLM - MUMPS permits canonic number followed by concatenate operator\n  if (stream.match(/^[-+]?\\d+(\\.\\d+)?([eE][-+]?\\d+)?/))\n    return \"number\";\n\n  // Handle Strings\n  if (ch == '\"') {\n    if (stream.skipTo('\"')) {\n      stream.next();\n      return \"string\";\n    } else {\n      stream.skipToEnd();\n      return \"error\";\n    }\n  }\n\n  // Handle operators and Delimiters\n  if (stream.match(doubleOperators) || stream.match(singleOperators))\n    return \"operator\";\n\n  // Prevents leading \".\" in DO block from falling through to error\n  if (stream.match(singleDelimiters))\n    return null;\n\n  if (brackets.test(ch)) {\n    stream.next();\n    return \"bracket\";\n  }\n\n  if (state.commandMode > 0 && stream.match(command))\n    return \"controlKeyword\";\n\n  if (stream.match(intrinsicFuncs))\n    return \"builtin\";\n\n  if (stream.match(identifiers))\n    return \"variable\";\n\n  // Detect dollar-sign when not a documented intrinsic function\n  // \"^\" may introduce a GVN or SSVN - Color same as function\n  if (ch === \"$\" || ch === \"^\") {\n    stream.next();\n    return \"builtin\";\n  }\n\n  // MUMPS Indirection\n  if (ch === \"@\") {\n    stream.next();\n    return \"string.special\";\n  }\n\n  if (/[\\w%]/.test(ch)) {\n    stream.eatWhile(/[\\w%]/);\n    return \"variable\";\n  }\n\n  // Handle non-detected items\n  stream.next();\n  return \"error\";\n}\n\nexport const mumps = {\n  name: \"mumps\",\n  startState: function() {\n    return {\n      label: false,\n      commandMode: 0\n    };\n  },\n\n  token: function(stream, state) {\n    var style = tokenBase(stream, state);\n    if (state.label) return \"tag\";\n    return style;\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,WAAW,KAAK;IACvB,OAAO,IAAI,OAAO,QAAQ,MAAM,IAAI,CAAC,SAAS,SAAS;AACzD;AAEA,IAAI,kBAAkB,IAAI,OAAO;AACjC,IAAI,kBAAkB,IAAI,OAAO;AACjC,IAAI,mBAAmB,IAAI,OAAO;AAClC,IAAI,WAAW,IAAI,OAAO;AAC1B,IAAI,cAAc,IAAI,OAAO;AAC7B,IAAI,kBAAkB;IAAC;IAAQ;IAAQ;IAAK;IAAO;IAAM;IAAQ;IAAQ;IAAQ;IAAM;IAAM;IAAO;IAAO;IAAQ;IAAM;IAAQ;IAAQ;IAAQ;IAAO;IAAW;IAAa;IAAU;IAAO;IAAQ;IAAS;IAAU;IAAI;IAAI;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;IAAI;IAAI;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;IAAK;IAAM;IAAO;IAAM;IAAK;IAAK;IAAK;CAAI;AAC7U,0EAA0E;AAC1E,IAAI,sBAAsB;IAAC;IAAY;IAAW;IAAW;IAAY;IAAa;IAAY;IAAc;IAAW;IAAc;IAAU;IAAc;IAAS;IAAgB;IAAU;IAAc;IAAa;IAAW;IAAW;IAAY;IAAY;IAAc;IAAiB;IAAY;IAAW;IAAa;IAAc;IAAa;IAAY;IAAW;IAAW;IAAgB;IAAW;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAS;IAAS;IAAS;IAAQ;IAAS;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAQ;IAAS;IAAQ;IAAQ;IAAQ;IAAS;IAAS;IAAQ;IAAS;IAAQ;IAAS;IAAQ;IAAS;IAAQ;CAAO;AACzqB,IAAI,iBAAiB,WAAW;AAChC,IAAI,UAAU,WAAW;AAEzB,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,OAAO,GAAG,IAAI;QAChB,MAAM,KAAK,GAAG;QACd,MAAM,WAAW,GAAG;IACtB;IAEA,mEAAmE;IACnE,sEAAsE;IACtE,wDAAwD;IAExD,6EAA6E;IAC7E,qEAAqE;IACrE,IAAI,KAAK,OAAO,IAAI;IAEpB,IAAI,MAAM,OAAO,MAAM,MAAM;QAC3B,MAAM,KAAK,GAAG;QACd,IAAI,MAAM,WAAW,IAAI,GACvB,MAAM,WAAW,GAAG;aACjB,IAAI,AAAC,MAAM,WAAW,GAAG,KAAO,MAAM,WAAW,IAAI,GACxD,MAAM,WAAW,GAAG;IACxB,OAAO,IAAI,AAAC,MAAM,OAAS,MAAM,WAAW,GAAG,GAAI;QACjD,IAAI,MAAM,KACR,MAAM,WAAW,GAAG,CAAC,GAAK,iCAAiC;aAE3D,MAAM,WAAW,GAAG;IACxB;IAEA,0CAA0C;IAC1C,IAAI,AAAC,OAAO,OAAS,OAAO,UAC1B,MAAM,KAAK,GAAG;IAEhB,gCAAgC;IAChC,IAAI,OAAO,KAAK;QACd,OAAO,SAAS;QAChB,OAAO;IACT;IAEA,6FAA6F;IAC7F,IAAI,OAAO,KAAK,CAAC,qCACf,OAAO;IAET,iBAAiB;IACjB,IAAI,MAAM,KAAK;QACb,IAAI,OAAO,MAAM,CAAC,MAAM;YACtB,OAAO,IAAI;YACX,OAAO;QACT,OAAO;YACL,OAAO,SAAS;YAChB,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,IAAI,OAAO,KAAK,CAAC,oBAAoB,OAAO,KAAK,CAAC,kBAChD,OAAO;IAET,iEAAiE;IACjE,IAAI,OAAO,KAAK,CAAC,mBACf,OAAO;IAET,IAAI,SAAS,IAAI,CAAC,KAAK;QACrB,OAAO,IAAI;QACX,OAAO;IACT;IAEA,IAAI,MAAM,WAAW,GAAG,KAAK,OAAO,KAAK,CAAC,UACxC,OAAO;IAET,IAAI,OAAO,KAAK,CAAC,iBACf,OAAO;IAET,IAAI,OAAO,KAAK,CAAC,cACf,OAAO;IAET,8DAA8D;IAC9D,2DAA2D;IAC3D,IAAI,OAAO,OAAO,OAAO,KAAK;QAC5B,OAAO,IAAI;QACX,OAAO;IACT;IAEA,oBAAoB;IACpB,IAAI,OAAO,KAAK;QACd,OAAO,IAAI;QACX,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,CAAC,KAAK;QACpB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IAEA,4BAA4B;IAC5B,OAAO,IAAI;IACX,OAAO;AACT;AAEO,MAAM,QAAQ;IACnB,MAAM;IACN,YAAY;QACV,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,QAAQ,UAAU,QAAQ;QAC9B,IAAI,MAAM,KAAK,EAAE,OAAO;QACxB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}