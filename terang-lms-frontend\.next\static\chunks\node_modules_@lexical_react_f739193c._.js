(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "LexicalComposerContext": (()=>LexicalComposerContext),
    "createLexicalComposerContext": (()=>createLexicalComposerContext),
    "useLexicalComposerContext": (()=>useLexicalComposerContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Do not require this module directly! Use normal `invariant` calls.
function formatDevErrorMessage(message) {
    throw new Error(message);
}
const LexicalComposerContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
function createLexicalComposerContext(parent, theme) {
    let parentContext = null;
    if (parent != null) {
        parentContext = parent[1];
    }
    function getTheme() {
        if (theme != null) {
            return theme;
        }
        return parentContext != null ? parentContext.getTheme() : null;
    }
    return {
        getTheme
    };
}
function useLexicalComposerContext() {
    const composerContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(LexicalComposerContext);
    if (composerContext == null) {
        {
            formatDevErrorMessage(`LexicalComposerContext.useLexicalComposerContext: cannot find a LexicalComposerContext`);
        }
    }
    return composerContext;
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "HistoryPlugin": (()=>HistoryPlugin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$history$2f$LexicalHistory$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/history/LexicalHistory.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function useHistory(editor, externalHistoryState, delay = 1000) {
    const historyState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useHistory.useMemo[historyState]": ()=>externalHistoryState || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$history$2f$LexicalHistory$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createEmptyHistoryState"])()
    }["useHistory.useMemo[historyState]"], [
        externalHistoryState
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useHistory.useEffect": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$history$2f$LexicalHistory$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["registerHistory"])(editor, historyState, delay);
        }
    }["useHistory.useEffect"], [
        delay,
        editor,
        historyState
    ]);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function HistoryPlugin({ delay, externalHistoryState }) {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    useHistory(editor, externalHistoryState, delay);
    return null;
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalContentEditable.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "ContentEditable": (()=>ContentEditable),
    "ContentEditableElement": (()=>ContentEditableElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$text$2f$LexicalText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/text/LexicalText.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
;
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // This workaround is no longer necessary in React 19,
// but we currently support React >=17.x
// https://github.com/facebook/react/pull/26395
const useLayoutEffectImpl = CAN_USE_DOM ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"];
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Source: https://github.com/gregberge/react-merge-refs/blob/main/src/index.tsx
function mergeRefs(...refs) {
    return (value)=>{
        refs.forEach((ref)=>{
            if (typeof ref === 'function') {
                ref(value);
            } else if (ref != null) {
                ref.current = value;
            }
        });
    };
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function ContentEditableElementImpl({ editor, ariaActiveDescendant, ariaAutoComplete, ariaControls, ariaDescribedBy, ariaErrorMessage, ariaExpanded, ariaInvalid, ariaLabel, ariaLabelledBy, ariaMultiline, ariaOwns, ariaRequired, autoCapitalize, className, id, role = 'textbox', spellCheck = true, style, tabIndex, 'data-testid': testid, ...rest }, ref) {
    const [isEditable, setEditable] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(editor.isEditable());
    const handleRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ContentEditableElementImpl.useCallback[handleRef]": (rootElement)=>{
            // defaultView is required for a root element.
            // In multi-window setups, the defaultView may not exist at certain points.
            if (rootElement && rootElement.ownerDocument && rootElement.ownerDocument.defaultView) {
                editor.setRootElement(rootElement);
            } else {
                editor.setRootElement(null);
            }
        }
    }["ContentEditableElementImpl.useCallback[handleRef]"], [
        editor
    ]);
    const mergedRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ContentEditableElementImpl.useMemo[mergedRefs]": ()=>mergeRefs(ref, handleRef)
    }["ContentEditableElementImpl.useMemo[mergedRefs]"], [
        handleRef,
        ref
    ]);
    useLayoutEffectImpl({
        "ContentEditableElementImpl.useLayoutEffectImpl": ()=>{
            setEditable(editor.isEditable());
            return editor.registerEditableListener({
                "ContentEditableElementImpl.useLayoutEffectImpl": (currentIsEditable)=>{
                    setEditable(currentIsEditable);
                }
            }["ContentEditableElementImpl.useLayoutEffectImpl"]);
        }
    }["ContentEditableElementImpl.useLayoutEffectImpl"], [
        editor
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        "aria-activedescendant": isEditable ? ariaActiveDescendant : undefined,
        "aria-autocomplete": isEditable ? ariaAutoComplete : 'none',
        "aria-controls": isEditable ? ariaControls : undefined,
        "aria-describedby": ariaDescribedBy,
        ...ariaErrorMessage != null ? {
            'aria-errormessage': ariaErrorMessage
        } : {},
        "aria-expanded": isEditable && role === 'combobox' ? !!ariaExpanded : undefined,
        ...ariaInvalid != null ? {
            'aria-invalid': ariaInvalid
        } : {},
        "aria-label": ariaLabel,
        "aria-labelledby": ariaLabelledBy,
        "aria-multiline": ariaMultiline,
        "aria-owns": isEditable ? ariaOwns : undefined,
        "aria-readonly": isEditable ? undefined : true,
        "aria-required": ariaRequired,
        autoCapitalize: autoCapitalize,
        className: className,
        contentEditable: isEditable,
        "data-testid": testid,
        id: id,
        ref: mergedRefs,
        role: role,
        spellCheck: spellCheck,
        style: style,
        tabIndex: tabIndex,
        ...rest
    });
}
const ContentEditableElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(ContentEditableElementImpl);
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function canShowPlaceholderFromCurrentEditorState(editor) {
    const currentCanShowPlaceholder = editor.getEditorState().read((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$text$2f$LexicalText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$canShowPlaceholderCurry"])(editor.isComposing()));
    return currentCanShowPlaceholder;
}
function useCanShowPlaceholder(editor) {
    const [canShowPlaceholder, setCanShowPlaceholder] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "useCanShowPlaceholder.useState": ()=>canShowPlaceholderFromCurrentEditorState(editor)
    }["useCanShowPlaceholder.useState"]);
    useLayoutEffectImpl({
        "useCanShowPlaceholder.useLayoutEffectImpl": ()=>{
            function resetCanShowPlaceholder() {
                const currentCanShowPlaceholder = canShowPlaceholderFromCurrentEditorState(editor);
                setCanShowPlaceholder(currentCanShowPlaceholder);
            }
            resetCanShowPlaceholder();
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])(editor.registerUpdateListener({
                "useCanShowPlaceholder.useLayoutEffectImpl": ()=>{
                    resetCanShowPlaceholder();
                }
            }["useCanShowPlaceholder.useLayoutEffectImpl"]), editor.registerEditableListener({
                "useCanShowPlaceholder.useLayoutEffectImpl": ()=>{
                    resetCanShowPlaceholder();
                }
            }["useCanShowPlaceholder.useLayoutEffectImpl"]));
        }
    }["useCanShowPlaceholder.useLayoutEffectImpl"], [
        editor
    ]);
    return canShowPlaceholder;
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * @deprecated This type has been renamed to `ContentEditableProps` to provide a clearer and more descriptive name.
 * For backward compatibility, this type is still exported as `Props`, but it is recommended to migrate to using `ContentEditableProps` instead.
 *
 * @note This alias is maintained for compatibility purposes but may be removed in future versions.
 * Please update your codebase to use `ContentEditableProps` to ensure long-term maintainability.
 */ const ContentEditable = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(ContentEditableImpl);
function ContentEditableImpl(props, ref) {
    const { placeholder, ...rest } = props;
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(ContentEditableElement, {
                editor: editor,
                ...rest,
                ref: ref
            }),
            placeholder != null && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Placeholder, {
                editor: editor,
                content: placeholder
            })
        ]
    });
}
function Placeholder({ content, editor }) {
    const showPlaceholder = useCanShowPlaceholder(editor);
    const [isEditable, setEditable] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(editor.isEditable());
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "Placeholder.useLayoutEffect": ()=>{
            setEditable(editor.isEditable());
            return editor.registerEditableListener({
                "Placeholder.useLayoutEffect": (currentIsEditable)=>{
                    setEditable(currentIsEditable);
                }
            }["Placeholder.useLayoutEffect"]);
        }
    }["Placeholder.useLayoutEffect"], [
        editor
    ]);
    if (!showPlaceholder) {
        return null;
    }
    let placeholder = null;
    if (typeof content === 'function') {
        placeholder = content(isEditable);
    } else if (content !== null) {
        placeholder = content;
    }
    if (placeholder === null) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        "aria-hidden": true,
        children: placeholder
    });
}
;
}}),
"[project]/node_modules/@lexical/react/useLexicalEditable.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "useLexicalEditable": (()=>useLexicalEditable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // This workaround is no longer necessary in React 19,
// but we currently support React >=17.x
// https://github.com/facebook/react/pull/26395
const useLayoutEffectImpl = CAN_USE_DOM ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"];
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * Shortcut to Lexical subscriptions when values are used for render.
 * @param subscription - The function to create the {@link LexicalSubscription}. This function's identity must be stable (e.g. defined at module scope or with useCallback).
 */ function useLexicalSubscription(subscription) {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    const initializedSubscription = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useLexicalSubscription.useMemo[initializedSubscription]": ()=>subscription(editor)
    }["useLexicalSubscription.useMemo[initializedSubscription]"], [
        editor,
        subscription
    ]);
    const [value, setValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "useLexicalSubscription.useState": ()=>initializedSubscription.initialValueFn()
    }["useLexicalSubscription.useState"]);
    const valueRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(value);
    useLayoutEffectImpl({
        "useLexicalSubscription.useLayoutEffectImpl": ()=>{
            const { initialValueFn, subscribe } = initializedSubscription;
            const currentValue = initialValueFn();
            if (valueRef.current !== currentValue) {
                valueRef.current = currentValue;
                setValue(currentValue);
            }
            return subscribe({
                "useLexicalSubscription.useLayoutEffectImpl": (newValue)=>{
                    valueRef.current = newValue;
                    setValue(newValue);
                }
            }["useLexicalSubscription.useLayoutEffectImpl"]);
        }
    }["useLexicalSubscription.useLayoutEffectImpl"], [
        initializedSubscription,
        subscription
    ]);
    return value;
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function subscription(editor) {
    return {
        initialValueFn: ()=>editor.isEditable(),
        subscribe: (callback)=>{
            return editor.registerEditableListener(callback);
        }
    };
}
/**
 * Get the current value for {@link LexicalEditor.isEditable}
 * using {@link useLexicalSubscription}.
 * You should prefer this over manually observing the value with
 * {@link LexicalEditor.registerEditableListener},
 * which is a bit tricky to do correctly, particularly when using
 * React StrictMode (the default for development) or concurrency.
 */ function useLexicalEditable() {
    return useLexicalSubscription(subscription);
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "RichTextPlugin": (()=>RichTextPlugin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$useLexicalEditable$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/useLexicalEditable.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$text$2f$LexicalText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/text/LexicalText.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$dragon$2f$LexicalDragon$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/dragon/LexicalDragon.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // This workaround is no longer necessary in React 19,
// but we currently support React >=17.x
// https://github.com/facebook/react/pull/26395
const useLayoutEffectImpl = CAN_USE_DOM ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"];
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function canShowPlaceholderFromCurrentEditorState(editor) {
    const currentCanShowPlaceholder = editor.getEditorState().read((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$text$2f$LexicalText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$canShowPlaceholderCurry"])(editor.isComposing()));
    return currentCanShowPlaceholder;
}
function useCanShowPlaceholder(editor) {
    const [canShowPlaceholder, setCanShowPlaceholder] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "useCanShowPlaceholder.useState": ()=>canShowPlaceholderFromCurrentEditorState(editor)
    }["useCanShowPlaceholder.useState"]);
    useLayoutEffectImpl({
        "useCanShowPlaceholder.useLayoutEffectImpl": ()=>{
            function resetCanShowPlaceholder() {
                const currentCanShowPlaceholder = canShowPlaceholderFromCurrentEditorState(editor);
                setCanShowPlaceholder(currentCanShowPlaceholder);
            }
            resetCanShowPlaceholder();
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])(editor.registerUpdateListener({
                "useCanShowPlaceholder.useLayoutEffectImpl": ()=>{
                    resetCanShowPlaceholder();
                }
            }["useCanShowPlaceholder.useLayoutEffectImpl"]), editor.registerEditableListener({
                "useCanShowPlaceholder.useLayoutEffectImpl": ()=>{
                    resetCanShowPlaceholder();
                }
            }["useCanShowPlaceholder.useLayoutEffectImpl"]));
        }
    }["useCanShowPlaceholder.useLayoutEffectImpl"], [
        editor
    ]);
    return canShowPlaceholder;
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function useDecorators(editor, ErrorBoundary) {
    const [decorators, setDecorators] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "useDecorators.useState": ()=>editor.getDecorators()
    }["useDecorators.useState"]);
    // Subscribe to changes
    useLayoutEffectImpl({
        "useDecorators.useLayoutEffectImpl": ()=>{
            return editor.registerDecoratorListener({
                "useDecorators.useLayoutEffectImpl": (nextDecorators)=>{
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["flushSync"])({
                        "useDecorators.useLayoutEffectImpl": ()=>{
                            setDecorators(nextDecorators);
                        }
                    }["useDecorators.useLayoutEffectImpl"]);
                }
            }["useDecorators.useLayoutEffectImpl"]);
        }
    }["useDecorators.useLayoutEffectImpl"], [
        editor
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useDecorators.useEffect": ()=>{
            // If the content editable mounts before the subscription is added, then
            // nothing will be rendered on initial pass. We can get around that by
            // ensuring that we set the value.
            setDecorators(editor.getDecorators());
        }
    }["useDecorators.useEffect"], [
        editor
    ]);
    // Return decorators defined as React Portals
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useDecorators.useMemo": ()=>{
            const decoratedPortals = [];
            const decoratorKeys = Object.keys(decorators);
            for(let i = 0; i < decoratorKeys.length; i++){
                const nodeKey = decoratorKeys[i];
                const reactDecorator = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(ErrorBoundary, {
                    onError: {
                        "useDecorators.useMemo.reactDecorator": (e)=>editor._onError(e)
                    }["useDecorators.useMemo.reactDecorator"],
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Suspense"], {
                        fallback: null,
                        children: decorators[nodeKey]
                    })
                });
                const element = editor.getElementByKey(nodeKey);
                if (element !== null) {
                    decoratedPortals.push(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])(reactDecorator, element, nodeKey));
                }
            }
            return decoratedPortals;
        }
    }["useDecorators.useMemo"], [
        ErrorBoundary,
        decorators,
        editor
    ]);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function useRichTextSetup(editor) {
    useLayoutEffectImpl({
        "useRichTextSetup.useLayoutEffectImpl": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["registerRichText"])(editor), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$dragon$2f$LexicalDragon$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["registerDragonSupport"])(editor));
        // We only do this for init
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["useRichTextSetup.useLayoutEffectImpl"], [
        editor
    ]);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function RichTextPlugin({ contentEditable, // TODO Remove. This property is now part of ContentEditable
placeholder = null, ErrorBoundary }) {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    const decorators = useDecorators(editor, ErrorBoundary);
    useRichTextSetup(editor);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            contentEditable,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Placeholder, {
                content: placeholder
            }),
            decorators
        ]
    });
}
// TODO remove
function Placeholder({ content }) {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    const showPlaceholder = useCanShowPlaceholder(editor);
    const editable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$useLexicalEditable$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalEditable"])();
    if (!showPlaceholder) {
        return null;
    }
    if (typeof content === 'function') {
        return content(editable);
    } else {
        return content;
    }
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "LexicalErrorBoundary": (()=>LexicalErrorBoundary)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
;
;
function _setPrototypeOf(o, p) {
    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _setPrototypeOf(o, p);
}
function _inheritsLoose(subClass, superClass) {
    subClass.prototype = Object.create(superClass.prototype);
    subClass.prototype.constructor = subClass;
    _setPrototypeOf(subClass, superClass);
}
var changedArray = function changedArray(a, b) {
    if (a === void 0) {
        a = [];
    }
    if (b === void 0) {
        b = [];
    }
    return a.length !== b.length || a.some(function(item, index) {
        return !Object.is(item, b[index]);
    });
};
var initialState = {
    error: null
};
var ErrorBoundary = /*#__PURE__*/ function(_React$Component) {
    _inheritsLoose(ErrorBoundary, _React$Component);
    function ErrorBoundary() {
        var _this;
        for(var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++){
            _args[_key] = arguments[_key];
        }
        _this = _React$Component.call.apply(_React$Component, [
            this
        ].concat(_args)) || this;
        _this.state = initialState;
        _this.resetErrorBoundary = function() {
            var _this$props;
            for(var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){
                args[_key2] = arguments[_key2];
            }
            _this.props.onReset == null ? void 0 : (_this$props = _this.props).onReset.apply(_this$props, args);
            _this.reset();
        };
        return _this;
    }
    ErrorBoundary.getDerivedStateFromError = function getDerivedStateFromError(error) {
        return {
            error: error
        };
    };
    var _proto = ErrorBoundary.prototype;
    _proto.reset = function reset() {
        this.setState(initialState);
    };
    _proto.componentDidCatch = function componentDidCatch(error, info) {
        var _this$props$onError, _this$props2;
        (_this$props$onError = (_this$props2 = this.props).onError) == null ? void 0 : _this$props$onError.call(_this$props2, error, info);
    };
    _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {
        var error = this.state.error;
        var resetKeys = this.props.resetKeys; // There's an edge case where if the thing that triggered the error
        // happens to *also* be in the resetKeys array, we'd end up resetting
        // the error boundary immediately. This would likely trigger a second
        // error to be thrown.
        // So we make sure that we don't check the resetKeys on the first call
        // of cDU after the error is set
        if (error !== null && prevState.error !== null && changedArray(prevProps.resetKeys, resetKeys)) {
            var _this$props$onResetKe, _this$props3;
            (_this$props$onResetKe = (_this$props3 = this.props).onResetKeysChange) == null ? void 0 : _this$props$onResetKe.call(_this$props3, prevProps.resetKeys, resetKeys);
            this.reset();
        }
    };
    _proto.render = function render() {
        var error = this.state.error;
        var _this$props4 = this.props, fallbackRender = _this$props4.fallbackRender, FallbackComponent = _this$props4.FallbackComponent, fallback = _this$props4.fallback;
        if (error !== null) {
            var _props = {
                error: error,
                resetErrorBoundary: this.resetErrorBoundary
            };
            if (/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(fallback)) {
                return fallback;
            } else if (typeof fallbackRender === 'function') {
                return fallbackRender(_props);
            } else if (FallbackComponent) {
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(FallbackComponent, _props);
            } else {
                throw new Error('react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop');
            }
        }
        return this.props.children;
    };
    return ErrorBoundary;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"]);
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function LexicalErrorBoundary({ children, onError }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(ErrorBoundary, {
        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
            style: {
                border: '1px solid #f00',
                color: '#f00',
                padding: '8px'
            },
            children: "An error was thrown."
        }),
        onError: onError,
        children: children
    });
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalTabIndentationPlugin.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "TabIndentationPlugin": (()=>TabIndentationPlugin),
    "registerTabIndentation": (()=>registerTabIndentation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function $indentOverTab(selection) {
    // const handled = new Set();
    const nodes = selection.getNodes();
    const canIndentBlockNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$filter"])(nodes, (node)=>{
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isBlockElementNode"])(node) && node.canIndent()) {
            return node;
        }
        return null;
    });
    // 1. If selection spans across canIndent block nodes: indent
    if (canIndentBlockNodes.length > 0) {
        return true;
    }
    // 2. If first (anchor/focus) is at block start: indent
    const anchor = selection.anchor;
    const focus = selection.focus;
    const first = focus.isBefore(anchor) ? focus : anchor;
    const firstNode = first.getNode();
    const firstBlock = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$getNearestBlockElementAncestorOrThrow"])(firstNode);
    if (firstBlock.canIndent()) {
        const firstBlockKey = firstBlock.getKey();
        let selectionAtStart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createRangeSelection"])();
        selectionAtStart.anchor.set(firstBlockKey, 0, 'element');
        selectionAtStart.focus.set(firstBlockKey, 0, 'element');
        selectionAtStart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$normalizeSelection__EXPERIMENTAL"])(selectionAtStart);
        if (selectionAtStart.anchor.is(first)) {
            return true;
        }
    }
    // 3. Else: tab
    return false;
}
function registerTabIndentation(editor, maxIndent) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])(editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KEY_TAB_COMMAND"], (event)=>{
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        event.preventDefault();
        const command = $indentOverTab(selection) ? event.shiftKey ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OUTDENT_CONTENT_COMMAND"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INDENT_CONTENT_COMMAND"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_TAB_COMMAND"];
        return editor.dispatchCommand(command, undefined);
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INDENT_CONTENT_COMMAND"], ()=>{
        if (maxIndent == null) {
            return false;
        }
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
            return false;
        }
        const indents = selection.getNodes().map((node)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$getNearestBlockElementAncestorOrThrow"])(node).getIndent());
        return Math.max(...indents) + 1 >= maxIndent;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_CRITICAL"]));
}
/**
 * This plugin adds the ability to indent content using the tab key. Generally, we don't
 * recommend using this plugin as it could negatively affect accessibility for keyboard
 * users, causing focus to become trapped within the editor.
 */ function TabIndentationPlugin({ maxIndent }) {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TabIndentationPlugin.useEffect": ()=>{
            return registerTabIndentation(editor, maxIndent);
        }
    }["TabIndentationPlugin.useEffect"], [
        editor,
        maxIndent
    ]);
    return null;
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "CheckListPlugin": (()=>CheckListPlugin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/list/LexicalList.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function CheckListPlugin() {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CheckListPlugin.useEffect": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["registerCheckList"])(editor);
        }
    }["CheckListPlugin.useEffect"], [
        editor
    ]);
    return null;
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalListPlugin.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "ListPlugin": (()=>ListPlugin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/list/LexicalList.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function useList(editor) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useList.useEffect": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["registerList"])(editor);
        }
    }["useList.useEffect"], [
        editor
    ]);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function ListPlugin({ hasStrictIndent = false }) {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ListPlugin.useEffect": ()=>{
            if (!editor.hasNodes([
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListNode"],
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListItemNode"]
            ])) {
                throw new Error('ListPlugin: ListNode and/or ListItemNode not registered on editor');
            }
        }
    }["ListPlugin.useEffect"], [
        editor
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ListPlugin.useEffect": ()=>{
            if (!hasStrictIndent) {
                return;
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["registerListStrictIndentTransform"])(editor);
        }
    }["ListPlugin.useEffect"], [
        editor,
        hasStrictIndent
    ]);
    useList(editor);
    return null;
}
;
}}),
"[project]/node_modules/@lexical/react/useLexicalNodeSelection.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "useLexicalNodeSelection": (()=>useLexicalNodeSelection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /**
 * A helper function to determine if a specific node is selected in a Lexical editor.
 *
 * @param {LexicalEditor} editor - The LexicalEditor instance.
 * @param {NodeKey} key - The key of the node to check.
 * @returns {boolean} Whether the node is selected.
 */ function isNodeSelected(editor, key) {
    return editor.getEditorState().read(()=>{
        const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getNodeByKey"])(key);
        if (node === null) {
            return false; // Node doesn't exist, so it's not selected.
        }
        return node.isSelected(); // Check if the node is selected.
    });
}
/**
 * A custom hook to manage the selection state of a specific node in a Lexical editor.
 *
 * This hook provides utilities to:
 * - Check if a node is selected.
 * - Update its selection state.
 * - Clear the selection.
 *
 * @param {NodeKey} key - The key of the node to track selection for.
 * @returns {[boolean, (selected: boolean) => void, () => void]} A tuple containing:
 * - `isSelected` (boolean): Whether the node is currently selected.
 * - `setSelected` (function): A function to set the selection state of the node.
 * - `clearSelected` (function): A function to clear the selection of the node.
 *
 */ function useLexicalNodeSelection(key) {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    // State to track whether the node is currently selected.
    const [isSelected, setIsSelected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "useLexicalNodeSelection.useState": ()=>isNodeSelected(editor, key)
    }["useLexicalNodeSelection.useState"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useLexicalNodeSelection.useEffect": ()=>{
            let isMounted = true;
            const unregister = editor.registerUpdateListener({
                "useLexicalNodeSelection.useEffect.unregister": ()=>{
                    if (isMounted) {
                        setIsSelected(isNodeSelected(editor, key));
                    }
                }
            }["useLexicalNodeSelection.useEffect.unregister"]);
            return ({
                "useLexicalNodeSelection.useEffect": ()=>{
                    isMounted = false; // Prevent updates after component unmount.
                    unregister();
                }
            })["useLexicalNodeSelection.useEffect"];
        }
    }["useLexicalNodeSelection.useEffect"], [
        editor,
        key
    ]);
    const setSelected = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLexicalNodeSelection.useCallback[setSelected]": (selected)=>{
            editor.update({
                "useLexicalNodeSelection.useCallback[setSelected]": ()=>{
                    let selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
                    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
                        selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createNodeSelection"])();
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$setSelection"])(selection);
                    }
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
                        if (selected) {
                            selection.add(key);
                        } else {
                            selection.delete(key);
                        }
                    }
                }
            }["useLexicalNodeSelection.useCallback[setSelected]"]);
        }
    }["useLexicalNodeSelection.useCallback[setSelected]"], [
        editor,
        key
    ]);
    const clearSelected = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLexicalNodeSelection.useCallback[clearSelected]": ()=>{
            editor.update({
                "useLexicalNodeSelection.useCallback[clearSelected]": ()=>{
                    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
                        selection.clear();
                    }
                }
            }["useLexicalNodeSelection.useCallback[clearSelected]"]);
        }
    }["useLexicalNodeSelection.useCallback[clearSelected]"], [
        editor
    ]);
    return [
        isSelected,
        setSelected,
        clearSelected
    ];
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "$createHorizontalRuleNode": (()=>$createHorizontalRuleNode),
    "$isHorizontalRuleNode": (()=>$isHorizontalRuleNode),
    "HorizontalRuleNode": (()=>HorizontalRuleNode),
    "INSERT_HORIZONTAL_RULE_COMMAND": (()=>INSERT_HORIZONTAL_RULE_COMMAND)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$useLexicalNodeSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/useLexicalNodeSelection.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
;
;
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const INSERT_HORIZONTAL_RULE_COMMAND = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createCommand"])('INSERT_HORIZONTAL_RULE_COMMAND');
function HorizontalRuleComponent({ nodeKey }) {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    const [isSelected, setSelected, clearSelection] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$useLexicalNodeSelection$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalNodeSelection"])(nodeKey);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HorizontalRuleComponent.useEffect": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])(editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLICK_COMMAND"], {
                "HorizontalRuleComponent.useEffect": (event)=>{
                    const hrElem = editor.getElementByKey(nodeKey);
                    if (event.target === hrElem) {
                        if (!event.shiftKey) {
                            clearSelection();
                        }
                        setSelected(!isSelected);
                        return true;
                    }
                    return false;
                }
            }["HorizontalRuleComponent.useEffect"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]));
        }
    }["HorizontalRuleComponent.useEffect"], [
        clearSelection,
        editor,
        isSelected,
        nodeKey,
        setSelected
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HorizontalRuleComponent.useEffect": ()=>{
            const hrElem = editor.getElementByKey(nodeKey);
            const isSelectedClassName = editor._config.theme.hrSelected ?? 'selected';
            if (hrElem !== null) {
                if (isSelected) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addClassNamesToElement"])(hrElem, isSelectedClassName);
                } else {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["removeClassNamesFromElement"])(hrElem, isSelectedClassName);
                }
            }
        }
    }["HorizontalRuleComponent.useEffect"], [
        editor,
        isSelected,
        nodeKey
    ]);
    return null;
}
class HorizontalRuleNode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecoratorNode"] {
    static getType() {
        return 'horizontalrule';
    }
    static clone(node) {
        return new HorizontalRuleNode(node.__key);
    }
    static importJSON(serializedNode) {
        return $createHorizontalRuleNode().updateFromJSON(serializedNode);
    }
    static importDOM() {
        return {
            hr: ()=>({
                    conversion: $convertHorizontalRuleElement,
                    priority: 0
                })
        };
    }
    exportDOM() {
        return {
            element: document.createElement('hr')
        };
    }
    createDOM(config) {
        const element = document.createElement('hr');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addClassNamesToElement"])(element, config.theme.hr);
        return element;
    }
    getTextContent() {
        return '\n';
    }
    isInline() {
        return false;
    }
    updateDOM() {
        return false;
    }
    decorate() {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(HorizontalRuleComponent, {
            nodeKey: this.__key
        });
    }
}
function $convertHorizontalRuleElement() {
    return {
        node: $createHorizontalRuleNode()
    };
}
function $createHorizontalRuleNode() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$applyNodeReplacement"])(new HorizontalRuleNode());
}
function $isHorizontalRuleNode(node) {
    return node instanceof HorizontalRuleNode;
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "HorizontalRulePlugin": (()=>HorizontalRulePlugin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalHorizontalRuleNode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function HorizontalRulePlugin() {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HorizontalRulePlugin.useEffect": ()=>{
            return editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalHorizontalRuleNode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_HORIZONTAL_RULE_COMMAND"], {
                "HorizontalRulePlugin.useEffect": (type)=>{
                    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
                    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
                        return false;
                    }
                    const focusNode = selection.focus.getNode();
                    if (focusNode !== null) {
                        const horizontalRuleNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalHorizontalRuleNode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createHorizontalRuleNode"])();
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["$insertNodeToNearestRoot"])(horizontalRuleNode);
                    }
                    return true;
                }
            }["HorizontalRulePlugin.useEffect"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_EDITOR"]);
        }
    }["HorizontalRulePlugin.useEffect"], [
        editor
    ]);
    return null;
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "DEFAULT_TRANSFORMERS": (()=>DEFAULT_TRANSFORMERS),
    "MarkdownShortcutPlugin": (()=>MarkdownShortcutPlugin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$markdown$2f$LexicalMarkdown$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalHorizontalRuleNode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const HR = {
    dependencies: [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalHorizontalRuleNode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HorizontalRuleNode"]
    ],
    export: (node)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalHorizontalRuleNode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isHorizontalRuleNode"])(node) ? '***' : null;
    },
    regExp: /^(---|\*\*\*|___)\s?$/,
    replace: (parentNode, _1, _2, isImport)=>{
        const line = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalHorizontalRuleNode$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createHorizontalRuleNode"])();
        // TODO: Get rid of isImport flag
        if (isImport || parentNode.getNextSibling() != null) {
            parentNode.replace(line);
        } else {
            parentNode.insertBefore(line);
        }
        line.selectNext();
    },
    type: 'element'
};
const DEFAULT_TRANSFORMERS = [
    HR,
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$markdown$2f$LexicalMarkdown$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSFORMERS"]
];
function MarkdownShortcutPlugin({ transformers = DEFAULT_TRANSFORMERS }) {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkdownShortcutPlugin.useEffect": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$markdown$2f$LexicalMarkdown$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["registerMarkdownShortcuts"])(editor, transformers);
        }
    }["MarkdownShortcutPlugin.useEffect"], [
        editor,
        transformers
    ]);
    return null;
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "LinkPlugin": (()=>LinkPlugin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/link/LexicalLink.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ function LinkPlugin({ validateUrl, attributes }) {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LinkPlugin.useEffect": ()=>{
            if (!editor.hasNodes([
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LinkNode"]
            ])) {
                throw new Error('LinkPlugin: LinkNode not registered on editor');
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])(editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOGGLE_LINK_COMMAND"], {
                "LinkPlugin.useEffect": (payload)=>{
                    if (payload === null) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$toggleLink"])(payload);
                        return true;
                    } else if (typeof payload === 'string') {
                        if (validateUrl === undefined || validateUrl(payload)) {
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$toggleLink"])(payload, attributes);
                            return true;
                        }
                        return false;
                    } else {
                        const { url, target, rel, title } = payload;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$toggleLink"])(url, {
                            ...attributes,
                            rel,
                            target,
                            title
                        });
                        return true;
                    }
                }
            }["LinkPlugin.useEffect"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]), validateUrl !== undefined ? editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PASTE_COMMAND"], {
                "LinkPlugin.useEffect": (event)=>{
                    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
                    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection) || selection.isCollapsed() || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["objectKlassEquals"])(event, ClipboardEvent)) {
                        return false;
                    }
                    if (event.clipboardData === null) {
                        return false;
                    }
                    const clipboardText = event.clipboardData.getData('text');
                    if (!validateUrl(clipboardText)) {
                        return false;
                    }
                    // If we select nodes that are elements then avoid applying the link.
                    if (!selection.getNodes().some({
                        "LinkPlugin.useEffect": (node)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(node)
                    }["LinkPlugin.useEffect"])) {
                        editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOGGLE_LINK_COMMAND"], {
                            ...attributes,
                            url: clipboardText
                        });
                        event.preventDefault();
                        return true;
                    }
                    return false;
                }
            }["LinkPlugin.useEffect"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]) : ({
                "LinkPlugin.useEffect": ()=>{
                // Don't paste arbitrary text as a link when there's no validate function
                }
            })["LinkPlugin.useEffect"]);
        }
    }["LinkPlugin.useEffect"], [
        editor,
        validateUrl,
        attributes
    ]);
    return null;
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalAutoLinkPlugin.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "AutoLinkPlugin": (()=>AutoLinkPlugin),
    "createLinkMatcherWithRegExp": (()=>createLinkMatcherWithRegExp)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/link/LexicalLink.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/utils/LexicalUtils.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Do not require this module directly! Use normal `invariant` calls.
function formatDevErrorMessage(message) {
    throw new Error(message);
}
function createLinkMatcherWithRegExp(regExp, urlTransformer = (text)=>text) {
    return (text)=>{
        const match = regExp.exec(text);
        if (match === null) {
            return null;
        }
        return {
            index: match.index,
            length: match[0].length,
            text: match[0],
            url: urlTransformer(match[0])
        };
    };
}
function findFirstMatch(text, matchers) {
    for(let i = 0; i < matchers.length; i++){
        const match = matchers[i](text);
        if (match) {
            return match;
        }
    }
    return null;
}
const PUNCTUATION_OR_SPACE = /[.,;\s]/;
function isSeparator(char) {
    return PUNCTUATION_OR_SPACE.test(char);
}
function endsWithSeparator(textContent) {
    return isSeparator(textContent[textContent.length - 1]);
}
function startsWithSeparator(textContent) {
    return isSeparator(textContent[0]);
}
/**
 * Check if the text content starts with a fullstop followed by a top-level domain.
 * Meaning if the text content can be a beginning of a top level domain.
 * @param textContent
 * @param isEmail
 * @returns boolean
 */ function startsWithTLD(textContent, isEmail) {
    if (isEmail) {
        return /^\.[a-zA-Z]{2,}/.test(textContent);
    } else {
        return /^\.[a-zA-Z0-9]{1,}/.test(textContent);
    }
}
function isPreviousNodeValid(node) {
    let previousNode = node.getPreviousSibling();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(previousNode)) {
        previousNode = previousNode.getLastDescendant();
    }
    return previousNode === null || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(previousNode) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(previousNode) && endsWithSeparator(previousNode.getTextContent());
}
function isNextNodeValid(node) {
    let nextNode = node.getNextSibling();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isElementNode"])(nextNode)) {
        nextNode = nextNode.getFirstDescendant();
    }
    return nextNode === null || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLineBreakNode"])(nextNode) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(nextNode) && startsWithSeparator(nextNode.getTextContent());
}
function isContentAroundIsValid(matchStart, matchEnd, text, nodes) {
    const contentBeforeIsValid = matchStart > 0 ? isSeparator(text[matchStart - 1]) : isPreviousNodeValid(nodes[0]);
    if (!contentBeforeIsValid) {
        return false;
    }
    const contentAfterIsValid = matchEnd < text.length ? isSeparator(text[matchEnd]) : isNextNodeValid(nodes[nodes.length - 1]);
    return contentAfterIsValid;
}
function extractMatchingNodes(nodes, startIndex, endIndex) {
    const unmodifiedBeforeNodes = [];
    const matchingNodes = [];
    const unmodifiedAfterNodes = [];
    let matchingOffset = 0;
    let currentOffset = 0;
    const currentNodes = [
        ...nodes
    ];
    while(currentNodes.length > 0){
        const currentNode = currentNodes[0];
        const currentNodeText = currentNode.getTextContent();
        const currentNodeLength = currentNodeText.length;
        const currentNodeStart = currentOffset;
        const currentNodeEnd = currentOffset + currentNodeLength;
        if (currentNodeEnd <= startIndex) {
            unmodifiedBeforeNodes.push(currentNode);
            matchingOffset += currentNodeLength;
        } else if (currentNodeStart >= endIndex) {
            unmodifiedAfterNodes.push(currentNode);
        } else {
            matchingNodes.push(currentNode);
        }
        currentOffset += currentNodeLength;
        currentNodes.shift();
    }
    return [
        matchingOffset,
        unmodifiedBeforeNodes,
        matchingNodes,
        unmodifiedAfterNodes
    ];
}
function $createAutoLinkNode_(nodes, startIndex, endIndex, match) {
    const linkNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createAutoLinkNode"])(match.url, match.attributes);
    if (nodes.length === 1) {
        let remainingTextNode = nodes[0];
        let linkTextNode;
        if (startIndex === 0) {
            [linkTextNode, remainingTextNode] = remainingTextNode.splitText(endIndex);
        } else {
            [, linkTextNode, remainingTextNode] = remainingTextNode.splitText(startIndex, endIndex);
        }
        const textNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(match.text);
        textNode.setFormat(linkTextNode.getFormat());
        textNode.setDetail(linkTextNode.getDetail());
        textNode.setStyle(linkTextNode.getStyle());
        linkNode.append(textNode);
        linkTextNode.replace(linkNode);
        return remainingTextNode;
    } else if (nodes.length > 1) {
        const firstTextNode = nodes[0];
        let offset = firstTextNode.getTextContent().length;
        let firstLinkTextNode;
        if (startIndex === 0) {
            firstLinkTextNode = firstTextNode;
        } else {
            [, firstLinkTextNode] = firstTextNode.splitText(startIndex);
        }
        const linkNodes = [];
        let remainingTextNode;
        for(let i = 1; i < nodes.length; i++){
            const currentNode = nodes[i];
            const currentNodeText = currentNode.getTextContent();
            const currentNodeLength = currentNodeText.length;
            const currentNodeStart = offset;
            const currentNodeEnd = offset + currentNodeLength;
            if (currentNodeStart < endIndex) {
                if (currentNodeEnd <= endIndex) {
                    linkNodes.push(currentNode);
                } else {
                    const [linkTextNode, endNode] = currentNode.splitText(endIndex - currentNodeStart);
                    linkNodes.push(linkTextNode);
                    remainingTextNode = endNode;
                }
            }
            offset += currentNodeLength;
        }
        const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
        const selectedTextNode = selection ? selection.getNodes().find(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"]) : undefined;
        const textNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(firstLinkTextNode.getTextContent());
        textNode.setFormat(firstLinkTextNode.getFormat());
        textNode.setDetail(firstLinkTextNode.getDetail());
        textNode.setStyle(firstLinkTextNode.getStyle());
        linkNode.append(textNode, ...linkNodes);
        // it does not preserve caret position if caret was at the first text node
        // so we need to restore caret position
        if (selectedTextNode && selectedTextNode === firstLinkTextNode) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
                textNode.select(selection.anchor.offset, selection.focus.offset);
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isNodeSelection"])(selection)) {
                textNode.select(0, textNode.getTextContent().length);
            }
        }
        firstLinkTextNode.replace(linkNode);
        return remainingTextNode;
    }
    return undefined;
}
function $handleLinkCreation(nodes, matchers, onChange) {
    let currentNodes = [
        ...nodes
    ];
    const initialText = currentNodes.map((node)=>node.getTextContent()).join('');
    let text = initialText;
    let match;
    let invalidMatchEnd = 0;
    while((match = findFirstMatch(text, matchers)) && match !== null){
        const matchStart = match.index;
        const matchLength = match.length;
        const matchEnd = matchStart + matchLength;
        const isValid = isContentAroundIsValid(invalidMatchEnd + matchStart, invalidMatchEnd + matchEnd, initialText, currentNodes);
        if (isValid) {
            const [matchingOffset, , matchingNodes, unmodifiedAfterNodes] = extractMatchingNodes(currentNodes, invalidMatchEnd + matchStart, invalidMatchEnd + matchEnd);
            const actualMatchStart = invalidMatchEnd + matchStart - matchingOffset;
            const actualMatchEnd = invalidMatchEnd + matchEnd - matchingOffset;
            const remainingTextNode = $createAutoLinkNode_(matchingNodes, actualMatchStart, actualMatchEnd, match);
            currentNodes = remainingTextNode ? [
                remainingTextNode,
                ...unmodifiedAfterNodes
            ] : unmodifiedAfterNodes;
            onChange(match.url, null);
            invalidMatchEnd = 0;
        } else {
            invalidMatchEnd += matchEnd;
        }
        text = text.substring(matchEnd);
    }
}
function handleLinkEdit(linkNode, matchers, onChange) {
    // Check children are simple text
    const children = linkNode.getChildren();
    const childrenLength = children.length;
    for(let i = 0; i < childrenLength; i++){
        const child = children[i];
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(child) || !child.isSimpleText()) {
            replaceWithChildren(linkNode);
            onChange(null, linkNode.getURL());
            return;
        }
    }
    // Check text content fully matches
    const text = linkNode.getTextContent();
    const match = findFirstMatch(text, matchers);
    if (match === null || match.text !== text) {
        replaceWithChildren(linkNode);
        onChange(null, linkNode.getURL());
        return;
    }
    // Check neighbors
    if (!isPreviousNodeValid(linkNode) || !isNextNodeValid(linkNode)) {
        replaceWithChildren(linkNode);
        onChange(null, linkNode.getURL());
        return;
    }
    const url = linkNode.getURL();
    if (url !== match.url) {
        linkNode.setURL(match.url);
        onChange(match.url, url);
    }
    if (match.attributes) {
        const rel = linkNode.getRel();
        if (rel !== match.attributes.rel) {
            linkNode.setRel(match.attributes.rel || null);
            onChange(match.attributes.rel || null, rel);
        }
        const target = linkNode.getTarget();
        if (target !== match.attributes.target) {
            linkNode.setTarget(match.attributes.target || null);
            onChange(match.attributes.target || null, target);
        }
    }
}
// Bad neighbors are edits in neighbor nodes that make AutoLinks incompatible.
// Given the creation preconditions, these can only be simple text nodes.
function handleBadNeighbors(textNode, matchers, onChange) {
    const previousSibling = textNode.getPreviousSibling();
    const nextSibling = textNode.getNextSibling();
    const text = textNode.getTextContent();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isAutoLinkNode"])(previousSibling) && !previousSibling.getIsUnlinked() && (!startsWithSeparator(text) || startsWithTLD(text, previousSibling.isEmailURI()))) {
        previousSibling.append(textNode);
        handleLinkEdit(previousSibling, matchers, onChange);
        onChange(null, previousSibling.getURL());
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isAutoLinkNode"])(nextSibling) && !nextSibling.getIsUnlinked() && !endsWithSeparator(text)) {
        replaceWithChildren(nextSibling);
        handleLinkEdit(nextSibling, matchers, onChange);
        onChange(null, nextSibling.getURL());
    }
}
function replaceWithChildren(node) {
    const children = node.getChildren();
    const childrenLength = children.length;
    for(let j = childrenLength - 1; j >= 0; j--){
        node.insertAfter(children[j]);
    }
    node.remove();
    return children.map((child)=>child.getLatest());
}
function getTextNodesToMatch(textNode) {
    // check if next siblings are simple text nodes till a node contains a space separator
    const textNodesToMatch = [
        textNode
    ];
    let nextSibling = textNode.getNextSibling();
    while(nextSibling !== null && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isTextNode"])(nextSibling) && nextSibling.isSimpleText()){
        textNodesToMatch.push(nextSibling);
        if (/[\s]/.test(nextSibling.getTextContent())) {
            break;
        }
        nextSibling = nextSibling.getNextSibling();
    }
    return textNodesToMatch;
}
function useAutoLink(editor, matchers, onChange) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAutoLink.useEffect": ()=>{
            if (!editor.hasNodes([
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AutoLinkNode"]
            ])) {
                {
                    formatDevErrorMessage(`LexicalAutoLinkPlugin: AutoLinkNode not registered on editor`);
                }
            }
            const onChangeWrapped = {
                "useAutoLink.useEffect.onChangeWrapped": (url, prevUrl)=>{
                    if (onChange) {
                        onChange(url, prevUrl);
                    }
                }
            }["useAutoLink.useEffect.onChangeWrapped"];
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$utils$2f$LexicalUtils$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeRegister"])(editor.registerNodeTransform(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextNode"], {
                "useAutoLink.useEffect": (textNode)=>{
                    const parent = textNode.getParentOrThrow();
                    const previous = textNode.getPreviousSibling();
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isAutoLinkNode"])(parent) && !parent.getIsUnlinked()) {
                        handleLinkEdit(parent, matchers, onChangeWrapped);
                    } else if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isLinkNode"])(parent)) {
                        if (textNode.isSimpleText() && (startsWithSeparator(textNode.getTextContent()) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isAutoLinkNode"])(previous))) {
                            const textNodesToMatch = getTextNodesToMatch(textNode);
                            $handleLinkCreation(textNodesToMatch, matchers, onChangeWrapped);
                        }
                        handleBadNeighbors(textNode, matchers, onChangeWrapped);
                    }
                }
            }["useAutoLink.useEffect"]), editor.registerCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOGGLE_LINK_COMMAND"], {
                "useAutoLink.useEffect": (payload)=>{
                    const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
                    if (payload !== null || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
                        return false;
                    }
                    const nodes = selection.extract();
                    nodes.forEach({
                        "useAutoLink.useEffect": (node)=>{
                            const parent = node.getParent();
                            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isAutoLinkNode"])(parent)) {
                                // invert the value
                                parent.setIsUnlinked(!parent.getIsUnlinked());
                                parent.markDirty();
                            }
                        }
                    }["useAutoLink.useEffect"]);
                    return false;
                }
            }["useAutoLink.useEffect"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COMMAND_PRIORITY_LOW"]));
        }
    }["useAutoLink.useEffect"], [
        editor,
        matchers,
        onChange
    ]);
}
function AutoLinkPlugin({ matchers, onChange }) {
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    useAutoLink(editor, matchers, onChange);
    return null;
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalCollaborationContext.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "CollaborationContext": (()=>CollaborationContext),
    "useCollaborationContext": (()=>useCollaborationContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ const entries = [
    [
        'Cat',
        'rgb(125, 50, 0)'
    ],
    [
        'Dog',
        'rgb(100, 0, 0)'
    ],
    [
        'Rabbit',
        'rgb(150, 0, 0)'
    ],
    [
        'Frog',
        'rgb(200, 0, 0)'
    ],
    [
        'Fox',
        'rgb(200, 75, 0)'
    ],
    [
        'Hedgehog',
        'rgb(0, 75, 0)'
    ],
    [
        'Pigeon',
        'rgb(0, 125, 0)'
    ],
    [
        'Squirrel',
        'rgb(75, 100, 0)'
    ],
    [
        'Bear',
        'rgb(125, 100, 0)'
    ],
    [
        'Tiger',
        'rgb(0, 0, 150)'
    ],
    [
        'Leopard',
        'rgb(0, 0, 200)'
    ],
    [
        'Zebra',
        'rgb(0, 0, 250)'
    ],
    [
        'Wolf',
        'rgb(0, 100, 150)'
    ],
    [
        'Owl',
        'rgb(0, 100, 100)'
    ],
    [
        'Gull',
        'rgb(100, 0, 100)'
    ],
    [
        'Squid',
        'rgb(150, 0, 150)'
    ]
];
const randomEntry = entries[Math.floor(Math.random() * entries.length)];
const CollaborationContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    clientID: 0,
    color: randomEntry[1],
    isCollabActive: false,
    name: randomEntry[0],
    yjsDocMap: new Map()
});
function useCollaborationContext(username, color) {
    const collabContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(CollaborationContext);
    if (username != null) {
        collabContext.name = username;
    }
    if (color != null) {
        collabContext.color = color;
    }
    return collabContext;
}
;
}}),
"[project]/node_modules/@lexical/react/LexicalNestedComposer.dev.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ __turbopack_context__.s({
    "LexicalNestedComposer": (()=>LexicalNestedComposer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalCollaborationContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalCollaborationContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
;
;
;
;
;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ // Do not require this module directly! Use normal `invariant` calls.
function formatDevErrorMessage(message) {
    throw new Error(message);
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */ /*@__INLINE__*/ function warnOnlyOnce(message) {
    {
        let run = false;
        return ()=>{
            if (!run) {
                console.warn(message);
            }
            run = true;
        };
    }
}
function getTransformSetFromKlass(klass) {
    const transforms = new Set();
    const { ownNodeConfig } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStaticNodeConfig"])(klass);
    const transform = klass.transform();
    if (ownNodeConfig) {
        const $transform = ownNodeConfig.$transform;
        if ($transform) {
            transforms.add($transform);
        }
    }
    if (transform) {
        transforms.add(transform);
    }
    return transforms;
}
const initialNodesWarning = warnOnlyOnce(`LexicalNestedComposer initialNodes is deprecated and will be removed in v0.32.0, it has never worked correctly.\nYou can configure your editor's nodes with createEditor({nodes: [], parentEditor: $getEditor()})`);
const explicitNamespaceWarning = warnOnlyOnce(`LexicalNestedComposer initialEditor should explicitly initialize its namespace when the node configuration differs from the parentEditor. For backwards compatibility, the namespace will be initialized from parentEditor until v0.32.0, but this has always had incorrect copy/paste behavior when the configuration differed.\nYou can configure your editor's namespace with createEditor({namespace: 'nested-editor-namespace', nodes: [], parentEditor: $getEditor()}).`);
function LexicalNestedComposer({ initialEditor, children, initialNodes, initialTheme, skipCollabChecks, skipEditableListener }) {
    const wasCollabPreviouslyReadyRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const parentContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LexicalComposerContext"]);
    if (parentContext == null) {
        {
            formatDevErrorMessage(`Unexpected parent context null on a nested composer`);
        }
    }
    const [parentEditor, { getTheme: getParentTheme }] = parentContext;
    const composerContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "LexicalNestedComposer.useMemo[composerContext]": ()=>{
            const composerTheme = initialTheme || getParentTheme() || undefined;
            const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createLexicalComposerContext"])(parentContext, composerTheme);
            if (composerTheme !== undefined) {
                initialEditor._config.theme = composerTheme;
            }
            initialEditor._parentEditor = initialEditor._parentEditor || parentEditor;
            const createEditorArgs = initialEditor._createEditorArgs;
            const explicitNamespace = createEditorArgs && createEditorArgs.namespace;
            if (!initialNodes) {
                if (!(createEditorArgs && createEditorArgs.nodes)) {
                    const parentNodes = initialEditor._nodes = new Map(parentEditor._nodes);
                    if (!explicitNamespace) {
                        // This is the only safe situation to inherit the parent's namespace
                        initialEditor._config.namespace = parentEditor._config.namespace;
                    }
                    for (const [type, entry] of parentNodes){
                        initialEditor._nodes.set(type, {
                            exportDOM: entry.exportDOM,
                            klass: entry.klass,
                            replace: entry.replace,
                            replaceWithKlass: entry.replaceWithKlass,
                            sharedNodeState: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createSharedNodeState"])(entry.klass),
                            transforms: getTransformSetFromKlass(entry.klass)
                        });
                    }
                } else if (!explicitNamespace) {
                    explicitNamespaceWarning();
                    initialEditor._config.namespace = parentEditor._config.namespace;
                }
            } else {
                initialNodesWarning();
                if (!explicitNamespace) {
                    explicitNamespaceWarning();
                    initialEditor._config.namespace = parentEditor._config.namespace;
                }
                for (let klass of initialNodes){
                    let replace = null;
                    let replaceWithKlass = null;
                    if (typeof klass !== 'function') {
                        const options = klass;
                        klass = options.replace;
                        replace = options.with;
                        replaceWithKlass = options.withKlass || null;
                    }
                    const registeredKlass = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRegisteredNode"])(initialEditor, klass.getType());
                    initialEditor._nodes.set(klass.getType(), {
                        exportDOM: registeredKlass ? registeredKlass.exportDOM : undefined,
                        klass,
                        replace,
                        replaceWithKlass,
                        sharedNodeState: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createSharedNodeState"])(klass),
                        transforms: getTransformSetFromKlass(klass)
                    });
                }
            }
            return [
                initialEditor,
                context
            ];
        }
    }["LexicalNestedComposer.useMemo[composerContext]"], // We only do this for init
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []);
    // If collaboration is enabled, make sure we don't render the children until the collaboration subdocument is ready.
    const { isCollabActive, yjsDocMap } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalCollaborationContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCollaborationContext"])();
    const isCollabReady = skipCollabChecks || wasCollabPreviouslyReadyRef.current || yjsDocMap.has(initialEditor.getKey());
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LexicalNestedComposer.useEffect": ()=>{
            if (isCollabReady) {
                wasCollabPreviouslyReadyRef.current = true;
            }
        }
    }["LexicalNestedComposer.useEffect"], [
        isCollabReady
    ]);
    // Update `isEditable` state of nested editor in response to the same change on parent editor.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LexicalNestedComposer.useEffect": ()=>{
            if (!skipEditableListener) {
                const editableListener = {
                    "LexicalNestedComposer.useEffect.editableListener": (editable)=>initialEditor.setEditable(editable)
                }["LexicalNestedComposer.useEffect.editableListener"];
                editableListener(parentEditor.isEditable());
                return parentEditor.registerEditableListener(editableListener);
            }
        }
    }["LexicalNestedComposer.useEffect"], [
        initialEditor,
        parentEditor,
        skipEditableListener
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LexicalComposerContext"].Provider, {
        value: composerContext,
        children: !isCollabActive || isCollabReady ? children : null
    });
}
;
}}),
}]);

//# sourceMappingURL=node_modules_%40lexical_react_f739193c._.js.map