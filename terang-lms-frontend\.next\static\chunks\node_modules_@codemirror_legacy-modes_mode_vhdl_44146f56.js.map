{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/vhdl.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\",\");\n  for (var i = 0; i < words.length; ++i) {\n    var allCaps = words[i].toUpperCase();\n    var firstCap = words[i].charAt(0).toUpperCase() + words[i].slice(1);\n    obj[words[i]] = true;\n    obj[allCaps] = true;\n    obj[firstCap] = true;\n  }\n  return obj;\n}\n\nfunction metaHook(stream) {\n  stream.eatWhile(/[\\w\\$_]/);\n  return \"meta\";\n}\n\nvar atoms = words(\"null\"),\n    hooks = {\"`\": metaHook, \"$\": metaHook},\n    multiLineStrings = false;\n\nvar keywords = words(\"abs,access,after,alias,all,and,architecture,array,assert,attribute,begin,block,\" +\n                     \"body,buffer,bus,case,component,configuration,constant,disconnect,downto,else,elsif,end,end block,end case,\" +\n                     \"end component,end for,end generate,end if,end loop,end process,end record,end units,entity,exit,file,for,\" +\n                     \"function,generate,generic,generic map,group,guarded,if,impure,in,inertial,inout,is,label,library,linkage,\" +\n                     \"literal,loop,map,mod,nand,new,next,nor,null,of,on,open,or,others,out,package,package body,port,port map,\" +\n                     \"postponed,procedure,process,pure,range,record,register,reject,rem,report,return,rol,ror,select,severity,signal,\" +\n                     \"sla,sll,sra,srl,subtype,then,to,transport,type,unaffected,units,until,use,variable,wait,when,while,with,xnor,xor\");\n\nvar blockKeywords = words(\"architecture,entity,begin,case,port,else,elsif,end,for,function,if\");\n\nvar isOperatorChar = /[&|~><!\\)\\(*#%@+\\/=?\\:;}{,\\.\\^\\-\\[\\]]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (hooks[ch]) {\n    var result = hooks[ch](stream, state);\n    if (result !== false) return result;\n  }\n  if (ch == '\"') {\n    state.tokenize = tokenString2(ch);\n    return state.tokenize(stream, state);\n  }\n  if (ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  }\n  if (/[\\d']/.test(ch)) {\n    stream.eatWhile(/[\\w\\.']/);\n    return \"number\";\n  }\n  if (ch == \"-\") {\n    if (stream.eat(\"-\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_]/);\n  var cur = stream.current();\n  if (keywords.propertyIsEnumerable(cur.toLowerCase())) {\n    if (blockKeywords.propertyIsEnumerable(cur)) curPunc = \"newstatement\";\n    return \"keyword\";\n  }\n  if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {end = true; break;}\n      escaped = !escaped && next == \"--\";\n    }\n    if (end || !(escaped || multiLineStrings))\n      state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\nfunction tokenString2(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {end = true; break;}\n      escaped = !escaped && next == \"--\";\n    }\n    if (end || !(escaped || multiLineStrings))\n      state.tokenize = tokenBase;\n    return \"string.special\";\n  };\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type) {\n  return state.context = new Context(state.indented, col, type, null, state.context);\n}\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n// Interface\nexport const vhdl = {\n  name: \"vhdl\",\n  startState: function(indentUnit) {\n    return {\n      tokenize: null,\n      context: new Context(-indentUnit, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\" || style == \"meta\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\") && ctx.type == \"statement\") popContext(state);\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (ctx.type == \"}\" || ctx.type == \"top\" || (ctx.type == \"statement\" && curPunc == \"newstatement\"))\n      pushContext(state, stream.column(), \"statement\");\n    state.startOfLine = false;\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase && state.tokenize != null) return 0;\n    var firstChar = textAfter && textAfter.charAt(0), ctx = state.context, closing = firstChar == ctx.type;\n    if (ctx.type == \"statement\") return ctx.indented + (firstChar == \"{\" ? 0 : cx.unit);\n    else if (ctx.align) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indented + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"--\"}\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;QACrC,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC,WAAW;QAClC,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QACjE,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;QAChB,GAAG,CAAC,QAAQ,GAAG;QACf,GAAG,CAAC,SAAS,GAAG;IAClB;IACA,OAAO;AACT;AAEA,SAAS,SAAS,MAAM;IACtB,OAAO,QAAQ,CAAC;IAChB,OAAO;AACT;AAEA,IAAI,QAAQ,MAAM,SACd,QAAQ;IAAC,KAAK;IAAU,KAAK;AAAQ,GACrC,mBAAmB;AAEvB,IAAI,WAAW,MAAM,oFACA,+GACA,8GACA,8GACA,6GACA,oHACA;AAErB,IAAI,gBAAgB,MAAM;AAE1B,IAAI,iBAAiB;AACrB,IAAI;AAEJ,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,KAAK,CAAC,GAAG,EAAE;QACb,IAAI,SAAS,KAAK,CAAC,GAAG,CAAC,QAAQ;QAC/B,IAAI,WAAW,OAAO,OAAO;IAC/B;IACA,IAAI,MAAM,KAAK;QACb,MAAM,QAAQ,GAAG,aAAa;QAC9B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IACA,IAAI,MAAM,KAAK;QACb,MAAM,QAAQ,GAAG,YAAY;QAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IACA,IAAI,qBAAqB,IAAI,CAAC,KAAK;QACjC,UAAU;QACV,OAAO;IACT;IACA,IAAI,QAAQ,IAAI,CAAC,KAAK;QACpB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,IAAI,MAAM,KAAK;QACb,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO,SAAS;YAChB,OAAO;QACT;IACF;IACA,IAAI,eAAe,IAAI,CAAC,KAAK;QAC3B,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,OAAO,QAAQ,CAAC;IAChB,IAAI,MAAM,OAAO,OAAO;IACxB,IAAI,SAAS,oBAAoB,CAAC,IAAI,WAAW,KAAK;QACpD,IAAI,cAAc,oBAAoB,CAAC,MAAM,UAAU;QACvD,OAAO;IACT;IACA,IAAI,MAAM,oBAAoB,CAAC,MAAM,OAAO;IAC5C,OAAO;AACT;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;QACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,QAAQ,SAAS,CAAC,SAAS;gBAAC,MAAM;gBAAM;YAAM;YAClD,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,OAAO,CAAC,CAAC,WAAW,gBAAgB,GACtC,MAAM,QAAQ,GAAG;QACnB,OAAO;IACT;AACF;AACA,SAAS,aAAa,KAAK;IACzB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;QACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,QAAQ,SAAS,CAAC,SAAS;gBAAC,MAAM;gBAAM;YAAM;YAClD,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,OAAO,CAAC,CAAC,WAAW,gBAAgB,GACtC,MAAM,QAAQ,GAAG;QACnB,OAAO;IACT;AACF;AAEA,SAAS,QAAQ,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;IAClD,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,IAAI,GAAG;AACd;AACA,SAAS,YAAY,KAAK,EAAE,GAAG,EAAE,IAAI;IACnC,OAAO,MAAM,OAAO,GAAG,IAAI,QAAQ,MAAM,QAAQ,EAAE,KAAK,MAAM,MAAM,MAAM,OAAO;AACnF;AACA,SAAS,WAAW,KAAK;IACvB,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI;IAC1B,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAC/B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ;IACzC,OAAO,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;AAC3C;AAGO,MAAM,OAAO;IAClB,MAAM;IACN,YAAY,SAAS,UAAU;QAC7B,OAAO;YACL,UAAU;YACV,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,OAAO;YAC5C,UAAU;YACV,aAAa;QACf;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,MAAM,MAAM,OAAO;QACvB,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;YACnC,MAAM,QAAQ,GAAG,OAAO,WAAW;YACnC,MAAM,WAAW,GAAG;QACtB;QACA,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,UAAU;QACV,IAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,SAAS,EAAE,QAAQ;QAClD,IAAI,SAAS,aAAa,SAAS,QAAQ,OAAO;QAClD,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;QAEnC,IAAI,CAAC,WAAW,OAAO,WAAW,GAAG,KAAK,IAAI,IAAI,IAAI,aAAa,WAAW;aACzE,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK;YACvB,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;YACjD,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM,WAAW;YACtC,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;QACnD,OACK,IAAI,WAAW,IAAI,IAAI,EAAE,WAAW;aACpC,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,SAAU,IAAI,IAAI,IAAI,eAAe,WAAW,gBACtF,YAAY,OAAO,OAAO,MAAM,IAAI;QACtC,MAAM,WAAW,GAAG;QACpB,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,MAAM,QAAQ,IAAI,aAAa,MAAM,QAAQ,IAAI,MAAM,OAAO;QAClE,IAAI,YAAY,aAAa,UAAU,MAAM,CAAC,IAAI,MAAM,MAAM,OAAO,EAAE,UAAU,aAAa,IAAI,IAAI;QACtG,IAAI,IAAI,IAAI,IAAI,aAAa,OAAO,IAAI,QAAQ,GAAG,CAAC,aAAa,MAAM,IAAI,GAAG,IAAI;aAC7E,IAAI,IAAI,KAAK,EAAE,OAAO,IAAI,MAAM,GAAG,CAAC,UAAU,IAAI,CAAC;aACnD,OAAO,IAAI,QAAQ,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI;IACnD;IAEA,cAAc;QACZ,eAAe;QACf,eAAe;YAAC,MAAM;QAAI;IAC5B;AACF", "ignoreList": [0], "debugId": null}}]}