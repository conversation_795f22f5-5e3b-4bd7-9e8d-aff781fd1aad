{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/r.js"], "sourcesContent": ["function wordObj(words) {\n  var res = {};\n  for (var i = 0; i < words.length; ++i) res[words[i]] = true;\n  return res;\n}\nvar commonAtoms = [\"NULL\", \"NA\", \"Inf\", \"NaN\", \"NA_integer_\", \"NA_real_\", \"NA_complex_\", \"NA_character_\", \"TRUE\", \"FALSE\"];\nvar commonBuiltins = [\"list\", \"quote\", \"bquote\", \"eval\", \"return\", \"call\", \"parse\", \"deparse\"];\nvar commonKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\", \"in\", \"next\", \"break\"];\nvar commonBlockKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\"];\n\nvar atoms = wordObj(commonAtoms);\nvar builtins = wordObj(commonBuiltins);\nvar keywords = wordObj(commonKeywords);\nvar blockkeywords = wordObj(commonBlockKeywords);\nvar opChars = /[+\\-*\\/^<>=!&|~$:]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  curPunc = null;\n  var ch = stream.next();\n  if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == \"0\" && stream.eat(\"x\")) {\n    stream.eatWhile(/[\\da-f]/i);\n    return \"number\";\n  } else if (ch == \".\" && stream.eat(/\\d/)) {\n    stream.match(/\\d*(?:e[+\\-]?\\d+)?/);\n    return \"number\";\n  } else if (/\\d/.test(ch)) {\n    stream.match(/\\d*(?:\\.\\d+)?(?:e[+\\-]\\d+)?L?/);\n    return \"number\";\n  } else if (ch == \"'\" || ch == '\"') {\n    state.tokenize = tokenString(ch);\n    return \"string\";\n  } else if (ch == \"`\") {\n    stream.match(/[^`]+`/);\n    return \"string.special\";\n  } else if (ch == \".\" && stream.match(/.(?:[.]|\\d+)/)) {\n    return \"keyword\";\n  } else if (/[a-zA-Z\\.]/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    var word = stream.current();\n    if (atoms.propertyIsEnumerable(word)) return \"atom\";\n    if (keywords.propertyIsEnumerable(word)) {\n      // Block keywords start new blocks, except 'else if', which only starts\n      // one new block for the 'if', no block for the 'else'.\n      if (blockkeywords.propertyIsEnumerable(word) &&\n          !stream.match(/\\s*if(\\s+|$)/, false))\n        curPunc = \"block\";\n      return \"keyword\";\n    }\n    if (builtins.propertyIsEnumerable(word)) return \"builtin\";\n    return \"variable\";\n  } else if (ch == \"%\") {\n    if (stream.skipTo(\"%\")) stream.next();\n    return \"variableName.special\";\n  } else if (\n    (ch == \"<\" && stream.eat(\"-\")) ||\n      (ch == \"<\" && stream.match(\"<-\")) ||\n      (ch == \"-\" && stream.match(/>>?/))\n  ) {\n    return \"operator\";\n  } else if (ch == \"=\" && state.ctx.argList) {\n    return \"operator\";\n  } else if (opChars.test(ch)) {\n    if (ch == \"$\") return \"operator\";\n    stream.eatWhile(opChars);\n    return \"operator\";\n  } else if (/[\\(\\){}\\[\\];]/.test(ch)) {\n    curPunc = ch;\n    if (ch == \";\") return \"punctuation\";\n    return null;\n  } else {\n    return null;\n  }\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    if (stream.eat(\"\\\\\")) {\n      var ch = stream.next();\n      if (ch == \"x\") stream.match(/^[a-f0-9]{2}/i);\n      else if ((ch == \"u\" || ch == \"U\") && stream.eat(\"{\") && stream.skipTo(\"}\")) stream.next();\n      else if (ch == \"u\") stream.match(/^[a-f0-9]{4}/i);\n      else if (ch == \"U\") stream.match(/^[a-f0-9]{8}/i);\n      else if (/[0-7]/.test(ch)) stream.match(/^[0-7]{1,2}/);\n      return \"string.special\";\n    } else {\n      var next;\n      while ((next = stream.next()) != null) {\n        if (next == quote) { state.tokenize = tokenBase; break; }\n        if (next == \"\\\\\") { stream.backUp(1); break; }\n      }\n      return \"string\";\n    }\n  };\n}\n\nvar ALIGN_YES = 1, ALIGN_NO = 2, BRACELESS = 4\n\nfunction push(state, type, stream) {\n  state.ctx = {type: type,\n               indent: state.indent,\n               flags: 0,\n               column: stream.column(),\n               prev: state.ctx};\n}\nfunction setFlag(state, flag) {\n  var ctx = state.ctx\n  state.ctx = {type: ctx.type,\n               indent: ctx.indent,\n               flags: ctx.flags | flag,\n               column: ctx.column,\n               prev: ctx.prev}\n}\nfunction pop(state) {\n  state.indent = state.ctx.indent;\n  state.ctx = state.ctx.prev;\n}\n\nexport const r = {\n  name: \"r\",\n  startState: function(indentUnit) {\n    return {tokenize: tokenBase,\n            ctx: {type: \"top\",\n                  indent: -indentUnit,\n                  flags: ALIGN_NO},\n            indent: 0,\n            afterIdent: false};\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if ((state.ctx.flags & 3) == 0) state.ctx.flags |= ALIGN_NO\n      if (state.ctx.flags & BRACELESS) pop(state)\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    if (style != \"comment\" && (state.ctx.flags & ALIGN_NO) == 0) setFlag(state, ALIGN_YES)\n\n    if ((curPunc == \";\" || curPunc == \"{\" || curPunc == \"}\") && state.ctx.type == \"block\") pop(state);\n    if (curPunc == \"{\") push(state, \"}\", stream);\n    else if (curPunc == \"(\") {\n      push(state, \")\", stream);\n      if (state.afterIdent) state.ctx.argList = true;\n    }\n    else if (curPunc == \"[\") push(state, \"]\", stream);\n    else if (curPunc == \"block\") push(state, \"block\", stream);\n    else if (curPunc == state.ctx.type) pop(state);\n    else if (state.ctx.type == \"block\" && style != \"comment\") setFlag(state, BRACELESS)\n    state.afterIdent = style == \"variable\" || style == \"keyword\";\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase) return 0;\n    var firstChar = textAfter && textAfter.charAt(0), ctx = state.ctx,\n        closing = firstChar == ctx.type;\n    if (ctx.flags & BRACELESS) ctx = ctx.prev\n    if (ctx.type == \"block\") return ctx.indent + (firstChar == \"{\" ? 0 : cx.unit);\n    else if (ctx.flags & ALIGN_YES) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indent + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    wordChars: \".\",\n    commentTokens: {line: \"#\"},\n    autocomplete: commonAtoms.concat(commonBuiltins, commonKeywords)\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,QAAQ,KAAK;IACpB,IAAI,MAAM,CAAC;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AACA,IAAI,cAAc;IAAC;IAAQ;IAAM;IAAO;IAAO;IAAe;IAAY;IAAe;IAAiB;IAAQ;CAAQ;AAC1H,IAAI,iBAAiB;IAAC;IAAQ;IAAS;IAAU;IAAQ;IAAU;IAAQ;IAAS;CAAU;AAC9F,IAAI,iBAAiB;IAAC;IAAM;IAAQ;IAAU;IAAS;IAAY;IAAO;IAAM;IAAQ;CAAQ;AAChG,IAAI,sBAAsB;IAAC;IAAM;IAAQ;IAAU;IAAS;IAAY;CAAM;AAE9E,IAAI,QAAQ,QAAQ;AACpB,IAAI,WAAW,QAAQ;AACvB,IAAI,WAAW,QAAQ;AACvB,IAAI,gBAAgB,QAAQ;AAC5B,IAAI,UAAU;AACd,IAAI;AAEJ,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,UAAU;IACV,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,KAAK;QACb,OAAO,SAAS;QAChB,OAAO;IACT,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;QACvC,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,OAAO;QACxC,OAAO,KAAK,CAAC;QACb,OAAO;IACT,OAAO,IAAI,KAAK,IAAI,CAAC,KAAK;QACxB,OAAO,KAAK,CAAC;QACb,OAAO;IACT,OAAO,IAAI,MAAM,OAAO,MAAM,KAAK;QACjC,MAAM,QAAQ,GAAG,YAAY;QAC7B,OAAO;IACT,OAAO,IAAI,MAAM,KAAK;QACpB,OAAO,KAAK,CAAC;QACb,OAAO;IACT,OAAO,IAAI,MAAM,OAAO,OAAO,KAAK,CAAC,iBAAiB;QACpD,OAAO;IACT,OAAO,IAAI,aAAa,IAAI,CAAC,KAAK;QAChC,OAAO,QAAQ,CAAC;QAChB,IAAI,OAAO,OAAO,OAAO;QACzB,IAAI,MAAM,oBAAoB,CAAC,OAAO,OAAO;QAC7C,IAAI,SAAS,oBAAoB,CAAC,OAAO;YACvC,uEAAuE;YACvE,uDAAuD;YACvD,IAAI,cAAc,oBAAoB,CAAC,SACnC,CAAC,OAAO,KAAK,CAAC,gBAAgB,QAChC,UAAU;YACZ,OAAO;QACT;QACA,IAAI,SAAS,oBAAoB,CAAC,OAAO,OAAO;QAChD,OAAO;IACT,OAAO,IAAI,MAAM,KAAK;QACpB,IAAI,OAAO,MAAM,CAAC,MAAM,OAAO,IAAI;QACnC,OAAO;IACT,OAAO,IACL,AAAC,MAAM,OAAO,OAAO,GAAG,CAAC,QACtB,MAAM,OAAO,OAAO,KAAK,CAAC,SAC1B,MAAM,OAAO,OAAO,KAAK,CAAC,QAC7B;QACA,OAAO;IACT,OAAO,IAAI,MAAM,OAAO,MAAM,GAAG,CAAC,OAAO,EAAE;QACzC,OAAO;IACT,OAAO,IAAI,QAAQ,IAAI,CAAC,KAAK;QAC3B,IAAI,MAAM,KAAK,OAAO;QACtB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OAAO,IAAI,gBAAgB,IAAI,CAAC,KAAK;QACnC,UAAU;QACV,IAAI,MAAM,KAAK,OAAO;QACtB,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,GAAG,CAAC,OAAO;YACpB,IAAI,KAAK,OAAO,IAAI;YACpB,IAAI,MAAM,KAAK,OAAO,KAAK,CAAC;iBACvB,IAAI,CAAC,MAAM,OAAO,MAAM,GAAG,KAAK,OAAO,GAAG,CAAC,QAAQ,OAAO,MAAM,CAAC,MAAM,OAAO,IAAI;iBAClF,IAAI,MAAM,KAAK,OAAO,KAAK,CAAC;iBAC5B,IAAI,MAAM,KAAK,OAAO,KAAK,CAAC;iBAC5B,IAAI,QAAQ,IAAI,CAAC,KAAK,OAAO,KAAK,CAAC;YACxC,OAAO;QACT,OAAO;YACL,IAAI;YACJ,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;gBACrC,IAAI,QAAQ,OAAO;oBAAE,MAAM,QAAQ,GAAG;oBAAW;gBAAO;gBACxD,IAAI,QAAQ,MAAM;oBAAE,OAAO,MAAM,CAAC;oBAAI;gBAAO;YAC/C;YACA,OAAO;QACT;IACF;AACF;AAEA,IAAI,YAAY,GAAG,WAAW,GAAG,YAAY;AAE7C,SAAS,KAAK,KAAK,EAAE,IAAI,EAAE,MAAM;IAC/B,MAAM,GAAG,GAAG;QAAC,MAAM;QACN,QAAQ,MAAM,MAAM;QACpB,OAAO;QACP,QAAQ,OAAO,MAAM;QACrB,MAAM,MAAM,GAAG;IAAA;AAC9B;AACA,SAAS,QAAQ,KAAK,EAAE,IAAI;IAC1B,IAAI,MAAM,MAAM,GAAG;IACnB,MAAM,GAAG,GAAG;QAAC,MAAM,IAAI,IAAI;QACd,QAAQ,IAAI,MAAM;QAClB,OAAO,IAAI,KAAK,GAAG;QACnB,QAAQ,IAAI,MAAM;QAClB,MAAM,IAAI,IAAI;IAAA;AAC7B;AACA,SAAS,IAAI,KAAK;IAChB,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,MAAM;IAC/B,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,IAAI;AAC5B;AAEO,MAAM,IAAI;IACf,MAAM;IACN,YAAY,SAAS,UAAU;QAC7B,OAAO;YAAC,UAAU;YACV,KAAK;gBAAC,MAAM;gBACN,QAAQ,CAAC;gBACT,OAAO;YAAQ;YACrB,QAAQ;YACR,YAAY;QAAK;IAC3B;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,KAAK,IAAI;YACnD,IAAI,MAAM,GAAG,CAAC,KAAK,GAAG,WAAW,IAAI;YACrC,MAAM,MAAM,GAAG,OAAO,WAAW;QACnC;QACA,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QACnC,IAAI,SAAS,aAAa,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,QAAQ,KAAK,GAAG,QAAQ,OAAO;QAE5E,IAAI,CAAC,WAAW,OAAO,WAAW,OAAO,WAAW,GAAG,KAAK,MAAM,GAAG,CAAC,IAAI,IAAI,SAAS,IAAI;QAC3F,IAAI,WAAW,KAAK,KAAK,OAAO,KAAK;aAChC,IAAI,WAAW,KAAK;YACvB,KAAK,OAAO,KAAK;YACjB,IAAI,MAAM,UAAU,EAAE,MAAM,GAAG,CAAC,OAAO,GAAG;QAC5C,OACK,IAAI,WAAW,KAAK,KAAK,OAAO,KAAK;aACrC,IAAI,WAAW,SAAS,KAAK,OAAO,SAAS;aAC7C,IAAI,WAAW,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI;aACnC,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,WAAW,SAAS,WAAW,QAAQ,OAAO;QACzE,MAAM,UAAU,GAAG,SAAS,cAAc,SAAS;QACnD,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,MAAM,QAAQ,IAAI,WAAW,OAAO;QACxC,IAAI,YAAY,aAAa,UAAU,MAAM,CAAC,IAAI,MAAM,MAAM,GAAG,EAC7D,UAAU,aAAa,IAAI,IAAI;QACnC,IAAI,IAAI,KAAK,GAAG,WAAW,MAAM,IAAI,IAAI;QACzC,IAAI,IAAI,IAAI,IAAI,SAAS,OAAO,IAAI,MAAM,GAAG,CAAC,aAAa,MAAM,IAAI,GAAG,IAAI;aACvE,IAAI,IAAI,KAAK,GAAG,WAAW,OAAO,IAAI,MAAM,GAAG,CAAC,UAAU,IAAI,CAAC;aAC/D,OAAO,IAAI,MAAM,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI;IACjD;IAEA,cAAc;QACZ,WAAW;QACX,eAAe;YAAC,MAAM;QAAG;QACzB,cAAc,YAAY,MAAM,CAAC,gBAAgB;IACnD;AACF", "ignoreList": [0], "debugId": null}}]}