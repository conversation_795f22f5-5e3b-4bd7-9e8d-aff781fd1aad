{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/mscgen.js"], "sourcesContent": ["function mkParser(lang) {\n  return {\n    name: \"mscgen\",\n    startState: startStateFn,\n    copyState: copyStateFn,\n    token: produceTokenFunction(lang),\n    languageData: {\n      commentTokens: {line: \"#\", block: {open: \"/*\", close: \"*/\"}}\n    }\n  }\n}\n\nexport const mscgen = mkParser({\n  \"keywords\" : [\"msc\"],\n  \"options\" : [\"hscale\", \"width\", \"arcgradient\", \"wordwraparcs\"],\n  \"constants\" : [\"true\", \"false\", \"on\", \"off\"],\n  \"attributes\" : [\"label\", \"idurl\", \"id\", \"url\", \"linecolor\", \"linecolour\", \"textcolor\", \"textcolour\", \"textbgcolor\", \"textbgcolour\", \"arclinecolor\", \"arclinecolour\", \"arctextcolor\", \"arctextcolour\", \"arctextbgcolor\", \"arctextbgcolour\", \"arcskip\"],\n  \"brackets\" : [\"\\\\{\", \"\\\\}\"], // [ and  ] are brackets too, but these get handled in with lists\n  \"arcsWords\" : [\"note\", \"abox\", \"rbox\", \"box\"],\n  \"arcsOthers\" : [\"\\\\|\\\\|\\\\|\", \"\\\\.\\\\.\\\\.\", \"---\", \"--\", \"<->\", \"==\", \"<<=>>\", \"<=>\", \"\\\\.\\\\.\", \"<<>>\", \"::\", \"<:>\", \"->\", \"=>>\", \"=>\", \">>\", \":>\", \"<-\", \"<<=\", \"<=\", \"<<\", \"<:\", \"x-\", \"-x\"],\n  \"singlecomment\" : [\"//\", \"#\"],\n  \"operators\" : [\"=\"]\n})\n\nexport const msgenny = mkParser({\n  \"keywords\" : null,\n  \"options\" : [\"hscale\", \"width\", \"arcgradient\", \"wordwraparcs\", \"wordwrapentities\", \"watermark\"],\n  \"constants\" : [\"true\", \"false\", \"on\", \"off\", \"auto\"],\n  \"attributes\" : null,\n  \"brackets\" : [\"\\\\{\", \"\\\\}\"],\n  \"arcsWords\" : [\"note\", \"abox\", \"rbox\", \"box\", \"alt\", \"else\", \"opt\", \"break\", \"par\", \"seq\", \"strict\", \"neg\", \"critical\", \"ignore\", \"consider\", \"assert\", \"loop\", \"ref\", \"exc\"],\n  \"arcsOthers\" : [\"\\\\|\\\\|\\\\|\", \"\\\\.\\\\.\\\\.\", \"---\", \"--\", \"<->\", \"==\", \"<<=>>\", \"<=>\", \"\\\\.\\\\.\", \"<<>>\", \"::\", \"<:>\", \"->\", \"=>>\", \"=>\", \">>\", \":>\", \"<-\", \"<<=\", \"<=\", \"<<\", \"<:\", \"x-\", \"-x\"],\n  \"singlecomment\" : [\"//\", \"#\"],\n  \"operators\" : [\"=\"]\n})\n\nexport const xu = mkParser({\n  \"keywords\" : [\"msc\", \"xu\"],\n  \"options\" : [\"hscale\", \"width\", \"arcgradient\", \"wordwraparcs\", \"wordwrapentities\", \"watermark\"],\n  \"constants\" : [\"true\", \"false\", \"on\", \"off\", \"auto\"],\n  \"attributes\" : [\"label\", \"idurl\", \"id\", \"url\", \"linecolor\", \"linecolour\", \"textcolor\", \"textcolour\", \"textbgcolor\", \"textbgcolour\", \"arclinecolor\", \"arclinecolour\", \"arctextcolor\", \"arctextcolour\", \"arctextbgcolor\", \"arctextbgcolour\", \"arcskip\", \"title\", \"deactivate\", \"activate\", \"activation\"],\n  \"brackets\" : [\"\\\\{\", \"\\\\}\"],  // [ and  ] are brackets too, but these get handled in with lists\n  \"arcsWords\" : [\"note\", \"abox\", \"rbox\", \"box\", \"alt\", \"else\", \"opt\", \"break\", \"par\", \"seq\", \"strict\", \"neg\", \"critical\", \"ignore\", \"consider\", \"assert\", \"loop\", \"ref\", \"exc\"],\n  \"arcsOthers\" : [\"\\\\|\\\\|\\\\|\", \"\\\\.\\\\.\\\\.\", \"---\", \"--\", \"<->\", \"==\", \"<<=>>\", \"<=>\", \"\\\\.\\\\.\", \"<<>>\", \"::\", \"<:>\", \"->\", \"=>>\", \"=>\", \">>\", \":>\", \"<-\", \"<<=\", \"<=\", \"<<\", \"<:\", \"x-\", \"-x\"],\n  \"singlecomment\" : [\"//\", \"#\"],\n  \"operators\" : [\"=\"]\n})\n\nfunction wordRegexpBoundary(pWords) {\n  return new RegExp(\"^\\\\b(\" + pWords.join(\"|\") + \")\\\\b\", \"i\");\n}\n\nfunction wordRegexp(pWords) {\n  return new RegExp(\"^(?:\" + pWords.join(\"|\") + \")\", \"i\");\n}\n\nfunction startStateFn() {\n  return {\n    inComment : false,\n    inString : false,\n    inAttributeList : false,\n    inScript : false\n  };\n}\n\nfunction copyStateFn(pState) {\n  return {\n    inComment : pState.inComment,\n    inString : pState.inString,\n    inAttributeList : pState.inAttributeList,\n    inScript : pState.inScript\n  };\n}\n\nfunction produceTokenFunction(pConfig) {\n  return function(pStream, pState) {\n    if (pStream.match(wordRegexp(pConfig.brackets), true, true)) {\n      return \"bracket\";\n    }\n    /* comments */\n    if (!pState.inComment) {\n      if (pStream.match(/\\/\\*[^\\*\\/]*/, true, true)) {\n        pState.inComment = true;\n        return \"comment\";\n      }\n      if (pStream.match(wordRegexp(pConfig.singlecomment), true, true)) {\n        pStream.skipToEnd();\n        return \"comment\";\n      }\n    }\n    if (pState.inComment) {\n      if (pStream.match(/[^\\*\\/]*\\*\\//, true, true))\n        pState.inComment = false;\n      else\n        pStream.skipToEnd();\n      return \"comment\";\n    }\n    /* strings */\n    if (!pState.inString && pStream.match(/\\\"(\\\\\\\"|[^\\\"])*/, true, true)) {\n      pState.inString = true;\n      return \"string\";\n    }\n    if (pState.inString) {\n      if (pStream.match(/[^\\\"]*\\\"/, true, true))\n        pState.inString = false;\n      else\n        pStream.skipToEnd();\n      return \"string\";\n    }\n    /* keywords & operators */\n    if (!!pConfig.keywords && pStream.match(wordRegexpBoundary(pConfig.keywords), true, true))\n      return \"keyword\";\n\n    if (pStream.match(wordRegexpBoundary(pConfig.options), true, true))\n      return \"keyword\";\n\n    if (pStream.match(wordRegexpBoundary(pConfig.arcsWords), true, true))\n      return \"keyword\";\n\n    if (pStream.match(wordRegexp(pConfig.arcsOthers), true, true))\n      return \"keyword\";\n\n    if (!!pConfig.operators && pStream.match(wordRegexp(pConfig.operators), true, true))\n      return \"operator\";\n\n    if (!!pConfig.constants && pStream.match(wordRegexp(pConfig.constants), true, true))\n      return \"variable\";\n\n    /* attribute lists */\n    if (!pConfig.inAttributeList && !!pConfig.attributes && pStream.match('[', true, true)) {\n      pConfig.inAttributeList = true;\n      return \"bracket\";\n    }\n    if (pConfig.inAttributeList) {\n      if (pConfig.attributes !== null && pStream.match(wordRegexpBoundary(pConfig.attributes), true, true)) {\n        return \"attribute\";\n      }\n      if (pStream.match(']', true, true)) {\n        pConfig.inAttributeList = false;\n        return \"bracket\";\n      }\n    }\n\n    pStream.next();\n    return null\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,SAAS,IAAI;IACpB,OAAO;QACL,MAAM;QACN,YAAY;QACZ,WAAW;QACX,OAAO,qBAAqB;QAC5B,cAAc;YACZ,eAAe;gBAAC,MAAM;gBAAK,OAAO;oBAAC,MAAM;oBAAM,OAAO;gBAAI;YAAC;QAC7D;IACF;AACF;AAEO,MAAM,SAAS,SAAS;IAC7B,YAAa;QAAC;KAAM;IACpB,WAAY;QAAC;QAAU;QAAS;QAAe;KAAe;IAC9D,aAAc;QAAC;QAAQ;QAAS;QAAM;KAAM;IAC5C,cAAe;QAAC;QAAS;QAAS;QAAM;QAAO;QAAa;QAAc;QAAa;QAAc;QAAe;QAAgB;QAAgB;QAAiB;QAAgB;QAAiB;QAAkB;QAAmB;KAAU;IACrP,YAAa;QAAC;QAAO;KAAM;IAC3B,aAAc;QAAC;QAAQ;QAAQ;QAAQ;KAAM;IAC7C,cAAe;QAAC;QAAa;QAAa;QAAO;QAAM;QAAO;QAAM;QAAS;QAAO;QAAU;QAAQ;QAAM;QAAO;QAAM;QAAO;QAAM;QAAM;QAAM;QAAM;QAAO;QAAM;QAAM;QAAM;QAAM;KAAK;IAC5L,iBAAkB;QAAC;QAAM;KAAI;IAC7B,aAAc;QAAC;KAAI;AACrB;AAEO,MAAM,UAAU,SAAS;IAC9B,YAAa;IACb,WAAY;QAAC;QAAU;QAAS;QAAe;QAAgB;QAAoB;KAAY;IAC/F,aAAc;QAAC;QAAQ;QAAS;QAAM;QAAO;KAAO;IACpD,cAAe;IACf,YAAa;QAAC;QAAO;KAAM;IAC3B,aAAc;QAAC;QAAQ;QAAQ;QAAQ;QAAO;QAAO;QAAQ;QAAO;QAAS;QAAO;QAAO;QAAU;QAAO;QAAY;QAAU;QAAY;QAAU;QAAQ;QAAO;KAAM;IAC7K,cAAe;QAAC;QAAa;QAAa;QAAO;QAAM;QAAO;QAAM;QAAS;QAAO;QAAU;QAAQ;QAAM;QAAO;QAAM;QAAO;QAAM;QAAM;QAAM;QAAM;QAAO;QAAM;QAAM;QAAM;QAAM;KAAK;IAC5L,iBAAkB;QAAC;QAAM;KAAI;IAC7B,aAAc;QAAC;KAAI;AACrB;AAEO,MAAM,KAAK,SAAS;IACzB,YAAa;QAAC;QAAO;KAAK;IAC1B,WAAY;QAAC;QAAU;QAAS;QAAe;QAAgB;QAAoB;KAAY;IAC/F,aAAc;QAAC;QAAQ;QAAS;QAAM;QAAO;KAAO;IACpD,cAAe;QAAC;QAAS;QAAS;QAAM;QAAO;QAAa;QAAc;QAAa;QAAc;QAAe;QAAgB;QAAgB;QAAiB;QAAgB;QAAiB;QAAkB;QAAmB;QAAW;QAAS;QAAc;QAAY;KAAa;IACtS,YAAa;QAAC;QAAO;KAAM;IAC3B,aAAc;QAAC;QAAQ;QAAQ;QAAQ;QAAO;QAAO;QAAQ;QAAO;QAAS;QAAO;QAAO;QAAU;QAAO;QAAY;QAAU;QAAY;QAAU;QAAQ;QAAO;KAAM;IAC7K,cAAe;QAAC;QAAa;QAAa;QAAO;QAAM;QAAO;QAAM;QAAS;QAAO;QAAU;QAAQ;QAAM;QAAO;QAAM;QAAO;QAAM;QAAM;QAAM;QAAM;QAAO;QAAM;QAAM;QAAM;QAAM;KAAK;IAC5L,iBAAkB;QAAC;QAAM;KAAI;IAC7B,aAAc;QAAC;KAAI;AACrB;AAEA,SAAS,mBAAmB,MAAM;IAChC,OAAO,IAAI,OAAO,UAAU,OAAO,IAAI,CAAC,OAAO,QAAQ;AACzD;AAEA,SAAS,WAAW,MAAM;IACxB,OAAO,IAAI,OAAO,SAAS,OAAO,IAAI,CAAC,OAAO,KAAK;AACrD;AAEA,SAAS;IACP,OAAO;QACL,WAAY;QACZ,UAAW;QACX,iBAAkB;QAClB,UAAW;IACb;AACF;AAEA,SAAS,YAAY,MAAM;IACzB,OAAO;QACL,WAAY,OAAO,SAAS;QAC5B,UAAW,OAAO,QAAQ;QAC1B,iBAAkB,OAAO,eAAe;QACxC,UAAW,OAAO,QAAQ;IAC5B;AACF;AAEA,SAAS,qBAAqB,OAAO;IACnC,OAAO,SAAS,OAAO,EAAE,MAAM;QAC7B,IAAI,QAAQ,KAAK,CAAC,WAAW,QAAQ,QAAQ,GAAG,MAAM,OAAO;YAC3D,OAAO;QACT;QACA,YAAY,GACZ,IAAI,CAAC,OAAO,SAAS,EAAE;YACrB,IAAI,QAAQ,KAAK,CAAC,gBAAgB,MAAM,OAAO;gBAC7C,OAAO,SAAS,GAAG;gBACnB,OAAO;YACT;YACA,IAAI,QAAQ,KAAK,CAAC,WAAW,QAAQ,aAAa,GAAG,MAAM,OAAO;gBAChE,QAAQ,SAAS;gBACjB,OAAO;YACT;QACF;QACA,IAAI,OAAO,SAAS,EAAE;YACpB,IAAI,QAAQ,KAAK,CAAC,gBAAgB,MAAM,OACtC,OAAO,SAAS,GAAG;iBAEnB,QAAQ,SAAS;YACnB,OAAO;QACT;QACA,WAAW,GACX,IAAI,CAAC,OAAO,QAAQ,IAAI,QAAQ,KAAK,CAAC,mBAAmB,MAAM,OAAO;YACpE,OAAO,QAAQ,GAAG;YAClB,OAAO;QACT;QACA,IAAI,OAAO,QAAQ,EAAE;YACnB,IAAI,QAAQ,KAAK,CAAC,YAAY,MAAM,OAClC,OAAO,QAAQ,GAAG;iBAElB,QAAQ,SAAS;YACnB,OAAO;QACT;QACA,wBAAwB,GACxB,IAAI,CAAC,CAAC,QAAQ,QAAQ,IAAI,QAAQ,KAAK,CAAC,mBAAmB,QAAQ,QAAQ,GAAG,MAAM,OAClF,OAAO;QAET,IAAI,QAAQ,KAAK,CAAC,mBAAmB,QAAQ,OAAO,GAAG,MAAM,OAC3D,OAAO;QAET,IAAI,QAAQ,KAAK,CAAC,mBAAmB,QAAQ,SAAS,GAAG,MAAM,OAC7D,OAAO;QAET,IAAI,QAAQ,KAAK,CAAC,WAAW,QAAQ,UAAU,GAAG,MAAM,OACtD,OAAO;QAET,IAAI,CAAC,CAAC,QAAQ,SAAS,IAAI,QAAQ,KAAK,CAAC,WAAW,QAAQ,SAAS,GAAG,MAAM,OAC5E,OAAO;QAET,IAAI,CAAC,CAAC,QAAQ,SAAS,IAAI,QAAQ,KAAK,CAAC,WAAW,QAAQ,SAAS,GAAG,MAAM,OAC5E,OAAO;QAET,mBAAmB,GACnB,IAAI,CAAC,QAAQ,eAAe,IAAI,CAAC,CAAC,QAAQ,UAAU,IAAI,QAAQ,KAAK,CAAC,KAAK,MAAM,OAAO;YACtF,QAAQ,eAAe,GAAG;YAC1B,OAAO;QACT;QACA,IAAI,QAAQ,eAAe,EAAE;YAC3B,IAAI,QAAQ,UAAU,KAAK,QAAQ,QAAQ,KAAK,CAAC,mBAAmB,QAAQ,UAAU,GAAG,MAAM,OAAO;gBACpG,OAAO;YACT;YACA,IAAI,QAAQ,KAAK,CAAC,KAAK,MAAM,OAAO;gBAClC,QAAQ,eAAe,GAAG;gBAC1B,OAAO;YACT;QACF;QAEA,QAAQ,IAAI;QACZ,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}