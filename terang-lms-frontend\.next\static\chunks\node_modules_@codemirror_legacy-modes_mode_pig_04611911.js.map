{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/pig.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\n// builtin funcs taken from trunk revision 1303237\nvar pBuiltins = \"ABS ACOS ARITY ASIN ATAN AVG BAGSIZE BINSTORAGE BLOOM BUILDBLOOM CBRT CEIL \"\n    + \"CONCAT COR COS COSH COUNT COUNT_STAR COV CONSTANTSIZE CUBEDIMENSIONS DIFF DISTINCT DOUBLEABS \"\n    + \"DOUBLEAVG DOUBLEBASE DOUBLEMAX DOUBLEMIN DOUBLEROUND DOUBLESUM EXP FLOOR FLOATABS FLOATAVG \"\n    + \"FLOATMAX FLOATMIN FLOATROUND FLOATSUM GENERICINVOKER INDEXOF INTABS INTAVG INTMAX INTMIN \"\n    + \"INTSUM INVOKEFORDOUBLE INVOKEFORFLOAT INVOKEFORINT INVOKEFORLONG INVOKEFORSTRING INVOKER \"\n    + \"ISEMPTY JSONLOADER JSONMETADATA JSONSTORAGE LAST_INDEX_OF LCFIRST LOG LOG10 LOWER LONGABS \"\n    + \"LONGAVG LONGMAX LONGMIN LONGSUM MAX MIN MAPSIZE MONITOREDUDF NONDETERMINISTIC OUTPUTSCHEMA  \"\n    + \"PIGSTORAGE PIGSTREAMING RANDOM REGEX_EXTRACT REGEX_EXTRACT_ALL REPLACE ROUND SIN SINH SIZE \"\n    + \"SQRT STRSPLIT SUBSTRING SUM STRINGCONCAT STRINGMAX STRINGMIN STRINGSIZE TAN TANH TOBAG \"\n    + \"TOKENIZE TOMAP TOP TOTUPLE TRIM TEXTLOADER TUPLESIZE UCFIRST UPPER UTF8STORAGECONVERTER \";\n\n// taken from QueryLexer.g\nvar pKeywords = \"VOID IMPORT RETURNS DEFINE LOAD FILTER FOREACH ORDER CUBE DISTINCT COGROUP \"\n    + \"JOIN CROSS UNION SPLIT INTO IF OTHERWISE ALL AS BY USING INNER OUTER ONSCHEMA PARALLEL \"\n    + \"PARTITION GROUP AND OR NOT GENERATE FLATTEN ASC DESC IS STREAM THROUGH STORE MAPREDUCE \"\n    + \"SHIP CACHE INPUT OUTPUT STDERROR STDIN STDOUT LIMIT SAMPLE LEFT RIGHT FULL EQ GT LT GTE LTE \"\n    + \"NEQ MATCHES TRUE FALSE DUMP\";\n\n// data types\nvar pTypes = \"BOOLEAN INT LONG FLOAT DOUBLE CHARARRAY BYTEARRAY BAG TUPLE MAP \";\n\nvar builtins = words(pBuiltins), keywords = words(pKeywords), types = words(pTypes)\n\nvar isOperatorChar = /[*+\\-%<>=&?:\\/!|]/;\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\n\nfunction tokenComment(stream, state) {\n  var isEnd = false;\n  var ch;\n  while(ch = stream.next()) {\n    if(ch == \"/\" && isEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    isEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true; break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped)\n      state.tokenize = tokenBase;\n    return \"error\";\n  };\n}\n\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n\n  // is a start of string?\n  if (ch == '\"' || ch == \"'\")\n    return chain(stream, state, tokenString(ch));\n  // is it one of the special chars\n  else if(/[\\[\\]{}\\(\\),;\\.]/.test(ch))\n    return null;\n  // is it a number?\n  else if(/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  // multi line comment or operator\n  else if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      return chain(stream, state, tokenComment);\n    }\n    else {\n      stream.eatWhile(isOperatorChar);\n      return \"operator\";\n    }\n  }\n  // single line comment or operator\n  else if (ch==\"-\") {\n    if(stream.eat(\"-\")){\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    else {\n      stream.eatWhile(isOperatorChar);\n      return \"operator\";\n    }\n  }\n  // is it an operator\n  else if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  else {\n    // get the while word\n    stream.eatWhile(/[\\w\\$_]/);\n    // is it one of the listed keywords?\n    if (keywords && keywords.propertyIsEnumerable(stream.current().toUpperCase())) {\n      //keywords can be used as variables like flatten(group), group.$0 etc..\n      if (!stream.eat(\")\") && !stream.eat(\".\"))\n        return \"keyword\";\n    }\n    // is it one of the builtin functions?\n    if (builtins && builtins.propertyIsEnumerable(stream.current().toUpperCase()))\n      return \"builtin\";\n    // is it one of the listed types?\n    if (types && types.propertyIsEnumerable(stream.current().toUpperCase()))\n      return \"type\";\n    // default is a 'variable'\n    return \"variable\";\n  }\n}\n\n// Interface\nexport const pig = {\n  name: \"pig\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    if(stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    return style;\n  },\n\n  languageData: {\n    autocomplete: (pBuiltins + pTypes + pKeywords).split(\" \")\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AAEA,kDAAkD;AAClD,IAAI,YAAY,gFACV,kGACA,gGACA,8FACA,8FACA,+FACA,iGACA,gGACA,4FACA;AAEN,0BAA0B;AAC1B,IAAI,YAAY,gFACV,4FACA,4FACA,iGACA;AAEN,aAAa;AACb,IAAI,SAAS;AAEb,IAAI,WAAW,MAAM,YAAY,WAAW,MAAM,YAAY,QAAQ,MAAM;AAE5E,IAAI,iBAAiB;AAErB,SAAS,MAAM,MAAM,EAAE,KAAK,EAAE,CAAC;IAC7B,MAAM,QAAQ,GAAG;IACjB,OAAO,EAAE,QAAQ;AACnB;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,QAAQ;IACZ,IAAI;IACJ,MAAM,KAAK,OAAO,IAAI,GAAI;QACxB,IAAG,MAAM,OAAO,OAAO;YACrB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,QAAS,MAAM;IACjB;IACA,OAAO;AACT;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;QACjC,MAAM,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACpC,IAAI,QAAQ,SAAS,CAAC,SAAS;gBAC7B,MAAM;gBAAM;YACd;YACA,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,OAAO,CAAC,SACV,MAAM,QAAQ,GAAG;QACnB,OAAO;IACT;AACF;AAGA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IAEpB,wBAAwB;IACxB,IAAI,MAAM,OAAO,MAAM,KACrB,OAAO,MAAM,QAAQ,OAAO,YAAY;SAErC,IAAG,mBAAmB,IAAI,CAAC,KAC9B,OAAO;SAEJ,IAAG,KAAK,IAAI,CAAC,KAAK;QACrB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OAEK,IAAI,MAAM,KAAK;QAClB,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO,MAAM,QAAQ,OAAO;QAC9B,OACK;YACH,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;IACF,OAEK,IAAI,MAAI,KAAK;QAChB,IAAG,OAAO,GAAG,CAAC,MAAK;YACjB,OAAO,SAAS;YAChB,OAAO;QACT,OACK;YACH,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;IACF,OAEK,IAAI,eAAe,IAAI,CAAC,KAAK;QAChC,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OACK;QACH,qBAAqB;QACrB,OAAO,QAAQ,CAAC;QAChB,oCAAoC;QACpC,IAAI,YAAY,SAAS,oBAAoB,CAAC,OAAO,OAAO,GAAG,WAAW,KAAK;YAC7E,uEAAuE;YACvE,IAAI,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,OAAO,GAAG,CAAC,MAClC,OAAO;QACX;QACA,sCAAsC;QACtC,IAAI,YAAY,SAAS,oBAAoB,CAAC,OAAO,OAAO,GAAG,WAAW,KACxE,OAAO;QACT,iCAAiC;QACjC,IAAI,SAAS,MAAM,oBAAoB,CAAC,OAAO,OAAO,GAAG,WAAW,KAClE,OAAO;QACT,0BAA0B;QAC1B,OAAO;IACT;AACF;AAGO,MAAM,MAAM;IACjB,MAAM;IAEN,YAAY;QACV,OAAO;YACL,UAAU;YACV,aAAa;QACf;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAG,OAAO,QAAQ,IAAI,OAAO;QAC7B,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QACnC,OAAO;IACT;IAEA,cAAc;QACZ,cAAc,CAAC,YAAY,SAAS,SAAS,EAAE,KAAK,CAAC;IACvD;AACF", "ignoreList": [0], "debugId": null}}]}