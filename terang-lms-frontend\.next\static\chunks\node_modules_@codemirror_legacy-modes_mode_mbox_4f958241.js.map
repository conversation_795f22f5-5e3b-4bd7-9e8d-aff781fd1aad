{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/mbox.js"], "sourcesContent": ["var rfc2822 = [\n  \"From\", \"Sender\", \"Reply-To\", \"To\", \"Cc\", \"Bcc\", \"Message-ID\",\n  \"In-Reply-To\", \"References\", \"Resent-From\", \"Resent-Sender\", \"Resent-To\",\n  \"Resent-Cc\", \"Resent-Bcc\", \"Resent-Message-ID\", \"Return-Path\", \"Received\"\n];\nvar rfc2822NoEmail = [\n  \"Date\", \"Subject\", \"Comments\", \"Keywords\", \"Resent-Date\"\n];\n\nvar whitespace = /^[ \\t]/;\nvar separator = /^From /; // See RFC 4155\nvar rfc2822Header = new RegExp(\"^(\" + rfc2822.join(\"|\") + \"): \");\nvar rfc2822HeaderNoEmail = new RegExp(\"^(\" + rfc2822NoEmail.join(\"|\") + \"): \");\nvar header = /^[^:]+:/; // Optional fields defined in RFC 2822\nvar email = /^[^ ]+@[^ ]+/;\nvar untilEmail = /^.*?(?=[^ ]+?@[^ ]+)/;\nvar bracketedEmail = /^<.*?>/;\nvar untilBracketedEmail = /^.*?(?=<.*>)/;\n\nfunction styleForHeader(header) {\n  if (header === \"Subject\") return \"header\";\n  return \"string\";\n}\n\nfunction readToken(stream, state) {\n  if (stream.sol()) {\n    // From last line\n    state.inSeparator = false;\n    if (state.inHeader && stream.match(whitespace)) {\n      // Header folding\n      return null;\n    } else {\n      state.inHeader = false;\n      state.header = null;\n    }\n\n    if (stream.match(separator)) {\n      state.inHeaders = true;\n      state.inSeparator = true;\n      return \"atom\";\n    }\n\n    var match;\n    var emailPermitted = false;\n    if ((match = stream.match(rfc2822HeaderNoEmail)) ||\n        (emailPermitted = true) && (match = stream.match(rfc2822Header))) {\n      state.inHeaders = true;\n      state.inHeader = true;\n      state.emailPermitted = emailPermitted;\n      state.header = match[1];\n      return \"atom\";\n    }\n\n    // Use vim's heuristics: recognize custom headers only if the line is in a\n    // block of legitimate headers.\n    if (state.inHeaders && (match = stream.match(header))) {\n      state.inHeader = true;\n      state.emailPermitted = true;\n      state.header = match[1];\n      return \"atom\";\n    }\n\n    state.inHeaders = false;\n    stream.skipToEnd();\n    return null;\n  }\n\n  if (state.inSeparator) {\n    if (stream.match(email)) return \"link\";\n    if (stream.match(untilEmail)) return \"atom\";\n    stream.skipToEnd();\n    return \"atom\";\n  }\n\n  if (state.inHeader) {\n    var style = styleForHeader(state.header);\n\n    if (state.emailPermitted) {\n      if (stream.match(bracketedEmail)) return style + \" link\";\n      if (stream.match(untilBracketedEmail)) return style;\n    }\n    stream.skipToEnd();\n    return style;\n  }\n\n  stream.skipToEnd();\n  return null;\n};\n\nexport const mbox = {\n  name: \"mbox\",\n  startState: function() {\n    return {\n      // Is in a mbox separator\n      inSeparator: false,\n      // Is in a mail header\n      inHeader: false,\n      // If bracketed email is permitted. Only applicable when inHeader\n      emailPermitted: false,\n      // Name of current header\n      header: null,\n      // Is in a region of mail headers\n      inHeaders: false\n    };\n  },\n  token: readToken,\n  blankLine: function(state) {\n    state.inHeaders = state.inSeparator = state.inHeader = false;\n  },\n  languageData: {\n    autocomplete: rfc2822.concat(rfc2822NoEmail)\n  }\n}\n\n"], "names": [], "mappings": ";;;AAAA,IAAI,UAAU;IACZ;IAAQ;IAAU;IAAY;IAAM;IAAM;IAAO;IACjD;IAAe;IAAc;IAAe;IAAiB;IAC7D;IAAa;IAAc;IAAqB;IAAe;CAChE;AACD,IAAI,iBAAiB;IACnB;IAAQ;IAAW;IAAY;IAAY;CAC5C;AAED,IAAI,aAAa;AACjB,IAAI,YAAY,UAAU,eAAe;AACzC,IAAI,gBAAgB,IAAI,OAAO,OAAO,QAAQ,IAAI,CAAC,OAAO;AAC1D,IAAI,uBAAuB,IAAI,OAAO,OAAO,eAAe,IAAI,CAAC,OAAO;AACxE,IAAI,SAAS,WAAW,sCAAsC;AAC9D,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,iBAAiB;AACrB,IAAI,sBAAsB;AAE1B,SAAS,eAAe,MAAM;IAC5B,IAAI,WAAW,WAAW,OAAO;IACjC,OAAO;AACT;AAEA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,OAAO,GAAG,IAAI;QAChB,iBAAiB;QACjB,MAAM,WAAW,GAAG;QACpB,IAAI,MAAM,QAAQ,IAAI,OAAO,KAAK,CAAC,aAAa;YAC9C,iBAAiB;YACjB,OAAO;QACT,OAAO;YACL,MAAM,QAAQ,GAAG;YACjB,MAAM,MAAM,GAAG;QACjB;QAEA,IAAI,OAAO,KAAK,CAAC,YAAY;YAC3B,MAAM,SAAS,GAAG;YAClB,MAAM,WAAW,GAAG;YACpB,OAAO;QACT;QAEA,IAAI;QACJ,IAAI,iBAAiB;QACrB,IAAI,CAAC,QAAQ,OAAO,KAAK,CAAC,qBAAqB,KAC3C,CAAC,iBAAiB,IAAI,KAAK,CAAC,QAAQ,OAAO,KAAK,CAAC,cAAc,GAAG;YACpE,MAAM,SAAS,GAAG;YAClB,MAAM,QAAQ,GAAG;YACjB,MAAM,cAAc,GAAG;YACvB,MAAM,MAAM,GAAG,KAAK,CAAC,EAAE;YACvB,OAAO;QACT;QAEA,0EAA0E;QAC1E,+BAA+B;QAC/B,IAAI,MAAM,SAAS,IAAI,CAAC,QAAQ,OAAO,KAAK,CAAC,OAAO,GAAG;YACrD,MAAM,QAAQ,GAAG;YACjB,MAAM,cAAc,GAAG;YACvB,MAAM,MAAM,GAAG,KAAK,CAAC,EAAE;YACvB,OAAO;QACT;QAEA,MAAM,SAAS,GAAG;QAClB,OAAO,SAAS;QAChB,OAAO;IACT;IAEA,IAAI,MAAM,WAAW,EAAE;QACrB,IAAI,OAAO,KAAK,CAAC,QAAQ,OAAO;QAChC,IAAI,OAAO,KAAK,CAAC,aAAa,OAAO;QACrC,OAAO,SAAS;QAChB,OAAO;IACT;IAEA,IAAI,MAAM,QAAQ,EAAE;QAClB,IAAI,QAAQ,eAAe,MAAM,MAAM;QAEvC,IAAI,MAAM,cAAc,EAAE;YACxB,IAAI,OAAO,KAAK,CAAC,iBAAiB,OAAO,QAAQ;YACjD,IAAI,OAAO,KAAK,CAAC,sBAAsB,OAAO;QAChD;QACA,OAAO,SAAS;QAChB,OAAO;IACT;IAEA,OAAO,SAAS;IAChB,OAAO;AACT;;AAEO,MAAM,OAAO;IAClB,MAAM;IACN,YAAY;QACV,OAAO;YACL,yBAAyB;YACzB,aAAa;YACb,sBAAsB;YACtB,UAAU;YACV,iEAAiE;YACjE,gBAAgB;YAChB,yBAAyB;YACzB,QAAQ;YACR,iCAAiC;YACjC,WAAW;QACb;IACF;IACA,OAAO;IACP,WAAW,SAAS,KAAK;QACvB,MAAM,SAAS,GAAG,MAAM,WAAW,GAAG,MAAM,QAAQ,GAAG;IACzD;IACA,cAAc;QACZ,cAAc,QAAQ,MAAM,CAAC;IAC/B;AACF", "ignoreList": [0], "debugId": null}}]}