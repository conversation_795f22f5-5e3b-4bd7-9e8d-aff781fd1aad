{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/asterisk.js"], "sourcesContent": ["var atoms    = [\"exten\", \"same\", \"include\",\"ignorepat\",\"switch\"],\n    dpcmd    = [\"#include\",\"#exec\"],\n    apps     = [\n      \"addqueuemember\",\"adsiprog\",\"aelsub\",\"agentlogin\",\"agentmonitoroutgoing\",\"agi\",\n      \"alarmreceiver\",\"amd\",\"answer\",\"authenticate\",\"background\",\"backgrounddetect\",\n      \"bridge\",\"busy\",\"callcompletioncancel\",\"callcompletionrequest\",\"celgenuserevent\",\n      \"changemonitor\",\"chanisavail\",\"channelredirect\",\"chanspy\",\"clearhash\",\"confbridge\",\n      \"congestion\",\"continuewhile\",\"controlplayback\",\"dahdiacceptr2call\",\"dahdibarge\",\n      \"dahdiras\",\"dahdiscan\",\"dahdisendcallreroutingfacility\",\"dahdisendkeypadfacility\",\n      \"datetime\",\"dbdel\",\"dbdeltree\",\"deadagi\",\"dial\",\"dictate\",\"directory\",\"disa\",\n      \"dumpchan\",\"eagi\",\"echo\",\"endwhile\",\"exec\",\"execif\",\"execiftime\",\"exitwhile\",\"extenspy\",\n      \"externalivr\",\"festival\",\"flash\",\"followme\",\"forkcdr\",\"getcpeid\",\"gosub\",\"gosubif\",\n      \"goto\",\"gotoif\",\"gotoiftime\",\"hangup\",\"iax2provision\",\"ices\",\"importvar\",\"incomplete\",\n      \"ivrdemo\",\"jabberjoin\",\"jabberleave\",\"jabbersend\",\"jabbersendgroup\",\"jabberstatus\",\n      \"jack\",\"log\",\"macro\",\"macroexclusive\",\"macroexit\",\"macroif\",\"mailboxexists\",\"meetme\",\n      \"meetmeadmin\",\"meetmechanneladmin\",\"meetmecount\",\"milliwatt\",\"minivmaccmess\",\"minivmdelete\",\n      \"minivmgreet\",\"minivmmwi\",\"minivmnotify\",\"minivmrecord\",\"mixmonitor\",\"monitor\",\"morsecode\",\n      \"mp3player\",\"mset\",\"musiconhold\",\"nbscat\",\"nocdr\",\"noop\",\"odbc\",\"odbc\",\"odbcfinish\",\n      \"originate\",\"ospauth\",\"ospfinish\",\"osplookup\",\"ospnext\",\"page\",\"park\",\"parkandannounce\",\n      \"parkedcall\",\"pausemonitor\",\"pausequeuemember\",\"pickup\",\"pickupchan\",\"playback\",\"playtones\",\n      \"privacymanager\",\"proceeding\",\"progress\",\"queue\",\"queuelog\",\"raiseexception\",\"read\",\"readexten\",\n      \"readfile\",\"receivefax\",\"receivefax\",\"receivefax\",\"record\",\"removequeuemember\",\n      \"resetcdr\",\"retrydial\",\"return\",\"ringing\",\"sayalpha\",\"saycountedadj\",\"saycountednoun\",\n      \"saycountpl\",\"saydigits\",\"saynumber\",\"sayphonetic\",\"sayunixtime\",\"senddtmf\",\"sendfax\",\n      \"sendfax\",\"sendfax\",\"sendimage\",\"sendtext\",\"sendurl\",\"set\",\"setamaflags\",\n      \"setcallerpres\",\"setmusiconhold\",\"sipaddheader\",\"sipdtmfmode\",\"sipremoveheader\",\"skel\",\n      \"slastation\",\"slatrunk\",\"sms\",\"softhangup\",\"speechactivategrammar\",\"speechbackground\",\n      \"speechcreate\",\"speechdeactivategrammar\",\"speechdestroy\",\"speechloadgrammar\",\"speechprocessingsound\",\n      \"speechstart\",\"speechunloadgrammar\",\"stackpop\",\"startmusiconhold\",\"stopmixmonitor\",\"stopmonitor\",\n      \"stopmusiconhold\",\"stopplaytones\",\"system\",\"testclient\",\"testserver\",\"transfer\",\"tryexec\",\n      \"trysystem\",\"unpausemonitor\",\"unpausequeuemember\",\"userevent\",\"verbose\",\"vmauthenticate\",\n      \"vmsayname\",\"voicemail\",\"voicemailmain\",\"wait\",\"waitexten\",\"waitfornoise\",\"waitforring\",\n      \"waitforsilence\",\"waitmusiconhold\",\"waituntil\",\"while\",\"zapateller\"\n    ];\n\nfunction basicToken(stream,state){\n  var cur = '';\n  var ch = stream.next();\n  // comment\n  if (state.blockComment) {\n    if (ch == \"-\" && stream.match(\"-;\", true)) {\n      state.blockComment = false;\n    } else if (stream.skipTo(\"--;\")) {\n      stream.next();\n      stream.next();\n      stream.next();\n      state.blockComment = false;\n    } else {\n      stream.skipToEnd();\n    }\n    return \"comment\";\n  }\n  if(ch == \";\") {\n    if (stream.match(\"--\", true)) {\n      if (!stream.match(\"-\", false)) {  // Except ;--- is not a block comment\n        state.blockComment = true;\n        return \"comment\";\n      }\n    }\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  // context\n  if(ch == '[') {\n    stream.skipTo(']');\n    stream.eat(']');\n    return \"header\";\n  }\n  // string\n  if(ch == '\"') {\n    stream.skipTo('\"');\n    return \"string\";\n  }\n  if(ch == \"'\") {\n    stream.skipTo(\"'\");\n    return \"string.special\";\n  }\n  // dialplan commands\n  if(ch == '#') {\n    stream.eatWhile(/\\w/);\n    cur = stream.current();\n    if(dpcmd.indexOf(cur) !== -1) {\n      stream.skipToEnd();\n      return \"strong\";\n    }\n  }\n  // application args\n  if(ch == '$'){\n    var ch1 = stream.peek();\n    if(ch1 == '{'){\n      stream.skipTo('}');\n      stream.eat('}');\n      return \"variableName.special\";\n    }\n  }\n  // extension\n  stream.eatWhile(/\\w/);\n  cur = stream.current();\n  if(atoms.indexOf(cur) !== -1) {\n    state.extenStart = true;\n    switch(cur) {\n    case 'same': state.extenSame = true; break;\n    case 'include':\n    case 'switch':\n    case 'ignorepat':\n      state.extenInclude = true;break;\n    default:break;\n    }\n    return \"atom\";\n  }\n}\n\nexport const asterisk = {\n  name: \"asterisk\",\n  startState: function() {\n    return {\n      blockComment: false,\n      extenStart: false,\n      extenSame:  false,\n      extenInclude: false,\n      extenExten: false,\n      extenPriority: false,\n      extenApplication: false\n    };\n  },\n  token: function(stream, state) {\n\n    var cur = '';\n    if(stream.eatSpace()) return null;\n    // extension started\n    if(state.extenStart){\n      stream.eatWhile(/[^\\s]/);\n      cur = stream.current();\n      if(/^=>?$/.test(cur)){\n        state.extenExten = true;\n        state.extenStart = false;\n        return \"strong\";\n      } else {\n        state.extenStart = false;\n        stream.skipToEnd();\n        return \"error\";\n      }\n    } else if(state.extenExten) {\n      // set exten and priority\n      state.extenExten = false;\n      state.extenPriority = true;\n      stream.eatWhile(/[^,]/);\n      if(state.extenInclude) {\n        stream.skipToEnd();\n        state.extenPriority = false;\n        state.extenInclude = false;\n      }\n      if(state.extenSame) {\n        state.extenPriority = false;\n        state.extenSame = false;\n        state.extenApplication = true;\n      }\n      return \"tag\";\n    } else if(state.extenPriority) {\n      state.extenPriority = false;\n      state.extenApplication = true;\n      stream.next(); // get comma\n      if(state.extenSame) return null;\n      stream.eatWhile(/[^,]/);\n      return \"number\";\n    } else if(state.extenApplication) {\n      stream.eatWhile(/,/);\n      cur = stream.current();\n      if(cur === ',') return null;\n      stream.eatWhile(/\\w/);\n      cur = stream.current().toLowerCase();\n      state.extenApplication = false;\n      if(apps.indexOf(cur) !== -1){\n        return \"def\";\n      }\n    } else{\n      return basicToken(stream,state);\n    }\n\n    return null;\n  },\n\n  languageData: {\n    commentTokens: {line: \";\", block: {open: \";--\", close: \"--;\"}}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,QAAW;IAAC;IAAS;IAAQ;IAAU;IAAY;CAAS,EAC5D,QAAW;IAAC;IAAW;CAAQ,EAC/B,OAAW;IACT;IAAiB;IAAW;IAAS;IAAa;IAAuB;IACzE;IAAgB;IAAM;IAAS;IAAe;IAAa;IAC3D;IAAS;IAAO;IAAuB;IAAwB;IAC/D;IAAgB;IAAc;IAAkB;IAAU;IAAY;IACtE;IAAa;IAAgB;IAAkB;IAAoB;IACnE;IAAW;IAAY;IAAiC;IACxD;IAAW;IAAQ;IAAY;IAAU;IAAO;IAAU;IAAY;IACtE;IAAW;IAAO;IAAO;IAAW;IAAO;IAAS;IAAa;IAAY;IAC7E;IAAc;IAAW;IAAQ;IAAW;IAAU;IAAW;IAAQ;IACzE;IAAO;IAAS;IAAa;IAAS;IAAgB;IAAO;IAAY;IACzE;IAAU;IAAa;IAAc;IAAa;IAAkB;IACpE;IAAO;IAAM;IAAQ;IAAiB;IAAY;IAAU;IAAgB;IAC5E;IAAc;IAAqB;IAAc;IAAY;IAAgB;IAC7E;IAAc;IAAY;IAAe;IAAe;IAAa;IAAU;IAC/E;IAAY;IAAO;IAAc;IAAS;IAAQ;IAAO;IAAO;IAAO;IACvE;IAAY;IAAU;IAAY;IAAY;IAAU;IAAO;IAAO;IACtE;IAAa;IAAe;IAAmB;IAAS;IAAa;IAAW;IAChF;IAAiB;IAAa;IAAW;IAAQ;IAAW;IAAiB;IAAO;IACpF;IAAW;IAAa;IAAa;IAAa;IAAS;IAC3D;IAAW;IAAY;IAAS;IAAU;IAAW;IAAgB;IACrE;IAAa;IAAY;IAAY;IAAc;IAAc;IAAW;IAC5E;IAAU;IAAU;IAAY;IAAW;IAAU;IAAM;IAC3D;IAAgB;IAAiB;IAAe;IAAc;IAAkB;IAChF;IAAa;IAAW;IAAM;IAAa;IAAwB;IACnE;IAAe;IAA0B;IAAgB;IAAoB;IAC7E;IAAc;IAAsB;IAAW;IAAmB;IAAiB;IACnF;IAAkB;IAAgB;IAAS;IAAa;IAAa;IAAW;IAChF;IAAY;IAAiB;IAAqB;IAAY;IAAU;IACxE;IAAY;IAAY;IAAgB;IAAO;IAAY;IAAe;IAC1E;IAAiB;IAAkB;IAAY;IAAQ;CACxD;AAEL,SAAS,WAAW,MAAM,EAAC,KAAK;IAC9B,IAAI,MAAM;IACV,IAAI,KAAK,OAAO,IAAI;IACpB,UAAU;IACV,IAAI,MAAM,YAAY,EAAE;QACtB,IAAI,MAAM,OAAO,OAAO,KAAK,CAAC,MAAM,OAAO;YACzC,MAAM,YAAY,GAAG;QACvB,OAAO,IAAI,OAAO,MAAM,CAAC,QAAQ;YAC/B,OAAO,IAAI;YACX,OAAO,IAAI;YACX,OAAO,IAAI;YACX,MAAM,YAAY,GAAG;QACvB,OAAO;YACL,OAAO,SAAS;QAClB;QACA,OAAO;IACT;IACA,IAAG,MAAM,KAAK;QACZ,IAAI,OAAO,KAAK,CAAC,MAAM,OAAO;YAC5B,IAAI,CAAC,OAAO,KAAK,CAAC,KAAK,QAAQ;gBAC7B,MAAM,YAAY,GAAG;gBACrB,OAAO;YACT;QACF;QACA,OAAO,SAAS;QAChB,OAAO;IACT;IACA,UAAU;IACV,IAAG,MAAM,KAAK;QACZ,OAAO,MAAM,CAAC;QACd,OAAO,GAAG,CAAC;QACX,OAAO;IACT;IACA,SAAS;IACT,IAAG,MAAM,KAAK;QACZ,OAAO,MAAM,CAAC;QACd,OAAO;IACT;IACA,IAAG,MAAM,KAAK;QACZ,OAAO,MAAM,CAAC;QACd,OAAO;IACT;IACA,oBAAoB;IACpB,IAAG,MAAM,KAAK;QACZ,OAAO,QAAQ,CAAC;QAChB,MAAM,OAAO,OAAO;QACpB,IAAG,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG;YAC5B,OAAO,SAAS;YAChB,OAAO;QACT;IACF;IACA,mBAAmB;IACnB,IAAG,MAAM,KAAI;QACX,IAAI,MAAM,OAAO,IAAI;QACrB,IAAG,OAAO,KAAI;YACZ,OAAO,MAAM,CAAC;YACd,OAAO,GAAG,CAAC;YACX,OAAO;QACT;IACF;IACA,YAAY;IACZ,OAAO,QAAQ,CAAC;IAChB,MAAM,OAAO,OAAO;IACpB,IAAG,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG;QAC5B,MAAM,UAAU,GAAG;QACnB,OAAO;YACP,KAAK;gBAAQ,MAAM,SAAS,GAAG;gBAAM;YACrC,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,YAAY,GAAG;gBAAK;YAC5B;gBAAQ;QACR;QACA,OAAO;IACT;AACF;AAEO,MAAM,WAAW;IACtB,MAAM;IACN,YAAY;QACV,OAAO;YACL,cAAc;YACd,YAAY;YACZ,WAAY;YACZ,cAAc;YACd,YAAY;YACZ,eAAe;YACf,kBAAkB;QACpB;IACF;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAE3B,IAAI,MAAM;QACV,IAAG,OAAO,QAAQ,IAAI,OAAO;QAC7B,oBAAoB;QACpB,IAAG,MAAM,UAAU,EAAC;YAClB,OAAO,QAAQ,CAAC;YAChB,MAAM,OAAO,OAAO;YACpB,IAAG,QAAQ,IAAI,CAAC,MAAK;gBACnB,MAAM,UAAU,GAAG;gBACnB,MAAM,UAAU,GAAG;gBACnB,OAAO;YACT,OAAO;gBACL,MAAM,UAAU,GAAG;gBACnB,OAAO,SAAS;gBAChB,OAAO;YACT;QACF,OAAO,IAAG,MAAM,UAAU,EAAE;YAC1B,yBAAyB;YACzB,MAAM,UAAU,GAAG;YACnB,MAAM,aAAa,GAAG;YACtB,OAAO,QAAQ,CAAC;YAChB,IAAG,MAAM,YAAY,EAAE;gBACrB,OAAO,SAAS;gBAChB,MAAM,aAAa,GAAG;gBACtB,MAAM,YAAY,GAAG;YACvB;YACA,IAAG,MAAM,SAAS,EAAE;gBAClB,MAAM,aAAa,GAAG;gBACtB,MAAM,SAAS,GAAG;gBAClB,MAAM,gBAAgB,GAAG;YAC3B;YACA,OAAO;QACT,OAAO,IAAG,MAAM,aAAa,EAAE;YAC7B,MAAM,aAAa,GAAG;YACtB,MAAM,gBAAgB,GAAG;YACzB,OAAO,IAAI,IAAI,YAAY;YAC3B,IAAG,MAAM,SAAS,EAAE,OAAO;YAC3B,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT,OAAO,IAAG,MAAM,gBAAgB,EAAE;YAChC,OAAO,QAAQ,CAAC;YAChB,MAAM,OAAO,OAAO;YACpB,IAAG,QAAQ,KAAK,OAAO;YACvB,OAAO,QAAQ,CAAC;YAChB,MAAM,OAAO,OAAO,GAAG,WAAW;YAClC,MAAM,gBAAgB,GAAG;YACzB,IAAG,KAAK,OAAO,CAAC,SAAS,CAAC,GAAE;gBAC1B,OAAO;YACT;QACF,OAAM;YACJ,OAAO,WAAW,QAAO;QAC3B;QAEA,OAAO;IACT;IAEA,cAAc;QACZ,eAAe;YAAC,MAAM;YAAK,OAAO;gBAAC,MAAM;gBAAO,OAAO;YAAK;QAAC;IAC/D;AACF", "ignoreList": [0], "debugId": null}}]}