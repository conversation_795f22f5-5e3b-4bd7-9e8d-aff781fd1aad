{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/toml.js"], "sourcesContent": ["export const toml = {\n  name: \"toml\",\n  startState: function () {\n    return {\n      inString: false,\n      stringType: \"\",\n      lhs: true,\n      inArray: 0\n    };\n  },\n  token: function (stream, state) {\n    //check for state changes\n    if (!state.inString && ((stream.peek() == '\"') || (stream.peek() == \"'\"))) {\n      state.stringType = stream.peek();\n      stream.next(); // Skip quote\n      state.inString = true; // Update state\n    }\n    if (stream.sol() && state.inArray === 0) {\n      state.lhs = true;\n    }\n    //return state\n    if (state.inString) {\n      while (state.inString && !stream.eol()) {\n        if (stream.peek() === state.stringType) {\n          stream.next(); // Skip quote\n          state.inString = false; // Clear flag\n        } else if (stream.peek() === '\\\\') {\n          stream.next();\n          stream.next();\n        } else {\n          stream.match(/^.[^\\\\\\\"\\']*/);\n        }\n      }\n      return state.lhs ? \"property\" : \"string\"; // Token style\n    } else if (state.inArray && stream.peek() === ']') {\n      stream.next();\n      state.inArray--;\n      return 'bracket';\n    } else if (state.lhs && stream.peek() === '[' && stream.skipTo(']')) {\n      stream.next();//skip closing ]\n      // array of objects has an extra open & close []\n      if (stream.peek() === ']') stream.next();\n      return \"atom\";\n    } else if (stream.peek() === \"#\") {\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (stream.eatSpace()) {\n      return null;\n    } else if (state.lhs && stream.eatWhile(function (c) { return c != '=' && c != ' '; })) {\n      return \"property\";\n    } else if (state.lhs && stream.peek() === \"=\") {\n      stream.next();\n      state.lhs = false;\n      return null;\n    } else if (!state.lhs && stream.match(/^\\d\\d\\d\\d[\\d\\-\\:\\.T]*Z/)) {\n      return 'atom'; //date\n    } else if (!state.lhs && (stream.match('true') || stream.match('false'))) {\n      return 'atom';\n    } else if (!state.lhs && stream.peek() === '[') {\n      state.inArray++;\n      stream.next();\n      return 'bracket';\n    } else if (!state.lhs && stream.match(/^\\-?\\d+(?:\\.\\d+)?/)) {\n      return 'number';\n    } else if (!stream.eatSpace()) {\n      stream.next();\n    }\n    return null;\n  },\n  languageData: {\n    commentTokens: { line: '#' },\n  },\n};\n"], "names": [], "mappings": ";;;AAAO,MAAM,OAAO;IAClB,MAAM;IACN,YAAY;QACV,OAAO;YACL,UAAU;YACV,YAAY;YACZ,KAAK;YACL,SAAS;QACX;IACF;IACA,OAAO,SAAU,MAAM,EAAE,KAAK;QAC5B,yBAAyB;QACzB,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,AAAC,OAAO,IAAI,MAAM,OAAS,OAAO,IAAI,MAAM,GAAI,GAAG;YACzE,MAAM,UAAU,GAAG,OAAO,IAAI;YAC9B,OAAO,IAAI,IAAI,aAAa;YAC5B,MAAM,QAAQ,GAAG,MAAM,eAAe;QACxC;QACA,IAAI,OAAO,GAAG,MAAM,MAAM,OAAO,KAAK,GAAG;YACvC,MAAM,GAAG,GAAG;QACd;QACA,cAAc;QACd,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAO,MAAM,QAAQ,IAAI,CAAC,OAAO,GAAG,GAAI;gBACtC,IAAI,OAAO,IAAI,OAAO,MAAM,UAAU,EAAE;oBACtC,OAAO,IAAI,IAAI,aAAa;oBAC5B,MAAM,QAAQ,GAAG,OAAO,aAAa;gBACvC,OAAO,IAAI,OAAO,IAAI,OAAO,MAAM;oBACjC,OAAO,IAAI;oBACX,OAAO,IAAI;gBACb,OAAO;oBACL,OAAO,KAAK,CAAC;gBACf;YACF;YACA,OAAO,MAAM,GAAG,GAAG,aAAa,UAAU,cAAc;QAC1D,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,OAAO,KAAK;YACjD,OAAO,IAAI;YACX,MAAM,OAAO;YACb,OAAO;QACT,OAAO,IAAI,MAAM,GAAG,IAAI,OAAO,IAAI,OAAO,OAAO,OAAO,MAAM,CAAC,MAAM;YACnE,OAAO,IAAI,IAAG,gBAAgB;YAC9B,gDAAgD;YAChD,IAAI,OAAO,IAAI,OAAO,KAAK,OAAO,IAAI;YACtC,OAAO;QACT,OAAO,IAAI,OAAO,IAAI,OAAO,KAAK;YAChC,OAAO,SAAS;YAChB,OAAO;QACT,OAAO,IAAI,OAAO,QAAQ,IAAI;YAC5B,OAAO;QACT,OAAO,IAAI,MAAM,GAAG,IAAI,OAAO,QAAQ,CAAC,SAAU,CAAC;YAAI,OAAO,KAAK,OAAO,KAAK;QAAK,IAAI;YACtF,OAAO;QACT,OAAO,IAAI,MAAM,GAAG,IAAI,OAAO,IAAI,OAAO,KAAK;YAC7C,OAAO,IAAI;YACX,MAAM,GAAG,GAAG;YACZ,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,KAAK,CAAC,2BAA2B;YAC/D,OAAO,QAAQ,MAAM;QACvB,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,KAAK,CAAC,WAAW,OAAO,KAAK,CAAC,QAAQ,GAAG;YACxE,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,IAAI,OAAO,KAAK;YAC9C,MAAM,OAAO;YACb,OAAO,IAAI;YACX,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,KAAK,CAAC,sBAAsB;YAC1D,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,QAAQ,IAAI;YAC7B,OAAO,IAAI;QACb;QACA,OAAO;IACT;IACA,cAAc;QACZ,eAAe;YAAE,MAAM;QAAI;IAC7B;AACF", "ignoreList": [0], "debugId": null}}]}