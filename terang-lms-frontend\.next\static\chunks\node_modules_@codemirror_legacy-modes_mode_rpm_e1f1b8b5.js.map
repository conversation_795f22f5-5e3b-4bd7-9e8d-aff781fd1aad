{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/rpm.js"], "sourcesContent": ["var headerSeparator = /^-+$/;\nvar headerLine = /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)  ?\\d{1,2} \\d{2}:\\d{2}(:\\d{2})? [A-Z]{3,4} \\d{4} - /;\nvar simpleEmail = /^[\\w+.-]+@[\\w.-]+/;\n\nexport const rpmChanges = {\n  name: \"rpmchanges\",\n  token: function(stream) {\n    if (stream.sol()) {\n      if (stream.match(headerSeparator)) { return 'tag'; }\n      if (stream.match(headerLine)) { return 'tag'; }\n    }\n    if (stream.match(simpleEmail)) { return 'string'; }\n    stream.next();\n    return null;\n  }\n}\n\n// Quick and dirty spec file highlighting\n\nvar arch = /^(i386|i586|i686|x86_64|ppc64le|ppc64|ppc|ia64|s390x|s390|sparc64|sparcv9|sparc|noarch|alphaev6|alpha|hppa|mipsel)/;\n\nvar preamble = /^[a-zA-Z0-9()]+:/;\nvar section = /^%(debug_package|package|description|prep|build|install|files|clean|changelog|preinstall|preun|postinstall|postun|pretrans|posttrans|pre|post|triggerin|triggerun|verifyscript|check|triggerpostun|triggerprein|trigger)/;\nvar control_flow_complex = /^%(ifnarch|ifarch|if)/; // rpm control flow macros\nvar control_flow_simple = /^%(else|endif)/; // rpm control flow macros\nvar operators = /^(\\!|\\?|\\<\\=|\\<|\\>\\=|\\>|\\=\\=|\\&\\&|\\|\\|)/; // operators in control flow macros\n\nexport const rpmSpec = {\n  name: \"rpmspec\",\n  startState: function () {\n    return {\n      controlFlow: false,\n      macroParameters: false,\n      section: false\n    };\n  },\n  token: function (stream, state) {\n    var ch = stream.peek();\n    if (ch == \"#\") { stream.skipToEnd(); return \"comment\"; }\n\n    if (stream.sol()) {\n      if (stream.match(preamble)) { return \"header\"; }\n      if (stream.match(section)) { return \"atom\"; }\n    }\n\n    if (stream.match(/^\\$\\w+/)) { return \"def\"; } // Variables like '$RPM_BUILD_ROOT'\n    if (stream.match(/^\\$\\{\\w+\\}/)) { return \"def\"; } // Variables like '${RPM_BUILD_ROOT}'\n\n    if (stream.match(control_flow_simple)) { return \"keyword\"; }\n    if (stream.match(control_flow_complex)) {\n      state.controlFlow = true;\n      return \"keyword\";\n    }\n    if (state.controlFlow) {\n      if (stream.match(operators)) { return \"operator\"; }\n      if (stream.match(/^(\\d+)/)) { return \"number\"; }\n      if (stream.eol()) { state.controlFlow = false; }\n    }\n\n    if (stream.match(arch)) {\n      if (stream.eol()) { state.controlFlow = false; }\n      return \"number\";\n    }\n\n    // Macros like '%make_install' or '%attr(0775,root,root)'\n    if (stream.match(/^%[\\w]+/)) {\n      if (stream.match('(')) { state.macroParameters = true; }\n      return \"keyword\";\n    }\n    if (state.macroParameters) {\n      if (stream.match(/^\\d+/)) { return \"number\";}\n      if (stream.match(')')) {\n        state.macroParameters = false;\n        return \"keyword\";\n      }\n    }\n\n    // Macros like '%{defined fedora}'\n    if (stream.match(/^%\\{\\??[\\w \\-\\:\\!]+\\}/)) {\n      if (stream.eol()) { state.controlFlow = false; }\n      return \"def\";\n    }\n\n    stream.next();\n    return null;\n  }\n};\n\n"], "names": [], "mappings": ";;;;AAAA,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,cAAc;AAEX,MAAM,aAAa;IACxB,MAAM;IACN,OAAO,SAAS,MAAM;QACpB,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,OAAO,KAAK,CAAC,kBAAkB;gBAAE,OAAO;YAAO;YACnD,IAAI,OAAO,KAAK,CAAC,aAAa;gBAAE,OAAO;YAAO;QAChD;QACA,IAAI,OAAO,KAAK,CAAC,cAAc;YAAE,OAAO;QAAU;QAClD,OAAO,IAAI;QACX,OAAO;IACT;AACF;AAEA,yCAAyC;AAEzC,IAAI,OAAO;AAEX,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,uBAAuB,yBAAyB,0BAA0B;AAC9E,IAAI,sBAAsB,kBAAkB,0BAA0B;AACtE,IAAI,YAAY,2CAA2C,mCAAmC;AAEvF,MAAM,UAAU;IACrB,MAAM;IACN,YAAY;QACV,OAAO;YACL,aAAa;YACb,iBAAiB;YACjB,SAAS;QACX;IACF;IACA,OAAO,SAAU,MAAM,EAAE,KAAK;QAC5B,IAAI,KAAK,OAAO,IAAI;QACpB,IAAI,MAAM,KAAK;YAAE,OAAO,SAAS;YAAI,OAAO;QAAW;QAEvD,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,OAAO,KAAK,CAAC,WAAW;gBAAE,OAAO;YAAU;YAC/C,IAAI,OAAO,KAAK,CAAC,UAAU;gBAAE,OAAO;YAAQ;QAC9C;QAEA,IAAI,OAAO,KAAK,CAAC,WAAW;YAAE,OAAO;QAAO,EAAE,mCAAmC;QACjF,IAAI,OAAO,KAAK,CAAC,eAAe;YAAE,OAAO;QAAO,EAAE,qCAAqC;QAEvF,IAAI,OAAO,KAAK,CAAC,sBAAsB;YAAE,OAAO;QAAW;QAC3D,IAAI,OAAO,KAAK,CAAC,uBAAuB;YACtC,MAAM,WAAW,GAAG;YACpB,OAAO;QACT;QACA,IAAI,MAAM,WAAW,EAAE;YACrB,IAAI,OAAO,KAAK,CAAC,YAAY;gBAAE,OAAO;YAAY;YAClD,IAAI,OAAO,KAAK,CAAC,WAAW;gBAAE,OAAO;YAAU;YAC/C,IAAI,OAAO,GAAG,IAAI;gBAAE,MAAM,WAAW,GAAG;YAAO;QACjD;QAEA,IAAI,OAAO,KAAK,CAAC,OAAO;YACtB,IAAI,OAAO,GAAG,IAAI;gBAAE,MAAM,WAAW,GAAG;YAAO;YAC/C,OAAO;QACT;QAEA,yDAAyD;QACzD,IAAI,OAAO,KAAK,CAAC,YAAY;YAC3B,IAAI,OAAO,KAAK,CAAC,MAAM;gBAAE,MAAM,eAAe,GAAG;YAAM;YACvD,OAAO;QACT;QACA,IAAI,MAAM,eAAe,EAAE;YACzB,IAAI,OAAO,KAAK,CAAC,SAAS;gBAAE,OAAO;YAAS;YAC5C,IAAI,OAAO,KAAK,CAAC,MAAM;gBACrB,MAAM,eAAe,GAAG;gBACxB,OAAO;YACT;QACF;QAEA,kCAAkC;QAClC,IAAI,OAAO,KAAK,CAAC,0BAA0B;YACzC,IAAI,OAAO,GAAG,IAAI;gBAAE,MAAM,WAAW,GAAG;YAAO;YAC/C,OAAO;QACT;QAEA,OAAO,IAAI;QACX,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}