{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/haskell.js"], "sourcesContent": ["function switchState(source, setState, f) {\n  setState(f);\n  return f(source, setState);\n}\n\n// These should all be Unicode extended, as per the Haskell 2010 report\nvar smallRE = /[a-z_]/;\nvar largeRE = /[A-Z]/;\nvar digitRE = /\\d/;\nvar hexitRE = /[0-9A-Fa-f]/;\nvar octitRE = /[0-7]/;\nvar idRE = /[a-z_A-Z0-9'\\xa1-\\uffff]/;\nvar symbolRE = /[-!#$%&*+.\\/<=>?@\\\\^|~:]/;\nvar specialRE = /[(),;[\\]`{}]/;\nvar whiteCharRE = /[ \\t\\v\\f]/; // newlines are handled in tokenizer\n\nfunction normal(source, setState) {\n  if (source.eatWhile(whiteCharRE)) {\n    return null;\n  }\n\n  var ch = source.next();\n  if (specialRE.test(ch)) {\n    if (ch == '{' && source.eat('-')) {\n      var t = \"comment\";\n      if (source.eat('#')) {\n        t = \"meta\";\n      }\n      return switchState(source, setState, ncomment(t, 1));\n    }\n    return null;\n  }\n\n  if (ch == '\\'') {\n    if (source.eat('\\\\')) {\n      source.next();  // should handle other escapes here\n    }\n    else {\n      source.next();\n    }\n    if (source.eat('\\'')) {\n      return \"string\";\n    }\n    return \"error\";\n  }\n\n  if (ch == '\"') {\n    return switchState(source, setState, stringLiteral);\n  }\n\n  if (largeRE.test(ch)) {\n    source.eatWhile(idRE);\n    if (source.eat('.')) {\n      return \"qualifier\";\n    }\n    return \"type\";\n  }\n\n  if (smallRE.test(ch)) {\n    source.eatWhile(idRE);\n    return \"variable\";\n  }\n\n  if (digitRE.test(ch)) {\n    if (ch == '0') {\n      if (source.eat(/[xX]/)) {\n        source.eatWhile(hexitRE); // should require at least 1\n        return \"integer\";\n      }\n      if (source.eat(/[oO]/)) {\n        source.eatWhile(octitRE); // should require at least 1\n        return \"number\";\n      }\n    }\n    source.eatWhile(digitRE);\n    var t = \"number\";\n    if (source.match(/^\\.\\d+/)) {\n      t = \"number\";\n    }\n    if (source.eat(/[eE]/)) {\n      t = \"number\";\n      source.eat(/[-+]/);\n      source.eatWhile(digitRE); // should require at least 1\n    }\n    return t;\n  }\n\n  if (ch == \".\" && source.eat(\".\"))\n    return \"keyword\";\n\n  if (symbolRE.test(ch)) {\n    if (ch == '-' && source.eat(/-/)) {\n      source.eatWhile(/-/);\n      if (!source.eat(symbolRE)) {\n        source.skipToEnd();\n        return \"comment\";\n      }\n    }\n    source.eatWhile(symbolRE);\n    return \"variable\"\n  }\n\n  return \"error\";\n}\n\nfunction ncomment(type, nest) {\n  if (nest == 0) {\n    return normal;\n  }\n  return function(source, setState) {\n    var currNest = nest;\n    while (!source.eol()) {\n      var ch = source.next();\n      if (ch == '{' && source.eat('-')) {\n        ++currNest;\n      }\n      else if (ch == '-' && source.eat('}')) {\n        --currNest;\n        if (currNest == 0) {\n          setState(normal);\n          return type;\n        }\n      }\n    }\n    setState(ncomment(type, currNest));\n    return type;\n  };\n}\n\nfunction stringLiteral(source, setState) {\n  while (!source.eol()) {\n    var ch = source.next();\n    if (ch == '\"') {\n      setState(normal);\n      return \"string\";\n    }\n    if (ch == '\\\\') {\n      if (source.eol() || source.eat(whiteCharRE)) {\n        setState(stringGap);\n        return \"string\";\n      }\n      if (source.eat('&')) {\n      }\n      else {\n        source.next(); // should handle other escapes here\n      }\n    }\n  }\n  setState(normal);\n  return \"error\";\n}\n\nfunction stringGap(source, setState) {\n  if (source.eat('\\\\')) {\n    return switchState(source, setState, stringLiteral);\n  }\n  source.next();\n  setState(normal);\n  return \"error\";\n}\n\n\nvar wellKnownWords = (function() {\n  var wkw = {};\n  function setType(t) {\n    return function () {\n      for (var i = 0; i < arguments.length; i++)\n        wkw[arguments[i]] = t;\n    };\n  }\n\n  setType(\"keyword\")(\n    \"case\", \"class\", \"data\", \"default\", \"deriving\", \"do\", \"else\", \"foreign\",\n    \"if\", \"import\", \"in\", \"infix\", \"infixl\", \"infixr\", \"instance\", \"let\",\n    \"module\", \"newtype\", \"of\", \"then\", \"type\", \"where\", \"_\");\n\n  setType(\"keyword\")(\n    \"\\.\\.\", \":\", \"::\", \"=\", \"\\\\\", \"<-\", \"->\", \"@\", \"~\", \"=>\");\n\n  setType(\"builtin\")(\n    \"!!\", \"$!\", \"$\", \"&&\", \"+\", \"++\", \"-\", \".\", \"/\", \"/=\", \"<\", \"<*\", \"<=\",\n    \"<$>\", \"<*>\", \"=<<\", \"==\", \">\", \">=\", \">>\", \">>=\", \"^\", \"^^\", \"||\", \"*\",\n    \"*>\", \"**\");\n\n  setType(\"builtin\")(\n    \"Applicative\", \"Bool\", \"Bounded\", \"Char\", \"Double\", \"EQ\", \"Either\", \"Enum\",\n    \"Eq\", \"False\", \"FilePath\", \"Float\", \"Floating\", \"Fractional\", \"Functor\",\n    \"GT\", \"IO\", \"IOError\", \"Int\", \"Integer\", \"Integral\", \"Just\", \"LT\", \"Left\",\n    \"Maybe\", \"Monad\", \"Nothing\", \"Num\", \"Ord\", \"Ordering\", \"Rational\", \"Read\",\n    \"ReadS\", \"Real\", \"RealFloat\", \"RealFrac\", \"Right\", \"Show\", \"ShowS\",\n    \"String\", \"True\");\n\n  setType(\"builtin\")(\n    \"abs\", \"acos\", \"acosh\", \"all\", \"and\", \"any\", \"appendFile\", \"asTypeOf\",\n    \"asin\", \"asinh\", \"atan\", \"atan2\", \"atanh\", \"break\", \"catch\", \"ceiling\",\n    \"compare\", \"concat\", \"concatMap\", \"const\", \"cos\", \"cosh\", \"curry\",\n    \"cycle\", \"decodeFloat\", \"div\", \"divMod\", \"drop\", \"dropWhile\", \"either\",\n    \"elem\", \"encodeFloat\", \"enumFrom\", \"enumFromThen\", \"enumFromThenTo\",\n    \"enumFromTo\", \"error\", \"even\", \"exp\", \"exponent\", \"fail\", \"filter\",\n    \"flip\", \"floatDigits\", \"floatRadix\", \"floatRange\", \"floor\", \"fmap\",\n    \"foldl\", \"foldl1\", \"foldr\", \"foldr1\", \"fromEnum\", \"fromInteger\",\n    \"fromIntegral\", \"fromRational\", \"fst\", \"gcd\", \"getChar\", \"getContents\",\n    \"getLine\", \"head\", \"id\", \"init\", \"interact\", \"ioError\", \"isDenormalized\",\n    \"isIEEE\", \"isInfinite\", \"isNaN\", \"isNegativeZero\", \"iterate\", \"last\",\n    \"lcm\", \"length\", \"lex\", \"lines\", \"log\", \"logBase\", \"lookup\", \"map\",\n    \"mapM\", \"mapM_\", \"max\", \"maxBound\", \"maximum\", \"maybe\", \"min\", \"minBound\",\n    \"minimum\", \"mod\", \"negate\", \"not\", \"notElem\", \"null\", \"odd\", \"or\",\n    \"otherwise\", \"pi\", \"pred\", \"print\", \"product\", \"properFraction\", \"pure\",\n    \"putChar\", \"putStr\", \"putStrLn\", \"quot\", \"quotRem\", \"read\", \"readFile\",\n    \"readIO\", \"readList\", \"readLn\", \"readParen\", \"reads\", \"readsPrec\",\n    \"realToFrac\", \"recip\", \"rem\", \"repeat\", \"replicate\", \"return\", \"reverse\",\n    \"round\", \"scaleFloat\", \"scanl\", \"scanl1\", \"scanr\", \"scanr1\", \"seq\",\n    \"sequence\", \"sequence_\", \"show\", \"showChar\", \"showList\", \"showParen\",\n    \"showString\", \"shows\", \"showsPrec\", \"significand\", \"signum\", \"sin\",\n    \"sinh\", \"snd\", \"span\", \"splitAt\", \"sqrt\", \"subtract\", \"succ\", \"sum\",\n    \"tail\", \"take\", \"takeWhile\", \"tan\", \"tanh\", \"toEnum\", \"toInteger\",\n    \"toRational\", \"truncate\", \"uncurry\", \"undefined\", \"unlines\", \"until\",\n    \"unwords\", \"unzip\", \"unzip3\", \"userError\", \"words\", \"writeFile\", \"zip\",\n    \"zip3\", \"zipWith\", \"zipWith3\");\n\n  return wkw;\n})();\n\nexport const haskell = {\n  name: \"haskell\",\n  startState: function ()  { return { f: normal }; },\n  copyState:  function (s) { return { f: s.f }; },\n\n  token: function(stream, state) {\n    var t = state.f(stream, function(s) { state.f = s; });\n    var w = stream.current();\n    return wellKnownWords.hasOwnProperty(w) ? wellKnownWords[w] : t;\n  },\n\n  languageData: {\n    commentTokens: {line: \"--\", block: {open: \"{-\", close: \"-}\"}}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,YAAY,MAAM,EAAE,QAAQ,EAAE,CAAC;IACtC,SAAS;IACT,OAAO,EAAE,QAAQ;AACnB;AAEA,uEAAuE;AACvE,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,cAAc,aAAa,oCAAoC;AAEnE,SAAS,OAAO,MAAM,EAAE,QAAQ;IAC9B,IAAI,OAAO,QAAQ,CAAC,cAAc;QAChC,OAAO;IACT;IAEA,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,UAAU,IAAI,CAAC,KAAK;QACtB,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;YAChC,IAAI,IAAI;YACR,IAAI,OAAO,GAAG,CAAC,MAAM;gBACnB,IAAI;YACN;YACA,OAAO,YAAY,QAAQ,UAAU,SAAS,GAAG;QACnD;QACA,OAAO;IACT;IAEA,IAAI,MAAM,MAAM;QACd,IAAI,OAAO,GAAG,CAAC,OAAO;YACpB,OAAO,IAAI,IAAK,mCAAmC;QACrD,OACK;YACH,OAAO,IAAI;QACb;QACA,IAAI,OAAO,GAAG,CAAC,OAAO;YACpB,OAAO;QACT;QACA,OAAO;IACT;IAEA,IAAI,MAAM,KAAK;QACb,OAAO,YAAY,QAAQ,UAAU;IACvC;IAEA,IAAI,QAAQ,IAAI,CAAC,KAAK;QACpB,OAAO,QAAQ,CAAC;QAChB,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO;QACT;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,CAAC,KAAK;QACpB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,CAAC,KAAK;QACpB,IAAI,MAAM,KAAK;YACb,IAAI,OAAO,GAAG,CAAC,SAAS;gBACtB,OAAO,QAAQ,CAAC,UAAU,4BAA4B;gBACtD,OAAO;YACT;YACA,IAAI,OAAO,GAAG,CAAC,SAAS;gBACtB,OAAO,QAAQ,CAAC,UAAU,4BAA4B;gBACtD,OAAO;YACT;QACF;QACA,OAAO,QAAQ,CAAC;QAChB,IAAI,IAAI;QACR,IAAI,OAAO,KAAK,CAAC,WAAW;YAC1B,IAAI;QACN;QACA,IAAI,OAAO,GAAG,CAAC,SAAS;YACtB,IAAI;YACJ,OAAO,GAAG,CAAC;YACX,OAAO,QAAQ,CAAC,UAAU,4BAA4B;QACxD;QACA,OAAO;IACT;IAEA,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAC1B,OAAO;IAET,IAAI,SAAS,IAAI,CAAC,KAAK;QACrB,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;YAChC,OAAO,QAAQ,CAAC;YAChB,IAAI,CAAC,OAAO,GAAG,CAAC,WAAW;gBACzB,OAAO,SAAS;gBAChB,OAAO;YACT;QACF;QACA,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,IAAI,EAAE,IAAI;IAC1B,IAAI,QAAQ,GAAG;QACb,OAAO;IACT;IACA,OAAO,SAAS,MAAM,EAAE,QAAQ;QAC9B,IAAI,WAAW;QACf,MAAO,CAAC,OAAO,GAAG,GAAI;YACpB,IAAI,KAAK,OAAO,IAAI;YACpB,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;gBAChC,EAAE;YACJ,OACK,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;gBACrC,EAAE;gBACF,IAAI,YAAY,GAAG;oBACjB,SAAS;oBACT,OAAO;gBACT;YACF;QACF;QACA,SAAS,SAAS,MAAM;QACxB,OAAO;IACT;AACF;AAEA,SAAS,cAAc,MAAM,EAAE,QAAQ;IACrC,MAAO,CAAC,OAAO,GAAG,GAAI;QACpB,IAAI,KAAK,OAAO,IAAI;QACpB,IAAI,MAAM,KAAK;YACb,SAAS;YACT,OAAO;QACT;QACA,IAAI,MAAM,MAAM;YACd,IAAI,OAAO,GAAG,MAAM,OAAO,GAAG,CAAC,cAAc;gBAC3C,SAAS;gBACT,OAAO;YACT;YACA,IAAI,OAAO,GAAG,CAAC,MAAM,CACrB,OACK;gBACH,OAAO,IAAI,IAAI,mCAAmC;YACpD;QACF;IACF;IACA,SAAS;IACT,OAAO;AACT;AAEA,SAAS,UAAU,MAAM,EAAE,QAAQ;IACjC,IAAI,OAAO,GAAG,CAAC,OAAO;QACpB,OAAO,YAAY,QAAQ,UAAU;IACvC;IACA,OAAO,IAAI;IACX,SAAS;IACT,OAAO;AACT;AAGA,IAAI,iBAAiB,AAAC;IACpB,IAAI,MAAM,CAAC;IACX,SAAS,QAAQ,CAAC;QAChB,OAAO;YACL,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IACpC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG;QACxB;IACF;IAEA,QAAQ,WACN,QAAQ,SAAS,QAAQ,WAAW,YAAY,MAAM,QAAQ,WAC9D,MAAM,UAAU,MAAM,SAAS,UAAU,UAAU,YAAY,OAC/D,UAAU,WAAW,MAAM,QAAQ,QAAQ,SAAS;IAEtD,QAAQ,WACN,QAAQ,KAAK,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,KAAK;IAEtD,QAAQ,WACN,MAAM,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM,MAClE,OAAO,OAAO,OAAO,MAAM,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM,MAAM,KACpE,MAAM;IAER,QAAQ,WACN,eAAe,QAAQ,WAAW,QAAQ,UAAU,MAAM,UAAU,QACpE,MAAM,SAAS,YAAY,SAAS,YAAY,cAAc,WAC9D,MAAM,MAAM,WAAW,OAAO,WAAW,YAAY,QAAQ,MAAM,QACnE,SAAS,SAAS,WAAW,OAAO,OAAO,YAAY,YAAY,QACnE,SAAS,QAAQ,aAAa,YAAY,SAAS,QAAQ,SAC3D,UAAU;IAEZ,QAAQ,WACN,OAAO,QAAQ,SAAS,OAAO,OAAO,OAAO,cAAc,YAC3D,QAAQ,SAAS,QAAQ,SAAS,SAAS,SAAS,SAAS,WAC7D,WAAW,UAAU,aAAa,SAAS,OAAO,QAAQ,SAC1D,SAAS,eAAe,OAAO,UAAU,QAAQ,aAAa,UAC9D,QAAQ,eAAe,YAAY,gBAAgB,kBACnD,cAAc,SAAS,QAAQ,OAAO,YAAY,QAAQ,UAC1D,QAAQ,eAAe,cAAc,cAAc,SAAS,QAC5D,SAAS,UAAU,SAAS,UAAU,YAAY,eAClD,gBAAgB,gBAAgB,OAAO,OAAO,WAAW,eACzD,WAAW,QAAQ,MAAM,QAAQ,YAAY,WAAW,kBACxD,UAAU,cAAc,SAAS,kBAAkB,WAAW,QAC9D,OAAO,UAAU,OAAO,SAAS,OAAO,WAAW,UAAU,OAC7D,QAAQ,SAAS,OAAO,YAAY,WAAW,SAAS,OAAO,YAC/D,WAAW,OAAO,UAAU,OAAO,WAAW,QAAQ,OAAO,MAC7D,aAAa,MAAM,QAAQ,SAAS,WAAW,kBAAkB,QACjE,WAAW,UAAU,YAAY,QAAQ,WAAW,QAAQ,YAC5D,UAAU,YAAY,UAAU,aAAa,SAAS,aACtD,cAAc,SAAS,OAAO,UAAU,aAAa,UAAU,WAC/D,SAAS,cAAc,SAAS,UAAU,SAAS,UAAU,OAC7D,YAAY,aAAa,QAAQ,YAAY,YAAY,aACzD,cAAc,SAAS,aAAa,eAAe,UAAU,OAC7D,QAAQ,OAAO,QAAQ,WAAW,QAAQ,YAAY,QAAQ,OAC9D,QAAQ,QAAQ,aAAa,OAAO,QAAQ,UAAU,aACtD,cAAc,YAAY,WAAW,aAAa,WAAW,SAC7D,WAAW,SAAS,UAAU,aAAa,SAAS,aAAa,OACjE,QAAQ,WAAW;IAErB,OAAO;AACT;AAEO,MAAM,UAAU;IACrB,MAAM;IACN,YAAY;QAAe,OAAO;YAAE,GAAG;QAAO;IAAG;IACjD,WAAY,SAAU,CAAC;QAAI,OAAO;YAAE,GAAG,EAAE,CAAC;QAAC;IAAG;IAE9C,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,IAAI,MAAM,CAAC,CAAC,QAAQ,SAAS,CAAC;YAAI,MAAM,CAAC,GAAG;QAAG;QACnD,IAAI,IAAI,OAAO,OAAO;QACtB,OAAO,eAAe,cAAc,CAAC,KAAK,cAAc,CAAC,EAAE,GAAG;IAChE;IAEA,cAAc;QACZ,eAAe;YAAC,MAAM;YAAM,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;IAC9D;AACF", "ignoreList": [0], "debugId": null}}]}