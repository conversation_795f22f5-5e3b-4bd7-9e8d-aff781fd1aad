{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/fcl.js"], "sourcesContent": ["var keywords = {\n  \"term\": true,\n  \"method\": true, \"accu\": true,\n  \"rule\": true, \"then\": true, \"is\": true, \"and\": true, \"or\": true,\n  \"if\": true, \"default\": true\n};\n\nvar start_blocks = {\n  \"var_input\": true,\n  \"var_output\": true,\n  \"fuzzify\": true,\n  \"defuzzify\": true,\n  \"function_block\": true,\n  \"ruleblock\": true\n};\n\nvar end_blocks = {\n  \"end_ruleblock\": true,\n  \"end_defuzzify\": true,\n  \"end_function_block\": true,\n  \"end_fuzzify\": true,\n  \"end_var\": true\n};\n\nvar atoms = {\n  \"true\": true, \"false\": true, \"nan\": true,\n  \"real\": true, \"min\": true, \"max\": true, \"cog\": true, \"cogs\": true\n};\n\nvar isOperatorChar = /[+\\-*&^%:=<>!|\\/]/;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n\n  if (/[\\d\\.]/.test(ch)) {\n    if (ch == \".\") {\n      stream.match(/^[0-9]+([eE][\\-+]?[0-9]+)?/);\n    } else if (ch == \"0\") {\n      stream.match(/^[xX][0-9a-fA-F]+/) || stream.match(/^0[0-7]+/);\n    } else {\n      stream.match(/^[0-9]*\\.?[0-9]*([eE][\\-+]?[0-9]+)?/);\n    }\n    return \"number\";\n  }\n\n  if (ch == \"/\" || ch == \"(\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n\n  var cur = stream.current().toLowerCase();\n  if (keywords.propertyIsEnumerable(cur) ||\n      start_blocks.propertyIsEnumerable(cur) ||\n      end_blocks.propertyIsEnumerable(cur)) {\n    return \"keyword\";\n  }\n  if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  return \"variable\";\n}\n\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if ((ch == \"/\" || ch == \")\") && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\n\nfunction pushContext(state, col, type) {\n  return state.context = new Context(state.indented, col, type, null, state.context);\n}\n\nfunction popContext(state) {\n  if (!state.context.prev) return;\n  var t = state.context.type;\n  if (t == \"end_block\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n// Interface\n\nexport const fcl = {\n  name: \"fcl\",\n  startState: function(indentUnit) {\n    return {\n      tokenize: null,\n      context: new Context(-indentUnit, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    var cur = stream.current().toLowerCase();\n\n    if (start_blocks.propertyIsEnumerable(cur)) pushContext(state, stream.column(), \"end_block\");\n    else if (end_blocks.propertyIsEnumerable(cur))  popContext(state);\n\n    state.startOfLine = false;\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase && state.tokenize != null) return 0;\n    var ctx = state.context;\n\n    var closing = end_blocks.propertyIsEnumerable(textAfter);\n    if (ctx.align) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indented + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    commentTokens: {line: \"//\", block: {open: \"(*\", close: \"*)\"}}\n  }\n};\n\n"], "names": [], "mappings": ";;;AAAA,IAAI,WAAW;IACb,QAAQ;IACR,UAAU;IAAM,QAAQ;IACxB,QAAQ;IAAM,QAAQ;IAAM,MAAM;IAAM,OAAO;IAAM,MAAM;IAC3D,MAAM;IAAM,WAAW;AACzB;AAEA,IAAI,eAAe;IACjB,aAAa;IACb,cAAc;IACd,WAAW;IACX,aAAa;IACb,kBAAkB;IAClB,aAAa;AACf;AAEA,IAAI,aAAa;IACf,iBAAiB;IACjB,iBAAiB;IACjB,sBAAsB;IACtB,eAAe;IACf,WAAW;AACb;AAEA,IAAI,QAAQ;IACV,QAAQ;IAAM,SAAS;IAAM,OAAO;IACpC,QAAQ;IAAM,OAAO;IAAM,OAAO;IAAM,OAAO;IAAM,QAAQ;AAC/D;AAEA,IAAI,iBAAiB;AAErB,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IAEpB,IAAI,SAAS,IAAI,CAAC,KAAK;QACrB,IAAI,MAAM,KAAK;YACb,OAAO,KAAK,CAAC;QACf,OAAO,IAAI,MAAM,KAAK;YACpB,OAAO,KAAK,CAAC,wBAAwB,OAAO,KAAK,CAAC;QACpD,OAAO;YACL,OAAO,KAAK,CAAC;QACf;QACA,OAAO;IACT;IAEA,IAAI,MAAM,OAAO,MAAM,KAAK;QAC1B,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,MAAM,QAAQ,GAAG;YACjB,OAAO,aAAa,QAAQ;QAC9B;QACA,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO,SAAS;YAChB,OAAO;QACT;IACF;IACA,IAAI,eAAe,IAAI,CAAC,KAAK;QAC3B,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,OAAO,QAAQ,CAAC;IAEhB,IAAI,MAAM,OAAO,OAAO,GAAG,WAAW;IACtC,IAAI,SAAS,oBAAoB,CAAC,QAC9B,aAAa,oBAAoB,CAAC,QAClC,WAAW,oBAAoB,CAAC,MAAM;QACxC,OAAO;IACT;IACA,IAAI,MAAM,oBAAoB,CAAC,MAAM,OAAO;IAC5C,OAAO;AACT;AAGA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,WAAW,OAAO;IACtB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,CAAC,MAAM,OAAO,MAAM,GAAG,KAAK,UAAU;YACxC,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,SAAS,QAAQ,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;IAClD,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,SAAS,YAAY,KAAK,EAAE,GAAG,EAAE,IAAI;IACnC,OAAO,MAAM,OAAO,GAAG,IAAI,QAAQ,MAAM,QAAQ,EAAE,KAAK,MAAM,MAAM,MAAM,OAAO;AACnF;AAEA,SAAS,WAAW,KAAK;IACvB,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,EAAE;IACzB,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI;IAC1B,IAAI,KAAK,aACP,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ;IACzC,OAAO,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;AAC3C;AAIO,MAAM,MAAM;IACjB,MAAM;IACN,YAAY,SAAS,UAAU;QAC7B,OAAO;YACL,UAAU;YACV,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,OAAO;YAC5C,UAAU;YACV,aAAa;QACf;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,MAAM,MAAM,OAAO;QACvB,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;YACnC,MAAM,QAAQ,GAAG,OAAO,WAAW;YACnC,MAAM,WAAW,GAAG;QACtB;QACA,IAAI,OAAO,QAAQ,IAAI,OAAO;QAE9B,IAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,SAAS,EAAE,QAAQ;QAClD,IAAI,SAAS,WAAW,OAAO;QAC/B,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;QAEnC,IAAI,MAAM,OAAO,OAAO,GAAG,WAAW;QAEtC,IAAI,aAAa,oBAAoB,CAAC,MAAM,YAAY,OAAO,OAAO,MAAM,IAAI;aAC3E,IAAI,WAAW,oBAAoB,CAAC,MAAO,WAAW;QAE3D,MAAM,WAAW,GAAG;QACpB,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,MAAM,QAAQ,IAAI,aAAa,MAAM,QAAQ,IAAI,MAAM,OAAO;QAClE,IAAI,MAAM,MAAM,OAAO;QAEvB,IAAI,UAAU,WAAW,oBAAoB,CAAC;QAC9C,IAAI,IAAI,KAAK,EAAE,OAAO,IAAI,MAAM,GAAG,CAAC,UAAU,IAAI,CAAC;aAC9C,OAAO,IAAI,QAAQ,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI;IACnD;IAEA,cAAc;QACZ,eAAe;YAAC,MAAM;YAAM,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;IAC9D;AACF", "ignoreList": [0], "debugId": null}}]}