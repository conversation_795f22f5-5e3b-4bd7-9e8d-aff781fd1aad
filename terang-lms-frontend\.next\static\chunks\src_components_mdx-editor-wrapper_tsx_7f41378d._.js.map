{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/mdx-editor-wrapper.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { toast } from 'sonner';\r\n\r\n// Import MDX Editor and plugins dynamically to avoid SSR issues\r\nimport {\r\n  MDXEditor,\r\n  headingsPlugin,\r\n  listsPlugin,\r\n  quotePlugin,\r\n  thematicBreakPlugin,\r\n  markdownShortcutPlugin,\r\n  linkPlugin,\r\n  linkDialogPlugin,\r\n  imagePlugin,\r\n  tablePlugin,\r\n  codeBlockPlugin,\r\n  codeMirrorPlugin,\r\n  toolbarPlugin,\r\n  UndoRedo,\r\n  BoldItalicUnderlineToggles,\r\n  CreateLink,\r\n  InsertImage,\r\n  InsertTable,\r\n  InsertThematicBreak,\r\n  ListsToggle,\r\n  BlockTypeSelect,\r\n  CodeToggle,\r\n  InsertCodeBlock,\r\n} from '@mdxeditor/editor';\r\n\r\ninterface MDXEditorWrapperProps {\r\n  markdown: string;\r\n  onChange: (markdown: string) => void;\r\n  placeholder?: string;\r\n  className?: string;\r\n}\r\n\r\nexport default function MDXEditorWrapper({\r\n  markdown,\r\n  onChange,\r\n  placeholder = \"Enter your content here...\",\r\n  className = \"min-h-[200px]\",\r\n}: MDXEditorWrapperProps) {\r\n  return (\r\n    <MDXEditor\r\n      markdown={markdown}\r\n      onChange={onChange}\r\n      contentEditableClassName='prose max-w-none'\r\n      plugins={[\r\n        headingsPlugin(),\r\n        listsPlugin(),\r\n        quotePlugin(),\r\n        thematicBreakPlugin(),\r\n        markdownShortcutPlugin(),\r\n        linkPlugin(),\r\n        linkDialogPlugin(),\r\n        imagePlugin({\r\n          imageUploadHandler: async (image) => {\r\n            // Handle image upload\r\n            try {\r\n              const response = await fetch(`/api/upload?filename=${image.name}`, {\r\n                method: 'POST',\r\n                body: image,\r\n              });\r\n\r\n              if (!response.ok) {\r\n                throw new Error('Upload failed');\r\n              }\r\n\r\n              const result = await response.json();\r\n              return result.url;\r\n            } catch (error) {\r\n              console.error('Image upload failed:', error);\r\n              toast.error('Failed to upload image');\r\n              return '';\r\n            }\r\n          },\r\n        }),\r\n        tablePlugin(),\r\n        codeBlockPlugin({ defaultCodeBlockLanguage: 'javascript' }),\r\n        codeMirrorPlugin({\r\n          codeBlockLanguages: {\r\n            javascript: 'JavaScript',\r\n            typescript: 'TypeScript',\r\n            python: 'Python',\r\n            html: 'HTML',\r\n            css: 'CSS',\r\n            json: 'JSON',\r\n            markdown: 'Markdown',\r\n          },\r\n        }),\r\n        toolbarPlugin({\r\n          toolbarContents: () => (\r\n            <>\r\n              <UndoRedo />\r\n              <BoldItalicUnderlineToggles />\r\n              <CodeToggle />\r\n              <BlockTypeSelect />\r\n              <CreateLink />\r\n              <InsertImage />\r\n              <ListsToggle />\r\n              <InsertTable />\r\n              <InsertThematicBreak />\r\n              <InsertCodeBlock />\r\n            </>\r\n          ),\r\n        }),\r\n      ]}\r\n      placeholder={placeholder}\r\n      className={className}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA,gEAAgE;AAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;AAuCe,SAAS,iBAAiB,EACvC,QAAQ,EACR,QAAQ,EACR,cAAc,4BAA4B,EAC1C,YAAY,eAAe,EACL;IACtB,qBACE,6LAAC,6JAAA,CAAA,YAAS;QACR,UAAU;QACV,UAAU;QACV,0BAAyB;QACzB,SAAS;YACP,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD;YACb,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD;YACV,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD;YACV,CAAA,GAAA,yLAAA,CAAA,sBAAmB,AAAD;YAClB,CAAA,GAAA,4LAAA,CAAA,yBAAsB,AAAD;YACrB,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD;YACT,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD;YACf,CAAA,GAAA,6LAAA,CAAA,cAAW,AAAD,EAAE;gBACV,oBAAoB,OAAO;oBACzB,sBAAsB;oBACtB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,MAAM,IAAI,EAAE,EAAE;4BACjE,QAAQ;4BACR,MAAM;wBACR;wBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM;wBAClB;wBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;wBAClC,OAAO,OAAO,GAAG;oBACnB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,OAAO;oBACT;gBACF;YACF;YACA,CAAA,GAAA,6LAAA,CAAA,cAAW,AAAD;YACV,CAAA,GAAA,iMAAA,CAAA,kBAAe,AAAD,EAAE;gBAAE,0BAA0B;YAAa;YACzD,CAAA,GAAA,kLAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,oBAAoB;oBAClB,YAAY;oBACZ,YAAY;oBACZ,QAAQ;oBACR,MAAM;oBACN,KAAK;oBACL,MAAM;oBACN,UAAU;gBACZ;YACF;YACA,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE;gBACZ,iBAAiB,kBACf;;0CACE,6LAAC,gMAAA,CAAA,WAAQ;;;;;0CACT,6LAAC,kNAAA,CAAA,6BAA0B;;;;;0CAC3B,6LAAC,kMAAA,CAAA,aAAU;;;;;0CACX,6LAAC,uMAAA,CAAA,kBAAe;;;;;0CAChB,6LAAC,kMAAA,CAAA,aAAU;;;;;0CACX,6LAAC,mMAAA,CAAA,cAAW;;;;;0CACZ,6LAAC,mMAAA,CAAA,cAAW;;;;;0CACZ,6LAAC,mMAAA,CAAA,cAAW;;;;;0CACZ,6LAAC,2MAAA,CAAA,sBAAmB;;;;;0CACpB,6LAAC,uMAAA,CAAA,kBAAe;;;;;;;YAGtB;SACD;QACD,aAAa;QACb,WAAW;;;;;;AAGjB;KA3EwB", "debugId": null}}]}