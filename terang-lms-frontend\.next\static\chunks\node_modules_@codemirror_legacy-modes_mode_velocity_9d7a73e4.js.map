{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/velocity.js"], "sourcesContent": ["function parseWords(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar keywords = parseWords(\"#end #else #break #stop #[[ #]] \" +\n                          \"#{end} #{else} #{break} #{stop}\");\nvar functions = parseWords(\"#if #elseif #foreach #set #include #parse #macro #define #evaluate \" +\n                           \"#{if} #{elseif} #{foreach} #{set} #{include} #{parse} #{macro} #{define} #{evaluate}\");\nvar specials = parseWords(\"$foreach.count $foreach.hasNext $foreach.first $foreach.last $foreach.topmost $foreach.parent.count $foreach.parent.hasNext $foreach.parent.first $foreach.parent.last $foreach.parent $velocityCount $!bodyContent $bodyContent\");\nvar isOperatorChar = /[+\\-*&%=<>!?:\\/|]/;\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\nfunction tokenBase(stream, state) {\n  var beforeParams = state.beforeParams;\n  state.beforeParams = false;\n  var ch = stream.next();\n  // start of unparsed string?\n  if ((ch == \"'\") && !state.inString && state.inParams) {\n    state.lastTokenWasBuiltin = false;\n    return chain(stream, state, tokenString(ch));\n  }\n  // start of parsed string?\n  else if ((ch == '\"')) {\n    state.lastTokenWasBuiltin = false;\n    if (state.inString) {\n      state.inString = false;\n      return \"string\";\n    }\n    else if (state.inParams)\n      return chain(stream, state, tokenString(ch));\n  }\n  // is it one of the special signs []{}().,;? Separator?\n  else if (/[\\[\\]{}\\(\\),;\\.]/.test(ch)) {\n    if (ch == \"(\" && beforeParams)\n      state.inParams = true;\n    else if (ch == \")\") {\n      state.inParams = false;\n      state.lastTokenWasBuiltin = true;\n    }\n    return null;\n  }\n  // start of a number value?\n  else if (/\\d/.test(ch)) {\n    state.lastTokenWasBuiltin = false;\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  // multi line comment?\n  else if (ch == \"#\" && stream.eat(\"*\")) {\n    state.lastTokenWasBuiltin = false;\n    return chain(stream, state, tokenComment);\n  }\n  // unparsed content?\n  else if (ch == \"#\" && stream.match(/ *\\[ *\\[/)) {\n    state.lastTokenWasBuiltin = false;\n    return chain(stream, state, tokenUnparsed);\n  }\n  // single line comment?\n  else if (ch == \"#\" && stream.eat(\"#\")) {\n    state.lastTokenWasBuiltin = false;\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  // variable?\n  else if (ch == \"$\") {\n    stream.eat(\"!\");\n    stream.eatWhile(/[\\w\\d\\$_\\.{}-]/);\n    // is it one of the specials?\n    if (specials && specials.propertyIsEnumerable(stream.current())) {\n      return \"keyword\";\n    }\n    else {\n      state.lastTokenWasBuiltin = true;\n      state.beforeParams = true;\n      return \"builtin\";\n    }\n  }\n  // is it a operator?\n  else if (isOperatorChar.test(ch)) {\n    state.lastTokenWasBuiltin = false;\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  else {\n    // get the whole word\n    stream.eatWhile(/[\\w\\$_{}@]/);\n    var word = stream.current();\n    // is it one of the listed keywords?\n    if (keywords && keywords.propertyIsEnumerable(word))\n      return \"keyword\";\n    // is it one of the listed functions?\n    if (functions && functions.propertyIsEnumerable(word) ||\n        (stream.current().match(/^#@?[a-z0-9_]+ *$/i) && stream.peek()==\"(\") &&\n        !(functions && functions.propertyIsEnumerable(word.toLowerCase()))) {\n      state.beforeParams = true;\n      state.lastTokenWasBuiltin = false;\n      return \"keyword\";\n    }\n    if (state.inString) {\n      state.lastTokenWasBuiltin = false;\n      return \"string\";\n    }\n    if (stream.pos > word.length && stream.string.charAt(stream.pos-word.length-1)==\".\" && state.lastTokenWasBuiltin)\n      return \"builtin\";\n    // default: just a \"word\"\n    state.lastTokenWasBuiltin = false;\n    return null;\n  }\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if ((next == quote) && !escaped) {\n        end = true;\n        break;\n      }\n      if (quote=='\"' && stream.peek() == '$' && !escaped) {\n        state.inString = true;\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenUnparsed(stream, state) {\n  var maybeEnd = 0, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd == 2) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    if (ch == \"]\")\n      maybeEnd++;\n    else if (ch != \" \")\n      maybeEnd = 0;\n  }\n  return \"meta\";\n}\n// Interface\n\nexport const velocity = {\n  name: \"velocity\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      beforeParams: false,\n      inParams: false,\n      inString: false,\n      lastTokenWasBuiltin: false\n    };\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  },\n  languageData: {\n    commentTokens: {line: \"##\", block: {open: \"#*\", close: \"*#\"}}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,WAAW,GAAG;IACrB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AAEA,IAAI,WAAW,WAAW,qCACA;AAC1B,IAAI,YAAY,WAAW,wEACA;AAC3B,IAAI,WAAW,WAAW;AAC1B,IAAI,iBAAiB;AAErB,SAAS,MAAM,MAAM,EAAE,KAAK,EAAE,CAAC;IAC7B,MAAM,QAAQ,GAAG;IACjB,OAAO,EAAE,QAAQ;AACnB;AACA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,eAAe,MAAM,YAAY;IACrC,MAAM,YAAY,GAAG;IACrB,IAAI,KAAK,OAAO,IAAI;IACpB,4BAA4B;IAC5B,IAAI,AAAC,MAAM,OAAQ,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,EAAE;QACpD,MAAM,mBAAmB,GAAG;QAC5B,OAAO,MAAM,QAAQ,OAAO,YAAY;IAC1C,OAEK,IAAK,MAAM,KAAM;QACpB,MAAM,mBAAmB,GAAG;QAC5B,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,GAAG;YACjB,OAAO;QACT,OACK,IAAI,MAAM,QAAQ,EACrB,OAAO,MAAM,QAAQ,OAAO,YAAY;IAC5C,OAEK,IAAI,mBAAmB,IAAI,CAAC,KAAK;QACpC,IAAI,MAAM,OAAO,cACf,MAAM,QAAQ,GAAG;aACd,IAAI,MAAM,KAAK;YAClB,MAAM,QAAQ,GAAG;YACjB,MAAM,mBAAmB,GAAG;QAC9B;QACA,OAAO;IACT,OAEK,IAAI,KAAK,IAAI,CAAC,KAAK;QACtB,MAAM,mBAAmB,GAAG;QAC5B,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OAEK,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;QACrC,MAAM,mBAAmB,GAAG;QAC5B,OAAO,MAAM,QAAQ,OAAO;IAC9B,OAEK,IAAI,MAAM,OAAO,OAAO,KAAK,CAAC,aAAa;QAC9C,MAAM,mBAAmB,GAAG;QAC5B,OAAO,MAAM,QAAQ,OAAO;IAC9B,OAEK,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;QACrC,MAAM,mBAAmB,GAAG;QAC5B,OAAO,SAAS;QAChB,OAAO;IACT,OAEK,IAAI,MAAM,KAAK;QAClB,OAAO,GAAG,CAAC;QACX,OAAO,QAAQ,CAAC;QAChB,6BAA6B;QAC7B,IAAI,YAAY,SAAS,oBAAoB,CAAC,OAAO,OAAO,KAAK;YAC/D,OAAO;QACT,OACK;YACH,MAAM,mBAAmB,GAAG;YAC5B,MAAM,YAAY,GAAG;YACrB,OAAO;QACT;IACF,OAEK,IAAI,eAAe,IAAI,CAAC,KAAK;QAChC,MAAM,mBAAmB,GAAG;QAC5B,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT,OACK;QACH,qBAAqB;QACrB,OAAO,QAAQ,CAAC;QAChB,IAAI,OAAO,OAAO,OAAO;QACzB,oCAAoC;QACpC,IAAI,YAAY,SAAS,oBAAoB,CAAC,OAC5C,OAAO;QACT,qCAAqC;QACrC,IAAI,aAAa,UAAU,oBAAoB,CAAC,SAC5C,AAAC,OAAO,OAAO,GAAG,KAAK,CAAC,yBAAyB,OAAO,IAAI,MAAI,OAChE,CAAC,CAAC,aAAa,UAAU,oBAAoB,CAAC,KAAK,WAAW,GAAG,GAAG;YACtE,MAAM,YAAY,GAAG;YACrB,MAAM,mBAAmB,GAAG;YAC5B,OAAO;QACT;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,mBAAmB,GAAG;YAC5B,OAAO;QACT;QACA,IAAI,OAAO,GAAG,GAAG,KAAK,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAC,KAAK,MAAM,GAAC,MAAI,OAAO,MAAM,mBAAmB,EAC9G,OAAO;QACT,yBAAyB;QACzB,MAAM,mBAAmB,GAAG;QAC5B,OAAO;IACT;AACF;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;QACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,AAAC,QAAQ,SAAU,CAAC,SAAS;gBAC/B,MAAM;gBACN;YACF;YACA,IAAI,SAAO,OAAO,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS;gBAClD,MAAM,QAAQ,GAAG;gBACjB,MAAM;gBACN;YACF;YACA,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,KAAK,MAAM,QAAQ,GAAG;QAC1B,OAAO;IACT;AACF;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,WAAW,OAAO;IACtB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,IAAI,WAAW,GAAG;IAClB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,YAAY,GAAG;YAC9B,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,IAAI,MAAM,KACR;aACG,IAAI,MAAM,KACb,WAAW;IACf;IACA,OAAO;AACT;AAGO,MAAM,WAAW;IACtB,MAAM;IAEN,YAAY;QACV,OAAO;YACL,UAAU;YACV,cAAc;YACd,UAAU;YACV,UAAU;YACV,qBAAqB;QACvB;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IACA,cAAc;QACZ,eAAe;YAAC,MAAM;YAAM,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;IAC9D;AACF", "ignoreList": [0], "debugId": null}}]}