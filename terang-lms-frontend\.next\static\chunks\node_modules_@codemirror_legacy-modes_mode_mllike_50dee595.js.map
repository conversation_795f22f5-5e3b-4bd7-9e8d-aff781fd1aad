{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/mllike.js"], "sourcesContent": ["function mlLike(parserConfig) {\n  var words = {\n    'as': 'keyword',\n    'do': 'keyword',\n    'else': 'keyword',\n    'end': 'keyword',\n    'exception': 'keyword',\n    'fun': 'keyword',\n    'functor': 'keyword',\n    'if': 'keyword',\n    'in': 'keyword',\n    'include': 'keyword',\n    'let': 'keyword',\n    'of': 'keyword',\n    'open': 'keyword',\n    'rec': 'keyword',\n    'struct': 'keyword',\n    'then': 'keyword',\n    'type': 'keyword',\n    'val': 'keyword',\n    'while': 'keyword',\n    'with': 'keyword'\n  };\n\n  var extraWords = parserConfig.extraWords || {};\n  for (var prop in extraWords) {\n    if (extraWords.hasOwnProperty(prop)) {\n      words[prop] = parserConfig.extraWords[prop];\n    }\n  }\n  var hintWords = [];\n  for (var k in words) { hintWords.push(k); }\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n\n    if (ch === '\"') {\n      state.tokenize = tokenString;\n      return state.tokenize(stream, state);\n    }\n    if (ch === '{') {\n      if (stream.eat('|')) {\n        state.longString = true;\n        state.tokenize = tokenLongString;\n        return state.tokenize(stream, state);\n      }\n    }\n    if (ch === '(') {\n      if (stream.match(/^\\*(?!\\))/)) {\n        state.commentLevel++;\n        state.tokenize = tokenComment;\n        return state.tokenize(stream, state);\n      }\n    }\n    if (ch === '~' || ch === '?') {\n      stream.eatWhile(/\\w/);\n      return 'variableName.special';\n    }\n    if (ch === '`') {\n      stream.eatWhile(/\\w/);\n      return 'quote';\n    }\n    if (ch === '/' && parserConfig.slashComments && stream.eat('/')) {\n      stream.skipToEnd();\n      return 'comment';\n    }\n    if (/\\d/.test(ch)) {\n      if (ch === '0' && stream.eat(/[bB]/)) {\n        stream.eatWhile(/[01]/);\n      } if (ch === '0' && stream.eat(/[xX]/)) {\n        stream.eatWhile(/[0-9a-fA-F]/)\n      } if (ch === '0' && stream.eat(/[oO]/)) {\n        stream.eatWhile(/[0-7]/);\n      } else {\n        stream.eatWhile(/[\\d_]/);\n        if (stream.eat('.')) {\n          stream.eatWhile(/[\\d]/);\n        }\n        if (stream.eat(/[eE]/)) {\n          stream.eatWhile(/[\\d\\-+]/);\n        }\n      }\n      return 'number';\n    }\n    if ( /[+\\-*&%=<>!?|@\\.~:]/.test(ch)) {\n      return 'operator';\n    }\n    if (/[\\w\\xa1-\\uffff]/.test(ch)) {\n      stream.eatWhile(/[\\w\\xa1-\\uffff]/);\n      var cur = stream.current();\n      return words.hasOwnProperty(cur) ? words[cur] : 'variable';\n    }\n    return null\n  }\n\n  function tokenString(stream, state) {\n    var next, end = false, escaped = false;\n    while ((next = stream.next()) != null) {\n      if (next === '\"' && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next === '\\\\';\n    }\n    if (end && !escaped) {\n      state.tokenize = tokenBase;\n    }\n    return 'string';\n  };\n\n  function tokenComment(stream, state) {\n    var prev, next;\n    while(state.commentLevel > 0 && (next = stream.next()) != null) {\n      if (prev === '(' && next === '*') state.commentLevel++;\n      if (prev === '*' && next === ')') state.commentLevel--;\n      prev = next;\n    }\n    if (state.commentLevel <= 0) {\n      state.tokenize = tokenBase;\n    }\n    return 'comment';\n  }\n\n  function tokenLongString(stream, state) {\n    var prev, next;\n    while (state.longString && (next = stream.next()) != null) {\n      if (prev === '|' && next === '}') state.longString = false;\n      prev = next;\n    }\n    if (!state.longString) {\n      state.tokenize = tokenBase;\n    }\n    return 'string';\n  }\n\n  return {\n    startState: function() {return {tokenize: tokenBase, commentLevel: 0, longString: false};},\n    token: function(stream, state) {\n      if (stream.eatSpace()) return null;\n      return state.tokenize(stream, state);\n    },\n\n    languageData: {\n      autocomplete: hintWords,\n      commentTokens: {\n        line: parserConfig.slashComments ? \"//\" : undefined,\n        block: {open: \"(*\", close: \"*)\"}\n      }\n    }\n  };\n};\n\nexport const oCaml = mlLike({\n  name: \"ocaml\",\n  extraWords: {\n    'and': 'keyword',\n    'assert': 'keyword',\n    'begin': 'keyword',\n    'class': 'keyword',\n    'constraint': 'keyword',\n    'done': 'keyword',\n    'downto': 'keyword',\n    'external': 'keyword',\n    'function': 'keyword',\n    'initializer': 'keyword',\n    'lazy': 'keyword',\n    'match': 'keyword',\n    'method': 'keyword',\n    'module': 'keyword',\n    'mutable': 'keyword',\n    'new': 'keyword',\n    'nonrec': 'keyword',\n    'object': 'keyword',\n    'private': 'keyword',\n    'sig': 'keyword',\n    'to': 'keyword',\n    'try': 'keyword',\n    'value': 'keyword',\n    'virtual': 'keyword',\n    'when': 'keyword',\n\n    // builtins\n    'raise': 'builtin',\n    'failwith': 'builtin',\n    'true': 'builtin',\n    'false': 'builtin',\n\n    // Pervasives builtins\n    'asr': 'builtin',\n    'land': 'builtin',\n    'lor': 'builtin',\n    'lsl': 'builtin',\n    'lsr': 'builtin',\n    'lxor': 'builtin',\n    'mod': 'builtin',\n    'or': 'builtin',\n\n    // More Pervasives\n    'raise_notrace': 'builtin',\n    'trace': 'builtin',\n    'exit': 'builtin',\n    'print_string': 'builtin',\n    'print_endline': 'builtin',\n\n     'int': 'type',\n     'float': 'type',\n     'bool': 'type',\n     'char': 'type',\n     'string': 'type',\n     'unit': 'type',\n\n     // Modules\n     'List': 'builtin'\n  }\n});\n\nexport const fSharp = mlLike({\n  name: \"fsharp\",\n  extraWords: {\n    'abstract': 'keyword',\n    'assert': 'keyword',\n    'base': 'keyword',\n    'begin': 'keyword',\n    'class': 'keyword',\n    'default': 'keyword',\n    'delegate': 'keyword',\n    'do!': 'keyword',\n    'done': 'keyword',\n    'downcast': 'keyword',\n    'downto': 'keyword',\n    'elif': 'keyword',\n    'extern': 'keyword',\n    'finally': 'keyword',\n    'for': 'keyword',\n    'function': 'keyword',\n    'global': 'keyword',\n    'inherit': 'keyword',\n    'inline': 'keyword',\n    'interface': 'keyword',\n    'internal': 'keyword',\n    'lazy': 'keyword',\n    'let!': 'keyword',\n    'match': 'keyword',\n    'member': 'keyword',\n    'module': 'keyword',\n    'mutable': 'keyword',\n    'namespace': 'keyword',\n    'new': 'keyword',\n    'null': 'keyword',\n    'override': 'keyword',\n    'private': 'keyword',\n    'public': 'keyword',\n    'return!': 'keyword',\n    'return': 'keyword',\n    'select': 'keyword',\n    'static': 'keyword',\n    'to': 'keyword',\n    'try': 'keyword',\n    'upcast': 'keyword',\n    'use!': 'keyword',\n    'use': 'keyword',\n    'void': 'keyword',\n    'when': 'keyword',\n    'yield!': 'keyword',\n    'yield': 'keyword',\n\n    // Reserved words\n    'atomic': 'keyword',\n    'break': 'keyword',\n    'checked': 'keyword',\n    'component': 'keyword',\n    'const': 'keyword',\n    'constraint': 'keyword',\n    'constructor': 'keyword',\n    'continue': 'keyword',\n    'eager': 'keyword',\n    'event': 'keyword',\n    'external': 'keyword',\n    'fixed': 'keyword',\n    'method': 'keyword',\n    'mixin': 'keyword',\n    'object': 'keyword',\n    'parallel': 'keyword',\n    'process': 'keyword',\n    'protected': 'keyword',\n    'pure': 'keyword',\n    'sealed': 'keyword',\n    'tailcall': 'keyword',\n    'trait': 'keyword',\n    'virtual': 'keyword',\n    'volatile': 'keyword',\n\n    // builtins\n    'List': 'builtin',\n    'Seq': 'builtin',\n    'Map': 'builtin',\n    'Set': 'builtin',\n    'Option': 'builtin',\n    'int': 'builtin',\n    'string': 'builtin',\n    'not': 'builtin',\n    'true': 'builtin',\n    'false': 'builtin',\n\n    'raise': 'builtin',\n    'failwith': 'builtin'\n  },\n  slashComments: true\n});\n\nexport const sml = mlLike({\n  name: \"sml\",\n  extraWords: {\n    'abstype': 'keyword',\n    'and': 'keyword',\n    'andalso': 'keyword',\n    'case': 'keyword',\n    'datatype': 'keyword',\n    'fn': 'keyword',\n    'handle': 'keyword',\n    'infix': 'keyword',\n    'infixr': 'keyword',\n    'local': 'keyword',\n    'nonfix': 'keyword',\n    'op': 'keyword',\n    'orelse': 'keyword',\n    'raise': 'keyword',\n    'withtype': 'keyword',\n    'eqtype': 'keyword',\n    'sharing': 'keyword',\n    'sig': 'keyword',\n    'signature': 'keyword',\n    'structure': 'keyword',\n    'where': 'keyword',\n    'true': 'keyword',\n    'false': 'keyword',\n\n    // types\n    'int': 'builtin',\n    'real': 'builtin',\n    'string': 'builtin',\n    'char': 'builtin',\n    'bool': 'builtin'\n  },\n  slashComments: true\n});\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,OAAO,YAAY;IAC1B,IAAI,QAAQ;QACV,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;QACP,aAAa;QACb,OAAO;QACP,WAAW;QACX,MAAM;QACN,MAAM;QACN,WAAW;QACX,OAAO;QACP,MAAM;QACN,QAAQ;QACR,OAAO;QACP,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,SAAS;QACT,QAAQ;IACV;IAEA,IAAI,aAAa,aAAa,UAAU,IAAI,CAAC;IAC7C,IAAK,IAAI,QAAQ,WAAY;QAC3B,IAAI,WAAW,cAAc,CAAC,OAAO;YACnC,KAAK,CAAC,KAAK,GAAG,aAAa,UAAU,CAAC,KAAK;QAC7C;IACF;IACA,IAAI,YAAY,EAAE;IAClB,IAAK,IAAI,KAAK,MAAO;QAAE,UAAU,IAAI,CAAC;IAAI;IAE1C,SAAS,UAAU,MAAM,EAAE,KAAK;QAC9B,IAAI,KAAK,OAAO,IAAI;QAEpB,IAAI,OAAO,KAAK;YACd,MAAM,QAAQ,GAAG;YACjB,OAAO,MAAM,QAAQ,CAAC,QAAQ;QAChC;QACA,IAAI,OAAO,KAAK;YACd,IAAI,OAAO,GAAG,CAAC,MAAM;gBACnB,MAAM,UAAU,GAAG;gBACnB,MAAM,QAAQ,GAAG;gBACjB,OAAO,MAAM,QAAQ,CAAC,QAAQ;YAChC;QACF;QACA,IAAI,OAAO,KAAK;YACd,IAAI,OAAO,KAAK,CAAC,cAAc;gBAC7B,MAAM,YAAY;gBAClB,MAAM,QAAQ,GAAG;gBACjB,OAAO,MAAM,QAAQ,CAAC,QAAQ;YAChC;QACF;QACA,IAAI,OAAO,OAAO,OAAO,KAAK;YAC5B,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,IAAI,OAAO,KAAK;YACd,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,IAAI,OAAO,OAAO,aAAa,aAAa,IAAI,OAAO,GAAG,CAAC,MAAM;YAC/D,OAAO,SAAS;YAChB,OAAO;QACT;QACA,IAAI,KAAK,IAAI,CAAC,KAAK;YACjB,IAAI,OAAO,OAAO,OAAO,GAAG,CAAC,SAAS;gBACpC,OAAO,QAAQ,CAAC;YAClB;YAAE,IAAI,OAAO,OAAO,OAAO,GAAG,CAAC,SAAS;gBACtC,OAAO,QAAQ,CAAC;YAClB;YAAE,IAAI,OAAO,OAAO,OAAO,GAAG,CAAC,SAAS;gBACtC,OAAO,QAAQ,CAAC;YAClB,OAAO;gBACL,OAAO,QAAQ,CAAC;gBAChB,IAAI,OAAO,GAAG,CAAC,MAAM;oBACnB,OAAO,QAAQ,CAAC;gBAClB;gBACA,IAAI,OAAO,GAAG,CAAC,SAAS;oBACtB,OAAO,QAAQ,CAAC;gBAClB;YACF;YACA,OAAO;QACT;QACA,IAAK,sBAAsB,IAAI,CAAC,KAAK;YACnC,OAAO;QACT;QACA,IAAI,kBAAkB,IAAI,CAAC,KAAK;YAC9B,OAAO,QAAQ,CAAC;YAChB,IAAI,MAAM,OAAO,OAAO;YACxB,OAAO,MAAM,cAAc,CAAC,OAAO,KAAK,CAAC,IAAI,GAAG;QAClD;QACA,OAAO;IACT;IAEA,SAAS,YAAY,MAAM,EAAE,KAAK;QAChC,IAAI,MAAM,MAAM,OAAO,UAAU;QACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,SAAS,OAAO,CAAC,SAAS;gBAC5B,MAAM;gBACN;YACF;YACA,UAAU,CAAC,WAAW,SAAS;QACjC;QACA,IAAI,OAAO,CAAC,SAAS;YACnB,MAAM,QAAQ,GAAG;QACnB;QACA,OAAO;IACT;;IAEA,SAAS,aAAa,MAAM,EAAE,KAAK;QACjC,IAAI,MAAM;QACV,MAAM,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YAC9D,IAAI,SAAS,OAAO,SAAS,KAAK,MAAM,YAAY;YACpD,IAAI,SAAS,OAAO,SAAS,KAAK,MAAM,YAAY;YACpD,OAAO;QACT;QACA,IAAI,MAAM,YAAY,IAAI,GAAG;YAC3B,MAAM,QAAQ,GAAG;QACnB;QACA,OAAO;IACT;IAEA,SAAS,gBAAgB,MAAM,EAAE,KAAK;QACpC,IAAI,MAAM;QACV,MAAO,MAAM,UAAU,IAAI,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACzD,IAAI,SAAS,OAAO,SAAS,KAAK,MAAM,UAAU,GAAG;YACrD,OAAO;QACT;QACA,IAAI,CAAC,MAAM,UAAU,EAAE;YACrB,MAAM,QAAQ,GAAG;QACnB;QACA,OAAO;IACT;IAEA,OAAO;QACL,YAAY;YAAY,OAAO;gBAAC,UAAU;gBAAW,cAAc;gBAAG,YAAY;YAAK;QAAE;QACzF,OAAO,SAAS,MAAM,EAAE,KAAK;YAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO;YAC9B,OAAO,MAAM,QAAQ,CAAC,QAAQ;QAChC;QAEA,cAAc;YACZ,cAAc;YACd,eAAe;gBACb,MAAM,aAAa,aAAa,GAAG,OAAO;gBAC1C,OAAO;oBAAC,MAAM;oBAAM,OAAO;gBAAI;YACjC;QACF;IACF;AACF;;AAEO,MAAM,QAAQ,OAAO;IAC1B,MAAM;IACN,YAAY;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,SAAS;QACT,cAAc;QACd,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,QAAQ;QACR,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,OAAO;QACP,UAAU;QACV,UAAU;QACV,WAAW;QACX,OAAO;QACP,MAAM;QACN,OAAO;QACP,SAAS;QACT,WAAW;QACX,QAAQ;QAER,WAAW;QACX,SAAS;QACT,YAAY;QACZ,QAAQ;QACR,SAAS;QAET,sBAAsB;QACtB,OAAO;QACP,QAAQ;QACR,OAAO;QACP,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM;QAEN,kBAAkB;QAClB,iBAAiB;QACjB,SAAS;QACT,QAAQ;QACR,gBAAgB;QAChB,iBAAiB;QAEhB,OAAO;QACP,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,QAAQ;QAER,UAAU;QACV,QAAQ;IACX;AACF;AAEO,MAAM,SAAS,OAAO;IAC3B,MAAM;IACN,YAAY;QACV,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,SAAS;QACT,WAAW;QACX,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,UAAU;QACV,WAAW;QACX,OAAO;QACP,YAAY;QACZ,UAAU;QACV,WAAW;QACX,UAAU;QACV,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,UAAU;QACV,WAAW;QACX,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,SAAS;QAET,iBAAiB;QACjB,UAAU;QACV,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;QACT,cAAc;QACd,eAAe;QACf,YAAY;QACZ,SAAS;QACT,SAAS;QACT,YAAY;QACZ,SAAS;QACT,UAAU;QACV,SAAS;QACT,UAAU;QACV,YAAY;QACZ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,SAAS;QACT,WAAW;QACX,YAAY;QAEZ,WAAW;QACX,QAAQ;QACR,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;QAET,SAAS;QACT,YAAY;IACd;IACA,eAAe;AACjB;AAEO,MAAM,MAAM,OAAO;IACxB,MAAM;IACN,YAAY;QACV,WAAW;QACX,OAAO;QACP,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,MAAM;QACN,UAAU;QACV,SAAS;QACT,UAAU;QACV,SAAS;QACT,UAAU;QACV,MAAM;QACN,UAAU;QACV,SAAS;QACT,YAAY;QACZ,UAAU;QACV,WAAW;QACX,OAAO;QACP,aAAa;QACb,aAAa;QACb,SAAS;QACT,QAAQ;QACR,SAAS;QAET,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IACA,eAAe;AACjB", "ignoreList": [0], "debugId": null}}]}