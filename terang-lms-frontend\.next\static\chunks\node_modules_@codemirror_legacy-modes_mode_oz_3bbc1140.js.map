{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/oz.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nvar singleOperators = /[\\^@!\\|<>#~\\.\\*\\-\\+\\\\/,=]/;\nvar doubleOperators = /(<-)|(:=)|(=<)|(>=)|(<=)|(<:)|(>:)|(=:)|(\\\\=)|(\\\\=:)|(!!)|(==)|(::)/;\nvar tripleOperators = /(:::)|(\\.\\.\\.)|(=<:)|(>=:)/;\n\nvar middle = [\"in\", \"then\", \"else\", \"of\", \"elseof\", \"elsecase\", \"elseif\", \"catch\",\n              \"finally\", \"with\", \"require\", \"prepare\", \"import\", \"export\", \"define\", \"do\"];\nvar end = [\"end\"];\n\nvar atoms = wordRegexp([\"true\", \"false\", \"nil\", \"unit\"]);\nvar commonKeywords = wordRegexp([\"andthen\", \"at\", \"attr\", \"declare\", \"feat\", \"from\", \"lex\",\n                                 \"mod\", \"div\", \"mode\", \"orelse\", \"parser\", \"prod\", \"prop\", \"scanner\", \"self\", \"syn\", \"token\"]);\nvar openingKeywords = wordRegexp([\"local\", \"proc\", \"fun\", \"case\", \"class\", \"if\", \"cond\", \"or\", \"dis\",\n                                  \"choice\", \"not\", \"thread\", \"try\", \"raise\", \"lock\", \"for\", \"suchthat\", \"meth\", \"functor\"]);\nvar middleKeywords = wordRegexp(middle);\nvar endKeywords = wordRegexp(end);\n\n// Tokenizers\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  // Brackets\n  if(stream.match(/[{}]/)) {\n    return \"bracket\";\n  }\n\n  // Special [] keyword\n  if (stream.match('[]')) {\n    return \"keyword\"\n  }\n\n  // Operators\n  if (stream.match(tripleOperators) || stream.match(doubleOperators)) {\n    return \"operator\";\n  }\n\n  // Atoms\n  if(stream.match(atoms)) {\n    return 'atom';\n  }\n\n  // Opening keywords\n  var matched = stream.match(openingKeywords);\n  if (matched) {\n    if (!state.doInCurrentLine)\n      state.currentIndent++;\n    else\n      state.doInCurrentLine = false;\n\n    // Special matching for signatures\n    if(matched[0] == \"proc\" || matched[0] == \"fun\")\n      state.tokenize = tokenFunProc;\n    else if(matched[0] == \"class\")\n      state.tokenize = tokenClass;\n    else if(matched[0] == \"meth\")\n      state.tokenize = tokenMeth;\n\n    return 'keyword';\n  }\n\n  // Middle and other keywords\n  if (stream.match(middleKeywords) || stream.match(commonKeywords)) {\n    return \"keyword\"\n  }\n\n  // End keywords\n  if (stream.match(endKeywords)) {\n    state.currentIndent--;\n    return 'keyword';\n  }\n\n  // Eat the next char for next comparisons\n  var ch = stream.next();\n\n  // Strings\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n\n  // Numbers\n  if (/[~\\d]/.test(ch)) {\n    if (ch == \"~\") {\n      if(! /^[0-9]/.test(stream.peek()))\n        return null;\n      else if (( stream.next() == \"0\" && stream.match(/^[xX][0-9a-fA-F]+/)) || stream.match(/^[0-9]*(\\.[0-9]+)?([eE][~+]?[0-9]+)?/))\n        return \"number\";\n    }\n\n    if ((ch == \"0\" && stream.match(/^[xX][0-9a-fA-F]+/)) || stream.match(/^[0-9]*(\\.[0-9]+)?([eE][~+]?[0-9]+)?/))\n      return \"number\";\n\n    return null;\n  }\n\n  // Comments\n  if (ch == \"%\") {\n    stream.skipToEnd();\n    return 'comment';\n  }\n  else if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n  }\n\n  // Single operators\n  if(singleOperators.test(ch)) {\n    return \"operator\";\n  }\n\n  // If nothing match, we skip the entire alphanumerical block\n  stream.eatWhile(/\\w/);\n\n  return \"variable\";\n}\n\nfunction tokenClass(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n  stream.match(/([A-Z][A-Za-z0-9_]*)|(`.+`)/);\n  state.tokenize = tokenBase;\n  return \"type\"\n}\n\nfunction tokenMeth(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n  stream.match(/([a-zA-Z][A-Za-z0-9_]*)|(`.+`)/);\n  state.tokenize = tokenBase;\n  return \"def\"\n}\n\nfunction tokenFunProc(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  if(!state.hasPassedFirstStage && stream.eat(\"{\")) {\n    state.hasPassedFirstStage = true;\n    return \"bracket\";\n  }\n  else if(state.hasPassedFirstStage) {\n    stream.match(/([A-Z][A-Za-z0-9_]*)|(`.+`)|\\$/);\n    state.hasPassedFirstStage = false;\n    state.tokenize = tokenBase;\n    return \"def\"\n  }\n  else {\n    state.tokenize = tokenBase;\n    return null;\n  }\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(quote) {\n  return function (stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped)\n      state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction buildElectricInputRegEx() {\n  // Reindentation should occur on [] or on a match of any of\n  // the block closing keywords, at the end of a line.\n  var allClosings = middle.concat(end);\n  return new RegExp(\"[\\\\[\\\\]]|(\" + allClosings.join(\"|\") + \")$\");\n}\n\nexport const oz = {\n  name: \"oz\",\n\n  startState: function () {\n    return {\n      tokenize: tokenBase,\n      currentIndent: 0,\n      doInCurrentLine: false,\n      hasPassedFirstStage: false\n    };\n  },\n\n  token: function (stream, state) {\n    if (stream.sol())\n      state.doInCurrentLine = 0;\n\n    return state.tokenize(stream, state);\n  },\n\n  indent: function (state, textAfter, cx) {\n    var trueText = textAfter.replace(/^\\s+|\\s+$/g, '');\n\n    if (trueText.match(endKeywords) || trueText.match(middleKeywords) || trueText.match(/(\\[])/))\n      return cx.unit * (state.currentIndent - 1);\n\n    if (state.currentIndent < 0)\n      return 0;\n\n    return state.currentIndent * cx.unit\n  },\n\n  languageData: {\n    indentOnInut: buildElectricInputRegEx(),\n    commentTokens: {line: \"%\", block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,WAAW,KAAK;IACvB,OAAO,IAAI,OAAO,QAAQ,MAAM,IAAI,CAAC,SAAS;AAChD;AAEA,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AAEtB,IAAI,SAAS;IAAC;IAAM;IAAQ;IAAQ;IAAM;IAAU;IAAY;IAAU;IAC5D;IAAW;IAAQ;IAAW;IAAW;IAAU;IAAU;IAAU;CAAK;AAC1F,IAAI,MAAM;IAAC;CAAM;AAEjB,IAAI,QAAQ,WAAW;IAAC;IAAQ;IAAS;IAAO;CAAO;AACvD,IAAI,iBAAiB,WAAW;IAAC;IAAW;IAAM;IAAQ;IAAW;IAAQ;IAAQ;IACpD;IAAO;IAAO;IAAQ;IAAU;IAAU;IAAQ;IAAQ;IAAW;IAAQ;IAAO;CAAQ;AAC7H,IAAI,kBAAkB,WAAW;IAAC;IAAS;IAAQ;IAAO;IAAQ;IAAS;IAAM;IAAQ;IAAM;IAC7D;IAAU;IAAO;IAAU;IAAO;IAAS;IAAQ;IAAO;IAAY;IAAQ;CAAU;AAC1H,IAAI,iBAAiB,WAAW;AAChC,IAAI,cAAc,WAAW;AAE7B,aAAa;AACb,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,OAAO,QAAQ,IAAI;QACrB,OAAO;IACT;IAEA,WAAW;IACX,IAAG,OAAO,KAAK,CAAC,SAAS;QACvB,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,OAAO,KAAK,CAAC,OAAO;QACtB,OAAO;IACT;IAEA,YAAY;IACZ,IAAI,OAAO,KAAK,CAAC,oBAAoB,OAAO,KAAK,CAAC,kBAAkB;QAClE,OAAO;IACT;IAEA,QAAQ;IACR,IAAG,OAAO,KAAK,CAAC,QAAQ;QACtB,OAAO;IACT;IAEA,mBAAmB;IACnB,IAAI,UAAU,OAAO,KAAK,CAAC;IAC3B,IAAI,SAAS;QACX,IAAI,CAAC,MAAM,eAAe,EACxB,MAAM,aAAa;aAEnB,MAAM,eAAe,GAAG;QAE1B,kCAAkC;QAClC,IAAG,OAAO,CAAC,EAAE,IAAI,UAAU,OAAO,CAAC,EAAE,IAAI,OACvC,MAAM,QAAQ,GAAG;aACd,IAAG,OAAO,CAAC,EAAE,IAAI,SACpB,MAAM,QAAQ,GAAG;aACd,IAAG,OAAO,CAAC,EAAE,IAAI,QACpB,MAAM,QAAQ,GAAG;QAEnB,OAAO;IACT;IAEA,4BAA4B;IAC5B,IAAI,OAAO,KAAK,CAAC,mBAAmB,OAAO,KAAK,CAAC,iBAAiB;QAChE,OAAO;IACT;IAEA,eAAe;IACf,IAAI,OAAO,KAAK,CAAC,cAAc;QAC7B,MAAM,aAAa;QACnB,OAAO;IACT;IAEA,yCAAyC;IACzC,IAAI,KAAK,OAAO,IAAI;IAEpB,UAAU;IACV,IAAI,MAAM,OAAO,MAAM,KAAK;QAC1B,MAAM,QAAQ,GAAG,YAAY;QAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IAEA,UAAU;IACV,IAAI,QAAQ,IAAI,CAAC,KAAK;QACpB,IAAI,MAAM,KAAK;YACb,IAAG,CAAE,SAAS,IAAI,CAAC,OAAO,IAAI,KAC5B,OAAO;iBACJ,IAAI,AAAE,OAAO,IAAI,MAAM,OAAO,OAAO,KAAK,CAAC,wBAAyB,OAAO,KAAK,CAAC,yCACpF,OAAO;QACX;QAEA,IAAI,AAAC,MAAM,OAAO,OAAO,KAAK,CAAC,wBAAyB,OAAO,KAAK,CAAC,yCACnE,OAAO;QAET,OAAO;IACT;IAEA,WAAW;IACX,IAAI,MAAM,KAAK;QACb,OAAO,SAAS;QAChB,OAAO;IACT,OACK,IAAI,MAAM,KAAK;QAClB,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,MAAM,QAAQ,GAAG;YACjB,OAAO,aAAa,QAAQ;QAC9B;IACF;IAEA,mBAAmB;IACnB,IAAG,gBAAgB,IAAI,CAAC,KAAK;QAC3B,OAAO;IACT;IAEA,4DAA4D;IAC5D,OAAO,QAAQ,CAAC;IAEhB,OAAO;AACT;AAEA,SAAS,WAAW,MAAM,EAAE,KAAK;IAC/B,IAAI,OAAO,QAAQ,IAAI;QACrB,OAAO;IACT;IACA,OAAO,KAAK,CAAC;IACb,MAAM,QAAQ,GAAG;IACjB,OAAO;AACT;AAEA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,OAAO,QAAQ,IAAI;QACrB,OAAO;IACT;IACA,OAAO,KAAK,CAAC;IACb,MAAM,QAAQ,GAAG;IACjB,OAAO;AACT;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,OAAO,QAAQ,IAAI;QACrB,OAAO;IACT;IAEA,IAAG,CAAC,MAAM,mBAAmB,IAAI,OAAO,GAAG,CAAC,MAAM;QAChD,MAAM,mBAAmB,GAAG;QAC5B,OAAO;IACT,OACK,IAAG,MAAM,mBAAmB,EAAE;QACjC,OAAO,KAAK,CAAC;QACb,MAAM,mBAAmB,GAAG;QAC5B,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT,OACK;QACH,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT;AACF;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,WAAW,OAAO;IACtB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAU,MAAM,EAAE,KAAK;QAC5B,IAAI,UAAU,OAAO,MAAM,MAAM;QACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,QAAQ,SAAS,CAAC,SAAS;gBAC7B,MAAM;gBACN;YACF;YACA,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,OAAO,CAAC,SACV,MAAM,QAAQ,GAAG;QACnB,OAAO;IACT;AACF;AAEA,SAAS;IACP,2DAA2D;IAC3D,oDAAoD;IACpD,IAAI,cAAc,OAAO,MAAM,CAAC;IAChC,OAAO,IAAI,OAAO,eAAe,YAAY,IAAI,CAAC,OAAO;AAC3D;AAEO,MAAM,KAAK;IAChB,MAAM;IAEN,YAAY;QACV,OAAO;YACL,UAAU;YACV,eAAe;YACf,iBAAiB;YACjB,qBAAqB;QACvB;IACF;IAEA,OAAO,SAAU,MAAM,EAAE,KAAK;QAC5B,IAAI,OAAO,GAAG,IACZ,MAAM,eAAe,GAAG;QAE1B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IAEA,QAAQ,SAAU,KAAK,EAAE,SAAS,EAAE,EAAE;QACpC,IAAI,WAAW,UAAU,OAAO,CAAC,cAAc;QAE/C,IAAI,SAAS,KAAK,CAAC,gBAAgB,SAAS,KAAK,CAAC,mBAAmB,SAAS,KAAK,CAAC,UAClF,OAAO,GAAG,IAAI,GAAG,CAAC,MAAM,aAAa,GAAG,CAAC;QAE3C,IAAI,MAAM,aAAa,GAAG,GACxB,OAAO;QAET,OAAO,MAAM,aAAa,GAAG,GAAG,IAAI;IACtC;IAEA,cAAc;QACZ,cAAc;QACd,eAAe;YAAC,MAAM;YAAK,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;IAC7D;AACF", "ignoreList": [0], "debugId": null}}]}