{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/elm.js"], "sourcesContent": ["function switchState(source, setState, f)\n{\n  setState(f);\n  return f(source, setState);\n}\n\nvar lowerRE = /[a-z]/;\nvar upperRE = /[A-Z]/;\nvar innerRE = /[a-zA-Z0-9_]/;\n\nvar digitRE = /[0-9]/;\nvar hexRE = /[0-9A-Fa-f]/;\nvar symbolRE = /[-&*+.\\\\/<>=?^|:]/;\nvar specialRE = /[(),[\\]{}]/;\nvar spacesRE = /[ \\v\\f]/; // newlines are handled in tokenizer\n\nfunction normal()\n{\n  return function(source, setState)\n  {\n    if (source.eatWhile(spacesRE))\n    {\n      return null;\n    }\n\n    var char = source.next();\n\n    if (specialRE.test(char))\n    {\n      return (char === '{' && source.eat('-'))\n        ? switchState(source, setState, chompMultiComment(1))\n        : (char === '[' && source.match('glsl|'))\n        ? switchState(source, setState, chompGlsl)\n        : 'builtin';\n    }\n\n    if (char === '\\'')\n    {\n      return switchState(source, setState, chompChar);\n    }\n\n    if (char === '\"')\n    {\n      return source.eat('\"')\n        ? source.eat('\"')\n        ? switchState(source, setState, chompMultiString)\n        : 'string'\n      : switchState(source, setState, chompSingleString);\n    }\n\n    if (upperRE.test(char))\n    {\n      source.eatWhile(innerRE);\n      return 'type';\n    }\n\n    if (lowerRE.test(char))\n    {\n      var isDef = source.pos === 1;\n      source.eatWhile(innerRE);\n      return isDef ? \"def\" : \"variable\";\n    }\n\n    if (digitRE.test(char))\n    {\n      if (char === '0')\n      {\n        if (source.eat(/[xX]/))\n        {\n          source.eatWhile(hexRE); // should require at least 1\n          return \"number\";\n        }\n      }\n      else\n      {\n        source.eatWhile(digitRE);\n      }\n      if (source.eat('.'))\n      {\n        source.eatWhile(digitRE); // should require at least 1\n      }\n      if (source.eat(/[eE]/))\n      {\n        source.eat(/[-+]/);\n        source.eatWhile(digitRE); // should require at least 1\n      }\n      return \"number\";\n    }\n\n    if (symbolRE.test(char))\n    {\n      if (char === '-' && source.eat('-'))\n      {\n        source.skipToEnd();\n        return \"comment\";\n      }\n      source.eatWhile(symbolRE);\n      return \"keyword\";\n    }\n\n    if (char === '_')\n    {\n      return \"keyword\";\n    }\n\n    return \"error\";\n  }\n}\n\nfunction chompMultiComment(nest)\n{\n  if (nest == 0)\n  {\n    return normal();\n  }\n  return function(source, setState)\n  {\n    while (!source.eol())\n    {\n      var char = source.next();\n      if (char == '{' && source.eat('-'))\n      {\n        ++nest;\n      }\n      else if (char == '-' && source.eat('}'))\n      {\n        --nest;\n        if (nest === 0)\n        {\n          setState(normal());\n          return 'comment';\n        }\n      }\n    }\n    setState(chompMultiComment(nest));\n    return 'comment';\n  }\n}\n\nfunction chompMultiString(source, setState)\n{\n  while (!source.eol())\n  {\n    var char = source.next();\n    if (char === '\"' && source.eat('\"') && source.eat('\"'))\n    {\n      setState(normal());\n      return 'string';\n    }\n  }\n  return 'string';\n}\n\nfunction chompSingleString(source, setState)\n{\n  while (source.skipTo('\\\\\"')) { source.next(); source.next(); }\n  if (source.skipTo('\"'))\n  {\n    source.next();\n    setState(normal());\n    return 'string';\n  }\n  source.skipToEnd();\n  setState(normal());\n  return 'error';\n}\n\nfunction chompChar(source, setState)\n{\n  while (source.skipTo(\"\\\\'\")) { source.next(); source.next(); }\n  if (source.skipTo(\"'\"))\n  {\n    source.next();\n    setState(normal());\n    return 'string';\n  }\n  source.skipToEnd();\n  setState(normal());\n  return 'error';\n}\n\nfunction chompGlsl(source, setState)\n{\n  while (!source.eol())\n  {\n    var char = source.next();\n    if (char === '|' && source.eat(']'))\n    {\n      setState(normal());\n      return 'string';\n    }\n  }\n  return 'string';\n}\n\nvar wellKnownWords = {\n  case: 1,\n  of: 1,\n  as: 1,\n  if: 1,\n  then: 1,\n  else: 1,\n  let: 1,\n    in: 1,\n  type: 1,\n  alias: 1,\n  module: 1,\n  where: 1,\n  import: 1,\n  exposing: 1,\n  port: 1\n};\n\nexport const elm = {\n  name: \"elm\",\n  startState: function ()  { return { f: normal() }; },\n  copyState:  function (s) { return { f: s.f }; },\n\n  token: function(stream, state) {\n    var type = state.f(stream, function(s) { state.f = s; });\n    var word = stream.current();\n    return (wellKnownWords.hasOwnProperty(word)) ? 'keyword' : type;\n  },\n\n  languageData: {\n    commentTokens: {line: \"--\"}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,YAAY,MAAM,EAAE,QAAQ,EAAE,CAAC;IAEtC,SAAS;IACT,OAAO,EAAE,QAAQ;AACnB;AAEA,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,UAAU;AAEd,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,WAAW,WAAW,oCAAoC;AAE9D,SAAS;IAEP,OAAO,SAAS,MAAM,EAAE,QAAQ;QAE9B,IAAI,OAAO,QAAQ,CAAC,WACpB;YACE,OAAO;QACT;QAEA,IAAI,OAAO,OAAO,IAAI;QAEtB,IAAI,UAAU,IAAI,CAAC,OACnB;YACE,OAAO,AAAC,SAAS,OAAO,OAAO,GAAG,CAAC,OAC/B,YAAY,QAAQ,UAAU,kBAAkB,MAChD,AAAC,SAAS,OAAO,OAAO,KAAK,CAAC,WAC9B,YAAY,QAAQ,UAAU,aAC9B;QACN;QAEA,IAAI,SAAS,MACb;YACE,OAAO,YAAY,QAAQ,UAAU;QACvC;QAEA,IAAI,SAAS,KACb;YACE,OAAO,OAAO,GAAG,CAAC,OACd,OAAO,GAAG,CAAC,OACX,YAAY,QAAQ,UAAU,oBAC9B,WACF,YAAY,QAAQ,UAAU;QAClC;QAEA,IAAI,QAAQ,IAAI,CAAC,OACjB;YACE,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,QAAQ,IAAI,CAAC,OACjB;YACE,IAAI,QAAQ,OAAO,GAAG,KAAK;YAC3B,OAAO,QAAQ,CAAC;YAChB,OAAO,QAAQ,QAAQ;QACzB;QAEA,IAAI,QAAQ,IAAI,CAAC,OACjB;YACE,IAAI,SAAS,KACb;gBACE,IAAI,OAAO,GAAG,CAAC,SACf;oBACE,OAAO,QAAQ,CAAC,QAAQ,4BAA4B;oBACpD,OAAO;gBACT;YACF,OAEA;gBACE,OAAO,QAAQ,CAAC;YAClB;YACA,IAAI,OAAO,GAAG,CAAC,MACf;gBACE,OAAO,QAAQ,CAAC,UAAU,4BAA4B;YACxD;YACA,IAAI,OAAO,GAAG,CAAC,SACf;gBACE,OAAO,GAAG,CAAC;gBACX,OAAO,QAAQ,CAAC,UAAU,4BAA4B;YACxD;YACA,OAAO;QACT;QAEA,IAAI,SAAS,IAAI,CAAC,OAClB;YACE,IAAI,SAAS,OAAO,OAAO,GAAG,CAAC,MAC/B;gBACE,OAAO,SAAS;gBAChB,OAAO;YACT;YACA,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,KACb;YACE,OAAO;QACT;QAEA,OAAO;IACT;AACF;AAEA,SAAS,kBAAkB,IAAI;IAE7B,IAAI,QAAQ,GACZ;QACE,OAAO;IACT;IACA,OAAO,SAAS,MAAM,EAAE,QAAQ;QAE9B,MAAO,CAAC,OAAO,GAAG,GAClB;YACE,IAAI,OAAO,OAAO,IAAI;YACtB,IAAI,QAAQ,OAAO,OAAO,GAAG,CAAC,MAC9B;gBACE,EAAE;YACJ,OACK,IAAI,QAAQ,OAAO,OAAO,GAAG,CAAC,MACnC;gBACE,EAAE;gBACF,IAAI,SAAS,GACb;oBACE,SAAS;oBACT,OAAO;gBACT;YACF;QACF;QACA,SAAS,kBAAkB;QAC3B,OAAO;IACT;AACF;AAEA,SAAS,iBAAiB,MAAM,EAAE,QAAQ;IAExC,MAAO,CAAC,OAAO,GAAG,GAClB;QACE,IAAI,OAAO,OAAO,IAAI;QACtB,IAAI,SAAS,OAAO,OAAO,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,MAClD;YACE,SAAS;YACT,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,SAAS,kBAAkB,MAAM,EAAE,QAAQ;IAEzC,MAAO,OAAO,MAAM,CAAC,OAAQ;QAAE,OAAO,IAAI;QAAI,OAAO,IAAI;IAAI;IAC7D,IAAI,OAAO,MAAM,CAAC,MAClB;QACE,OAAO,IAAI;QACX,SAAS;QACT,OAAO;IACT;IACA,OAAO,SAAS;IAChB,SAAS;IACT,OAAO;AACT;AAEA,SAAS,UAAU,MAAM,EAAE,QAAQ;IAEjC,MAAO,OAAO,MAAM,CAAC,OAAQ;QAAE,OAAO,IAAI;QAAI,OAAO,IAAI;IAAI;IAC7D,IAAI,OAAO,MAAM,CAAC,MAClB;QACE,OAAO,IAAI;QACX,SAAS;QACT,OAAO;IACT;IACA,OAAO,SAAS;IAChB,SAAS;IACT,OAAO;AACT;AAEA,SAAS,UAAU,MAAM,EAAE,QAAQ;IAEjC,MAAO,CAAC,OAAO,GAAG,GAClB;QACE,IAAI,OAAO,OAAO,IAAI;QACtB,IAAI,SAAS,OAAO,OAAO,GAAG,CAAC,MAC/B;YACE,SAAS;YACT,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,IAAI,iBAAiB;IACnB,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,MAAM;IACN,KAAK;IACH,IAAI;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,UAAU;IACV,MAAM;AACR;AAEO,MAAM,MAAM;IACjB,MAAM;IACN,YAAY;QAAe,OAAO;YAAE,GAAG;QAAS;IAAG;IACnD,WAAY,SAAU,CAAC;QAAI,OAAO;YAAE,GAAG,EAAE,CAAC;QAAC;IAAG;IAE9C,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,MAAM,CAAC,CAAC,QAAQ,SAAS,CAAC;YAAI,MAAM,CAAC,GAAG;QAAG;QACtD,IAAI,OAAO,OAAO,OAAO;QACzB,OAAO,AAAC,eAAe,cAAc,CAAC,QAAS,YAAY;IAC7D;IAEA,cAAc;QACZ,eAAe;YAAC,MAAM;QAAI;IAC5B;AACF", "ignoreList": [0], "debugId": null}}]}