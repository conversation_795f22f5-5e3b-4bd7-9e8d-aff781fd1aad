{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/livescript.js"], "sourcesContent": ["var tokenBase = function(stream, state) {\n  var next_rule = state.next || \"start\";\n  if (next_rule) {\n    state.next = state.next;\n    var nr = Rules[next_rule];\n    if (nr.splice) {\n      for (var i$ = 0; i$ < nr.length; ++i$) {\n        var r = nr[i$];\n        if (r.regex && stream.match(r.regex)) {\n          state.next = r.next || state.next;\n          return r.token;\n        }\n      }\n      stream.next();\n      return 'error';\n    }\n    if (stream.match(r = Rules[next_rule])) {\n      if (r.regex && stream.match(r.regex)) {\n        state.next = r.next;\n        return r.token;\n      } else {\n        stream.next();\n        return 'error';\n      }\n    }\n  }\n  stream.next();\n  return 'error';\n};\n\nvar identifier = '(?![\\\\d\\\\s])[$\\\\w\\\\xAA-\\\\uFFDC](?:(?!\\\\s)[$\\\\w\\\\xAA-\\\\uFFDC]|-[A-Za-z])*';\nvar indenter = RegExp('(?:[({[=:]|[-~]>|\\\\b(?:e(?:lse|xport)|d(?:o|efault)|t(?:ry|hen)|finally|import(?:\\\\s*all)?|const|var|let|new|catch(?:\\\\s*' + identifier + ')?))\\\\s*$');\nvar keywordend = '(?![$\\\\w]|-[A-Za-z]|\\\\s*:(?![:=]))';\nvar stringfill = {\n  token: 'string',\n  regex: '.+'\n};\nvar Rules = {\n  start: [\n    {\n      token: 'docComment',\n      regex: '/\\\\*',\n      next: 'comment'\n    }, {\n      token: 'comment',\n      regex: '#.*'\n    }, {\n      token: 'keyword',\n      regex: '(?:t(?:h(?:is|row|en)|ry|ypeof!?)|c(?:on(?:tinue|st)|a(?:se|tch)|lass)|i(?:n(?:stanceof)?|mp(?:ort(?:\\\\s+all)?|lements)|[fs])|d(?:e(?:fault|lete|bugger)|o)|f(?:or(?:\\\\s+own)?|inally|unction)|s(?:uper|witch)|e(?:lse|x(?:tends|port)|val)|a(?:nd|rguments)|n(?:ew|ot)|un(?:less|til)|w(?:hile|ith)|o[fr]|return|break|let|var|loop)' + keywordend\n    }, {\n      token: 'atom',\n      regex: '(?:true|false|yes|no|on|off|null|void|undefined)' + keywordend\n    }, {\n      token: 'invalid',\n      regex: '(?:p(?:ackage|r(?:ivate|otected)|ublic)|i(?:mplements|nterface)|enum|static|yield)' + keywordend\n    }, {\n      token: 'className.standard',\n      regex: '(?:R(?:e(?:gExp|ferenceError)|angeError)|S(?:tring|yntaxError)|E(?:rror|valError)|Array|Boolean|Date|Function|Number|Object|TypeError|URIError)' + keywordend\n    }, {\n      token: 'variableName.function.standard',\n      regex: '(?:is(?:NaN|Finite)|parse(?:Int|Float)|Math|JSON|(?:en|de)codeURI(?:Component)?)' + keywordend\n    }, {\n      token: 'variableName.standard',\n      regex: '(?:t(?:hat|il|o)|f(?:rom|allthrough)|it|by|e)' + keywordend\n    }, {\n      token: 'variableName',\n      regex: identifier + '\\\\s*:(?![:=])'\n    }, {\n      token: 'variableName',\n      regex: identifier\n    }, {\n      token: 'operatorKeyword',\n      regex: '(?:\\\\.{3}|\\\\s+\\\\?)'\n    }, {\n      token: 'keyword',\n      regex: '(?:@+|::|\\\\.\\\\.)',\n      next: 'key'\n    }, {\n      token: 'operatorKeyword',\n      regex: '\\\\.\\\\s*',\n      next: 'key'\n    }, {\n      token: 'string',\n      regex: '\\\\\\\\\\\\S[^\\\\s,;)}\\\\]]*'\n    }, {\n      token: 'docString',\n      regex: '\\'\\'\\'',\n      next: 'qdoc'\n    }, {\n      token: 'docString',\n      regex: '\"\"\"',\n      next: 'qqdoc'\n    }, {\n      token: 'string',\n      regex: '\\'',\n      next: 'qstring'\n    }, {\n      token: 'string',\n      regex: '\"',\n      next: 'qqstring'\n    }, {\n      token: 'string',\n      regex: '`',\n      next: 'js'\n    }, {\n      token: 'string',\n      regex: '<\\\\[',\n      next: 'words'\n    }, {\n      token: 'regexp',\n      regex: '//',\n      next: 'heregex'\n    }, {\n      token: 'regexp',\n      regex: '\\\\/(?:[^[\\\\/\\\\n\\\\\\\\]*(?:(?:\\\\\\\\.|\\\\[[^\\\\]\\\\n\\\\\\\\]*(?:\\\\\\\\.[^\\\\]\\\\n\\\\\\\\]*)*\\\\])[^[\\\\/\\\\n\\\\\\\\]*)*)\\\\/[gimy$]{0,4}',\n      next: 'key'\n    }, {\n      token: 'number',\n      regex: '(?:0x[\\\\da-fA-F][\\\\da-fA-F_]*|(?:[2-9]|[12]\\\\d|3[0-6])r[\\\\da-zA-Z][\\\\da-zA-Z_]*|(?:\\\\d[\\\\d_]*(?:\\\\.\\\\d[\\\\d_]*)?|\\\\.\\\\d[\\\\d_]*)(?:e[+-]?\\\\d[\\\\d_]*)?[\\\\w$]*)'\n    }, {\n      token: 'paren',\n      regex: '[({[]'\n    }, {\n      token: 'paren',\n      regex: '[)}\\\\]]',\n      next: 'key'\n    }, {\n      token: 'operatorKeyword',\n      regex: '\\\\S+'\n    }, {\n      token: 'content',\n      regex: '\\\\s+'\n    }\n  ],\n  heregex: [\n    {\n      token: 'regexp',\n      regex: '.*?//[gimy$?]{0,4}',\n      next: 'start'\n    }, {\n      token: 'regexp',\n      regex: '\\\\s*#{'\n    }, {\n      token: 'comment',\n      regex: '\\\\s+(?:#.*)?'\n    }, {\n      token: 'regexp',\n      regex: '\\\\S+'\n    }\n  ],\n  key: [\n    {\n      token: 'operatorKeyword',\n      regex: '[.?@!]+'\n    }, {\n      token: 'variableName',\n      regex: identifier,\n      next: 'start'\n    }, {\n      token: 'content',\n      regex: '',\n      next: 'start'\n    }\n  ],\n  comment: [\n    {\n      token: 'docComment',\n      regex: '.*?\\\\*/',\n      next: 'start'\n    }, {\n      token: 'docComment',\n      regex: '.+'\n    }\n  ],\n  qdoc: [\n    {\n      token: 'string',\n      regex: \".*?'''\",\n      next: 'key'\n    }, stringfill\n  ],\n  qqdoc: [\n    {\n      token: 'string',\n      regex: '.*?\"\"\"',\n      next: 'key'\n    }, stringfill\n  ],\n  qstring: [\n    {\n      token: 'string',\n      regex: '[^\\\\\\\\\\']*(?:\\\\\\\\.[^\\\\\\\\\\']*)*\\'',\n      next: 'key'\n    }, stringfill\n  ],\n  qqstring: [\n    {\n      token: 'string',\n      regex: '[^\\\\\\\\\"]*(?:\\\\\\\\.[^\\\\\\\\\"]*)*\"',\n      next: 'key'\n    }, stringfill\n  ],\n  js: [\n    {\n      token: 'string',\n      regex: '[^\\\\\\\\`]*(?:\\\\\\\\.[^\\\\\\\\`]*)*`',\n      next: 'key'\n    }, stringfill\n  ],\n  words: [\n    {\n      token: 'string',\n      regex: '.*?\\\\]>',\n      next: 'key'\n    }, stringfill\n  ]\n};\nfor (var idx in Rules) {\n  var r = Rules[idx];\n  if (r.splice) {\n    for (var i = 0, len = r.length; i < len; ++i) {\n      var rr = r[i];\n      if (typeof rr.regex === 'string') {\n        Rules[idx][i].regex = new RegExp('^' + rr.regex);\n      }\n    }\n  } else if (typeof rr.regex === 'string') {\n    Rules[idx].regex = new RegExp('^' + r.regex);\n  }\n}\n\nexport const liveScript = {\n  name: \"livescript\",\n  startState: function(){\n    return {\n      next: 'start',\n      lastToken: {style: null, indent: 0, content: \"\"}\n    };\n  },\n  token: function(stream, state){\n    while (stream.pos == stream.start)\n      var style = tokenBase(stream, state);\n    state.lastToken = {\n      style: style,\n      indent: stream.indentation(),\n      content: stream.current()\n    };\n    return style.replace(/\\./g, ' ');\n  },\n  indent: function(state){\n    var indentation = state.lastToken.indent;\n    if (state.lastToken.content.match(indenter)) {\n      indentation += 2;\n    }\n    return indentation;\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,YAAY,SAAS,MAAM,EAAE,KAAK;IACpC,IAAI,YAAY,MAAM,IAAI,IAAI;IAC9B,wCAAe;QACb,MAAM,IAAI,GAAG,MAAM,IAAI;QACvB,IAAI,KAAK,KAAK,CAAC,UAAU;QACzB,IAAI,GAAG,MAAM,EAAE;YACb,IAAK,IAAI,KAAK,GAAG,KAAK,GAAG,MAAM,EAAE,EAAE,GAAI;gBACrC,IAAI,IAAI,EAAE,CAAC,GAAG;gBACd,IAAI,EAAE,KAAK,IAAI,OAAO,KAAK,CAAC,EAAE,KAAK,GAAG;oBACpC,MAAM,IAAI,GAAG,EAAE,IAAI,IAAI,MAAM,IAAI;oBACjC,OAAO,EAAE,KAAK;gBAChB;YACF;YACA,OAAO,IAAI;YACX,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,UAAU,GAAG;YACtC,IAAI,EAAE,KAAK,IAAI,OAAO,KAAK,CAAC,EAAE,KAAK,GAAG;gBACpC,MAAM,IAAI,GAAG,EAAE,IAAI;gBACnB,OAAO,EAAE,KAAK;YAChB,OAAO;gBACL,OAAO,IAAI;gBACX,OAAO;YACT;QACF;IACF;IACA,OAAO,IAAI;IACX,OAAO;AACT;AAEA,IAAI,aAAa;AACjB,IAAI,WAAW,OAAO,8HAA8H,aAAa;AACjK,IAAI,aAAa;AACjB,IAAI,aAAa;IACf,OAAO;IACP,OAAO;AACT;AACA,IAAI,QAAQ;IACV,OAAO;QACL;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;QACT;QAAG;YACD,OAAO;YACP,OAAO,0UAA0U;QACnV;QAAG;YACD,OAAO;YACP,OAAO,qDAAqD;QAC9D;QAAG;YACD,OAAO;YACP,OAAO,uFAAuF;QAChG;QAAG;YACD,OAAO;YACP,OAAO,oJAAoJ;QAC7J;QAAG;YACD,OAAO;YACP,OAAO,qFAAqF;QAC9F;QAAG;YACD,OAAO;YACP,OAAO,kDAAkD;QAC3D;QAAG;YACD,OAAO;YACP,OAAO,aAAa;QACtB;QAAG;YACD,OAAO;YACP,OAAO;QACT;QAAG;YACD,OAAO;YACP,OAAO;QACT;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;QACT;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;QACT;QAAG;YACD,OAAO;YACP,OAAO;QACT;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;QACT;QAAG;YACD,OAAO;YACP,OAAO;QACT;KACD;IACD,SAAS;QACP;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;QACT;QAAG;YACD,OAAO;YACP,OAAO;QACT;QAAG;YACD,OAAO;YACP,OAAO;QACT;KACD;IACD,KAAK;QACH;YACE,OAAO;YACP,OAAO;QACT;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;YACP,MAAM;QACR;KACD;IACD,SAAS;QACP;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;YACD,OAAO;YACP,OAAO;QACT;KACD;IACD,MAAM;QACJ;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;KACJ;IACD,OAAO;QACL;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;KACJ;IACD,SAAS;QACP;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;KACJ;IACD,UAAU;QACR;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;KACJ;IACD,IAAI;QACF;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;KACJ;IACD,OAAO;QACL;YACE,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAAG;KACJ;AACH;AACA,IAAK,IAAI,OAAO,MAAO;IACrB,IAAI,IAAI,KAAK,CAAC,IAAI;IAClB,IAAI,EAAE,MAAM,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,EAAE,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YAC5C,IAAI,KAAK,CAAC,CAAC,EAAE;YACb,IAAI,OAAO,GAAG,KAAK,KAAK,UAAU;gBAChC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,OAAO,MAAM,GAAG,KAAK;YACjD;QACF;IACF,OAAO,IAAI,OAAO,GAAG,KAAK,KAAK,UAAU;QACvC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK;IAC7C;AACF;AAEO,MAAM,aAAa;IACxB,MAAM;IACN,YAAY;QACV,OAAO;YACL,MAAM;YACN,WAAW;gBAAC,OAAO;gBAAM,QAAQ;gBAAG,SAAS;YAAE;QACjD;IACF;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,MAAO,OAAO,GAAG,IAAI,OAAO,KAAK,CAC/B,IAAI,QAAQ,UAAU,QAAQ;QAChC,MAAM,SAAS,GAAG;YAChB,OAAO;YACP,QAAQ,OAAO,WAAW;YAC1B,SAAS,OAAO,OAAO;QACzB;QACA,OAAO,MAAM,OAAO,CAAC,OAAO;IAC9B;IACA,QAAQ,SAAS,KAAK;QACpB,IAAI,cAAc,MAAM,SAAS,CAAC,MAAM;QACxC,IAAI,MAAM,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW;YAC3C,eAAe;QACjB;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}