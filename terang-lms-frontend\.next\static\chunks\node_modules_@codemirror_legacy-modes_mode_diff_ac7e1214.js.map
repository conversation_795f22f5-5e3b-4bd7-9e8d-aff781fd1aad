{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/diff.js"], "sourcesContent": ["var TOKEN_NAMES = {\n  '+': 'inserted',\n  '-': 'deleted',\n  '@': 'meta'\n};\n\nexport const diff = {\n  name: \"diff\",\n  token: function(stream) {\n    var tw_pos = stream.string.search(/[\\t ]+?$/);\n\n    if (!stream.sol() || tw_pos === 0) {\n      stream.skipToEnd();\n      return (\"error \" + (\n        TOKEN_NAMES[stream.string.charAt(0)] || '')).replace(/ $/, '');\n    }\n\n    var token_name = TOKEN_NAMES[stream.peek()] || stream.skipToEnd();\n\n    if (tw_pos === -1) {\n      stream.skipToEnd();\n    } else {\n      stream.pos = tw_pos;\n    }\n\n    return token_name;\n  }\n};\n\n"], "names": [], "mappings": ";;;AAAA,IAAI,cAAc;IAChB,KAAK;IACL,KAAK;IACL,KAAK;AACP;AAEO,MAAM,OAAO;IAClB,MAAM;IACN,OAAO,SAAS,MAAM;QACpB,IAAI,SAAS,OAAO,MAAM,CAAC,MAAM,CAAC;QAElC,IAAI,CAAC,OAAO,GAAG,MAAM,WAAW,GAAG;YACjC,OAAO,SAAS;YAChB,OAAO,CAAC,WAAW,CACjB,WAAW,CAAC,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,MAAM;QAC/D;QAEA,IAAI,aAAa,WAAW,CAAC,OAAO,IAAI,GAAG,IAAI,OAAO,SAAS;QAE/D,IAAI,WAAW,CAAC,GAAG;YACjB,OAAO,SAAS;QAClB,OAAO;YACL,OAAO,GAAG,GAAG;QACf;QAEA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}