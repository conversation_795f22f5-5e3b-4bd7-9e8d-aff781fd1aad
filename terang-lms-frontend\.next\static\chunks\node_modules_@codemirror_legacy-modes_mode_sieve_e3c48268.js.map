{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/sieve.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar keywords = words(\"if elsif else stop require\");\nvar atoms = words(\"true false not\");\n\nfunction tokenBase(stream, state) {\n\n  var ch = stream.next();\n  if (ch == \"/\" && stream.eat(\"*\")) {\n    state.tokenize = tokenCComment;\n    return tokenCComment(stream, state);\n  }\n\n  if (ch === '#') {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  if (ch == \"\\\"\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n\n  if (ch == \"(\") {\n    state._indent.push(\"(\");\n    // add virtual angel wings so that editor behaves...\n    // ...more sane incase of broken brackets\n    state._indent.push(\"{\");\n    return null;\n  }\n\n  if (ch === \"{\") {\n    state._indent.push(\"{\");\n    return null;\n  }\n\n  if (ch == \")\")  {\n    state._indent.pop();\n    state._indent.pop();\n  }\n\n  if (ch === \"}\") {\n    state._indent.pop();\n    return null;\n  }\n\n  if (ch == \",\")\n    return null;\n\n  if (ch == \";\")\n    return null;\n\n\n  if (/[{}\\(\\),;]/.test(ch))\n    return null;\n\n  // 1*DIGIT \"K\" / \"M\" / \"G\"\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\d]/);\n    stream.eat(/[KkMmGg]/);\n    return \"number\";\n  }\n\n  // \":\" (ALPHA / \"_\") *(ALPHA / DIGIT / \"_\")\n  if (ch == \":\") {\n    stream.eatWhile(/[a-zA-Z_]/);\n    stream.eatWhile(/[a-zA-Z0-9_]/);\n\n    return \"operator\";\n  }\n\n  stream.eatWhile(/\\w/);\n  var cur = stream.current();\n\n  // \"text:\" *(SP / HTAB) (hash-comment / CRLF)\n  // *(multiline-literal / multiline-dotstart)\n  // \".\" CRLF\n  if ((cur == \"text\") && stream.eat(\":\"))\n  {\n    state.tokenize = tokenMultiLineString;\n    return \"string\";\n  }\n\n  if (keywords.propertyIsEnumerable(cur))\n    return \"keyword\";\n\n  if (atoms.propertyIsEnumerable(cur))\n    return \"atom\";\n\n  return null;\n}\n\nfunction tokenMultiLineString(stream, state)\n{\n  state._multiLineString = true;\n  // the first line is special it may contain a comment\n  if (!stream.sol()) {\n    stream.eatSpace();\n\n    if (stream.peek() == \"#\") {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n\n    stream.skipToEnd();\n    return \"string\";\n  }\n\n  if ((stream.next() == \".\")  && (stream.eol()))\n  {\n    state._multiLineString = false;\n    state.tokenize = tokenBase;\n  }\n\n  return \"string\";\n}\n\nfunction tokenCComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped)\n        break;\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    if (!escaped) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nexport const sieve = {\n  name: \"sieve\",\n  startState: function(base) {\n    return {tokenize: tokenBase,\n            baseIndent: base || 0,\n            _indent: []};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace())\n      return null;\n\n    return (state.tokenize || tokenBase)(stream, state);\n  },\n\n  indent: function(state, _textAfter, cx) {\n    var length = state._indent.length;\n    if (_textAfter && (_textAfter[0] == \"}\"))\n      length--;\n\n    if (length <0)\n      length = 0;\n\n    return length * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*\\}$/\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AAEA,IAAI,WAAW,MAAM;AACrB,IAAI,QAAQ,MAAM;AAElB,SAAS,UAAU,MAAM,EAAE,KAAK;IAE9B,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;QAChC,MAAM,QAAQ,GAAG;QACjB,OAAO,cAAc,QAAQ;IAC/B;IAEA,IAAI,OAAO,KAAK;QACd,OAAO,SAAS;QAChB,OAAO;IACT;IAEA,IAAI,MAAM,MAAM;QACd,MAAM,QAAQ,GAAG,YAAY;QAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IAEA,IAAI,MAAM,KAAK;QACb,MAAM,OAAO,CAAC,IAAI,CAAC;QACnB,oDAAoD;QACpD,yCAAyC;QACzC,MAAM,OAAO,CAAC,IAAI,CAAC;QACnB,OAAO;IACT;IAEA,IAAI,OAAO,KAAK;QACd,MAAM,OAAO,CAAC,IAAI,CAAC;QACnB,OAAO;IACT;IAEA,IAAI,MAAM,KAAM;QACd,MAAM,OAAO,CAAC,GAAG;QACjB,MAAM,OAAO,CAAC,GAAG;IACnB;IAEA,IAAI,OAAO,KAAK;QACd,MAAM,OAAO,CAAC,GAAG;QACjB,OAAO;IACT;IAEA,IAAI,MAAM,KACR,OAAO;IAET,IAAI,MAAM,KACR,OAAO;IAGT,IAAI,aAAa,IAAI,CAAC,KACpB,OAAO;IAET,0BAA0B;IAC1B,IAAI,KAAK,IAAI,CAAC,KAAK;QACjB,OAAO,QAAQ,CAAC;QAChB,OAAO,GAAG,CAAC;QACX,OAAO;IACT;IAEA,2CAA2C;IAC3C,IAAI,MAAM,KAAK;QACb,OAAO,QAAQ,CAAC;QAChB,OAAO,QAAQ,CAAC;QAEhB,OAAO;IACT;IAEA,OAAO,QAAQ,CAAC;IAChB,IAAI,MAAM,OAAO,OAAO;IAExB,6CAA6C;IAC7C,4CAA4C;IAC5C,WAAW;IACX,IAAI,AAAC,OAAO,UAAW,OAAO,GAAG,CAAC,MAClC;QACE,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT;IAEA,IAAI,SAAS,oBAAoB,CAAC,MAChC,OAAO;IAET,IAAI,MAAM,oBAAoB,CAAC,MAC7B,OAAO;IAET,OAAO;AACT;AAEA,SAAS,qBAAqB,MAAM,EAAE,KAAK;IAEzC,MAAM,gBAAgB,GAAG;IACzB,qDAAqD;IACrD,IAAI,CAAC,OAAO,GAAG,IAAI;QACjB,OAAO,QAAQ;QAEf,IAAI,OAAO,IAAI,MAAM,KAAK;YACxB,OAAO,SAAS;YAChB,OAAO;QACT;QAEA,OAAO,SAAS;QAChB,OAAO;IACT;IAEA,IAAI,AAAC,OAAO,IAAI,MAAM,OAAU,OAAO,GAAG,IAC1C;QACE,MAAM,gBAAgB,GAAG;QACzB,MAAM,QAAQ,GAAG;IACnB;IAEA,OAAO;AACT;AAEA,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,IAAI,WAAW,OAAO;IACtB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;QACnC,IAAI,YAAY,MAAM,KAAK;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO;QACrB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;YACnC,IAAI,MAAM,SAAS,CAAC,SAClB;YACF,UAAU,CAAC,WAAW,MAAM;QAC9B;QACA,IAAI,CAAC,SAAS,MAAM,QAAQ,GAAG;QAC/B,OAAO;IACT;AACF;AAEO,MAAM,QAAQ;IACnB,MAAM;IACN,YAAY,SAAS,IAAI;QACvB,OAAO;YAAC,UAAU;YACV,YAAY,QAAQ;YACpB,SAAS,EAAE;QAAA;IACrB;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IACjB,OAAO;QAET,OAAO,CAAC,MAAM,QAAQ,IAAI,SAAS,EAAE,QAAQ;IAC/C;IAEA,QAAQ,SAAS,KAAK,EAAE,UAAU,EAAE,EAAE;QACpC,IAAI,SAAS,MAAM,OAAO,CAAC,MAAM;QACjC,IAAI,cAAe,UAAU,CAAC,EAAE,IAAI,KAClC;QAEF,IAAI,SAAQ,GACV,SAAS;QAEX,OAAO,SAAS,GAAG,IAAI;IACzB;IAEA,cAAc;QACZ,eAAe;IACjB;AACF", "ignoreList": [0], "debugId": null}}]}