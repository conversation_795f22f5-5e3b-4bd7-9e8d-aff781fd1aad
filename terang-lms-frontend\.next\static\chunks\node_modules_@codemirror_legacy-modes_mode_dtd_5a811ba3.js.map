{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/dtd.js"], "sourcesContent": ["var type;\nfunction ret(style, tp) {type = tp; return style;}\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n\n  if (ch == \"<\" && stream.eat(\"!\") ) {\n    if (stream.eatWhile(/[\\-]/)) {\n      state.tokenize = tokenSGMLComment;\n      return tokenSGMLComment(stream, state);\n    } else if (stream.eatWhile(/[\\w]/)) return ret(\"keyword\", \"doindent\");\n  } else if (ch == \"<\" && stream.eat(\"?\")) { //xml declaration\n    state.tokenize = inBlock(\"meta\", \"?>\");\n    return ret(\"meta\", ch);\n  } else if (ch == \"#\" && stream.eatWhile(/[\\w]/)) return ret(\"atom\", \"tag\");\n  else if (ch == \"|\") return ret(\"keyword\", \"separator\");\n  else if (ch.match(/[\\(\\)\\[\\]\\-\\.,\\+\\?>]/)) return ret(null, ch);//if(ch === \">\") return ret(null, \"endtag\"); else\n  else if (ch.match(/[\\[\\]]/)) return ret(\"rule\", ch);\n  else if (ch == \"\\\"\" || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  } else if (stream.eatWhile(/[a-zA-Z\\?\\+\\d]/)) {\n    var sc = stream.current();\n    if( sc.substr(sc.length-1,sc.length).match(/\\?|\\+/) !== null )stream.backUp(1);\n    return ret(\"tag\", \"tag\");\n  } else if (ch == \"%\" || ch == \"*\" ) return ret(\"number\", \"number\");\n  else {\n    stream.eatWhile(/[\\w\\\\\\-_%.{,]/);\n    return ret(null, null);\n  }\n}\n\nfunction tokenSGMLComment(stream, state) {\n  var dashes = 0, ch;\n  while ((ch = stream.next()) != null) {\n    if (dashes >= 2 && ch == \">\") {\n      state.tokenize = tokenBase;\n      break;\n    }\n    dashes = (ch == \"-\") ? dashes + 1 : 0;\n  }\n  return ret(\"comment\", \"comment\");\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    return ret(\"string\", \"tag\");\n  };\n}\n\nfunction inBlock(style, terminator) {\n  return function(stream, state) {\n    while (!stream.eol()) {\n      if (stream.match(terminator)) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      stream.next();\n    }\n    return style;\n  };\n}\n\nexport const dtd = {\n  name: \"dtd\",\n  startState: function() {\n    return {tokenize: tokenBase,\n            baseIndent: 0,\n            stack: []};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n\n    var context = state.stack[state.stack.length-1];\n    if (stream.current() == \"[\" || type === \"doindent\" || type == \"[\") state.stack.push(\"rule\");\n    else if (type === \"endtag\") state.stack[state.stack.length-1] = \"endtag\";\n    else if (stream.current() == \"]\" || type == \"]\" || (type == \">\" && context == \"rule\")) state.stack.pop();\n    else if (type == \"[\") state.stack.push(\"[\");\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var n = state.stack.length;\n\n    if( textAfter.charAt(0) === ']' )n--;\n    else if(textAfter.substr(textAfter.length-1, textAfter.length) === \">\"){\n      if(textAfter.substr(0,1) === \"<\") {}\n      else if( type == \"doindent\" && textAfter.length > 1 ) {}\n      else if( type == \"doindent\")n--;\n      else if( type == \">\" && textAfter.length > 1) {}\n      else if( type == \"tag\" && textAfter !== \">\") {}\n      else if( type == \"tag\" && state.stack[state.stack.length-1] == \"rule\")n--;\n      else if( type == \"tag\")n++;\n      else if( textAfter === \">\" && state.stack[state.stack.length-1] == \"rule\" && type === \">\")n--;\n      else if( textAfter === \">\" && state.stack[state.stack.length-1] == \"rule\") {}\n      else if( textAfter.substr(0,1) !== \"<\" && textAfter.substr(0,1) === \">\" )n=n-1;\n      else if( textAfter === \">\") {}\n      else n=n-1;\n      //over rule them all\n      if(type == null || type == \"]\")n--;\n    }\n\n    return state.baseIndent + n * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[\\]>]$/\n  }\n};\n\n"], "names": [], "mappings": ";;;AAAA,IAAI;AACJ,SAAS,IAAI,KAAK,EAAE,EAAE;IAAG,OAAO;IAAI,OAAO;AAAM;AAEjD,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IAEpB,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAO;QACjC,IAAI,OAAO,QAAQ,CAAC,SAAS;YAC3B,MAAM,QAAQ,GAAG;YACjB,OAAO,iBAAiB,QAAQ;QAClC,OAAO,IAAI,OAAO,QAAQ,CAAC,SAAS,OAAO,IAAI,WAAW;IAC5D,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;QACvC,MAAM,QAAQ,GAAG,QAAQ,QAAQ;QACjC,OAAO,IAAI,QAAQ;IACrB,OAAO,IAAI,MAAM,OAAO,OAAO,QAAQ,CAAC,SAAS,OAAO,IAAI,QAAQ;SAC/D,IAAI,MAAM,KAAK,OAAO,IAAI,WAAW;SACrC,IAAI,GAAG,KAAK,CAAC,yBAAyB,OAAO,IAAI,MAAM,KAAI,iDAAiD;SAC5G,IAAI,GAAG,KAAK,CAAC,WAAW,OAAO,IAAI,QAAQ;SAC3C,IAAI,MAAM,QAAQ,MAAM,KAAK;QAChC,MAAM,QAAQ,GAAG,YAAY;QAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC,OAAO,IAAI,OAAO,QAAQ,CAAC,mBAAmB;QAC5C,IAAI,KAAK,OAAO,OAAO;QACvB,IAAI,GAAG,MAAM,CAAC,GAAG,MAAM,GAAC,GAAE,GAAG,MAAM,EAAE,KAAK,CAAC,aAAa,MAAM,OAAO,MAAM,CAAC;QAC5E,OAAO,IAAI,OAAO;IACpB,OAAO,IAAI,MAAM,OAAO,MAAM,KAAM,OAAO,IAAI,UAAU;SACpD;QACH,OAAO,QAAQ,CAAC;QAChB,OAAO,IAAI,MAAM;IACnB;AACF;AAEA,SAAS,iBAAiB,MAAM,EAAE,KAAK;IACrC,IAAI,SAAS,GAAG;IAChB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;QACnC,IAAI,UAAU,KAAK,MAAM,KAAK;YAC5B,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,SAAS,AAAC,MAAM,MAAO,SAAS,IAAI;IACtC;IACA,OAAO,IAAI,WAAW;AACxB;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO;QACrB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;YACnC,IAAI,MAAM,SAAS,CAAC,SAAS;gBAC3B,MAAM,QAAQ,GAAG;gBACjB;YACF;YACA,UAAU,CAAC,WAAW,MAAM;QAC9B;QACA,OAAO,IAAI,UAAU;IACvB;AACF;AAEA,SAAS,QAAQ,KAAK,EAAE,UAAU;IAChC,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,MAAO,CAAC,OAAO,GAAG,GAAI;YACpB,IAAI,OAAO,KAAK,CAAC,aAAa;gBAC5B,MAAM,QAAQ,GAAG;gBACjB;YACF;YACA,OAAO,IAAI;QACb;QACA,OAAO;IACT;AACF;AAEO,MAAM,MAAM;IACjB,MAAM;IACN,YAAY;QACV,OAAO;YAAC,UAAU;YACV,YAAY;YACZ,OAAO,EAAE;QAAA;IACnB;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QAEnC,IAAI,UAAU,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAC,EAAE;QAC/C,IAAI,OAAO,OAAO,MAAM,OAAO,SAAS,cAAc,QAAQ,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;aAC/E,IAAI,SAAS,UAAU,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAC,EAAE,GAAG;aAC3D,IAAI,OAAO,OAAO,MAAM,OAAO,QAAQ,OAAQ,QAAQ,OAAO,WAAW,QAAS,MAAM,KAAK,CAAC,GAAG;aACjG,IAAI,QAAQ,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QACvC,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,IAAI,MAAM,KAAK,CAAC,MAAM;QAE1B,IAAI,UAAU,MAAM,CAAC,OAAO,KAAK;aAC5B,IAAG,UAAU,MAAM,CAAC,UAAU,MAAM,GAAC,GAAG,UAAU,MAAM,MAAM,KAAI;YACrE,IAAG,UAAU,MAAM,CAAC,GAAE,OAAO,KAAK,CAAC,OAC9B,IAAI,QAAQ,cAAc,UAAU,MAAM,GAAG,GAAI,CAAC,OAClD,IAAI,QAAQ,YAAW;iBACvB,IAAI,QAAQ,OAAO,UAAU,MAAM,GAAG,GAAG,CAAC,OAC1C,IAAI,QAAQ,SAAS,cAAc,KAAK,CAAC,OACzC,IAAI,QAAQ,SAAS,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAC,EAAE,IAAI,QAAO;iBACjE,IAAI,QAAQ,OAAM;iBAClB,IAAI,cAAc,OAAO,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAC,EAAE,IAAI,UAAU,SAAS,KAAI;iBACrF,IAAI,cAAc,OAAO,MAAM,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,GAAC,EAAE,IAAI,QAAQ,CAAC,OACvE,IAAI,UAAU,MAAM,CAAC,GAAE,OAAO,OAAO,UAAU,MAAM,CAAC,GAAE,OAAO,KAAK,IAAE,IAAE;iBACxE,IAAI,cAAc,KAAK,CAAC,OACxB,IAAE,IAAE;YACT,oBAAoB;YACpB,IAAG,QAAQ,QAAQ,QAAQ,KAAI;QACjC;QAEA,OAAO,MAAM,UAAU,GAAG,IAAI,GAAG,IAAI;IACvC;IAEA,cAAc;QACZ,eAAe;IACjB;AACF", "ignoreList": [0], "debugId": null}}]}