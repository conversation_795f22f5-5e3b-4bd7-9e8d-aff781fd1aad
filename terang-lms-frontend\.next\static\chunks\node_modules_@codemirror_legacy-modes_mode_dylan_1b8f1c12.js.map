{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/dylan.js"], "sourcesContent": ["function forEach(arr, f) {\n  for (var i = 0; i < arr.length; i++) f(arr[i], i)\n}\nfunction some(arr, f) {\n  for (var i = 0; i < arr.length; i++) if (f(arr[i], i)) return true\n  return false\n}\n\n// Words\nvar words = {\n  // Words that introduce unnamed definitions like \"define interface\"\n  unnamedDefinition: [\"interface\"],\n\n  // Words that introduce simple named definitions like \"define library\"\n  namedDefinition: [\"module\", \"library\", \"macro\",\n                    \"C-struct\", \"C-union\",\n                    \"C-function\", \"C-callable-wrapper\"\n                   ],\n\n  // Words that introduce type definitions like \"define class\".\n  // These are also parameterized like \"define method\" and are\n  // appended to otherParameterizedDefinitionWords\n  typeParameterizedDefinition: [\"class\", \"C-subtype\", \"C-mapped-subtype\"],\n\n  // Words that introduce trickier definitions like \"define method\".\n  // These require special definitions to be added to startExpressions\n  otherParameterizedDefinition: [\"method\", \"function\",\n                                 \"C-variable\", \"C-address\"\n                                ],\n\n  // Words that introduce module constant definitions.\n  // These must also be simple definitions and are\n  // appended to otherSimpleDefinitionWords\n  constantSimpleDefinition: [\"constant\"],\n\n  // Words that introduce module variable definitions.\n  // These must also be simple definitions and are\n  // appended to otherSimpleDefinitionWords\n  variableSimpleDefinition: [\"variable\"],\n\n  // Other words that introduce simple definitions\n  // (without implicit bodies).\n  otherSimpleDefinition: [\"generic\", \"domain\",\n                          \"C-pointer-type\",\n                          \"table\"\n                         ],\n\n  // Words that begin statements with implicit bodies.\n  statement: [\"if\", \"block\", \"begin\", \"method\", \"case\",\n              \"for\", \"select\", \"when\", \"unless\", \"until\",\n              \"while\", \"iterate\", \"profiling\", \"dynamic-bind\"\n             ],\n\n  // Patterns that act as separators in compound statements.\n  // This may include any general pattern that must be indented\n  // specially.\n  separator: [\"finally\", \"exception\", \"cleanup\", \"else\",\n              \"elseif\", \"afterwards\"\n             ],\n\n  // Keywords that do not require special indentation handling,\n  // but which should be highlighted\n  other: [\"above\", \"below\", \"by\", \"from\", \"handler\", \"in\",\n          \"instance\", \"let\", \"local\", \"otherwise\", \"slot\",\n          \"subclass\", \"then\", \"to\", \"keyed-by\", \"virtual\"\n         ],\n\n  // Condition signaling function calls\n  signalingCalls: [\"signal\", \"error\", \"cerror\",\n                   \"break\", \"check-type\", \"abort\"\n                  ]\n};\n\nwords[\"otherDefinition\"] =\n  words[\"unnamedDefinition\"]\n  .concat(words[\"namedDefinition\"])\n  .concat(words[\"otherParameterizedDefinition\"]);\n\nwords[\"definition\"] =\n  words[\"typeParameterizedDefinition\"]\n  .concat(words[\"otherDefinition\"]);\n\nwords[\"parameterizedDefinition\"] =\n  words[\"typeParameterizedDefinition\"]\n  .concat(words[\"otherParameterizedDefinition\"]);\n\nwords[\"simpleDefinition\"] =\n  words[\"constantSimpleDefinition\"]\n  .concat(words[\"variableSimpleDefinition\"])\n  .concat(words[\"otherSimpleDefinition\"]);\n\nwords[\"keyword\"] =\n  words[\"statement\"]\n  .concat(words[\"separator\"])\n  .concat(words[\"other\"]);\n\n// Patterns\nvar symbolPattern = \"[-_a-zA-Z?!*@<>$%]+\";\nvar symbol = new RegExp(\"^\" + symbolPattern);\nvar patterns = {\n  // Symbols with special syntax\n  symbolKeyword: symbolPattern + \":\",\n  symbolClass: \"<\" + symbolPattern + \">\",\n  symbolGlobal: \"\\\\*\" + symbolPattern + \"\\\\*\",\n  symbolConstant: \"\\\\$\" + symbolPattern\n};\nvar patternStyles = {\n  symbolKeyword: \"atom\",\n  symbolClass: \"tag\",\n  symbolGlobal: \"variableName.standard\",\n  symbolConstant: \"variableName.constant\"\n};\n\n// Compile all patterns to regular expressions\nfor (var patternName in patterns)\n  if (patterns.hasOwnProperty(patternName))\n    patterns[patternName] = new RegExp(\"^\" + patterns[patternName]);\n\n// Names beginning \"with-\" and \"without-\" are commonly\n// used as statement macro\npatterns[\"keyword\"] = [/^with(?:out)?-[-_a-zA-Z?!*@<>$%]+/];\n\nvar styles = {};\nstyles[\"keyword\"] = \"keyword\";\nstyles[\"definition\"] = \"def\";\nstyles[\"simpleDefinition\"] = \"def\";\nstyles[\"signalingCalls\"] = \"builtin\";\n\n// protected words lookup table\nvar wordLookup = {};\nvar styleLookup = {};\n\nforEach([\n  \"keyword\",\n  \"definition\",\n  \"simpleDefinition\",\n  \"signalingCalls\"\n], function(type) {\n  forEach(words[type], function(word) {\n    wordLookup[word] = type;\n    styleLookup[word] = styles[type];\n  });\n});\n\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\n\nfunction tokenBase(stream, state) {\n  // String\n  var ch = stream.peek();\n  if (ch == \"'\" || ch == '\"') {\n    stream.next();\n    return chain(stream, state, tokenString(ch, \"string\"));\n  }\n  // Comment\n  else if (ch == \"/\") {\n    stream.next();\n    if (stream.eat(\"*\")) {\n      return chain(stream, state, tokenComment);\n    } else if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    stream.backUp(1);\n  }\n  // Decimal\n  else if (/[+\\-\\d\\.]/.test(ch)) {\n    if (stream.match(/^[+-]?[0-9]*\\.[0-9]*([esdx][+-]?[0-9]+)?/i) ||\n        stream.match(/^[+-]?[0-9]+([esdx][+-]?[0-9]+)/i) ||\n        stream.match(/^[+-]?\\d+/)) {\n      return \"number\";\n    }\n  }\n  // Hash\n  else if (ch == \"#\") {\n    stream.next();\n    // Symbol with string syntax\n    ch = stream.peek();\n    if (ch == '\"') {\n      stream.next();\n      return chain(stream, state, tokenString('\"', \"string\"));\n    }\n    // Binary number\n    else if (ch == \"b\") {\n      stream.next();\n      stream.eatWhile(/[01]/);\n      return \"number\";\n    }\n    // Hex number\n    else if (ch == \"x\") {\n      stream.next();\n      stream.eatWhile(/[\\da-f]/i);\n      return \"number\";\n    }\n    // Octal number\n    else if (ch == \"o\") {\n      stream.next();\n      stream.eatWhile(/[0-7]/);\n      return \"number\";\n    }\n    // Token concatenation in macros\n    else if (ch == '#') {\n      stream.next();\n      return \"punctuation\";\n    }\n    // Sequence literals\n    else if ((ch == '[') || (ch == '(')) {\n      stream.next();\n      return \"bracket\";\n      // Hash symbol\n    } else if (stream.match(/f|t|all-keys|include|key|next|rest/i)) {\n      return \"atom\";\n    } else {\n      stream.eatWhile(/[-a-zA-Z]/);\n      return \"error\";\n    }\n  } else if (ch == \"~\") {\n    stream.next();\n    ch = stream.peek();\n    if (ch == \"=\") {\n      stream.next();\n      ch = stream.peek();\n      if (ch == \"=\") {\n        stream.next();\n        return \"operator\";\n      }\n      return \"operator\";\n    }\n    return \"operator\";\n  } else if (ch == \":\") {\n    stream.next();\n    ch = stream.peek();\n    if (ch == \"=\") {\n      stream.next();\n      return \"operator\";\n    } else if (ch == \":\") {\n      stream.next();\n      return \"punctuation\";\n    }\n  } else if (\"[](){}\".indexOf(ch) != -1) {\n    stream.next();\n    return \"bracket\";\n  } else if (\".,\".indexOf(ch) != -1) {\n    stream.next();\n    return \"punctuation\";\n  } else if (stream.match(\"end\")) {\n    return \"keyword\";\n  }\n  for (var name in patterns) {\n    if (patterns.hasOwnProperty(name)) {\n      var pattern = patterns[name];\n      if ((pattern instanceof Array && some(pattern, function(p) {\n        return stream.match(p);\n      })) || stream.match(pattern))\n        return patternStyles[name];\n    }\n  }\n  if (/[+\\-*\\/^=<>&|]/.test(ch)) {\n    stream.next();\n    return \"operator\";\n  }\n  if (stream.match(\"define\")) {\n    return \"def\";\n  } else {\n    stream.eatWhile(/[\\w\\-]/);\n    // Keyword\n    if (wordLookup.hasOwnProperty(stream.current())) {\n      return styleLookup[stream.current()];\n    } else if (stream.current().match(symbol)) {\n      return \"variable\";\n    } else {\n      stream.next();\n      return \"variableName.standard\";\n    }\n  }\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, maybeNested = false, nestedCount = 0, ch;\n  while ((ch = stream.next())) {\n    if (ch == \"/\" && maybeEnd) {\n      if (nestedCount > 0) {\n        nestedCount--;\n      } else {\n        state.tokenize = tokenBase;\n        break;\n      }\n    } else if (ch == \"*\" && maybeNested) {\n      nestedCount++;\n    }\n    maybeEnd = (ch == \"*\");\n    maybeNested = (ch == \"/\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(quote, style) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped) {\n      state.tokenize = tokenBase;\n    }\n    return style;\n  };\n}\n\n// Interface\nexport const dylan = {\n  name: \"dylan\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      currentIndent: 0\n    };\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace())\n      return null;\n    var style = state.tokenize(stream, state);\n    return style;\n  },\n  languageData: {\n    commentTokens: {block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n\n"], "names": [], "mappings": ";;;AAAA,SAAS,QAAQ,GAAG,EAAE,CAAC;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK,EAAE,GAAG,CAAC,EAAE,EAAE;AACjD;AACA,SAAS,KAAK,GAAG,EAAE,CAAC;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,OAAO;IAC9D,OAAO;AACT;AAEA,QAAQ;AACR,IAAI,QAAQ;IACV,mEAAmE;IACnE,mBAAmB;QAAC;KAAY;IAEhC,sEAAsE;IACtE,iBAAiB;QAAC;QAAU;QAAW;QACrB;QAAY;QACZ;QAAc;KACd;IAElB,6DAA6D;IAC7D,4DAA4D;IAC5D,gDAAgD;IAChD,6BAA6B;QAAC;QAAS;QAAa;KAAmB;IAEvE,kEAAkE;IAClE,oEAAoE;IACpE,8BAA8B;QAAC;QAAU;QACV;QAAc;KACd;IAE/B,oDAAoD;IACpD,gDAAgD;IAChD,yCAAyC;IACzC,0BAA0B;QAAC;KAAW;IAEtC,oDAAoD;IACpD,gDAAgD;IAChD,yCAAyC;IACzC,0BAA0B;QAAC;KAAW;IAEtC,gDAAgD;IAChD,6BAA6B;IAC7B,uBAAuB;QAAC;QAAW;QACX;QACA;KACA;IAExB,oDAAoD;IACpD,WAAW;QAAC;QAAM;QAAS;QAAS;QAAU;QAClC;QAAO;QAAU;QAAQ;QAAU;QACnC;QAAS;QAAW;QAAa;KACjC;IAEZ,0DAA0D;IAC1D,6DAA6D;IAC7D,aAAa;IACb,WAAW;QAAC;QAAW;QAAa;QAAW;QACnC;QAAU;KACV;IAEZ,6DAA6D;IAC7D,kCAAkC;IAClC,OAAO;QAAC;QAAS;QAAS;QAAM;QAAQ;QAAW;QAC3C;QAAY;QAAO;QAAS;QAAa;QACzC;QAAY;QAAQ;QAAM;QAAY;KACtC;IAER,qCAAqC;IACrC,gBAAgB;QAAC;QAAU;QAAS;QACnB;QAAS;QAAc;KACvB;AACnB;AAEA,KAAK,CAAC,kBAAkB,GACtB,KAAK,CAAC,oBAAoB,CACzB,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAC/B,MAAM,CAAC,KAAK,CAAC,+BAA+B;AAE/C,KAAK,CAAC,aAAa,GACjB,KAAK,CAAC,8BAA8B,CACnC,MAAM,CAAC,KAAK,CAAC,kBAAkB;AAElC,KAAK,CAAC,0BAA0B,GAC9B,KAAK,CAAC,8BAA8B,CACnC,MAAM,CAAC,KAAK,CAAC,+BAA+B;AAE/C,KAAK,CAAC,mBAAmB,GACvB,KAAK,CAAC,2BAA2B,CAChC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EACxC,MAAM,CAAC,KAAK,CAAC,wBAAwB;AAExC,KAAK,CAAC,UAAU,GACd,KAAK,CAAC,YAAY,CACjB,MAAM,CAAC,KAAK,CAAC,YAAY,EACzB,MAAM,CAAC,KAAK,CAAC,QAAQ;AAExB,WAAW;AACX,IAAI,gBAAgB;AACpB,IAAI,SAAS,IAAI,OAAO,MAAM;AAC9B,IAAI,WAAW;IACb,8BAA8B;IAC9B,eAAe,gBAAgB;IAC/B,aAAa,MAAM,gBAAgB;IACnC,cAAc,QAAQ,gBAAgB;IACtC,gBAAgB,QAAQ;AAC1B;AACA,IAAI,gBAAgB;IAClB,eAAe;IACf,aAAa;IACb,cAAc;IACd,gBAAgB;AAClB;AAEA,8CAA8C;AAC9C,IAAK,IAAI,eAAe,SACtB,IAAI,SAAS,cAAc,CAAC,cAC1B,QAAQ,CAAC,YAAY,GAAG,IAAI,OAAO,MAAM,QAAQ,CAAC,YAAY;AAElE,sDAAsD;AACtD,0BAA0B;AAC1B,QAAQ,CAAC,UAAU,GAAG;IAAC;CAAoC;AAE3D,IAAI,SAAS,CAAC;AACd,MAAM,CAAC,UAAU,GAAG;AACpB,MAAM,CAAC,aAAa,GAAG;AACvB,MAAM,CAAC,mBAAmB,GAAG;AAC7B,MAAM,CAAC,iBAAiB,GAAG;AAE3B,+BAA+B;AAC/B,IAAI,aAAa,CAAC;AAClB,IAAI,cAAc,CAAC;AAEnB,QAAQ;IACN;IACA;IACA;IACA;CACD,EAAE,SAAS,IAAI;IACd,QAAQ,KAAK,CAAC,KAAK,EAAE,SAAS,IAAI;QAChC,UAAU,CAAC,KAAK,GAAG;QACnB,WAAW,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;IAClC;AACF;AAGA,SAAS,MAAM,MAAM,EAAE,KAAK,EAAE,CAAC;IAC7B,MAAM,QAAQ,GAAG;IACjB,OAAO,EAAE,QAAQ;AACnB;AAEA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,SAAS;IACT,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,OAAO,MAAM,KAAK;QAC1B,OAAO,IAAI;QACX,OAAO,MAAM,QAAQ,OAAO,YAAY,IAAI;IAC9C,OAEK,IAAI,MAAM,KAAK;QAClB,OAAO,IAAI;QACX,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO,MAAM,QAAQ,OAAO;QAC9B,OAAO,IAAI,OAAO,GAAG,CAAC,MAAM;YAC1B,OAAO,SAAS;YAChB,OAAO;QACT;QACA,OAAO,MAAM,CAAC;IAChB,OAEK,IAAI,YAAY,IAAI,CAAC,KAAK;QAC7B,IAAI,OAAO,KAAK,CAAC,gDACb,OAAO,KAAK,CAAC,uCACb,OAAO,KAAK,CAAC,cAAc;YAC7B,OAAO;QACT;IACF,OAEK,IAAI,MAAM,KAAK;QAClB,OAAO,IAAI;QACX,4BAA4B;QAC5B,KAAK,OAAO,IAAI;QAChB,IAAI,MAAM,KAAK;YACb,OAAO,IAAI;YACX,OAAO,MAAM,QAAQ,OAAO,YAAY,KAAK;QAC/C,OAEK,IAAI,MAAM,KAAK;YAClB,OAAO,IAAI;YACX,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT,OAEK,IAAI,MAAM,KAAK;YAClB,OAAO,IAAI;YACX,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT,OAEK,IAAI,MAAM,KAAK;YAClB,OAAO,IAAI;YACX,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT,OAEK,IAAI,MAAM,KAAK;YAClB,OAAO,IAAI;YACX,OAAO;QACT,OAEK,IAAI,AAAC,MAAM,OAAS,MAAM,KAAM;YACnC,OAAO,IAAI;YACX,OAAO;QACP,cAAc;QAChB,OAAO,IAAI,OAAO,KAAK,CAAC,wCAAwC;YAC9D,OAAO;QACT,OAAO;YACL,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;IACF,OAAO,IAAI,MAAM,KAAK;QACpB,OAAO,IAAI;QACX,KAAK,OAAO,IAAI;QAChB,IAAI,MAAM,KAAK;YACb,OAAO,IAAI;YACX,KAAK,OAAO,IAAI;YAChB,IAAI,MAAM,KAAK;gBACb,OAAO,IAAI;gBACX,OAAO;YACT;YACA,OAAO;QACT;QACA,OAAO;IACT,OAAO,IAAI,MAAM,KAAK;QACpB,OAAO,IAAI;QACX,KAAK,OAAO,IAAI;QAChB,IAAI,MAAM,KAAK;YACb,OAAO,IAAI;YACX,OAAO;QACT,OAAO,IAAI,MAAM,KAAK;YACpB,OAAO,IAAI;YACX,OAAO;QACT;IACF,OAAO,IAAI,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG;QACrC,OAAO,IAAI;QACX,OAAO;IACT,OAAO,IAAI,KAAK,OAAO,CAAC,OAAO,CAAC,GAAG;QACjC,OAAO,IAAI;QACX,OAAO;IACT,OAAO,IAAI,OAAO,KAAK,CAAC,QAAQ;QAC9B,OAAO;IACT;IACA,IAAK,IAAI,QAAQ,SAAU;QACzB,IAAI,SAAS,cAAc,CAAC,OAAO;YACjC,IAAI,UAAU,QAAQ,CAAC,KAAK;YAC5B,IAAI,AAAC,mBAAmB,SAAS,KAAK,SAAS,SAAS,CAAC;gBACvD,OAAO,OAAO,KAAK,CAAC;YACtB,MAAO,OAAO,KAAK,CAAC,UAClB,OAAO,aAAa,CAAC,KAAK;QAC9B;IACF;IACA,IAAI,iBAAiB,IAAI,CAAC,KAAK;QAC7B,OAAO,IAAI;QACX,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,WAAW;QAC1B,OAAO;IACT,OAAO;QACL,OAAO,QAAQ,CAAC;QAChB,UAAU;QACV,IAAI,WAAW,cAAc,CAAC,OAAO,OAAO,KAAK;YAC/C,OAAO,WAAW,CAAC,OAAO,OAAO,GAAG;QACtC,OAAO,IAAI,OAAO,OAAO,GAAG,KAAK,CAAC,SAAS;YACzC,OAAO;QACT,OAAO;YACL,OAAO,IAAI;YACX,OAAO;QACT;IACF;AACF;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,WAAW,OAAO,cAAc,OAAO,cAAc,GAAG;IAC5D,MAAQ,KAAK,OAAO,IAAI,GAAK;QAC3B,IAAI,MAAM,OAAO,UAAU;YACzB,IAAI,cAAc,GAAG;gBACnB;YACF,OAAO;gBACL,MAAM,QAAQ,GAAG;gBACjB;YACF;QACF,OAAO,IAAI,MAAM,OAAO,aAAa;YACnC;QACF;QACA,WAAY,MAAM;QAClB,cAAe,MAAM;IACvB;IACA,OAAO;AACT;AAEA,SAAS,YAAY,KAAK,EAAE,KAAK;IAC/B,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;QACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,QAAQ,SAAS,CAAC,SAAS;gBAC7B,MAAM;gBACN;YACF;YACA,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,OAAO,CAAC,SAAS;YACnB,MAAM,QAAQ,GAAG;QACnB;QACA,OAAO;IACT;AACF;AAGO,MAAM,QAAQ;IACnB,MAAM;IACN,YAAY;QACV,OAAO;YACL,UAAU;YACV,eAAe;QACjB;IACF;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IACjB,OAAO;QACT,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QACnC,OAAO;IACT;IACA,cAAc;QACZ,eAAe;YAAC,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;IAClD;AACF", "ignoreList": [0], "debugId": null}}]}