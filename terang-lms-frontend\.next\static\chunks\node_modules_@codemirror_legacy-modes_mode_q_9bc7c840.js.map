{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/q.js"], "sourcesContent": ["var curPunc,\n    keywords=buildRE([\"abs\",\"acos\",\"aj\",\"aj0\",\"all\",\"and\",\"any\",\"asc\",\"asin\",\"asof\",\"atan\",\"attr\",\"avg\",\"avgs\",\"bin\",\"by\",\"ceiling\",\"cols\",\"cor\",\"cos\",\"count\",\"cov\",\"cross\",\"csv\",\"cut\",\"delete\",\"deltas\",\"desc\",\"dev\",\"differ\",\"distinct\",\"div\",\"do\",\"each\",\"ej\",\"enlist\",\"eval\",\"except\",\"exec\",\"exit\",\"exp\",\"fby\",\"fills\",\"first\",\"fkeys\",\"flip\",\"floor\",\"from\",\"get\",\"getenv\",\"group\",\"gtime\",\"hclose\",\"hcount\",\"hdel\",\"hopen\",\"hsym\",\"iasc\",\"idesc\",\"if\",\"ij\",\"in\",\"insert\",\"inter\",\"inv\",\"key\",\"keys\",\"last\",\"like\",\"list\",\"lj\",\"load\",\"log\",\"lower\",\"lsq\",\"ltime\",\"ltrim\",\"mavg\",\"max\",\"maxs\",\"mcount\",\"md5\",\"mdev\",\"med\",\"meta\",\"min\",\"mins\",\"mmax\",\"mmin\",\"mmu\",\"mod\",\"msum\",\"neg\",\"next\",\"not\",\"null\",\"or\",\"over\",\"parse\",\"peach\",\"pj\",\"plist\",\"prd\",\"prds\",\"prev\",\"prior\",\"rand\",\"rank\",\"ratios\",\"raze\",\"read0\",\"read1\",\"reciprocal\",\"reverse\",\"rload\",\"rotate\",\"rsave\",\"rtrim\",\"save\",\"scan\",\"select\",\"set\",\"setenv\",\"show\",\"signum\",\"sin\",\"sqrt\",\"ss\",\"ssr\",\"string\",\"sublist\",\"sum\",\"sums\",\"sv\",\"system\",\"tables\",\"tan\",\"til\",\"trim\",\"txf\",\"type\",\"uj\",\"ungroup\",\"union\",\"update\",\"upper\",\"upsert\",\"value\",\"var\",\"view\",\"views\",\"vs\",\"wavg\",\"where\",\"where\",\"while\",\"within\",\"wj\",\"wj1\",\"wsum\",\"xasc\",\"xbar\",\"xcol\",\"xcols\",\"xdesc\",\"xexp\",\"xgroup\",\"xkey\",\"xlog\",\"xprev\",\"xrank\"]),\n    E=/[|/&^!+:\\\\\\-*%$=~#;@><,?_\\'\\\"\\[\\(\\]\\)\\s{}]/;\nfunction buildRE(w){return new RegExp(\"^(\"+w.join(\"|\")+\")$\");}\nfunction tokenBase(stream,state){\n  var sol=stream.sol(),c=stream.next();\n  curPunc=null;\n  if(sol)\n    if(c==\"/\")\n      return(state.tokenize=tokenLineComment)(stream,state);\n  else if(c==\"\\\\\"){\n    if(stream.eol()||/\\s/.test(stream.peek()))\n      return stream.skipToEnd(),/^\\\\\\s*$/.test(stream.current())?(state.tokenize=tokenCommentToEOF)(stream):state.tokenize=tokenBase,\"comment\";\n    else\n      return state.tokenize=tokenBase,\"builtin\";\n  }\n  if(/\\s/.test(c))\n    return stream.peek()==\"/\"?(stream.skipToEnd(),\"comment\"):\"null\";\n  if(c=='\"')\n    return(state.tokenize=tokenString)(stream,state);\n  if(c=='`')\n    return stream.eatWhile(/[A-Za-z\\d_:\\/.]/),\"macroName\";\n  if((\".\"==c&&/\\d/.test(stream.peek()))||/\\d/.test(c)){\n    var t=null;\n    stream.backUp(1);\n    if(stream.match(/^\\d{4}\\.\\d{2}(m|\\.\\d{2}([DT](\\d{2}(:\\d{2}(:\\d{2}(\\.\\d{1,9})?)?)?)?)?)/)\n       || stream.match(/^\\d+D(\\d{2}(:\\d{2}(:\\d{2}(\\.\\d{1,9})?)?)?)/)\n       || stream.match(/^\\d{2}:\\d{2}(:\\d{2}(\\.\\d{1,9})?)?/)\n       || stream.match(/^\\d+[ptuv]{1}/))\n      t=\"temporal\";\n    else if(stream.match(/^0[NwW]{1}/)\n            || stream.match(/^0x[\\da-fA-F]*/)\n            || stream.match(/^[01]+[b]{1}/)\n            || stream.match(/^\\d+[chijn]{1}/)\n            || stream.match(/-?\\d*(\\.\\d*)?(e[+\\-]?\\d+)?(e|f)?/))\n      t=\"number\";\n    return(t&&(!(c=stream.peek())||E.test(c)))?t:(stream.next(),\"error\");\n  }\n  if(/[A-Za-z]|\\./.test(c))\n    return stream.eatWhile(/[A-Za-z._\\d]/),keywords.test(stream.current())?\"keyword\":\"variable\";\n  if(/[|/&^!+:\\\\\\-*%$=~#;@><\\.,?_\\']/.test(c))\n    return null;\n  if(/[{}\\(\\[\\]\\)]/.test(c))\n    return null;\n  return\"error\";\n}\nfunction tokenLineComment(stream,state){\n  return stream.skipToEnd(),/\\/\\s*$/.test(stream.current())?(state.tokenize=tokenBlockComment)(stream,state):(state.tokenize=tokenBase),\"comment\";\n}\nfunction tokenBlockComment(stream,state){\n  var f=stream.sol()&&stream.peek()==\"\\\\\";\n  stream.skipToEnd();\n  if(f&&/^\\\\\\s*$/.test(stream.current()))\n    state.tokenize=tokenBase;\n  return\"comment\";\n}\nfunction tokenCommentToEOF(stream){return stream.skipToEnd(),\"comment\";}\nfunction tokenString(stream,state){\n  var escaped=false,next,end=false;\n  while((next=stream.next())){\n    if(next==\"\\\"\"&&!escaped){end=true;break;}\n    escaped=!escaped&&next==\"\\\\\";\n  }\n  if(end)state.tokenize=tokenBase;\n  return\"string\";\n}\nfunction pushContext(state,type,col){state.context={prev:state.context,indent:state.indent,col:col,type:type};}\nfunction popContext(state){state.indent=state.context.indent;state.context=state.context.prev;}\nexport const q = {\n  name: \"q\",\n  startState:function(){\n    return{tokenize:tokenBase,\n           context:null,\n           indent:0,\n           col:0};\n  },\n  token:function(stream,state){\n    if(stream.sol()){\n      if(state.context&&state.context.align==null)\n        state.context.align=false;\n      state.indent=stream.indentation();\n    }\n    //if (stream.eatSpace()) return null;\n    var style=state.tokenize(stream,state);\n    if(style!=\"comment\"&&state.context&&state.context.align==null&&state.context.type!=\"pattern\"){\n      state.context.align=true;\n    }\n    if(curPunc==\"(\")pushContext(state,\")\",stream.column());\n    else if(curPunc==\"[\")pushContext(state,\"]\",stream.column());\n    else if(curPunc==\"{\")pushContext(state,\"}\",stream.column());\n    else if(/[\\]\\}\\)]/.test(curPunc)){\n      while(state.context&&state.context.type==\"pattern\")popContext(state);\n      if(state.context&&curPunc==state.context.type)popContext(state);\n    }\n    else if(curPunc==\".\"&&state.context&&state.context.type==\"pattern\")popContext(state);\n    else if(/atom|string|variable/.test(style)&&state.context){\n      if(/[\\}\\]]/.test(state.context.type))\n        pushContext(state,\"pattern\",stream.column());\n      else if(state.context.type==\"pattern\"&&!state.context.align){\n        state.context.align=true;\n        state.context.col=stream.column();\n      }\n    }\n    return style;\n  },\n  indent:function(state,textAfter,cx){\n    var firstChar=textAfter&&textAfter.charAt(0);\n    var context=state.context;\n    if(/[\\]\\}]/.test(firstChar))\n      while (context&&context.type==\"pattern\")context=context.prev;\n    var closing=context&&firstChar==context.type;\n    if(!context)\n      return 0;\n    else if(context.type==\"pattern\")\n      return context.col;\n    else if(context.align)\n      return context.col+(closing?0:1);\n    else\n      return context.indent+(closing?0:cx.unit);\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,SACA,WAAS,QAAQ;IAAC;IAAM;IAAO;IAAK;IAAM;IAAM;IAAM;IAAM;IAAM;IAAO;IAAO;IAAO;IAAO;IAAM;IAAO;IAAM;IAAK;IAAU;IAAO;IAAM;IAAM;IAAQ;IAAM;IAAQ;IAAM;IAAM;IAAS;IAAS;IAAO;IAAM;IAAS;IAAW;IAAM;IAAK;IAAO;IAAK;IAAS;IAAO;IAAS;IAAO;IAAO;IAAM;IAAM;IAAQ;IAAQ;IAAQ;IAAO;IAAQ;IAAO;IAAM;IAAS;IAAQ;IAAQ;IAAS;IAAS;IAAO;IAAQ;IAAO;IAAO;IAAQ;IAAK;IAAK;IAAK;IAAS;IAAQ;IAAM;IAAM;IAAO;IAAO;IAAO;IAAO;IAAK;IAAO;IAAM;IAAQ;IAAM;IAAQ;IAAQ;IAAO;IAAM;IAAO;IAAS;IAAM;IAAO;IAAM;IAAO;IAAM;IAAO;IAAO;IAAO;IAAM;IAAM;IAAO;IAAM;IAAO;IAAM;IAAO;IAAK;IAAO;IAAQ;IAAQ;IAAK;IAAQ;IAAM;IAAO;IAAO;IAAQ;IAAO;IAAO;IAAS;IAAO;IAAQ;IAAQ;IAAa;IAAU;IAAQ;IAAS;IAAQ;IAAQ;IAAO;IAAO;IAAS;IAAM;IAAS;IAAO;IAAS;IAAM;IAAO;IAAK;IAAM;IAAS;IAAU;IAAM;IAAO;IAAK;IAAS;IAAS;IAAM;IAAM;IAAO;IAAM;IAAO;IAAK;IAAU;IAAQ;IAAS;IAAQ;IAAS;IAAQ;IAAM;IAAO;IAAQ;IAAK;IAAO;IAAQ;IAAQ;IAAQ;IAAS;IAAK;IAAM;IAAO;IAAO;IAAO;IAAO;IAAQ;IAAQ;IAAO;IAAS;IAAO;IAAO;IAAQ;CAAQ,GAC7tC,IAAE;AACN,SAAS,QAAQ,CAAC;IAAE,OAAO,IAAI,OAAO,OAAK,EAAE,IAAI,CAAC,OAAK;AAAM;AAC7D,SAAS,UAAU,MAAM,EAAC,KAAK;IAC7B,IAAI,MAAI,OAAO,GAAG,IAAG,IAAE,OAAO,IAAI;IAClC,UAAQ;IACR,IAAG,KACD;QAAA,IAAG,KAAG,KACJ,OAAM,CAAC,MAAM,QAAQ,GAAC,gBAAgB,EAAE,QAAO;aAC9C,IAAG,KAAG,MAAK;YACd,IAAG,OAAO,GAAG,MAAI,KAAK,IAAI,CAAC,OAAO,IAAI,KACpC,OAAO,OAAO,SAAS,IAAG,UAAU,IAAI,CAAC,OAAO,OAAO,MAAI,CAAC,MAAM,QAAQ,GAAC,iBAAiB,EAAE,UAAQ,MAAM,QAAQ,GAAC,WAAU;iBAE/H,OAAO,MAAM,QAAQ,GAAC,WAAU;QACpC;IAAA;IACA,IAAG,KAAK,IAAI,CAAC,IACX,OAAO,OAAO,IAAI,MAAI,MAAI,CAAC,OAAO,SAAS,IAAG,SAAS,IAAE;IAC3D,IAAG,KAAG,KACJ,OAAM,CAAC,MAAM,QAAQ,GAAC,WAAW,EAAE,QAAO;IAC5C,IAAG,KAAG,KACJ,OAAO,OAAO,QAAQ,CAAC,oBAAmB;IAC5C,IAAG,AAAC,OAAK,KAAG,KAAK,IAAI,CAAC,OAAO,IAAI,OAAM,KAAK,IAAI,CAAC,IAAG;QAClD,IAAI,IAAE;QACN,OAAO,MAAM,CAAC;QACd,IAAG,OAAO,KAAK,CAAC,4EACV,OAAO,KAAK,CAAC,iDACb,OAAO,KAAK,CAAC,wCACb,OAAO,KAAK,CAAC,kBACjB,IAAE;aACC,IAAG,OAAO,KAAK,CAAC,iBACV,OAAO,KAAK,CAAC,qBACb,OAAO,KAAK,CAAC,mBACb,OAAO,KAAK,CAAC,qBACb,OAAO,KAAK,CAAC,qCACtB,IAAE;QACJ,OAAM,AAAC,KAAG,CAAC,CAAC,CAAC,IAAE,OAAO,IAAI,EAAE,KAAG,EAAE,IAAI,CAAC,EAAE,IAAG,IAAE,CAAC,OAAO,IAAI,IAAG,OAAO;IACrE;IACA,IAAG,cAAc,IAAI,CAAC,IACpB,OAAO,OAAO,QAAQ,CAAC,iBAAgB,SAAS,IAAI,CAAC,OAAO,OAAO,MAAI,YAAU;IACnF,IAAG,iCAAiC,IAAI,CAAC,IACvC,OAAO;IACT,IAAG,eAAe,IAAI,CAAC,IACrB,OAAO;IACT,OAAM;AACR;AACA,SAAS,iBAAiB,MAAM,EAAC,KAAK;IACpC,OAAO,OAAO,SAAS,IAAG,SAAS,IAAI,CAAC,OAAO,OAAO,MAAI,CAAC,MAAM,QAAQ,GAAC,iBAAiB,EAAE,QAAO,SAAQ,MAAM,QAAQ,GAAC,WAAW;AACxI;AACA,SAAS,kBAAkB,MAAM,EAAC,KAAK;IACrC,IAAI,IAAE,OAAO,GAAG,MAAI,OAAO,IAAI,MAAI;IACnC,OAAO,SAAS;IAChB,IAAG,KAAG,UAAU,IAAI,CAAC,OAAO,OAAO,KACjC,MAAM,QAAQ,GAAC;IACjB,OAAM;AACR;AACA,SAAS,kBAAkB,MAAM;IAAE,OAAO,OAAO,SAAS,IAAG;AAAU;AACvE,SAAS,YAAY,MAAM,EAAC,KAAK;IAC/B,IAAI,UAAQ,OAAM,MAAK,MAAI;IAC3B,MAAO,OAAK,OAAO,IAAI,GAAI;QACzB,IAAG,QAAM,QAAM,CAAC,SAAQ;YAAC,MAAI;YAAK;QAAM;QACxC,UAAQ,CAAC,WAAS,QAAM;IAC1B;IACA,IAAG,KAAI,MAAM,QAAQ,GAAC;IACtB,OAAM;AACR;AACA,SAAS,YAAY,KAAK,EAAC,IAAI,EAAC,GAAG;IAAE,MAAM,OAAO,GAAC;QAAC,MAAK,MAAM,OAAO;QAAC,QAAO,MAAM,MAAM;QAAC,KAAI;QAAI,MAAK;IAAI;AAAE;AAC9G,SAAS,WAAW,KAAK;IAAE,MAAM,MAAM,GAAC,MAAM,OAAO,CAAC,MAAM;IAAC,MAAM,OAAO,GAAC,MAAM,OAAO,CAAC,IAAI;AAAC;AACvF,MAAM,IAAI;IACf,MAAM;IACN,YAAW;QACT,OAAM;YAAC,UAAS;YACT,SAAQ;YACR,QAAO;YACP,KAAI;QAAC;IACd;IACA,OAAM,SAAS,MAAM,EAAC,KAAK;QACzB,IAAG,OAAO,GAAG,IAAG;YACd,IAAG,MAAM,OAAO,IAAE,MAAM,OAAO,CAAC,KAAK,IAAE,MACrC,MAAM,OAAO,CAAC,KAAK,GAAC;YACtB,MAAM,MAAM,GAAC,OAAO,WAAW;QACjC;QACA,qCAAqC;QACrC,IAAI,QAAM,MAAM,QAAQ,CAAC,QAAO;QAChC,IAAG,SAAO,aAAW,MAAM,OAAO,IAAE,MAAM,OAAO,CAAC,KAAK,IAAE,QAAM,MAAM,OAAO,CAAC,IAAI,IAAE,WAAU;YAC3F,MAAM,OAAO,CAAC,KAAK,GAAC;QACtB;QACA,IAAG,WAAS,KAAI,YAAY,OAAM,KAAI,OAAO,MAAM;aAC9C,IAAG,WAAS,KAAI,YAAY,OAAM,KAAI,OAAO,MAAM;aACnD,IAAG,WAAS,KAAI,YAAY,OAAM,KAAI,OAAO,MAAM;aACnD,IAAG,WAAW,IAAI,CAAC,UAAS;YAC/B,MAAM,MAAM,OAAO,IAAE,MAAM,OAAO,CAAC,IAAI,IAAE,UAAU,WAAW;YAC9D,IAAG,MAAM,OAAO,IAAE,WAAS,MAAM,OAAO,CAAC,IAAI,EAAC,WAAW;QAC3D,OACK,IAAG,WAAS,OAAK,MAAM,OAAO,IAAE,MAAM,OAAO,CAAC,IAAI,IAAE,WAAU,WAAW;aACzE,IAAG,uBAAuB,IAAI,CAAC,UAAQ,MAAM,OAAO,EAAC;YACxD,IAAG,SAAS,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,GACjC,YAAY,OAAM,WAAU,OAAO,MAAM;iBACtC,IAAG,MAAM,OAAO,CAAC,IAAI,IAAE,aAAW,CAAC,MAAM,OAAO,CAAC,KAAK,EAAC;gBAC1D,MAAM,OAAO,CAAC,KAAK,GAAC;gBACpB,MAAM,OAAO,CAAC,GAAG,GAAC,OAAO,MAAM;YACjC;QACF;QACA,OAAO;IACT;IACA,QAAO,SAAS,KAAK,EAAC,SAAS,EAAC,EAAE;QAChC,IAAI,YAAU,aAAW,UAAU,MAAM,CAAC;QAC1C,IAAI,UAAQ,MAAM,OAAO;QACzB,IAAG,SAAS,IAAI,CAAC,YACf,MAAO,WAAS,QAAQ,IAAI,IAAE,UAAU,UAAQ,QAAQ,IAAI;QAC9D,IAAI,UAAQ,WAAS,aAAW,QAAQ,IAAI;QAC5C,IAAG,CAAC,SACF,OAAO;aACJ,IAAG,QAAQ,IAAI,IAAE,WACpB,OAAO,QAAQ,GAAG;aACf,IAAG,QAAQ,KAAK,EACnB,OAAO,QAAQ,GAAG,GAAC,CAAC,UAAQ,IAAE,CAAC;aAE/B,OAAO,QAAQ,MAAM,GAAC,CAAC,UAAQ,IAAE,GAAG,IAAI;IAC5C;AACF", "ignoreList": [0], "debugId": null}}]}