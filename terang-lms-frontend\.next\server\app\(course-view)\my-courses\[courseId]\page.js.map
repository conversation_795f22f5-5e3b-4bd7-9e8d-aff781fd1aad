{"version": 3, "file": "../app/(course-view)/my-courses/[courseId]/page.js", "mappings": "4cAoBM,MAAU,cAAiB,WAjBG,CAiBQ,CAAU,QAhBzC,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,MAAM,GAAK,UAAU,EACzD,CAAC,MAAQ,EAAE,EAAG,CAAa,eAAK,SAAU,EAC1C,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EAC3C,yBCPA,8JCKA,SAASA,EAAgB,eACvBC,EAAgB,CAAC,CACjB,GAAGC,EACoD,EACvD,MAAO,UAACC,EAAAA,EAAyB,EAACC,YAAU,mBAAmBH,cAAeA,EAAgB,GAAGC,CAAK,CAAEG,sBAAoB,4BAA4BC,wBAAsB,kBAAkBC,0BAAwB,eAC1N,CACA,SAASC,EAAQ,CACf,GAAGN,EACgD,EACnD,MAAO,UAACF,EAAAA,CAAgBK,sBAAoB,kBAAkBC,wBAAsB,UAAUC,0BAAwB,uBAClH,UAACJ,EAAAA,EAAqB,EAACC,YAAU,UAAW,GAAGF,CAAK,CAAEG,sBAAoB,wBAAwBE,0BAAwB,iBAEhI,CACA,SAASE,EAAe,CACtB,GAAGP,EACmD,EACtD,MAAO,UAACC,EAAAA,EAAwB,EAACC,YAAU,kBAAmB,GAAGF,CAAK,CAAEG,sBAAoB,2BAA2BC,wBAAsB,iBAAiBC,0BAAwB,eACxL,CACA,SAASG,EAAe,WACtBC,CAAS,CACTC,aAAa,CAAC,UACdC,CAAQ,CACR,GAAGX,EACmD,EACtD,MAAO,UAACC,EAAAA,EAAuB,EAACE,sBAAoB,0BAA0BC,wBAAsB,iBAAiBC,0BAAwB,uBACzI,WAACJ,EAAAA,EAAwB,EAACC,YAAU,kBAAkBQ,WAAYA,EAAYD,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yaAA0aH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,2BAA2BE,0BAAwB,wBACjmBM,EACD,UAACV,EAAAA,EAAsB,EAACQ,UAAU,+FAA+FN,sBAAoB,yBAAyBE,0BAAwB,oBAG9M,yBCnCA,sGCAA,sCAAsL,qTCiBtL,OACA,UACA,GACA,CACA,UACA,gBACA,CACA,UACA,aACA,CACA,UACA,aACA,CACA,uBAAiC,EACjC,MAvBA,IAAoB,sCAAsL,CAuB1M,oJAEA,CAAS,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CACA,QA3CA,IAAsB,sCAAgK,CA2CtL,+HACA,WA3CA,IAAsB,4CAAgF,CA2CtG,+CACA,cA3CA,IAAsB,4CAAmF,CA2CzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QA9DA,IAAsB,sCAAiJ,CA8DvK,gHACA,gBA9DA,IAAsB,uCAAuJ,CA8D7K,sHACA,aA9DA,IAAsB,uCAAoJ,CA8D1K,mHACA,WA9DA,IAAsB,4CAAgF,CA8DtG,+CACA,cA9DA,IAAsB,4CAAmF,CA8DzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,wJAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,iDACA,kCAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,wBCzGD,8DCmBI,sBAAsB,kuBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAAC,GAAG,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,sCAAsC,CACtD,aAAa,CAAE,MAAM,CACrB,iBAAiB,GACjB,aAAa,WACb,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CACJ,CAAG,CAAC,CA/BoBQ,EAoCnB,IAAC,OAOF,EAEE,OAOF,EAEE,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,qGCAA,mECAA,0GCAA,4ECgBM,MAAY,cAAiB,aAbC,CAAC,CAAC,MAAQ,EAAE,EAAG,gBAAkB,KAAK,CAAS,QAAC,CAAC,kDCa/E,MAAO,cAAiB,QAbM,CAAC,CAaW,SAbC,EAAE,OAAQ,oBAAsB,KAAK,CAAS,QAAC,CAAC,2BCHjG,gDCAA,iDCAA,iDCAA,wGCAA,oNCM+C,MAAQ,OAAC,4BAA4B,sGAAsG,WAAW,4CAA4C,WAAW,yGAAyG,WAAW,2EAA2E,IAAyB,ECA7Z,OAAC,SAAF,SAAE,UAA4B,iDAAiD,WAAW,sKAAsK,WAAW,+WAA+W,WAAW,2CAA2C,IAAyB,eCAhtB,IAAM,EAAE,OAAC,GAAF,UAAE,UAAuB,mDAAmD,WAAW,kEAAkE,WAAW,+FAA+F,WAAW,gUAAgU,IAAyB,eCA9mB,MAAQ,OAAC,4BAA4B,4KAA4K,WAAW,oIAAoI,IAAyB,0HCFja,IAAMC,EAAoC,CAAC,OAChDC,CAAK,MACLC,CAAI,YACJC,CAAU,aACVC,GAAc,CAAK,UACnBP,CAAQ,OACRQ,CAAK,CACLC,cAAa,CAAK,CAClBC,UAAQ,CACRC,SAAO,aACPC,GAAc,CAAK,UACnBC,GAAW,CAAK,CACjB,IAEC,IAAMC,EAAmBV,EAAMW,MAAM,CAAG,GACxC,MAAO,WAACC,MAAAA,CAAIlB,UAAU,cAAcL,wBAAsB,WAAWC,0BAAwB,0BACzF,UAACP,EAAAA,EAAeA,CAAAA,CAACK,sBAAoB,kBAAkBE,0BAAwB,yBAC7E,WAACC,EAAAA,EAAOA,CAAAA,CAACP,cAAe,IAAKI,sBAAoB,UAAUE,0BAAwB,0BACjF,UAACE,EAAAA,EAAcA,CAAAA,CAACqB,OAAO,IAACzB,sBAAoB,iBAAiBE,0BAAwB,yBACnF,WAACwB,SAAAA,CAAOP,QAAS,KACbC,GAAeF,EACjBA,IACSC,GACTA,CAH2B,EAK/B,EAAGb,CAHmB,SAGR,CAAC,8FAA8F,EAAEQ,EAAaO,EAAW,uEAAyE,iCAAmC,oCAAoC,CAAEM,MAAO,CAC9RC,YAAa,GAAW,GAARZ,EAAa,GAAG,EAAE,CAAC,EAClCa,SAAU,CAACf,YACTM,GAAe,UAACU,OAAAA,CAAKxB,UAAU,oDAC3BW,EAAa,UAACc,EAAAA,CAAWA,CAAAA,CAACzB,UAAU,YAAe,UAAC0B,EAAAA,CAAYA,CAAAA,CAAC1B,UAAU,cAEhF,UAACwB,OAAAA,CAAKxB,UAAU,oDAA4CO,IAC5D,UAACiB,OAAAA,CAAKxB,UAAW,CAAC,mBAAmB,EAAEM,EAAMW,MAAM,CAAG,GAAK,wBAA0B,YAAY,UAC9FX,EAAMW,MAAM,CAAG,GAAK,UAACO,OAAAA,CAAKxB,UAAU,iBAASM,IAAgBA,IAE/D,CAACE,GAAc,UAACmB,EAAAA,CAAIA,CAAAA,CAAC3B,UAAU,YAC/BS,GAAe,UAACmB,EAAAA,CAAYA,CAAAA,CAAC5B,UAAU,gCAG3CgB,GAAoB,UAACjB,EAAAA,EAAcA,CAAAA,CAAC8B,KAAK,QAAQ7B,UAAU,oBACxD,UAAC8B,IAAAA,UAAGxB,WAIXK,GAAcT,GAAY,UAACgB,MAAAA,UAAKhB,MAEvC,EAAE,yBC3BI,MAAO,OAAiB,QApBM,CAClC,CAAC,MAAQ,EAAE,EAAG,CAAa,eAAK,SAAU,EAC1C,CAAC,MAAQ,EAAE,EAAG,CAAa,eAAK,SAAU,EAC1C,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CAAC,MAAQ,EAAE,EAAG,CAAW,aAAK,SAAU,EAC1C,kDCeM,MAAa,OAAiB,EAAjB,YAtBiB,CAsBc,CArB/C,MAAQ,EAAE,EAAG,CAAqB,QAqBuB,WArBvB,IAAK,SAAU,EAClD,CACE,OACA,CACE,CAAG,0FACH,GAAK,SACP,EACF,CACF,iBCWM,MAAS,OAAiB,UApBI,CAClC,CAAC,MAAQ,EAAE,EAAG,CAAgC,GAmBI,2BAnBJ,IAAK,SAAU,EAC7D,CAAC,MAAQ,EAAE,EAAG,CAAkC,oCAAK,SAAU,EAC/D,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CAAC,MAAQ,EAAE,EAAG,CAA8D,gEAAK,SAAU,EAC3F,CAAC,MAAQ,EAAE,EAAG,CAA+D,iEAAK,SAAU,EAC5F,CAAC,MAAQ,EAAE,EAAG,CAA+B,iCAAK,SAAU,EAC9D,ECLa6B,EAAkD,CAAC,QAC9DC,CAAM,YACNC,CAAU,iBACVC,CAAe,kBACfC,CAAgB,gBAChBC,CAAc,CACdC,iBAAe,oBACfC,CAAkB,CACnB,GACQ,WAACC,EAAAA,EAAIA,CAAAA,CAACvC,UAAU,uCAAuCN,sBAAoB,OAAOC,wBAAsB,kBAAkBC,0BAAwB,kCACrJ,UAAC4C,EAAAA,EAAUA,CAAAA,CAACxC,UAAU,OAAON,sBAAoB,aAAaE,0BAAwB,iCACpF,WAAC6C,EAAAA,EAASA,CAAAA,CAACzC,UAAU,4BAA4BN,sBAAoB,YAAYE,0BAAwB,kCACvG,UAAC8C,EAAIA,CAAC1C,CAAD0C,SAAW,eAAehD,sBAAoB,OAAOE,0BAAwB,0BAA0B,yBAIhH,WAAC+C,EAAAA,EAAWA,CAAAA,CAAC3C,UAAU,wEAAwEN,sBAAoB,cAAcE,0BAAwB,kCACtJoC,EAAOY,OAAO,CAACC,GAAG,CAAC,CAACC,EAAQC,KAC7B,IAAMC,EAAkBF,EAAOG,QAAQ,CAACC,KAAK,CAACC,GAAMA,EAAGC,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,GAAK0C,EAAGG,IAAI,CAACC,QAAQ,GAAKT,EAAOU,UAAU,CAACD,QAAQ,CAE5I,MAAO,WAAClD,EAAQA,CAAiBoD,GAAIX,EAArBzC,EAA8B,CAAEC,MAAOwC,EAAOxC,KAAK,CAAEC,KAAM,UAACmD,EAAAA,CAAQA,CAAAA,CAAC1D,UAAU,YAAcQ,WAAYsC,EAAOtC,UAAU,CAAEC,YAAauC,EAAiBtC,MAAO,EAAGI,aAAa,EAAMH,WAAYuB,CAAe,CAACY,EAAOW,EAAE,CAAC,CAAE7C,SAAU,IAAMwB,EAAeU,EAAOW,EAAE,EAAG1C,SADlQuB,CAC4QqB,GADrPZ,YAExCD,EAAOG,QAAQ,CAACJ,GAAG,CAACe,IACvB,IAAMC,EAAmBD,EAAQR,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,GAAKmD,EAAQN,IAAI,CAACC,QAAQ,CAC5F,MAAO,WAAClD,EAAQA,CAAkBoD,GAAIG,EAAtBvD,EAAgC,CAAEC,MAAOsD,EAAQtD,KAAK,CAAEC,KAAM,UAACuD,EAAAA,CAAIA,CAAAA,CAAC9D,UAAU,YAAcQ,WAAYoD,EAAQpD,UAAU,CAAEC,YAAaoD,EAAkBnD,MAAO,EAAGI,aAAa,EAAMH,WAAYwB,CAAgB,CAACyB,EAAQH,EAAE,CAAC,CAAE7C,SAAU,IAAMyB,EAAgBuB,EAAQH,EAAE,YACnRG,EAAQR,QAAQ,CAACP,GAAG,CAACkB,GAAW,UAAC1D,EAAQA,CAAkBoD,GAAIM,EAAtB1D,EAAgC,CAAEC,MAAOyD,EAAQzD,KAAK,CAAEC,KAAuB,UAAjBwD,EAAQC,IAAI,CAAe,UAACC,EAAAA,CAAIA,CAAAA,CAACjE,UAAU,yBAA6C,QAAjB+D,EAAQC,IAAI,CAAa,UAACE,EAAAA,CAAQA,CAAAA,CAAClE,UAAU,yBAA6C,qBAATgE,IAAI,CAAwB,UAACC,EAAAA,CAAIA,CAAAA,CAACjE,UAAU,0BAA6B,UAACmE,EAAUA,CAACnE,OAADmE,GAAW,0BAA4B3D,WAAYoD,EAAQpD,UAAU,CAAEC,YAAasD,EAAQtD,WAAW,CAAEC,MAAO,EAAGG,QAAS,IAAMoB,EAAWa,EAAOW,EAAE,CAAEG,EAAQH,EAAE,CAAEM,EAAQN,EAAE,GAAldM,EAAQN,EAAE,GAC1D,UAACpD,EAAQA,CAA4BoD,GAAI,EAAhCpD,CAAmCuD,EAAQH,EAAE,CAAC,KAAK,CAAC,CAAEnD,MAAM,eAAeC,KAAM,UAAC6D,EAAAA,CAAMA,CAAAA,CAACpE,UAAU,YAAcQ,WAAYoD,EAAQR,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,EAAGA,YAAamD,EAAQN,IAAI,CAACC,QAAQ,CAAE7C,MAAO,EAAGG,QAAS,IAAMoB,EAAWa,EAAOW,EAAE,CAAEG,EAAQH,EAAE,GAAhQ,GAAGG,EAAQH,EAAE,CAAC,KAAK,CAAC,IAFrBG,EAAQH,EAAE,CAIlC,GACI,UAACpD,EAAQA,CAA2BoD,GAAI,EAA/BpD,CAAkCyC,EAAOW,EAAE,CAAC,KAAK,CAAC,CAAEnD,MAAM,cAAcC,KAAM,UAAC4D,EAAUA,CAACnE,OAADmE,GAAW,YAAc3D,WAAYsC,EAAOG,QAAQ,CAACC,KAAK,CAACC,GAAMA,EAAGC,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,IAAM0C,CAAAA,CAAGG,IAAI,CAACC,QAAQ,EAAIJ,EAAGG,IAAI,CAACe,QAAQ,EAAIlB,EAAGG,IAAI,CAACgB,WAAW,GAAI7D,YAAaqC,EAAOU,UAAU,CAACD,QAAQ,CAAE7C,MAAO,EAAGG,QAAS,IAAMoB,EAAWa,EAAOW,EAAE,GAAnV,GAAGX,EAAOW,EAAE,CAAC,KAAK,CAAC,IARlBX,EAAOW,EAAE,CAUjC,GAEE,UAACpD,EAAQA,CAAkBoD,GAAG,EAArBpD,WAAkCC,MAAM,aAAaC,KAAM,UAACgE,EAAMA,CAACvE,GAADuE,OAAW,YAAc/D,WAAYwB,EAAOY,OAAO,CAACM,KAAK,CAACsB,GAAKA,EAAEvB,QAAQ,CAACC,KAAK,CAACC,GAAMA,EAAGC,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,IAAM0C,CAAAA,CAAGG,IAAI,CAACC,QAAQ,EAAIJ,EAAGG,IAAI,CAACe,QAAQ,EAAIlB,EAAGG,IAAI,CAACgB,WAAW,KAAOE,CAAAA,CAAEhB,UAAU,CAACD,QAAQ,EAAIiB,EAAEhB,UAAU,CAACa,QAAQ,EAAIG,EAAEhB,UAAU,CAACc,WAAAA,GAAe7D,YAAauB,EAAOyC,SAAS,CAAClB,QAAQ,CAAE7C,MAAO,EAAGhB,sBAAoB,WAAWE,0BAAwB,yBAAtb,wDCjBhB,MAAQ,OAAiB,SAjBK,CAClC,CAAC,OAAQ,CAAE,GAAI,CAAM,CAgB2B,GAhB3B,GAAI,CAAM,OAAI,GAAK,IAAI,GAAK,KAAK,SAAU,EAChE,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAM,OAAI,IAAM,IAAI,IAAM,KAAK,SAAU,EAClE,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,KAAK,GAAK,UAAU,EAC1D,uCCaM,MAAe,OAAiB,IAAjB,YAjBe,CAClC,CAAC,MAAQ,EAAE,EAAG,CAAa,UAgBmC,CAhBnC,IAAK,SAAU,EAC1C,CAAC,MAAQ,EAAE,EAAG,CAAe,iBAAK,SAAU,EAC5C,CAAC,MAAQ,EAAE,EAAG,CAA4D,8DAAK,SAAU,EAC3F,ECCa8E,EAA0C,CAAC,SACtDX,CAAO,kBACPY,CAAgB,CAChBhE,YAAU,gBACViE,CAAc,CACf,IAECC,QAAQC,GAAG,CAAC,gCAAiCf,GAW7C,IAAMgB,EAAehB,EAAQzD,KAAK,EAAK,kBAAOyD,EAAQA,OAAO,EAAqC,OAApBA,EAAQA,OAAO,EAAa,UAAWA,EAAQA,OAAO,EAAqC,UAAjC,OAAOA,EAAQA,OAAO,CAACiB,KAAK,CAAgBC,CAVjJ,IAEjC,IAAK,IAAMC,KADGC,EAASC,CACJC,IADS,CAAC,MACH,CACxB,IAAMC,EAAQJ,EAAKI,KAAK,CAAC,mBACzB,GAAIA,GAASA,CAAK,CAAC,EAAE,CACnB,CADqB,MACdA,CAAK,CAAC,EAAE,CAACC,IAAI,EAExB,CACA,OAAO,KACT,EAC+MxB,EAAQA,OAAO,CAACiB,KAAK,EAAI,KAAG,EAAM,WA6KjP,MAAO,UAACzC,EAAAA,EAAIA,CAAAA,CAACkB,GAAI,CAAC,QAAQ,EAAEM,EAAQN,EAAE,EAAE,CAAEzD,UAAU,sDAAsDN,sBAAoB,OAAOC,wBAAsB,cAAcC,0BAAwB,4BAC7L,UAAC+C,EAAAA,EAAWA,CAAAA,CAAC3C,UAAU,OAAON,sBAAoB,cAAcE,0BAAwB,4BACtF,WAACsB,MAAAA,CAAIlB,UAAU,0BACb,WAACkB,MAAAA,CAAIlB,UAAU,mDAAmDa,QAAS+D,YACzE,WAAC1D,MAAAA,CAAIlB,UAAU,+CACZwF,CAjCU,KACrB,OAAQzB,EAAQC,IAAI,EAClB,IAAK,QACH,MAAO,UAACC,EAAAA,CAAIA,CAAAA,CAACjE,UAAU,wBACzB,KAAK,MACH,MAAO,UAACkE,EAAAA,CAAQA,CAAAA,CAAClE,UAAU,wBAC7B,KAAK,iBACH,MAAO,UAACiE,EAAAA,CAAIA,CAAAA,CAACjE,UAAU,yBACzB,KAAK,QACH,MAAO,UAACyF,EAAAA,CAAKA,CAAAA,CAACzF,UAAU,0BAC1B,SACE,MAAO,UAACmE,EAAUA,CAACnE,OAADmE,GAAW,yBACjC,CACF,KAqBY,WAACjD,MAAAA,CAAIlB,UAAU,mBACb,UAACwB,OAAAA,CAAKxB,UAAU,+BAAuB+E,IACvC,WAAC7D,MAAAA,CAAIlB,UAAU,6CACb,UAAC0F,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,UAAU3F,UAAU,UAAUN,sBAAoB,QAAQE,0BAAwB,4BAC9FgG,CAxBS,KAC1B,OAAQ7B,EAAQC,IAAI,EAClB,IAAK,QACH,MAAO,OACT,KAAK,MACH,MAAO,cACT,KAAK,iBACH,MAAO,gBACT,KAAK,QACH,MAAO,OACT,SACE,MAAO,kBACX,CACF,OAaiBD,EAAQ8B,QAAQ,EAAI,WAACH,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,UAAU3F,UAAU,oBACpD,UAAC8F,EAAKA,CAAC9F,EAAD8F,QAAW,iBAChB/B,EAAQ8B,QAAQ,CAAC,mBAK5B,UAAC3E,MAAAA,CAAIlB,UAAU,uCACZW,EAAa,UAACoF,EAAAA,CAASA,CAAAA,CAAC/F,UAAU,0BAA6B,UAACyB,EAAAA,CAAWA,CAAAA,CAACzB,UAAU,+BAI1FW,GAAc,WAACO,MAAAA,CAAIlB,UAAU,oCACR,SAAjB+D,EAAQC,IAAI,CAAc,WAAC9C,MAAAA,CAAIlB,UAAU,sBACtC,UAACkB,MAAAA,CAAIlB,UAAU,mDACb,UAACgG,EAAAA,EAAaA,CAAAA,CAACC,cAAe,CAACC,EAAAA,CAASA,CAAC,CAAEC,WAAY,CAC3DC,GAAI,CAAC,MACHC,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAAC6G,KAAAA,CAAGpG,UAAU,wCAAyC,GAAGT,CAAK,GACrE+G,GAAI,CAAC,MACHD,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAAC+G,KAAAA,CAAGtG,UAAU,2CAA4C,GAAGT,CAAK,GACxEgH,GAAI,CAAC,MACHF,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAACgH,KAAAA,CAAGvG,UAAU,2CAA4C,GAAGT,CAAK,GACxEiH,GAAI,CAAC,MACHH,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAACiH,KAAAA,CAAGxG,UAAU,6CAA8C,GAAGT,CAAK,GAC1EuC,EAAG,CAAC,MACFuE,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAACuC,IAAAA,CAAE9B,UAAU,uBAAwB,GAAGT,CAAK,GACnDkH,GAAI,CAAC,CACHJ,MAAI,CACJ,GAAG9G,EACJ,GAAK,UAACkH,KAAAA,CAAGzG,UAAU,sBAAuB,GAAGT,CAAK,GACnDmH,GAAI,CAAC,MACHL,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAACmH,KAAAA,CAAG1G,UAAU,yBAA0B,GAAGT,CAAK,GACtDoH,GAAI,CAAC,MACHN,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAACoH,KAAAA,CAAG3G,UAAU,OAAQ,GAAGT,CAAK,GACpCqH,WAAY,CAAC,MACXP,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAACqH,aAAAA,CAAW5G,UAAU,wEAAyE,GAAGT,CAAK,GAC7GsH,KAAM,CAAC,MACLR,CAAI,WACJrG,CAAS,CACTE,UAAQ,CACR,GAAGX,EACJ,GACe,iBAAiBuH,IAAI,CAAC9G,GAAa,IAI7B,UAAC6G,OAAAA,CAAK7G,UAAU,gFAAiF,GAAGT,CAAK,UAChHW,IAHK,UAAC2G,OAAAA,CAAK7G,UAAU,oDAAqD,GAAGT,CAAK,UAClFW,IAKf6G,IAAK,CAAC,MACJV,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAACwH,MAAAA,CAAI/G,UAAU,OAAQ,GAAGT,CAAK,GACrCyH,MAAO,CAAC,MACNX,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAAC2B,MAAAA,CAAIlB,UAAU,gCACT,UAACgH,QAAAA,CAAMhH,UAAU,4CAA6C,GAAGT,CAAK,KAElF0H,MAAO,CAAC,MACNZ,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAAC0H,QAAAA,CAAMjH,UAAU,aAAc,GAAGT,CAAK,GAC7C2H,GAAI,CAAC,MACHb,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAAC2H,KAAAA,CAAGlH,UAAU,2DAA4D,GAAGT,CAAK,GACxF4H,GAAI,CAAC,CACHd,MAAI,CACJ,GAAG9G,EACJ,GAAK,UAAC4H,KAAAA,CAAGnH,UAAU,mCAAoC,GAAGT,CAAK,GAChE6H,GAAI,CAAC,MACHf,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAAC6H,KAAAA,CAAGpH,UAAU,uBAAwB,GAAGT,CAAK,GACpD8H,OAAQ,CAAC,MACPhB,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAAC8H,SAAAA,CAAOrH,UAAU,8BAA+B,GAAGT,CAAK,GAC/D+H,GAAI,CAAC,MACHjB,CAAI,CACJ,GAAG9G,EACJ,GAAK,UAAC+H,KAAAA,CAAGtH,UAAU,SAAU,GAAGT,CAAK,EACxC,WACoC,UAA3B,OAAOwE,EAAQA,OAAO,CAAgBA,EAAQA,OAAO,CAA8B,UAA3B,OAAOA,EAAQA,OAAO,EAAqC,OAApBA,EAAQA,OAAO,EAAa,UAAWA,EAAQA,OAAO,EAAqC,UAAjC,OAAOA,EAAQA,OAAO,CAACiB,KAAK,CAAgBjB,EAAQA,OAAO,CAACiB,KAAK,CAAGuC,MAAMC,OAAO,CAACzD,EAAQA,OAAO,EAAIA,EAAQA,OAAO,CAAClB,GAAG,CAAC4E,GAAwB,SAAfA,EAAMzD,IAAI,CAAcyD,EAAMzC,KAAK,CAAG,IAAI0C,IAAI,CAAC,IAAM,OAGrV,WAACC,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKjC,QAAQ,UAAU3F,UAAU,iDAAiDa,QAhS3E,CAgSoFgH,IA9RtH,IAAMC,EAAcC,OAAOC,IAAI,CAAC,GAAI,UACpC,GAAI,CAACF,EAAa,OAGlB,IAAMG,EAAc,CAAC;;;;iBAIR,EAAElE,EAAQzD,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cAgFnB,EAAEyD,EAAQzD,KAAK,CAAC;;;;IAI1B,CAAC,CACDwH,EAAYI,QAAQ,CAACC,KAAK,CAACF,GAC3BH,EAAYI,QAAQ,CAACE,KAAK,GAG1B,IAAMC,EAAcP,EAAYI,QAAQ,CAACI,cAAc,CAAC,oBACxD,GAAID,EAAa,CAEf,IAAIE,EAAW,GAGgB,UAA3B,OAAOxE,EAAQA,OAAO,CACxBwE,EAAWxE,EAAQA,OAAO,CACU,UAA3B,OAAOA,EAAQA,OAAO,EAAqC,OAApBA,EAAQA,OAAO,EAAa,UAAWA,EAAQA,OAAO,EAAqC,UAAjC,OAAOA,EAAQA,OAAO,CAACiB,KAAK,CACtIuD,EAAWxE,EAAQA,OAAO,CAACiB,KAAK,CACvBuC,MAAMC,OAAO,CAACzD,EAAQA,OAAO,GAAG,CAEzCwE,EAAWxE,EAAQA,OAAO,CAAClB,GAAG,CAAC4E,GAAwB,SAAfA,EAAMzD,IAAI,CAAcyD,EAAMzC,KAAK,CAAG,IAAI0C,IAAI,CAAC,KAuBzFW,EAAYG,SAAS,CADrBD,EACwBA,CAJxBA,EAAW,OADXA,CACmBA,CADRA,CAHXA,EAAWA,CADXA,EAAWA,CADXA,EAAWA,GAHAA,CADXA,EAAWA,CAHXA,EAAWA,CADXA,EAAWA,GADAA,EAASE,OAAO,CAAC,gBAAiB,gBACzBA,OAAO,CAAC,eAAgB,gBACxBA,OAAO,CAAC,cAAe,gBAGvBA,OAAO,CAAC,kBAAmB,wBAC3BA,OAAO,CAAC,cAAe,gBAGvBA,OAAO,CAAC,eAAgB,gBACxBA,OAAO,CAAC,oBAAqB,gBAC7BA,OAAO,CAAC,kBAAmB,gBAG3BA,OAAO,CAAC,QAAS,YACP,QAGVA,OAAO,CAAC,YAAa,GAE3C,CAGAC,WAAW,KACTZ,EAAYa,KAAK,GACjBb,EAAYc,KAAK,GACjBd,EAAYM,KAAK,EACnB,EAAG,IACL,YAkJkB,UAACS,EAAAA,CAAQA,CAAAA,CAAC7I,UAAU,iBAAiB,wBAGf,QAAjB+D,EAAQC,IAAI,CAAa,WAAC9C,MAAAA,CAAIlB,UAAU,sBAC/C,UAAC8I,SAAAA,CAAOC,IAAK,GAA8B,UAA3B,OAAOhF,EAAQA,OAAO,CAAgBA,EAAQA,OAAO,CAA8B,UAA3B,OAAOA,EAAQA,OAAO,EAAqC,OAApBA,EAAQA,OAAO,EAAa,UAAWA,EAAQA,OAAO,EAAqC,UAAjC,OAAOA,EAAQA,OAAO,CAACiB,KAAK,CAAgBjB,EAAQA,OAAO,CAACiB,KAAK,CAAG,GAAG,qBAAqB,CAAC,CAAEhF,UAAU,6BAA6BM,MAAOyD,EAAQzD,KAAK,GACnU,WAACY,MAAAA,CAAIlB,UAAU,2BACb,WAAC2H,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKjC,QAAQ,UAAU3F,UAAU,iDAAiDa,QAAS,KAC5G,IAAMmI,EAAoC,UAA3B,OAAOjF,EAAQA,OAAO,CAAgBA,EAAQA,OAAO,CAA8B,iBAApBA,EAAQA,OAAO,EAAqC,OAApBA,EAAQA,OAAO,EAAa,UAAWA,EAAQA,OAAO,EAAqC,UAAjC,OAAOA,EAAQA,OAAO,CAACiB,KAAK,CAAgBjB,EAAQA,OAAO,CAACiB,KAAK,CAAG,GACxOgE,GAAQjB,OAAOC,IAAI,CAACgB,EAAQ,SAClC,YACQ,UAACC,EAAAA,CAAGA,CAAAA,CAACjJ,UAAU,iBAAiB,qBAGlC,WAAC2H,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKjC,QAAQ,UAAU3F,UAAU,iCAAiCa,QAAS,KAC5F,IAAMmI,EAAoC,UAA3B,OAAOjF,EAAQA,OAAO,CAAgBA,EAAQA,OAAO,CAA8B,UAA3B,OAAOA,EAAQA,OAAO,EAAqC,OAApBA,EAAQA,OAAO,EAAa,UAAWA,EAAQA,OAAO,EAAqC,iBAA1BA,EAAQA,OAAO,CAACiB,KAAK,CAAgBjB,EAAQA,OAAO,CAACiB,KAAK,CAAG,GAC5O,GAAIgE,EAAQ,CACV,IAAME,EAAOhB,SAASiB,aAAa,CAAC,KACpCD,EAAKE,IAAI,CAAGJ,EACZE,EAAKG,QAAQ,CAAGtF,EAAQzD,KAAK,EAAI,eACjC4H,SAASoB,IAAI,CAACC,WAAW,CAACL,GAC1BA,EAAKM,KAAK,GACVtB,SAASoB,IAAI,CAACG,WAAW,CAACP,EAC5B,CACF,YACQ,UAACL,EAAAA,CAAQA,CAAAA,CAAC7I,UAAU,iBAAiB,oBAIjB,UAAjB+D,EAAQC,IAAI,CAAe,WAAC9C,MAAAA,CAAIlB,UAAU,sBACjD,UAAC0J,MAAAA,CAAIX,IAAgC,UAA3B,OAAOhF,EAAQA,OAAO,CAAgBA,EAAQA,OAAO,CAA8B,UAA3B,OAAOA,EAAQA,OAAO,EAAqC,OAApBA,EAAQA,OAAO,EAAa,UAAWA,EAAQA,OAAO,EAAqC,UAAjC,OAAOA,EAAQA,OAAO,CAACiB,KAAK,CAAgBjB,EAAQA,OAAO,CAACiB,KAAK,CAAG,GAAI2E,IAAK5F,EAAQzD,KAAK,EAAI,QAASN,UAAU,qCACpR,WAACkB,MAAAA,CAAIlB,UAAU,2BACb,WAAC2H,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKjC,QAAQ,UAAU3F,UAAU,iDAAiDa,QAAS,KAC5G,IAAM+I,EAAsC,UAA3B,OAAO7F,EAAQA,OAAO,CAAgBA,EAAQA,OAAO,CAA8B,UAA3B,OAAOA,EAAQA,OAAO,EAAiBA,SAAQA,OAAO,EAAa,UAAWA,EAAQA,OAAO,EAAqC,UAAjC,OAAOA,EAAQA,OAAO,CAACiB,KAAK,CAAgBjB,EAAQA,OAAO,CAACiB,KAAK,CAAG,GAC1O4E,GAAU7B,OAAOC,IAAI,CAAC4B,EAAU,SACtC,YACQ,UAACC,EAAYA,CAAC7J,SAAD6J,CAAW,iBAAiB,qBAG3C,WAAClC,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKjC,QAAQ,UAAU3F,UAAU,iCAAiCa,QAAS,KAC5F,IAAM+I,EAAsC,UAA3B,OAAO7F,EAAQA,OAAO,CAAgBA,EAAQA,OAAO,CAA8B,UAA3B,OAAOA,EAAQA,OAAO,EAAqC,OAApBA,EAAQA,OAAO,EAAa,UAAWA,EAAQA,OAAO,EAAqC,UAAjC,OAAOA,EAAQA,OAAO,CAACiB,KAAK,CAAgBjB,EAAQA,OAAO,CAACiB,KAAK,CAAG,GAC9O,GAAI4E,EAAU,CACZ,IAAMV,EAAOhB,SAASiB,aAAa,CAAC,KACpCD,EAAKE,IAAI,CAAGQ,EACZV,EAAKG,QAAQ,CAAGtF,EAAQzD,KAAK,EAAI,YACjC4H,SAASoB,IAAI,CAACC,WAAW,CAACL,GAC1BA,EAAKM,KAAK,GACVtB,SAASoB,IAAI,CAACG,WAAW,CAACP,EAC5B,CACF,YACQ,UAACL,EAAAA,CAAQA,CAAAA,CAAC7I,UAAU,iBAAiB,oBAIjB,UAAjB+D,EAAQC,IAAI,EAAiC,mBAAjBD,EAAQC,IAAI,CAAwB,WAAC9C,MAAAA,CAAIlB,UAAU,sBACtF,UAACkB,MAAAA,CAAIlB,UAAU,sEACZ,CAAC,KACN,IAAM8J,EAAW,iBAAO/F,EAAQA,OAAO,CAAgBA,EAAQA,OAAO,CAA8B,UAA3B,OAAOA,EAAQA,OAAO,EAAqC,OAApBA,EAAQA,OAAO,EAAa,UAAWA,EAAQA,OAAO,EAAqC,UAAjC,OAAOA,EAAQA,OAAO,CAACiB,KAAK,CAAgBjB,EAAQA,OAAO,CAACiB,KAAK,CAAG,GAC9O,GAAI,CAAC8E,EACH,MAAO,EADM,CACN,OAAC5I,MAAAA,CAAIlB,UAAU,oFAA2E,2BAInG,GAAI8J,EAASC,QAAQ,CAAC,yBAA2BD,EAASC,QAAQ,CAAC,aAAc,CAC/E,IAAMC,EAAYF,EAAS1E,KAAK,CAAC,KAAK,CAAC,EAAE,EAAEA,MAAM,IAAI,CAAC,EAAE,EAAI0E,EAAS1E,KAAK,CAAC,KAAK6E,GAAG,GACnF,MAAO,UAACnB,SAAAA,CAAO9I,UAAU,gBAAgB+I,IAAK,CAAC,8BAA8B,EAAEiB,EAAAA,CAAW,CAAEE,YAAY,IAAIC,MAAM,2FAA2FC,eAAe,IAAC9J,MAAOyD,EAAQzD,KAAK,EACnP,CAAO,IAAIwJ,EAASC,QAAQ,CAAC,cAK3B,MAAO,UAACM,QAAAA,CAAMC,QAAQ,IAACtK,UAAU,gBAAgB+I,IAAKe,EAAUxJ,MAAOyD,EAAQzD,KAAK,UAAE,gDAL5C,EAC1C,IAAMiK,EAAUT,EAAS1E,KAAK,CAAC,KAAK6E,GAAG,GACvC,MAAO,UAACnB,SAAAA,CAAO9I,UAAU,gBAAgB+I,IAAK,CAAC,+BAA+B,EAAEwB,EAAAA,CAAS,CAAEL,YAAY,IAAIC,MAAM,2CAA2CC,eAAe,IAAC9J,MAAOyD,EAAQzD,KAAK,EAClM,EAMF,KANS,CAQL,UAACY,MAAAA,CAAIlB,UAAU,0BACb,WAAC2H,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKjC,QAAQ,UAAU3F,UAAU,iDAAiDa,QAAS,KAC5G,IAAMiJ,EAAsC,UAA3B,OAAO/F,EAAQA,OAAO,CAAgBA,EAAQA,OAAO,CAA8B,UAA3B,OAAOA,EAAQA,OAAO,EAAqC,OAApBA,EAAQA,OAAO,EAAa,UAAWA,EAAQA,OAAO,EAAqC,UAAjC,OAAOA,EAAQA,OAAO,CAACiB,KAAK,CAAgBjB,EAAQA,OAAO,CAACiB,KAAK,CAAG,GAC1O8E,GAAU/B,OAAOC,IAAI,CAAC8B,EAAU,SACtC,YACQ,UAACD,EAAYA,CAAC7J,SAAD6J,CAAW,iBAAiB,0BAItC,UAAC3I,MAAAA,CAAIlB,UAAU,qBACtB,UAACkB,MAAAA,CAAIlB,UAAU,gFACb,WAACkB,MAAAA,CAAIlB,UAAU,wBACb,UAACiE,EAAAA,CAAIA,CAAAA,CAACjE,UAAU,yCAChB,UAAC8B,IAAAA,CAAE9B,UAAU,iCAAwB,sDAQ7C,UAACkB,MAAAA,CAAIlB,UAAU,8BACb,WAAC2H,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKjC,QAAS5B,EAAQtD,WAAW,CAAG,UAAY,UAAWT,UAAW,CAAC,cAAc,EAAE+D,EAAQtD,WAAW,CAAG,6CAA+C,kCAAkC,CAAEI,QAAS2J,IACxNA,EAAEC,eAAe,GACjB9F,GACF,YACM,UAAC/C,EAAAA,CAAYA,CAAAA,CAAC5B,UAAU,iBACvB+D,EAAQtD,WAAW,CAAG,YAAc,8BAOvD,EAAE,0BCpaK,IAAMiK,EAAoC,CAAC,MAChDpH,CAAI,YACJ9C,CAAU,CACVmK,aAAW,CACZ,GAyBQ,UAACpI,EAAAA,EAAIA,CAAAA,CAACvC,UAAW,CAAC,mBAAmB,EAAE,CAACQ,EAAa,aAAe,IAAI,CAAEd,sBAAoB,OAAOC,wBAAsB,WAAWC,0BAAwB,yBACjK,UAAC+C,EAAAA,EAAWA,CAAAA,CAAC3C,UAAU,MAAMN,sBAAoB,cAAcE,0BAAwB,yBACrF,WAACsB,MAAAA,CAAIlB,UAAU,8CACb,WAACkB,MAAAA,CAAIlB,UAAU,wCACb,UAACkB,MAAAA,CAAIlB,UAAW,CAAC,eAAe,EAAE4K,CA5BnB,KACvB,OAAQtH,EAAKU,IAAI,EACf,IAAK,UACH,MAAO,2CACT,KAAK,SACH,MAAO,iDACT,KAAK,QACH,MAAO,wCACT,SACE,MAAO,2CACX,EACF,IAiB4C4G,CAAoB,UACnDC,CAjBO,KAClB,OAAQvH,EAAKU,IAAI,EACf,IAAK,UAML,QALE,MAAO,UAACI,EAAAA,CAAMA,CAAAA,CAACpE,UAAU,WAC3B,KAAK,SACH,MAAO,UAACmE,EAAUA,CAACnE,OAADmE,GAAW,WAC/B,KAAK,QACH,MAAO,UAACI,EAAMA,CAACvE,GAADuE,OAAW,WAG7B,EACF,MAQU,WAACrD,MAAAA,WACC,UAACsF,KAAAA,CAAGxG,UAAU,uBAAesD,EAAKhD,KAAK,GACvC,WAACY,MAAAA,CAAIlB,UAAU,6CACb,WAACwB,OAAAA,CAAKxB,UAAU,kCAAwB,eACzBsD,EAAKwH,YAAY,CAAC,OAEhCxH,EAAKyH,SAAS,EAAI,WAACvJ,OAAAA,CAAKxB,UAAU,kCAC/B,UAACgL,EAAAA,CAAKA,CAAAA,CAAChL,UAAU,wBAChBsD,EAAKyH,SAAS,CAAC,UAEpB,WAACvJ,OAAAA,CAAKxB,UAAU,kCAAwB,aAC3BsD,EAAKe,QAAQ,CAAC,IAAEf,EAAKgB,WAAW,aAKnD,WAACpD,MAAAA,CAAIlB,UAAU,wCACZsD,EAAKC,QAAQ,EAAI,WAACmC,EAAAA,CAAKA,CAAAA,CAAC1F,UAAU,wCAC/B,UAACiL,EAAAA,CAAWA,CAAAA,CAACjL,UAAU,iBAAiB,WAC/BsD,EAAK4H,SAAS,CAAC,QAE5B,UAACvD,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKrG,SAAU,CAACf,GAAc8C,EAAKe,QAAQ,EAAIf,EAAKgB,WAAW,CAAEzD,QAAS8J,EAAahF,QAASrC,EAAKC,QAAQ,CAAG,UAAY,UAAW7D,sBAAoB,SAASE,0BAAwB,yBACpL,IAAlB0D,EAAKe,QAAQ,CAAS,aAAef,EAAKC,QAAQ,CAAG,SAAW,uBCxDlE4H,GAAsC,CAAC,MAClD7H,CAAI,QACJ8H,CAAM,SACNC,CAAO,YACPC,CAAU,CACX,IACC,GAAM,CAACC,EAAiBC,EAAmB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACjD,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAEnC,CAAC,GACE,CAACG,EAAUC,EAAY,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACnI,EAAKyH,SAAS,CAAoB,GAAjBzH,EAAKyH,SAAS,CAAQ,MAC1E,CAACe,EAAcC,EAAgB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjDO,IAAAA,SAAe,CAAC,KACd,GAAIZ,GAAuB,OAAbQ,GAAqBA,EAAW,EAAG,CAC/C,IAAMK,EAAQC,YAAY,KACxBL,EAAYM,GACV,OAAIA,GAAiBA,GAAQ,GAAG,IAEvB,GAEFA,EAAO,EAElB,EAAG,KACH,MAAO,IAAMC,cAAcH,EAC7B,CACF,EAAG,CAACb,EAAQQ,EAAS,EAMrB,IAAMS,EAAqB,CAACC,EAAoBC,KAC9CZ,EAAWQ,GAAS,EAClB,EADkB,CACfA,CAAI,CACP,CAACG,EAAW,CAAEC,EAChB,EACF,EACMC,EAAmB,KACvB,GAAIV,EAAc,OAClBC,GAAgB,GAGhB,IAAIU,EAAiB,EACrBnJ,EAAKoJ,SAAS,CAACC,OAAO,CAACC,IACrB,IAAMC,EAAanB,CAAO,CAACkB,EAASnJ,EAAE,CAAC,CACvC,GAAsB,oBAAlBmJ,EAAS5I,IAAI,EAA4C,mBAAmB,CAArC4I,EAAS5I,IAAI,CAEtD,GAAI6I,IAAeD,EAASE,aAAa,CACvCL,CADyC,OAItC,CACH,IAAMM,EAAiBH,EAASI,OAAO,EAAE,CAACH,EAAqB,CAC3DE,GAA4C,UAA1B,OAAOA,GAA+BA,EAAeE,SAAS,EAAE,GAGxF,KAC2B,eAAlBL,EAAS5I,IAAI,EAAuC,cAAc,CAAhC4I,EAAS5I,IAAI,CAEpD6I,IAAeD,EAASE,aAAa,EAAE,IAGhB,SAAS,CAA3BF,EAAS5I,IAAI,EAGlB6I,GAAoC,IAAI,CAA1BA,EAAWtH,IAAI,IAC/BkH,GAGN,GACA,IAAMS,EAAQC,KAAKC,KAAK,CAACX,EAAiBnJ,EAAKoJ,SAAS,CAACzL,MAAM,CAAG,KAClEyH,WAAW,KACT4C,EAAW4B,GACXnB,GAAgB,GAChBJ,EAAW,CAAC,GACZH,EAAmB,GACflI,EAAKyH,SAAS,EAAEc,EAA6B,GAAjBvI,EAAKyH,SAAS,CAChD,EAAG,IACL,EACA,GAAI,CAACK,GAAoC,IAA1B9H,EAAKoJ,SAAS,CAACzL,MAAM,CAAQ,OAAO,KACnD,IAAMoM,EAAW/J,EAAKoJ,SAAS,CAACnB,EAAgB,CAC1C+B,EAAiB/B,IAAoBjI,EAAKoJ,SAAS,CAACzL,MAAM,CAAG,EAC7DsM,OAAsCC,IAAzB9B,CAAO,CAAC2B,EAAS5J,EAAE,CAAC,CACvC,MAAO,UAACgK,EAAAA,EAAMA,CAAAA,CAACzF,KAAMoD,EAAQsC,aAAcrC,EAAS3L,sBAAoB,SAASC,wBAAsB,YAAYC,0BAAwB,0BACvI,WAAC+N,EAAAA,EAAaA,CAAAA,CAAC3N,UAAU,6CAA6CN,sBAAoB,gBAAgBE,0BAAwB,2BAChI,UAACgO,EAAAA,EAAYA,CAAAA,CAAClO,sBAAoB,eAAeE,0BAAwB,0BACvE,WAACiO,EAAAA,EAAWA,CAAAA,CAAC7N,UAAU,oCAAoCN,sBAAoB,cAAcE,0BAAwB,2BACnH,UAAC4B,OAAAA,UAAM8B,EAAKhD,KAAK,GACH,OAAbsL,GAAqB,WAAClG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,UAAU3F,UAAU,wCACrD,UAACgL,EAAAA,CAAKA,CAAAA,CAAChL,UAAU,iBAChB8N,CAhEKC,IAClB,IAAMC,EAAOb,KAAKc,KAAK,CAACF,EAAU,IAElC,MAAO,GAAGC,EAAK,CAAC,EAAEE,CADLH,EAAU,IACAI,QAAQ,GAAGC,QAAQ,CAAC,EAAG,MAAM,GA6D5BxC,WAKpB,WAAC1K,MAAAA,CAAIlB,UAAU,sBAEb,WAACkB,MAAAA,CAAIlB,UAAU,sBACb,WAACkB,MAAAA,CAAIlB,UAAU,uDACb,WAACwB,OAAAA,WAAK,YACM+J,EAAkB,EAAE,OAAKjI,EAAKoJ,SAAS,CAACzL,MAAM,IAE1D,WAACO,OAAAA,WACE2L,KAAKC,KAAK,CAAC,CAAC7B,EAAkB,GAAKjI,EAAKoJ,SAAS,CAACzL,MAAM,CAAG,KAAK,mBAIrE,UAACoN,EAAAA,CAAQA,CAAAA,CAACrJ,MAAO,CAACuG,GAAkB,EAAKjI,EAAKoJ,SAAS,CAACzL,MAAM,CAAG,IAAKvB,sBAAoB,WAAWE,0BAAwB,sBAI/H,UAAC2C,EAAAA,EAAIA,CAAAA,CAAC7C,sBAAoB,OAAOE,0BAAwB,0BACvD,WAAC+C,EAAAA,EAAWA,CAAAA,CAAC3C,UAAU,MAAMN,sBAAoB,cAAcE,0BAAwB,2BACrF,UAAC2G,KAAAA,CAAGvG,UAAU,oCACkB,UAA7B,OAAOqN,EAAST,QAAQ,CAAgBS,EAAST,QAAQ,CAAGrF,MAAMC,OAAO,CAAC6F,EAAST,QAAQ,EAAIS,EAAST,QAAQ,CAAC/J,GAAG,CAAC,CAAC4E,EAAO1E,IAAU,WAACiJ,IAAAA,QAAc,YACjI,SAAfvE,EAAMzD,IAAI,EAAe,UAACxC,OAAAA,UAAMiG,EAAMzC,KAAK,GAC5B,UAAfyC,EAAMzD,IAAI,EAAgByD,EAAMzC,KAAK,EAAI,UAAC0E,MAAAA,CAAIX,IAAKtB,EAAMzC,KAAK,CAAE2E,IAAK,CAAC,eAAe,EAAE5G,EAAAA,CAAO,CAAE/C,UAAU,gDAF4C+C,IAGpI,UAACvB,OAAAA,UAAM8M,OAAOjB,EAAST,QAAQ,MAGxDS,CAAkB,oBAAlBA,EAASrJ,IAAI,EAA0BqJ,sBAASrJ,IAAI,CAAqB,EAAMqJ,EAASL,OAAO,EAAI,UAAC9L,MAAAA,CAAIlB,UAAU,qBAC/GqN,EAASL,OAAO,CAACnK,GAAG,CAAC,CAAC0L,EAAQxL,IAAU,WAACyL,QAAAA,CAAkBxO,UAAU,uDAClE,UAACyO,QAAAA,CAAMzK,KAAK,QAAQ0K,KAAMrB,EAAS5J,EAAE,CAAEuB,MAAOjC,EAAO4L,QAASjD,CAAO,CAAC2B,EAAS5J,EAAE,CAAC,GAAKV,EAAO6L,SAAU,IAAMvC,EAAmBgB,EAAS5J,EAAE,CAAEV,GAAQ/C,UAAU,0BAChK,UAACwB,OAAAA,UACoB,UAAlB,OAAO+M,EAAsBA,EAAShH,MAAMC,OAAO,CAAC+G,EAAOxK,OAAO,EAAIwK,EAAOxK,OAAO,CAAClB,GAAG,CAAC,CAAC4E,EAAOoH,IAAqB,WAAC7C,IAAAA,QAAc,YAChH,SAAfvE,EAAMzD,IAAI,EAAe,UAACxC,OAAAA,UAAMiG,EAAMzC,KAAK,GAC5B,UAAfyC,EAAMzD,IAAI,EAAgByD,EAAMzC,KAAK,EAAI,UAAC0E,MAAAA,CAAIX,IAAKtB,EAAMzC,KAAK,CAAE2E,IAAK,CAAC,aAAa,EAAEkF,EAAAA,CAAkB,CAAE7O,UAAU,+CAFkB6O,IAGnH,UAACrN,OAAAA,UAAM8M,OAAOC,EAAOxK,OAAO,EAAIwK,SANVxL,MAWvDsK,CAAkB,iBAATrJ,IAAI,EAAuC,eAAlBqJ,EAASrJ,IAAI,CAAgB,EAAM,WAAC9C,MAAAA,CAAIlB,UAAU,sBAClF,WAACwO,QAAAA,CAAMxO,UAAU,uDACf,UAACyO,QAAAA,CAAMzK,KAAK,QAAQ0K,KAAMrB,EAAS5J,EAAE,CAAEuB,MAAM,OAAO2J,QAAkC,SAAzBjD,CAAO,CAAC2B,EAAS5J,EAAE,CAAC,CAAamL,SAAU,IAAMvC,EAAmBgB,EAAS5J,EAAE,CAAE,QAASzD,UAAU,0BACjK,UAACwB,OAAAA,UAAK,YAER,WAACgN,QAAAA,CAAMxO,UAAU,uDACf,UAACyO,QAAAA,CAAMzK,KAAK,QAAQ0K,KAAMrB,EAAS5J,EAAE,CAAEuB,MAAM,QAAQ2J,QAAkC,UAAzBjD,CAAO,CAAC2B,EAAS5J,EAAE,CAAC,CAAcmL,SAAU,IAAMvC,EAAmBgB,EAAS5J,EAAE,CAAE,SAAUzD,UAAU,0BACpK,UAACwB,OAAAA,UAAK,gBAIO,UAAlB6L,EAASrJ,IAAI,EAAgB,UAAC8K,WAAAA,CAAS9O,UAAU,qGAAqG+O,KAAM,EAAGC,YAAY,2BAA2BhK,MAAO0G,CAAO,CAAC2B,EAAS5J,EAAE,CAAC,EAAI,GAAImL,SAAUpE,GAAK6B,EAAmBgB,EAAS5J,EAAE,CAAE+G,EAAEyE,MAAM,CAACjK,KAAK,SAK3S,WAAC9D,MAAAA,CAAIlB,UAAU,iCACb,UAAC2H,EAAAA,CAAMA,CAAAA,CAAChC,QAAQ,UAAU9E,QAAS,IAAM2K,EAAmBW,GAAQgB,KAAK+B,GAAG,CAAC,EAAG/C,EAAO,IAAK5K,SAA8B,IAApBgK,GAAyBO,EAAcpM,sBAAoB,SAASE,0BAAwB,0BAAiB,aAInN,UAACsB,MAAAA,CAAIlB,UAAU,0BACXsN,EAEY,UAAC3F,EAAAA,CAAMA,CAAAA,CAAC9G,QAAS2L,EAAkBjL,SAAU,CAACgM,GAAczB,EAAc9L,UAAU,2CAC7F8L,EAAe,gBAAkB,gBAHnB,UAACnE,EAAAA,CAAMA,CAAAA,CAAC9G,QAAS,IAAM2K,EAAmBW,GAAQA,EAAO,GAAI5K,SAAU,CAACgM,GAAczB,WAAc,qBAUrI,EAAE,GCrK2D,CAAC,CAC5DlI,SAAO,CACPuL,kBAAgB,iBAChBC,CAAe,yBACfC,CAAuB,aACvB1E,CAAW,YACXhK,CAAU,kBACV2O,CAAgB,CACjB,IACC,IAAMC,EAAoB3L,EAAQR,QAAQ,CAACoM,MAAM,CAACnM,GAAKA,EAAE5C,WAAW,EAAEQ,MAAM,CACtEwO,EAAgB7L,EAAQR,QAAQ,CAACnC,MAAM,CAE7C,MAAO,UAACsB,EAAAA,EAAIA,CAAAA,CAACkB,GAAI,CAAC,QAAQ,EAAEG,EAAQH,EAAE,EAAE,CAAEzD,UAAW,CAAC,6BAA6B,EAAE4D,EAAQpD,UAAU,CAAG,qBAAuB,oBAAoB,CAAC,EAAE,CAACoD,EAAQpD,UAAU,CAAG,aAAe,IAAI,CAAEd,sBAAoB,OAAOC,wBAAsB,iBAAiBC,0BAAwB,+BACzR,UAAC+C,EAAAA,EAAWA,CAAAA,CAAC3C,UAAU,MAAMN,sBAAoB,cAAcE,0BAAwB,+BACrF,WAACsB,MAAAA,CAAIlB,UAAU,0BACb,WAACkB,MAAAA,CAAIlB,UAAU,mDAAmDa,QAAS,IAAM+C,EAAQpD,UAAU,EAAI8O,cACrG,WAACpO,MAAAA,CAAIlB,UAAU,+CACb,UAACkB,MAAAA,CAAIlB,UAAW,CAAC,eAAe,EAAE4D,EAAQpD,UAAU,CAAG,4BAA8B,6BAA6B,UAChH,UAACsD,EAAAA,CAAIA,CAAAA,CAAC9D,UAAU,UAAUN,sBAAoB,OAAOE,0BAAwB,0BAE/E,WAACsB,MAAAA,CAAIlB,UAAU,mBACb,WAACkB,MAAAA,CAAIlB,UAAU,wCACb,UAACwB,OAAAA,CAAKxB,UAAU,uBAAe4D,EAAQtD,KAAK,GAC3C,CAACsD,EAAQpD,UAAU,EAAI,UAACmB,EAAAA,CAAIA,CAAAA,CAAC3B,UAAU,6BAE1C,UAACkB,MAAAA,CAAIlB,UAAU,yBACb,UAACqO,EAAAA,CAAQA,CAAAA,CAACrJ,MAfTyK,CAegBC,CAfA,EAAIH,EAAoBE,EAAgB,IAAM,EAepCzP,UAAU,MAAMN,sBAAoB,WAAWE,0BAAwB,0BAEpG,WAACkC,IAAAA,CAAE9B,UAAU,uCACVuP,EAAkB,IAAEE,EAAc,sBAClC7L,EAAQN,IAAI,CAACC,QAAQ,EAAI,0BAI/BK,EAAQpD,UAAU,GAAKG,CAAAA,CAAa,UAACoF,EAAAA,CAASA,CAAAA,CAAC/F,UAAU,0BAA6B,UAACyB,EAAAA,CAAWA,CAAAA,CAACzB,UAAU,0BAAyB,IAGxIW,GAAciD,EAAQpD,UAAU,EAAI,WAACU,MAAAA,CAAIlB,UAAU,+BAEhD,UAACkB,MAAAA,CAAIlB,UAAU,qBACZ4D,EAAQR,QAAQ,CAACP,GAAG,CAACkB,GAAW,UAACW,EAAWA,CAAkBX,QAAlBW,EAAoCC,iBAAkB,IAAM0K,EAAwBtL,EAAQN,EAAE,EAAG9C,WAAYwO,CAAgB,CAACpL,EAAQN,EAAE,CAAC,GAAI,EAAOmB,eAAgB,IAAMwK,EAAgBrL,EAAQN,EAAE,GAA9LM,EAAQN,EAAE,KAI/D,UAACiH,EAAQA,CAACpH,KAADoH,EAAepH,IAAI,CAAE9C,WAAY+O,IAAsBE,EAAe9E,YAAa,IAAMA,EAAY/G,EAAQN,IAAI,CAACG,EAAE,aAK3I,EAAE,GChDyD,CAAC,QAC1DX,CAAM,CACNqM,kBAAgB,kBAChBhN,CAAgB,iBAChBiN,CAAe,yBACfC,CAAuB,aACvB1E,CAAW,YACXhK,CAAU,kBACV2O,CAAgB,CAChBjN,iBAAe,CACfsN,qBAAmB,uBACnBC,CAAqB,CACtB,IACC,IAAMC,EAAgB/M,EAAOG,QAAQ,CAAChC,MAAM,CACtC6O,EAAoBhN,EAAOG,QAAQ,CAACuM,MAAM,CAACrM,GAAMA,EAAGC,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,IAAM0C,CAAAA,CAAGG,IAAI,CAACC,QAAQ,EAAIJ,EAAGG,IAAI,CAACe,QAAQ,EAAIlB,EAAGG,IAAI,CAACgB,WAAAA,GAAcrD,MAAM,CAI/J8O,EAAmBjN,EAAOG,QAAQ,CAACuM,MAAM,CAACrM,GAAMA,EAAG3C,UAAU,EAEnE,OAD4BuP,EAAiB9O,MAAM,CAAG,GAAK8O,EAAiB7M,KAAK,CAACC,IAAkC,IAA5BhB,CAAgB,CAACgB,EAAGM,EAAE,CAAC,EACxG,WAAClB,EAAAA,EAAIA,CAAAA,CAACkB,GAAI,CAAC,OAAO,EAAEX,EAAOW,EAAE,EAAE,CAAEzD,UAAW,CAAC,4BAA4B,EAAE8C,EAAOtC,UAAU,CAAG,mBAAqB,mBAAmB,CAAEd,sBAAoB,OAAOC,wBAAsB,gBAAgBC,0BAAwB,+BACrO,UAAC4C,EAAAA,EAAUA,CAAAA,CAACxC,UAAW,CAAC,SAAS,EAAE,CAAC8C,EAAOtC,UAAU,CAAG,aAAe,IAAI,CAAEd,sBAAoB,aAAaE,0BAAwB,8BACpI,WAACsB,MAAAA,CAAIlB,UAAU,mDAAmDa,QAAS,IAAMiC,EAAOtC,UAAU,EAAI8O,cACpG,WAACpO,MAAAA,CAAIlB,UAAU,+CACb,UAACkB,MAAAA,CAAIlB,UAAW,CAAC,eAAe,EAAE8C,EAAOtC,UAAU,CAAG,gCAAkC,6BAA6B,UACnH,UAACkD,EAAAA,CAAQA,CAAAA,CAAC1D,UAAU,UAAUN,sBAAoB,WAAWE,0BAAwB,yBAEvF,WAACsB,MAAAA,CAAIlB,UAAU,mBACb,WAACkB,MAAAA,CAAIlB,UAAU,wCACb,UAACyC,EAAAA,EAASA,CAAAA,CAACzC,UAAU,UAAUN,sBAAoB,YAAYE,0BAAwB,8BAAsBkD,EAAOxC,KAAK,GACxH,CAACwC,EAAOtC,UAAU,EAAI,UAACmB,EAAAA,CAAIA,CAAAA,CAAC3B,UAAU,6BAEzC,UAAC8B,IAAAA,CAAE9B,UAAU,8BAAsB8C,EAAOkN,WAAW,GACrD,UAAC9O,MAAAA,CAAIlB,UAAU,yBACb,UAACqO,EAAAA,CAAQA,CAAAA,CAACrJ,MAnBP6K,CAmBcH,CAnBE,EAAII,EAAoBD,EAAgB,IAAM,EAmBtC7P,UAAU,MAAMN,sBAAoB,WAAWE,0BAAwB,yBAEpG,WAACkC,IAAAA,CAAE9B,UAAU,uCACV8P,EAAkB,IAAED,EAAc,sBAClC/M,EAAOU,UAAU,CAACD,QAAQ,EAAI,iCAIpCT,EAAOtC,UAAU,EAAKG,EAAAA,CAAa,UAACoF,EAAAA,CAASA,CAAAA,CAAC/F,UAAU,0BAA6B,UAACyB,EAAAA,CAAWA,CAAAA,CAACzB,UAAU,0BAAyB,MAIzIW,GAAcmC,EAAOtC,UAAU,EAAI,WAACmC,EAAAA,EAAWA,CAAAA,CAAC3C,UAAU,iBAIvD,UAACkB,MAAAA,CAAIlB,UAAU,qBACZ8C,EAAOG,QAAQ,CAACJ,GAAG,CAACe,GAAW,UAACqM,GAAcA,CAAkBrM,QAASA,EAA3BqM,iBAAsDd,EAAkBC,gBAAiBA,EAAiBC,wBAAyBA,EAAyB1E,YAAaA,EAAahK,WAAYwB,CAAgB,CAACyB,EAAQH,EAAE,CAAC,GAAI,EAAO6L,iBAAkB,IAAMjN,EAAgBuB,EAAQH,EAAE,GAArRG,EAAQH,EAAE,KAIjE,WAACvC,MAAAA,CAAIlB,UAAU,+BACb,UAACwG,KAAAA,CAAGxG,UAAU,4CAAmC,sBAGjD,UAAC0K,EAAQA,CAACpH,KAADoH,EAAclH,UAAU,CAAEhD,WAAYsP,IAAsBD,EAAelF,YAAa,IAAMA,EAAY7H,EAAOU,UAAU,CAACC,EAAE,YAInJ,EAAE,iDE3CK,IAAMyM,GAAsC,CAAC,YAClDC,CAAU,iBACVjO,CAAe,kBACfC,CAAgB,kBAChBgN,CAAgB,gBAChB/M,CAAc,iBACdC,CAAe,iBACf+M,CAAe,yBACfC,CAAuB,aACvB1E,CAAW,qBACXyF,CAAmB,oBACnBC,CAAkB,CAClBC,sBAAoB,6BACpBC,CAA2B,CAC3BC,+BAA6B,CAC9B,IACC,GAAM,CAACC,EAAqBC,EAAuB,CAAGjF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzD,CAACnJ,EAAoBqO,EAAsB,CAAGlF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAClC0E,EAAWvN,OAAO,CAAC4M,MAAM,CAAChL,GAAKA,EAAEhE,UAAU,EAAE0C,KAAK,CAACsB,GAAKtC,CAAe,CAACsC,EAAEf,EAAE,CAAC,EACxG,IAAMmN,EAAgBT,EAAWvN,OAAO,CAACN,EAAmB,CACtDuO,EAAgBvO,EAAqB,EACrCwO,EAAYxO,EAAqB6N,EAAWvN,OAAO,CAAC3B,MAAM,CAAG,EAW7D8P,EAAyB,IAC7B,IAAMC,EAAcb,EAAWvN,OAAO,CAACqO,SAAS,CAACzM,GAAKA,EAAEf,EAAE,GAAKyN,GAC3C,CAAC,GAAG,CAApBF,GACFL,EAAsBK,EAE1B,EACA,MAAO,UAAC9P,MAAAA,CAAIlB,UAAU,wCAAwCL,wBAAsB,YAAYC,0BAAwB,0BACpH,WAACsB,MAAAA,CAAIlB,UAAU,iEAEb,UAACkB,MAAAA,CAAIlB,UAAU,iDACb,UAACkB,MAAAA,CAAIlB,UAAU,kBACb,UAAC+B,EAAeA,CAACC,OAAQmO,EAAYlO,GAArBF,QAAiC,CAACmP,EAAUC,EAAWC,KACvEL,EAAuBG,GACvBd,EAAoBc,EAAUC,EAAWC,EAC3C,EAAGlP,gBAAiBA,EAAiBC,iBAAkBA,EAAkBC,eAAgBA,EAAgBC,gBAAiBA,EAAiBC,mBAAoBA,EAAoB5C,sBAAoB,kBAAkBE,0BAAwB,uBAKnP,WAACsB,MAAAA,CAAIlB,UAAU,4DACZyQ,GAAuB,WAACvP,MAAAA,CAAIlB,UAAU,sEACnC,UAACoB,SAAAA,CAAOP,QAAS,IAAM6P,GAAuB,GAAQ1Q,UAAU,8EAA8EqR,aAAW,iCACvJ,UAACC,GAAAA,CAACA,CAAAA,CAACtR,UAAU,4BAEf,UAACuG,KAAAA,CAAGvG,UAAU,iDAAwC,oBACtD,UAAC8B,IAAAA,CAAE9B,UAAU,sCAA6B,iKAK1C,WAACkB,MAAAA,CAAIlB,UAAU,0DACb,WAACkB,MAAAA,CAAIlB,UAAU,wCACb,UAACmE,EAAUA,CAACnE,OAADmE,GAAW,0BACtB,UAAC3C,OAAAA,UAAK,oCAER,WAACN,MAAAA,CAAIlB,UAAU,wCACb,UAACoE,EAAAA,CAAMA,CAAAA,CAACpE,UAAU,0BAClB,UAACwB,OAAAA,UAAK,uBAER,WAACN,MAAAA,CAAIlB,UAAU,wCACb,UAACuR,GAAAA,CAAKA,CAAAA,CAACvR,UAAU,0BACjB,UAACwB,OAAAA,UAAK,qCAMd,UAACN,MAAAA,CAAIlB,UAAU,qBACZ4Q,GAAiB,UAAC1P,MAAAA,CAA2BsQ,iBAAgBZ,EAAcnN,EAAE,UAC1E,UAACgO,GAAaA,CAAC3O,OAAQ8N,EAATa,iBAA0CtC,EAAkBhN,iBAAkBA,EAAkBiN,gBAAiBA,EAAiBC,wBAAyBA,EAAyB1E,YAAaA,EAAahK,WAAYuB,CAAe,CAAC0O,EAAcnN,EAAE,CAAC,GAAI,EAAO6L,iBAAkB,IAAMlN,EAAewO,EAAcnN,EAAE,EAAGpB,gBAAiBA,EAAiBsN,oBAAqB,IAAMY,EAA4BK,EAAcnN,EAAE,EAAGmM,sBAAuB,IAAMY,EAA8BI,EAAcnN,EAAE,KAD7emN,EAAcnN,EAAE,IAM9C,WAACvC,MAAAA,CAAIlB,UAAU,kFACb,WAAC2H,EAAAA,CAAMA,CAAAA,CAAChC,QAAQ,UAAU9E,QAjEP,CAiEgB6Q,IAhEvCb,GACFF,EAAsBrO,EAAqB,EAE/C,EA6DmEf,IAhE9C,KAgEwD,CAACsP,EAAe7Q,UAAU,8BAA8BN,sBAAoB,SAASE,0BAAwB,2BAC9K,UAAC+R,GAAAA,CAAWA,CAAAA,CAAC3R,UAAU,UAAUN,sBAAoB,cAAcE,0BAAwB,mBAC3F,UAAC4B,OAAAA,UAAK,wBAGR,WAACN,MAAAA,CAAIlB,UAAU,wBACb,WAACuG,KAAAA,CAAGvG,UAAU,kCAAwB,SAC7BsC,EAAqB,EAAE,SAAO6N,EAAWvN,OAAO,CAAC3B,MAAM,IAEhE,UAACa,IAAAA,CAAE9B,UAAU,iCAAyB4Q,GAAetQ,WAGvD,WAACqH,EAAAA,CAAMA,CAAAA,CAAChC,QAAQ,UAAU9E,QAxEX,CAwEoB+Q,IAvEvCd,GACFH,EAAsBrO,EAAqB,EAE/C,EAoE+Df,SAAU,CAACuP,EAAW9Q,UAAU,8BAA8BN,sBAAoB,SAASE,0BAAwB,2BACtK,UAAC4B,OAAAA,UAAK,sBACN,UAACE,EAAAA,CAAYA,CAAAA,CAAC1B,UAAU,UAAUN,sBAAoB,eAAeE,0BAAwB,gCAM3G,EAAE,gBC7HK,IAAMiS,GAA0C,CAAC,YACtD1B,CAAU,iBACV2B,CAAe,CAChB,GACQ,WAAC5Q,MAAAA,CAAIlB,UAAU,aAAaL,wBAAsB,cAAcC,0BAAwB,6BAE3F,WAAC2C,EAAAA,EAAIA,CAAAA,CAAC7C,sBAAoB,OAAOE,0BAAwB,6BACvD,UAAC4C,EAAAA,EAAUA,CAAAA,CAAC9C,sBAAoB,aAAaE,0BAAwB,4BACnE,WAAC6C,EAAAA,EAASA,CAAAA,CAACzC,UAAU,8BAA8BN,sBAAoB,YAAYE,0BAAwB,6BACzG,UAACmS,GAAAA,CAASA,CAAAA,CAAC/R,UAAU,UAAUN,sBAAoB,YAAYE,0BAAwB,qBACvF,UAAC4B,OAAAA,UAAK,oCAGV,UAACmB,EAAAA,EAAWA,CAAAA,CAACjD,sBAAoB,cAAcE,0BAAwB,4BACrE,WAACsB,MAAAA,CAAIlB,UAAU,kDACb,WAACkB,MAAAA,CAAIlB,UAAU,wBACb,WAACkB,MAAAA,CAAIlB,UAAU,kDACZmN,KAAKC,KAAK,CAAC0E,GAAiB,OAE/B,UAAChQ,IAAAA,CAAE9B,UAAU,yBAAgB,yBAC7B,UAACqO,EAAAA,CAAQA,CAAAA,CAACrJ,MAAO8M,EAAiB9R,UAAU,OAAON,sBAAoB,WAAWE,0BAAwB,wBAE5G,WAACsB,MAAAA,CAAIlB,UAAU,wBACb,UAACkB,MAAAA,CAAIlB,UAAU,kDACZmQ,EAAWvN,OAAO,CAAC4M,MAAM,CAAChL,GAAKA,EAAEhB,UAAU,CAACD,QAAQ,EAAEtC,MAAM,GAE/D,UAACa,IAAAA,CAAE9B,UAAU,yBAAgB,kBAC7B,UAACqO,EAAAA,CAAQA,CAAAA,CAACrJ,MAAOmL,EAAWvN,OAAO,CAAC4M,MAAM,CAAChL,GAAKA,EAAEhB,UAAU,CAACD,QAAQ,EAAEtC,MAAM,CAAGkP,EAAWvN,OAAO,CAAC3B,MAAM,CAAG,IAAKjB,UAAU,OAAON,sBAAoB,WAAWE,0BAAwB,wBAE3L,WAACsB,MAAAA,CAAIlB,UAAU,wBACb,UAACkB,MAAAA,CAAIlB,UAAU,mDAA0C,MACzD,UAAC8B,IAAAA,CAAE9B,UAAU,yBAAgB,gBAC7B,UAAC8B,IAAAA,CAAE9B,UAAU,sCAA6B,+CASlD,WAACuC,EAAAA,EAAIA,CAAAA,CAAC7C,sBAAoB,OAAOE,0BAAwB,6BACvD,UAAC4C,EAAAA,EAAUA,CAAAA,CAAC9C,sBAAoB,aAAaE,0BAAwB,4BACnE,UAAC6C,EAAAA,EAASA,CAAAA,CAAC/C,sBAAoB,YAAYE,0BAAwB,4BAAmB,4BAExF,UAAC+C,EAAAA,EAAWA,CAAAA,CAACjD,sBAAoB,cAAcE,0BAAwB,4BACrE,UAACsB,MAAAA,CAAIlB,UAAU,qBACZmQ,EAAWvN,OAAO,CAACC,GAAG,CAACC,IACxB,IAAMgN,EAAoBhN,EAAOG,QAAQ,CAACuM,MAAM,CAACrM,GAAMA,EAAGC,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,GAAK0C,EAAGG,IAAI,CAACC,QAAQ,EAAEtC,MAAM,CAClH+Q,EAAiBlC,EAAoBhN,EAAOG,QAAQ,CAAChC,MAAM,CAAG,IACpE,MAAO,WAACC,MAAAA,CAAoBlB,UAAU,kCAChC,WAACkB,MAAAA,CAAIlB,UAAU,mDACb,UAACwG,KAAAA,CAAGxG,UAAU,uBAAe8C,EAAOxC,KAAK,GACzC,UAACoF,EAAAA,CAAKA,CAAAA,CAACC,QAAS7C,EAAOU,UAAU,CAACD,QAAQ,CAAG,UAAY,mBACtDT,EAAOU,UAAU,CAACD,QAAQ,CAAG,UAAY,sBAG9C,UAAC8K,EAAAA,CAAQA,CAAAA,CAACrJ,MAAOgN,EAAgBhS,UAAU,SAC3C,WAACkB,MAAAA,CAAIlB,UAAU,uDACb,WAACwB,OAAAA,WACEsO,EAAkB,IAAEhN,EAAOG,QAAQ,CAAChC,MAAM,CAAC,kBAG9C,WAACO,OAAAA,WAAM2L,KAAKC,KAAK,CAAC4E,GAAgB,YAbzBlP,EAAOW,EAAE,CAgB5B,UAMJ,WAAClB,EAAAA,EAAIA,CAAAA,CAAC7C,sBAAoB,OAAOE,0BAAwB,6BACvD,UAAC4C,EAAAA,EAAUA,CAAAA,CAAC9C,sBAAoB,aAAaE,0BAAwB,4BACnE,UAAC6C,EAAAA,EAASA,CAAAA,CAAC/C,sBAAoB,YAAYE,0BAAwB,4BAAmB,oBAExF,UAAC+C,EAAAA,EAAWA,CAAAA,CAACjD,sBAAoB,cAAcE,0BAAwB,4BACpE,CAAC,KACF,IAAMqS,EAAa,EAAE,OAiCrB,CA9BA9B,EAAWvN,OAAO,CAAC+J,OAAO,CAAC7J,IACzBA,EAAOG,QAAQ,CAAC0J,OAAO,CAAC/I,IAClBA,EAAQN,IAAI,CAACe,QAAQ,CAAG,GAAG,EAClB6N,IAAI,CAAC,CACd,GAAGtO,EAAQN,IAAI,CACf6O,WAAYrP,EAAOxC,KAAK,CACxB8R,YAAaxO,EAAQtD,KAAK,CAC1B0D,KAAM,SACR,EAEJ,GACIlB,EAAOU,UAAU,CAACa,QAAQ,CAAG,GAAG,EACvB6N,IAAI,CAAC,CACd,GAAGpP,EAAOU,UAAU,CACpB2O,WAAYrP,EAAOxC,KAAK,CACxB8R,YAAa,KACbpO,KAAM,QACR,EAEJ,GAGImM,EAAW1L,SAAS,CAACJ,QAAQ,CAAG,GAAG,EAC1B6N,IAAI,CAAC,CACd,GAAG/B,EAAW1L,SAAS,CACvB0N,WAAY,mBACZC,YAAa,KACbpO,KAAM,OACR,GAEwB,GAAG,CAAzBiO,EAAWhR,MAAM,EACZ,WAACC,MAAAA,CAAIlB,UAAU,2CAChB,UAACoE,EAAAA,CAAMA,CAAAA,CAACpE,UAAU,yCAClB,UAAC8B,IAAAA,UAAE,wEAKJ,UAACZ,MAAAA,CAAIlB,UAAU,qBACfiS,EAAWpP,GAAG,CAACS,GAAQ,WAACpC,MAAAA,CAAkBlB,UAAU,kCACjD,WAACkB,MAAAA,CAAIlB,UAAU,kDACb,WAACkB,MAAAA,WACC,UAACsF,KAAAA,CAAGxG,UAAU,uBAAesD,EAAKhD,KAAK,GACvC,WAACwB,IAAAA,CAAE9B,UAAU,kCACVsD,EAAK6O,UAAU,CACf7O,EAAK8O,WAAW,CAAG,CAAC,GAAG,EAAE9O,EAAK8O,WAAW,EAAE,CAAG,SAGnD,UAAC1M,EAAAA,CAAKA,CAAAA,CAACC,QAASrC,EAAKC,QAAQ,CAAG,UAAY,cAAevD,UAAWsD,EAAKC,QAAQ,CAAG,6CAA+C,YAClID,EAAKC,QAAQ,CAAG,QAAU,mBAI/B,WAACrC,MAAAA,CAAIlB,UAAU,0DACb,WAACkB,MAAAA,WACC,UAACM,OAAAA,CAAKxB,UAAU,yBAAgB,UAChC,UAACkB,MAAAA,CAAIlB,UAAU,4BACOwN,IAAnBlK,EAAK4H,SAAS,CAAiB,GAAG5H,EAAK4H,SAAS,CAAC,CAAC,CAAC,CAAG,WAG3D,WAAChK,MAAAA,WACC,UAACM,OAAAA,CAAKxB,UAAU,yBAAgB,gBAChC,WAACkB,MAAAA,CAAIlB,UAAU,wBAAesD,EAAKwH,YAAY,CAAC,UAElD,WAAC5J,MAAAA,WACC,UAACM,OAAAA,CAAKxB,UAAU,yBAAgB,eAChC,WAACkB,MAAAA,CAAIlB,UAAU,wBACZsD,EAAKe,QAAQ,CAAC,IAAEf,EAAKgB,WAAW,OAGrC,WAACpD,MAAAA,WACC,UAACM,OAAAA,CAAKxB,UAAU,yBAAgB,WAChC,UAACkB,MAAAA,CAAIlB,UAAU,kCACZsD,EAAKU,IAAI,SAKIwJ,SAAnBlK,EAAK4H,SAAS,EAAkB,UAAChK,MAAAA,CAAIlB,UAAU,gBAC5C,UAACqO,EAAAA,CAAQA,CAAAA,CAACrJ,MAAO1B,EAAK4H,SAAS,CAAElL,UAAW,CAAC,IAAI,EAAEsD,EAAKC,QAAQ,CAAG,iBAAmB,gBAAgB,OAxC5ED,EAAKG,EAAE,KA4CjD,yBCpKD,IAAM4O,GAAkC,CAAC,YAC9ClC,CAAU,aACVxF,CAAW,CACZ,IACC,IAAM2H,EAAsBnC,EAAWvN,OAAO,CAACM,KAAK,CAACsB,GAAKA,EAAEvB,QAAQ,CAACC,KAAK,CAACC,GAAMA,EAAGC,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,GAAK0C,EAAGG,IAAI,CAACC,QAAQ,GAAKiB,EAAEhB,UAAU,CAACD,QAAQ,EACpK,MAAO,WAAChB,EAAAA,EAAIA,CAAAA,CAACvC,UAAU,YAAYN,sBAAoB,OAAOC,wBAAsB,UAAUC,0BAAwB,yBAClH,UAAC4C,EAAAA,EAAUA,CAAAA,CAAC9C,sBAAoB,aAAaE,0BAAwB,wBACnE,WAAC6C,EAAAA,EAASA,CAAAA,CAACzC,UAAU,8BAA8BN,sBAAoB,YAAYE,0BAAwB,yBACzG,UAAC2E,EAAMA,CAACvE,GAADuE,OAAW,UAAU7E,sBAAoB,SAASE,0BAAwB,iBACjF,UAAC4B,OAAAA,UAAK,iCAGV,UAACmB,EAAAA,EAAWA,CAAAA,CAAC3C,UAAU,MAAMN,sBAAoB,cAAcE,0BAAwB,wBACrF,WAACsB,MAAAA,CAAIlB,UAAU,sBAEb,WAACkB,MAAAA,CAAIlB,UAAU,+DACb,UAACwG,KAAAA,CAAGxG,UAAU,6CAAoC,sBAGlD,WAACyG,KAAAA,CAAGzG,UAAU,6CACZ,UAAC2G,KAAAA,UAAG,wDACJ,WAACA,KAAAA,WAAG,0BACsBwJ,EAAW1L,SAAS,CAACqG,YAAY,CAAC,OAE5D,WAACnE,KAAAA,WAAG,kBAAgBwJ,EAAW1L,SAAS,CAACsG,SAAS,CAAC,YACnD,WAACpE,KAAAA,WAAG,yBAAuBwJ,EAAW1L,SAAS,CAACH,WAAW,UAK/D,WAACpD,MAAAA,CAAIlB,UAAU,kDACb,WAACkB,MAAAA,CAAIlB,UAAU,sBACb,UAACwG,KAAAA,CAAGxG,UAAU,qCAA4B,qBAGzCmQ,EAAWvN,OAAO,CAACC,GAAG,CAACC,GAAU,WAAC5B,MAAAA,CAAoBlB,UAAU,wEAC7D,UAACwB,OAAAA,CAAKxB,UAAU,mBAAW8C,EAAOxC,KAAK,GACtCwC,EAAOU,UAAU,CAACD,QAAQ,CAAG,UAAC0H,EAAAA,CAAWA,CAAAA,CAACjL,UAAU,2BAA8B,UAACuS,GAAAA,CAAOA,CAAAA,CAACvS,UAAU,2BAF9D8C,EAAOW,EAAE,MAKvD,WAACvC,MAAAA,CAAIlB,UAAU,sBACb,UAACwG,KAAAA,CAAGxG,UAAU,qCAA4B,oBAC1C,WAACkB,MAAAA,CAAIlB,UAAU,sBACb,WAACkB,MAAAA,CAAIlB,UAAU,8CACb,UAACwB,OAAAA,CAAKxB,UAAU,iCAAwB,wBACxC,WAACwB,OAAAA,CAAKxB,UAAU,wBACbmQ,EAAW1L,SAAS,CAACJ,QAAQ,CAAC,IAC9B8L,EAAW1L,SAAS,CAACH,WAAW,OAGrC,WAACpD,MAAAA,CAAIlB,UAAU,8CACb,UAACwB,OAAAA,CAAKxB,UAAU,iCAAwB,kBACxC,UAACwB,OAAAA,CAAKxB,UAAU,uBACbmQ,EAAW1L,SAAS,CAACyG,SAAS,CAAG,GAAGiF,EAAW1L,SAAS,CAACyG,SAAS,CAAC,CAAC,CAAC,CAAG,WAG7E,WAAChK,MAAAA,CAAIlB,UAAU,8CACb,UAACwB,OAAAA,CAAKxB,UAAU,iCAAwB,WACxC,UAAC0F,EAAAA,CAAKA,CAAAA,CAACC,QAASwK,EAAW1L,SAAS,CAAClB,QAAQ,CAAG,UAAY,UAAW7D,sBAAoB,QAAQE,0BAAwB,wBACxHuQ,EAAW1L,SAAS,CAAClB,QAAQ,CAAG,QAAU,8BAQrD,WAACrC,MAAAA,CAAIlB,UAAU,sCACb,WAAC2H,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKjC,QAAQ,MAAMpE,SAAU4O,EAAW1L,SAAS,CAACJ,QAAQ,EAAI8L,EAAW1L,SAAS,CAACH,WAAW,CAAEzD,QAAS,IAAM8J,EAAYwF,EAAW1L,SAAS,CAAChB,EAAE,EAAG/D,sBAAoB,SAASE,0BAAwB,yBACrN,UAAC2E,EAAMA,CAACvE,GAADuE,OAAW,eAAe7E,sBAAoB,SAASE,0BAAwB,iBACnD,IAAlCuQ,EAAW1L,SAAS,CAACJ,QAAQ,CAAS,oBAAsB,wBAE9D,CAACiO,GAAuB,UAACxQ,IAAAA,CAAE9B,UAAU,sCAA6B,+DAO/E,EAAE,GC1E2D,CAAC,YAC5DmQ,CAAU,aACVqC,CAAW,iBACXV,CAAe,uBACfW,CAAqB,mBACrBC,CAAiB,CACjBC,eAAa,CACd,IACC,IAAMC,EAAoB,UAExB,IAAMC,EAAOC,EAAAA,EAAWA,CAACC,OAAO,GAG1BC,EAAmC,CACvCC,YAAaJ,GAAMnE,MAAQ,WAE3BwE,WAAY/C,EAAWzB,IAAI,CAC3ByE,WAAYhD,EAAWtJ,IAAI,CAC3BuM,eAAgBjD,EAAWkD,WAAW,CAACD,cAAc,EAAI,IAAIE,OAAOC,WAAW,GAAGnO,KAAK,CAAC,IAAI,CAAC,EAAE,CAC/FoO,WAAYrD,EAAW1L,SAAS,CAACyG,SAAS,EAAI,EAC9CuI,eAAgBtD,EAAWuD,UAAU,CACrCC,gBAAiBnB,EAAY9D,IAAI,CACjCkF,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqBA,EACtC,CAGA,OAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAwBA,CAACd,EACjC,EACA,MAAO,WAACzQ,EAAAA,EAAIA,CAAAA,CAAC7C,sBAAoB,OAAOC,wBAAsB,iBAAiBC,0BAAwB,gCACnG,UAAC4C,EAAAA,EAAUA,CAAAA,CAAC9C,sBAAoB,aAAaE,0BAAwB,+BACnE,WAAC6C,EAAAA,EAASA,CAAAA,CAACzC,UAAU,8BAA8BqB,MAAO,CAC1D0S,MAAOvB,EAAYwB,mBAAmB,EAAEC,YAC1C,EAAGvU,sBAAoB,YAAYE,0BAAwB,gCACvD,UAAC2R,GAAAA,CAAKA,CAAAA,CAACvR,UAAU,UAAUN,sBAAoB,QAAQE,0BAAwB,wBAC/E,UAAC4B,OAAAA,UAAK,iCAGV,UAACmB,EAAAA,EAAWA,CAAAA,CAAC3C,UAAU,MAAMN,sBAAoB,cAAcE,0BAAwB,+BAClD,IAAlCuQ,EAAW1L,SAAS,CAACJ,QAAQ,CAChC,UAACnD,KAD6E,CAC7EA,CAAIlB,UAAU,iCACT,WAACkB,MAAAA,CAAIlB,UAAU,mGACb,UAAC2B,EAAAA,CAAIA,CAAAA,CAAC3B,UAAU,2CAChB,UAACuG,KAAAA,CAAGvG,UAAU,mDAA0C,+BAGxD,UAAC8B,IAAAA,CAAE9B,UAAU,2BAAkB,8GAIzBmQ,EAAWkD,WAAW,CAACa,UAAU,EAAI/D,EAAWkD,WAAW,CAACc,WAAW,EA0BvEhE,CA1B2E,CA0BhEkD,WAAW,CAACa,UAAU,CAC/C,EADmD,CACnD,QAAChT,MAAAA,CAAIlB,UAAU,iBA1BdkB,GAyBgG,cAE3F,WAACA,MAAAA,CAAIlB,UAAU,+FACb,UAACuR,GAAAA,CAAKA,CAAAA,CAACvR,UAAU,0CACjB,UAACuG,KAAAA,CAAGvG,UAAU,kDAAyC,aAGvD,WAAC8B,IAAAA,CAAE9B,UAAU,2BAAiB,4CACcmQ,EAAWzB,IAAI,CAAC,kCAG3DyB,EAAWkD,WAAW,CAACD,cAAc,EAAI,WAACtR,IAAAA,CAAE9B,UAAU,wCAA8B,qBAC9D,IAClB,IAAIsT,KAAKnD,EAAWkD,WAAW,CAACD,cAAc,EAAEgB,kBAAkB,SAGzE,WAAClT,MAAAA,CAAIlB,UAAU,0CACb,WAAC2H,EAAAA,CAAMA,CAAAA,CAAC9G,QAAS6R,EAAmB1S,UAAU,4CAC5C,UAACiJ,EAAAA,CAAGA,CAAAA,CAACjJ,UAAU,iBAAiB,sBAGlC,WAAC2H,EAAAA,CAAMA,CAAAA,CAAChC,QAAQ,UAAU3F,UAAU,oDAAoDa,QAAS+R,YAC/F,UAAC/J,EAAAA,CAAQA,CAAAA,CAAC7I,UAAU,iBAAiB,qBAK/C,EADe,CACf,QAACkB,MAAAA,CAAIlB,IAD8B,MACpB,sBACT,WAACkB,MAAAA,CAAIlB,UAAU,2EACb,UAAC2B,EAAAA,CAAIA,CAAAA,CAAC3B,UAAU,yCAChB,UAACuG,KAAAA,CAAGvG,UAAU,iDAAwC,2BAGtD,UAAC8B,IAAAA,CAAE9B,UAAU,8BAAqB,0FAMpC,WAACkB,MAAAA,CAAIlB,UAAU,kDACb,WAACkB,MAAAA,CAAIlB,UAAU,sBACb,UAACwG,KAAAA,CAAGxG,UAAU,qCAA4B,uBACzCmQ,EAAWvN,OAAO,CAACC,GAAG,CAACC,GAAU,WAAC5B,MAAAA,CAAoBlB,UAAU,wEAC7D,UAACwB,OAAAA,CAAKxB,UAAU,mBAAW8C,EAAOxC,KAAK,GACtCwC,EAAOU,UAAU,CAACD,QAAQ,CAAG,UAAC0H,EAAAA,CAAWA,CAAAA,CAACjL,UAAU,2BAA8B,UAACuS,GAAAA,CAAOA,CAAAA,CAACvS,UAAU,4BAF9D8C,EAAOW,EAAE,MAKvD,WAACvC,MAAAA,CAAIlB,UAAU,sBACb,UAACwG,KAAAA,CAAGxG,UAAU,qCAA4B,sBAG1C,WAACkB,MAAAA,CAAIlB,UAAU,wEACb,WAACwB,OAAAA,CAAKxB,UAAU,oBAAU,oBACNmQ,EAAW1L,SAAS,CAACqG,YAAY,CAAC,QAErDqF,EAAW1L,SAAS,CAAClB,QAAQ,CAAG,UAAC0H,EAAAA,CAAWA,CAAAA,CAACjL,UAAU,2BAA8B,UAACuS,GAAAA,CAAOA,CAAAA,CAACvS,UAAU,6BAE3G,WAACkB,MAAAA,CAAIlB,UAAU,wEACb,UAACwB,OAAAA,CAAKxB,UAAU,mBAAU,gCACzB8R,GAAmB,GAAK,UAAC7G,EAAAA,CAAWA,CAAAA,CAACjL,UAAU,2BAA8B,UAACuS,GAAAA,CAAOA,CAAAA,CAACvS,UAAU,0CAOnH,EAAE,SEwSF,OApb6B,KAC3B,IAAMqU,EAASC,CAAAA,EAAAA,CAmbQC,CAnbRD,CAmbS,QAnbTA,CAASA,GAClBE,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClBC,EAAeC,CAAAA,EAAAA,EAAAA,eAAAA,CAAeA,GAC9BC,EAAWP,EAAOO,QAAQ,CAC1B,CACJzE,WAAY0E,CAAiB,sBAC7BC,CAAoB,CACpBC,eAAa,oBACbC,CAAkB,CACnB,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,GAGX9E,EAAa4E,EAAcH,IAAaC,EACxC,CAAC1F,EAAkB+F,EAAoB,CAAGzJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAErD,CAAC,GACE,CAACvJ,EAAiBiT,EAAmB,CAAG1J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAEnD,CAAC,GACE,CAACtJ,EAAkBiT,EAAoB,CAAG3J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAErD,CAAC,GACE,CAAC4J,EAAaC,EAAe,CAAG7J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,MACtD,CAAC8J,EAAiBC,EAAmB,CAAG/J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjD,CAACgK,EAAWC,EAAa,CAAGjK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,UAC3CkK,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAAMlB,EAAamB,GAAG,CAAC,OACzBD,GACFF,EADO,EAGX,EAAG,CAAChB,EAAa,EACjB,IAAMoB,EAAgBC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,IAChCb,EAAoB/I,GAAS,EAC3B,EAD2B,CACxBA,CAAI,CACP,CAACiF,EAAU,CAAE,CAACjF,CAAI,CAACiF,EAAU,CAC/B,EACF,EAAG,EAAE,EACC4E,EAAeD,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,IAC/BZ,EAAmBhJ,GAAS,EAC1B,EAD0B,CACvBA,CAAI,CACP,CAAC+E,EAAS,CAAE,CAAC/E,CAAI,CAAC+E,EAAS,CAC7B,EACF,EAAG,EAAE,EACC+E,EAAgBF,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,IAChCX,EAAoBjJ,GAAS,EAC3B,EAD2B,CACxBA,CAAI,CACP,CAACgF,EAAU,CAAE,CAAChF,CAAI,CAACgF,EAAU,CAC/B,EACF,EAAG,EAAE,EACC+E,EAAmBH,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,KACnC,IAAMI,EAEF,CAAC,EACLhG,EAAWvN,OAAO,CAAC+J,OAAO,CAAC7J,IACrBA,EAAOtC,UAAU,EAAE,CACrB2V,CAAkB,CAACrT,EAAOW,EAAE,CAAC,EAAG,EAEpC,GACA0R,EAAmBgB,EACrB,EAAG,CAAChG,EAAWvN,OAAO,CAAC,EACjBwT,EAAqBL,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,KACrCZ,EAAmB,CAAC,EACtB,EAAG,EAAE,EACCkB,EAA4BN,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAE7E,IAC7C,IAAMoF,EAAenG,EAAWvN,OAAO,CAAC2T,IAAI,CAAC/R,GAAKA,EAAEf,EAAE,GAAKyN,GAC3D,GAAI,CAACoF,EAAc,OACnB,IAAME,EAAsB,CAC1B,GAAGrU,CAAgB,EAErBmU,EAAarT,QAAQ,CAAC0J,OAAO,CAAC/I,IACxBA,EAAQpD,UAAU,EAAE,CACtBgW,CAAmB,CAAC5S,EAAQH,EAAE,CAAC,EAAG,EAEtC,GACA2R,EAAoBoB,EACtB,EAAG,CAACrG,EAAWvN,OAAO,CAAET,EAAiB,EACnCsU,EAA8BV,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAE7E,IAC/C,IAAMoF,EAAenG,EAAWvN,OAAO,CAAC2T,IAAI,CAAC/R,GAAKA,EAAEf,EAAE,GAAKyN,GAC3D,GAAI,CAACoF,EAAc,OACnBnB,EAAmBhJ,GAAS,EAC1B,EAD0B,CACvBA,CAAI,CACP,CAAC+E,EAAS,EAAE,EACd,GAIA,IAAMsF,EAAsB,CAC1B,GAAGrU,CAAgB,EAErBmU,EAAarT,QAAQ,CAAC0J,OAAO,CAAC/I,IAC5B,OAAO4S,CAAmB,CAAC5S,EAAQH,EAAE,CAAC,GAExC2R,EAAoBoB,EACtB,EAAG,CAACrG,EAAWvN,OAAO,CAAET,EAAiB,EACnCuU,EAAwBX,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,IACxC,IAAMY,EAAYC,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAAC3G,IACxC4G,GAAe,EACnB,IAAK,IAAMT,KAAgBK,EAAU/T,OAAO,CAAE,CAC5C,IAAK,IAAMgB,KAAW0S,EAAarT,QAAQ,CAAE,CAC3C,IAAMc,EAAUH,EAAQR,QAAQ,CAACmT,IAAI,CAAClT,GAAKA,EAAEI,EAAE,GAAK2N,GACpD,GAAIrN,EAAS,CACXA,EAAQtD,WAAW,CAAG,CAACsD,EAAQtD,WAAW,CAC1CsW,GAAe,EACf,IAAMxH,EAAoB3L,EAAQR,QAAQ,CAACoM,MAAM,CAACnM,GAAKA,EAAE5C,WAAW,EAAEQ,MAAM,CACtE+V,EAAmBpT,EAAQqT,KAAK,CAChCC,EAAcZ,EAAarT,QAAQ,CAACsT,IAAI,CAACpT,GAAMA,EAAG8T,KAAK,GAAKD,EAAmB,GAKrF,GAJIE,GAAe3H,IAAsB3L,EAAQR,QAAQ,CAACnC,MAAM,EAAI2C,EAAQN,IAAI,CAACC,QAAQ,EAAE,CACzF2T,EAAY1W,UAAU,EAAG,GAEE8V,EAAarT,QAAQ,CAACC,KAAK,CAACC,GAAMA,EAAGC,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,GAAK0C,EAAGG,IAAI,CAACC,QAAQ,GAC5F+S,EAAa9S,UAAU,CAACD,QAAQ,CAAE,CAC5D,IAAM4T,EAAkBb,EAAaW,KAAK,CACpCG,EAAaT,EAAU/T,OAAO,CAAC2T,IAAI,CAAC/R,GAAKA,EAAEyS,KAAK,GAAKE,EAAkB,GACzEC,IACFA,EAAW5W,MADG,IACO,EAAG,EACpB4W,EAAWnU,QAAQ,CAAChC,MAAM,CAAG,GAAG,CAClCmW,EAAWnU,QAAQ,CAAC,EAAE,CAACzC,UAAU,EAAG,GAG1C,CACA,KACF,CACF,CACA,GAAIuW,EAAc,KACpB,CACAjC,EAAqB6B,EACvB,EAAG,CAACxG,EAAY2E,EAAqB,EAC/BuC,EAAYtB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,IAC5B,IAAIzS,EAGJ,GAAI6M,EAAW1L,SAAS,CAAChB,EAAE,GAAK6T,EAAQ,YAEtC9C,EAAOtC,IAAI,CAAC,CAAC,YAAY,EAAE0C,EAAS,wBAAwB,EAAE0C,EAAAA,CAAQ,EAKxE,IAAK,IAAMhB,KAAgBnG,EAAWvN,OAAO,CAAE,CAC7C,IAAK,IAAMgB,KAAW0S,EAAarT,QAAQ,CAAE,GACvCW,EAAQN,IAAI,CAACG,EAAE,GAAK6T,EAAQ,YAI9BhC,EAAe,IAHR1R,EAAQN,IAAI,GAIdA,GAKLgT,CALS,CAKI9S,UAAU,CAACC,EAAE,GAAK6T,EAAQ,YAIzChC,EAAe,IAHRgB,EAAa9S,UAIlB,EAIN,CAJSF,EAKR,CAAC6M,CALW,CAKCyE,EAAUJ,EAAO,EAC3B+C,EAAqBxB,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,IACrC,GAAI,CAACV,EAAa,OAClB,IAAMsB,EAAYC,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAAC3G,IACtCqH,EAAa,IACjBlU,EAAKe,QAAQ,EAAI,EACjBf,EAAK4H,SAAS,CAAGgC,EACjB5J,EAAKC,QAAQ,CAAG2J,GAAS5J,EAAKwH,YAAY,EAE5C,IAAK,IAAMwL,KAAgBK,EAAU/T,OAAO,CAAE,CAC5C,IAAK,IAAMgB,KAAW0S,EAAarT,QAAQ,CAAE,GACvCW,EAAQN,IAAI,CAACG,EAAE,GAAK4R,EAAY5R,EAAE,CAAE,CACtC+T,EAAW5T,EAAQN,IAAI,EACvB,IAAMmU,EAAuB7T,EAAQR,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,EAEtE,GAAI,CAACmD,EAAQN,IAAI,CAACC,QAAQ,EAAIK,EAAQN,IAAI,CAACe,QAAQ,EAAIT,EAAQN,IAAI,CAACgB,WAAW,GAAKmT,EAAsB,CACxG,IAAMP,EAAcZ,EAAarT,QAAQ,CAACsT,IAAI,CAACpT,GAAMA,EAAG8T,KAAK,GAAKrT,EAAQqT,KAAK,CAAG,GAC9EC,IACFA,EAAY1W,OADG,GACO,EAAG,EAE7B,CACA,KACF,CAEF,GAAI8V,EAAa9S,UAAU,CAACC,EAAE,GAAK4R,EAAY5R,EAAE,CAAE,CACjD+T,EAAWlB,EAAa9S,UAAU,EAClC,IAAMkU,EAAuBpB,EAAarT,QAAQ,CAACC,KAAK,CAACC,GAAMA,EAAGC,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,IAAM0C,CAAAA,CAAGG,IAAI,CAACC,QAAQ,EAAIJ,EAAGG,IAAI,CAACe,QAAQ,EAAIlB,EAAGG,IAAI,CAACgB,WAAAA,GACzJ,GAAI,CAACgS,EAAa9S,UAAU,CAACD,QAAQ,EAAI+S,EAAa9S,UAAU,CAACa,QAAQ,EAAIiS,EAAa9S,UAAU,CAACc,WAAAA,GAAgBoT,EAAsB,CACzI,IAAMN,EAAaT,EAAU/T,OAAO,CAAC2T,IAAI,CAAC/R,GAAKA,EAAEyS,KAAK,GAAKX,EAAaW,KAAK,CAAG,GAC5EG,IACFA,EAAW5W,MADG,IACO,EAAG,EACpB4W,EAAWnU,QAAQ,CAAChC,MAAM,CAAG,GAAG,CAClCmW,EAAWnU,QAAQ,CAAC,EAAE,CAACzC,UAAU,EAAG,GAG1C,CACF,CACF,CACA,GAAImW,EAAUlS,SAAS,CAAChB,EAAE,GAAK4R,EAAY5R,EAAE,CAAE,CAC7C+T,EAAWb,EAAUlS,SAAS,EAC9B,IAAMkT,EAAsBhB,EAAU/T,OAAO,CAACM,KAAK,CAACsB,GAAKA,EAAEvB,QAAQ,CAACC,KAAK,CAACC,GAAMA,EAAGC,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,IAAM0C,CAAAA,CAAGG,IAAI,CAACC,QAAQ,EAAIJ,EAAGG,IAAI,CAACe,QAAQ,EAAIlB,EAAGG,IAAI,CAACgB,WAAAA,KAAkBE,CAAAA,CAAEhB,UAAU,CAACD,QAAQ,EAAIiB,EAAEhB,UAAU,CAACa,QAAQ,EAAIG,EAAEhB,UAAU,CAACc,WAAAA,GACvPqS,EAAUlS,SAAS,CAAClB,QAAQ,EAAIoU,IAClChB,EAAUtD,WAAW,CAACa,GADiC,OACvB,EAAG,EACnCyC,EAAUtD,WAAW,CAACD,cAAc,CAAG,IAAIE,OAAOC,WAAW,GAAGnO,KAAK,CAAC,IAAI,CAAC,EAAE,CAC7EuR,EAAUiB,MAAM,CAAG,YAEvB,CACA9C,EAAqB6B,GACrBrB,EAAe,KACjB,EAAG,CAACD,EAAalF,EAAY2E,EAAqB,EAC5C+C,EAAsB9B,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,KAClC5F,EAAWkD,WAAW,CAACa,UAAU,EAAE,CASrCY,EARsB,CACpB,GAAG3E,CAAU,CACbkD,YAAa,CAMMyE,GALd3H,EAAWkD,WAAW,CACzBc,aAAa,EACb4D,eAAgB,CAAC,aAAa,EAAE5H,EAAW1M,EAAE,EAAE,CAEnD,GAEA+R,EAAmB,IAEvB,EAAG,CAACrF,EAAY2E,EAAqB,EAC/BkD,EAA0BjC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,CAAC7E,EAAkBC,EAAoBC,KAEjFsE,EAAa,UAGbP,EAAmBhJ,GAAS,EAC1B,EAD0B,CACvBA,CAAI,CACP,CAAC+E,EAAS,EAAE,EACd,GACIC,GACFiE,EAAoBjJ,GAAS,EAC3B,CAFW,CACgB,CACxBA,CAAI,CACP,CAACgF,EAAU,EAAE,EACf,GAIEC,GACF8D,EAAoB/I,GAAS,EAC3B,CAFW,CACgB,CACxBA,CAAI,CACP,CAACiF,EAAU,EAAE,EACf,GAIF1I,WAAW,SACLuP,EAEFA,EADE7G,EACS,CAAC,QADC,EACSA,EAAAA,CAAW,CACxBD,EACE,CAAC,QADQ,EACEA,EAAAA,CAAW,CAEtB,CAAC,OAAO,EAAED,EAAAA,CAAU,CAEjC,IAAMgH,EAAgBhQ,SAASI,cAAc,CAAC2P,GAC1CC,IACFA,EAAcC,SADG,KACW,CAAC,CAC3BC,SAAU,SACV3Q,MAAO,QACP4Q,OAAQ,SACV,GAGAH,EAAcI,SAAS,CAACC,GAAG,CAAC,SAAU,gBAAiB,mBACvD7P,WAAW,KACTwP,EAAcI,SAAS,CAACE,MAAM,CAAC,SAAU,gBAAiB,kBAC5D,EAAG,KAEP,EAAG,IACL,EAAG,EAAE,EAIC1I,EAAoBK,EAAWvN,OAAO,CAAC6V,MAAM,CAAC,CAACC,EAAO5V,IAAW4V,EAAQ5V,EAAOG,QAAQ,CAACuM,MAAM,CAACrM,GAAMA,EAAGC,QAAQ,CAACF,KAAK,CAACG,GAAKA,EAAE5C,WAAW,GAAK0C,EAAGG,IAAI,CAACC,QAAQ,EAAEtC,MAAM,CAAE,GACzK4O,EAAgBM,EAAWvN,OAAO,CAAC6V,MAAM,CAAC,CAACC,EAAO5V,IAAW4V,EAAQ5V,EAAOG,QAAQ,CAAChC,MAAM,CAAE,GAC7F6Q,GAAkBjC,EAAgB,EAAIC,EAAoBD,EAAgB,IAAM,EACtF,MAAO,UAAC3O,MAAAA,CAAIlB,UAAU,wCAAwCL,wBAAsB,aAAaC,0BAAwB,oBACrH,WAACsB,MAAAA,CAAIlB,UAAU,6CAEb,WAACkB,MAAAA,CAAIlB,UAAU,8CACb,WAACkB,MAAAA,CAAIlB,UAAU,wCACb,UAAC2Y,IAAIA,CAACvP,KAAK,KAANuP,SAAoBjZ,sBAAoB,OAAOE,0BAAwB,oBAC1E,WAAC+H,EAAAA,CAAMA,CAAAA,CAAChC,QAAQ,UAAUiC,KAAK,KAAK5H,UAAU,8BAA8BN,sBAAoB,SAASE,0BAAwB,qBAC/H,UAACgZ,EAAAA,CAAaA,CAAAA,CAAC5Y,UAAU,UAAUN,sBAAoB,gBAAgBE,0BAAwB,aAC/F,UAAC4B,OAAAA,UAAK,gCAGV,WAACN,MAAAA,CAAIlB,UAAU,wCACb,UAAC6Y,CAAYA,CAAAA,CAAC7Y,UAAU,oCAAoCN,sBAAoB,eAAeE,0BAAwB,aACvH,WAACsB,MAAAA,WACC,UAACkF,KAAAA,CAAGpG,UAAU,4CACXmQ,EAAWzB,IAAI,GAElB,WAAC5M,IAAAA,CAAE9B,UAAU,0BAAgB,gBAAcmQ,EAAWtJ,IAAI,IAC1D,WAAC/E,IAAAA,CAAE9B,UAAU,0BAAgB,eACdmQ,EAAWuD,UAAU,IAEpC,WAACxS,MAAAA,CAAIlB,UAAU,6CACb,WAACwB,OAAAA,CAAKxB,UAAU,kCACd,UAAC8Y,EAAYA,CAAC9Y,UAAU,KAAX8Y,iBAAiCpZ,sBAAoB,eAAeE,0BAAwB,aACxGuQ,EAAW4I,SAAS,CAAC,MAAI5I,EAAW6I,OAAO,IAE9C,UAACtT,EAAAA,CAAKA,CAAAA,CAACC,QAA+B,cAAtBwK,EAAWyH,MAAM,CAAmB,UAAY,YAAalY,sBAAoB,QAAQE,0BAAwB,oBACxG,cAAtBuQ,EAAWyH,MAAM,CAAmB,UAAY,+BAM3D,UAAC1W,MAAAA,CAAIlB,UAAU,uCACb,WAACkB,MAAAA,CAAIlB,UAAU,uBACb,UAAC8B,IAAAA,CAAE9B,UAAU,iCAAwB,yBACrC,WAACkB,MAAAA,CAAIlB,UAAU,wCACb,UAACqO,EAAAA,CAAQA,CAAAA,CAACrJ,MAAO8M,GAAiB9R,UAAU,OAAON,sBAAoB,WAAWE,0BAAwB,aAC1G,WAAC4B,OAAAA,CAAKxB,UAAU,gCACbmN,KAAKC,KAAK,CAAC0E,IAAiB,kBAQvC,WAACmH,EAAAA,EAAIA,CAAAA,CAACjU,MAAOyQ,EAAWyD,cAAexD,EAAc1V,UAAU,OAAON,sBAAoB,OAAOE,0BAAwB,qBACvH,WAACuZ,EAAAA,EAAQA,CAAAA,CAACnZ,UAAU,0BAA0BN,sBAAoB,WAAWE,0BAAwB,qBACnG,WAACwZ,EAAAA,EAAWA,CAAAA,CAACpU,MAAM,SAAShF,UAAU,8BAA8BN,sBAAoB,cAAcE,0BAAwB,qBAC5H,UAACyZ,EAAAA,CAAYA,CAAAA,CAACrZ,UAAU,UAAUN,sBAAoB,eAAeE,0BAAwB,aAC7F,UAAC4B,OAAAA,UAAK,qBAER,WAAC4X,EAAAA,EAAWA,CAAAA,CAACpU,MAAM,WAAWhF,UAAU,8BAA8BN,sBAAoB,cAAcE,0BAAwB,qBAC9H,UAAC0Z,EAAYA,CAACtZ,SAADsZ,CAAW,UAAU5Z,sBAAoB,eAAeE,0BAAwB,aAC7F,UAAC4B,OAAAA,UAAK,gBAER,WAAC4X,EAAAA,EAAWA,CAAAA,CAACpU,MAAM,OAAOhF,UAAU,8BAA8BN,sBAAoB,cAAcE,0BAAwB,qBAC1H,UAAC2Z,EAAAA,CAAUA,CAAAA,CAACvZ,UAAU,UAAUN,sBAAoB,aAAaE,0BAAwB,aACzF,UAAC4B,OAAAA,UAAK,kBAER,WAAC4X,EAAAA,EAAWA,CAAAA,CAACpU,MAAM,cAAchF,UAAU,8BAA8BN,sBAAoB,cAAcE,0BAAwB,qBACjI,UAAC4Z,EAAAA,CAASA,CAAAA,CAACxZ,UAAU,UAAUN,sBAAoB,YAAYE,0BAAwB,aACvF,UAAC4B,OAAAA,UAAK,qBAIV,UAACiY,EAAAA,EAAWA,CAAAA,CAACzU,MAAM,SAAShF,UAAU,OAAON,sBAAoB,cAAcE,0BAAwB,oBACrG,UAACsQ,GAASA,CAACC,KAADD,MAAaC,EAAYjO,gBAAiBA,EAAiBC,iBAAkBA,EAAkBgN,iBAAkBA,EAAkB/M,eAAgB4T,EAAc3T,gBAAiB4T,EAAe7G,gBAAiB0G,EAAezG,wBAAyBqH,EAAuB/L,YAAa0M,EAAWjH,oBAAqB4H,EAAyB3H,mBAAoB6F,EAAkB5F,qBAAsB8F,EAAoB7F,4BAA6B8F,EAA2B7F,8BAA+BiG,EAA6B/W,sBAAoB,YAAYE,0BAAwB,eAG/lB,UAAC6Z,EAAAA,EAAWA,CAAAA,CAACzU,MAAM,WAAWhF,UAAU,OAAON,sBAAoB,cAAcE,0BAAwB,oBACvG,UAACiS,GAAWA,CAAC1B,OAAD0B,IAAa1B,EAAY2B,gBAAiBA,GAAiBpS,sBAAoB,cAAcE,0BAAwB,eAGnI,UAAC6Z,EAAAA,EAAWA,CAAAA,CAACzU,MAAM,OAAOhF,UAAU,OAAON,sBAAoB,cAAcE,0BAAwB,oBACnG,UAACyS,GAAOA,CAAClC,GAADkC,QAAalC,EAAYxF,YAAa0M,EAAW3X,sBAAoB,UAAUE,0BAAwB,eAGjH,UAAC6Z,EAAAA,EAAWA,CAAAA,CAACzU,MAAM,cAAchF,UAAU,OAAON,sBAAoB,cAAcE,0BAAwB,oBAC1G,UAAC8Z,GAAcA,CAACvJ,UAADuJ,CAAavJ,EAAYqC,YAAa,CACrD/O,GAAI,gBACJiL,KAAM,qCACNiL,UAAW,MACXC,QAAS,oBACT5F,oBAAqB,CACnBC,aAAc,UACd4F,eAAgB,UAChBC,cAAe,sCACfC,eAAgB,0BAClB,CACF,EAAGjI,gBAAiBA,GAAiBW,sBAAuBoF,EAAqBnF,kBAAmB,IAAM8C,GAAmB,GAAO9V,sBAAoB,iBAAiBE,0BAAwB,kBAKlMyV,GAAe,UAAClK,GAASA,CAAC7H,KAAD6H,EAAoBC,QAAQ,EAAME,WAAYiM,EAAoBlM,QAAS,IAAMiK,EAAe,QAG1H,UAAC7H,EAAAA,EAAMA,CAAAA,CAACzF,KAAMuN,EAAiB7H,aAAc8H,EAAoB9V,sBAAoB,SAASE,0BAAwB,oBACpH,UAAC+N,EAAAA,EAAaA,CAAAA,CAAC3N,UAAU,sCAAsCN,sBAAoB,gBAAgBE,0BAAwB,oBACzH,WAACsB,MAAAA,CAAIlB,UAAU,8CACb,UAAC4N,EAAAA,EAAYA,CAAAA,CAAC5N,UAAU,mCAAmCN,sBAAoB,eAAeE,0BAAwB,oBACpH,WAACiO,EAAAA,EAAWA,CAAAA,CAAC7N,UAAU,8BAA8BN,sBAAoB,cAAcE,0BAAwB,qBAC7G,UAAC4Z,EAAAA,CAASA,CAAAA,CAACxZ,UAAU,UAAUN,sBAAoB,YAAYE,0BAAwB,aACvF,UAAC4B,OAAAA,UAAK,yBAGV,UAACN,MAAAA,CAAIlB,UAAU,4CACb,UAACkB,MAAAA,CAAIlB,UAAU,gBAAgBga,wBAAyB,CACxDC,OAAQC,CAAAA,EAAAA,EAAAA,EAAAA,CAA4BA,CAAC,CACnCjH,YAAaH,EAAAA,EAAWA,CAACC,OAAO,IAAIrE,MAAQ,WAC5CwE,WAAY/C,EAAWzB,IAAI,CAC3ByE,WAAYhD,EAAWtJ,IAAI,CAC3BuM,eAAgBjD,EAAWkD,WAAW,CAACD,cAAc,EAAI,IAAIE,OAAOC,WAAW,GAAGnO,KAAK,CAAC,IAAI,CAAC,EAAE,CAC/FoO,WAAYrD,EAAW1L,SAAS,CAACyG,SAAS,EAAI,EAC9CuI,eAAgBtD,EAAWuD,UAAU,CACrCC,gBAAiB,qCACjBC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqBA,EACtC,EACF,MAEA,UAAC3S,MAAAA,CAAIlB,UAAU,qDACb,WAACkB,MAAAA,CAAIlB,UAAU,uCACb,UAAC2H,EAAAA,CAAMA,CAAAA,CAAChC,QAAQ,UAAU9E,QAAS,IAAM2U,GAAmB,GAAQ9V,sBAAoB,SAASE,0BAAwB,oBAAW,UAGpI,WAAC+H,EAAAA,CAAMA,CAAAA,CAAC9G,QAAS,UAEjB,IAAMmS,EAAmC,CACvCC,YAAaH,EAAAA,EAAWA,CAACC,OAAO,IAAIrE,MAAQ,WAE5CwE,WAAY/C,EAAWzB,IAAI,CAC3ByE,WAAYhD,EAAWtJ,IAAI,CAC3BuM,eAAgBjD,EAAWkD,WAAW,CAACD,cAAc,EAAI,IAAIE,OAAOC,WAAW,GAAGnO,KAAK,CAAC,IAAI,CAAC,EAAE,CAC/FoO,WAAYrD,EAAW1L,SAAS,CAACyG,SAAS,EAAI,EAC9CuI,eAAgBtD,EAAWuD,UAAU,CACrCC,gBAAiB,qCACjBC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqBA,EACtC,CAGA,OAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAwBA,CAACd,EACjC,EAAGtT,sBAAoB,SAASE,0BAAwB,qBACpD,UAACua,CAAYA,CAAAA,CAACna,UAAU,eAAeN,sBAAoB,eAAeE,0BAAwB,aAAa,+BAUnI,iDCnbM,MAAc,cAAiB,eAbD,CAAC,CAAC,MAAQ,EAAE,EAAG,cAAgB,KAAK,CAAS,QAAC,CAAC,2BCHnF,kDCAA,gECAA,wDCAA,uDCAA,sDCAA,+ECmBM,MAAO,cAAiB,QAhBM,CAClC,CAAC,MAAQ,EAAE,KAAO,MAAM,OAAQ,IAAM,GAAG,CAAK,KAAG,KAAM,CAAI,MAAK,GAAI,GAAK,KAAK,SAAU,EACxF,CAAC,MAAQ,EAAE,EAAG,CAA4B,8BAAK,SAAU,EAC3D,yBCNA,sDCAA,sDCAA,yKCKA,IAAMqZ,EAAOmB,EAAAA,EAAkB,CACzBjB,EAAWnN,EAAAA,UAAgB,CAAyG,CAAC,WACzIhM,CAAS,CACT,GAAGT,EACJ,CAAE8a,IAAQ,UAACD,EAAAA,EAAkB,EAACC,IAAKA,EAAKra,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6FAA8FH,GAAa,GAAGT,CAAK,IAC1K4Z,EAASmB,WAAW,CAAGF,EAAAA,EAAkB,CAACE,WAAW,CACrD,IAAMlB,EAAcpN,EAAAA,UAAgB,CAA+G,CAAC,WAClJhM,CAAS,CACT,GAAGT,EACJ,CAAE8a,IAAQ,UAACD,EAAAA,EAAqB,EAACC,IAAKA,EAAKra,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qZAAsZH,GAAa,GAAGT,CAAK,IACre6Z,EAAYkB,WAAW,CAAGF,EAAAA,EAAqB,CAACE,WAAW,CAC3D,IAAMb,EAAczN,EAAAA,UAAgB,CAA+G,CAAC,WAClJhM,CAAS,CACT,GAAGT,EACJ,CAAE8a,IAAQ,UAACD,EAAAA,EAAqB,EAACC,IAAKA,EAAKra,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kIAAmIH,GAAa,GAAGT,CAAK,IAClNka,EAAYa,WAAW,CAAGF,EAAAA,EAAqB,CAACE,WAAW,yBCpB3D,oDCAA,kECAA,yDCAA,uDCAA,oICM+C,MAAQ,cAAC,4BAA4B,+CAA+C,WAAW,0oBAA0oB,IAAyB,+CCc3yB,MAAW,cAAiB,YAjBE,CAClC,CAAC,MAAQ,EAAE,EAAG,CAA6C,+CAAK,SAAU,EAC1E,CAAC,UAAY,EAAE,OAAQ,CAAoB,sBAAK,SAAU,EAC1D,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAM,OAAI,IAAM,IAAI,GAAK,KAAK,SAAU,EACnE,yBCPA,sDCAA,4DCAA,wDCAA,2GCGO,IAAMxH,EAAc,CACzByH,QAAS,IAIT,EAEAxH,QAAS,IAKA,KAGTyH,WAAY,KAIZ,EAEAC,gBAAiB,IACR3H,SAAYC,OAAO,GAG5B2H,QAAS,IACP,IAAM7H,EAAOC,EAAYC,OAAO,GAChC,OAAOF,GAAM8H,OAASA,CACxB,EAEAC,aAAc,IACL9H,EAAY4H,OAAO,CAAC,eAG7BG,UAAW,IACF/H,EAAY4H,OAAO,CAAC,WAG7BI,UAAW,IACFhI,EAAY4H,OAAO,CAAC,UAE/B,EAAE,EAG6B,IAC7B,OAAQ7H,EAAK8H,IAAI,EACf,IAAK,cACH,MAAO,kBACT,KAAK,UACH,MAAO,oBACT,KAAK,UACH,MAAO,UACT,SACE,MAAO,YACX,CACF,EAAE,EAGuB,KACvB,IAAM9H,EAAOC,EAAYC,OAAO,UAC3BF,GAKI,CALL,EAAO,CAQb,EAAE,EAGyB,IACzB,IAAMA,EAAOkI,WACb,GAEIlI,CAFA,CAEK8H,CAFE,GAEE,GAAKK,EAOXnI,EATW,IAUpB,EAAE,IARgC,oBC9ElC,uDCAA,sDCAA,iDCAA,kFCmBM,MAAc,cAAiB,eAhBD,CAClC,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,MAAM,GAAK,UAAU,EACzD,CAAC,MAAQ,EAAE,EAAG,CAAiB,mBAAK,SAAU,EAChD,0BCNA,uHCcO,IAAMgB,EAAwB,KACnC,IAAMoH,EAAO,IAAI3H,OAAO4H,WAAW,GAC7BC,EAAYhO,KAAKc,KAAK,CAAiB,IAAhBd,KAAKiO,MAAM,IACrCjN,QAAQ,GACRC,QAAQ,CAAC,EAAG,KACf,MAAO,CAAC,KAAK,EAAE6M,EAAK,CAAC,EAAEE,EAAAA,CAAW,EAGvBE,EAA0B,GAC9B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA+KwB,EAAEC,EAAK3H,eAAe,CAAC;;;;;uCAKlB,EAAE2H,EAAKrI,WAAW,CAAC;;;;;;0CAMhB,EAAEqI,EAAKpI,UAAU,CAAC;wDACJ,EAAEoI,EAAKnI,UAAU,CAAC;;;4CAG9B,EAAEmI,EAAK9H,UAAU,CAAC;;;;;;6CAMjB,EAAE8H,EAAK7H,cAAc,CAAC;;;;kEAID,EAAE6H,EAAKlI,cAAc,CAAC;;;;;mCAKrD,EAAEkI,EAAK1H,aAAa,CAAC;sCAClB,EAAE0H,EAAKlI,cAAc,CAAC;;;;;EAK1D,CAAC,CAIU8G,EAA+B,GACnC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA0KwB,EAAEoB,EAAK3H,eAAe,CAAC;;;;;uCAKlB,EAAE2H,EAAKrI,WAAW,CAAC;;;;;;0CAMhB,EAAEqI,EAAKpI,UAAU,CAAC;wDACJ,EAAEoI,EAAKnI,UAAU,CAAC;;;4CAG9B,EAAEmI,EAAK9H,UAAU,CAAC;;;;;;6CAMjB,EAAE8H,EAAK7H,cAAc,CAAC;;;;kEAID,EAAE6H,EAAKlI,cAAc,CAAC;;;;;mCAKrD,EAAEkI,EAAK1H,aAAa,CAAC;sCAClB,EAAE0H,EAAKlI,cAAc,CAAC;;;;EAI1D,CAAC,CAGUU,EAA2B,MACtCwH,IAEA,GAAI,CACF,IAAMrT,EAAcoT,EAAwBC,GAGtCC,EAAW,MAAMC,MAAM,oBAAqB,CAChDC,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,EACApS,KAAMsN,KAAKE,SAAS,CAAC,aAAE7O,CAAY,EACrC,GAEA,GAAI,CAACsT,EAASI,EAAE,CACd,CADgB,KACV,MAAU,0BAGlB,IAAMC,EAAO,MAAML,EAASK,IAAI,GAC1BC,EAAMC,IAAIC,eAAe,CAACH,GAE1B1S,EAAOhB,SAASiB,aAAa,CAAC,KACpCD,EAAKE,IAAI,CAAGyS,EACZ3S,EAAKG,QAAQ,CAAG,CAAC,YAAY,EAAEiS,EAAK1H,aAAa,CAAC,IAAI,CAAC,CACvD1L,SAASoB,IAAI,CAACC,WAAW,CAACL,GAC1BA,EAAKM,KAAK,GACVtB,SAASoB,IAAI,CAACG,WAAW,CAACP,GAC1B4S,IAAIE,eAAe,CAACH,EACtB,CAAE,MAAOI,EAAO,CACdpX,QAAQoX,KAAK,CAAC,wBAAyBA,GAGvC,IAAML,EAAO,IAAIM,KAAK,CADFb,EAAwBC,GACT,CAAE,CAAEtX,KAAM,WAAY,GACnD6X,EAAMC,IAAIC,eAAe,CAACH,GAE1B1S,EAAOhB,SAASiB,aAAa,CAAC,KACpCD,EAAKE,IAAI,CAAGyS,EACZ3S,EAAKG,QAAQ,CAAG,CAAC,YAAY,EAAEiS,EAAK1H,aAAa,CAAC,KAAK,CAAC,CACxD1L,SAASoB,IAAI,CAACC,WAAW,CAACL,GAC1BA,EAAKM,KAAK,GACVtB,SAASoB,IAAI,CAACG,WAAW,CAACP,GAC1B4S,IAAIE,eAAe,CAACH,EACtB,CACF,EAAE,EAEiCP,IACjC,IAAMrT,EAAcoT,EAAwBC,GACtCa,EAAYpU,OAAOC,IAAI,CAAC,GAAI,UAC9BmU,IACFA,EAAUjU,KADG,GACK,CAACC,KAAK,CAACF,GACzBkU,EAAUjU,QAAQ,CAACE,KAAK,GAE5B,EAAE,EAE8B,MAC9BkT,IAEA,GAAIc,UAAUC,KAAK,CACjB,CADmB,EACf,CACF,MAAMD,UAAUC,KAAK,CAAC,CACpB/b,MAAO,CAAC,4BAA4B,EAAEgb,EAAKpI,UAAU,EAAE,CACvDoJ,KAAM,CAAC,eAAe,EAAEhB,EAAKpI,UAAU,CAAC,iBAAiB,EAAEoI,EAAK9H,UAAU,CAAC,EAAE,CAAC,CAC9EqI,IAAK9T,OAAOwU,QAAQ,CAACnT,IAAI,EAE7B,CAAE,MAAO6S,EAAO,CACdpX,QAAQoX,KAAK,CAAC,6BAA8BA,GAE5CO,EACE,CAAC,eAAe,EAAElB,EAAKpI,UAAU,CAAC,iBAAiB,EAAEoI,EAAK9H,UAAU,CAAC,mBAAmB,EAAE8H,EAAK1H,aAAa,EAAE,CAElH,MAGA4I,EACE,CAAC,eAAe,EAAElB,EAAKpI,UAAU,CAAC,iBAAiB,EAAEoI,EAAK9H,UAAU,CAAC,mBAAmB,EAAE8H,EAAK1H,aAAa,EAAE,CAGpH,EAAE,EAEsB,IACtBwI,UAAUK,SAAS,CAChBC,SAAS,CAACJ,GACVK,IAAI,CAAC,KAEJ9X,QAAQC,GAAG,CAAC,0CACd,GACC8X,KAAK,CAAC,IACL/X,QAAQoX,KAAK,CAAC,+BAAgCA,EAChD,EACJ,0BCzhBA,iDCAA,wDCAA,sDCAA,uCAAsL,oKCMtL,SAASxO,EAAO,CACd,GAAGlO,EAC+C,EAClD,MAAO,UAACsd,EAAAA,EAAoB,EAACpd,YAAU,SAAU,GAAGF,CAAK,CAAEG,sBAAoB,uBAAuBC,wBAAsB,SAASC,0BAAwB,cAC/J,CACA,SAASkd,EAAc,CACrB,GAAGvd,EACkD,EACrD,MAAO,UAACsd,EAAAA,EAAuB,EAACpd,YAAU,iBAAkB,GAAGF,CAAK,CAAEG,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,cACpL,CACA,SAASmd,EAAa,CACpB,GAAGxd,EACiD,EACpD,MAAO,UAACsd,EAAAA,EAAsB,EAACpd,YAAU,gBAAiB,GAAGF,CAAK,CAAEG,sBAAoB,yBAAyBC,wBAAsB,eAAeC,0BAAwB,cAChL,CAMA,SAASod,EAAc,WACrBhd,CAAS,CACT,GAAGT,EACkD,EACrD,MAAO,UAACsd,EAAAA,EAAuB,EAACpd,YAAU,iBAAiBO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yJAA0JH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,cACxW,CACA,SAAS+N,EAAc,WACrB3N,CAAS,UACTE,CAAQ,CACR,GAAGX,EACkD,EACrD,MAAO,WAACwd,EAAAA,CAAatd,YAAU,gBAAgBC,sBAAoB,eAAeC,wBAAsB,gBAAgBC,0BAAwB,uBAC5I,UAACod,EAAAA,CAActd,sBAAoB,gBAAgBE,0BAAwB,eAC3E,WAACid,EAAAA,EAAuB,EAACpd,YAAU,iBAAiBO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8WAA+WH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,0BAA0BE,0BAAwB,uBAC3gBM,EACD,WAAC2c,EAAAA,EAAqB,EAAC7c,UAAU,oWAAoWN,sBAAoB,wBAAwBE,0BAAwB,uBACvc,UAACqd,EAAAA,CAAKA,CAAAA,CAACvd,sBAAoB,QAAQE,0BAAwB,eAC3D,UAAC4B,OAAAA,CAAKxB,UAAU,mBAAU,kBAIpC,CACA,SAAS4N,EAAa,WACpB5N,CAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAAC2B,MAAAA,CAAIzB,YAAU,gBAAgBO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+CAAgDH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,eAAeC,0BAAwB,cAC1L,CACA,SAASsd,EAAa,WACpBld,CAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAAC2B,MAAAA,CAAIzB,YAAU,gBAAgBO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yDAA0DH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,eAAeC,0BAAwB,cACpM,CACA,SAASiO,EAAY,WACnB7N,CAAS,CACT,GAAGT,EACgD,EACnD,MAAO,UAACsd,EAAAA,EAAqB,EAACpd,YAAU,eAAeO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,cAC5O,CACA,SAASud,EAAkB,WACzBnd,CAAS,CACT,GAAGT,EACsD,EACzD,MAAO,UAACsd,EAAAA,EAA2B,EAACpd,YAAU,qBAAqBO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,cAC/P,0BCvEA", "sources": ["webpack://terang-lms-ui/../../../src/icons/circle-x.ts", "webpack://terang-lms-ui/external node-commonjs \"node:process\"", "webpack://terang-lms-ui/./src/components/ui/tooltip.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/?3425", "webpack://terang-lms-ui/?4fc2", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/../../../src/icons/chevron-up.ts", "webpack://terang-lms-ui/../../../src/icons/play.ts", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/building_02_icon.js", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/calendar_01_icon.js", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/chart_icon.js", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/download_01_icon.js", "webpack://terang-lms-ui/./src/components/lms/tree-node.tsx", "webpack://terang-lms-ui/../../../src/icons/list.ts", "webpack://terang-lms-ui/../../../src/icons/book-marked.ts", "webpack://terang-lms-ui/../../../src/icons/trophy.ts", "webpack://terang-lms-ui/./src/components/lms/table-of-contents.tsx", "webpack://terang-lms-ui/../../../src/icons/timer.ts", "webpack://terang-lms-ui/../../../src/icons/external-link.ts", "webpack://terang-lms-ui/./src/components/lms/content-item.tsx", "webpack://terang-lms-ui/./src/components/lms/quiz-card.tsx", "webpack://terang-lms-ui/./src/components/lms/quiz-modal.tsx", "webpack://terang-lms-ui/./src/components/lms/chapter-section.tsx", "webpack://terang-lms-ui/./src/components/lms/module-section.tsx", "webpack://terang-lms-ui/./src/components/lms/certificate-template.tsx", "webpack://terang-lms-ui/./src/components/lms/tabs/course-tab.tsx", "webpack://terang-lms-ui/./src/components/lms/tabs/progress-tab.tsx", "webpack://terang-lms-ui/./src/components/lms/tabs/exam-tab.tsx", "webpack://terang-lms-ui/./src/components/lms/tabs/certificate-tab.tsx", "webpack://terang-lms-ui/./src/components/lms/index.ts", "webpack://terang-lms-ui/./src/app/(course-view)/my-courses/[courseId]/page.tsx", "webpack://terang-lms-ui/../../../src/icons/chevron-down.ts", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/../../../src/icons/lock.ts", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:url\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/./src/components/ui/tabs.tsx", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/book_open_01_icon.js", "webpack://terang-lms-ui/../../../src/icons/download.ts", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/./src/lib/auth.ts", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/../../../src/icons/circle-check.ts", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/./src/lib/certificate.ts", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/?0394", "webpack://terang-lms-ui/./src/components/ui/dialog.tsx", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm15 9-6 6', key: '1uzhvr' }],\n  ['path', { d: 'm9 9 6 6', key: 'z0biqf' }],\n];\n\n/**\n * @component @name CircleX\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtMTUgOS02IDYiIC8+CiAgPHBhdGggZD0ibTkgOSA2IDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleX = createLucideIcon('CircleX', __iconNode);\n\nexport default CircleX;\n", "module.exports = require(\"node:process\");", "'use client';\n\nimport * as React from 'react';\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip';\nimport { cn } from '@/lib/utils';\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return <TooltipPrimitive.Provider data-slot='tooltip-provider' delayDuration={delayDuration} {...props} data-sentry-element=\"TooltipPrimitive.Provider\" data-sentry-component=\"TooltipProvider\" data-sentry-source-file=\"tooltip.tsx\" />;\n}\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return <TooltipProvider data-sentry-element=\"TooltipProvider\" data-sentry-component=\"Tooltip\" data-sentry-source-file=\"tooltip.tsx\">\r\n      <TooltipPrimitive.Root data-slot='tooltip' {...props} data-sentry-element=\"TooltipPrimitive.Root\" data-sentry-source-file=\"tooltip.tsx\" />\r\n    </TooltipProvider>;\n}\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot='tooltip-trigger' {...props} data-sentry-element=\"TooltipPrimitive.Trigger\" data-sentry-component=\"TooltipTrigger\" data-sentry-source-file=\"tooltip.tsx\" />;\n}\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return <TooltipPrimitive.Portal data-sentry-element=\"TooltipPrimitive.Portal\" data-sentry-component=\"TooltipContent\" data-sentry-source-file=\"tooltip.tsx\">\r\n      <TooltipPrimitive.Content data-slot='tooltip-content' sideOffset={sideOffset} className={cn('bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance', className)} {...props} data-sentry-element=\"TooltipPrimitive.Content\" data-sentry-source-file=\"tooltip.tsx\">\r\n        {children}\r\n        <TooltipPrimitive.Arrow className='bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]' data-sentry-element=\"TooltipPrimitive.Arrow\" data-sentry-source-file=\"tooltip.tsx\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>;\n}\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\page.tsx\");\n", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module7 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page8 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(course-view)',\n        {\n        children: [\n        'my-courses',\n        {\n        children: [\n        '[courseId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page8, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\layout.tsx\"],\n'forbidden': [module6, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module7, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/(course-view)/my-courses/[courseId]/page\",\n        pathname: \"/my-courses/[courseId]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"module\");", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/(course-view)/my-courses/[courseId]',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/(course-view)/my-courses/[courseId]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/(course-view)/my-courses/[courseId]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/(course-view)/my-courses/[courseId]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm18 15-6-6-6 6', key: '153udz' }]];\n\n/**\n * @component @name ChevronUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTggMTUtNi02LTYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronUp = createLucideIcon('ChevronUp', __iconNode);\n\nexport default ChevronUp;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('Play', __iconNode);\n\nexport default Play;\n", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"Building02Icon\",[[\"path\",{d:\"M15 2H9C5.69067 2 5 2.69067 5 6V22H19V6C19 2.69067 18.3093 2 15 2Z\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M3 22H21\",stroke:\"currentColor\",key:\"k1\"}],[\"path\",{d:\"M15 22V19C15 17.3453 14.6547 17 13 17H11C9.34533 17 9 17.3453 9 19V22\",stroke:\"currentColor\",key:\"k2\"}],[\"path\",{d:\"M13.5 6H10.5M13.5 9.5H10.5M13.5 13H10.5\",stroke:\"currentColor\",key:\"k3\"}]]);export{o as default};\n", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"Calendar01Icon\",[[\"path\",{d:\"M18 2V4M6 2V4\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M10 17L9.99999 13.3472C9.99999 13.1555 9.86325 13 9.69458 13H9M13.6297 17L14.9842 13.3492C15.0475 13.1785 14.9128 13 14.7207 13H13\",stroke:\"currentColor\",key:\"k1\"}],[\"path\",{d:\"M2.5 12.2432C2.5 7.88594 2.5 5.70728 3.75212 4.35364C5.00424 3 7.01949 3 11.05 3H12.95C16.9805 3 18.9958 3 20.2479 4.35364C21.5 5.70728 21.5 7.88594 21.5 12.2432V12.7568C21.5 17.1141 21.5 19.2927 20.2479 20.6464C18.9958 22 16.9805 22 12.95 22H11.05C7.01949 22 5.00424 22 3.75212 20.6464C2.5 19.2927 2.5 17.1141 2.5 12.7568V12.2432Z\",stroke:\"currentColor\",key:\"k2\"}],[\"path\",{d:\"M6 8H18\",stroke:\"currentColor\",key:\"k3\"}]]);export{o as default};\n", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"ChartIcon\",[[\"path\",{d:\"M2 21.5L22 21.5\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M18 15.5H18.009M18 18.5H18.009\",stroke:\"currentColor\",key:\"k1\"}],[\"path\",{d:\"M6 18.5H6.00898M6 15.5H6.00898M6 12.5H6.00898M6 9.5H6.00898\",stroke:\"currentColor\",key:\"k2\"}],[\"path\",{d:\"M7.79063 5.39186L16.2183 9.5904M8 4.5C8 5.60457 7.10457 6.5 6 6.5C4.89543 6.5 4 5.60457 4 4.5C4 3.39543 4.89543 2.5 6 2.5C7.10457 2.5 8 3.39543 8 4.5ZM20 10.5C20 11.6046 19.1046 12.5 18 12.5C16.8954 12.5 16 11.6046 16 10.5C16 9.39543 16.8954 8.5 18 8.5C19.1046 8.5 20 9.39543 20 10.5Z\",stroke:\"currentColor\",key:\"k3\"}]]);export{o as default};\n", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport o from\"../create-hugeicon-component.js\";const r=o(\"Download01Icon\",[[\"path\",{d:\"M3.09502 10C3.03241 10.457 3 10.9245 3 11.4C3 16.7019 7.02944 21 12 21C16.9706 21 21 16.7019 21 11.4C21 10.9245 20.9676 10.457 20.905 10\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M12 13L12 3M12 13C11.2998 13 9.99153 11.0057 9.5 10.5M12 13C12.7002 13 14.0085 11.0057 14.5 10.5\",stroke:\"currentColor\",key:\"k1\"}]]);export{r as default};\n", "import React from 'react';\nimport { ChevronDown, ChevronRight, Lock, CheckCircle2 } from 'lucide-react';\nimport { TreeNodeProps } from '@/types/lms';\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\nexport const TreeNode: React.FC<TreeNodeProps> = ({\n  title,\n  icon,\n  isUnlocked,\n  isCompleted = false,\n  children,\n  level,\n  isExpanded = false,\n  onToggle,\n  onClick,\n  hasChildren = false,\n  isActive = false\n}) => {\n  // Check if text might be truncated (rough estimate)\n  const mightBeTruncated = title.length > 20;\n  return <div className='select-none' data-sentry-component=\"TreeNode\" data-sentry-source-file=\"tree-node.tsx\">\r\n      <TooltipProvider data-sentry-element=\"TooltipProvider\" data-sentry-source-file=\"tree-node.tsx\">\r\n        <Tooltip delayDuration={500} data-sentry-element=\"Tooltip\" data-sentry-source-file=\"tree-node.tsx\">\r\n          <TooltipTrigger asChild data-sentry-element=\"TooltipTrigger\" data-sentry-source-file=\"tree-node.tsx\">\r\n            <button onClick={() => {\n            if (hasChildren && onToggle) {\n              onToggle();\n            } else if (onClick) {\n              onClick();\n            }\n          }} className={`flex w-full items-center space-x-3 rounded-lg px-4 py-3 text-left text-base transition-colors ${isUnlocked ? isActive ? 'bg-blue-100 text-blue-800 border-2 border-blue-300 hover:bg-blue-150' : 'text-blue-700 hover:bg-blue-50' : 'cursor-not-allowed text-gray-400'}`} style={{\n            paddingLeft: `${level * 20 + 16}px`\n          }} disabled={!isUnlocked}>\r\n              {hasChildren && <span className='flex h-5 w-5 items-center justify-center'>\r\n                  {isExpanded ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}\r\n                </span>}\r\n              <span className='flex h-5 w-5 items-center justify-center'>{icon}</span>\r\n              <span className={`flex-1 font-medium ${title.length > 35 ? 'text-sm leading-tight' : 'truncate'}`}>\r\n                {title.length > 35 ? <span className=\"block\">{title}</span> : title}\r\n              </span>\r\n              {!isUnlocked && <Lock className='h-4 w-4' />}\r\n              {isCompleted && <CheckCircle2 className='h-4 w-4 text-green-500' />}\r\n            </button>\r\n          </TooltipTrigger>\r\n          {mightBeTruncated && <TooltipContent side=\"right\" className=\"max-w-xs\">\r\n              <p>{title}</p>\r\n            </TooltipContent>}\r\n        </Tooltip>\r\n      </TooltipProvider>\r\n      {isExpanded && children && <div>{children}</div>}\r\n    </div>;\n};", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12h.01', key: 'nlz23k' }],\n  ['path', { d: 'M3 18h.01', key: '1tta3j' }],\n  ['path', { d: 'M3 6h.01', key: '1rqtza' }],\n  ['path', { d: 'M8 12h13', key: '1za7za' }],\n  ['path', { d: 'M8 18h13', key: '1lx6n3' }],\n  ['path', { d: 'M8 6h13', key: 'ik3vkj' }],\n];\n\n/**\n * @component @name List\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmguMDEiIC8+CiAgPHBhdGggZD0iTTMgMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik0zIDZoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEyaDEzIiAvPgogIDxwYXRoIGQ9Ik04IDE4aDEzIiAvPgogIDxwYXRoIGQ9Ik04IDZoMTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/list\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst List = createLucideIcon('List', __iconNode);\n\nexport default List;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 2v8l3-3 3 3V2', key: 'sqw3rj' }],\n  [\n    'path',\n    {\n      d: 'M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20',\n      key: 'k3hazp',\n    },\n  ],\n];\n\n/**\n * @component @name BookMarked\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMnY4bDMtMyAzIDNWMiIgLz4KICA8cGF0aCBkPSJNNCAxOS41di0xNUEyLjUgMi41IDAgMCAxIDYuNSAySDE5YTEgMSAwIDAgMSAxIDF2MThhMSAxIDAgMCAxLTEgMUg2LjVhMSAxIDAgMCAxIDAtNUgyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/book-marked\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookMarked = createLucideIcon('BookMarked', __iconNode);\n\nexport default BookMarked;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M6 9H4.5a2.5 2.5 0 0 1 0-5H6', key: '17hqa7' }],\n  ['path', { d: 'M18 9h1.5a2.5 2.5 0 0 0 0-5H18', key: 'lmptdp' }],\n  ['path', { d: 'M4 22h16', key: '57wxv0' }],\n  ['path', { d: 'M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22', key: '1nw9bq' }],\n  ['path', { d: 'M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22', key: '1np0yb' }],\n  ['path', { d: 'M18 2H6v7a6 6 0 0 0 12 0V2Z', key: 'u46fv3' }],\n];\n\n/**\n * @component @name Trophy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/trophy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trophy = createLucideIcon('Trophy', __iconNode);\n\nexport default Trophy;\n", "import React from 'react';\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';\nimport { BookOpen, Book, List, Play, FileText, BookMarked, Target, Trophy } from 'lucide-react';\nimport { TableOfContentsProps } from '@/types/lms';\nimport { TreeNode } from './tree-node';\nexport const TableOfContents: React.FC<TableOfContentsProps> = ({\n  course,\n  onNavigate,\n  expandedModules,\n  expandedChapters,\n  onToggleModule,\n  onToggleChapter,\n  currentModuleIndex\n}) => {\n  return <Card className='sticky top-4 h-fit w-full max-w-full' data-sentry-element=\"Card\" data-sentry-component=\"TableOfContents\" data-sentry-source-file=\"table-of-contents.tsx\">\r\n      <CardHeader className='pb-4' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"table-of-contents.tsx\">\r\n        <CardTitle className='flex items-center text-xl' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"table-of-contents.tsx\">\r\n          <List className='mr-3 h-6 w-6' data-sentry-element=\"List\" data-sentry-source-file=\"table-of-contents.tsx\" />\r\n          Table of Contents\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className='max-h-[calc(100vh-12rem)] space-y-1 overflow-y-auto overflow-x-hidden' data-sentry-element=\"CardContent\" data-sentry-source-file=\"table-of-contents.tsx\">\r\n        {course.modules.map((module, index) => {\n        const moduleCompleted = module.chapters.every(ch => ch.contents.every(c => c.isCompleted) && ch.quiz.isPassed) && module.moduleQuiz.isPassed;\n        const isCurrentModule = currentModuleIndex === index;\n        return <TreeNode key={module.id} id={module.id} title={module.title} icon={<BookOpen className='h-5 w-5' />} isUnlocked={module.isUnlocked} isCompleted={moduleCompleted} level={0} hasChildren={true} isExpanded={expandedModules[module.id]} onToggle={() => onToggleModule(module.id)} isActive={isCurrentModule}>\r\n              {module.chapters.map(chapter => {\n            const chapterCompleted = chapter.contents.every(c => c.isCompleted) && chapter.quiz.isPassed;\n            return <TreeNode key={chapter.id} id={chapter.id} title={chapter.title} icon={<Book className='h-4 w-4' />} isUnlocked={chapter.isUnlocked} isCompleted={chapterCompleted} level={1} hasChildren={true} isExpanded={expandedChapters[chapter.id]} onToggle={() => onToggleChapter(chapter.id)}>\r\n                    {chapter.contents.map(content => <TreeNode key={content.id} id={content.id} title={content.title} icon={content.type === 'video' ? <Play className='h-4 w-4 text-red-500' /> : content.type === 'pdf' ? <FileText className='h-4 w-4 text-red-600' /> : content.type === 'zoom-recording' ? <Play className='h-4 w-4 text-blue-500' /> : <BookMarked className='h-4 w-4 text-blue-500' />} isUnlocked={chapter.isUnlocked} isCompleted={content.isCompleted} level={2} onClick={() => onNavigate(module.id, chapter.id, content.id)} />)}\r\n                    <TreeNode key={`${chapter.id}-quiz`} id={`${chapter.id}-quiz`} title='Chapter Quiz' icon={<Target className='h-4 w-4' />} isUnlocked={chapter.contents.every(c => c.isCompleted)} isCompleted={chapter.quiz.isPassed} level={2} onClick={() => onNavigate(module.id, chapter.id)} />\r\n                  </TreeNode>;\n          })}\r\n              <TreeNode key={`${module.id}-quiz`} id={`${module.id}-quiz`} title='Module Quiz' icon={<BookMarked className='h-4 w-4' />} isUnlocked={module.chapters.every(ch => ch.contents.every(c => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts))} isCompleted={module.moduleQuiz.isPassed} level={1} onClick={() => onNavigate(module.id)} />\r\n            </TreeNode>;\n      })}\r\n\r\n        <TreeNode key='final-exam' id='final-exam' title='Final Exam' icon={<Trophy className='h-5 w-5' />} isUnlocked={course.modules.every(m => m.chapters.every(ch => ch.contents.every(c => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)) && (m.moduleQuiz.isPassed || m.moduleQuiz.attempts >= m.moduleQuiz.maxAttempts))} isCompleted={course.finalExam.isPassed} level={0} data-sentry-element=\"TreeNode\" data-sentry-source-file=\"table-of-contents.tsx\" />\r\n      </CardContent>\r\n    </Card>;\n};", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '10', x2: '14', y1: '2', y2: '2', key: '14vaq8' }],\n  ['line', { x1: '12', x2: '15', y1: '14', y2: '11', key: '17fdiu' }],\n  ['circle', { cx: '12', cy: '14', r: '8', key: '1e1u0o' }],\n];\n\n/**\n * @component @name Timer\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTAiIHgyPSIxNCIgeTE9IjIiIHkyPSIyIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjE1IiB5MT0iMTQiIHkyPSIxMSIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjE0IiByPSI4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/timer\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Timer = createLucideIcon('Timer', __iconNode);\n\nexport default Timer;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 3h6v6', key: '1q9fwt' }],\n  ['path', { d: 'M10 14 21 3', key: 'gplh6r' }],\n  ['path', { d: 'M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6', key: 'a6xqqp' }],\n];\n\n/**\n * @component @name ExternalLink\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgM2g2djYiIC8+CiAgPHBhdGggZD0iTTEwIDE0IDIxIDMiIC8+CiAgPHBhdGggZD0iTTE4IDEzdjZhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWOGEyIDIgMCAwIDEgMi0yaDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/external-link\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ExternalLink = createLucideIcon('ExternalLink', __iconNode);\n\nexport default ExternalLink;\n", "import React from 'react';\nimport ReactMarkdown from 'react-markdown';\nimport remarkGfm from 'remark-gfm';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Play, FileText, BookMarked, CheckCircle2, ChevronUp, ChevronDown, Timer, Download, Eye, ExternalLink, Image } from 'lucide-react';\nimport { ContentItemProps } from '@/types/lms';\nexport const ContentItem: React.FC<ContentItemProps> = ({\n  content,\n  onToggleComplete,\n  isExpanded,\n  onToggleExpand\n}) => {\n  // Console log for debugging content object\n  console.log('ContentItem received content:', content);\n  const extractFirstMarkdownHeader = (markdown: string): string | null => {\n    const lines = markdown.split('\\n');\n    for (const line of lines) {\n      const match = line.match(/^#{1,4}\\s+(.*)$/);\n      if (match && match[1]) {\n        return match[1].trim();\n      }\n    }\n    return null;\n  };\n  const displayTitle = content.title || (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string' ? extractFirstMarkdownHeader(content.content.value) : null) || 'No Title';\n  const handleDownloadMarkdownAsPDF = () => {\n    // Create a new window for printing\n    const printWindow = window.open('', '_blank');\n    if (!printWindow) return;\n\n    // Create HTML content for the markdown\n    const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n        <head>\n          <title>${content.title}</title>\n          <style>\n            @media print {\n              @page {\n                size: A4;\n                margin: 2cm;\n              }\n              body {\n                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n                line-height: 1.6;\n                color: #333;\n              }\n            }\n            body {\n              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n              line-height: 1.6;\n              color: #333;\n              max-width: 800px;\n              margin: 0 auto;\n              padding: 20px;\n            }\n            h1 { color: #2d3748; font-size: 2em; margin-bottom: 1em; }\n            h2 { color: #4a5568; font-size: 1.5em; margin: 1.5em 0 0.5em; }\n            h3 { color: #4a5568; font-size: 1.25em; margin: 1.2em 0 0.5em; }\n            h4 { color: #718096; font-size: 1.1em; margin: 1em 0 0.5em; }\n            p { margin-bottom: 1em; }\n            ul, ol { margin-bottom: 1em; padding-left: 2em; }\n            li { margin-bottom: 0.25em; }\n            blockquote {\n              border-left: 4px solid #3182ce;\n              background: #ebf8ff;\n              padding: 1em;\n              margin: 1em 0;\n              font-style: italic;\n            }\n            code {\n              background: #f7fafc;\n              padding: 0.2em 0.4em;\n              border-radius: 3px;\n              font-family: 'Courier New', monospace;\n              font-size: 0.9em;\n            }\n            pre {\n              background: #2d3748;\n              color: #f7fafc;\n              padding: 1em;\n              border-radius: 5px;\n              overflow-x: auto;\n              margin: 1em 0;\n            }\n            pre code {\n              background: none;\n              padding: 0;\n              color: inherit;\n            }\n            table {\n              border-collapse: collapse;\n              width: 100%;\n              margin: 1em 0;\n            }\n            th, td {\n              border: 1px solid #e2e8f0;\n              padding: 0.5em;\n              text-align: left;\n            }\n            th {\n              background: #f7fafc;\n              font-weight: 600;\n            }\n            hr {\n              border: none;\n              height: 1px;\n              background: #e2e8f0;\n              margin: 2em 0;\n            }\n            strong { font-weight: 600; }\n            em { font-style: italic; }\n          </style>\n        </head>\n        <body>\n          <h1>${content.title}</h1>\n          <div id=\"markdown-content\"></div>\n        </body>\n      </html>\n    `;\n    printWindow.document.write(htmlContent);\n    printWindow.document.close();\n\n    // Convert markdown to HTML and insert it\n    const markdownDiv = printWindow.document.getElementById('markdown-content');\n    if (markdownDiv) {\n      // Simple markdown to HTML conversion for basic formatting\n      let htmlText = '';\n\n      // Check if content.content is a string, ContentBlock object, or ContentBlock array\n      if (typeof content.content === 'string') {\n        htmlText = content.content;\n      } else if (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') {\n        htmlText = content.content.value;\n      } else if (Array.isArray(content.content)) {\n        // Convert ContentBlock array to string\n        htmlText = content.content.map(block => block.type === 'text' ? block.value : '').join('');\n      }\n\n      // Headers\n      htmlText = htmlText.replace(/^### (.*$)/gim, '<h3>$1</h3>');\n      htmlText = htmlText.replace(/^## (.*$)/gim, '<h2>$1</h2>');\n      htmlText = htmlText.replace(/^# (.*$)/gim, '<h1>$1</h1>');\n\n      // Bold and italic\n      htmlText = htmlText.replace(/\\*\\*(.*)\\*\\*/gim, '<strong>$1</strong>');\n      htmlText = htmlText.replace(/\\*(.*)\\*/gim, '<em>$1</em>');\n\n      // Lists\n      htmlText = htmlText.replace(/^\\* (.*$)/gim, '<li>$1</li>');\n      htmlText = htmlText.replace(/(<li>.*<\\/li>)/gim, '<ul>$1</ul>');\n      htmlText = htmlText.replace(/^\\d+\\. (.*$)/gim, '<li>$1</li>');\n\n      // Line breaks to paragraphs\n      htmlText = htmlText.replace(/\\n\\n/g, '</p><p>');\n      htmlText = '<p>' + htmlText + '</p>';\n\n      // Clean up empty paragraphs\n      htmlText = htmlText.replace(/<p><\\/p>/g, '');\n      markdownDiv.innerHTML = htmlText;\n    }\n\n    // Wait for content to load then print\n    setTimeout(() => {\n      printWindow.focus();\n      printWindow.print();\n      printWindow.close();\n    }, 250);\n  };\n  const getContentIcon = () => {\n    switch (content.type) {\n      case 'video':\n        return <Play className='h-4 w-4 text-red-500' />;\n      case 'pdf':\n        return <FileText className='h-4 w-4 text-red-600' />;\n      case 'zoom-recording':\n        return <Play className='h-4 w-4 text-blue-500' />;\n      case 'image':\n        return <Image className='h-4 w-4 text-green-500' />;\n      default:\n        return <BookMarked className='h-4 w-4 text-blue-500' />;\n    }\n  };\n  const getContentTypeLabel = () => {\n    switch (content.type) {\n      case 'video':\n        return 'Video';\n      case 'pdf':\n        return 'PDF Document';\n      case 'zoom-recording':\n        return 'Zoom Recording';\n      case 'image':\n        return 'Image';\n      default:\n        return 'Reading Material';\n    }\n  };\n  return <Card id={`content-${content.id}`} className='my-2 ml-6 border-l-4 border-l-blue-200 scroll-mt-20' data-sentry-element=\"Card\" data-sentry-component=\"ContentItem\" data-sentry-source-file=\"content-item.tsx\">\r\n      <CardContent className='py-3' data-sentry-element=\"CardContent\" data-sentry-source-file=\"content-item.tsx\">\r\n        <div className='flex flex-col'>\r\n          <div className='flex cursor-pointer items-center justify-between' onClick={onToggleExpand}>\r\n            <div className='flex flex-1 items-center space-x-3'>\r\n              {getContentIcon()}\r\n              <div className='flex-1'>\r\n                <span className='text-sm font-medium'>{displayTitle}</span>\r\n                <div className='mt-1 flex items-center space-x-2'>\r\n                  <Badge variant='outline' className='text-xs' data-sentry-element=\"Badge\" data-sentry-source-file=\"content-item.tsx\">\r\n                    {getContentTypeLabel()}\r\n                  </Badge>\r\n                  {content.duration && <Badge variant='outline' className='text-xs'>\r\n                      <Timer className='mr-1 h-3 w-3' />\r\n                      {content.duration} min\r\n                    </Badge>}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className='flex items-center space-x-2'>\r\n              {isExpanded ? <ChevronUp className='h-4 w-4 text-gray-400' /> : <ChevronDown className='h-4 w-4 text-gray-400' />}\r\n            </div>\r\n          </div>\r\n\r\n          {isExpanded && <div className='mt-4 border-t pt-4 pl-7'>\r\n              {content.type === 'text' ? <div className='space-y-4'>\r\n                  <div className='prose prose-sm max-w-none text-gray-700'>\r\n                    <ReactMarkdown remarkPlugins={[remarkGfm]} components={{\n                h1: ({\n                  node,\n                  ...props\n                }) => <h1 className='mb-4 text-2xl font-bold text-gray-900' {...props} />,\n                h2: ({\n                  node,\n                  ...props\n                }) => <h2 className='mb-3 text-xl font-semibold text-gray-800' {...props} />,\n                h3: ({\n                  node,\n                  ...props\n                }) => <h3 className='mb-2 text-lg font-semibold text-gray-800' {...props} />,\n                h4: ({\n                  node,\n                  ...props\n                }) => <h4 className='mb-2 text-base font-semibold text-gray-700' {...props} />,\n                p: ({\n                  node,\n                  ...props\n                }) => <p className='mb-3 leading-relaxed' {...props} />,\n                ul: ({\n                  node,\n                  ...props\n                }) => <ul className='mb-3 ml-4 list-disc' {...props} />,\n                ol: ({\n                  node,\n                  ...props\n                }) => <ol className='mb-3 ml-4 list-decimal' {...props} />,\n                li: ({\n                  node,\n                  ...props\n                }) => <li className='mb-1' {...props} />,\n                blockquote: ({\n                  node,\n                  ...props\n                }) => <blockquote className='mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic' {...props} />,\n                code: ({\n                  node,\n                  className,\n                  children,\n                  ...props\n                }) => {\n                  const match = /language-(\\w+)/.exec(className || '');\n                  const isInline = !match;\n                  return isInline ? <code className='rounded bg-gray-100 px-1 py-0.5 font-mono text-sm' {...props}>\r\n                              {children}\r\n                            </code> : <code className='block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100' {...props}>\r\n                              {children}\r\n                            </code>;\n                },\n                pre: ({\n                  node,\n                  ...props\n                }) => <pre className='mb-4' {...props} />,\n                table: ({\n                  node,\n                  ...props\n                }) => <div className='mb-4 overflow-x-auto'>\r\n                            <table className='min-w-full rounded border border-gray-200' {...props} />\r\n                          </div>,\n                thead: ({\n                  node,\n                  ...props\n                }) => <thead className='bg-gray-50' {...props} />,\n                th: ({\n                  node,\n                  ...props\n                }) => <th className='border border-gray-200 px-3 py-2 text-left font-semibold' {...props} />,\n                td: ({\n                  node,\n                  ...props\n                }) => <td className='border border-gray-200 px-3 py-2' {...props} />,\n                hr: ({\n                  node,\n                  ...props\n                }) => <hr className='my-6 border-gray-300' {...props} />,\n                strong: ({\n                  node,\n                  ...props\n                }) => <strong className='font-semibold text-gray-900' {...props} />,\n                em: ({\n                  node,\n                  ...props\n                }) => <em className='italic' {...props} />\n              }}>\r\n                      {typeof content.content === 'string' ? content.content : typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string' ? content.content.value : Array.isArray(content.content) ? content.content.map(block => block.type === 'text' ? block.value : '').join('') : ''}\r\n                    </ReactMarkdown>\r\n                  </div>\r\n                  <Button size='sm' variant='outline' className='border-blue-200 text-blue-600 hover:bg-blue-50' onClick={handleDownloadMarkdownAsPDF}>\r\n                    <Download className='mr-2 h-4 w-4' />\r\n                    Download as PDF\r\n                  </Button>\r\n                </div> : content.type === 'pdf' ? <div className='space-y-4'>\r\n                  <iframe src={`${typeof content.content === 'string' ? content.content : typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string' ? content.content.value : ''}#toolbar=0&navpanes=0`} className='w-full h-96 rounded border' title={content.title} />\r\n                  <div className='flex space-x-2'>\r\n                    <Button size='sm' variant='outline' className='border-blue-200 text-blue-600 hover:bg-blue-50' onClick={() => {\n                const pdfUrl = typeof content.content === 'string' ? content.content : typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string' ? content.content.value : '';\n                if (pdfUrl) window.open(pdfUrl, '_blank');\n              }}>\r\n                      <Eye className='mr-2 h-4 w-4' />\r\n                      Open in New Tab\r\n                    </Button>\r\n                    <Button size='sm' variant='outline' className='text-gray-600 hover:bg-gray-50' onClick={() => {\n                const pdfUrl = typeof content.content === 'string' ? content.content : typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string' ? content.content.value : '';\n                if (pdfUrl) {\n                  const link = document.createElement('a');\n                  link.href = pdfUrl;\n                  link.download = content.title || 'document.pdf';\n                  document.body.appendChild(link);\n                  link.click();\n                  document.body.removeChild(link);\n                }\n              }}>\r\n                      <Download className='mr-2 h-4 w-4' />\r\n                      Download\r\n                    </Button>\r\n                  </div>\r\n                </div> : content.type === 'image' ? <div className='space-y-4'>\r\n                  <img src={typeof content.content === 'string' ? content.content : typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string' ? content.content.value : ''} alt={content.title || 'Image'} className='max-w-full h-auto rounded border' />\r\n                  <div className='flex space-x-2'>\r\n                    <Button size='sm' variant='outline' className='border-blue-200 text-blue-600 hover:bg-blue-50' onClick={() => {\n                const imageUrl = typeof content.content === 'string' ? content.content : typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string' ? content.content.value : '';\n                if (imageUrl) window.open(imageUrl, '_blank');\n              }}>\r\n                      <ExternalLink className='mr-2 h-4 w-4' />\r\n                      Open in New Tab\r\n                    </Button>\r\n                    <Button size='sm' variant='outline' className='text-gray-600 hover:bg-gray-50' onClick={() => {\n                const imageUrl = typeof content.content === 'string' ? content.content : typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string' ? content.content.value : '';\n                if (imageUrl) {\n                  const link = document.createElement('a');\n                  link.href = imageUrl;\n                  link.download = content.title || 'image.jpg';\n                  document.body.appendChild(link);\n                  link.click();\n                  document.body.removeChild(link);\n                }\n              }}>\r\n                      <Download className='mr-2 h-4 w-4' />\r\n                      Download\r\n                    </Button>\r\n                  </div>\r\n                </div> : content.type === 'video' || content.type === 'zoom-recording' ? <div className='space-y-4'>\r\n                  <div className='aspect-video w-full overflow-hidden rounded-lg bg-gray-100'>\r\n                    {(() => {\n                const videoUrl = typeof content.content === 'string' ? content.content : typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string' ? content.content.value : '';\n                if (!videoUrl) {\n                  return <div className='flex h-full w-full items-center justify-center text-center text-gray-500'>\r\n                            No video URL provided.\r\n                          </div>;\n                }\n                if (videoUrl.includes('youtube.com/watch?v=') || videoUrl.includes('youtu.be/')) {\n                  const youtubeId = videoUrl.split('v=')[1]?.split('&')[0] || videoUrl.split('/').pop();\n                  return <iframe className='h-full w-full' src={`https://www.youtube.com/embed/${youtubeId}`} frameBorder='0' allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture' allowFullScreen title={content.title}></iframe>;\n                } else if (videoUrl.includes('vimeo.com/')) {\n                  const vimeoId = videoUrl.split('/').pop();\n                  return <iframe className='h-full w-full' src={`https://player.vimeo.com/video/${vimeoId}`} frameBorder='0' allow='autoplay; fullscreen; picture-in-picture' allowFullScreen title={content.title}></iframe>;\n                } else {\n                  // Generic video tag for other direct video links\n                  return <video controls className='h-full w-full' src={videoUrl} title={content.title}>\r\n                            Your browser does not support the video tag.\r\n                          </video>;\n                }\n              })()}\r\n                  </div>\r\n                  <div className='flex space-x-2'>\r\n                    <Button size='sm' variant='outline' className='border-blue-200 text-blue-600 hover:bg-blue-50' onClick={() => {\n                const videoUrl = typeof content.content === 'string' ? content.content : typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string' ? content.content.value : '';\n                if (videoUrl) window.open(videoUrl, '_blank');\n              }}>\r\n                      <ExternalLink className='mr-2 h-4 w-4' />\r\n                      Open in New Tab\r\n                    </Button>\r\n                  </div>\r\n                </div> : <div className='space-y-4'>\r\n                  <div className='flex aspect-video items-center justify-center rounded-lg bg-gray-100'>\r\n                    <div className='text-center'>\r\n                      <Play className='mx-auto mb-2 h-12 w-12 text-gray-400' />\r\n                      <p className='text-sm text-gray-500'>\r\n                        Unsupported Media Type or Invalid Content.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>}\r\n              \r\n              {/* Mark as Complete Button - Moved to bottom */}\r\n              <div className='mt-4 pt-4 border-t'>\r\n                <Button size='sm' variant={content.isCompleted ? 'default' : 'outline'} className={`min-w-[120px] ${content.isCompleted ? 'bg-green-600 text-white hover:bg-green-700' : 'text-gray-600 hover:bg-gray-50'}`} onClick={e => {\n              e.stopPropagation();\n              onToggleComplete();\n            }}>\r\n                  <CheckCircle2 className='mr-2 h-4 w-4' />\r\n                  {content.isCompleted ? 'Completed' : 'Mark Complete'}\r\n                </Button>\r\n              </div>\r\n            </div>}\r\n        </div>\r\n      </CardContent>\r\n    </Card>;\n};", "import React from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Target, BookMarked, Trophy, Clock, CheckCircle } from 'lucide-react';\nimport { QuizCardProps } from '@/types/lms';\nexport const QuizCard: React.FC<QuizCardProps> = ({\n  quiz,\n  isUnlocked,\n  onStartQuiz\n}) => {\n  const getQuizTypeColor = () => {\n    switch (quiz.type) {\n      case 'chapter':\n        return 'bg-blue-100 text-blue-700 border-blue-200';\n      case 'module':\n        return 'bg-purple-100 text-purple-700 border-purple-200';\n      case 'final':\n        return 'bg-red-100 text-red-700 border-red-200';\n      default:\n        return 'bg-gray-100 text-gray-700 border-gray-200';\n    }\n  };\n  const getQuizIcon = () => {\n    switch (quiz.type) {\n      case 'chapter':\n        return <Target className='h-4 w-4' />;\n      case 'module':\n        return <BookMarked className='h-4 w-4' />;\n      case 'final':\n        return <Trophy className='h-4 w-4' />;\n      default:\n        return <Target className='h-4 w-4' />;\n    }\n  };\n  return <Card className={`my-3 ml-6 border-2 ${!isUnlocked ? 'opacity-50' : ''}`} data-sentry-element=\"Card\" data-sentry-component=\"QuizCard\" data-sentry-source-file=\"quiz-card.tsx\">\r\n      <CardContent className='p-4' data-sentry-element=\"CardContent\" data-sentry-source-file=\"quiz-card.tsx\">\r\n        <div className='flex items-center justify-between'>\r\n          <div className='flex items-center space-x-3'>\r\n            <div className={`rounded-lg p-2 ${getQuizTypeColor()}`}>\r\n              {getQuizIcon()}\r\n            </div>\r\n            <div>\r\n              <h4 className='font-medium'>{quiz.title}</h4>\r\n              <div className='mt-1 flex items-center space-x-3'>\r\n                <span className='text-sm text-gray-600'>\r\n                  Min. Score: {quiz.minimumScore}%\r\n                </span>\r\n                {quiz.timeLimit && <span className='text-sm text-gray-600'>\r\n                    <Clock className='mr-1 inline h-3 w-3' />\r\n                    {quiz.timeLimit} min\r\n                  </span>}\r\n                <span className='text-sm text-gray-600'>\r\n                  Attempts: {quiz.attempts}/{quiz.maxAttempts}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className='flex items-center space-x-2'>\r\n            {quiz.isPassed && <Badge className='bg-green-100 text-green-700'>\r\n                <CheckCircle className='mr-1 h-3 w-3' />\r\n                Passed ({quiz.lastScore}%)\r\n              </Badge>}\r\n            <Button size='sm' disabled={!isUnlocked || quiz.attempts >= quiz.maxAttempts} onClick={onStartQuiz} variant={quiz.isPassed ? 'outline' : 'default'} data-sentry-element=\"Button\" data-sentry-source-file=\"quiz-card.tsx\">\r\n              {quiz.attempts === 0 ? 'Start Quiz' : quiz.isPassed ? 'Retake' : 'Continue'}\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>;\n};", "import React, { useState } from 'react';\nimport { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Clock } from 'lucide-react';\nimport { QuizModalProps } from '@/types/lms';\nexport const QuizModal: React.FC<QuizModalProps> = ({\n  quiz,\n  isOpen,\n  onClose,\n  onComplete\n}) => {\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState<{\n    [key: string]: any;\n  }>({});\n  const [timeLeft, setTimeLeft] = useState(quiz.timeLimit ? quiz.timeLimit * 60 : null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  React.useEffect(() => {\n    if (isOpen && timeLeft !== null && timeLeft > 0) {\n      const timer = setInterval(() => {\n        setTimeLeft(prev => {\n          if (prev === null || prev <= 1) {\n            handleSubmitQuiz();\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n      return () => clearInterval(timer);\n    }\n  }, [isOpen, timeLeft]);\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  const handleAnswerChange = (questionId: string, answer: any) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n  const handleSubmitQuiz = () => {\n    if (isSubmitting) return;\n    setIsSubmitting(true);\n\n    // Calculate score\n    let correctAnswers = 0;\n    quiz.questions.forEach(question => {\n      const userAnswer = answers[question.id];\n      if (question.type === 'multiple_choice' || question.type === 'multiple-choice') {\n        // Check if the selected index matches the correct answer\n        if (userAnswer === question.correctAnswer) {\n          correctAnswers++;\n        }\n        // Also check for the complex format with isCorrect\n        else {\n          const selectedOption = question.options?.[userAnswer as number];\n          if (selectedOption && typeof selectedOption === 'object' && selectedOption.isCorrect) {\n            correctAnswers++;\n          }\n        }\n      } else if (question.type === 'true_false' || question.type === 'true-false') {\n        // Compare with correctAnswer for true-false questions\n        if (userAnswer === question.correctAnswer) {\n          correctAnswers++;\n        }\n      } else if (question.type === 'essay') {\n        // For essay, simply check if an answer was provided.\n        // A more complex grading logic would be needed for actual correctness.\n        if (userAnswer && userAnswer.trim() !== '') {\n          correctAnswers++;\n        }\n      }\n    });\n    const score = Math.round(correctAnswers / quiz.questions.length * 100);\n    setTimeout(() => {\n      onComplete(score);\n      setIsSubmitting(false);\n      setAnswers({});\n      setCurrentQuestion(0);\n      if (quiz.timeLimit) setTimeLeft(quiz.timeLimit * 60);\n    }, 1000);\n  };\n  if (!isOpen || quiz.questions.length === 0) return null;\n  const currentQ = quiz.questions[currentQuestion];\n  const isLastQuestion = currentQuestion === quiz.questions.length - 1;\n  const canProceed = answers[currentQ.id] !== undefined;\n  return <Dialog open={isOpen} onOpenChange={onClose} data-sentry-element=\"Dialog\" data-sentry-component=\"QuizModal\" data-sentry-source-file=\"quiz-modal.tsx\">\r\n      <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto p-6' data-sentry-element=\"DialogContent\" data-sentry-source-file=\"quiz-modal.tsx\">\r\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"quiz-modal.tsx\">\r\n          <DialogTitle className='flex items-center justify-between' data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"quiz-modal.tsx\">\r\n            <span>{quiz.title}</span>\r\n            {timeLeft !== null && <Badge variant='outline' className='border-red-200 text-red-600'>\r\n                <Clock className='mr-1 h-4 w-4' />\r\n                {formatTime(timeLeft)}\r\n              </Badge>}\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className='space-y-6'>\r\n          {/* Progress Bar */}\r\n          <div className='space-y-2'>\r\n            <div className='flex justify-between text-sm text-gray-600'>\r\n              <span>\r\n                Question {currentQuestion + 1} of {quiz.questions.length}\r\n              </span>\r\n              <span>\r\n                {Math.round((currentQuestion + 1) / quiz.questions.length * 100)}\r\n                % Complete\r\n              </span>\r\n            </div>\r\n            <Progress value={(currentQuestion + 1) / quiz.questions.length * 100} data-sentry-element=\"Progress\" data-sentry-source-file=\"quiz-modal.tsx\" />\r\n          </div>\r\n\r\n          {/* Question */}\r\n          <Card data-sentry-element=\"Card\" data-sentry-source-file=\"quiz-modal.tsx\">\r\n            <CardContent className='p-6' data-sentry-element=\"CardContent\" data-sentry-source-file=\"quiz-modal.tsx\">\r\n              <h3 className='mb-4 text-lg font-medium'>\r\n                {typeof currentQ.question === 'string' ? currentQ.question : Array.isArray(currentQ.question) ? currentQ.question.map((block, index) => <React.Fragment key={index}>\r\n                      {block.type === 'text' && <span>{block.value}</span>}\r\n                      {block.type === 'image' && block.value && <img src={block.value} alt={`Question image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />}\r\n                    </React.Fragment>) : <span>{String(currentQ.question)}</span>}\r\n              </h3>\r\n\r\n              {(currentQ.type === 'multiple_choice' || currentQ.type === 'multiple-choice') && currentQ.options && <div className='space-y-3'>\r\n                  {currentQ.options.map((option, index) => <label key={index} className='flex cursor-pointer items-center space-x-3'>\r\n                      <input type='radio' name={currentQ.id} value={index} checked={answers[currentQ.id] === index} onChange={() => handleAnswerChange(currentQ.id, index)} className='h-4 w-4 text-blue-600' />\r\n                      <span>\r\n                        {typeof option === 'string' ? option : Array.isArray(option.content) ? option.content.map((block, optionBlockIndex) => <React.Fragment key={optionBlockIndex}>\r\n                              {block.type === 'text' && <span>{block.value}</span>}\r\n                              {block.type === 'image' && block.value && <img src={block.value} alt={`Option image ${optionBlockIndex}`} className=\"inline-block max-h-8 object-contain ml-1\" />}\r\n                            </React.Fragment>) : <span>{String(option.content || option)}</span>}\r\n                      </span>\r\n                    </label>)}\r\n                </div>}\r\n\r\n              {(currentQ.type === 'true_false' || currentQ.type === 'true-false') && <div className='space-y-3'>\r\n                  <label className='flex cursor-pointer items-center space-x-3'>\r\n                    <input type='radio' name={currentQ.id} value='true' checked={answers[currentQ.id] === 'true'} onChange={() => handleAnswerChange(currentQ.id, 'true')} className='h-4 w-4 text-blue-600' />\r\n                    <span>True</span>\r\n                  </label>\r\n                  <label className='flex cursor-pointer items-center space-x-3'>\r\n                    <input type='radio' name={currentQ.id} value='false' checked={answers[currentQ.id] === 'false'} onChange={() => handleAnswerChange(currentQ.id, 'false')} className='h-4 w-4 text-blue-600' />\r\n                    <span>False</span>\r\n                  </label>\r\n                </div>}\r\n\r\n              {currentQ.type === 'essay' && <textarea className='w-full resize-none rounded-lg border p-3 focus:border-transparent focus:ring-2 focus:ring-blue-500' rows={6} placeholder='Type your answer here...' value={answers[currentQ.id] || ''} onChange={e => handleAnswerChange(currentQ.id, e.target.value)} />}\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Navigation */}\r\n          <div className='flex justify-between'>\r\n            <Button variant='outline' onClick={() => setCurrentQuestion(prev => Math.max(0, prev - 1))} disabled={currentQuestion === 0 || isSubmitting} data-sentry-element=\"Button\" data-sentry-source-file=\"quiz-modal.tsx\">\r\n              Previous\r\n            </Button>\r\n\r\n            <div className='flex space-x-2'>\r\n              {!isLastQuestion ? <Button onClick={() => setCurrentQuestion(prev => prev + 1)} disabled={!canProceed || isSubmitting}>\r\n                  Next\r\n                </Button> : <Button onClick={handleSubmitQuiz} disabled={!canProceed || isSubmitting} className='bg-green-600 hover:bg-green-700'>\r\n                  {isSubmitting ? 'Submitting...' : 'Submit Quiz'}\r\n                </Button>}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>;\n};", "import React from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Progress } from '@/components/ui/progress';\nimport { Book, Lock, ChevronUp, ChevronDown } from 'lucide-react';\nimport { ChapterSectionProps } from '@/types/lms';\nimport { ContentItem } from './content-item';\nimport { QuizCard } from './quiz-card';\nexport const ChapterSection: React.FC<ChapterSectionProps> = ({\n  chapter,\n  expandedContents,\n  onToggleContent,\n  onToggleContentComplete,\n  onStartQuiz,\n  isExpanded,\n  onToggleExpanded\n}) => {\n  const completedContents = chapter.contents.filter(c => c.isCompleted).length;\n  const totalContents = chapter.contents.length;\n  const progress = totalContents > 0 ? completedContents / totalContents * 100 : 0;\n  return <Card id={`chapter-${chapter.id}`} className={`my-3 border-l-4 scroll-mt-20 ${chapter.isUnlocked ? 'border-l-green-400' : 'border-l-gray-300'} ${!chapter.isUnlocked ? 'opacity-60' : ''}`} data-sentry-element=\"Card\" data-sentry-component=\"ChapterSection\" data-sentry-source-file=\"chapter-section.tsx\">\r\n      <CardContent className='p-4' data-sentry-element=\"CardContent\" data-sentry-source-file=\"chapter-section.tsx\">\r\n        <div className='flex flex-col'>\r\n          <div className='flex cursor-pointer items-center justify-between' onClick={() => chapter.isUnlocked && onToggleExpanded()}>\r\n            <div className='flex flex-1 items-center space-x-3'>\r\n              <div className={`rounded-lg p-2 ${chapter.isUnlocked ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-500'}`}>\r\n                <Book className='h-5 w-5' data-sentry-element=\"Book\" data-sentry-source-file=\"chapter-section.tsx\" />\r\n              </div>\r\n              <div className='flex-1'>\r\n                <div className='flex items-center space-x-2'>\r\n                  <span className='font-medium'>{chapter.title}</span>\r\n                  {!chapter.isUnlocked && <Lock className='h-4 w-4 text-gray-400' />}\r\n                </div>\r\n                <div className='mt-2 max-w-md'>\r\n                  <Progress value={progress} className='h-2' data-sentry-element=\"Progress\" data-sentry-source-file=\"chapter-section.tsx\" />\r\n                </div>\r\n                <p className='mt-1 text-sm text-gray-500'>\r\n                  {completedContents}/{totalContents} contents completed\r\n                  {chapter.quiz.isPassed && ' • Quiz passed'}\r\n                </p>\r\n              </div>\r\n            </div>\r\n            {chapter.isUnlocked && (isExpanded ? <ChevronUp className='h-5 w-5 text-gray-400' /> : <ChevronDown className='h-5 w-5 text-gray-400' />)}\r\n          </div>\r\n\r\n          {isExpanded && chapter.isUnlocked && <div className='mt-4 border-t pt-4'>\r\n              {/* Chapter Contents */}\r\n              <div className='space-y-2'>\r\n                {chapter.contents.map(content => <ContentItem key={content.id} content={content} onToggleComplete={() => onToggleContentComplete(content.id)} isExpanded={expandedContents[content.id] || false} onToggleExpand={() => onToggleContent(content.id)} />)}\r\n              </div>\r\n\r\n              {/* Chapter Quiz */}\r\n              <QuizCard quiz={chapter.quiz} isUnlocked={completedContents === totalContents} onStartQuiz={() => onStartQuiz(chapter.quiz.id)} />\r\n            </div>}\r\n        </div>\r\n      </CardContent>\r\n    </Card>;\n};", "import React from 'react';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Progress } from '@/components/ui/progress';\nimport { BookOpen, Lock, ChevronUp, ChevronDown, Maximize, Minimize } from 'lucide-react';\nimport { ModuleSectionProps } from '@/types/lms';\nimport { ChapterSection } from './chapter-section';\nimport { QuizCard } from './quiz-card';\nexport const ModuleSection: React.FC<ModuleSectionProps> = ({\n  module,\n  expandedContents,\n  expandedChapters,\n  onToggleContent,\n  onToggleContentComplete,\n  onStartQuiz,\n  isExpanded,\n  onToggleExpanded,\n  onToggleChapter,\n  onExpandAllChapters,\n  onCollapseAllChapters\n}) => {\n  const totalChapters = module.chapters.length;\n  const completedChapters = module.chapters.filter(ch => ch.contents.every(c => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)).length;\n  const progress = totalChapters > 0 ? completedChapters / totalChapters * 100 : 0;\n\n  // Only check unlocked chapters for expansion state\n  const unlockedChapters = module.chapters.filter(ch => ch.isUnlocked);\n  const allChaptersExpanded = unlockedChapters.length > 0 && unlockedChapters.every(ch => expandedChapters[ch.id] === true);\n  return <Card id={`module-${module.id}`} className={`mb-6 shadow-md scroll-mt-20 ${module.isUnlocked ? 'border-green-200' : 'border-gray-200'}`} data-sentry-element=\"Card\" data-sentry-component=\"ModuleSection\" data-sentry-source-file=\"module-section.tsx\">\r\n      <CardHeader className={`border-b ${!module.isUnlocked ? 'opacity-60' : ''}`} data-sentry-element=\"CardHeader\" data-sentry-source-file=\"module-section.tsx\">\r\n        <div className='flex cursor-pointer items-center justify-between' onClick={() => module.isUnlocked && onToggleExpanded()}>\r\n          <div className='flex flex-1 items-center space-x-4'>\r\n            <div className={`rounded-lg p-3 ${module.isUnlocked ? 'bg-purple-100 text-purple-700' : 'bg-gray-100 text-gray-500'}`}>\r\n              <BookOpen className='h-6 w-6' data-sentry-element=\"BookOpen\" data-sentry-source-file=\"module-section.tsx\" />\r\n            </div>\r\n            <div className='flex-1'>\r\n              <div className='flex items-center space-x-2'>\r\n                <CardTitle className='text-lg' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"module-section.tsx\">{module.title}</CardTitle>\r\n                {!module.isUnlocked && <Lock className='h-4 w-4 text-gray-400' />}\r\n              </div>\r\n              <p className='mt-1 text-gray-600'>{module.description}</p>\r\n              <div className='mt-3 max-w-md'>\r\n                <Progress value={progress} className='h-3' data-sentry-element=\"Progress\" data-sentry-source-file=\"module-section.tsx\" />\r\n              </div>\r\n              <p className='mt-2 text-sm text-gray-500'>\r\n                {completedChapters}/{totalChapters} chapters completed\r\n                {module.moduleQuiz.isPassed && ' • Module quiz passed'}\r\n              </p>\r\n            </div>\r\n          </div>\r\n          {module.isUnlocked && (isExpanded ? <ChevronUp className='h-6 w-6 text-gray-400' /> : <ChevronDown className='h-6 w-6 text-gray-400' />)}\r\n        </div>\r\n      </CardHeader>\r\n\r\n      {isExpanded && module.isUnlocked && <CardContent className='pt-6'>\r\n          {/* Chapter Expand/Collapse Controls */}\r\n\r\n          {/* Module Chapters */}\r\n          <div className='space-y-2'>\r\n            {module.chapters.map(chapter => <ChapterSection key={chapter.id} chapter={chapter} expandedContents={expandedContents} onToggleContent={onToggleContent} onToggleContentComplete={onToggleContentComplete} onStartQuiz={onStartQuiz} isExpanded={expandedChapters[chapter.id] || false} onToggleExpanded={() => onToggleChapter(chapter.id)} />)}\r\n          </div>\r\n\r\n          {/* Module Quiz */}\r\n          <div className='mt-6 border-t pt-4'>\r\n            <h4 className='mb-3 font-medium text-purple-700'>\r\n              Module Assessment\r\n            </h4>\r\n            <QuizCard quiz={module.moduleQuiz} isUnlocked={completedChapters === totalChapters} onStartQuiz={() => onStartQuiz(module.moduleQuiz.id)} />\r\n          </div>\r\n        </CardContent>}\r\n    </Card>;\n};", "import React from 'react';\nimport { Building, Award } from 'lucide-react';\nimport Image from 'next/image'; // Import the Image component\nimport { CertificateTemplateProps } from '@/types/lms';\nexport const CertificateTemplate: React.FC<CertificateTemplateProps> = ({\n  institution,\n  course,\n  studentName,\n  completionDate\n}) => {\n  const primaryColor = institution.certificateTemplate?.primaryColor || '#1e40af';\n  const secondaryColor = institution.certificateTemplate?.secondaryColor || '#f59e0b';\n  return <div className='space-y-6 rounded-lg border-4 p-8 text-center' style={{\n    borderColor: secondaryColor,\n    background: `linear-gradient(to right, ${primaryColor}10, ${secondaryColor}10)`\n  }} data-sentry-component=\"CertificateTemplate\" data-sentry-source-file=\"certificate-template.tsx\">\r\n      <div className='border-b-2 pb-4' style={{\n      borderColor: secondaryColor\n    }}>\r\n        <div className='mb-2 flex items-center justify-center space-x-3'>\r\n          {institution.certificateTemplate?.logoUrl && <Image src={institution.certificateTemplate.logoUrl} alt={institution.name} width={48} // Add the width property\n        height={48} // Add the height property\n        className='h-12 w-12' />}\r\n          <Building className='h-8 w-8' style={{\n          color: primaryColor\n        }} data-sentry-element=\"Building\" data-sentry-source-file=\"certificate-template.tsx\" />\r\n        </div>\r\n        <h2 className='text-3xl font-bold' style={{\n        color: primaryColor\n      }}>\r\n          {institution.name}\r\n        </h2>\r\n        <p style={{\n        color: primaryColor\n      }}>Certificate of Completion</p>\r\n      </div>\r\n\r\n      <div className='space-y-4'>\r\n        <p className='text-lg' style={{\n        color: `${primaryColor}cc`\n      }}>\r\n          This is to certify that\r\n        </p>\r\n        <h3 className='text-2xl font-bold' style={{\n        color: primaryColor\n      }}>\r\n          {studentName}\r\n        </h3>\r\n        <p className='text-lg' style={{\n        color: `${primaryColor}cc`\n      }}>\r\n          has successfully completed the course\r\n        </p>\r\n        <h4 className='text-xl font-semibold' style={{\n        color: primaryColor\n      }}>\r\n          {course.name}\r\n        </h4>\r\n        <p style={{\n        color: `${primaryColor}cc`\n      }}>Course Code: {course.code}</p>\r\n      </div>\r\n\r\n      <div className='flex justify-center space-x-12 py-6'>\r\n        <div className='text-center'>\r\n          <div className='mb-2 h-1 w-32' style={{\n          backgroundColor: secondaryColor\n        }}></div>\r\n          <p className='text-sm' style={{\n          color: `${primaryColor}cc`\n        }}>\r\n            Instructor\r\n          </p>\r\n          <p className='font-medium' style={{\n          color: primaryColor\n        }}>\r\n            {course.instructor}\r\n          </p>\r\n        </div>\r\n        <div className='text-center'>\r\n          <div className='mb-2 h-1 w-32' style={{\n          backgroundColor: secondaryColor\n        }}></div>\r\n          <p className='text-sm' style={{\n          color: `${primaryColor}cc`\n        }}>\r\n            Date of Completion\r\n          </p>\r\n          <p className='font-medium' style={{\n          color: primaryColor\n        }}>\r\n            {completionDate}\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className='border-t-2 pt-4' style={{\n      borderColor: secondaryColor\n    }}>\r\n        <Award className='mx-auto mb-2 h-12 w-12' style={{\n        color: secondaryColor\n      }} data-sentry-element=\"Award\" data-sentry-source-file=\"certificate-template.tsx\" />\r\n        <p className='text-sm' style={{\n        color: `${primaryColor}99`\n      }}>\r\n          Certificate ID: {institution.shortName}-{course.code}-2024-001\r\n        </p>\r\n        {institution.certificateTemplate?.signatoryName && <div className='mt-4'>\r\n            <p className='font-medium' style={{\n          color: primaryColor\n        }}>\r\n              {institution.certificateTemplate.signatoryName}\r\n            </p>\r\n            <p className='text-sm' style={{\n          color: `${primaryColor}cc`\n        }}>\r\n              {institution.certificateTemplate.signatoryTitle}\r\n            </p>\r\n          </div>}\r\n      </div>\r\n    </div>;\n};", "import React, { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { BookMarked, Target, Award, Maximize, Minimize, X, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Course } from '@/types/lms';\nimport { TableOfContents } from '../table-of-contents';\nimport { ModuleSection } from '../module-section';\ninterface CourseTabProps {\n  courseData: Course;\n  expandedModules: {\n    [key: string]: boolean;\n  };\n  expandedChapters: {\n    [key: string]: boolean;\n  };\n  expandedContents: {\n    [key: string]: boolean;\n  };\n  onToggleModule: (moduleId: string) => void;\n  onToggleChapter: (chapterId: string) => void;\n  onToggleContent: (contentId: string) => void;\n  onToggleContentComplete: (contentId: string) => void;\n  onStartQuiz: (quizId: string) => void;\n  onNavigateToSection: (moduleId: string, chapterId?: string, contentId?: string) => void;\n  onExpandAllModules: () => void;\n  onCollapseAllModules: () => void;\n  onExpandAllChaptersInModule: (moduleId: string) => void;\n  onCollapseAllChaptersInModule: (moduleId: string) => void;\n}\nexport const CourseTab: React.FC<CourseTabProps> = ({\n  courseData,\n  expandedModules,\n  expandedChapters,\n  expandedContents,\n  onToggleModule,\n  onToggleChapter,\n  onToggleContent,\n  onToggleContentComplete,\n  onStartQuiz,\n  onNavigateToSection,\n  onExpandAllModules,\n  onCollapseAllModules,\n  onExpandAllChaptersInModule,\n  onCollapseAllChaptersInModule\n}) => {\n  const [showCourseStructure, setShowCourseStructure] = useState(true);\n  const [currentModuleIndex, setCurrentModuleIndex] = useState(0);\n  const allModulesExpanded = courseData.modules.filter(m => m.isUnlocked).every(m => expandedModules[m.id]);\n  const currentModule = courseData.modules[currentModuleIndex];\n  const canGoPrevious = currentModuleIndex > 0;\n  const canGoNext = currentModuleIndex < courseData.modules.length - 1;\n  const handlePreviousModule = () => {\n    if (canGoPrevious) {\n      setCurrentModuleIndex(currentModuleIndex - 1);\n    }\n  };\n  const handleNextModule = () => {\n    if (canGoNext) {\n      setCurrentModuleIndex(currentModuleIndex + 1);\n    }\n  };\n  const handleModuleNavigation = (moduleId: string) => {\n    const moduleIndex = courseData.modules.findIndex(m => m.id === moduleId);\n    if (moduleIndex !== -1) {\n      setCurrentModuleIndex(moduleIndex);\n    }\n  };\n  return <div className=\"h-[calc(100vh-210px)] overflow-hidden\" data-sentry-component=\"CourseTab\" data-sentry-source-file=\"course-tab.tsx\">\r\n      <div className='grid grid-cols-1 gap-6 lg:grid-cols-4 min-w-0 h-full'>\r\n        {/* Sidebar */}\r\n        <div className='lg:col-span-1 min-w-0 overflow-y-auto'>\r\n          <div className='w-full'>\r\n            <TableOfContents course={courseData} onNavigate={(moduleId, chapterId, contentId) => {\n            handleModuleNavigation(moduleId);\n            onNavigateToSection(moduleId, chapterId, contentId);\n          }} expandedModules={expandedModules} expandedChapters={expandedChapters} onToggleModule={onToggleModule} onToggleChapter={onToggleChapter} currentModuleIndex={currentModuleIndex} data-sentry-element=\"TableOfContents\" data-sentry-source-file=\"course-tab.tsx\" />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Main Content */}\r\n        <div className='space-y-4 lg:col-span-3 min-w-0 overflow-y-auto'>\r\n          {showCourseStructure && <div className='rounded-lg border border-blue-200 bg-blue-50 p-4 relative'>\r\n              <button onClick={() => setShowCourseStructure(false)} className='absolute top-3 right-3 p-1 rounded-full hover:bg-blue-100 transition-colors' aria-label='Tutup struktur kursus'>\r\n                <X className='h-4 w-4 text-blue-600' />\r\n              </button>\r\n              <h3 className='mb-2 font-semibold text-blue-900 pr-8'>Struktur Kursus</h3>\r\n              <p className='mb-3 text-sm text-blue-800'>\r\n                Selesaikan semua modul secara berurutan. Setiap bab harus diselesaikan\r\n                sebelum mengakses kuis. Kuis modul akan terbuka setelah menyelesaikan semua\r\n                kuis bab.\r\n              </p>\r\n              <div className='grid grid-cols-1 gap-4 text-sm md:grid-cols-3'>\r\n                <div className='flex items-center space-x-2'>\r\n                  <BookMarked className='h-4 w-4 text-blue-600' />\r\n                  <span>Jalur Pembelajaran Berurutan</span>\r\n                </div>\r\n                <div className='flex items-center space-x-2'>\r\n                  <Target className='h-4 w-4 text-blue-600' />\r\n                  <span>Kuis Interaktif</span>\r\n                </div>\r\n                <div className='flex items-center space-x-2'>\r\n                  <Award className='h-4 w-4 text-blue-600' />\r\n                  <span>Sertifikasi Profesional</span>\r\n                </div>\r\n              </div>\r\n            </div>}\r\n\r\n          {/* Current Module */}\r\n          <div className='space-y-6'>\r\n            {currentModule && <div key={currentModule.id} data-module-id={currentModule.id}>\r\n                <ModuleSection module={currentModule} expandedContents={expandedContents} expandedChapters={expandedChapters} onToggleContent={onToggleContent} onToggleContentComplete={onToggleContentComplete} onStartQuiz={onStartQuiz} isExpanded={expandedModules[currentModule.id] || false} onToggleExpanded={() => onToggleModule(currentModule.id)} onToggleChapter={onToggleChapter} onExpandAllChapters={() => onExpandAllChaptersInModule(currentModule.id)} onCollapseAllChapters={() => onCollapseAllChaptersInModule(currentModule.id)} />\r\n              </div>}\r\n          </div>\r\n\r\n          {/* Module Navigation */}\r\n          <div className='mb-6 flex items-center justify-between rounded-lg border bg-white p-4'>\r\n            <Button variant='outline' onClick={handlePreviousModule} disabled={!canGoPrevious} className='flex items-center space-x-2' data-sentry-element=\"Button\" data-sentry-source-file=\"course-tab.tsx\">\r\n              <ChevronLeft className='h-4 w-4' data-sentry-element=\"ChevronLeft\" data-sentry-source-file=\"course-tab.tsx\" />\r\n              <span>Modul Sebelumnya</span>\r\n            </Button>\r\n            \r\n            <div className='text-center'>\r\n              <h3 className='text-lg font-semibold'>\r\n                Modul {currentModuleIndex + 1} dari {courseData.modules.length}\r\n              </h3>\r\n              <p className='text-sm text-gray-600'>{currentModule?.title}</p>\r\n            </div>\r\n            \r\n            <Button variant='outline' onClick={handleNextModule} disabled={!canGoNext} className='flex items-center space-x-2' data-sentry-element=\"Button\" data-sentry-source-file=\"course-tab.tsx\">\r\n              <span>Modul Selanjutnya</span>\r\n              <ChevronRight className='h-4 w-4' data-sentry-element=\"ChevronRight\" data-sentry-source-file=\"course-tab.tsx\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>;\n};", "import React from 'react';\nimport { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';\nimport { Progress } from '@/components/ui/progress';\nimport { Badge } from '@/components/ui/badge';\nimport { BarChart3, Target } from 'lucide-react';\nimport { Course } from '@/types/lms';\ninterface ProgressTabProps {\n  courseData: Course;\n  overallProgress: number;\n}\nexport const ProgressTab: React.FC<ProgressTabProps> = ({\n  courseData,\n  overallProgress\n}) => {\n  return <div className='grid gap-6' data-sentry-component=\"ProgressTab\" data-sentry-source-file=\"progress-tab.tsx\">\r\n      {/* Progress Overview */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"progress-tab.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"progress-tab.tsx\">\r\n          <CardTitle className='flex items-center space-x-2' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"progress-tab.tsx\">\r\n            <BarChart3 className='h-5 w-5' data-sentry-element=\"BarChart3\" data-sentry-source-file=\"progress-tab.tsx\" />\r\n            <span>Ringkasan Kemajuan Belajar</span>\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"progress-tab.tsx\">\r\n          <div className='grid grid-cols-1 gap-6 md:grid-cols-3'>\r\n            <div className='text-center'>\r\n              <div className='mb-2 text-3xl font-bold text-blue-600'>\r\n                {Math.round(overallProgress)}%\r\n              </div>\r\n              <p className='text-gray-600'>Kemajuan Keseluruhan</p>\r\n              <Progress value={overallProgress} className='mt-2' data-sentry-element=\"Progress\" data-sentry-source-file=\"progress-tab.tsx\" />\r\n            </div>\r\n            <div className='text-center'>\r\n              <div className='mb-2 text-3xl font-bold text-green-600'>\r\n                {courseData.modules.filter(m => m.moduleQuiz.isPassed).length}\r\n              </div>\r\n              <p className='text-gray-600'>Modul Selesai</p>\r\n              <Progress value={courseData.modules.filter(m => m.moduleQuiz.isPassed).length / courseData.modules.length * 100} className='mt-2' data-sentry-element=\"Progress\" data-sentry-source-file=\"progress-tab.tsx\" />\r\n            </div>\r\n            <div className='text-center'>\r\n              <div className='mb-2 text-3xl font-bold text-purple-600'>0</div>\r\n              <p className='text-gray-600'>Jam Belajar</p>\r\n              <p className='mt-1 text-sm text-gray-500'>\r\n                Pelacakan waktu segera tersedia\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Module Progress Details */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"progress-tab.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"progress-tab.tsx\">\r\n          <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"progress-tab.tsx\">Detail Kemajuan Modul</CardTitle>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"progress-tab.tsx\">\r\n          <div className='space-y-4'>\r\n            {courseData.modules.map(module => {\n            const completedChapters = module.chapters.filter(ch => ch.contents.every(c => c.isCompleted) && ch.quiz.isPassed).length;\n            const moduleProgress = completedChapters / module.chapters.length * 100;\n            return <div key={module.id} className='rounded-lg border p-4'>\r\n                  <div className='mb-3 flex items-center justify-between'>\r\n                    <h4 className='font-medium'>{module.title}</h4>\r\n                    <Badge variant={module.moduleQuiz.isPassed ? 'default' : 'outline'}>\r\n                      {module.moduleQuiz.isPassed ? 'Selesai' : 'Sedang Belajar'}\r\n                    </Badge>\r\n                  </div>\r\n                  <Progress value={moduleProgress} className='mb-2' />\r\n                  <div className='flex justify-between text-sm text-gray-600'>\r\n                    <span>\r\n                      {completedChapters}/{module.chapters.length} bab\r\n                      selesai\r\n                    </span>\r\n                    <span>{Math.round(moduleProgress)}%</span>\r\n                  </div>\r\n                </div>;\n          })}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Quiz Results */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"progress-tab.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"progress-tab.tsx\">\r\n          <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"progress-tab.tsx\">Performa Kuis</CardTitle>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"progress-tab.tsx\">\r\n          {(() => {\n          const allQuizzes = [];\n\n          // Collect all quizzes from chapters and modules\n          courseData.modules.forEach(module => {\n            module.chapters.forEach(chapter => {\n              if (chapter.quiz.attempts > 0) {\n                allQuizzes.push({\n                  ...chapter.quiz,\n                  moduleName: module.title,\n                  chapterName: chapter.title,\n                  type: 'chapter' as const\n                });\n              }\n            });\n            if (module.moduleQuiz.attempts > 0) {\n              allQuizzes.push({\n                ...module.moduleQuiz,\n                moduleName: module.title,\n                chapterName: null,\n                type: 'module' as const\n              });\n            }\n          });\n\n          // Add final exam if attempted\n          if (courseData.finalExam.attempts > 0) {\n            allQuizzes.push({\n              ...courseData.finalExam,\n              moduleName: 'Final Assessment',\n              chapterName: null,\n              type: 'final' as const\n            });\n          }\n          if (allQuizzes.length === 0) {\n            return <div className='py-8 text-center text-gray-500'>\r\n                  <Target className='mx-auto mb-4 h-12 w-12 text-gray-400' />\r\n                  <p>\r\n                    Hasil kuis akan muncul di sini saat Anda menyelesaikan penilaian\r\n                  </p>\r\n                </div>;\n          }\n          return <div className='space-y-4'>\r\n                {allQuizzes.map(quiz => <div key={quiz.id} className='rounded-lg border p-4'>\r\n                    <div className='mb-2 flex items-start justify-between'>\r\n                      <div>\r\n                        <h4 className='font-medium'>{quiz.title}</h4>\r\n                        <p className='text-sm text-gray-600'>\r\n                          {quiz.moduleName}\r\n                          {quiz.chapterName ? ` • ${quiz.chapterName}` : ''}\r\n                        </p>\r\n                      </div>\r\n                      <Badge variant={quiz.isPassed ? 'default' : 'destructive'} className={quiz.isPassed ? 'bg-green-600 hover:bg-green-700 text-white' : ''}>\r\n                        {quiz.isPassed ? 'Lulus' : 'Tidak Lulus'}\r\n                      </Badge>\r\n                    </div>\r\n\r\n                    <div className='grid grid-cols-2 gap-4 text-sm md:grid-cols-4'>\r\n                      <div>\r\n                        <span className='text-gray-500'>Skor:</span>\r\n                        <div className='font-medium'>\r\n                          {quiz.lastScore !== undefined ? `${quiz.lastScore}%` : 'N/A'}\r\n                        </div>\r\n                      </div>\r\n                      <div>\r\n                        <span className='text-gray-500'>Diperlukan:</span>\r\n                        <div className='font-medium'>{quiz.minimumScore}%</div>\r\n                      </div>\r\n                      <div>\r\n                        <span className='text-gray-500'>Percobaan:</span>\r\n                        <div className='font-medium'>\r\n                          {quiz.attempts}/{quiz.maxAttempts}\r\n                        </div>\r\n                      </div>\r\n                      <div>\r\n                        <span className='text-gray-500'>Jenis:</span>\r\n                        <div className='font-medium capitalize'>\r\n                          {quiz.type}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {quiz.lastScore !== undefined && <div className='mt-3'>\r\n                        <Progress value={quiz.lastScore} className={`h-2 ${quiz.isPassed ? 'text-green-600' : 'text-red-600'}`} />\r\n                      </div>}\r\n                  </div>)}\r\n              </div>;\n        })()}\r\n        </CardContent>\r\n      </Card>\r\n    </div>;\n};", "import React from 'react';\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Trophy, CheckCircle, XCircle } from 'lucide-react';\nimport { Course } from '@/types/lms';\ninterface ExamTabProps {\n  courseData: Course;\n  onStartQuiz: (quizId: string) => void;\n}\nexport const ExamTab: React.FC<ExamTabProps> = ({\n  courseData,\n  onStartQuiz\n}) => {\n  const isFinalExamUnlocked = courseData.modules.every(m => m.chapters.every(ch => ch.contents.every(c => c.isCompleted) && ch.quiz.isPassed) && m.moduleQuiz.isPassed);\n  return <Card className='shadow-sm' data-sentry-element=\"Card\" data-sentry-component=\"ExamTab\" data-sentry-source-file=\"exam-tab.tsx\">\r\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"exam-tab.tsx\">\r\n        <CardTitle className='flex items-center space-x-2' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"exam-tab.tsx\">\r\n          <Trophy className='h-6 w-6' data-sentry-element=\"Trophy\" data-sentry-source-file=\"exam-tab.tsx\" />\r\n          <span>Ujian Akhir Sertifikasi</span>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className='p-6' data-sentry-element=\"CardContent\" data-sentry-source-file=\"exam-tab.tsx\">\r\n        <div className='space-y-6'>\r\n          {/* Exam Requirements */}\r\n          <div className='rounded-lg border border-amber-200 bg-amber-50 p-4'>\r\n            <h4 className='mb-2 font-semibold text-amber-800'>\r\n              Persyaratan Ujian\r\n            </h4>\r\n            <ul className='space-y-1 text-sm text-amber-700'>\r\n              <li>• Selesaikan semua modul dan lulus semua kuis modul</li>\r\n              <li>\r\n                • Nilai minimum lulus: {courseData.finalExam.minimumScore}%\r\n              </li>\r\n              <li>• Batas waktu: {courseData.finalExam.timeLimit} menit</li>\r\n              <li>• Maksimal percobaan: {courseData.finalExam.maxAttempts}</li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Current Status */}\r\n          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>\r\n            <div className='space-y-4'>\r\n              <h4 className='font-medium text-gray-900'>\r\n                Status Prasyarat\r\n              </h4>\r\n              {courseData.modules.map(module => <div key={module.id} className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>\r\n                  <span className='text-sm'>{module.title}</span>\r\n                  {module.moduleQuiz.isPassed ? <CheckCircle className='h-5 w-5 text-green-600' /> : <XCircle className='h-5 w-5 text-red-500' />}\r\n                </div>)}\r\n            </div>\r\n            <div className='space-y-4'>\r\n              <h4 className='font-medium text-gray-900'>Informasi Ujian</h4>\r\n              <div className='space-y-3'>\r\n                <div className='flex items-center justify-between'>\r\n                  <span className='text-sm text-gray-600'>Percobaan Digunakan</span>\r\n                  <span className='font-medium'>\r\n                    {courseData.finalExam.attempts}/\r\n                    {courseData.finalExam.maxAttempts}\r\n                  </span>\r\n                </div>\r\n                <div className='flex items-center justify-between'>\r\n                  <span className='text-sm text-gray-600'>Skor Terakhir</span>\r\n                  <span className='font-medium'>\r\n                    {courseData.finalExam.lastScore ? `${courseData.finalExam.lastScore}%` : 'N/A'}\r\n                  </span>\r\n                </div>\r\n                <div className='flex items-center justify-between'>\r\n                  <span className='text-sm text-gray-600'>Status</span>\r\n                  <Badge variant={courseData.finalExam.isPassed ? 'default' : 'outline'} data-sentry-element=\"Badge\" data-sentry-source-file=\"exam-tab.tsx\">\r\n                    {courseData.finalExam.isPassed ? 'Lulus' : 'Belum Diambil'}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Exam Action */}\r\n          <div className='border-t pt-4 text-center'>\r\n            <Button size='lg' variant='iai' disabled={courseData.finalExam.attempts >= courseData.finalExam.maxAttempts} onClick={() => onStartQuiz(courseData.finalExam.id)} data-sentry-element=\"Button\" data-sentry-source-file=\"exam-tab.tsx\">\r\n              <Trophy className='mr-2 h-5 w-5' data-sentry-element=\"Trophy\" data-sentry-source-file=\"exam-tab.tsx\" />\r\n              {courseData.finalExam.attempts === 0 ? 'Mulai Ujian Akhir' : 'Ulangi Ujian Akhir'}\r\n            </Button>\r\n            {!isFinalExamUnlocked && <p className='mt-2 text-sm text-gray-500'>\r\n                Selesaikan semua modul untuk membuka ujian akhir\r\n              </p>}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>;\n};", "import React from 'react';\nimport { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Award, CheckCircle, XCircle, Lock, Eye, Download } from 'lucide-react';\nimport { Course, Institution } from '@/types/lms';\nimport { CertificateData, downloadCertificateAsPDF, generateCertificateId } from '@/lib/certificate';\nimport { authStorage } from '@/lib/auth';\ninterface CertificateTabProps {\n  courseData: Course;\n  institution: Institution;\n  overallProgress: number;\n  onGenerateCertificate: () => void;\n  onShowCertificate: () => void;\n  onDownloadPDF?: () => void;\n}\nexport const CertificateTab: React.FC<CertificateTabProps> = ({\n  courseData,\n  institution,\n  overallProgress,\n  onGenerateCertificate,\n  onShowCertificate,\n  onDownloadPDF\n}) => {\n  const handleDownloadPDF = async () => {\n    // Get user from local storage\n    const user = authStorage.getUser();\n\n    // Create certificate data\n    const certificateData: CertificateData = {\n      studentName: user?.name || '<PERSON>',\n      // Use user's name from context\n      courseName: courseData.name,\n      courseCode: courseData.code,\n      completionDate: courseData.certificate.completionDate || new Date().toISOString().split('T')[0],\n      finalScore: courseData.finalExam.lastScore || 0,\n      instructorName: courseData.instructor,\n      institutionName: institution.name,\n      certificateId: generateCertificateId()\n    };\n\n    // Use the new PDF download function\n    await downloadCertificateAsPDF(certificateData);\n  };\n  return <Card data-sentry-element=\"Card\" data-sentry-component=\"CertificateTab\" data-sentry-source-file=\"certificate-tab.tsx\">\r\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"certificate-tab.tsx\">\r\n        <CardTitle className='flex items-center space-x-2' style={{\n        color: institution.certificateTemplate?.primaryColor\n      }} data-sentry-element=\"CardTitle\" data-sentry-source-file=\"certificate-tab.tsx\">\r\n          <Award className='h-6 w-6' data-sentry-element=\"Award\" data-sentry-source-file=\"certificate-tab.tsx\" />\r\n          <span>Sertifikasi Profesional</span>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className='p-6' data-sentry-element=\"CardContent\" data-sentry-source-file=\"certificate-tab.tsx\">\r\n        {courseData.finalExam.attempts === 0 ? (/* User hasn't taken final exam yet */\n      <div className='space-y-6 text-center'>\r\n            <div className='rounded-lg border-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50 p-8'>\r\n              <Lock className='mx-auto mb-4 h-16 w-16 text-yellow-600' />\r\n              <h3 className='mb-2 text-2xl font-bold text-yellow-800'>\r\n                Belum Mengikuti Final Exam\r\n              </h3>\r\n              <p className='text-yellow-700'>\r\n                Kamu belum mengikuti final exam. Selesaikan final exam terlebih dahulu untuk mendapatkan sertifikat.\r\n              </p>\r\n            </div>\r\n          </div>) : courseData.certificate.isEligible && courseData.certificate.isGenerated ? (/* Certificate Generated */\n      <div className='space-y-6 text-center'>\r\n            <div className='rounded-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-8'>\r\n              <Award className='mx-auto mb-4 h-16 w-16 text-green-600' />\r\n              <h3 className='mb-2 text-2xl font-bold text-green-800'>\r\n                Selamat!\r\n              </h3>\r\n              <p className='text-green-700'>\r\n                Anda telah berhasil menyelesaikan kursus {courseData.name} dan\r\n                memperoleh sertifikasi.\r\n              </p>\r\n              {courseData.certificate.completionDate && <p className='mt-2 text-sm text-green-600'>\r\n                  Diselesaikan pada:{' '}\r\n                  {new Date(courseData.certificate.completionDate).toLocaleDateString()}\r\n                </p>}\r\n            </div>\r\n            <div className='flex justify-center space-x-4'>\r\n              <Button onClick={onShowCertificate} className='bg-green-600 hover:bg-green-700'>\r\n                <Eye className='mr-2 h-4 w-4' />\r\n                Lihat Sertifikat\r\n              </Button>\r\n              <Button variant='outline' className='border-green-600 text-green-600 hover:bg-green-50' onClick={handleDownloadPDF}>\r\n                <Download className='mr-2 h-4 w-4' />\r\n                Unduh PDF\r\n              </Button>\r\n            </div>\r\n          </div>) : courseData.certificate.isEligible ? (/* Certificate Ready - Show View and Download */\n      <div className='space-y-6 text-center'>\r\n            <div className='rounded-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-8'>\r\n              <Award className='mx-auto mb-4 h-16 w-16 text-green-600' />\r\n              <h3 className='mb-2 text-2xl font-bold text-green-800'>\r\n                Selamat!\r\n              </h3>\r\n              <p className='text-green-700'>\r\n                Anda telah berhasil menyelesaikan kursus {courseData.name} dan\r\n                memperoleh sertifikasi.\r\n              </p>\r\n              {courseData.certificate.completionDate && <p className='mt-2 text-sm text-green-600'>\r\n                  Diselesaikan pada:{' '}\r\n                  {new Date(courseData.certificate.completionDate).toLocaleDateString()}\r\n                </p>}\r\n            </div>\r\n            <div className='flex justify-center space-x-4'>\r\n              <Button onClick={onShowCertificate} className='bg-green-600 hover:bg-green-700'>\r\n                <Eye className='mr-2 h-4 w-4' />\r\n                Lihat Sertifikat\r\n              </Button>\r\n              <Button variant='outline' className='border-green-600 text-green-600 hover:bg-green-50' onClick={handleDownloadPDF}>\r\n                <Download className='mr-2 h-4 w-4' />\r\n                Unduh PDF\r\n              </Button>\r\n            </div>\r\n          </div>) : (/* Not Eligible Yet */\n      <div className='space-y-6'>\r\n            <div className='rounded-lg border-2 border-gray-200 bg-gray-50 p-8 text-center'>\r\n              <Lock className='mx-auto mb-4 h-16 w-16 text-gray-400' />\r\n              <h3 className='mb-2 text-2xl font-bold text-gray-700'>\r\n                Persyaratan Sertifikat\r\n              </h3>\r\n              <p className='mb-4 text-gray-600'>\r\n                Selesaikan semua persyaratan kursus untuk memperoleh sertifikasi\r\n                profesional Anda.\r\n              </p>\r\n            </div>\r\n\r\n            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>\r\n              <div className='space-y-3'>\r\n                <h4 className='font-medium text-gray-900'>Penyelesaian Modul</h4>\r\n                {courseData.modules.map(module => <div key={module.id} className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>\r\n                    <span className='text-sm'>{module.title}</span>\r\n                    {module.moduleQuiz.isPassed ? <CheckCircle className='h-5 w-5 text-green-600' /> : <XCircle className='h-5 w-5 text-gray-400' />}\r\n                  </div>)}\r\n              </div>\r\n              <div className='space-y-3'>\r\n                <h4 className='font-medium text-gray-900'>\r\n                  Persyaratan Akhir\r\n                </h4>\r\n                <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>\r\n                  <span className='text-sm'>\r\n                    Final Exam (Min. {courseData.finalExam.minimumScore}%)\r\n                  </span>\r\n                  {courseData.finalExam.isPassed ? <CheckCircle className='h-5 w-5 text-green-600' /> : <XCircle className='h-5 w-5 text-gray-400' />}\r\n                </div>\r\n                <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>\r\n                  <span className='text-sm'>Skor Keseluruhan (Min. 70%)</span>\r\n                  {overallProgress >= 70 ? <CheckCircle className='h-5 w-5 text-green-600' /> : <XCircle className='h-5 w-5 text-gray-400' />}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>)}\r\n      </CardContent>\r\n    </Card>;\n};", "export { TreeNode } from './tree-node';\r\nexport { TableOfContents } from './table-of-contents';\r\nexport { ContentItem } from './content-item';\r\nexport { QuizCard } from './quiz-card';\r\nexport { QuizModal } from './quiz-modal';\r\nexport { ChapterSection } from './chapter-section';\r\nexport { ModuleSection } from './module-section';\r\nexport { CertificateTemplate } from './certificate-template';\r\n\r\n// Tab components\r\nexport { CourseTab } from './tabs/course-tab';\r\nexport { ProgressTab } from './tabs/progress-tab';\r\nexport { ExamTab } from './tabs/exam-tab';\r\nexport { CertificateTab } from './tabs/certificate-tab';\r\n\r\n// Final Exam components\r\nexport { Question, Option, QuestionBank } from './final-exam';\r\n", "'use client';\n\nimport React, { useState, useCallback, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Progress } from '@/components/ui/progress';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Badge } from '@/components/ui/badge';\nimport { BookOpen01Icon as BookOpenIcon, ChartIcon as BarChartIcon, Award01Icon as TrophyIcon, Award01Icon as AwardIcon, Calendar01Icon as CalendarIcon, Download01Icon as DownloadIcon, Building02Icon as BuildingIcon, ArrowLeft01Icon as ArrowLeftIcon } from 'hugeicons-react';\nimport Link from 'next/link';\nimport { useParams, useRouter, useSearchParams } from 'next/navigation';\nimport { Course, Quiz } from '@/types/lms';\nimport { useEnrollment } from '@/contexts/enrollment-context';\nimport { CertificateData, generateCertificateId, downloadCertificateAsPDF, generateCertificateModalHTML } from '@/lib/certificate';\nimport { authStorage } from '@/lib/auth';\nimport { QuizModal, CourseTab, ProgressTab, ExamTab, CertificateTab } from '@/components/lms';\nconst CoursePage: React.FC = () => {\n  const params = useParams();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const courseId = params.courseId as string;\n  const {\n    courseData: defaultCourseData,\n    updateCourseProgress,\n    getCourseById,\n    isEnrolledInCourse\n  } = useEnrollment();\n\n  // Get the specific course based on courseId from URL\n  const courseData = getCourseById(courseId) || defaultCourseData;\n  const [expandedContents, setExpandedContents] = useState<{\n    [key: string]: boolean;\n  }>({});\n  const [expandedModules, setExpandedModules] = useState<{\n    [key: string]: boolean;\n  }>({});\n  const [expandedChapters, setExpandedChapters] = useState<{\n    [key: string]: boolean;\n  }>({});\n  const [currentQuiz, setCurrentQuiz] = useState<Quiz | null>(null);\n  const [showCertificate, setShowCertificate] = useState(false);\n  const [activeTab, setActiveTab] = useState('course');\n  useEffect(() => {\n    const tab = searchParams.get('tab');\n    if (tab) {\n      setActiveTab(tab);\n    }\n  }, [searchParams]);\n  const toggleContent = useCallback((contentId: string) => {\n    setExpandedContents(prev => ({\n      ...prev,\n      [contentId]: !prev[contentId]\n    }));\n  }, []);\n  const toggleModule = useCallback((moduleId: string) => {\n    setExpandedModules(prev => ({\n      ...prev,\n      [moduleId]: !prev[moduleId]\n    }));\n  }, []);\n  const toggleChapter = useCallback((chapterId: string) => {\n    setExpandedChapters(prev => ({\n      ...prev,\n      [chapterId]: !prev[chapterId]\n    }));\n  }, []);\n  const expandAllModules = useCallback(() => {\n    const newExpandedModules: {\n      [key: string]: boolean;\n    } = {};\n    courseData.modules.forEach(module => {\n      if (module.isUnlocked) {\n        newExpandedModules[module.id] = true;\n      }\n    });\n    setExpandedModules(newExpandedModules);\n  }, [courseData.modules]);\n  const collapseAllModules = useCallback(() => {\n    setExpandedModules({});\n  }, []);\n  const expandAllChaptersInModule = useCallback((moduleId: string) => {\n    const courseModule = courseData.modules.find(m => m.id === moduleId);\n    if (!courseModule) return;\n    const newExpandedChapters = {\n      ...expandedChapters\n    };\n    courseModule.chapters.forEach(chapter => {\n      if (chapter.isUnlocked) {\n        newExpandedChapters[chapter.id] = true;\n      }\n    });\n    setExpandedChapters(newExpandedChapters);\n  }, [courseData.modules, expandedChapters]);\n  const collapseAllChaptersInModule = useCallback((moduleId: string) => {\n    const courseModule = courseData.modules.find(m => m.id === moduleId);\n    if (!courseModule) return;\n    setExpandedModules(prev => ({\n      ...prev,\n      [moduleId]: false\n    }));\n    const newExpandedContents = {\n      ...expandedContents\n    };\n    const newExpandedChapters = {\n      ...expandedChapters\n    };\n    courseModule.chapters.forEach(chapter => {\n      delete newExpandedChapters[chapter.id];\n    });\n    setExpandedChapters(newExpandedChapters);\n  }, [courseData.modules, expandedChapters]);\n  const toggleContentComplete = useCallback((contentId: string) => {\n    const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;\n    let contentFound = false;\n    for (const courseModule of newCourse.modules) {\n      for (const chapter of courseModule.chapters) {\n        const content = chapter.contents.find(c => c.id === contentId);\n        if (content) {\n          content.isCompleted = !content.isCompleted;\n          contentFound = true;\n          const completedContents = chapter.contents.filter(c => c.isCompleted).length;\n          const nextChapterIndex = chapter.order;\n          const nextChapter = courseModule.chapters.find(ch => ch.order === nextChapterIndex + 1);\n          if (nextChapter && completedContents === chapter.contents.length && chapter.quiz.isPassed) {\n            nextChapter.isUnlocked = true;\n          }\n          const allChaptersCompleted = courseModule.chapters.every(ch => ch.contents.every(c => c.isCompleted) && ch.quiz.isPassed);\n          if (allChaptersCompleted && courseModule.moduleQuiz.isPassed) {\n            const nextModuleIndex = courseModule.order;\n            const nextModule = newCourse.modules.find(m => m.order === nextModuleIndex + 1);\n            if (nextModule) {\n              nextModule.isUnlocked = true;\n              if (nextModule.chapters.length > 0) {\n                nextModule.chapters[0].isUnlocked = true;\n              }\n            }\n          }\n          break;\n        }\n      }\n      if (contentFound) break;\n    }\n    updateCourseProgress(newCourse);\n  }, [courseData, updateCourseProgress]);\n  const startQuiz = useCallback((quizId: string) => {\n    let quiz: Quiz | undefined;\n\n    // Check if it's the final exam\n    if (courseData.finalExam.id === quizId) {\n      // Navigate to dedicated exam page for final exam\n      router.push(`/my-courses/${courseId}/exam?type=final&examId=${quizId}`);\n      return;\n    }\n\n    // Check module and chapter quizzes\n    for (const courseModule of courseData.modules) {\n      for (const chapter of courseModule.chapters) {\n        if (chapter.quiz.id === quizId) {\n          quiz = chapter.quiz;\n          // For now, chapter quizzes can also use the exam page or modal\n          // You can decide whether to navigate or use modal\n          setCurrentQuiz({\n            ...quiz\n          });\n          return;\n        }\n      }\n      if (courseModule.moduleQuiz.id === quizId) {\n        quiz = courseModule.moduleQuiz;\n        // For now, module quizzes can also use the exam page or modal\n        // You can decide whether to navigate or use modal\n        setCurrentQuiz({\n          ...quiz\n        });\n        return;\n      }\n    }\n  }, [courseData, courseId, router]);\n  const handleQuizComplete = useCallback((score: number) => {\n    if (!currentQuiz) return;\n    const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;\n    const updateQuiz = (quiz: Quiz) => {\n      quiz.attempts += 1;\n      quiz.lastScore = score;\n      quiz.isPassed = score >= quiz.minimumScore;\n    };\n    for (const courseModule of newCourse.modules) {\n      for (const chapter of courseModule.chapters) {\n        if (chapter.quiz.id === currentQuiz.id) {\n          updateQuiz(chapter.quiz);\n          const allContentsCompleted = chapter.contents.every(c => c.isCompleted);\n          // Unlock next chapter if: (quiz passed OR max attempts reached) AND all contents completed\n          if ((chapter.quiz.isPassed || chapter.quiz.attempts >= chapter.quiz.maxAttempts) && allContentsCompleted) {\n            const nextChapter = courseModule.chapters.find(ch => ch.order === chapter.order + 1);\n            if (nextChapter) {\n              nextChapter.isUnlocked = true;\n            }\n          }\n          break;\n        }\n      }\n      if (courseModule.moduleQuiz.id === currentQuiz.id) {\n        updateQuiz(courseModule.moduleQuiz);\n        const allChaptersCompleted = courseModule.chapters.every(ch => ch.contents.every(c => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts));\n        if ((courseModule.moduleQuiz.isPassed || courseModule.moduleQuiz.attempts >= courseModule.moduleQuiz.maxAttempts) && allChaptersCompleted) {\n          const nextModule = newCourse.modules.find(m => m.order === courseModule.order + 1);\n          if (nextModule) {\n            nextModule.isUnlocked = true;\n            if (nextModule.chapters.length > 0) {\n              nextModule.chapters[0].isUnlocked = true;\n            }\n          }\n        }\n      }\n    }\n    if (newCourse.finalExam.id === currentQuiz.id) {\n      updateQuiz(newCourse.finalExam);\n      const allModulesCompleted = newCourse.modules.every(m => m.chapters.every(ch => ch.contents.every(c => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)) && (m.moduleQuiz.isPassed || m.moduleQuiz.attempts >= m.moduleQuiz.maxAttempts));\n      if (newCourse.finalExam.isPassed && allModulesCompleted) {\n        newCourse.certificate.isEligible = true;\n        newCourse.certificate.completionDate = new Date().toISOString().split('T')[0];\n        newCourse.status = 'completed';\n      }\n    }\n    updateCourseProgress(newCourse);\n    setCurrentQuiz(null);\n  }, [currentQuiz, courseData, updateCourseProgress]);\n  const generateCertificate = useCallback(() => {\n    if (courseData.certificate.isEligible) {\n      const updatedCourse = {\n        ...courseData,\n        certificate: {\n          ...courseData.certificate,\n          isGenerated: true,\n          certificateUrl: `#certificate-${courseData.id}`\n        }\n      };\n      updateCourseProgress(updatedCourse);\n      setShowCertificate(true);\n    }\n  }, [courseData, updateCourseProgress]);\n  const handleNavigateToSection = useCallback((moduleId: string, chapterId?: string, contentId?: string) => {\n    // Set active tab first\n    setActiveTab('course');\n\n    // Expand the module and chapter\n    setExpandedModules(prev => ({\n      ...prev,\n      [moduleId]: true\n    }));\n    if (chapterId) {\n      setExpandedChapters(prev => ({\n        ...prev,\n        [chapterId]: true\n      }));\n    }\n\n    // If navigating to specific content, also expand the content\n    if (contentId) {\n      setExpandedContents(prev => ({\n        ...prev,\n        [contentId]: true\n      }));\n    }\n\n    // Smooth scroll to the target element after a short delay to allow DOM updates\n    setTimeout(() => {\n      let targetId: string;\n      if (contentId) {\n        targetId = `content-${contentId}`;\n      } else if (chapterId) {\n        targetId = `chapter-${chapterId}`;\n      } else {\n        targetId = `module-${moduleId}`;\n      }\n      const targetElement = document.getElementById(targetId);\n      if (targetElement) {\n        targetElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start',\n          inline: 'nearest'\n        });\n\n        // Optional: Add a brief highlight effect\n        targetElement.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-75');\n        setTimeout(() => {\n          targetElement.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-75');\n        }, 2000);\n      }\n    }, 100);\n  }, []);\n\n  // Remove the old handleDownloadPDF function since we're now using the one from certificate-tab\n\n  const completedChapters = courseData.modules.reduce((total, module) => total + module.chapters.filter(ch => ch.contents.every(c => c.isCompleted) && ch.quiz.isPassed).length, 0);\n  const totalChapters = courseData.modules.reduce((total, module) => total + module.chapters.length, 0);\n  const overallProgress = totalChapters > 0 ? completedChapters / totalChapters * 100 : 0;\n  return <div className='min-h-screen bg-gray-50 overflow-auto' data-sentry-component=\"CoursePage\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='mx-auto max-w-full space-y-6 p-8'>\r\n        {/* Header with Back Button */}\r\n        <div className='flex items-center justify-between'>\r\n          <div className='flex items-center space-x-4'>\r\n            <Link href='/my-courses' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n              <Button variant='outline' size='sm' className='flex items-center space-x-2' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                <ArrowLeftIcon className='h-4 w-4' data-sentry-element=\"ArrowLeftIcon\" data-sentry-source-file=\"page.tsx\" />\r\n                <span>Kembali ke Kursus Saya</span>\r\n              </Button>\r\n            </Link>\r\n            <div className='flex items-center space-x-3'>\r\n              <BuildingIcon className='h-8 w-8 text-[var(--iai-primary)]' data-sentry-element=\"BuildingIcon\" data-sentry-source-file=\"page.tsx\" />\r\n              <div>\r\n                <h1 className='text-3xl font-bold text-gray-900'>\r\n                  {courseData.name}\r\n                </h1>\r\n                <p className='text-gray-600'>Kode Kursus: {courseData.code}</p>\r\n                <p className='text-gray-600'>\r\n                  Instruktur: {courseData.instructor}\r\n                </p>\r\n                <div className='mt-2 flex items-center space-x-4'>\r\n                  <span className='text-sm text-gray-500'>\r\n                    <CalendarIcon className='mr-1 inline h-4 w-4' data-sentry-element=\"CalendarIcon\" data-sentry-source-file=\"page.tsx\" />\r\n                    {courseData.startDate} - {courseData.endDate}\r\n                  </span>\r\n                  <Badge variant={courseData.status === 'completed' ? 'default' : 'secondary'} data-sentry-element=\"Badge\" data-sentry-source-file=\"page.tsx\">\r\n                    {courseData.status === 'completed' ? 'Selesai' : 'Sedang Belajar'}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className='flex items-center space-x-4'>\r\n            <div className='text-right'>\r\n              <p className='text-sm text-gray-500'>Kemajuan Keseluruhan</p>\r\n              <div className='flex items-center space-x-2'>\r\n                <Progress value={overallProgress} className='w-32' data-sentry-element=\"Progress\" data-sentry-source-file=\"page.tsx\" />\r\n                <span className='text-sm font-medium'>\r\n                  {Math.round(overallProgress)}%\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Tabs */}\r\n        <Tabs value={activeTab} onValueChange={setActiveTab} className='mb-6' data-sentry-element=\"Tabs\" data-sentry-source-file=\"page.tsx\">\r\n          <TabsList className='grid w-full grid-cols-4' data-sentry-element=\"TabsList\" data-sentry-source-file=\"page.tsx\">\r\n            <TabsTrigger value='course' className='flex items-center space-x-2' data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"page.tsx\">\r\n              <BookOpenIcon className='h-4 w-4' data-sentry-element=\"BookOpenIcon\" data-sentry-source-file=\"page.tsx\" />\r\n              <span>Konten Kursus</span>\r\n            </TabsTrigger>\r\n            <TabsTrigger value='progress' className='flex items-center space-x-2' data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"page.tsx\">\r\n              <BarChartIcon className='h-4 w-4' data-sentry-element=\"BarChartIcon\" data-sentry-source-file=\"page.tsx\" />\r\n              <span>Kemajuan</span>\r\n            </TabsTrigger>\r\n            <TabsTrigger value='exam' className='flex items-center space-x-2' data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"page.tsx\">\r\n              <TrophyIcon className='h-4 w-4' data-sentry-element=\"TrophyIcon\" data-sentry-source-file=\"page.tsx\" />\r\n              <span>Final Exam</span>\r\n            </TabsTrigger>\r\n            <TabsTrigger value='certificate' className='flex items-center space-x-2' data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"page.tsx\">\r\n              <AwardIcon className='h-4 w-4' data-sentry-element=\"AwardIcon\" data-sentry-source-file=\"page.tsx\" />\r\n              <span>Sertifikat</span>\r\n            </TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value='course' className='mt-4' data-sentry-element=\"TabsContent\" data-sentry-source-file=\"page.tsx\">\r\n            <CourseTab courseData={courseData} expandedModules={expandedModules} expandedChapters={expandedChapters} expandedContents={expandedContents} onToggleModule={toggleModule} onToggleChapter={toggleChapter} onToggleContent={toggleContent} onToggleContentComplete={toggleContentComplete} onStartQuiz={startQuiz} onNavigateToSection={handleNavigateToSection} onExpandAllModules={expandAllModules} onCollapseAllModules={collapseAllModules} onExpandAllChaptersInModule={expandAllChaptersInModule} onCollapseAllChaptersInModule={collapseAllChaptersInModule} data-sentry-element=\"CourseTab\" data-sentry-source-file=\"page.tsx\" />\r\n          </TabsContent>\r\n\r\n          <TabsContent value='progress' className='mt-4' data-sentry-element=\"TabsContent\" data-sentry-source-file=\"page.tsx\">\r\n            <ProgressTab courseData={courseData} overallProgress={overallProgress} data-sentry-element=\"ProgressTab\" data-sentry-source-file=\"page.tsx\" />\r\n          </TabsContent>\r\n\r\n          <TabsContent value='exam' className='mt-4' data-sentry-element=\"TabsContent\" data-sentry-source-file=\"page.tsx\">\r\n            <ExamTab courseData={courseData} onStartQuiz={startQuiz} data-sentry-element=\"ExamTab\" data-sentry-source-file=\"page.tsx\" />\r\n          </TabsContent>\r\n\r\n          <TabsContent value='certificate' className='mt-4' data-sentry-element=\"TabsContent\" data-sentry-source-file=\"page.tsx\">\r\n            <CertificateTab courseData={courseData} institution={{\n            id: 'iai-indonesia',\n            name: 'Indonesian Institute of Architects',\n            shortName: 'IAI',\n            website: 'https://iai.or.id',\n            certificateTemplate: {\n              primaryColor: '#1e40af',\n              secondaryColor: '#f59e0b',\n              signatoryName: 'Ar. Georgius Budi Yulianto, IAI, AA',\n              signatoryTitle: 'Ketua Umum IAI 2024-2027'\n            }\n          }} overallProgress={overallProgress} onGenerateCertificate={generateCertificate} onShowCertificate={() => setShowCertificate(true)} data-sentry-element=\"CertificateTab\" data-sentry-source-file=\"page.tsx\" />\r\n          </TabsContent>\r\n        </Tabs>\r\n\r\n        {/* Quiz Modal - Only for chapter and module quizzes */}\r\n        {currentQuiz && <QuizModal quiz={currentQuiz} isOpen={true} onComplete={handleQuizComplete} onClose={() => setCurrentQuiz(null)} />}\r\n\r\n        {/* Certificate Modal */}\r\n        <Dialog open={showCertificate} onOpenChange={setShowCertificate} data-sentry-element=\"Dialog\" data-sentry-source-file=\"page.tsx\">\r\n          <DialogContent className='max-h-[90vh] max-w-7xl w-[95vw] p-0' data-sentry-element=\"DialogContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='flex h-full max-h-[90vh] flex-col'>\r\n              <DialogHeader className='flex-shrink-0 border-b px-6 py-4' data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"page.tsx\">\r\n                <DialogTitle className='flex items-center space-x-2' data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"page.tsx\">\r\n                  <AwardIcon className='h-5 w-5' data-sentry-element=\"AwardIcon\" data-sentry-source-file=\"page.tsx\" />\r\n                  <span>Sertifikat Anda</span>\r\n                </DialogTitle>\r\n              </DialogHeader>\r\n              <div className='flex-1 overflow-y-auto px-6 py-6'>\r\n                <div className='w-full h-full' dangerouslySetInnerHTML={{\n                __html: generateCertificateModalHTML({\n                  studentName: authStorage.getUser()?.name || 'John Doe',\n                  courseName: courseData.name,\n                  courseCode: courseData.code,\n                  completionDate: courseData.certificate.completionDate || new Date().toISOString().split('T')[0],\n                  finalScore: courseData.finalExam.lastScore || 0,\n                  instructorName: courseData.instructor,\n                  institutionName: 'Indonesian Institute of Architects',\n                  certificateId: generateCertificateId()\n                })\n              }} />\r\n              </div>\r\n              <div className='flex-shrink-0 border-t bg-white px-6 py-4'>\r\n                <div className='flex justify-end space-x-2'>\r\n                  <Button variant='outline' onClick={() => setShowCertificate(false)} data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                    Tutup\r\n                  </Button>\r\n                  <Button onClick={async () => {\n                  // Create certificate data\n                  const certificateData: CertificateData = {\n                    studentName: authStorage.getUser()?.name || 'John Doe',\n                    // Use user's name from context\n                    courseName: courseData.name,\n                    courseCode: courseData.code,\n                    completionDate: courseData.certificate.completionDate || new Date().toISOString().split('T')[0],\n                    finalScore: courseData.finalExam.lastScore || 0,\n                    instructorName: courseData.instructor,\n                    institutionName: 'Indonesian Institute of Architects',\n                    certificateId: generateCertificateId()\n                  };\n\n                  // Use the new PDF download function\n                  await downloadCertificateAsPDF(certificateData);\n                }} data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                    <DownloadIcon className='mr-2 h-4 w-4' data-sentry-element=\"DownloadIcon\" data-sentry-source-file=\"page.tsx\" />\r\n                    Unduh PDF\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </DialogContent>\r\n        </Dialog>\r\n      </div>\r\n    </div>;\n};\nexport default CoursePage;", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('ChevronDown', __iconNode);\n\nexport default ChevronDown;\n", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '11', x: '3', y: '11', rx: '2', ry: '2', key: '1w4ew1' }],\n  ['path', { d: 'M7 11V7a5 5 0 0 1 10 0v4', key: 'fwvmzm' }],\n];\n\n/**\n * @component @name Lock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTEiIHg9IjMiIHk9IjExIiByeD0iMiIgcnk9IjIiIC8+CiAgPHBhdGggZD0iTTcgMTFWN2E1IDUgMCAwIDEgMTAgMHY0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lock = createLucideIcon('Lock', __iconNode);\n\nexport default Lock;\n", "module.exports = require(\"node:os\");", "module.exports = require(\"node:url\");", "module.exports = require(\"node:diagnostics_channel\");", "\"use client\";\n\nimport * as React from \"react\";\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\nimport { cn } from \"@/lib/utils\";\nconst Tabs = TabsPrimitive.Root;\nconst TabsList = React.forwardRef<React.ElementRef<typeof TabsPrimitive.List>, React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>>(({\n  className,\n  ...props\n}, ref) => <TabsPrimitive.List ref={ref} className={cn(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className)} {...props} />);\nTabsList.displayName = TabsPrimitive.List.displayName;\nconst TabsTrigger = React.forwardRef<React.ElementRef<typeof TabsPrimitive.Trigger>, React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>>(({\n  className,\n  ...props\n}, ref) => <TabsPrimitive.Trigger ref={ref} className={cn(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer\", className)} {...props} />);\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\nconst TabsContent = React.forwardRef<React.ElementRef<typeof TabsPrimitive.Content>, React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>>(({\n  className,\n  ...props\n}, ref) => <TabsPrimitive.Content ref={ref} className={cn(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className)} {...props} />);\nTabsContent.displayName = TabsPrimitive.Content.displayName;\nexport { Tabs, TabsList, TabsTrigger, TabsContent };", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport C from\"../create-hugeicon-component.js\";const o=C(\"BookOpen01Icon\",[[\"path\",{d:\"M12 6L12 20\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M5.98056 3.28544C9.32175 3.9216 11.3131 5.25231 12 6.01628C12.6869 5.25231 14.6782 3.9216 18.0194 3.28544C19.7121 2.96315 20.5584 2.80201 21.2792 3.41964C22 4.03727 22 5.04022 22 7.04612V14.255C22 16.0891 22 17.0061 21.5374 17.5787C21.0748 18.1512 20.0564 18.3451 18.0194 18.733C16.2037 19.0787 14.7866 19.6295 13.7608 20.1831C12.7516 20.7277 12.247 21 12 21C11.753 21 11.2484 20.7277 10.2392 20.1831C9.21344 19.6295 7.79633 19.0787 5.98056 18.733C3.94365 18.3451 2.9252 18.1512 2.4626 17.5787C2 17.0061 2 16.0891 2 14.255V7.04612C2 5.04022 2 4.03727 2.72078 3.41964C3.44157 2.80201 4.2879 2.96315 5.98056 3.28544Z\",stroke:\"currentColor\",key:\"k1\"}]]);export{o as default};\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['polyline', { points: '7 10 12 15 17 10', key: '2ggqvy' }],\n  ['line', { x1: '12', x2: '12', y1: '15', y2: '3', key: '1vk2je' }],\n];\n\n/**\n * @component @name Download\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSI3IDEwIDEyIDE1IDE3IDEwIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMTUiIHkyPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/download\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Download = createLucideIcon('Download', __iconNode);\n\nexport default Download;\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "import { AuthUser } from '@/types/database';\r\n\r\n// Client-side auth utilities\r\nexport const authStorage = {\r\n  setUser: (user: AuthUser) => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('auth_user', JSON.stringify(user));\r\n    }\r\n  },\r\n\r\n  getUser: (): AuthUser | null => {\r\n    if (typeof window !== 'undefined') {\r\n      const stored = localStorage.getItem('auth_user');\r\n      return stored ? JSON.parse(stored) : null;\r\n    }\r\n    return null;\r\n  },\r\n\r\n  removeUser: () => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.removeItem('auth_user');\r\n    }\r\n  },\r\n\r\n  isAuthenticated: (): boolean => {\r\n    return authStorage.getUser() !== null;\r\n  },\r\n\r\n  hasRole: (role: string): boolean => {\r\n    const user = authStorage.getUser();\r\n    return user?.role === role;\r\n  },\r\n\r\n  isSuperAdmin: (): boolean => {\r\n    return authStorage.hasRole('super_admin');\r\n  },\r\n\r\n  isTeacher: (): boolean => {\r\n    return authStorage.hasRole('teacher');\r\n  },\r\n\r\n  isStudent: (): boolean => {\r\n    return authStorage.hasRole('student');\r\n  }\r\n};\r\n\r\n// Role-based redirect logic\r\nexport const getRedirectPath = (user: AuthUser): string => {\r\n  switch (user.role) {\r\n    case 'super_admin':\r\n      return '/dashboard/admin';\r\n    case 'teacher':\r\n      return '/dashboard/teacher';\r\n    case 'student':\r\n      return '/courses';\r\n    default:\r\n      return '/dashboard';\r\n  }\r\n};\r\n\r\n// Protected route checker\r\nexport const checkAuth = (): AuthUser | null => {\r\n  const user = authStorage.getUser();\r\n  if (!user) {\r\n    // Redirect to sign in if not authenticated\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = '/auth/sign-in';\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n\r\n// Role-based access control\r\nexport const requireRole = (requiredRole: string): AuthUser | null => {\r\n  const user = checkAuth();\r\n  if (!user) return null;\r\n\r\n  if (user.role !== requiredRole) {\r\n    // Redirect to appropriate dashboard if wrong role\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = getRedirectPath(user);\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n\r\n// Multiple roles checker\r\nexport const requireAnyRole = (roles: string[]): AuthUser | null => {\r\n  const user = checkAuth();\r\n  if (!user) return null;\r\n\r\n  if (!roles.includes(user.role)) {\r\n    // Redirect to appropriate dashboard if wrong role\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = getRedirectPath(user);\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm9 12 2 2 4-4', key: 'dzmm74' }],\n];\n\n/**\n * @component @name CircleCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtOSAxMiAyIDIgNC00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheck = createLucideIcon('CircleCheck', __iconNode);\n\nexport default CircleCheck;\n", "module.exports = require(\"node:readline\");", "// Certificate generation utilities\r\n// In a real implementation, you might use libraries like jsPDF, canvas, or server-side PDF generation\r\n\r\nexport interface CertificateData {\r\n  studentName: string;\r\n  courseName: string;\r\n  courseCode: string;\r\n  completionDate: string;\r\n  finalScore: number;\r\n  instructorName: string;\r\n  institutionName: string;\r\n  certificateId: string;\r\n}\r\n\r\nexport const generateCertificateId = (): string => {\r\n  const year = new Date().getFullYear();\r\n  const randomNum = Math.floor(Math.random() * 10000)\r\n    .toString()\r\n    .padStart(4, '0');\r\n  return `CERT-${year}-${randomNum}`;\r\n};\r\n\r\nexport const generateCertificateHTML = (data: CertificateData): string => {\r\n  return `\r\n  <!DOCTYPE html>\r\n  <html lang=\"id\">\r\n  <head>\r\n      <meta charset=\"UTF-8\">\r\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n      <title>Serti<PERSON>kat Kelulusan</title>\r\n      <!-- Font -->\r\n      <link href=\"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap\" rel=\"stylesheet\">\r\n      <style>\r\n          :root{\r\n              --primary:#4a90e2;\r\n              --accent:#a370e8;\r\n              --text:#2c3e50;\r\n              --muted:#7f8c8d;\r\n              --light:#f0f4f8;\r\n              --white:#ffffff;\r\n          }\r\n          *{box-sizing:border-box;margin:0;padding:0}\r\n          body{\r\n              background:linear-gradient(135deg,var(--light),#e2e8f0);\r\n              font-family:'Montserrat',sans-serif;\r\n              display:flex;\r\n              align-items:center;\r\n              justify-content:center;\r\n              min-height:100vh;\r\n              padding:20px;\r\n          }\r\n          .certificate{\r\n              width:100%;\r\n              max-width:900px;\r\n              background:var(--white);\r\n              border-radius:16px;\r\n              box-shadow:0 20px 40px rgba(0,0,0,.08);\r\n              position:relative;\r\n              overflow:hidden;\r\n              padding:80px 80px 110px;\r\n          }\r\n          .certificate::before,\r\n          .certificate::after{\r\n              content:'';\r\n              position:absolute;\r\n              width:300px;\r\n              height:300px;\r\n              border-radius:50%;\r\n              opacity:.05;\r\n              z-index:0;\r\n          }\r\n          .certificate::before{top:-80px;left:-80px;background:radial-gradient(var(--primary),transparent 70%)}\r\n          .certificate::after{bottom:-80px;right:-80px;background:radial-gradient(var(--accent),transparent 70%)}\r\n\r\n          .watermark{\r\n              position:absolute;\r\n              top:50%;left:50%;\r\n              transform:translate(-50%,-50%) rotate(-45deg);\r\n              font-family:'Playfair Display',serif;\r\n              font-size:150px;\r\n              color:rgba(0,0,0,.03);\r\n              font-weight:700;\r\n              pointer-events:none;\r\n              z-index:0;\r\n          }\r\n\r\n          .header{text-align:center;margin-bottom:50px}\r\n          .title{\r\n              font-family:'Playfair Display',serif;\r\n              font-size:44px;\r\n              color:var(--text);\r\n              margin:0;\r\n          }\r\n          .subtitle{\r\n              font-size:16px;\r\n              color:var(--muted);\r\n              margin-top:8px;\r\n          }\r\n\r\n          .main-content{\r\n              text-align:center;\r\n              margin-bottom:60px;\r\n          }\r\n          .awarded-to{\r\n              font-size:16px;\r\n              color:var(--muted);\r\n              margin-bottom:8px;\r\n          }\r\n          .student-name{\r\n              font-family:'Playfair Display',serif;\r\n              font-size:42px;\r\n              color:var(--text);\r\n              position:relative;\r\n              display:inline-block;\r\n              margin-bottom:20px;\r\n          }\r\n          .student-name::after{\r\n              content:'';\r\n              position:absolute;\r\n              left:50%;\r\n              bottom:-6px;\r\n              transform:translateX(-50%);\r\n              width:80%;\r\n              height:3px;\r\n              background:linear-gradient(90deg,var(--primary),var(--accent));\r\n              border-radius:2px;\r\n          }\r\n          .completion-text{\r\n              font-size:18px;\r\n              color:#555;\r\n              line-height:1.6;\r\n              max-width:600px;\r\n              margin:0 auto 25px;\r\n          }\r\n          .course-details{\r\n              display:inline-block;\r\n              background:var(--light);\r\n              border-radius:12px;\r\n              padding:20px 35px;\r\n              box-shadow:0 4px 15px rgba(0,0,0,.05);\r\n              margin-bottom:25px;\r\n          }\r\n          .course-name{\r\n              font-size:24px;\r\n              font-weight:600;\r\n              color:var(--text);\r\n              margin:0;\r\n          }\r\n          .course-code{\r\n              font-size:15px;\r\n              color:var(--muted);\r\n              margin-top:4px;\r\n          }\r\n          .score{\r\n              font-size:20px;\r\n              font-weight:700;\r\n              color:var(--primary);\r\n          }\r\n\r\n          .footer{\r\n              display:flex;\r\n              justify-content:space-around;\r\n              align-items:flex-end;\r\n              border-top:1px solid #ecf0f1;\r\n              padding-top:30px;\r\n          }\r\n          .signature-section{\r\n              text-align:center;\r\n              flex:1;\r\n          }\r\n          .signature-line{\r\n              width:180px;\r\n              height:1px;\r\n              background:var(--muted);\r\n              margin:0 auto 8px;\r\n          }\r\n          .signature-label{\r\n              font-size:14px;\r\n              color:var(--muted);\r\n              line-height:1.4;\r\n          }\r\n\r\n          .id-date-row{\r\n              margin-top:30px;\r\n              display:flex;\r\n              justify-content:space-between;\r\n              font-size:13px;\r\n              color:#95a5a6;\r\n          }\r\n      </style>\r\n  </head>\r\n  <body>\r\n      <div class=\"certificate\">\r\n          <div class=\"watermark\">TERANG</div>\r\n\r\n          <!-- Konten utama -->\r\n          <div class=\"header\">\r\n              <h1 class=\"title\">Sertifikat Kelulusan</h1>\r\n              <p class=\"subtitle\">${data.institutionName}</p>\r\n          </div>\r\n\r\n          <div class=\"main-content\">\r\n              <p class=\"awarded-to\">Dengan bangga mempersembahkan sertifikat ini kepada</p>\r\n              <h2 class=\"student-name\">${data.studentName}</h2>\r\n              <p class=\"completion-text\">\r\n                  karena telah berhasil menyelesaikan dan lulus dari program\r\n              </p>\r\n\r\n              <div class=\"course-details\">\r\n                  <h3 class=\"course-name\">${data.courseName}</h3>\r\n                  <div class=\"course-code\">Kode Kursus: ${data.courseCode}</div>\r\n              </div>\r\n\r\n              <p class=\"score\">Nilai Akhir: ${data.finalScore}%</p>\r\n          </div>\r\n\r\n          <div class=\"footer\">\r\n              <div class=\"signature-section\">\r\n                  <div class=\"signature-line\"></div>\r\n                  <p class=\"signature-label\">${data.instructorName}<br>Instruktur Kursus</p>\r\n              </div>\r\n              <div class=\"signature-section\">\r\n                  <div class=\"signature-line\"></div>\r\n                  <p class=\"signature-label\">Tanggal Kelulusan<br>${data.completionDate}</p>\r\n              </div>\r\n          </div>\r\n\r\n          <div class=\"id-date-row\">\r\n              <span>ID Sertifikat: ${data.certificateId}</span>\r\n              <span>Diterbitkan pada: ${data.completionDate}</span>\r\n          </div>\r\n      </div>\r\n  </body>\r\n  </html>\r\n  `;\r\n};\r\n\r\n// Modal-friendly version (just the content without html/body tags)\r\nexport const generateCertificateModalHTML = (data: CertificateData): string => {\r\n  return `\r\n  <link href=\"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap\" rel=\"stylesheet\">\r\n  <style>\r\n      .certificate-modal-container {\r\n          --primary:#4a90e2;\r\n          --accent:#a370e8;\r\n          --text:#2c3e50;\r\n          --muted:#7f8c8d;\r\n          --light:#f0f4f8;\r\n          --white:#ffffff;\r\n          font-family:'Montserrat',sans-serif;\r\n          width: 100%;\r\n          min-height: 600px;\r\n          background: var(--white);\r\n          position: relative;\r\n      }\r\n      .certificate-modal{\r\n          width:100%;\r\n          background:var(--white);\r\n          border-radius:16px;\r\n          box-shadow:0 20px 40px rgba(0,0,0,.08);\r\n          position:relative;\r\n          overflow:hidden;\r\n          padding:60px 60px 80px;\r\n          margin: 0;\r\n      }\r\n      .certificate-modal *{box-sizing:border-box;}\r\n      \r\n      .certificate-modal::before,\r\n      .certificate-modal::after{\r\n          content:'';\r\n          position:absolute;\r\n          width:250px;\r\n          height:250px;\r\n          border-radius:50%;\r\n          opacity:.05;\r\n          z-index:0;\r\n      }\r\n      .certificate-modal::before{top:-60px;left:-60px;background:radial-gradient(var(--primary),transparent 70%)}\r\n      .certificate-modal::after{bottom:-60px;right:-60px;background:radial-gradient(var(--accent),transparent 70%)}\r\n\r\n      .certificate-modal .watermark{\r\n          position:absolute;\r\n          top:50%;left:50%;\r\n          transform:translate(-50%,-50%) rotate(-45deg);\r\n          font-family:'Playfair Display',serif;\r\n          font-size:100px;\r\n          color:rgba(0,0,0,.03);\r\n          font-weight:700;\r\n          pointer-events:none;\r\n          z-index:0;\r\n      }\r\n\r\n      .certificate-modal .header{text-align:center;margin-bottom:40px;position:relative;z-index:1;}\r\n      .certificate-modal .title{\r\n          font-family:'Playfair Display',serif;\r\n          font-size:32px;\r\n          color:var(--text);\r\n          margin:0;\r\n      }\r\n      .certificate-modal .subtitle{\r\n          font-size:14px;\r\n          color:var(--muted);\r\n          margin-top:8px;\r\n      }\r\n\r\n      .certificate-modal .main-content{\r\n          text-align:center;\r\n          margin-bottom:50px;\r\n          position:relative;\r\n          z-index:1;\r\n      }\r\n      .certificate-modal .awarded-to{\r\n          font-size:14px;\r\n          color:var(--muted);\r\n          margin-bottom:8px;\r\n      }\r\n      .certificate-modal .student-name{\r\n          font-family:'Playfair Display',serif;\r\n          font-size:28px;\r\n          color:var(--text);\r\n          position:relative;\r\n          display:inline-block;\r\n          margin-bottom:20px;\r\n      }\r\n      .certificate-modal .student-name::after{\r\n          content:'';\r\n          position:absolute;\r\n          left:50%;\r\n          bottom:-6px;\r\n          transform:translateX(-50%);\r\n          width:80%;\r\n          height:3px;\r\n          background:linear-gradient(90deg,var(--primary),var(--accent));\r\n          border-radius:2px;\r\n      }\r\n      .certificate-modal .completion-text{\r\n          font-size:15px;\r\n          color:#555;\r\n          line-height:1.6;\r\n          max-width:500px;\r\n          margin:0 auto 20px;\r\n      }\r\n      .certificate-modal .course-details{\r\n          display:inline-block;\r\n          background:var(--light);\r\n          border-radius:12px;\r\n          padding:16px 30px;\r\n          box-shadow:0 4px 15px rgba(0,0,0,.05);\r\n          margin-bottom:20px;\r\n      }\r\n      .certificate-modal .course-name{\r\n          font-size:18px;\r\n          font-weight:600;\r\n          color:var(--text);\r\n          margin:0;\r\n      }\r\n      .certificate-modal .course-code{\r\n          font-size:13px;\r\n          color:var(--muted);\r\n          margin-top:4px;\r\n      }\r\n      .certificate-modal .score{\r\n          font-size:16px;\r\n          font-weight:700;\r\n          color:var(--primary);\r\n      }\r\n\r\n      .certificate-modal .footer{\r\n          display:flex;\r\n          justify-content:space-around;\r\n          align-items:flex-end;\r\n          border-top:1px solid #ecf0f1;\r\n          padding-top:25px;\r\n          position:relative;\r\n          z-index:1;\r\n      }\r\n      .certificate-modal .signature-section{\r\n          text-align:center;\r\n          flex:1;\r\n      }\r\n      .certificate-modal .signature-line{\r\n          width:140px;\r\n          height:1px;\r\n          background:var(--muted);\r\n          margin:0 auto 8px;\r\n      }\r\n      .certificate-modal .signature-label{\r\n          font-size:12px;\r\n          color:var(--muted);\r\n          line-height:1.4;\r\n      }\r\n\r\n      .certificate-modal .id-date-row{\r\n          margin-top:25px;\r\n          display:flex;\r\n          justify-content:space-between;\r\n          font-size:11px;\r\n          color:#95a5a6;\r\n          position:relative;\r\n          z-index:1;\r\n      }\r\n  </style>\r\n  \r\n  <div class=\"certificate-modal-container\">\r\n      <div class=\"certificate-modal\">\r\n          <div class=\"watermark\">TERANG</div>\r\n\r\n          <div class=\"header\">\r\n              <h1 class=\"title\">Sertifikat Kelulusan</h1>\r\n              <p class=\"subtitle\">${data.institutionName}</p>\r\n          </div>\r\n\r\n          <div class=\"main-content\">\r\n              <p class=\"awarded-to\">Dengan bangga mempersembahkan sertifikat ini kepada</p>\r\n              <h2 class=\"student-name\">${data.studentName}</h2>\r\n              <p class=\"completion-text\">\r\n                  karena telah berhasil menyelesaikan dan lulus dari program\r\n              </p>\r\n\r\n              <div class=\"course-details\">\r\n                  <h3 class=\"course-name\">${data.courseName}</h3>\r\n                  <div class=\"course-code\">Kode Kursus: ${data.courseCode}</div>\r\n              </div>\r\n\r\n              <p class=\"score\">Nilai Akhir: ${data.finalScore}%</p>\r\n          </div>\r\n\r\n          <div class=\"footer\">\r\n              <div class=\"signature-section\">\r\n                  <div class=\"signature-line\"></div>\r\n                  <p class=\"signature-label\">${data.instructorName}<br>Instruktur Kursus</p>\r\n              </div>\r\n              <div class=\"signature-section\">\r\n                  <div class=\"signature-line\"></div>\r\n                  <p class=\"signature-label\">Tanggal Kelulusan<br>${data.completionDate}</p>\r\n              </div>\r\n          </div>\r\n\r\n          <div class=\"id-date-row\">\r\n              <span>ID Sertifikat: ${data.certificateId}</span>\r\n              <span>Diterbitkan pada: ${data.completionDate}</span>\r\n          </div>\r\n      </div>\r\n  </div>\r\n  `;\r\n};\r\n\r\nexport const downloadCertificateAsPDF = async (\r\n  data: CertificateData\r\n): Promise<void> => {\r\n  try {\r\n    const htmlContent = generateCertificateHTML(data);\r\n    \r\n    // Call our API endpoint to generate PDF using puppeteer-service\r\n    const response = await fetch('/api/certificates', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ htmlContent }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error('Failed to generate PDF');\r\n    }\r\n\r\n    const blob = await response.blob();\r\n    const url = URL.createObjectURL(blob);\r\n\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = `certificate-${data.certificateId}.pdf`;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n    URL.revokeObjectURL(url);\r\n  } catch (error) {\r\n    console.error('Error generating PDF:', error);\r\n    // Fallback to downloading as HTML if PDF generation fails\r\n    const htmlContent = generateCertificateHTML(data);\r\n    const blob = new Blob([htmlContent], { type: 'text/html' });\r\n    const url = URL.createObjectURL(blob);\r\n\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = `certificate-${data.certificateId}.html`;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n    URL.revokeObjectURL(url);\r\n  }\r\n};\r\n\r\nexport const previewCertificate = (data: CertificateData): void => {\r\n  const htmlContent = generateCertificateHTML(data);\r\n  const newWindow = window.open('', '_blank');\r\n  if (newWindow) {\r\n    newWindow.document.write(htmlContent);\r\n    newWindow.document.close();\r\n  }\r\n};\r\n\r\nexport const shareCertificate = async (\r\n  data: CertificateData\r\n): Promise<void> => {\r\n  if (navigator.share) {\r\n    try {\r\n      await navigator.share({\r\n        title: `Certificate of Completion - ${data.courseName}`,\r\n        text: `I've completed ${data.courseName} with a score of ${data.finalScore}%!`,\r\n        url: window.location.href\r\n      });\r\n    } catch (error) {\r\n      console.error('Error sharing certificate:', error);\r\n      // Fallback to copying to clipboard\r\n      copyToClipboard(\r\n        `I've completed ${data.courseName} with a score of ${data.finalScore}%! Certificate ID: ${data.certificateId}`\r\n      );\r\n    }\r\n  } else {\r\n    // Fallback for browsers that don't support Web Share API\r\n    copyToClipboard(\r\n      `I've completed ${data.courseName} with a score of ${data.finalScore}%! Certificate ID: ${data.certificateId}`\r\n    );\r\n  }\r\n};\r\n\r\nconst copyToClipboard = (text: string): void => {\r\n  navigator.clipboard\r\n    .writeText(text)\r\n    .then(() => {\r\n      // You could show a toast notification here\r\n      console.log('Certificate details copied to clipboard');\r\n    })\r\n    .catch((error) => {\r\n      console.error('Failed to copy to clipboard:', error);\r\n    });\r\n};\r\n", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\page.tsx\");\n", "'use client';\n\nimport * as React from 'react';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { XIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot='dialog' {...props} data-sentry-element=\"DialogPrimitive.Root\" data-sentry-component=\"Dialog\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot='dialog-trigger' {...props} data-sentry-element=\"DialogPrimitive.Trigger\" data-sentry-component=\"DialogTrigger\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot='dialog-portal' {...props} data-sentry-element=\"DialogPrimitive.Portal\" data-sentry-component=\"DialogPortal\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot='dialog-close' {...props} data-sentry-element=\"DialogPrimitive.Close\" data-sentry-component=\"DialogClose\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return <DialogPrimitive.Overlay data-slot='dialog-overlay' className={cn('data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50', className)} {...props} data-sentry-element=\"DialogPrimitive.Overlay\" data-sentry-component=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return <DialogPortal data-slot='dialog-portal' data-sentry-element=\"DialogPortal\" data-sentry-component=\"DialogContent\" data-sentry-source-file=\"dialog.tsx\">\r\n      <DialogOverlay data-sentry-element=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />\r\n      <DialogPrimitive.Content data-slot='dialog-content' className={cn('bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg', className)} {...props} data-sentry-element=\"DialogPrimitive.Content\" data-sentry-source-file=\"dialog.tsx\">\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\" data-sentry-element=\"DialogPrimitive.Close\" data-sentry-source-file=\"dialog.tsx\">\r\n          <XIcon data-sentry-element=\"XIcon\" data-sentry-source-file=\"dialog.tsx\" />\r\n          <span className='sr-only'>Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>;\n}\nfunction DialogHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-header' className={cn('flex flex-col gap-2 text-center sm:text-left', className)} {...props} data-sentry-component=\"DialogHeader\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-footer' className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)} {...props} data-sentry-component=\"DialogFooter\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return <DialogPrimitive.Title data-slot='dialog-title' className={cn('text-lg leading-none font-semibold', className)} {...props} data-sentry-element=\"DialogPrimitive.Title\" data-sentry-component=\"DialogTitle\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return <DialogPrimitive.Description data-slot='dialog-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-element=\"DialogPrimitive.Description\" data-sentry-component=\"DialogDescription\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nexport { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogOverlay, DialogPortal, DialogTitle, DialogTrigger };", "module.exports = require(\"events\");"], "names": ["TooltipProvider", "delayDuration", "props", "TooltipPrimitive", "data-slot", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "className", "sideOffset", "children", "cn", "serverComponentModule.default", "TreeNode", "title", "icon", "isUnlocked", "isCompleted", "level", "isExpanded", "onToggle", "onClick", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive", "mightBeTruncated", "length", "div", "<PERSON><PERSON><PERSON><PERSON>", "button", "style", "paddingLeft", "disabled", "span", "ChevronDown", "ChevronRight", "Lock", "CheckCircle2", "side", "p", "TableOfContents", "course", "onNavigate", "expandedModules", "expandedChapters", "onToggleModule", "onToggleChapter", "currentModuleIndex", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "List", "<PERSON><PERSON><PERSON><PERSON>", "modules", "map", "module", "index", "moduleCompleted", "chapters", "every", "ch", "contents", "c", "quiz", "isPassed", "moduleQuiz", "id", "BookOpen", "isCurrentModule", "chapter", "chapterCompleted", "Book", "content", "type", "Play", "FileText", "BookMarked", "Target", "attempts", "maxAttempts", "Trophy", "m", "finalExam", "ContentItem", "onToggleComplete", "onToggleExpand", "console", "log", "displayTitle", "value", "extractFirstMarkdownHeader", "line", "markdown", "split", "lines", "match", "trim", "getContentIcon", "Image", "Badge", "variant", "getContentTypeLabel", "duration", "Timer", "ChevronUp", "ReactMarkdown", "remarkPlugins", "remarkGfm", "components", "h1", "node", "h2", "h3", "h4", "ul", "ol", "li", "blockquote", "code", "exec", "pre", "table", "thead", "th", "td", "hr", "strong", "em", "Array", "isArray", "block", "join", "<PERSON><PERSON>", "size", "handleDownloadMarkdownAsPDF", "printWindow", "window", "open", "htmlContent", "document", "write", "close", "markdownDiv", "getElementById", "htmlText", "innerHTML", "replace", "setTimeout", "focus", "print", "Download", "iframe", "src", "pdfUrl", "Eye", "link", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "img", "alt", "imageUrl", "ExternalLink", "videoUrl", "includes", "youtubeId", "pop", "frameBorder", "allow", "allowFullScreen", "video", "controls", "vimeoId", "e", "stopPropagation", "QuizCard", "onStartQuiz", "getQuizTypeColor", "getQuizIcon", "minimumScore", "timeLimit", "Clock", "CheckCircle", "lastScore", "QuizModal", "isOpen", "onClose", "onComplete", "currentQuestion", "setCurrentQuestion", "useState", "answers", "setAnswers", "timeLeft", "setTimeLeft", "isSubmitting", "setIsSubmitting", "React", "timer", "setInterval", "prev", "clearInterval", "handleAnswerChange", "questionId", "answer", "handleSubmitQuiz", "correctAnswers", "questions", "for<PERSON>ach", "question", "userAnswer", "<PERSON><PERSON><PERSON><PERSON>", "selectedOption", "options", "isCorrect", "score", "Math", "round", "currentQ", "isLastQuestion", "canProceed", "undefined", "Dialog", "onOpenChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "formatTime", "seconds", "mins", "floor", "secs", "toString", "padStart", "Progress", "String", "option", "label", "input", "name", "checked", "onChange", "optionBlockIndex", "textarea", "rows", "placeholder", "target", "max", "expandedContents", "onToggleContent", "onToggleContentComplete", "onToggleExpanded", "completedContents", "filter", "totalContents", "progress", "onExpandAllChapters", "onCollapseAllChapters", "totalChapters", "completedChapters", "unlockedChapters", "description", "ChapterSection", "CourseTab", "courseData", "onNavigateToSection", "onExpandAllModules", "onCollapseAllModules", "onExpandAllChaptersInModule", "onCollapseAllChaptersInModule", "showCourseStructure", "setShowCourseStructure", "setCurrentModuleIndex", "currentModule", "canGoPrevious", "canGoNext", "handleModuleNavigation", "moduleIndex", "findIndex", "moduleId", "chapterId", "contentId", "aria-label", "X", "Award", "data-module-id", "ModuleSection", "handlePreviousModule", "ChevronLeft", "handleNextModule", "ProgressTab", "overallProgress", "BarChart3", "moduleProgress", "allQuizzes", "push", "moduleName", "chapterName", "ExamTab", "isFinalExamUnlocked", "XCircle", "institution", "onGenerateCertificate", "onShowCertificate", "onDownloadPDF", "handleDownloadPDF", "user", "authStorage", "getUser", "certificateData", "studentName", "courseName", "courseCode", "completionDate", "certificate", "Date", "toISOString", "finalScore", "<PERSON><PERSON><PERSON>", "instructor", "institutionName", "certificateId", "generateCertificateId", "downloadCertificateAsPDF", "color", "certificateTemplate", "primaryColor", "isEligible", "isGenerated", "toLocaleDateString", "params", "useParams", "CoursePage", "router", "useRouter", "searchParams", "useSearchParams", "courseId", "defaultCourseData", "updateCourseProgress", "getCourseById", "isEnrolledInCourse", "useEnrollment", "setExpandedContents", "setExpandedModules", "setExpandedChapters", "currentQuiz", "setCurrentQuiz", "showCertificate", "setShowCertificate", "activeTab", "setActiveTab", "useEffect", "tab", "get", "toggle<PERSON><PERSON>nt", "useCallback", "toggleModule", "toggle<PERSON>hapter", "expandAllModules", "newExpandedModules", "collapseAllModules", "expandAllChaptersInModule", "courseModule", "find", "newExpandedChapters", "collapseAllChaptersInModule", "toggleContentComplete", "newCourse", "JSON", "parse", "stringify", "contentFound", "nextChapterIndex", "order", "nextChapter", "nextModuleIndex", "nextModule", "startQuiz", "quizId", "handleQuizComplete", "updateQuiz", "allContentsCompleted", "allChaptersCompleted", "allModulesCompleted", "status", "generateCertificate", "updatedCourse", "certificateUrl", "handleNavigateToSection", "targetId", "targetElement", "scrollIntoView", "behavior", "inline", "classList", "add", "remove", "reduce", "total", "Link", "ArrowLeftIcon", "BuildingIcon", "CalendarIcon", "startDate", "endDate", "Tabs", "onValueChange", "TabsList", "TabsTrigger", "BookOpenIcon", "BarChartIcon", "TrophyIcon", "AwardIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CertificateTab", "shortName", "website", "secondaryColor", "signatoryName", "signatoryTitle", "dangerouslySetInnerHTML", "__html", "generateCertificateModalHTML", "DownloadIcon", "TabsPrimitive", "ref", "displayName", "setUser", "removeUser", "isAuthenticated", "hasRole", "role", "isSuperAdmin", "<PERSON><PERSON><PERSON>er", "isStudent", "checkAuth", "requiredRole", "year", "getFullYear", "randomNum", "random", "generateCertificateHTML", "data", "response", "fetch", "method", "headers", "ok", "blob", "url", "URL", "createObjectURL", "revokeObjectURL", "error", "Blob", "newWindow", "navigator", "share", "text", "location", "copyToClipboard", "clipboard", "writeText", "then", "catch", "DialogPrimitive", "DialogTrigger", "DialogPortal", "DialogOverlay", "XIcon", "<PERSON><PERSON><PERSON><PERSON>er", "DialogDescription"], "sourceRoot": ""}