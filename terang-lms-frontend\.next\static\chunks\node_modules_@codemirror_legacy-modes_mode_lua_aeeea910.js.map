{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/lua.js"], "sourcesContent": ["function prefixRE(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")\", \"i\");\n}\nfunction wordRE(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n}\n\n// long list of standard functions from lua manual\nvar builtins = wordRE([\n  \"_G\",\"_VERSION\",\"assert\",\"collectgarbage\",\"dofile\",\"error\",\"getfenv\",\"getmetatable\",\"ipairs\",\"load\",\n  \"loadfile\",\"loadstring\",\"module\",\"next\",\"pairs\",\"pcall\",\"print\",\"rawequal\",\"rawget\",\"rawset\",\"require\",\n  \"select\",\"setfenv\",\"setmetatable\",\"tonumber\",\"tostring\",\"type\",\"unpack\",\"xpcall\",\n\n  \"coroutine.create\",\"coroutine.resume\",\"coroutine.running\",\"coroutine.status\",\"coroutine.wrap\",\"coroutine.yield\",\n\n  \"debug.debug\",\"debug.getfenv\",\"debug.gethook\",\"debug.getinfo\",\"debug.getlocal\",\"debug.getmetatable\",\n  \"debug.getregistry\",\"debug.getupvalue\",\"debug.setfenv\",\"debug.sethook\",\"debug.setlocal\",\"debug.setmetatable\",\n  \"debug.setupvalue\",\"debug.traceback\",\n\n  \"close\",\"flush\",\"lines\",\"read\",\"seek\",\"setvbuf\",\"write\",\n\n  \"io.close\",\"io.flush\",\"io.input\",\"io.lines\",\"io.open\",\"io.output\",\"io.popen\",\"io.read\",\"io.stderr\",\"io.stdin\",\n  \"io.stdout\",\"io.tmpfile\",\"io.type\",\"io.write\",\n\n  \"math.abs\",\"math.acos\",\"math.asin\",\"math.atan\",\"math.atan2\",\"math.ceil\",\"math.cos\",\"math.cosh\",\"math.deg\",\n  \"math.exp\",\"math.floor\",\"math.fmod\",\"math.frexp\",\"math.huge\",\"math.ldexp\",\"math.log\",\"math.log10\",\"math.max\",\n  \"math.min\",\"math.modf\",\"math.pi\",\"math.pow\",\"math.rad\",\"math.random\",\"math.randomseed\",\"math.sin\",\"math.sinh\",\n  \"math.sqrt\",\"math.tan\",\"math.tanh\",\n\n  \"os.clock\",\"os.date\",\"os.difftime\",\"os.execute\",\"os.exit\",\"os.getenv\",\"os.remove\",\"os.rename\",\"os.setlocale\",\n  \"os.time\",\"os.tmpname\",\n\n  \"package.cpath\",\"package.loaded\",\"package.loaders\",\"package.loadlib\",\"package.path\",\"package.preload\",\n  \"package.seeall\",\n\n  \"string.byte\",\"string.char\",\"string.dump\",\"string.find\",\"string.format\",\"string.gmatch\",\"string.gsub\",\n  \"string.len\",\"string.lower\",\"string.match\",\"string.rep\",\"string.reverse\",\"string.sub\",\"string.upper\",\n\n  \"table.concat\",\"table.insert\",\"table.maxn\",\"table.remove\",\"table.sort\"\n]);\nvar keywords = wordRE([\"and\",\"break\",\"elseif\",\"false\",\"nil\",\"not\",\"or\",\"return\",\n                       \"true\",\"function\", \"end\", \"if\", \"then\", \"else\", \"do\",\n                       \"while\", \"repeat\", \"until\", \"for\", \"in\", \"local\" ]);\n\nvar indentTokens = wordRE([\"function\", \"if\",\"repeat\",\"do\", \"\\\\(\", \"{\"]);\nvar dedentTokens = wordRE([\"end\", \"until\", \"\\\\)\", \"}\"]);\nvar dedentPartial = prefixRE([\"end\", \"until\", \"\\\\)\", \"}\", \"else\", \"elseif\"]);\n\nfunction readBracket(stream) {\n  var level = 0;\n  while (stream.eat(\"=\")) ++level;\n  stream.eat(\"[\");\n  return level;\n}\n\nfunction normal(stream, state) {\n  var ch = stream.next();\n  if (ch == \"-\" && stream.eat(\"-\")) {\n    if (stream.eat(\"[\") && stream.eat(\"[\"))\n      return (state.cur = bracketed(readBracket(stream), \"comment\"))(stream, state);\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (ch == \"\\\"\" || ch == \"'\")\n    return (state.cur = string(ch))(stream, state);\n  if (ch == \"[\" && /[\\[=]/.test(stream.peek()))\n    return (state.cur = bracketed(readBracket(stream), \"string\"))(stream, state);\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w.%]/);\n    return \"number\";\n  }\n  if (/[\\w_]/.test(ch)) {\n    stream.eatWhile(/[\\w\\\\\\-_.]/);\n    return \"variable\";\n  }\n  return null;\n}\n\nfunction bracketed(level, style) {\n  return function(stream, state) {\n    var curlev = null, ch;\n    while ((ch = stream.next()) != null) {\n      if (curlev == null) {if (ch == \"]\") curlev = 0;}\n      else if (ch == \"=\") ++curlev;\n      else if (ch == \"]\" && curlev == level) { state.cur = normal; break; }\n      else curlev = null;\n    }\n    return style;\n  };\n}\n\nfunction string(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) break;\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    if (!escaped) state.cur = normal;\n    return \"string\";\n  };\n}\n\nexport const lua = {\n  name: \"lua\",\n\n  startState: function() {\n    return {basecol: 0, indentDepth: 0, cur: normal};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = state.cur(stream, state);\n    var word = stream.current();\n    if (style == \"variable\") {\n      if (keywords.test(word)) style = \"keyword\";\n      else if (builtins.test(word)) style = \"builtin\";\n    }\n    if ((style != \"comment\") && (style != \"string\")){\n      if (indentTokens.test(word)) ++state.indentDepth;\n      else if (dedentTokens.test(word)) --state.indentDepth;\n    }\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var closing = dedentPartial.test(textAfter);\n    return state.basecol + cx.unit * (state.indentDepth - (closing ? 1 : 0));\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*(?:end|until|else|\\)|\\})$/,\n    commentTokens: {line: \"--\", block: {open: \"--[[\", close: \"]]--\"}}\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,SAAS,KAAK;IACrB,OAAO,IAAI,OAAO,SAAS,MAAM,IAAI,CAAC,OAAO,KAAK;AACpD;AACA,SAAS,OAAO,KAAK;IACnB,OAAO,IAAI,OAAO,SAAS,MAAM,IAAI,CAAC,OAAO,MAAM;AACrD;AAEA,kDAAkD;AAClD,IAAI,WAAW,OAAO;IACpB;IAAK;IAAW;IAAS;IAAiB;IAAS;IAAQ;IAAU;IAAe;IAAS;IAC7F;IAAW;IAAa;IAAS;IAAO;IAAQ;IAAQ;IAAQ;IAAW;IAAS;IAAS;IAC7F;IAAS;IAAU;IAAe;IAAW;IAAW;IAAO;IAAS;IAExE;IAAmB;IAAmB;IAAoB;IAAmB;IAAiB;IAE9F;IAAc;IAAgB;IAAgB;IAAgB;IAAiB;IAC/E;IAAoB;IAAmB;IAAgB;IAAgB;IAAiB;IACxF;IAAmB;IAEnB;IAAQ;IAAQ;IAAQ;IAAO;IAAO;IAAU;IAEhD;IAAW;IAAW;IAAW;IAAW;IAAU;IAAY;IAAW;IAAU;IAAY;IACnG;IAAY;IAAa;IAAU;IAEnC;IAAW;IAAY;IAAY;IAAY;IAAa;IAAY;IAAW;IAAY;IAC/F;IAAW;IAAa;IAAY;IAAa;IAAY;IAAa;IAAW;IAAa;IAClG;IAAW;IAAY;IAAU;IAAW;IAAW;IAAc;IAAkB;IAAW;IAClG;IAAY;IAAW;IAEvB;IAAW;IAAU;IAAc;IAAa;IAAU;IAAY;IAAY;IAAY;IAC9F;IAAU;IAEV;IAAgB;IAAiB;IAAkB;IAAkB;IAAe;IACpF;IAEA;IAAc;IAAc;IAAc;IAAc;IAAgB;IAAgB;IACxF;IAAa;IAAe;IAAe;IAAa;IAAiB;IAAa;IAEtF;IAAe;IAAe;IAAa;IAAe;CAC3D;AACD,IAAI,WAAW,OAAO;IAAC;IAAM;IAAQ;IAAS;IAAQ;IAAM;IAAM;IAAK;IAChD;IAAO;IAAY;IAAO;IAAM;IAAQ;IAAQ;IAChD;IAAS;IAAU;IAAS;IAAO;IAAM;CAAS;AAEzE,IAAI,eAAe,OAAO;IAAC;IAAY;IAAK;IAAS;IAAM;IAAO;CAAI;AACtE,IAAI,eAAe,OAAO;IAAC;IAAO;IAAS;IAAO;CAAI;AACtD,IAAI,gBAAgB,SAAS;IAAC;IAAO;IAAS;IAAO;IAAK;IAAQ;CAAS;AAE3E,SAAS,YAAY,MAAM;IACzB,IAAI,QAAQ;IACZ,MAAO,OAAO,GAAG,CAAC,KAAM,EAAE;IAC1B,OAAO,GAAG,CAAC;IACX,OAAO;AACT;AAEA,SAAS,OAAO,MAAM,EAAE,KAAK;IAC3B,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;QAChC,IAAI,OAAO,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,MAChC,OAAO,CAAC,MAAM,GAAG,GAAG,UAAU,YAAY,SAAS,UAAU,EAAE,QAAQ;QACzE,OAAO,SAAS;QAChB,OAAO;IACT;IACA,IAAI,MAAM,QAAQ,MAAM,KACtB,OAAO,CAAC,MAAM,GAAG,GAAG,OAAO,GAAG,EAAE,QAAQ;IAC1C,IAAI,MAAM,OAAO,QAAQ,IAAI,CAAC,OAAO,IAAI,KACvC,OAAO,CAAC,MAAM,GAAG,GAAG,UAAU,YAAY,SAAS,SAAS,EAAE,QAAQ;IACxE,IAAI,KAAK,IAAI,CAAC,KAAK;QACjB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,IAAI,QAAQ,IAAI,CAAC,KAAK;QACpB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,OAAO;AACT;AAEA,SAAS,UAAU,KAAK,EAAE,KAAK;IAC7B,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,SAAS,MAAM;QACnB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;YACnC,IAAI,UAAU,MAAM;gBAAC,IAAI,MAAM,KAAK,SAAS;YAAE,OAC1C,IAAI,MAAM,KAAK,EAAE;iBACjB,IAAI,MAAM,OAAO,UAAU,OAAO;gBAAE,MAAM,GAAG,GAAG;gBAAQ;YAAO,OAC/D,SAAS;QAChB;QACA,OAAO;IACT;AACF;AAEA,SAAS,OAAO,KAAK;IACnB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO;QACrB,MAAO,CAAC,KAAK,OAAO,IAAI,EAAE,KAAK,KAAM;YACnC,IAAI,MAAM,SAAS,CAAC,SAAS;YAC7B,UAAU,CAAC,WAAW,MAAM;QAC9B;QACA,IAAI,CAAC,SAAS,MAAM,GAAG,GAAG;QAC1B,OAAO;IACT;AACF;AAEO,MAAM,MAAM;IACjB,MAAM;IAEN,YAAY;QACV,OAAO;YAAC,SAAS;YAAG,aAAa;YAAG,KAAK;QAAM;IACjD;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,IAAI,QAAQ,MAAM,GAAG,CAAC,QAAQ;QAC9B,IAAI,OAAO,OAAO,OAAO;QACzB,IAAI,SAAS,YAAY;YACvB,IAAI,SAAS,IAAI,CAAC,OAAO,QAAQ;iBAC5B,IAAI,SAAS,IAAI,CAAC,OAAO,QAAQ;QACxC;QACA,IAAI,AAAC,SAAS,aAAe,SAAS,UAAU;YAC9C,IAAI,aAAa,IAAI,CAAC,OAAO,EAAE,MAAM,WAAW;iBAC3C,IAAI,aAAa,IAAI,CAAC,OAAO,EAAE,MAAM,WAAW;QACvD;QACA,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,EAAE;QACnC,IAAI,UAAU,cAAc,IAAI,CAAC;QACjC,OAAO,MAAM,OAAO,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,WAAW,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC;IACzE;IAEA,cAAc;QACZ,eAAe;QACf,eAAe;YAAC,MAAM;YAAM,OAAO;gBAAC,MAAM;gBAAQ,OAAO;YAAM;QAAC;IAClE;AACF", "ignoreList": [0], "debugId": null}}]}