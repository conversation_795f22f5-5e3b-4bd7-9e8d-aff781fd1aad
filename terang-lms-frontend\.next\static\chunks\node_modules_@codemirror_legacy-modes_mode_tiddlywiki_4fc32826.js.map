{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/tiddlywiki.js"], "sourcesContent": ["// Tokenizer\nvar textwords = {};\n\nvar keywords = {\n  \"allTags\": true, \"closeAll\": true, \"list\": true,\n  \"newJournal\": true, \"newTiddler\": true,\n  \"permaview\": true, \"saveChanges\": true,\n  \"search\": true, \"slider\": true, \"tabs\": true,\n  \"tag\": true, \"tagging\": true, \"tags\": true,\n  \"tiddler\": true, \"timeline\": true,\n  \"today\": true, \"version\": true, \"option\": true,\n  \"with\": true, \"filter\": true\n};\n\nvar isSpaceName = /[\\w_\\-]/i,\n    reHR = /^\\-\\-\\-\\-+$/,                                 // <hr>\n    reWikiCommentStart = /^\\/\\*\\*\\*$/,            // /***\n    reWikiCommentStop = /^\\*\\*\\*\\/$/,             // ***/\n    reBlockQuote = /^<<<$/,\n\n    reJsCodeStart = /^\\/\\/\\{\\{\\{$/,                       // //{{{ js block start\n    reJsCodeStop = /^\\/\\/\\}\\}\\}$/,                        // //}}} js stop\n    reXmlCodeStart = /^<!--\\{\\{\\{-->$/,           // xml block start\n    reXmlCodeStop = /^<!--\\}\\}\\}-->$/,            // xml stop\n\n    reCodeBlockStart = /^\\{\\{\\{$/,                        // {{{ TW text div block start\n    reCodeBlockStop = /^\\}\\}\\}$/,                 // }}} TW text stop\n\n    reUntilCodeStop = /.*?\\}\\}\\}/;\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\n\nfunction tokenBase(stream, state) {\n  var sol = stream.sol(), ch = stream.peek();\n\n  state.block = false;        // indicates the start of a code block.\n\n  // check start of  blocks\n  if (sol && /[<\\/\\*{}\\-]/.test(ch)) {\n    if (stream.match(reCodeBlockStart)) {\n      state.block = true;\n      return chain(stream, state, twTokenCode);\n    }\n    if (stream.match(reBlockQuote))\n      return 'quote';\n    if (stream.match(reWikiCommentStart) || stream.match(reWikiCommentStop))\n      return 'comment';\n    if (stream.match(reJsCodeStart) || stream.match(reJsCodeStop) || stream.match(reXmlCodeStart) || stream.match(reXmlCodeStop))\n      return 'comment';\n    if (stream.match(reHR))\n      return 'contentSeparator';\n  }\n\n  stream.next();\n  if (sol && /[\\/\\*!#;:>|]/.test(ch)) {\n    if (ch == \"!\") { // tw header\n      stream.skipToEnd();\n      return \"header\";\n    }\n    if (ch == \"*\") { // tw list\n      stream.eatWhile('*');\n      return \"comment\";\n    }\n    if (ch == \"#\") { // tw numbered list\n      stream.eatWhile('#');\n      return \"comment\";\n    }\n    if (ch == \";\") { // definition list, term\n      stream.eatWhile(';');\n      return \"comment\";\n    }\n    if (ch == \":\") { // definition list, description\n      stream.eatWhile(':');\n      return \"comment\";\n    }\n    if (ch == \">\") { // single line quote\n      stream.eatWhile(\">\");\n      return \"quote\";\n    }\n    if (ch == '|')\n      return 'header';\n  }\n\n  if (ch == '{' && stream.match('{{'))\n    return chain(stream, state, twTokenCode);\n\n  // rudimentary html:// file:// link matching. TW knows much more ...\n  if (/[hf]/i.test(ch) &&\n      /[ti]/i.test(stream.peek()) &&\n      stream.match(/\\b(ttps?|tp|ile):\\/\\/[\\-A-Z0-9+&@#\\/%?=~_|$!:,.;]*[A-Z0-9+&@#\\/%=~_|$]/i))\n    return \"link\";\n\n  // just a little string indicator, don't want to have the whole string covered\n  if (ch == '\"')\n    return 'string';\n\n  if (ch == '~')    // _no_ CamelCase indicator should be bold\n    return 'brace';\n\n  if (/[\\[\\]]/.test(ch) && stream.match(ch)) // check for [[..]]\n    return 'brace';\n\n  if (ch == \"@\") {    // check for space link. TODO fix @@...@@ highlighting\n    stream.eatWhile(isSpaceName);\n    return \"link\";\n  }\n\n  if (/\\d/.test(ch)) {        // numbers\n    stream.eatWhile(/\\d/);\n    return \"number\";\n  }\n\n  if (ch == \"/\") { // tw invisible comment\n    if (stream.eat(\"%\")) {\n      return chain(stream, state, twTokenComment);\n    } else if (stream.eat(\"/\")) { //\n      return chain(stream, state, twTokenEm);\n    }\n  }\n\n  if (ch == \"_\" && stream.eat(\"_\")) // tw underline\n    return chain(stream, state, twTokenUnderline);\n\n  // strikethrough and mdash handling\n  if (ch == \"-\" && stream.eat(\"-\")) {\n    // if strikethrough looks ugly, change CSS.\n    if (stream.peek() != ' ')\n      return chain(stream, state, twTokenStrike);\n    // mdash\n    if (stream.peek() == ' ')\n      return 'brace';\n  }\n\n  if (ch == \"'\" && stream.eat(\"'\")) // tw bold\n    return chain(stream, state, twTokenStrong);\n\n  if (ch == \"<\" && stream.eat(\"<\")) // tw macro\n    return chain(stream, state, twTokenMacro);\n\n  // core macro handling\n  stream.eatWhile(/[\\w\\$_]/);\n  return textwords.propertyIsEnumerable(stream.current()) ? \"keyword\" : null\n}\n\n// tw invisible comment\nfunction twTokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"%\");\n  }\n  return \"comment\";\n}\n\n// tw strong / bold\nfunction twTokenStrong(stream, state) {\n  var maybeEnd = false,\n      ch;\n  while (ch = stream.next()) {\n    if (ch == \"'\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"'\");\n  }\n  return \"strong\";\n}\n\n// tw code\nfunction twTokenCode(stream, state) {\n  var sb = state.block;\n\n  if (sb && stream.current()) {\n    return \"comment\";\n  }\n\n  if (!sb && stream.match(reUntilCodeStop)) {\n    state.tokenize = tokenBase;\n    return \"comment\";\n  }\n\n  if (sb && stream.sol() && stream.match(reCodeBlockStop)) {\n    state.tokenize = tokenBase;\n    return \"comment\";\n  }\n\n  stream.next();\n  return \"comment\";\n}\n\n// tw em / italic\nfunction twTokenEm(stream, state) {\n  var maybeEnd = false,\n      ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"/\");\n  }\n  return \"emphasis\";\n}\n\n// tw underlined text\nfunction twTokenUnderline(stream, state) {\n  var maybeEnd = false,\n      ch;\n  while (ch = stream.next()) {\n    if (ch == \"_\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"_\");\n  }\n  return \"link\";\n}\n\n// tw strike through text looks ugly\n// change CSS if needed\nfunction twTokenStrike(stream, state) {\n  var maybeEnd = false, ch;\n\n  while (ch = stream.next()) {\n    if (ch == \"-\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"-\");\n  }\n  return \"deleted\";\n}\n\n// macro\nfunction twTokenMacro(stream, state) {\n  if (stream.current() == '<<') {\n    return 'meta';\n  }\n\n  var ch = stream.next();\n  if (!ch) {\n    state.tokenize = tokenBase;\n    return null;\n  }\n  if (ch == \">\") {\n    if (stream.peek() == '>') {\n      stream.next();\n      state.tokenize = tokenBase;\n      return \"meta\";\n    }\n  }\n\n  stream.eatWhile(/[\\w\\$_]/);\n  return keywords.propertyIsEnumerable(stream.current()) ? \"keyword\" : null\n}\n\n// Interface\nexport const tiddlyWiki = {\n  name: \"tiddlywiki\",\n\n  startState: function () {\n    return {tokenize: tokenBase};\n  },\n\n  token: function (stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    return style;\n  }\n};\n\n"], "names": [], "mappings": "AAAA,YAAY;;;;AACZ,IAAI,YAAY,CAAC;AAEjB,IAAI,WAAW;IACb,WAAW;IAAM,YAAY;IAAM,QAAQ;IAC3C,cAAc;IAAM,cAAc;IAClC,aAAa;IAAM,eAAe;IAClC,UAAU;IAAM,UAAU;IAAM,QAAQ;IACxC,OAAO;IAAM,WAAW;IAAM,QAAQ;IACtC,WAAW;IAAM,YAAY;IAC7B,SAAS;IAAM,WAAW;IAAM,UAAU;IAC1C,QAAQ;IAAM,UAAU;AAC1B;AAEA,IAAI,cAAc,YACd,OAAO,eACP,qBAAqB,cACrB,oBAAoB,cACpB,eAAe,SAEf,gBAAgB,gBAChB,eAAe,gBACf,iBAAiB,mBACjB,gBAAgB,mBAEhB,mBAAmB,YACnB,kBAAkB,YAElB,kBAAkB;AAEtB,SAAS,MAAM,MAAM,EAAE,KAAK,EAAE,CAAC;IAC7B,MAAM,QAAQ,GAAG;IACjB,OAAO,EAAE,QAAQ;AACnB;AAEA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,MAAM,OAAO,GAAG,IAAI,KAAK,OAAO,IAAI;IAExC,MAAM,KAAK,GAAG,OAAc,uCAAuC;IAEnE,yBAAyB;IACzB,IAAI,OAAO,cAAc,IAAI,CAAC,KAAK;QACjC,IAAI,OAAO,KAAK,CAAC,mBAAmB;YAClC,MAAM,KAAK,GAAG;YACd,OAAO,MAAM,QAAQ,OAAO;QAC9B;QACA,IAAI,OAAO,KAAK,CAAC,eACf,OAAO;QACT,IAAI,OAAO,KAAK,CAAC,uBAAuB,OAAO,KAAK,CAAC,oBACnD,OAAO;QACT,IAAI,OAAO,KAAK,CAAC,kBAAkB,OAAO,KAAK,CAAC,iBAAiB,OAAO,KAAK,CAAC,mBAAmB,OAAO,KAAK,CAAC,gBAC5G,OAAO;QACT,IAAI,OAAO,KAAK,CAAC,OACf,OAAO;IACX;IAEA,OAAO,IAAI;IACX,IAAI,OAAO,eAAe,IAAI,CAAC,KAAK;QAClC,IAAI,MAAM,KAAK;YACb,OAAO,SAAS;YAChB,OAAO;QACT;QACA,IAAI,MAAM,KAAK;YACb,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,IAAI,MAAM,KAAK;YACb,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,IAAI,MAAM,KAAK;YACb,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,IAAI,MAAM,KAAK;YACb,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,IAAI,MAAM,KAAK;YACb,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,IAAI,MAAM,KACR,OAAO;IACX;IAEA,IAAI,MAAM,OAAO,OAAO,KAAK,CAAC,OAC5B,OAAO,MAAM,QAAQ,OAAO;IAE9B,oEAAoE;IACpE,IAAI,QAAQ,IAAI,CAAC,OACb,QAAQ,IAAI,CAAC,OAAO,IAAI,OACxB,OAAO,KAAK,CAAC,4EACf,OAAO;IAET,8EAA8E;IAC9E,IAAI,MAAM,KACR,OAAO;IAET,IAAI,MAAM,KACR,OAAO;IAET,IAAI,SAAS,IAAI,CAAC,OAAO,OAAO,KAAK,CAAC,KACpC,OAAO;IAET,IAAI,MAAM,KAAK;QACb,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IAEA,IAAI,KAAK,IAAI,CAAC,KAAK;QACjB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IAEA,IAAI,MAAM,KAAK;QACb,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO,MAAM,QAAQ,OAAO;QAC9B,OAAO,IAAI,OAAO,GAAG,CAAC,MAAM;YAC1B,OAAO,MAAM,QAAQ,OAAO;QAC9B;IACF;IAEA,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAC1B,OAAO,MAAM,QAAQ,OAAO;IAE9B,mCAAmC;IACnC,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAAM;QAChC,2CAA2C;QAC3C,IAAI,OAAO,IAAI,MAAM,KACnB,OAAO,MAAM,QAAQ,OAAO;QAC9B,QAAQ;QACR,IAAI,OAAO,IAAI,MAAM,KACnB,OAAO;IACX;IAEA,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAC1B,OAAO,MAAM,QAAQ,OAAO;IAE9B,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC,MAC1B,OAAO,MAAM,QAAQ,OAAO;IAE9B,sBAAsB;IACtB,OAAO,QAAQ,CAAC;IAChB,OAAO,UAAU,oBAAoB,CAAC,OAAO,OAAO,MAAM,YAAY;AACxE;AAEA,uBAAuB;AACvB,SAAS,eAAe,MAAM,EAAE,KAAK;IACnC,IAAI,WAAW,OAAO;IACtB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,mBAAmB;AACnB,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,IAAI,WAAW,OACX;IACJ,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,UAAU;AACV,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,IAAI,KAAK,MAAM,KAAK;IAEpB,IAAI,MAAM,OAAO,OAAO,IAAI;QAC1B,OAAO;IACT;IAEA,IAAI,CAAC,MAAM,OAAO,KAAK,CAAC,kBAAkB;QACxC,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT;IAEA,IAAI,MAAM,OAAO,GAAG,MAAM,OAAO,KAAK,CAAC,kBAAkB;QACvD,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT;IAEA,OAAO,IAAI;IACX,OAAO;AACT;AAEA,iBAAiB;AACjB,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,WAAW,OACX;IACJ,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,qBAAqB;AACrB,SAAS,iBAAiB,MAAM,EAAE,KAAK;IACrC,IAAI,WAAW,OACX;IACJ,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,oCAAoC;AACpC,uBAAuB;AACvB,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,IAAI,WAAW,OAAO;IAEtB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,QAAQ;AACR,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,OAAO,OAAO,MAAM,MAAM;QAC5B,OAAO;IACT;IAEA,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,CAAC,IAAI;QACP,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT;IACA,IAAI,MAAM,KAAK;QACb,IAAI,OAAO,IAAI,MAAM,KAAK;YACxB,OAAO,IAAI;YACX,MAAM,QAAQ,GAAG;YACjB,OAAO;QACT;IACF;IAEA,OAAO,QAAQ,CAAC;IAChB,OAAO,SAAS,oBAAoB,CAAC,OAAO,OAAO,MAAM,YAAY;AACvE;AAGO,MAAM,aAAa;IACxB,MAAM;IAEN,YAAY;QACV,OAAO;YAAC,UAAU;QAAS;IAC7B;IAEA,OAAO,SAAU,MAAM,EAAE,KAAK;QAC5B,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QACnC,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}