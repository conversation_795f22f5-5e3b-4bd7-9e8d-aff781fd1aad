{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/troff.js"], "sourcesContent": ["var words = {};\n\nfunction tokenBase(stream) {\n  if (stream.eatSpace()) return null;\n\n  var sol = stream.sol();\n  var ch = stream.next();\n\n  if (ch === '\\\\') {\n    if (stream.match('fB') || stream.match('fR') || stream.match('fI') ||\n        stream.match('u')  || stream.match('d')  ||\n        stream.match('%')  || stream.match('&')) {\n      return 'string';\n    }\n    if (stream.match('m[')) {\n      stream.skipTo(']');\n      stream.next();\n      return 'string';\n    }\n    if (stream.match('s+') || stream.match('s-')) {\n      stream.eatWhile(/[\\d-]/);\n      return 'string';\n    }\n    if (stream.match('\\(') || stream.match('*\\(')) {\n      stream.eatWhile(/[\\w-]/);\n      return 'string';\n    }\n    return 'string';\n  }\n  if (sol && (ch === '.' || ch === '\\'')) {\n    if (stream.eat('\\\\') && stream.eat('\\\"')) {\n      stream.skipToEnd();\n      return 'comment';\n    }\n  }\n  if (sol && ch === '.') {\n    if (stream.match('B ') || stream.match('I ') || stream.match('R ')) {\n      return 'attribute';\n    }\n    if (stream.match('TH ') || stream.match('SH ') || stream.match('SS ') || stream.match('HP ')) {\n      stream.skipToEnd();\n      return 'quote';\n    }\n    if ((stream.match(/[A-Z]/) && stream.match(/[A-Z]/)) || (stream.match(/[a-z]/) && stream.match(/[a-z]/))) {\n      return 'attribute';\n    }\n  }\n  stream.eatWhile(/[\\w-]/);\n  var cur = stream.current();\n  return words.hasOwnProperty(cur) ? words[cur] : null;\n}\n\nfunction tokenize(stream, state) {\n  return (state.tokens[0] || tokenBase) (stream, state);\n};\n\nexport const troff = {\n  name: \"troff\",\n  startState: function() {return {tokens:[]};},\n  token: function(stream, state) {\n    return tokenize(stream, state);\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,QAAQ,CAAC;AAEb,SAAS,UAAU,MAAM;IACvB,IAAI,OAAO,QAAQ,IAAI,OAAO;IAE9B,IAAI,MAAM,OAAO,GAAG;IACpB,IAAI,KAAK,OAAO,IAAI;IAEpB,IAAI,OAAO,MAAM;QACf,IAAI,OAAO,KAAK,CAAC,SAAS,OAAO,KAAK,CAAC,SAAS,OAAO,KAAK,CAAC,SACzD,OAAO,KAAK,CAAC,QAAS,OAAO,KAAK,CAAC,QACnC,OAAO,KAAK,CAAC,QAAS,OAAO,KAAK,CAAC,MAAM;YAC3C,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,OAAO;YACtB,OAAO,MAAM,CAAC;YACd,OAAO,IAAI;YACX,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,SAAS,OAAO,KAAK,CAAC,OAAO;YAC5C,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YAC7C,OAAO,QAAQ,CAAC;YAChB,OAAO;QACT;QACA,OAAO;IACT;IACA,IAAI,OAAO,CAAC,OAAO,OAAO,OAAO,IAAI,GAAG;QACtC,IAAI,OAAO,GAAG,CAAC,SAAS,OAAO,GAAG,CAAC,OAAO;YACxC,OAAO,SAAS;YAChB,OAAO;QACT;IACF;IACA,IAAI,OAAO,OAAO,KAAK;QACrB,IAAI,OAAO,KAAK,CAAC,SAAS,OAAO,KAAK,CAAC,SAAS,OAAO,KAAK,CAAC,OAAO;YAClE,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,UAAU,OAAO,KAAK,CAAC,UAAU,OAAO,KAAK,CAAC,UAAU,OAAO,KAAK,CAAC,QAAQ;YAC5F,OAAO,SAAS;YAChB,OAAO;QACT;QACA,IAAI,AAAC,OAAO,KAAK,CAAC,YAAY,OAAO,KAAK,CAAC,YAAc,OAAO,KAAK,CAAC,YAAY,OAAO,KAAK,CAAC,UAAW;YACxG,OAAO;QACT;IACF;IACA,OAAO,QAAQ,CAAC;IAChB,IAAI,MAAM,OAAO,OAAO;IACxB,OAAO,MAAM,cAAc,CAAC,OAAO,KAAK,CAAC,IAAI,GAAG;AAClD;AAEA,SAAS,SAAS,MAAM,EAAE,KAAK;IAC7B,OAAO,CAAC,MAAM,MAAM,CAAC,EAAE,IAAI,SAAS,EAAG,QAAQ;AACjD;;AAEO,MAAM,QAAQ;IACnB,MAAM;IACN,YAAY;QAAY,OAAO;YAAC,QAAO,EAAE;QAAA;IAAE;IAC3C,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,OAAO,SAAS,QAAQ;IAC1B;AACF", "ignoreList": [0], "debugId": null}}]}