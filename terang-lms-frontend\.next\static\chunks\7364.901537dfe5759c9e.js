try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="225477da-6af8-4a4d-8801-b6a4e250f975",e._sentryDebugIdIdentifier="sentry-dbid-225477da-6af8-4a4d-8801-b6a4e250f975")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7364],{47364:(e,a,t)=>{t.r(a),t.d(a,{default:()=>D});var r=t(95155);t(12115);var n=t(18720),o=t(43909),s=t(33289),d=t(31561),l=t(70062),i=t(43042),c=t(9813),p=t(4556),u=t(62331),f=t(2066),y=t(4831),g=t(90140),h=t(2806),m=t(45757),x=t(16178),b=t(43066),j=t(33045),w=t(75481),k=t(37796),_=t(5498),E=t(97325),C=t(65259),I=t(61981),S=t(71937);function D(e){let{markdown:a,onChange:t,placeholder:D="Enter your content here...",className:N="min-h-[200px]"}=e;return(0,r.jsx)(o.R,{markdown:a,onChange:t,contentEditableClassName:"prose max-w-none",plugins:[(0,s.xO)(),(0,d.Zq)(),(0,l.G)(),(0,i.Y)(),(0,c.r)(),(0,p.O)(),(0,u.Mi)(),(0,f.Pz)({imageUploadHandler:async e=>{try{let a=await fetch("/api/upload?filename=".concat(e.name),{method:"POST",body:e});if(!a.ok)throw Error("Upload failed");return(await a.json()).url}catch(e){return console.error("Image upload failed:",e),n.oR.error("Failed to upload image"),""}}}),(0,y.c3)(),(0,g.oe)({defaultCodeBlockLanguage:"javascript"}),(0,h.sl)({codeBlockLanguages:{javascript:"JavaScript",typescript:"TypeScript",python:"Python",html:"HTML",css:"CSS",json:"JSON",markdown:"Markdown"}}),(0,m.F7)({toolbarContents:()=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.l,{}),(0,r.jsx)(b.p,{}),(0,r.jsx)(j.S,{}),(0,r.jsx)(w._,{}),(0,r.jsx)(k.d,{}),(0,r.jsx)(_.N,{}),(0,r.jsx)(E.I,{}),(0,r.jsx)(C._,{}),(0,r.jsx)(I.a,{}),(0,r.jsx)(S.O,{})]})})],placeholder:D,className:N,"data-sentry-element":"MDXEditor","data-sentry-component":"MDXEditorWrapper","data-sentry-source-file":"mdx-editor-wrapper.tsx"})}}}]);