{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/z80.js"], "sourcesContent": ["function mkZ80(ez80) {\n  var keywords1, keywords2;\n  if (ez80) {\n    keywords1 = /^(exx?|(ld|cp)([di]r?)?|[lp]ea|pop|push|ad[cd]|cpl|daa|dec|inc|neg|sbc|sub|and|bit|[cs]cf|x?or|res|set|r[lr]c?a?|r[lr]d|s[lr]a|srl|djnz|nop|[de]i|halt|im|in([di]mr?|ir?|irx|2r?)|ot(dmr?|[id]rx|imr?)|out(0?|[di]r?|[di]2r?)|tst(io)?|slp)(\\.([sl]?i)?[sl])?\\b/i;\n    keywords2 = /^(((call|j[pr]|rst|ret[in]?)(\\.([sl]?i)?[sl])?)|(rs|st)mix)\\b/i;\n  } else {\n    keywords1 = /^(exx?|(ld|cp|in)([di]r?)?|pop|push|ad[cd]|cpl|daa|dec|inc|neg|sbc|sub|and|bit|[cs]cf|x?or|res|set|r[lr]c?a?|r[lr]d|s[lr]a|srl|djnz|nop|rst|[de]i|halt|im|ot[di]r|out[di]?)\\b/i;\n    keywords2 = /^(call|j[pr]|ret[in]?|b_?(call|jump))\\b/i;\n  }\n\n  var variables1 = /^(af?|bc?|c|de?|e|hl?|l|i[xy]?|r|sp)\\b/i;\n  var variables2 = /^(n?[zc]|p[oe]?|m)\\b/i;\n  var errors = /^([hl][xy]|i[xy][hl]|slia|sll)\\b/i;\n  var numbers = /^([\\da-f]+h|[0-7]+o|[01]+b|\\d+d?)\\b/i;\n\n  return {\n    name: \"z80\",\n    startState: function() {\n      return {\n        context: 0\n      };\n    },\n    token: function(stream, state) {\n      if (!stream.column())\n        state.context = 0;\n\n      if (stream.eatSpace())\n        return null;\n\n      var w;\n\n      if (stream.eatWhile(/\\w/)) {\n        if (ez80 && stream.eat('.')) {\n          stream.eatWhile(/\\w/);\n        }\n        w = stream.current();\n\n        if (stream.indentation()) {\n          if ((state.context == 1 || state.context == 4) && variables1.test(w)) {\n            state.context = 4;\n            return 'variable';\n          }\n\n          if (state.context == 2 && variables2.test(w)) {\n            state.context = 4;\n            return 'variableName.special';\n          }\n\n          if (keywords1.test(w)) {\n            state.context = 1;\n            return 'keyword';\n          } else if (keywords2.test(w)) {\n            state.context = 2;\n            return 'keyword';\n          } else if (state.context == 4 && numbers.test(w)) {\n            return 'number';\n          }\n\n          if (errors.test(w))\n            return 'error';\n        } else if (stream.match(numbers)) {\n          return 'number';\n        } else {\n          return null;\n        }\n      } else if (stream.eat(';')) {\n        stream.skipToEnd();\n        return 'comment';\n      } else if (stream.eat('\"')) {\n        while (w = stream.next()) {\n          if (w == '\"')\n            break;\n\n          if (w == '\\\\')\n            stream.next();\n        }\n        return 'string';\n      } else if (stream.eat('\\'')) {\n        if (stream.match(/\\\\?.'/))\n          return 'number';\n      } else if (stream.eat('.') || stream.sol() && stream.eat('#')) {\n        state.context = 5;\n\n        if (stream.eatWhile(/\\w/))\n          return 'def';\n      } else if (stream.eat('$')) {\n        if (stream.eatWhile(/[\\da-f]/i))\n          return 'number';\n      } else if (stream.eat('%')) {\n        if (stream.eatWhile(/[01]/))\n          return 'number';\n      } else {\n        stream.next();\n      }\n      return null;\n    }\n  };\n};\n\nexport const z80 = mkZ80(false)\nexport const ez80 = mkZ80(true)\n"], "names": [], "mappings": ";;;;AAAA,SAAS,MAAM,IAAI;IACjB,IAAI,WAAW;IACf,IAAI,MAAM;QACR,YAAY;QACZ,YAAY;IACd,OAAO;QACL,YAAY;QACZ,YAAY;IACd;IAEA,IAAI,aAAa;IACjB,IAAI,aAAa;IACjB,IAAI,SAAS;IACb,IAAI,UAAU;IAEd,OAAO;QACL,MAAM;QACN,YAAY;YACV,OAAO;gBACL,SAAS;YACX;QACF;QACA,OAAO,SAAS,MAAM,EAAE,KAAK;YAC3B,IAAI,CAAC,OAAO,MAAM,IAChB,MAAM,OAAO,GAAG;YAElB,IAAI,OAAO,QAAQ,IACjB,OAAO;YAET,IAAI;YAEJ,IAAI,OAAO,QAAQ,CAAC,OAAO;gBACzB,IAAI,QAAQ,OAAO,GAAG,CAAC,MAAM;oBAC3B,OAAO,QAAQ,CAAC;gBAClB;gBACA,IAAI,OAAO,OAAO;gBAElB,IAAI,OAAO,WAAW,IAAI;oBACxB,IAAI,CAAC,MAAM,OAAO,IAAI,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,IAAI;wBACpE,MAAM,OAAO,GAAG;wBAChB,OAAO;oBACT;oBAEA,IAAI,MAAM,OAAO,IAAI,KAAK,WAAW,IAAI,CAAC,IAAI;wBAC5C,MAAM,OAAO,GAAG;wBAChB,OAAO;oBACT;oBAEA,IAAI,UAAU,IAAI,CAAC,IAAI;wBACrB,MAAM,OAAO,GAAG;wBAChB,OAAO;oBACT,OAAO,IAAI,UAAU,IAAI,CAAC,IAAI;wBAC5B,MAAM,OAAO,GAAG;wBAChB,OAAO;oBACT,OAAO,IAAI,MAAM,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI;wBAChD,OAAO;oBACT;oBAEA,IAAI,OAAO,IAAI,CAAC,IACd,OAAO;gBACX,OAAO,IAAI,OAAO,KAAK,CAAC,UAAU;oBAChC,OAAO;gBACT,OAAO;oBACL,OAAO;gBACT;YACF,OAAO,IAAI,OAAO,GAAG,CAAC,MAAM;gBAC1B,OAAO,SAAS;gBAChB,OAAO;YACT,OAAO,IAAI,OAAO,GAAG,CAAC,MAAM;gBAC1B,MAAO,IAAI,OAAO,IAAI,GAAI;oBACxB,IAAI,KAAK,KACP;oBAEF,IAAI,KAAK,MACP,OAAO,IAAI;gBACf;gBACA,OAAO;YACT,OAAO,IAAI,OAAO,GAAG,CAAC,OAAO;gBAC3B,IAAI,OAAO,KAAK,CAAC,UACf,OAAO;YACX,OAAO,IAAI,OAAO,GAAG,CAAC,QAAQ,OAAO,GAAG,MAAM,OAAO,GAAG,CAAC,MAAM;gBAC7D,MAAM,OAAO,GAAG;gBAEhB,IAAI,OAAO,QAAQ,CAAC,OAClB,OAAO;YACX,OAAO,IAAI,OAAO,GAAG,CAAC,MAAM;gBAC1B,IAAI,OAAO,QAAQ,CAAC,aAClB,OAAO;YACX,OAAO,IAAI,OAAO,GAAG,CAAC,MAAM;gBAC1B,IAAI,OAAO,QAAQ,CAAC,SAClB,OAAO;YACX,OAAO;gBACL,OAAO,IAAI;YACb;YACA,OAAO;QACT;IACF;AACF;;AAEO,MAAM,MAAM,MAAM;AAClB,MAAM,OAAO,MAAM", "ignoreList": [0], "debugId": null}}]}