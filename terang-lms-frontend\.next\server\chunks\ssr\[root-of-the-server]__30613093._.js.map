{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card'\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-header'\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-title'\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-action'\r\n      className={cn(\r\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-content'\r\n      className={cn('px-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-footer'\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n))\r\nProgress.displayName = ProgressPrimitive.Root.displayName\r\n\r\nexport { Progress }"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot='label'\r\n      className={cn(\r\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<'textarea'>) {\r\n  return (\r\n    <textarea\r\n      data-slot='textarea'\r\n      className={cn(\r\n        'border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot='select' {...props} />;\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot='select-group' {...props} />;\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot='select-value' {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default';\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot='select-trigger'\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className='size-4 opacity-50' />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot='select-content'\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1'\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot='select-label'\r\n      className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot='select-item'\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className='absolute right-2 flex size-3.5 items-center justify-center'>\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className='size-4' />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot='select-separator'\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot='select-scroll-up-button'\r\n      className={cn(\r\n        'flex cursor-default items-center justify-center py-1',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className='size-4' />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot='select-scroll-down-button'\r\n      className={cn(\r\n        'flex cursor-default items-center justify-center py-1',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className='size-4' />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\r\n        destructive:\r\n          'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'span'> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'span';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='badge'\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/calendar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { DayPicker } from 'react-day-picker';\r\nimport type { ComponentProps } from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { buttonVariants } from '@/components/ui/button';\r\nimport { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons';\r\n\r\n// Custom icons that meet the DayPicker requirements\r\nconst LeftIcon = () => <ChevronLeftIcon className='size-4' />;\r\nconst RightIcon = () => <ChevronRightIcon className='size-4' />;\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: ComponentProps<typeof DayPicker>) {\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn('p-3', className)}\r\n      classNames={{\r\n        months: 'flex flex-col sm:flex-row gap-2',\r\n        month: 'flex flex-col gap-4',\r\n        caption: 'flex justify-center pt-1 relative items-center w-full',\r\n        caption_label: 'text-sm font-medium',\r\n        nav: 'flex items-center gap-1',\r\n        nav_button: cn(\r\n          buttonVariants({ variant: 'outline' }),\r\n          'size-7 bg-transparent p-0 opacity-50 hover:opacity-100'\r\n        ),\r\n        nav_button_previous: 'absolute left-1',\r\n        nav_button_next: 'absolute right-1',\r\n        table: 'w-full border-collapse space-x-1',\r\n        head_row: 'flex',\r\n        head_cell:\r\n          'text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]',\r\n        row: 'flex w-full mt-2',\r\n        cell: cn(\r\n          'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md',\r\n          props.mode === 'range'\r\n            ? '[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md'\r\n            : '[&:has([aria-selected])]:rounded-md'\r\n        ),\r\n        day: cn(\r\n          buttonVariants({ variant: 'ghost' }),\r\n          'size-8 p-0 font-normal aria-selected:opacity-100'\r\n        ),\r\n        day_range_start:\r\n          'day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground',\r\n        day_range_end:\r\n          'day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground',\r\n        day_selected:\r\n          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',\r\n        day_today: 'bg-accent text-accent-foreground',\r\n        day_outside:\r\n          'day-outside text-muted-foreground aria-selected:text-muted-foreground',\r\n        day_disabled: 'text-muted-foreground opacity-50',\r\n        day_range_middle:\r\n          'aria-selected:bg-accent aria-selected:text-accent-foreground',\r\n        day_hidden: 'invisible',\r\n        ...classNames\r\n      }}\r\n      components={{\r\n        IconLeft: LeftIcon,\r\n        IconRight: RightIcon\r\n      }}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Calendar };\r\n"], "names": [], "mappings": ";;;;AAGA;AAGA;AACA;AACA;AARA;;;;;;AAUA,oDAAoD;AACpD,MAAM,WAAW,kBAAM,8OAAC,gLAAA,CAAA,kBAAe;QAAC,WAAU;;;;;;AAClD,MAAM,YAAY,kBAAM,8OAAC,gLAAA,CAAA,mBAAgB;QAAC,WAAU;;;;;;AAEpD,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OAC8B;IACjC,qBACE,8OAAC,8JAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YACL,MAAM,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACL,mKACA,MAAM,IAAI,KAAK,UACX,yKACA;YAEN,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,iBACE;YACF,eACE;YACF,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU;YACV,WAAW;QACb;QACC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot='popover' {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot='popover-trigger' {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot='popover-content'\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot='popover-anchor' {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 616, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/checkbox.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as CheckboxPrimitive from '@radix-ui/react-checkbox';\r\nimport { CheckIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Checkbox({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\r\n  return (\r\n    <CheckboxPrimitive.Root\r\n      data-slot='checkbox'\r\n      className={cn(\r\n        'peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <CheckboxPrimitive.Indicator\r\n        data-slot='checkbox-indicator'\r\n        className='flex items-center justify-center text-current transition-none'\r\n      >\r\n        <CheckIcon className='size-3.5' />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>\r\n  );\r\n}\r\n\r\nexport { Checkbox };\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/course/steps/basic-info-step.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { format } from 'date-fns';\r\nimport { id } from 'date-fns/locale';\r\nimport { CalendarIcon, Upload, X, Shuffle, Info } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { CourseData } from '../course-creation-wizard';\r\nimport { toast } from 'sonner';\r\n\r\ninterface BasicInfoStepProps {\r\n  data: CourseData;\r\n  onUpdate: (updates: Partial<CourseData>) => void;\r\n}\r\n\r\nexport function BasicInfoStep({ data, onUpdate }: BasicInfoStepProps) {\r\n  const [isGeneratingCode, setIsGeneratingCode] = useState(false);\r\n  const [dateRangeEnabled, setDateRangeEnabled] = useState(Boolean(data.startDate || data.endDate));\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const generateCourseCode = () => {\r\n    setIsGeneratingCode(true);\r\n    // Simulate API call\r\n    setTimeout(() => {\r\n      const code = Math.random().toString(36).substring(2, 8).toUpperCase();\r\n      onUpdate({ courseCode: code });\r\n      setIsGeneratingCode(false);\r\n      toast.success('Kode course berhasil dibuat');\r\n    }, 1000);\r\n  };\r\n\r\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    // Validate file type\r\n    if (!file.type.startsWith('image/')) {\r\n      toast.error('File harus berupa gambar');\r\n      return;\r\n    }\r\n\r\n    // Validate file size (max 5MB)\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      toast.error('Ukuran file maksimal 5MB');\r\n      return;\r\n    }\r\n\r\n    // Create preview URL\r\n    const previewUrl = URL.createObjectURL(file);\r\n    onUpdate({ \r\n      coverImage: file, \r\n      coverImagePreview: previewUrl \r\n    });\r\n    toast.success('Gambar berhasil diunggah');\r\n  };\r\n\r\n  const removeCoverImage = () => {\r\n    if (data.coverImagePreview) {\r\n      URL.revokeObjectURL(data.coverImagePreview);\r\n    }\r\n    onUpdate({ \r\n      coverImage: undefined, \r\n      coverImagePreview: undefined \r\n    });\r\n  };\r\n\r\n  const handleEnrollmentTypeChange = (value: 'code' | 'invitation' | 'both' | 'purchase') => {\r\n    const updates: Partial<CourseData> = { enrollmentType: value };\r\n    \r\n    // Auto-set default currency when switching to purchase/both\r\n    if ((value === 'purchase' || value === 'both') && !data.currency) {\r\n      updates.currency = 'IDR';\r\n    }\r\n    \r\n    onUpdate(updates);\r\n  };\r\n\r\n  const handleDateRangeToggle = (checked: boolean) => {\r\n    setDateRangeEnabled(checked);\r\n    if (!checked) {\r\n      onUpdate({ startDate: null, endDate: null });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Course Name */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"courseName\">Nama Course *</Label>\r\n        <Input\r\n          id=\"courseName\"\r\n          placeholder=\"Masukkan nama course\"\r\n          value={data.name}\r\n          onChange={(e) => onUpdate({ name: e.target.value })}\r\n        />\r\n      </div>\r\n\r\n      {/* Instructor */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"instructor\">Nama Instruktur *</Label>\r\n        <Input\r\n          id=\"instructor\"\r\n          placeholder=\"Masukkan nama instruktur\"\r\n          value={data.instructor}\r\n          onChange={(e) => onUpdate({ instructor: e.target.value })}\r\n        />\r\n      </div>\r\n\r\n      {/* Course Code */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"courseCode\">Kode Course *</Label>\r\n        <div className=\"flex space-x-2\">\r\n          <Input\r\n            id=\"courseCode\"\r\n            placeholder=\"Kode unik untuk course\"\r\n            value={data.courseCode}\r\n            onChange={(e) => onUpdate({ courseCode: e.target.value.toUpperCase() })}\r\n            className=\"flex-1\"\r\n          />\r\n          <Button \r\n            type=\"button\" \r\n            variant=\"outline\" \r\n            onClick={generateCourseCode}\r\n            disabled={isGeneratingCode}\r\n          >\r\n            <Shuffle className=\"w-4 h-4 mr-2\" />\r\n            {isGeneratingCode ? 'Membuat...' : 'Generate'}\r\n          </Button>\r\n        </div>\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Kode ini akan digunakan siswa untuk mendaftar ke course\r\n        </p>\r\n      </div>\r\n\r\n      {/* Description */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"description\">Deskripsi Course *</Label>\r\n        <Textarea\r\n          id=\"description\"\r\n          placeholder=\"Jelaskan tentang course ini...\"\r\n          value={data.description}\r\n          onChange={(e) => onUpdate({ description: e.target.value })}\r\n          rows={4}\r\n        />\r\n      </div>\r\n\r\n      {/* Cover Image */}\r\n      <div className=\"space-y-2\">\r\n        <Label>Cover Image</Label>\r\n        {data.coverImagePreview ? (\r\n          <div className=\"relative\">\r\n            <img\r\n              src={data.coverImagePreview}\r\n              alt=\"Course cover\"\r\n              className=\"w-full h-auto object-cover rounded-md aspect-video\"\r\n            />\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"destructive\"\r\n              size=\"sm\"\r\n              className=\"absolute top-2 right-2\"\r\n              onClick={removeCoverImage}\r\n            >\r\n              <X className=\"w-4 h-4\" />\r\n            </Button>\r\n          </div>\r\n        ) : (\r\n          <div\r\n            className=\"border-2 border-dashed border-muted-foreground/25 rounded-md p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors aspect-video flex flex-col items-center justify-center\"\r\n            onClick={() => fileInputRef.current?.click()}\r\n          >\r\n            <Upload className=\"w-8 h-8 mx-auto mb-2 text-muted-foreground\" />\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              Klik untuk upload cover image\r\n            </p>\r\n            <p className=\"text-xs text-muted-foreground mt-1\">\r\n              PNG, JPG hingga 5MB\r\n            </p>\r\n          </div>\r\n        )}\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={handleImageUpload}\r\n          className=\"hidden\"\r\n        />\r\n      </div>\r\n\r\n      {/* Course Type */}\r\n      <div className=\"space-y-2\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Label>Tipe Course *</Label>\r\n          <Popover>\r\n            <PopoverTrigger asChild>\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"h-auto p-1\">\r\n                <Info className=\"h-4 w-4 text-muted-foreground\" />\r\n              </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"w-96\" align=\"start\">\r\n              <div className=\"space-y-4\">\r\n                <h4 className=\"font-medium text-sm\">Informasi Tipe Course</h4>\r\n                <div className=\"grid grid-cols-1 gap-4\">\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Badge variant=\"secondary\">Self-paced</Badge>\r\n                    </div>\r\n                    <ul className=\"text-xs text-muted-foreground space-y-1\">\r\n                      <li>• Siswa belajar dengan kecepatan sendiri</li>\r\n                      <li>• Tidak ada deadline ketat</li>\r\n                      <li>• Akses selamanya setelah enrollment</li>\r\n                      <li>• Cocok untuk pembelajaran mandiri</li>\r\n                    </ul>\r\n                  </div>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Badge variant=\"default\">Verified</Badge>\r\n                    </div>\r\n                    <ul className=\"text-xs text-muted-foreground space-y-1\">\r\n                      <li>• Course dengan jadwal dan deadline</li>\r\n                      <li>• Sertifikat resmi setelah selesai</li>\r\n                      <li>• Monitoring progress lebih ketat</li>\r\n                      <li>• Cocok untuk pembelajaran formal</li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </PopoverContent>\r\n          </Popover>\r\n        </div>\r\n        <Select\r\n          value={data.type}\r\n          onValueChange={(value: 'self_paced' | 'verified') => onUpdate({ type: value })}\r\n        >\r\n          <SelectTrigger>\r\n            <SelectValue />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"self_paced\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"secondary\">Self-paced</Badge>\r\n                <span>Siswa belajar dengan kecepatan sendiri</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"verified\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"default\">Verified</Badge>\r\n                <span>Course dengan jadwal dan deadline</span>\r\n              </div>\r\n            </SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n\r\n      {/* Enrollment Type */}\r\n      <div className=\"space-y-2\">\r\n        <Label>Tipe Pendaftaran *</Label>\r\n        <Select\r\n          value={data.enrollmentType}\r\n          onValueChange={handleEnrollmentTypeChange}\r\n        >\r\n          <SelectTrigger>\r\n            <SelectValue />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"code\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"outline\">Kode</Badge>\r\n                <span>Siswa mendaftar dengan kode</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"invitation\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"outline\">Undangan</Badge>\r\n                <span>Hanya dengan undangan</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"both\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"outline\">Keduanya</Badge>\r\n                <span>Kode atau undangan</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"purchase\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"default\">Berbayar</Badge>\r\n                <span>Siswa harus membeli</span>\r\n              </div>\r\n            </SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n\r\n      {/* Price and Currency (only for purchase/both enrollment type) */}\r\n      {(data.enrollmentType === 'purchase' || data.enrollmentType === 'both') && (\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n          <div className=\"space-y-2 md:col-span-2\">\r\n            <Label htmlFor=\"price\">Harga *</Label>\r\n            <Input\r\n              id=\"price\"\r\n              type=\"number\"\r\n              placeholder=\"0\"\r\n              value={data.price || ''}\r\n              onChange={(e) => onUpdate({ price: parseFloat(e.target.value) || 0 })}\r\n              min=\"0\"\r\n              step=\"1000\"\r\n            />\r\n          </div>\r\n          <div className=\"space-y-2\">\r\n            <Label>Mata Uang *</Label>\r\n            <Select\r\n              value={data.currency || 'IDR'}\r\n              onValueChange={(value) => onUpdate({ currency: value })}\r\n            >\r\n              <SelectTrigger>\r\n                <SelectValue />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"IDR\">IDR (Rupiah)</SelectItem>\r\n                <SelectItem value=\"USD\">USD (Dollar)</SelectItem>\r\n                <SelectItem value=\"EUR\">EUR (Euro)</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Date Range */}\r\n      <div className=\"space-y-2\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Checkbox\r\n            id=\"enableDateRange\"\r\n            checked={dateRangeEnabled}\r\n            onCheckedChange={handleDateRangeToggle}\r\n          />\r\n          <Label htmlFor=\"enableDateRange\">Atur Tanggal Mulai & Selesai</Label>\r\n        </div>\r\n        {dateRangeEnabled && (\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label>Tanggal Mulai</Label>\r\n              <Popover>\r\n                <PopoverTrigger asChild>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className={cn(\r\n                      \"w-full justify-start text-left font-normal\",\r\n                      !data.startDate && \"text-muted-foreground\"\r\n                    )}\r\n                  >\r\n                    <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                    {data.startDate ? (\r\n                      format(data.startDate, \"PPP\", { locale: id })\r\n                    ) : (\r\n                      \"Pilih tanggal mulai\"\r\n                    )}\r\n                  </Button>\r\n                </PopoverTrigger>\r\n                <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                  <Calendar\r\n                    mode=\"single\"\r\n                    selected={data.startDate || undefined}\r\n                    onSelect={(date) => onUpdate({ startDate: date })}\r\n                    disabled={(date) => date < new Date()}\r\n                    initialFocus\r\n                  />\r\n                </PopoverContent>\r\n              </Popover>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <Label>Tanggal Selesai</Label>\r\n              <Popover>\r\n                <PopoverTrigger asChild>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className={cn(\r\n                      \"w-full justify-start text-left font-normal\",\r\n                      !data.endDate && \"text-muted-foreground\"\r\n                    )}\r\n                  >\r\n                    <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                    {data.endDate ? (\r\n                      format(data.endDate, \"PPP\", { locale: id })\r\n                    ) : (\r\n                      \"Pilih tanggal selesai\"\r\n                    )}\r\n                  </Button>\r\n                </PopoverTrigger>\r\n                <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                  <Calendar\r\n                    mode=\"single\"\r\n                    selected={data.endDate || undefined}\r\n                    onSelect={(date) => onUpdate({ endDate: date })}\r\n                    disabled={(date) => Boolean(date < new Date() || (data.startDate && date <= data.startDate))}\r\n                    initialFocus\r\n                  />\r\n                </PopoverContent>\r\n              </Popover>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAlBA;;;;;;;;;;;;;;;;;AAyBO,SAAS,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAsB;IAClE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,KAAK,SAAS,IAAI,KAAK,OAAO;IAC/F,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,qBAAqB;QACzB,oBAAoB;QACpB,oBAAoB;QACpB,WAAW;YACT,MAAM,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW;YACnE,SAAS;gBAAE,YAAY;YAAK;YAC5B,oBAAoB;YACpB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,GAAG;IACL;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,+BAA+B;QAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,qBAAqB;QACrB,MAAM,aAAa,IAAI,eAAe,CAAC;QACvC,SAAS;YACP,YAAY;YACZ,mBAAmB;QACrB;QACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,mBAAmB;QACvB,IAAI,KAAK,iBAAiB,EAAE;YAC1B,IAAI,eAAe,CAAC,KAAK,iBAAiB;QAC5C;QACA,SAAS;YACP,YAAY;YACZ,mBAAmB;QACrB;IACF;IAEA,MAAM,6BAA6B,CAAC;QAClC,MAAM,UAA+B;YAAE,gBAAgB;QAAM;QAE7D,4DAA4D;QAC5D,IAAI,CAAC,UAAU,cAAc,UAAU,MAAM,KAAK,CAAC,KAAK,QAAQ,EAAE;YAChE,QAAQ,QAAQ,GAAG;QACrB;QAEA,SAAS;IACX;IAEA,MAAM,wBAAwB,CAAC;QAC7B,oBAAoB;QACpB,IAAI,CAAC,SAAS;YACZ,SAAS;gBAAE,WAAW;gBAAM,SAAS;YAAK;QAC5C;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAa;;;;;;kCAC5B,8OAAC,iIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,aAAY;wBACZ,OAAO,KAAK,IAAI;wBAChB,UAAU,CAAC,IAAM,SAAS;gCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4BAAC;;;;;;;;;;;;0BAKrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAa;;;;;;kCAC5B,8OAAC,iIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,aAAY;wBACZ,OAAO,KAAK,UAAU;wBACtB,UAAU,CAAC,IAAM,SAAS;gCAAE,YAAY,EAAE,MAAM,CAAC,KAAK;4BAAC;;;;;;;;;;;;0BAK3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAa;;;;;;kCAC5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO,KAAK,UAAU;gCACtB,UAAU,CAAC,IAAM,SAAS;wCAAE,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;oCAAG;gCACrE,WAAU;;;;;;0CAEZ,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;;kDAEV,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAClB,mBAAmB,eAAe;;;;;;;;;;;;;kCAGvC,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAM/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAc;;;;;;kCAC7B,8OAAC,oIAAA,CAAA,WAAQ;wBACP,IAAG;wBACH,aAAY;wBACZ,OAAO,KAAK,WAAW;wBACvB,UAAU,CAAC,IAAM,SAAS;gCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACxD,MAAM;;;;;;;;;;;;0BAKV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;kCAAC;;;;;;oBACN,KAAK,iBAAiB,iBACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,KAAK,KAAK,iBAAiB;gCAC3B,KAAI;gCACJ,WAAU;;;;;;0CAEZ,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;6CAIjB,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,aAAa,OAAO,EAAE;;0CAErC,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAG7C,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;kCAKtD,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAO;wBACP,UAAU;wBACV,WAAU;;;;;;;;;;;;0BAKd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC,mIAAA,CAAA,UAAO;;kDACN,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGpB,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,WAAU;wCAAO,OAAM;kDACrC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAY;;;;;;;;;;;8EAE7B,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;sFAAG;;;;;;sFACJ,8OAAC;sFAAG;;;;;;sFACJ,8OAAC;sFAAG;;;;;;sFACJ,8OAAC;sFAAG;;;;;;;;;;;;;;;;;;sEAGR,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAU;;;;;;;;;;;8EAE3B,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;sFAAG;;;;;;sFACJ,8OAAC;sFAAG;;;;;;sFACJ,8OAAC;sFAAG;;;;;;sFACJ,8OAAC;sFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlB,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO,KAAK,IAAI;wBAChB,eAAe,CAAC,QAAqC,SAAS;gCAAE,MAAM;4BAAM;;0CAE5E,8OAAC,kIAAA,CAAA,gBAAa;0CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;kCAAC;;;;;;kCACP,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO,KAAK,cAAc;wBAC1B,eAAe;;0CAEf,8OAAC,kIAAA,CAAA,gBAAa;0CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQf,CAAC,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,MAAM,mBACpE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAQ;;;;;;0CACvB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO,KAAK,KAAK,IAAI;gCACrB,UAAU,CAAC,IAAM,SAAS;wCAAE,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oCAAE;gCACnE,KAAI;gCACJ,MAAK;;;;;;;;;;;;kCAGT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO,KAAK,QAAQ,IAAI;gCACxB,eAAe,CAAC,QAAU,SAAS;wCAAE,UAAU;oCAAM;;kDAErD,8OAAC,kIAAA,CAAA,gBAAa;kDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oIAAA,CAAA,WAAQ;gCACP,IAAG;gCACH,SAAS;gCACT,iBAAiB;;;;;;0CAEnB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAkB;;;;;;;;;;;;oBAElC,kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,KAAK,SAAS,IAAI;;sEAGrB,8OAAC,8MAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDACvB,KAAK,SAAS,GACb,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,SAAS,EAAE,OAAO;4DAAE,QAAQ,2IAAA,CAAA,KAAE;wDAAC,KAE3C;;;;;;;;;;;;0DAIN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,WAAU;gDAAa,OAAM;0DAC3C,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oDACP,MAAK;oDACL,UAAU,KAAK,SAAS,IAAI;oDAC5B,UAAU,CAAC,OAAS,SAAS;4DAAE,WAAW;wDAAK;oDAC/C,UAAU,CAAC,OAAS,OAAO,IAAI;oDAC/B,YAAY;;;;;;;;;;;;;;;;;;;;;;;0CAMpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,KAAK,OAAO,IAAI;;sEAGnB,8OAAC,8MAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDACvB,KAAK,OAAO,GACX,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,OAAO,EAAE,OAAO;4DAAE,QAAQ,2IAAA,CAAA,KAAE;wDAAC,KAEzC;;;;;;;;;;;;0DAIN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,WAAU;gDAAa,OAAM;0DAC3C,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oDACP,MAAK;oDACL,UAAU,KAAK,OAAO,IAAI;oDAC1B,UAAU,CAAC,OAAS,SAAS;4DAAE,SAAS;wDAAK;oDAC7C,UAAU,CAAC,OAAS,QAAQ,OAAO,IAAI,UAAW,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS;oDAC1F,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhC", "debugId": null}}, {"offset": {"line": 1806, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/switch.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SwitchPrimitive from '@radix-ui/react-switch';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Switch({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\r\n  return (\r\n    <SwitchPrimitive.Root\r\n      data-slot='switch'\r\n      className={cn(\r\n        'peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SwitchPrimitive.Thumb\r\n        data-slot='switch-thumb'\r\n        className={cn(\r\n          'bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0'\r\n        )}\r\n      />\r\n    </SwitchPrimitive.Root>\r\n  );\r\n}\r\n\r\nexport { Switch };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,kKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1842, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot='dialog' {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot='dialog-trigger' {...props} />;\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot='dialog-portal' {...props} />;\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot='dialog-close' {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot='dialog-overlay'\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot='dialog-portal'>\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot='dialog-content'\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className='sr-only'>Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  );\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='dialog-header'\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='dialog-footer'\r\n      className={cn(\r\n        'flex flex-col-reverse gap-2 sm:flex-row sm:justify-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot='dialog-title'\r\n      className={cn('text-lg leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot='dialog-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2015, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { buttonVariants } from '@/components/ui/button';\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot='alert-dialog' {...props} />;\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot='alert-dialog-trigger' {...props} />\r\n  );\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot='alert-dialog-portal' {...props} />\r\n  );\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot='alert-dialog-overlay'\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot='alert-dialog-content'\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  );\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='alert-dialog-header'\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='alert-dialog-footer'\r\n      className={cn(\r\n        'flex flex-col-reverse gap-2 sm:flex-row sm:justify-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot='alert-dialog-title'\r\n      className={cn('text-lg font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot='alert-dialog-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: 'outline' }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,2KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,2KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/course/steps/module-structure-step.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { <PERSON><PERSON> } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\r\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';\r\nimport { \r\n  Plus, \r\n  GripVertical, \r\n  Edit, \r\n  Trash2, \r\n  <PERSON><PERSON><PERSON>, \r\n  FileText, \r\n  HelpCircle,\r\n  ChevronDown,\r\n  ChevronRight\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { CourseData, ModuleData, ChapterData } from '../course-creation-wizard';\r\nimport { toast } from 'sonner';\r\n\r\ninterface ModuleStructureStepProps {\r\n  data: CourseData;\r\n  onUpdate: (updates: Partial<CourseData>) => void;\r\n}\r\n\r\nexport function ModuleStructureStep({ data, onUpdate }: ModuleStructureStepProps) {\r\n  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());\r\n  const [editingModule, setEditingModule] = useState<ModuleData | null>(null);\r\n  const [editingChapter, setEditingChapter] = useState<{ moduleId: string; chapter: ChapterData | null }>({\r\n    moduleId: '',\r\n    chapter: null\r\n  });\r\n  const [isModuleDialogOpen, setIsModuleDialogOpen] = useState(false);\r\n  const [isChapterDialogOpen, setIsChapterDialogOpen] = useState(false);\r\n\r\n  const toggleModuleExpansion = (moduleId: string) => {\r\n    const newExpanded = new Set(expandedModules);\r\n    if (newExpanded.has(moduleId)) {\r\n      newExpanded.delete(moduleId);\r\n    } else {\r\n      newExpanded.add(moduleId);\r\n    }\r\n    setExpandedModules(newExpanded);\r\n  };\r\n\r\n  const createNewModule = () => {\r\n    const newModule: ModuleData = {\r\n      id: `module-${Date.now()}`,\r\n      name: '',\r\n      description: '',\r\n      orderIndex: data.modules.length,\r\n      chapters: [],\r\n      hasModuleQuiz: false\r\n    };\r\n    setEditingModule(newModule);\r\n    setIsModuleDialogOpen(true);\r\n  };\r\n\r\n  const editModule = (moduleItem: ModuleData) => {\r\n    setEditingModule({ ...moduleItem });\r\n    setIsModuleDialogOpen(true);\r\n  };\r\n\r\n  const saveModule = () => {\r\n    if (!editingModule || !editingModule.name.trim()) {\r\n      toast.error('Nama modul harus diisi');\r\n      return;\r\n    }\r\n\r\n    const updatedModules = [...data.modules];\r\n    const existingIndex = updatedModules.findIndex(m => m.id === editingModule.id);\r\n    \r\n    if (existingIndex >= 0) {\r\n      updatedModules[existingIndex] = editingModule;\r\n      toast.success('Modul berhasil diperbarui');\r\n    } else {\r\n      updatedModules.push(editingModule);\r\n      toast.success('Modul berhasil ditambahkan');\r\n    }\r\n\r\n    onUpdate({ modules: updatedModules });\r\n    setIsModuleDialogOpen(false);\r\n    setEditingModule(null);\r\n  };\r\n\r\n  const deleteModule = (moduleId: string) => {\r\n    const updatedModules = data.modules\r\n      .filter(m => m.id !== moduleId)\r\n      .map((m, index) => ({ ...m, orderIndex: index }));\r\n    \r\n    onUpdate({ modules: updatedModules });\r\n    toast.success('Modul berhasil dihapus');\r\n  };\r\n\r\n  const createNewChapter = (moduleId: string) => {\r\n    const moduleItem = data.modules.find(m => m.id === moduleId);\r\n    if (!moduleItem) return;\r\n\r\n    const newChapter: ChapterData = {\r\n      id: `chapter-${Date.now()}`,\r\n      name: '',\r\n      content: [],\r\n      orderIndex: moduleItem.chapters.length,\r\n      hasChapterQuiz: false\r\n    };\r\n    \r\n    setEditingChapter({ moduleId, chapter: newChapter });\r\n    setIsChapterDialogOpen(true);\r\n  };\r\n\r\n  const editChapter = (moduleId: string, chapter: ChapterData) => {\r\n    setEditingChapter({ moduleId, chapter: { ...chapter } });\r\n    setIsChapterDialogOpen(true);\r\n  };\r\n\r\n  const saveChapter = () => {\r\n    if (!editingChapter.chapter || !editingChapter.chapter.name.trim()) {\r\n      toast.error('Nama chapter harus diisi');\r\n      return;\r\n    }\r\n\r\n    const updatedModules = data.modules.map(moduleItem => {\r\n      if (moduleItem.id === editingChapter.moduleId) {\r\n        const updatedChapters = [...moduleItem.chapters];\r\n        const existingIndex = updatedChapters.findIndex(c => c.id === editingChapter.chapter!.id);\r\n        \r\n        if (existingIndex >= 0) {\r\n          updatedChapters[existingIndex] = editingChapter.chapter!;\r\n        } else {\r\n          updatedChapters.push(editingChapter.chapter!);\r\n        }\r\n        \r\n        return { ...moduleItem, chapters: updatedChapters };\r\n      }\r\n      return moduleItem;\r\n    });\r\n\r\n    onUpdate({ modules: updatedModules });\r\n    setIsChapterDialogOpen(false);\r\n    setEditingChapter({ moduleId: '', chapter: null });\r\n    toast.success('Chapter berhasil disimpan');\r\n  };\r\n\r\n  const deleteChapter = (moduleId: string, chapterId: string) => {\r\n    const updatedModules = data.modules.map(moduleItem => {\r\n      if (moduleItem.id === moduleId) {\r\n        const updatedChapters = moduleItem.chapters\r\n          .filter(c => c.id !== chapterId)\r\n          .map((c, index) => ({ ...c, orderIndex: index }));\r\n        return { ...moduleItem, chapters: updatedChapters };\r\n      }\r\n      return moduleItem;\r\n    });\r\n\r\n    onUpdate({ modules: updatedModules });\r\n    toast.success('Chapter berhasil dihapus');\r\n  };\r\n\r\n  const moveModule = (moduleId: string, direction: 'up' | 'down') => {\r\n    const currentIndex = data.modules.findIndex(m => m.id === moduleId);\r\n    if (currentIndex === -1) return;\r\n\r\n    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;\r\n    if (newIndex < 0 || newIndex >= data.modules.length) return;\r\n\r\n    const updatedModules = [...data.modules];\r\n    [updatedModules[currentIndex], updatedModules[newIndex]] = \r\n    [updatedModules[newIndex], updatedModules[currentIndex]];\r\n    \r\n    // Update order indices\r\n    updatedModules.forEach((moduleItem, index) => {\r\n      moduleItem.orderIndex = index;\r\n    });\r\n\r\n    onUpdate({ modules: updatedModules });\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold\">Struktur Modul Course</h3>\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Buat modul dan chapter untuk mengorganisir konten course\r\n          </p>\r\n        </div>\r\n        <Button onClick={createNewModule}>\r\n          <Plus className=\"w-4 h-4 mr-2\" />\r\n          Tambah Modul\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Modules List */}\r\n      {data.modules.length === 0 ? (\r\n        <Card>\r\n          <CardContent className=\"flex flex-col items-center justify-center py-12\">\r\n            <BookOpen className=\"w-12 h-12 text-muted-foreground mb-4\" />\r\n            <h3 className=\"text-lg font-semibold mb-2\">Belum ada modul</h3>\r\n            <p className=\"text-muted-foreground text-center mb-4\">\r\n              Mulai dengan membuat modul pertama untuk course Anda\r\n            </p>\r\n            <Button onClick={createNewModule}>\r\n              <Plus className=\"w-4 h-4 mr-2\" />\r\n              Buat Modul Pertama\r\n            </Button>\r\n          </CardContent>\r\n        </Card>\r\n      ) : (\r\n        <div className=\"space-y-4\">\r\n          {data.modules.map((moduleItem, moduleIndex) => {\r\n            const isExpanded = expandedModules.has(moduleItem.id);\r\n            \r\n            return (\r\n              <Card key={moduleItem.id} className=\"overflow-hidden\">\r\n                <CardHeader className=\"pb-3\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center space-x-3\">\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <GripVertical className=\"w-4 h-4 text-muted-foreground cursor-move\" />\r\n                        <Badge variant=\"outline\">Modul {moduleIndex + 1}</Badge>\r\n                      </div>\r\n                      <div>\r\n                        <CardTitle className=\"text-base\">{moduleItem.name || 'Modul Tanpa Nama'}</CardTitle>\r\n                        {moduleItem.description && (\r\n                          <CardDescription className=\"mt-1\">\r\n                            {moduleItem.description}\r\n                          </CardDescription>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <div className=\"flex items-center space-x-2\">\r\n                      {moduleItem.hasModuleQuiz && (\r\n                        <Badge variant=\"secondary\">\r\n                          <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                          Quiz Modul\r\n                        </Badge>\r\n                      )}\r\n                      <Badge variant=\"outline\">\r\n                        {moduleItem.chapters.length} Chapter\r\n                      </Badge>\r\n                      \r\n                      <div className=\"flex items-center space-x-1\">\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          size=\"sm\"\r\n                          onClick={() => moveModule(moduleItem.id, 'up')}\r\n                          disabled={moduleIndex === 0}\r\n                        >\r\n                          ↑\r\n                        </Button>\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          size=\"sm\"\r\n                          onClick={() => moveModule(moduleItem.id, 'down')}\r\n                          disabled={moduleIndex === data.modules.length - 1}\r\n                        >\r\n                          ↓\r\n                        </Button>\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          size=\"sm\"\r\n                          onClick={() => editModule(moduleItem)}\r\n                        >\r\n                          <Edit className=\"w-4 h-4\" />\r\n                        </Button>\r\n                        <AlertDialog>\r\n                          <AlertDialogTrigger asChild>\r\n                            <Button variant=\"ghost\" size=\"sm\">\r\n                              <Trash2 className=\"w-4 h-4\" />\r\n                            </Button>\r\n                          </AlertDialogTrigger>\r\n                          <AlertDialogContent>\r\n                            <AlertDialogHeader>\r\n                              <AlertDialogTitle>Hapus Modul</AlertDialogTitle>\r\n                              <AlertDialogDescription>\r\n                                Apakah Anda yakin ingin menghapus modul &ldquo;{moduleItem.name}&rdquo;? \r\n                                Semua chapter di dalam modul ini juga akan terhapus.\r\n                              </AlertDialogDescription>\r\n                            </AlertDialogHeader>\r\n                            <AlertDialogFooter>\r\n                              <AlertDialogCancel>Batal</AlertDialogCancel>\r\n                              <AlertDialogAction onClick={() => deleteModule(moduleItem.id)}>\r\n                                Hapus\r\n                              </AlertDialogAction>\r\n                            </AlertDialogFooter>\r\n                          </AlertDialogContent>\r\n                        </AlertDialog>\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          size=\"sm\"\r\n                          onClick={() => toggleModuleExpansion(moduleItem.id)}\r\n                        >\r\n                          {isExpanded ? (\r\n                            <ChevronDown className=\"w-4 h-4\" />\r\n                          ) : (\r\n                            <ChevronRight className=\"w-4 h-4\" />\r\n                          )}\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </CardHeader>\r\n                \r\n                {isExpanded && (\r\n                  <CardContent className=\"pt-0\">\r\n                    <div className=\"space-y-3\">\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <h4 className=\"text-sm font-medium\">Chapters</h4>\r\n                        <Button \r\n                          variant=\"outline\" \r\n                          size=\"sm\"\r\n                          onClick={() => createNewChapter(moduleItem.id)}\r\n                        >\r\n                          <Plus className=\"w-4 h-4 mr-2\" />\r\n                          Tambah Chapter\r\n                        </Button>\r\n                      </div>\r\n                      \r\n                      {moduleItem.chapters.length === 0 ? (\r\n                        <div className=\"text-center py-8 text-muted-foreground\">\r\n                          <FileText className=\"w-8 h-8 mx-auto mb-2\" />\r\n                          <p className=\"text-sm\">Belum ada chapter</p>\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"space-y-2\">\r\n                          {moduleItem.chapters.map((chapter, chapterIndex) => (\r\n                            <div \r\n                              key={chapter.id}\r\n                              className=\"flex items-center justify-between p-3 bg-muted/50 rounded-lg\"\r\n                            >\r\n                              <div className=\"flex items-center space-x-3\">\r\n                                <GripVertical className=\"w-4 h-4 text-muted-foreground cursor-move\" />\r\n                                <Badge variant=\"outline\" className=\"text-xs\">\r\n                                  {chapterIndex + 1}\r\n                                </Badge>\r\n                                <div>\r\n                                  <p className=\"text-sm font-medium\">\r\n                                    {chapter.name || 'Chapter Tanpa Nama'}\r\n                                  </p>\r\n                                  {chapter.hasChapterQuiz && (\r\n                                    <Badge variant=\"secondary\" className=\"text-xs mt-1\">\r\n                                      <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                                      Quiz\r\n                                    </Badge>\r\n                                  )}\r\n                                </div>\r\n                              </div>\r\n                              \r\n                              <div className=\"flex items-center space-x-1\">\r\n                                <Button\r\n                                  variant=\"ghost\"\r\n                                  size=\"sm\"\r\n                                  onClick={() => editChapter(moduleItem.id, chapter)}\r\n                                >\r\n                                  <Edit className=\"w-4 h-4\" />\r\n                                </Button>\r\n                                <AlertDialog>\r\n                                  <AlertDialogTrigger asChild>\r\n                                    <Button variant=\"ghost\" size=\"sm\">\r\n                                      <Trash2 className=\"w-4 h-4\" />\r\n                                    </Button>\r\n                                  </AlertDialogTrigger>\r\n                                  <AlertDialogContent>\r\n                                    <AlertDialogHeader>\r\n                                      <AlertDialogTitle>Hapus Chapter</AlertDialogTitle>\r\n                                      <AlertDialogDescription>\r\n                                        Apakah Anda yakin ingin menghapus chapter &ldquo;{chapter.name}&rdquo;?\r\n                                      </AlertDialogDescription>\r\n                                    </AlertDialogHeader>\r\n                                    <AlertDialogFooter>\r\n                                      <AlertDialogCancel>Batal</AlertDialogCancel>\r\n                                      <AlertDialogAction \r\n                                        onClick={() => deleteChapter(moduleItem.id, chapter.id)}\r\n                                      >\r\n                                        Hapus\r\n                                      </AlertDialogAction>\r\n                                    </AlertDialogFooter>\r\n                                  </AlertDialogContent>\r\n                                </AlertDialog>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </CardContent>\r\n                )}\r\n              </Card>\r\n            );\r\n          })}\r\n        </div>\r\n      )}\r\n\r\n      {/* Module Dialog */}\r\n      <Dialog open={isModuleDialogOpen} onOpenChange={setIsModuleDialogOpen}>\r\n        <DialogContent className=\"sm:max-w-md\">\r\n          <DialogHeader>\r\n            <DialogTitle>\r\n              {editingModule?.name ? 'Edit Modul' : 'Tambah Modul Baru'}\r\n            </DialogTitle>\r\n            <DialogDescription>\r\n              Isi informasi dasar untuk modul ini\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"moduleName\">Nama Modul *</Label>\r\n              <Input\r\n                id=\"moduleName\"\r\n                placeholder=\"Masukkan nama modul\"\r\n                value={editingModule?.name || ''}\r\n                onChange={(e) => setEditingModule(prev => \r\n                  prev ? { ...prev, name: e.target.value } : null\r\n                )}\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"moduleDescription\">Deskripsi</Label>\r\n              <Textarea\r\n                id=\"moduleDescription\"\r\n                placeholder=\"Jelaskan tentang modul ini...\"\r\n                value={editingModule?.description || ''}\r\n                onChange={(e) => setEditingModule(prev => \r\n                  prev ? { ...prev, description: e.target.value } : null\r\n                )}\r\n                rows={3}\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"flex items-center space-x-2\">\r\n              <Switch\r\n                id=\"hasModuleQuiz\"\r\n                checked={editingModule?.hasModuleQuiz || false}\r\n                onCheckedChange={(checked) => setEditingModule(prev => \r\n                  prev ? { ...prev, hasModuleQuiz: checked } : null\r\n                )}\r\n              />\r\n              <Label htmlFor=\"hasModuleQuiz\">Tambahkan quiz di akhir modul</Label>\r\n            </div>\r\n          </div>\r\n          \r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setIsModuleDialogOpen(false)}>\r\n              Batal\r\n            </Button>\r\n            <Button onClick={saveModule}>\r\n              {editingModule?.name ? 'Perbarui' : 'Tambah'} Modul\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Chapter Dialog */}\r\n      <Dialog open={isChapterDialogOpen} onOpenChange={setIsChapterDialogOpen}>\r\n        <DialogContent className=\"sm:max-w-md\">\r\n          <DialogHeader>\r\n            <DialogTitle>\r\n              {editingChapter.chapter?.name ? 'Edit Chapter' : 'Tambah Chapter Baru'}\r\n            </DialogTitle>\r\n            <DialogDescription>\r\n              Isi informasi dasar untuk chapter ini\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"chapterName\">Nama Chapter *</Label>\r\n              <Input\r\n                id=\"chapterName\"\r\n                placeholder=\"Masukkan nama chapter\"\r\n                value={editingChapter.chapter?.name || ''}\r\n                onChange={(e) => setEditingChapter(prev => ({\r\n                  ...prev,\r\n                  chapter: prev.chapter ? { ...prev.chapter, name: e.target.value } : null\r\n                }))}\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"flex items-center space-x-2\">\r\n              <Switch\r\n                id=\"hasChapterQuiz\"\r\n                checked={editingChapter.chapter?.hasChapterQuiz || false}\r\n                onCheckedChange={(checked) => setEditingChapter(prev => ({\r\n                  ...prev,\r\n                  chapter: prev.chapter ? { ...prev.chapter, hasChapterQuiz: checked } : null\r\n                }))}\r\n              />\r\n              <Label htmlFor=\"hasChapterQuiz\">Tambahkan quiz untuk chapter ini</Label>\r\n            </div>\r\n          </div>\r\n          \r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setIsChapterDialogOpen(false)}>\r\n              Batal\r\n            </Button>\r\n            <Button onClick={saveChapter}>\r\n              {editingChapter.chapter?.name ? 'Perbarui' : 'Tambah'} Chapter\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Summary */}\r\n      {data.modules.length > 0 && (\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"text-lg\">Ringkasan Struktur</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-primary\">{data.modules.length}</div>\r\n                <div className=\"text-sm text-muted-foreground\">Modul</div>\r\n              </div>\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-primary\">\r\n                  {data.modules.reduce((acc, moduleItem) => acc + moduleItem.chapters.length, 0)}\r\n                </div>\r\n                <div className=\"text-sm text-muted-foreground\">Chapter</div>\r\n              </div>\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-primary\">\r\n                  {data.modules.filter(m => m.hasModuleQuiz).length}\r\n                </div>\r\n                <div className=\"text-sm text-muted-foreground\">Quiz Modul</div>\r\n              </div>\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-primary\">\r\n                  {data.modules.reduce((acc, moduleItem) => \r\n                    acc + moduleItem.chapters.filter(c => c.hasChapterQuiz).length, 0\r\n                  )}\r\n                </div>\r\n                <div className=\"text-sm text-muted-foreground\">Quiz Chapter</div>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAzBA;;;;;;;;;;;;;;AAgCO,SAAS,oBAAoB,EAAE,IAAI,EAAE,QAAQ,EAA4B;IAC9E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqD;QACtG,UAAU;QACV,SAAS;IACX;IACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,wBAAwB,CAAC;QAC7B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,WAAW;YAC7B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,mBAAmB;IACrB;IAEA,MAAM,kBAAkB;QACtB,MAAM,YAAwB;YAC5B,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;YAC1B,MAAM;YACN,aAAa;YACb,YAAY,KAAK,OAAO,CAAC,MAAM;YAC/B,UAAU,EAAE;YACZ,eAAe;QACjB;QACA,iBAAiB;QACjB,sBAAsB;IACxB;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB;YAAE,GAAG,UAAU;QAAC;QACjC,sBAAsB;IACxB;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,iBAAiB,CAAC,cAAc,IAAI,CAAC,IAAI,IAAI;YAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,iBAAiB;eAAI,KAAK,OAAO;SAAC;QACxC,MAAM,gBAAgB,eAAe,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,EAAE;QAE7E,IAAI,iBAAiB,GAAG;YACtB,cAAc,CAAC,cAAc,GAAG;YAChC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,OAAO;YACL,eAAe,IAAI,CAAC;YACpB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QAEA,SAAS;YAAE,SAAS;QAAe;QACnC,sBAAsB;QACtB,iBAAiB;IACnB;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,iBAAiB,KAAK,OAAO,CAChC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UACrB,GAAG,CAAC,CAAC,GAAG,QAAU,CAAC;gBAAE,GAAG,CAAC;gBAAE,YAAY;YAAM,CAAC;QAEjD,SAAS;YAAE,SAAS;QAAe;QACnC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,KAAK,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACnD,IAAI,CAAC,YAAY;QAEjB,MAAM,aAA0B;YAC9B,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;YAC3B,MAAM;YACN,SAAS,EAAE;YACX,YAAY,WAAW,QAAQ,CAAC,MAAM;YACtC,gBAAgB;QAClB;QAEA,kBAAkB;YAAE;YAAU,SAAS;QAAW;QAClD,uBAAuB;IACzB;IAEA,MAAM,cAAc,CAAC,UAAkB;QACrC,kBAAkB;YAAE;YAAU,SAAS;gBAAE,GAAG,OAAO;YAAC;QAAE;QACtD,uBAAuB;IACzB;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,eAAe,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI;YAClE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,iBAAiB,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA;YACtC,IAAI,WAAW,EAAE,KAAK,eAAe,QAAQ,EAAE;gBAC7C,MAAM,kBAAkB;uBAAI,WAAW,QAAQ;iBAAC;gBAChD,MAAM,gBAAgB,gBAAgB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,OAAO,CAAE,EAAE;gBAExF,IAAI,iBAAiB,GAAG;oBACtB,eAAe,CAAC,cAAc,GAAG,eAAe,OAAO;gBACzD,OAAO;oBACL,gBAAgB,IAAI,CAAC,eAAe,OAAO;gBAC7C;gBAEA,OAAO;oBAAE,GAAG,UAAU;oBAAE,UAAU;gBAAgB;YACpD;YACA,OAAO;QACT;QAEA,SAAS;YAAE,SAAS;QAAe;QACnC,uBAAuB;QACvB,kBAAkB;YAAE,UAAU;YAAI,SAAS;QAAK;QAChD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,gBAAgB,CAAC,UAAkB;QACvC,MAAM,iBAAiB,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA;YACtC,IAAI,WAAW,EAAE,KAAK,UAAU;gBAC9B,MAAM,kBAAkB,WAAW,QAAQ,CACxC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WACrB,GAAG,CAAC,CAAC,GAAG,QAAU,CAAC;wBAAE,GAAG,CAAC;wBAAE,YAAY;oBAAM,CAAC;gBACjD,OAAO;oBAAE,GAAG,UAAU;oBAAE,UAAU;gBAAgB;YACpD;YACA,OAAO;QACT;QAEA,SAAS;YAAE,SAAS;QAAe;QACnC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,aAAa,CAAC,UAAkB;QACpC,MAAM,eAAe,KAAK,OAAO,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1D,IAAI,iBAAiB,CAAC,GAAG;QAEzB,MAAM,WAAW,cAAc,OAAO,eAAe,IAAI,eAAe;QACxE,IAAI,WAAW,KAAK,YAAY,KAAK,OAAO,CAAC,MAAM,EAAE;QAErD,MAAM,iBAAiB;eAAI,KAAK,OAAO;SAAC;QACxC,CAAC,cAAc,CAAC,aAAa,EAAE,cAAc,CAAC,SAAS,CAAC,GACxD;YAAC,cAAc,CAAC,SAAS;YAAE,cAAc,CAAC,aAAa;SAAC;QAExD,uBAAuB;QACvB,eAAe,OAAO,CAAC,CAAC,YAAY;YAClC,WAAW,UAAU,GAAG;QAC1B;QAEA,SAAS;YAAE,SAAS;QAAe;IACrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;kCAI/C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;;0CACf,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMpC,KAAK,OAAO,CAAC,MAAM,KAAK,kBACvB,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;sCAGtD,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;qCAMvC,8OAAC;gBAAI,WAAU;0BACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,YAAY;oBAC7B,MAAM,aAAa,gBAAgB,GAAG,CAAC,WAAW,EAAE;oBAEpD,qBACE,8OAAC,gIAAA,CAAA,OAAI;wBAAqB,WAAU;;0CAClC,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;;gEAAU;gEAAO,cAAc;;;;;;;;;;;;;8DAEhD,8OAAC;;sEACC,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa,WAAW,IAAI,IAAI;;;;;;wDACpD,WAAW,WAAW,kBACrB,8OAAC,gIAAA,CAAA,kBAAe;4DAAC,WAAU;sEACxB,WAAW,WAAW;;;;;;;;;;;;;;;;;;sDAM/B,8OAAC;4CAAI,WAAU;;gDACZ,WAAW,aAAa,kBACvB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;;sEACb,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAI3C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;;wDACZ,WAAW,QAAQ,CAAC,MAAM;wDAAC;;;;;;;8DAG9B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,WAAW,WAAW,EAAE,EAAE;4DACzC,UAAU,gBAAgB;sEAC3B;;;;;;sEAGD,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,WAAW,WAAW,EAAE,EAAE;4DACzC,UAAU,gBAAgB,KAAK,OAAO,CAAC,MAAM,GAAG;sEACjD;;;;;;sEAGD,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,WAAW;sEAE1B,cAAA,8OAAC,2MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC,2IAAA,CAAA,cAAW;;8EACV,8OAAC,2IAAA,CAAA,qBAAkB;oEAAC,OAAO;8EACzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;8EAGtB,8OAAC,2IAAA,CAAA,qBAAkB;;sFACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8FAChB,8OAAC,2IAAA,CAAA,mBAAgB;8FAAC;;;;;;8FAClB,8OAAC,2IAAA,CAAA,yBAAsB;;wFAAC;wFAC0B,WAAW,IAAI;wFAAC;;;;;;;;;;;;;sFAIpE,8OAAC,2IAAA,CAAA,oBAAiB;;8FAChB,8OAAC,2IAAA,CAAA,oBAAiB;8FAAC;;;;;;8FACnB,8OAAC,2IAAA,CAAA,oBAAiB;oFAAC,SAAS,IAAM,aAAa,WAAW,EAAE;8FAAG;;;;;;;;;;;;;;;;;;;;;;;;sEAMrE,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,sBAAsB,WAAW,EAAE;sEAEjD,2BACC,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;qFAEvB,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQnC,4BACC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,iBAAiB,WAAW,EAAE;;sEAE7C,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;wCAKpC,WAAW,QAAQ,CAAC,MAAM,KAAK,kBAC9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;iEAGzB,8OAAC;4CAAI,WAAU;sDACZ,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BACjC,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;8EACxB,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;8EAChC,eAAe;;;;;;8EAElB,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFACV,QAAQ,IAAI,IAAI;;;;;;wEAElB,QAAQ,cAAc,kBACrB,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAY,WAAU;;8FACnC,8OAAC,kNAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;sEAO/C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,YAAY,WAAW,EAAE,EAAE;8EAE1C,cAAA,8OAAC,2MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,8OAAC,2IAAA,CAAA,cAAW;;sFACV,8OAAC,2IAAA,CAAA,qBAAkB;4EAAC,OAAO;sFACzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gFAAC,SAAQ;gFAAQ,MAAK;0FAC3B,cAAA,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;;;;;;;;;;;sFAGtB,8OAAC,2IAAA,CAAA,qBAAkB;;8FACjB,8OAAC,2IAAA,CAAA,oBAAiB;;sGAChB,8OAAC,2IAAA,CAAA,mBAAgB;sGAAC;;;;;;sGAClB,8OAAC,2IAAA,CAAA,yBAAsB;;gGAAC;gGAC4B,QAAQ,IAAI;gGAAC;;;;;;;;;;;;;8FAGnE,8OAAC,2IAAA,CAAA,oBAAiB;;sGAChB,8OAAC,2IAAA,CAAA,oBAAiB;sGAAC;;;;;;sGACnB,8OAAC,2IAAA,CAAA,oBAAiB;4FAChB,SAAS,IAAM,cAAc,WAAW,EAAE,EAAE,QAAQ,EAAE;sGACvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDA9CJ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;uBAnHpB,WAAW,EAAE;;;;;gBAiL5B;;;;;;0BAKJ,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAoB,cAAc;0BAC9C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CACT,eAAe,OAAO,eAAe;;;;;;8CAExC,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa;;;;;;sDAC5B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO,eAAe,QAAQ;4CAC9B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAChC,OAAO;wDAAE,GAAG,IAAI;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC,IAAI;;;;;;;;;;;;8CAKjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAoB;;;;;;sDACnC,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO,eAAe,eAAe;4CACrC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAChC,OAAO;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC,IAAI;4CAEpD,MAAM;;;;;;;;;;;;8CAIV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,IAAG;4CACH,SAAS,eAAe,iBAAiB;4CACzC,iBAAiB,CAAC,UAAY,iBAAiB,CAAA,OAC7C,OAAO;wDAAE,GAAG,IAAI;wDAAE,eAAe;oDAAQ,IAAI;;;;;;sDAGjD,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAgB;;;;;;;;;;;;;;;;;;sCAInC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,sBAAsB;8CAAQ;;;;;;8CAGvE,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;;wCACd,eAAe,OAAO,aAAa;wCAAS;;;;;;;;;;;;;;;;;;;;;;;;0BAOrD,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAqB,cAAc;0BAC/C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CACT,eAAe,OAAO,EAAE,OAAO,iBAAiB;;;;;;8CAEnD,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO,eAAe,OAAO,EAAE,QAAQ;4CACvC,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wDAC1C,GAAG,IAAI;wDACP,SAAS,KAAK,OAAO,GAAG;4DAAE,GAAG,KAAK,OAAO;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC,IAAI;oDACtE,CAAC;;;;;;;;;;;;8CAIL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,IAAG;4CACH,SAAS,eAAe,OAAO,EAAE,kBAAkB;4CACnD,iBAAiB,CAAC,UAAY,kBAAkB,CAAA,OAAQ,CAAC;wDACvD,GAAG,IAAI;wDACP,SAAS,KAAK,OAAO,GAAG;4DAAE,GAAG,KAAK,OAAO;4DAAE,gBAAgB;wDAAQ,IAAI;oDACzE,CAAC;;;;;;sDAEH,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAiB;;;;;;;;;;;;;;;;;;sCAIpC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,uBAAuB;8CAAQ;;;;;;8CAGxE,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;;wCACd,eAAe,OAAO,EAAE,OAAO,aAAa;wCAAS;;;;;;;;;;;;;;;;;;;;;;;;YAO7D,KAAK,OAAO,CAAC,MAAM,GAAG,mBACrB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAmC,KAAK,OAAO,CAAC,MAAM;;;;;;sDACrE,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,aAAe,MAAM,WAAW,QAAQ,CAAC,MAAM,EAAE;;;;;;sDAE9E,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,EAAE,MAAM;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,aACzB,MAAM,WAAW,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,EAAE,MAAM,EAAE;;;;;;sDAGpE,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D", "debugId": null}}, {"offset": {"line": 3488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/dynamic-content-editor.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useCallback } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Plus, Trash2, Image as ImageIcon, TextIcon, VideoIcon, FileTextIcon, MonitorPlayIcon, Link, Upload } from 'lucide-react';\r\nimport { FileUploader } from '@/components/file-uploader';\r\nimport Image from 'next/image';\r\nimport { toast } from 'sonner';\r\nimport dynamic from 'next/dynamic';\r\n\r\n// Create a wrapper component for MDX Editor with all plugins\r\nconst MDXEditorWrapper = dynamic(\r\n  () => import('./mdx-editor-wrapper'),\r\n  {\r\n    ssr: false,\r\n    loading: () => <div className=\"min-h-[200px] flex items-center justify-center\">Loading editor...</div>\r\n  }\r\n);\r\n\r\n// Define the types for content blocks\r\nexport type ContentBlock = {\r\n  id: string;\r\n  type: 'text' | 'image' | 'video' | 'pdf' | 'zoom-recording';\r\n  value: string; // For text content, image URL, video URL, PDF URL, Zoom recording URL\r\n};\r\n\r\ninterface DynamicContentEditorProps {\r\n  initialContent: ContentBlock[];\r\n  onContentChange: (content: ContentBlock[]) => void;\r\n  allowImages?: boolean;\r\n  placeholder?: string;\r\n  contentRefs?: React.MutableRefObject<{ [key: string]: HTMLDivElement | null }>;\r\n}\r\n\r\nexport function DynamicContentEditor({\r\n  initialContent,\r\n  onContentChange,\r\n  allowImages = true,\r\n  placeholder,\r\n  contentRefs,\r\n}: DynamicContentEditorProps) {\r\n  const [content, setContent] = useState<ContentBlock[]>(initialContent);\r\n  const [showFileDialog, setShowFileDialog] = useState(false);\r\n  const [selectedFileType, setSelectedFileType] = useState<'image' | 'video' | 'pdf' | 'zoom-recording'>('image');\r\n  const [linkUrl, setLinkUrl] = useState('');\r\n\r\n  React.useEffect(() => {\r\n    setContent(initialContent);\r\n  }, [initialContent]);\r\n\r\n  const addBlock = (type: ContentBlock['type']) => {\r\n    const newBlock: ContentBlock = {\r\n      id: `block-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,\r\n      type,\r\n      value: '',\r\n    };\r\n    const updatedContent = [...content, newBlock];\r\n    setContent(updatedContent);\r\n    onContentChange(updatedContent);\r\n  };\r\n\r\n  const handleFileBlockAdd = (type: 'image' | 'video' | 'pdf' | 'zoom-recording') => {\r\n    setSelectedFileType(type);\r\n    setShowFileDialog(true);\r\n    setLinkUrl('');\r\n  };\r\n\r\n  const handleAddFromLink = () => {\r\n    if (linkUrl.trim()) {\r\n      const newBlock: ContentBlock = {\r\n        id: `block-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,\r\n        type: selectedFileType,\r\n        value: linkUrl.trim(),\r\n      };\r\n      const updatedContent = [...content, newBlock];\r\n      setContent(updatedContent);\r\n      onContentChange(updatedContent);\r\n      setShowFileDialog(false);\r\n      setLinkUrl('');\r\n      toast.success('Block berhasil ditambahkan dari link!');\r\n    } else {\r\n      toast.error('Silakan masukkan URL yang valid');\r\n    }\r\n  };\r\n\r\n  const handleAddFromUpload = () => {\r\n    addBlock(selectedFileType);\r\n    setShowFileDialog(false);\r\n  };\r\n\r\n  const updateBlock = (id: string, newValue: string) => {\r\n    const updatedContent = content.map((block) =>\r\n      block.id === id ? { ...block, value: newValue } : block\r\n    );\r\n    setContent(updatedContent);\r\n    onContentChange(updatedContent);\r\n  };\r\n\r\n  const removeBlock = (id: string) => {\r\n    const updatedContent = content.filter((block) => block.id !== id);\r\n    setContent(updatedContent);\r\n    onContentChange(updatedContent);\r\n  };\r\n\r\n  const handleFileUpload = useCallback(async (files: File[], blockId: string, fileType: 'image' | 'video' | 'pdf' | 'zoom-recording') => {\r\n    if (!files || files.length === 0) {\r\n      toast.error('No file selected for upload.');\r\n      return;\r\n    }\r\n\r\n    const file = files[0];\r\n    toast.info(`Uploading ${file.name}...`);\r\n\r\n    try {\r\n      const response = await fetch(`/api/upload?filename=${file.name}`, {\r\n        method: 'POST',\r\n        body: file,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Upload failed: ${response.statusText}`);\r\n      }\r\n\r\n      const newBlob = await response.json();\r\n      updateBlock(blockId, newBlob.url);\r\n      toast.success(`${fileType.charAt(0).toUpperCase() + fileType.slice(1)} uploaded successfully!`);\r\n    } catch (error) {\r\n      console.error(`Error uploading ${fileType}:`, error);\r\n      toast.error(`Failed to upload ${fileType}: ${(error as Error).message}`);\r\n    }\r\n  }, [updateBlock]);\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {content.map((block, index) => (\r\n        <Card\r\n          key={block.id}\r\n          className=\"relative p-4 mb-4 scroll-mt-4\"\r\n          ref={(el) => {\r\n            if (contentRefs) {\r\n              contentRefs.current[block.id || `block-${index}`] = el;\r\n            }\r\n          }}\r\n          id={block.id || `block-${index}`}\r\n        >\r\n          {block.type === 'text' ? (\r\n            <div className=\"mdx-editor-wrapper\">\r\n              <MDXEditorWrapper\r\n                markdown={block.value || ''}\r\n                onChange={(markdown) => updateBlock(block.id, markdown)}\r\n                placeholder={placeholder || \"Enter your content here...\"}\r\n                className=\"min-h-[200px]\"\r\n              />\r\n            </div>\r\n          ) : block.type === 'image' ? (\r\n            <div className=\"space-y-2\">\r\n              {block.value ? (\r\n                <div className=\"relative w-full h-48 border rounded-md overflow-hidden\">\r\n                  <Image\r\n                    src={block.value}\r\n                    alt=\"Uploaded content\"\r\n                    layout=\"fill\"\r\n                    objectFit=\"contain\"\r\n                    className=\"rounded-md\"\r\n                  />\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"text-sm text-muted-foreground mb-2\">Pilih cara menambahkan gambar:</div>\r\n                  <div className=\"flex gap-2\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => {\r\n                        const input = document.createElement('input');\r\n                        input.type = 'file';\r\n                        input.accept = 'image/*';\r\n                        input.onchange = (e) => {\r\n                          const files = (e.target as HTMLInputElement).files;\r\n                          if (files) handleFileUpload(Array.from(files), block.id, 'image');\r\n                        };\r\n                        input.click();\r\n                      }}\r\n                    >\r\n                      <Upload className=\"h-4 w-4 mr-2\" />\r\n                      Upload File\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => {\r\n                        const url = prompt('Masukkan URL gambar:');\r\n                        if (url) updateBlock(block.id, url);\r\n                      }}\r\n                    >\r\n                      <Link className=\"h-4 w-4 mr-2\" />\r\n                      Dari Link\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : block.type === 'video' ? (\r\n            <div className=\"space-y-2\">\r\n              {block.value ? (\r\n                <video controls src={block.value} className=\"w-full h-auto max-h-96 rounded-md\" />\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"text-sm text-muted-foreground mb-2\">Pilih cara menambahkan video:</div>\r\n                  <div className=\"flex gap-2\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => {\r\n                        const input = document.createElement('input');\r\n                        input.type = 'file';\r\n                        input.accept = 'video/*';\r\n                        input.onchange = (e) => {\r\n                          const files = (e.target as HTMLInputElement).files;\r\n                          if (files) handleFileUpload(Array.from(files), block.id, 'video');\r\n                        };\r\n                        input.click();\r\n                      }}\r\n                    >\r\n                      <Upload className=\"h-4 w-4 mr-2\" />\r\n                      Upload File\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => {\r\n                        const url = prompt('Masukkan URL video:');\r\n                        if (url) updateBlock(block.id, url);\r\n                      }}\r\n                    >\r\n                      <Link className=\"h-4 w-4 mr-2\" />\r\n                      Dari Link\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : block.type === 'pdf' ? (\r\n            <div className=\"space-y-2\">\r\n              {block.value ? (\r\n                <iframe src={block.value} className=\"w-full h-96 rounded-md\" />\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"text-sm text-muted-foreground mb-2\">Pilih cara menambahkan PDF:</div>\r\n                  <div className=\"flex gap-2\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => {\r\n                        const input = document.createElement('input');\r\n                        input.type = 'file';\r\n                        input.accept = 'application/pdf';\r\n                        input.onchange = (e) => {\r\n                          const files = (e.target as HTMLInputElement).files;\r\n                          if (files) handleFileUpload(Array.from(files), block.id, 'pdf');\r\n                        };\r\n                        input.click();\r\n                      }}\r\n                    >\r\n                      <Upload className=\"h-4 w-4 mr-2\" />\r\n                      Upload File\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => {\r\n                        const url = prompt('Masukkan URL PDF:');\r\n                        if (url) updateBlock(block.id, url);\r\n                      }}\r\n                    >\r\n                      <Link className=\"h-4 w-4 mr-2\" />\r\n                      Dari Link\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : block.type === 'zoom-recording' ? (\r\n            <div className=\"space-y-2\">\r\n              {block.value ? (\r\n                <iframe src={block.value} className=\"w-full h-96 rounded-md\" />\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"text-sm text-muted-foreground mb-2\">Pilih cara menambahkan Zoom Recording:</div>\r\n                  <div className=\"flex gap-2\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => {\r\n                        const input = document.createElement('input');\r\n                        input.type = 'file';\r\n                        input.accept = 'video/*';\r\n                        input.onchange = (e) => {\r\n                          const files = (e.target as HTMLInputElement).files;\r\n                          if (files) handleFileUpload(Array.from(files), block.id, 'zoom-recording');\r\n                        };\r\n                        input.click();\r\n                      }}\r\n                    >\r\n                      <Upload className=\"h-4 w-4 mr-2\" />\r\n                      Upload File\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => {\r\n                        const url = prompt('Masukkan URL Zoom Recording:');\r\n                        if (url) updateBlock(block.id, url);\r\n                      }}\r\n                    >\r\n                      <Link className=\"h-4 w-4 mr-2\" />\r\n                      Dari Link\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <Textarea\r\n              placeholder={`Enter ${block.type} URL`}\r\n              value={block.value}\r\n              onChange={(e) => updateBlock(block.id, e.target.value)}\r\n              rows={3}\r\n            />\r\n          )}\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"absolute top-2 right-2 text-muted-foreground hover:text-destructive\"\r\n            onClick={() => removeBlock(block.id)}\r\n          >\r\n            <Trash2 className=\"h-4 w-4\" />\r\n          </Button>\r\n        </Card>\r\n      ))}\r\n      <div className=\"flex flex-wrap gap-2 pt-2\">\r\n        <Button variant=\"outline\" onClick={() => addBlock('text')} size=\"sm\">\r\n          <TextIcon className=\"h-4 w-4 mr-2\" /> Add Text Block\r\n        </Button>\r\n        {allowImages && (\r\n          <>\r\n            <Button variant=\"outline\" onClick={() => handleFileBlockAdd('image')} size=\"sm\">\r\n              <ImageIcon className=\"h-4 w-4 mr-2\" /> Add Image Block\r\n            </Button>\r\n            <Button variant=\"outline\" onClick={() => handleFileBlockAdd('video')} size=\"sm\">\r\n              <MonitorPlayIcon className=\"h-4 w-4 mr-2\" /> Add Video Block\r\n            </Button>\r\n            <Button variant=\"outline\" onClick={() => handleFileBlockAdd('pdf')} size=\"sm\">\r\n              <FileTextIcon className=\"h-4 w-4 mr-2\" /> Add PDF Block\r\n            </Button>\r\n            <Button variant=\"outline\" onClick={() => handleFileBlockAdd('zoom-recording')} size=\"sm\">\r\n              <VideoIcon className=\"h-4 w-4 mr-2\" /> Add Zoom Recording Block\r\n            </Button>\r\n          </>\r\n        )}\r\n      </div>\r\n\r\n      {/* File Addition Dialog */}\r\n      <Dialog open={showFileDialog} onOpenChange={setShowFileDialog}>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Tambah {selectedFileType.charAt(0).toUpperCase() + selectedFileType.slice(1)} Block</DialogTitle>\r\n            <DialogDescription>\r\n              Pilih cara menambahkan {selectedFileType}: upload file atau masukkan link.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"link-url\">URL {selectedFileType.charAt(0).toUpperCase() + selectedFileType.slice(1)}</Label>\r\n              <Input\r\n                id=\"link-url\"\r\n                placeholder={`Masukkan URL ${selectedFileType}...`}\r\n                value={linkUrl}\r\n                onChange={(e) => setLinkUrl(e.target.value)}\r\n              />\r\n            </div>\r\n          </div>\r\n          <DialogFooter className=\"flex gap-2\">\r\n            <Button variant=\"outline\" onClick={handleAddFromUpload}>\r\n              <Upload className=\"h-4 w-4 mr-2\" />\r\n              Upload File\r\n            </Button>\r\n            <Button onClick={handleAddFromLink} disabled={!linkUrl.trim()}>\r\n              <Link className=\"h-4 w-4 mr-2\" />\r\n              Gunakan Link\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;;AAbA;;;;;;;;;;;;;AAeA,6DAA6D;AAC7D,MAAM,mBAAmB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAG3B,KAAK;IACL,SAAS,kBAAM,8OAAC;YAAI,WAAU;sBAAiD;;;;;;;AAmB5E,SAAS,qBAAqB,EACnC,cAAc,EACd,eAAe,EACf,cAAc,IAAI,EAClB,WAAW,EACX,WAAW,EACe;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgD;IACvG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,WAAW;IACb,GAAG;QAAC;KAAe;IAEnB,MAAM,WAAW,CAAC;QAChB,MAAM,WAAyB;YAC7B,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI;YACvE;YACA,OAAO;QACT;QACA,MAAM,iBAAiB;eAAI;YAAS;SAAS;QAC7C,WAAW;QACX,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB;QACpB,kBAAkB;QAClB,WAAW;IACb;IAEA,MAAM,oBAAoB;QACxB,IAAI,QAAQ,IAAI,IAAI;YAClB,MAAM,WAAyB;gBAC7B,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI;gBACvE,MAAM;gBACN,OAAO,QAAQ,IAAI;YACrB;YACA,MAAM,iBAAiB;mBAAI;gBAAS;aAAS;YAC7C,WAAW;YACX,gBAAgB;YAChB,kBAAkB;YAClB,WAAW;YACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,OAAO;YACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,SAAS;QACT,kBAAkB;IACpB;IAEA,MAAM,cAAc,CAAC,IAAY;QAC/B,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAC,QAClC,MAAM,EAAE,KAAK,KAAK;gBAAE,GAAG,KAAK;gBAAE,OAAO;YAAS,IAAI;QAEpD,WAAW;QACX,gBAAgB;IAClB;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;QAC9D,WAAW;QACX,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,OAAe,SAAiB;QAC1E,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC;QAEtC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE,EAAE;gBAChE,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,UAAU,EAAE;YACzD;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YACnC,YAAY,SAAS,QAAQ,GAAG;YAChC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC,GAAG,uBAAuB,CAAC;QAChG,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,EAAE;YAC9C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS,EAAE,EAAE,AAAC,MAAgB,OAAO,EAAE;QACzE;IACF,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC;QAAI,WAAU;;YACZ,QAAQ,GAAG,CAAC,CAAC,OAAO,sBACnB,8OAAC,gIAAA,CAAA,OAAI;oBAEH,WAAU;oBACV,KAAK,CAAC;wBACJ,IAAI,aAAa;4BACf,YAAY,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG;wBACtD;oBACF;oBACA,IAAI,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;;wBAE/B,MAAM,IAAI,KAAK,uBACd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,UAAU,MAAM,KAAK,IAAI;gCACzB,UAAU,CAAC,WAAa,YAAY,MAAM,EAAE,EAAE;gCAC9C,aAAa,eAAe;gCAC5B,WAAU;;;;;;;;;;mCAGZ,MAAM,IAAI,KAAK,wBACjB,8OAAC;4BAAI,WAAU;sCACZ,MAAM,KAAK,iBACV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,MAAM,KAAK;oCAChB,KAAI;oCACJ,QAAO;oCACP,WAAU;oCACV,WAAU;;;;;;;;;;qDAId,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,MAAM,QAAQ,SAAS,aAAa,CAAC;oDACrC,MAAM,IAAI,GAAG;oDACb,MAAM,MAAM,GAAG;oDACf,MAAM,QAAQ,GAAG,CAAC;wDAChB,MAAM,QAAQ,AAAC,EAAE,MAAM,CAAsB,KAAK;wDAClD,IAAI,OAAO,iBAAiB,MAAM,IAAI,CAAC,QAAQ,MAAM,EAAE,EAAE;oDAC3D;oDACA,MAAM,KAAK;gDACb;;kEAEA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,MAAM,MAAM,OAAO;oDACnB,IAAI,KAAK,YAAY,MAAM,EAAE,EAAE;gDACjC;;kEAEA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;mCAOzC,MAAM,IAAI,KAAK,wBACjB,8OAAC;4BAAI,WAAU;sCACZ,MAAM,KAAK,iBACV,8OAAC;gCAAM,QAAQ;gCAAC,KAAK,MAAM,KAAK;gCAAE,WAAU;;;;;qDAE5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,MAAM,QAAQ,SAAS,aAAa,CAAC;oDACrC,MAAM,IAAI,GAAG;oDACb,MAAM,MAAM,GAAG;oDACf,MAAM,QAAQ,GAAG,CAAC;wDAChB,MAAM,QAAQ,AAAC,EAAE,MAAM,CAAsB,KAAK;wDAClD,IAAI,OAAO,iBAAiB,MAAM,IAAI,CAAC,QAAQ,MAAM,EAAE,EAAE;oDAC3D;oDACA,MAAM,KAAK;gDACb;;kEAEA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,MAAM,MAAM,OAAO;oDACnB,IAAI,KAAK,YAAY,MAAM,EAAE,EAAE;gDACjC;;kEAEA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;mCAOzC,MAAM,IAAI,KAAK,sBACjB,8OAAC;4BAAI,WAAU;sCACZ,MAAM,KAAK,iBACV,8OAAC;gCAAO,KAAK,MAAM,KAAK;gCAAE,WAAU;;;;;qDAEpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,MAAM,QAAQ,SAAS,aAAa,CAAC;oDACrC,MAAM,IAAI,GAAG;oDACb,MAAM,MAAM,GAAG;oDACf,MAAM,QAAQ,GAAG,CAAC;wDAChB,MAAM,QAAQ,AAAC,EAAE,MAAM,CAAsB,KAAK;wDAClD,IAAI,OAAO,iBAAiB,MAAM,IAAI,CAAC,QAAQ,MAAM,EAAE,EAAE;oDAC3D;oDACA,MAAM,KAAK;gDACb;;kEAEA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,MAAM,MAAM,OAAO;oDACnB,IAAI,KAAK,YAAY,MAAM,EAAE,EAAE;gDACjC;;kEAEA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;mCAOzC,MAAM,IAAI,KAAK,iCACjB,8OAAC;4BAAI,WAAU;sCACZ,MAAM,KAAK,iBACV,8OAAC;gCAAO,KAAK,MAAM,KAAK;gCAAE,WAAU;;;;;qDAEpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,MAAM,QAAQ,SAAS,aAAa,CAAC;oDACrC,MAAM,IAAI,GAAG;oDACb,MAAM,MAAM,GAAG;oDACf,MAAM,QAAQ,GAAG,CAAC;wDAChB,MAAM,QAAQ,AAAC,EAAE,MAAM,CAAsB,KAAK;wDAClD,IAAI,OAAO,iBAAiB,MAAM,IAAI,CAAC,QAAQ,MAAM,EAAE,EAAE;oDAC3D;oDACA,MAAM,KAAK;gDACb;;kEAEA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,MAAM,MAAM,OAAO;oDACnB,IAAI,KAAK,YAAY,MAAM,EAAE,EAAE;gDACjC;;kEAEA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;iDAQ3C,8OAAC,oIAAA,CAAA,WAAQ;4BACP,aAAa,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC;4BACtC,OAAO,MAAM,KAAK;4BAClB,UAAU,CAAC,IAAM,YAAY,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4BACrD,MAAM;;;;;;sCAGV,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,YAAY,MAAM,EAAE;sCAEnC,cAAA,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;mBAxMf,MAAM,EAAE;;;;;0BA4MjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS,IAAM,SAAS;wBAAS,MAAK;;0CAC9D,8OAAC,sMAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;oBAEtC,6BACC;;0CACE,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,mBAAmB;gCAAU,MAAK;;kDACzE,8OAAC,oMAAA,CAAA,QAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAExC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,mBAAmB;gCAAU,MAAK;;kDACzE,8OAAC,wNAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAE9C,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,mBAAmB;gCAAQ,MAAK;;kDACvE,8OAAC,kNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAE3C,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,mBAAmB;gCAAmB,MAAK;;kDAClF,8OAAC,wMAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;0BAO9C,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,cAAc;0BAC1C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;;wCAAC;wCAAQ,iBAAiB,MAAM,CAAC,GAAG,WAAW,KAAK,iBAAiB,KAAK,CAAC;wCAAG;;;;;;;8CAC1F,8OAAC,kIAAA,CAAA,oBAAiB;;wCAAC;wCACO;wCAAiB;;;;;;;;;;;;;sCAG7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAW;4CAAK,iBAAiB,MAAM,CAAC,GAAG,WAAW,KAAK,iBAAiB,KAAK,CAAC;;;;;;;kDACjG,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAa,CAAC,aAAa,EAAE,iBAAiB,GAAG,CAAC;wCAClD,OAAO;wCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;sCAIhD,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;;sDACjC,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAmB,UAAU,CAAC,QAAQ,IAAI;;sDACzD,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C", "debugId": null}}, {"offset": {"line": 4386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/course/steps/content-creation-step.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea'; // Keep for other uses\r\nimport { DynamicContentEditor, ContentBlock } from '@/components/dynamic-content-editor';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\r\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';\r\nimport { \r\n  FileText, \r\n  HelpCircle, \r\n  Plus, \r\n  Edit, \r\n  Trash2, \r\n  Eye, \r\n  Save,\r\n  BookOpen,\r\n  CheckCircle,\r\n  Clock,\r\n  Type,\r\n  Image,\r\n  Video,\r\n  FileIcon,\r\n  Navigation\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { CourseData, ModuleData, ChapterData, QuizData, QuestionData } from '../course-creation-wizard';\r\nimport { toast } from 'sonner';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\n\r\ninterface ContentCreationStepProps {\r\n  data: CourseData;\r\n  onUpdate: (updates: Partial<CourseData>) => void;\r\n}\r\n\r\nexport function ContentCreationStep({ data, onUpdate }: ContentCreationStepProps) {\r\n  const [selectedModule, setSelectedModule] = useState<string>(data.modules[0]?.id || '');\r\n  const [selectedChapter, setSelectedChapter] = useState<string>('');\r\n  const [editingQuiz, setEditingQuiz] = useState<{ type: 'chapter' | 'module' | 'final'; quiz: QuizData | null }>({\r\n    type: 'chapter',\r\n    quiz: null\r\n  });\r\n  const [isQuizDialogOpen, setIsQuizDialogOpen] = useState(false);\r\n  const [editingQuestion, setEditingQuestion] = useState<QuestionData | null>(null);\r\n  const [isQuestionDialogOpen, setIsQuestionDialogOpen] = useState(false);\r\n  const [previewMode, setPreviewMode] = useState(false);\r\n  \r\n  // Add ref for content scrolling\r\n  const contentRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});\r\n\r\n  const currentModule = data.modules.find(m => m.id === selectedModule);\r\n  const currentChapter = currentModule?.chapters.find(c => c.id === selectedChapter);\r\n\r\n  // Function to scroll to specific content block\r\n  const scrollToContent = (blockId: string) => {\r\n    const element = contentRefs.current[blockId];\r\n    if (element) {\r\n      element.scrollIntoView({ \r\n        behavior: 'smooth', \r\n        block: 'start',\r\n        inline: 'nearest'\r\n      });\r\n    }\r\n  };\r\n\r\n  // Function to get content type icon\r\n  const getContentTypeIcon = (type: string) => {\r\n    switch (type) {\r\n      case 'text':\r\n        return <Type className=\"w-3 h-3\" />;\r\n      case 'image':\r\n        return <Image className=\"w-3 h-3\" />;\r\n      case 'video':\r\n        return <Video className=\"w-3 h-3\" />;\r\n      case 'pdf':\r\n        return <FileText className=\"w-3 h-3\" />;\r\n      case 'zoom-recording':\r\n        return <Video className=\"w-3 h-3\" />;\r\n      default:\r\n        return <FileIcon className=\"w-3 h-3\" />;\r\n    }\r\n  };\r\n\r\n  // Function to get short content preview\r\n  const getContentPreview = (block: ContentBlock) => {\r\n    if (block.type === 'text') {\r\n      return block.value?.slice(0, 30) + (block.value && block.value.length > 30 ? '...' : '') || 'Empty text';\r\n    }\r\n    return block.type.charAt(0).toUpperCase() + block.type.slice(1);\r\n  };\r\n\r\n  const updateChapterContent = (content: ContentBlock[]) => {\r\n    if (!currentModule || !currentChapter) return;\r\n\r\n    const updatedModules = data.modules.map(module => {\r\n      if (module.id === selectedModule) {\r\n        const updatedChapters = module.chapters.map(chapter => {\r\n          if (chapter.id === selectedChapter) {\r\n            return { ...chapter, content };\r\n          }\r\n          return chapter;\r\n        });\r\n        return { ...module, chapters: updatedChapters };\r\n      }\r\n      return module;\r\n    });\r\n\r\n    onUpdate({ modules: updatedModules });\r\n  };\r\n\r\n  const createQuiz = (type: 'chapter' | 'module' | 'final') => {\r\n    const newQuiz: QuizData = {\r\n      id: `quiz-${Date.now()}`,\r\n      name: type === 'chapter' ? `Quiz ${currentChapter?.name}` : \r\n            type === 'module' ? `Quiz ${currentModule?.name}` : \r\n            `Final Exam - ${data.name}`,\r\n      description: '',\r\n      questions: [],\r\n      minimumScore: 70,\r\n      timeLimit: type === 'final' ? 120 : undefined // Default 2 hours for final exam\r\n    };\r\n    \r\n    setEditingQuiz({ type, quiz: newQuiz });\r\n    setIsQuizDialogOpen(true);\r\n  };\r\n\r\n  const editQuiz = (type: 'chapter' | 'module' | 'final', quiz: QuizData) => {\r\n    setEditingQuiz({ type, quiz: { ...quiz } });\r\n    setIsQuizDialogOpen(true);\r\n  };\r\n\r\n  const saveQuiz = () => {\r\n    if (!editingQuiz.quiz || !editingQuiz.quiz.name.trim()) {\r\n      toast.error('Nama quiz harus diisi');\r\n      return;\r\n    }\r\n\r\n    if (editingQuiz.type === 'final') {\r\n      onUpdate({ finalExam: editingQuiz.quiz! });\r\n    } else {\r\n      const updatedModules = data.modules.map(module => {\r\n        if (module.id === selectedModule) {\r\n          if (editingQuiz.type === 'module') {\r\n            return { ...module, moduleQuiz: editingQuiz.quiz! };\r\n          } else {\r\n            const updatedChapters = module.chapters.map(chapter => {\r\n              if (chapter.id === selectedChapter) {\r\n                return { ...chapter, chapterQuiz: editingQuiz.quiz! };\r\n              }\r\n              return chapter;\r\n            });\r\n            return { ...module, chapters: updatedChapters };\r\n          }\r\n        }\r\n        return module;\r\n      });\r\n      onUpdate({ modules: updatedModules });\r\n    }\r\n\r\n    setIsQuizDialogOpen(false);\r\n    setEditingQuiz({ type: 'chapter', quiz: null });\r\n    toast.success('Quiz berhasil disimpan');\r\n  };\r\n\r\n  const createQuestion = () => {\r\n    const newQuestion: QuestionData = {\r\n      id: editingQuestion?.id || `question-${Date.now()}`,\r\n      type: 'multiple_choice',\r\n      question: [{ type: 'text', value: '' }],\r\n      options: editingQuestion?.type === 'true_false'\r\n        ? [\r\n            { content: [{ type: 'text', value: 'True' }], isCorrect: false },\r\n            { content: [{ type: 'text', value: 'False' }], isCorrect: false }\r\n          ]\r\n        : [\r\n            { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n            { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n            { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n            { content: [{ type: 'text', value: '' }], isCorrect: false }\r\n          ],\r\n      essayAnswer: '',\r\n      explanation: [],\r\n      points: 1,\r\n      orderIndex: editingQuiz.quiz?.questions.length || 0\r\n    };\r\n    \r\n    setEditingQuestion(newQuestion);\r\n    setIsQuestionDialogOpen(true);\r\n  };\r\n\r\n  const editQuestion = (question: QuestionData) => {\r\n    setEditingQuestion({ ...question });\r\n    setIsQuestionDialogOpen(true);\r\n  };\r\n\r\n  const saveQuestion = () => {\r\n    if (!editingQuestion || editingQuestion.question.length === 0 || (editingQuestion.question[0].type === 'text' && !editingQuestion.question[0].value.trim())) {\r\n      toast.error('Pertanyaan harus diisi');\r\n      return;\r\n    }\r\n\r\n    if (!editingQuiz.quiz) return;\r\n\r\n    const updatedQuestions = [...editingQuiz.quiz.questions];\r\n    const existingIndex = updatedQuestions.findIndex(q => q.id === editingQuestion.id);\r\n    \r\n    if (existingIndex >= 0) {\r\n      updatedQuestions[existingIndex] = editingQuestion;\r\n    } else {\r\n      updatedQuestions.push(editingQuestion);\r\n    }\r\n\r\n    setEditingQuiz(prev => ({\r\n      ...prev,\r\n      quiz: prev.quiz ? { ...prev.quiz, questions: updatedQuestions } : null\r\n    }));\r\n    \r\n    setIsQuestionDialogOpen(false);\r\n    setEditingQuestion(null);\r\n    toast.success('Pertanyaan berhasil disimpan');\r\n  };\r\n\r\n  const deleteQuestion = (questionId: string) => {\r\n    if (!editingQuiz.quiz) return;\r\n\r\n    const updatedQuestions = editingQuiz.quiz.questions\r\n      .filter(q => q.id !== questionId)\r\n      .map((q, index) => ({ ...q, orderIndex: index }));\r\n\r\n    setEditingQuiz(prev => ({\r\n      ...prev,\r\n      quiz: prev.quiz ? { ...prev.quiz, questions: updatedQuestions } : null\r\n    }));\r\n    \r\n    toast.success('Pertanyaan berhasil dihapus');\r\n  };\r\n\r\n  const getCompletionStatus = () => {\r\n    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);\r\n    const completedChapters = data.modules.reduce((acc, module) => \r\n      acc + module.chapters.filter(chapter => chapter.content && chapter.content.length > 0).length, 0\r\n    );\r\n    \r\n    return {\r\n      total: totalChapters,\r\n      completed: completedChapters,\r\n      percentage: totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0\r\n    };\r\n  };\r\n\r\n  const completionStatus = getCompletionStatus();\r\n\r\n  if (data.modules.length === 0) {\r\n    return (\r\n      <div className=\"text-center py-12\">\r\n        <BookOpen className=\"w-12 h-12 text-muted-foreground mx-auto mb-4\" />\r\n        <h3 className=\"text-lg font-semibold mb-2\">Belum ada modul</h3>\r\n        <p className=\"text-muted-foreground\">\r\n          Kembali ke langkah sebelumnya untuk membuat struktur modul terlebih dahulu\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Progress */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold\">Pembuatan Konten</h3>\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Tambahkan konten dan quiz untuk setiap chapter\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-4\">\r\n          <div className=\"text-right\">\r\n            <div className=\"text-sm font-medium\">\r\n              {completionStatus.completed} / {completionStatus.total} Chapter\r\n            </div>\r\n            <div className=\"text-xs text-muted-foreground\">\r\n              {completionStatus.percentage}% selesai\r\n            </div>\r\n          </div>\r\n          <div className={cn(\r\n            \"w-12 h-12 rounded-full flex items-center justify-center\",\r\n            completionStatus.percentage === 100 \r\n              ? \"bg-green-100 text-green-600\" \r\n              : \"bg-muted text-muted-foreground\"\r\n          )}>\r\n            {completionStatus.percentage === 100 ? (\r\n              <CheckCircle className=\"w-6 h-6\" />\r\n            ) : (\r\n              <Clock className=\"w-6 h-6\" />\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\r\n        {/* Module/Chapter Navigation */}\r\n        <div className=\"lg:col-span-1\">\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"text-base\">Navigasi Konten</CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4 max-h-[70vh] overflow-y-auto\">\r\n              {/* Final Exam Section */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"p-3 rounded-lg border-2 border-dashed border-primary/30 bg-primary/5\">\r\n                  <div className=\"flex items-center justify-between mb-2\">\r\n                    <div>\r\n                      <div className=\"font-medium text-sm text-primary\">Final Exam</div>\r\n                      <div className=\"text-xs text-muted-foreground\">\r\n                        Ujian akhir untuk seluruh course\r\n                      </div>\r\n                    </div>\r\n                    {data.finalExam && (\r\n                      <CheckCircle className=\"w-4 h-4 text-green-600\" />\r\n                    )}\r\n                  </div>\r\n                  <Button\r\n                    variant={data.finalExam ? \"outline\" : \"default\"}\r\n                    size=\"sm\"\r\n                    className=\"w-full\"\r\n                    onClick={() => {\r\n                      if (data.finalExam) {\r\n                        editQuiz('final', data.finalExam);\r\n                      } else {\r\n                        createQuiz('final');\r\n                      }\r\n                    }}\r\n                  >\r\n                    <HelpCircle className=\"w-4 h-4 mr-2\" />\r\n                    {data.finalExam ? 'Edit Final Exam' : 'Buat Final Exam'}\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modules */}\r\n              {data.modules.map(module => (\r\n                <div key={module.id} className=\"space-y-2\">\r\n                  <div \r\n                    className={cn(\r\n                      \"p-2 rounded-lg cursor-pointer transition-colors\",\r\n                      selectedModule === module.id \r\n                        ? \"bg-primary text-primary-foreground\" \r\n                        : \"bg-muted hover:bg-muted/80\"\r\n                    )}\r\n                    onClick={() => {\r\n                      setSelectedModule(module.id);\r\n                      setSelectedChapter('');\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div>\r\n                        <div className=\"font-medium text-sm\">{module.name}</div>\r\n                        <div className=\"text-xs opacity-75\">\r\n                          {module.chapters.length} chapters\r\n                        </div>\r\n                      </div>\r\n                      {module.moduleQuiz && (\r\n                        <Badge variant=\"secondary\" className=\"text-xs\">\r\n                          <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                          Quiz\r\n                        </Badge>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {selectedModule === module.id && (\r\n                    <div className=\"ml-4 space-y-2\">\r\n                      {/* Module Quiz Button */}\r\n                      <div className=\"p-2 rounded bg-secondary/50\">\r\n                        <Button\r\n                          variant={module.moduleQuiz ? \"outline\" : \"secondary\"}\r\n                          size=\"sm\"\r\n                          className=\"w-full text-xs\"\r\n                          onClick={() => {\r\n                            if (module.moduleQuiz) {\r\n                              editQuiz('module', module.moduleQuiz);\r\n                            } else {\r\n                              createQuiz('module');\r\n                            }\r\n                          }}\r\n                        >\r\n                          <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                          {module.moduleQuiz ? 'Edit Module Quiz' : 'Buat Module Quiz'}\r\n                        </Button>\r\n                      </div>\r\n                      \r\n                      {/* Chapters */}\r\n                      {module.chapters.map(chapter => {\r\n                        const hasContent = chapter.content && chapter.content.length > 0;\r\n                        return (\r\n                          <div key={chapter.id} className=\"space-y-1\">\r\n                            <div\r\n                              className={cn(\r\n                                \"p-2 rounded text-xs cursor-pointer transition-colors flex items-center justify-between\",\r\n                                selectedChapter === chapter.id\r\n                                  ? \"bg-primary/20 text-primary\"\r\n                                  : \"hover:bg-muted/50\"\r\n                              )}\r\n                              onClick={() => setSelectedChapter(chapter.id)}\r\n                            >\r\n                              <span>{chapter.name}</span>\r\n                              {hasContent && (\r\n                                <CheckCircle className=\"w-3 h-3 text-green-600\" />\r\n                              )}\r\n                            </div>\r\n                            \r\n                            {/* Content Block Navigation - show when chapter is selected and has content */}\r\n                            {selectedChapter === chapter.id && hasContent && chapter.content && (\r\n                              <div className=\"ml-4 space-y-1\">\r\n                                <div className=\"flex items-center space-x-1 text-xs text-muted-foreground mb-1\">\r\n                                  <Navigation className=\"w-3 h-3\" />\r\n                                  <span>Content Blocks</span>\r\n                                </div>\r\n                                {chapter.content.map((block, index) => (\r\n                                  <button\r\n                                    key={block.id || index}\r\n                                    onClick={() => scrollToContent(block.id || `block-${index}`)}\r\n                                    className=\"w-full text-left p-1.5 rounded text-xs hover:bg-primary/10 transition-colors flex items-center space-x-2\"\r\n                                  >\r\n                                    {getContentTypeIcon(block.type)}\r\n                                    <span className=\"truncate flex-1\">\r\n                                      {index + 1}. {getContentPreview(block)}\r\n                                    </span>\r\n                                  </button>\r\n                                ))}\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Content Editor */}\r\n        <div className=\"lg:col-span-3\">\r\n          {!selectedChapter ? (\r\n            <div className=\"space-y-6\">\r\n              {/* Final Exam Info */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center space-x-2\">\r\n                    <HelpCircle className=\"w-5 h-5 text-primary\" />\r\n                    <span>Final Exam</span>\r\n                    {data.finalExam && (\r\n                      <Badge variant=\"secondary\">Sudah dibuat</Badge>\r\n                    )}\r\n                  </CardTitle>\r\n                  <CardDescription>\r\n                    Ujian akhir untuk menguji pemahaman siswa terhadap seluruh materi course\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  {data.finalExam ? (\r\n                    <div className=\"space-y-4\">\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                        <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                          <div className=\"text-2xl font-bold text-primary\">\r\n                            {data.finalExam.questions.length}\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">Pertanyaan</div>\r\n                        </div>\r\n                        <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                          <div className=\"text-2xl font-bold text-primary\">\r\n                            {data.finalExam.minimumScore}%\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">Nilai Minimum</div>\r\n                        </div>\r\n                        <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                          <div className=\"text-2xl font-bold text-primary\">\r\n                            {data.finalExam.questions.reduce((sum, q) => sum + q.points, 0)}\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">Total Poin</div>\r\n                        </div>\r\n                        {data.finalExam.timeLimit && (\r\n                          <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                            <div className=\"text-2xl font-bold text-primary\">\r\n                              {data.finalExam.timeLimit}\r\n                            </div>\r\n                            <div className=\"text-sm text-muted-foreground\">Menit</div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"flex space-x-2\">\r\n                        <Button\r\n                          onClick={() => editQuiz('final', data.finalExam!)}\r\n                          className=\"flex-1\"\r\n                        >\r\n                          <Edit className=\"w-4 h-4 mr-2\" />\r\n                          Edit Final Exam\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"text-center py-8\">\r\n                      <HelpCircle className=\"w-12 h-12 text-muted-foreground mx-auto mb-4\" />\r\n                      <h3 className=\"text-lg font-semibold mb-2\">Belum ada Final Exam</h3>\r\n                      <p className=\"text-muted-foreground mb-4\">\r\n                        Final Exam adalah ujian akhir yang menguji pemahaman siswa terhadap seluruh materi course\r\n                      </p>\r\n                      <Button onClick={() => createQuiz('final')}>\r\n                        <Plus className=\"w-4 h-4 mr-2\" />\r\n                        Buat Final Exam\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </CardContent>\r\n              </Card>\r\n\r\n              {/* Module Overview */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle>Overview Modul</CardTitle>\r\n                  <CardDescription>\r\n                    Pilih chapter dari navigasi di sebelah kiri untuk mulai menambahkan konten\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                    {data.modules.map(module => (\r\n                      <div key={module.id} className=\"p-4 border rounded-lg\">\r\n                        <div className=\"flex items-center justify-between mb-2\">\r\n                          <h4 className=\"font-medium\">{module.name}</h4>\r\n                          {module.moduleQuiz && (\r\n                            <Badge variant=\"secondary\">\r\n                              <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                              Quiz\r\n                            </Badge>\r\n                          )}\r\n                        </div>\r\n                        <div className=\"text-sm text-muted-foreground mb-3\">\r\n                          {module.chapters.length} chapters\r\n                        </div>\r\n                        <div className=\"space-y-1\">\r\n                          {module.chapters.map(chapter => {\r\n                            const hasContent = chapter.content && chapter.content.length > 0;\r\n                            return (\r\n                              <div key={chapter.id} className=\"flex items-center justify-between text-xs\">\r\n                                <span>{chapter.name}</span>\r\n                                {hasContent ? (\r\n                                  <CheckCircle className=\"w-3 h-3 text-green-600\" />\r\n                                ) : (\r\n                                  <Clock className=\"w-3 h-3 text-muted-foreground\" />\r\n                                )}\r\n                              </div>\r\n                            );\r\n                          })}\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-6\">\r\n              {/* Chapter Header */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <CardTitle className=\"flex items-center space-x-2\">\r\n                        <span>{currentChapter?.name}</span>\r\n                        {currentChapter?.hasChapterQuiz && (\r\n                          <Badge variant=\"secondary\">\r\n                            <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                            Chapter Quiz\r\n                          </Badge>\r\n                        )}\r\n                        {currentModule?.moduleQuiz && (\r\n                          <Badge variant=\"outline\">\r\n                            <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                            Module Quiz\r\n                          </Badge>\r\n                        )}\r\n                      </CardTitle>\r\n                      <CardDescription>\r\n                        Modul: {currentModule?.name}\r\n                        {currentModule?.moduleQuiz && (\r\n                          <span className=\"ml-2 text-xs text-primary\">\r\n                            • Module ini memiliki quiz\r\n                          </span>\r\n                        )}\r\n                      </CardDescription>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() => setPreviewMode(!previewMode)}\r\n                      >\r\n                        <Eye className=\"w-4 h-4 mr-2\" />\r\n                        {previewMode ? 'Edit' : 'Preview'}\r\n                      </Button>\r\n                      {currentModule?.moduleQuiz && (\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"sm\"\r\n                          onClick={() => editQuiz('module', currentModule.moduleQuiz!)}\r\n                        >\r\n                          <HelpCircle className=\"w-4 h-4 mr-2\" />\r\n                          Edit Module Quiz\r\n                        </Button>\r\n                      )}\r\n                      {currentChapter?.hasChapterQuiz && (\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"sm\"\r\n                          onClick={() => {\r\n                            if (currentChapter.chapterQuiz) {\r\n                              editQuiz('chapter', currentChapter.chapterQuiz);\r\n                            } else {\r\n                              createQuiz('chapter');\r\n                            }\r\n                          }}\r\n                        >\r\n                          <HelpCircle className=\"w-4 h-4 mr-2\" />\r\n                          {currentChapter.chapterQuiz ? 'Edit Chapter Quiz' : 'Buat Chapter Quiz'}\r\n                        </Button>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </CardHeader>\r\n              </Card>\r\n\r\n              {/* Content Editor/Preview with Scrollable Container */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"text-base\">Konten Chapter</CardTitle>\r\n                  <CardDescription>\r\n                    {previewMode \r\n                      ? 'Preview konten seperti yang akan dilihat siswa'\r\n                      : 'Gunakan Markdown untuk memformat konten'}\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  {previewMode ? (\r\n                    <div className=\"max-h-[60vh] overflow-y-auto prose max-w-none pr-4\">\r\n                      {currentChapter?.content && currentChapter.content.length > 0 ? (\r\n                        <>\r\n                          {currentChapter.content.map((block: any, index: number) => (\r\n                            <div \r\n                              key={block.id || index} \r\n                              ref={(el) => {\r\n                                contentRefs.current[block.id || `block-${index}`] = el;\r\n                              }}\r\n                              className=\"mb-6 scroll-mt-4\"\r\n                              id={block.id || `block-${index}`}\r\n                            >\r\n                              {/* Content Block Header for easier identification */}\r\n                              <div className=\"flex items-center space-x-2 mb-2 py-1 px-2 bg-muted/30 rounded text-xs text-muted-foreground\">\r\n                                {getContentTypeIcon(block.type)}\r\n                                <span>Content-{index + 1}</span>\r\n                                <span>({block.type})</span>\r\n                              </div>\r\n                              \r\n                              {block.type === 'text' ? (\r\n                                <ReactMarkdown\r\n                                  remarkPlugins={[remarkGfm]}\r\n                                  components={{\r\n                                    h1: ({ node, ...props }) => (\r\n                                      <h1 className=\"mb-4 text-2xl font-bold text-gray-900\" {...props} />\r\n                                    ),\r\n                                    h2: ({ node, ...props }) => (\r\n                                      <h2 className=\"mb-3 text-xl font-semibold text-gray-800\" {...props} />\r\n                                    ),\r\n                                    h3: ({ node, ...props }) => (\r\n                                      <h3 className=\"mb-2 text-lg font-semibold text-gray-800\" {...props} />\r\n                                    ),\r\n                                    h4: ({ node, ...props }) => (\r\n                                      <h4 className=\"mb-2 text-base font-semibold text-gray-700\" {...props} />\r\n                                    ),\r\n                                    p: ({ node, ...props }) => (\r\n                                      <p className=\"mb-3 leading-relaxed\" {...props} />\r\n                                    ),\r\n                                    ul: ({ node, ...props }) => (\r\n                                      <ul className=\"mb-3 ml-4 list-disc\" {...props} />\r\n                                    ),\r\n                                    ol: ({ node, ...props }) => (\r\n                                      <ol className=\"mb-3 ml-4 list-decimal\" {...props} />\r\n                                    ),\r\n                                    li: ({ node, ...props }) => (\r\n                                      <li className=\"mb-1\" {...props} />\r\n                                    ),\r\n                                    blockquote: ({ node, ...props }) => (\r\n                                      <blockquote\r\n                                        className=\"mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic\"\r\n                                        {...props}\r\n                                      />\r\n                                    ),\r\n                                    code: ({ node, className, children, ...props }) => {\r\n                                      const match = /language-(\\w+)/.exec(className || '');\r\n                                      const isInline = !match;\r\n                                      return isInline ? (\r\n                                        <code\r\n                                          className=\"rounded bg-gray-100 px-1 py-0.5 font-mono text-sm\"\r\n                                          {...props}\r\n                                        >\r\n                                          {children}\r\n                                        </code>\r\n                                      ) : (\r\n                                        <code\r\n                                          className=\"block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100\"\r\n                                          {...props}\r\n                                        >\r\n                                          {children}\r\n                                        </code>\r\n                                      );\r\n                                    },\r\n                                    pre: ({ node, ...props }) => (\r\n                                      <pre className=\"mb-4\" {...props} />\r\n                                    ),\r\n                                    table: ({ node, ...props }) => (\r\n                                      <div className=\"mb-4 overflow-x-auto\">\r\n                                        <table\r\n                                          className=\"min-w-full rounded border border-gray-200\"\r\n                                          {...props}\r\n                                        />\r\n                                      </div>\r\n                                    ),\r\n                                    thead: ({ node, ...props }) => (\r\n                                      <thead className=\"bg-gray-50\" {...props} />\r\n                                    ),\r\n                                    th: ({ node, ...props }) => (\r\n                                      <th\r\n                                        className=\"border border-gray-200 px-3 py-2 text-left font-semibold\"\r\n                                        {...props}\r\n                                      />\r\n                                    ),\r\n                                    td: ({ node, ...props }) => (\r\n                                      <td\r\n                                        className=\"border border-gray-200 px-3 py-2\"\r\n                                        {...props}\r\n                                      />\r\n                                    ),\r\n                                    hr: ({ node, ...props }) => (\r\n                                      <hr className=\"my-6 border-gray-300\" {...props} />\r\n                                    ),\r\n                                    strong: ({ node, ...props }) => (\r\n                                      <strong\r\n                                        className=\"font-semibold text-gray-900\"\r\n                                        {...props}\r\n                                      />\r\n                                    ),\r\n                                    em: ({ node, ...props }) => (\r\n                                      <em className=\"italic\" {...props} />\r\n                                    )\r\n                                  }}\r\n                                >\r\n                                  {block.value}\r\n                                </ReactMarkdown>\r\n                              ) : block.type === 'image' ? (\r\n                                <div className=\"my-4\">\r\n                                  <img \r\n                                    src={block.value} \r\n                                    alt=\"Content\" \r\n                                    className=\"max-w-full h-auto rounded-md\"\r\n                                  />\r\n                                </div>\r\n                              ) : block.type === 'video' ? (\r\n                                <div className=\"my-4\">\r\n                                  <video \r\n                                    src={block.value} \r\n                                    controls \r\n                                    className=\"max-w-full rounded-md\"\r\n                                  />\r\n                                </div>\r\n                              ) : block.type === 'pdf' ? (\r\n                                <div className=\"my-4\">\r\n                                  <iframe \r\n                                    src={block.value} \r\n                                    className=\"w-full h-96 rounded-md\"\r\n                                    title=\"PDF Content\"\r\n                                  />\r\n                                </div>\r\n                              ) : (\r\n                                <div className=\"my-4 p-4 bg-muted rounded-md\">\r\n                                  <p className=\"text-sm text-muted-foreground\">\r\n                                    {block.type === 'zoom-recording' \r\n                                      ? 'Zoom Recording: ' \r\n                                      : 'File: '}\r\n                                    <a \r\n                                      href={block.value} \r\n                                      target=\"_blank\" \r\n                                      rel=\"noopener noreferrer\"\r\n                                      className=\"text-primary hover:underline\"\r\n                                    >\r\n                                      {block.value}\r\n                                    </a>\r\n                                  </p>\r\n                                </div>\r\n                              )}\r\n                            </div>\r\n                          ))}\r\n                        </>\r\n                      ) : (\r\n                        <p className=\"text-muted-foreground italic\">\r\n                          Belum ada konten untuk chapter ini\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"space-y-4\">\r\n                      <div className=\"max-h-[60vh] overflow-y-auto pr-4\">\r\n                        <DynamicContentEditor\r\n                          initialContent={currentChapter?.content || []}\r\n                          onContentChange={updateChapterContent}\r\n                          contentRefs={contentRefs}\r\n                        />\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center text-sm text-muted-foreground\">\r\n                        <span>Mendukung Markdown formatting</span>\r\n                        <span>\r\n                          {currentChapter?.content?.length || 0} blok konten\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Quiz Dialog */}\r\n      <Dialog open={isQuizDialogOpen} onOpenChange={setIsQuizDialogOpen}>\r\n        <DialogContent className=\"sm:max-w-4xl max-h-[80vh] overflow-y-auto p-6\">\r\n          <DialogHeader>\r\n            <DialogTitle>\r\n              {editingQuiz.quiz?.questions.length ? 'Edit Quiz' : 'Buat Quiz Baru'}\r\n            </DialogTitle>\r\n            <DialogDescription>\r\n              Buat pertanyaan untuk menguji pemahaman siswa\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"space-y-6\">\r\n            {/* Quiz Info */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"quizName\">Nama Quiz *</Label>\r\n                <Input\r\n                  id=\"quizName\"\r\n                  placeholder=\"Masukkan nama quiz\"\r\n                  value={editingQuiz.quiz?.name || ''}\r\n                  onChange={(e) => setEditingQuiz(prev => ({\r\n                    ...prev,\r\n                    quiz: prev.quiz ? { ...prev.quiz, name: e.target.value } : null\r\n                  }))}\r\n                />\r\n              </div>\r\n              \r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"minimumScore\">Nilai Minimum (%)</Label>\r\n                <Input\r\n                  id=\"minimumScore\"\r\n                  type=\"number\"\r\n                  min=\"0\"\r\n                  max=\"100\"\r\n                  value={editingQuiz.quiz?.minimumScore || 70}\r\n                  onChange={(e) => setEditingQuiz(prev => ({\r\n                    ...prev,\r\n                    quiz: prev.quiz ? { ...prev.quiz, minimumScore: parseInt(e.target.value) } : null\r\n                  }))}\r\n                />\r\n              </div>\r\n              \r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"timeLimit\">Batas Waktu (menit)</Label>\r\n                <Input\r\n                  id=\"timeLimit\"\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  value={editingQuiz.quiz?.timeLimit || ''}\r\n                  onChange={(e) => setEditingQuiz(prev => ({\r\n                    ...prev,\r\n                    quiz: prev.quiz ? { ...prev.quiz, timeLimit: e.target.value ? parseInt(e.target.value) : undefined } : null\r\n                  }))}\r\n                  placeholder=\"Tanpa batas waktu\"\r\n                />\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"quizDescription\">Deskripsi</Label>\r\n              <Textarea\r\n                id=\"quizDescription\"\r\n                placeholder=\"Jelaskan tentang quiz ini...\"\r\n                value={editingQuiz.quiz?.description || ''}\r\n                onChange={(e) => setEditingQuiz(prev => ({\r\n                  ...prev,\r\n                  quiz: prev.quiz ? { ...prev.quiz, description: e.target.value } : null\r\n                }))}\r\n                rows={2}\r\n              />\r\n            </div>\r\n\r\n            {/* Questions */}\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h4 className=\"text-lg font-semibold\">Pertanyaan</h4>\r\n                <Button onClick={createQuestion}>\r\n                  <Plus className=\"w-4 h-4 mr-2\" />\r\n                  Tambah Pertanyaan\r\n                </Button>\r\n              </div>\r\n              \r\n              {editingQuiz.quiz?.questions.length === 0 ? (\r\n                <div className=\"text-center py-8 text-muted-foreground\">\r\n                  <HelpCircle className=\"w-8 h-8 mx-auto mb-2\" />\r\n                  <p className=\"text-sm\">Belum ada pertanyaan</p>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-3\">\r\n                  {editingQuiz.quiz?.questions.map((question, index) => (\r\n                    <Card key={question.id}>\r\n                      <CardContent className=\"p-4\">\r\n                        <div className=\"flex items-start justify-between\">\r\n                          <div className=\"flex-1\">\r\n                            <div className=\"flex items-center space-x-2 mb-2\">\r\n                              <Badge variant=\"outline\">{index + 1}</Badge>\r\n                              <Badge variant=\"secondary\">\r\n                                {question.type === 'multiple_choice' ? 'Pilihan Ganda' :\r\n                                 question.type === 'true_false' ? 'Benar/Salah' : 'Essay'}\r\n                              </Badge>\r\n                              <span className=\"text-sm text-muted-foreground\">\r\n                                {question.points} poin\r\n                              </span>\r\n                            </div>\r\n                            <div className=\"text-sm\">\r\n                              {question.question.map((block, blockIndex) => (\r\n                                <React.Fragment key={blockIndex}>\r\n                                  {block.type === 'text' && <p>{block.value}</p>}\r\n                                  {block.type === 'image' && block.value && (\r\n                                    <img src={block.value} alt={`Question image ${blockIndex}`} className=\"max-w-xs max-h-32 object-contain mt-2\" />\r\n                                  )}\r\n                                </React.Fragment>\r\n                              ))}\r\n                            </div>\r\n                            {question.type === 'multiple_choice' && question.options && (\r\n                              <div className=\"mt-2 space-y-1\">\r\n                                {question.options.map((option, optIndex) => (\r\n                                  <div key={optIndex} className=\"text-xs text-muted-foreground\">\r\n                                    {String.fromCharCode(65 + optIndex)}.\r\n                                    {option.content.map((block, optionBlockIndex) => (\r\n                                      <React.Fragment key={optionBlockIndex}>\r\n                                        {block.type === 'text' && <span>{block.value}</span>}\r\n                                        {block.type === 'image' && block.value && (\r\n                                          <img src={block.value} alt={`Option image ${optionBlockIndex}`} className=\"inline-block max-h-8 object-contain ml-1\" />\r\n                                        )}\r\n                                      </React.Fragment>\r\n                                    ))}\r\n                                  </div>\r\n                                ))}\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                          <div className=\"flex items-center space-x-1\">\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              size=\"sm\"\r\n                              onClick={() => editQuestion(question)}\r\n                            >\r\n                              <Edit className=\"w-4 h-4\" />\r\n                            </Button>\r\n                            <AlertDialog>\r\n                              <AlertDialogTrigger asChild>\r\n                                <Button variant=\"ghost\" size=\"sm\">\r\n                                  <Trash2 className=\"w-4 h-4\" />\r\n                                </Button>\r\n                              </AlertDialogTrigger>\r\n                              <AlertDialogContent>\r\n                                <AlertDialogHeader>\r\n                                  <AlertDialogTitle>Hapus Pertanyaan</AlertDialogTitle>\r\n                                  <AlertDialogDescription>\r\n                                    Apakah Anda yakin ingin menghapus pertanyaan ini?\r\n                                  </AlertDialogDescription>\r\n                                </AlertDialogHeader>\r\n                                <AlertDialogFooter>\r\n                                  <AlertDialogCancel>Batal</AlertDialogCancel>\r\n                                  <AlertDialogAction onClick={() => deleteQuestion(question.id)}>\r\n                                    Hapus\r\n                                  </AlertDialogAction>\r\n                                </AlertDialogFooter>\r\n                              </AlertDialogContent>\r\n                            </AlertDialog>\r\n                          </div>\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          \r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setIsQuizDialogOpen(false)}>\r\n              Batal\r\n            </Button>\r\n            <Button onClick={saveQuiz}>\r\n              <Save className=\"w-4 h-4 mr-2\" />\r\n              Simpan Quiz\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Question Dialog */}\r\n      <Dialog open={isQuestionDialogOpen} onOpenChange={setIsQuestionDialogOpen}>\r\n        <DialogContent className=\"sm:max-w-2xl max-h-[80vh] overflow-y-auto p-6\">\r\n          <DialogHeader>\r\n            <DialogTitle>\r\n              {editingQuestion?.question ? 'Edit Pertanyaan' : 'Tambah Pertanyaan Baru'}\r\n            </DialogTitle>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"space-y-4\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"questionType\">Tipe Pertanyaan</Label>\r\n                <Select \r\n                  value={editingQuestion?.type || 'multiple_choice'}\r\n                  onValueChange={(value: 'multiple_choice' | 'true_false' | 'essay') => {\r\n                    setEditingQuestion(prev => {\r\n                      if (!prev) return null;\r\n                      const newQuestion = { ...prev, type: value };\r\n                      if (value === 'true_false') {\r\n                        newQuestion.options = [\r\n                          { content: [{ type: 'text', value: 'True' }], isCorrect: false },\r\n                          { content: [{ type: 'text', value: 'False' }], isCorrect: false }\r\n                        ];\r\n                      } else if (value === 'multiple_choice') {\r\n                        newQuestion.options = [\r\n                          { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n                          { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n                          { content: [{ type: 'text', value: '' }], isCorrect: false },\r\n                          { content: [{ type: 'text', value: '' }], isCorrect: false }\r\n                        ];\r\n                      } else {\r\n                        newQuestion.options = undefined; // Clear options for essay\r\n                      }\r\n                      return newQuestion;\r\n                    });\r\n                  }}\r\n                >\r\n                  <SelectTrigger>\r\n                    <SelectValue />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"multiple_choice\">Pilihan Ganda</SelectItem>\r\n                    <SelectItem value=\"true_false\">Benar/Salah</SelectItem>\r\n                    <SelectItem value=\"essay\">Essay</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n              \r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"questionPoints\">Poin</Label>\r\n                <Input\r\n                  id=\"questionPoints\"\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  value={editingQuestion?.points || 1}\r\n                  onChange={(e) => setEditingQuestion(prev => \r\n                    prev ? { ...prev, points: parseInt(e.target.value) } : null\r\n                  )}\r\n                />\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"questionText\">Pertanyaan *</Label>\r\n              <DynamicContentEditor\r\n                initialContent={editingQuestion?.question || []}\r\n                onContentChange={(content) => setEditingQuestion(prev =>\r\n                  prev ? { ...prev, question: content } : null\r\n                )}\r\n                allowImages={true} // Allow images in questions\r\n              />\r\n            </div>\r\n            \r\n            {(editingQuestion?.type === 'multiple_choice' || editingQuestion?.type === 'true_false') && (\r\n              <div className=\"space-y-4\">\r\n                <Label>Pilihan Jawaban</Label>\r\n                {editingQuestion.options?.map((option, index) => (\r\n                  <div key={index} className=\"flex flex-col space-y-2 border p-3 rounded-md\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      {editingQuestion.type === 'multiple_choice' && (\r\n                        <span className=\"text-sm font-medium w-6\">\r\n                          {String.fromCharCode(65 + index)}.\r\n                        </span>\r\n                      )}\r\n                      {editingQuestion.type === 'multiple_choice' ? (\r\n                        <DynamicContentEditor\r\n                          initialContent={option.content || []}\r\n                          onContentChange={(content) => {\r\n                            const newOptions = [...(editingQuestion.options || [])];\r\n                            newOptions[index] = { ...newOptions[index], content: content };\r\n                            setEditingQuestion(prev =>\r\n                              prev ? { ...prev, options: newOptions } : null\r\n                            );\r\n                          }}\r\n                          allowImages={true} // Allow images in options\r\n                        />\r\n                      ) : (\r\n                        <span className=\"text-base font-medium\">{option.content[0].value}</span>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2 mt-2\">\r\n                      <Checkbox\r\n                        id={`option-correct-${index}`}\r\n                        checked={option.isCorrect}\r\n                        onCheckedChange={(checked: boolean) => {\r\n                          const newOptions = [...(editingQuestion.options || [])];\r\n                          newOptions[index] = { ...newOptions[index], isCorrect: checked as boolean };\r\n                          setEditingQuestion(prev =>\r\n                            prev ? { ...prev, options: newOptions } : null\r\n                          );\r\n                        }}\r\n                      />\r\n                      <Label htmlFor={`option-correct-${index}`}>Jawaban Benar</Label>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            \r\n            \r\n            {editingQuestion && editingQuestion.type === 'essay' && (\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"essay-answer\">Jawaban Esai</Label>\r\n                <Textarea\r\n                  id=\"essay-answer\"\r\n                  placeholder=\"Masukkan jawaban esai untuk pertanyaan ini\"\r\n                  value={editingQuestion.essayAnswer || ''}\r\n                  onChange={(e) => setEditingQuestion(prev =>\r\n                    prev ? { ...prev, essayAnswer: e.target.value } : null\r\n                  )}\r\n                  rows={4}\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {editingQuestion && (\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"explanation\">Penjelasan Jawaban (Opsional)</Label>\r\n                <DynamicContentEditor\r\n                  initialContent={editingQuestion?.explanation || []}\r\n                  onContentChange={(content) => {\r\n                    setEditingQuestion(prev =>\r\n                      prev ? { ...prev, explanation: content } : null\r\n                    );\r\n                  }}\r\n                  placeholder=\"Jelaskan jawaban yang benar atau berikan informasi tambahan\"\r\n                  allowImages={true}\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setIsQuestionDialogOpen(false)}>\r\n              Batal\r\n            </Button>\r\n            <Button onClick={saveQuestion}>\r\n              {editingQuestion?.question ? 'Perbarui' : 'Tambah'} Pertanyaan\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA,uOAAqD,sBAAsB;AAC3E;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAEA;AACA;AACA;AApCA;;;;;;;;;;;;;;;;;;;AA2CO,SAAS,oBAAoB,EAAE,IAAI,EAAE,QAAQ,EAA4B;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,KAAK,OAAO,CAAC,EAAE,EAAE,MAAM;IACpF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmE;QAC9G,MAAM;QACN,MAAM;IACR;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC5E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,gCAAgC;IAChC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA4C,CAAC;IAEtE,MAAM,gBAAgB,KAAK,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACtD,MAAM,iBAAiB,eAAe,SAAS,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK;IAElE,+CAA+C;IAC/C,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,YAAY,OAAO,CAAC,QAAQ;QAC5C,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBACrB,UAAU;gBACV,OAAO;gBACP,QAAQ;YACV;QACF;IACF;IAEA,oCAAoC;IACpC,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,8OAAC,sMAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,wCAAwC;IACxC,MAAM,oBAAoB,CAAC;QACzB,IAAI,MAAM,IAAI,KAAK,QAAQ;YACzB,OAAO,MAAM,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,KAAK,QAAQ,EAAE,KAAK;QAC9F;QACA,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC;IAC/D;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,CAAC,iBAAiB,CAAC,gBAAgB;QAEvC,MAAM,iBAAiB,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA;YACtC,IAAI,OAAO,EAAE,KAAK,gBAAgB;gBAChC,MAAM,kBAAkB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA;oBAC1C,IAAI,QAAQ,EAAE,KAAK,iBAAiB;wBAClC,OAAO;4BAAE,GAAG,OAAO;4BAAE;wBAAQ;oBAC/B;oBACA,OAAO;gBACT;gBACA,OAAO;oBAAE,GAAG,MAAM;oBAAE,UAAU;gBAAgB;YAChD;YACA,OAAO;QACT;QAEA,SAAS;YAAE,SAAS;QAAe;IACrC;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,UAAoB;YACxB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,MAAM,SAAS,YAAY,CAAC,KAAK,EAAE,gBAAgB,MAAM,GACnD,SAAS,WAAW,CAAC,KAAK,EAAE,eAAe,MAAM,GACjD,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;YACjC,aAAa;YACb,WAAW,EAAE;YACb,cAAc;YACd,WAAW,SAAS,UAAU,MAAM,UAAU,iCAAiC;QACjF;QAEA,eAAe;YAAE;YAAM,MAAM;QAAQ;QACrC,oBAAoB;IACtB;IAEA,MAAM,WAAW,CAAC,MAAsC;QACtD,eAAe;YAAE;YAAM,MAAM;gBAAE,GAAG,IAAI;YAAC;QAAE;QACzC,oBAAoB;IACtB;IAEA,MAAM,WAAW;QACf,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;YACtD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,YAAY,IAAI,KAAK,SAAS;YAChC,SAAS;gBAAE,WAAW,YAAY,IAAI;YAAE;QAC1C,OAAO;YACL,MAAM,iBAAiB,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA;gBACtC,IAAI,OAAO,EAAE,KAAK,gBAAgB;oBAChC,IAAI,YAAY,IAAI,KAAK,UAAU;wBACjC,OAAO;4BAAE,GAAG,MAAM;4BAAE,YAAY,YAAY,IAAI;wBAAE;oBACpD,OAAO;wBACL,MAAM,kBAAkB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA;4BAC1C,IAAI,QAAQ,EAAE,KAAK,iBAAiB;gCAClC,OAAO;oCAAE,GAAG,OAAO;oCAAE,aAAa,YAAY,IAAI;gCAAE;4BACtD;4BACA,OAAO;wBACT;wBACA,OAAO;4BAAE,GAAG,MAAM;4BAAE,UAAU;wBAAgB;oBAChD;gBACF;gBACA,OAAO;YACT;YACA,SAAS;gBAAE,SAAS;YAAe;QACrC;QAEA,oBAAoB;QACpB,eAAe;YAAE,MAAM;YAAW,MAAM;QAAK;QAC7C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,iBAAiB;QACrB,MAAM,cAA4B;YAChC,IAAI,iBAAiB,MAAM,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;YACnD,MAAM;YACN,UAAU;gBAAC;oBAAE,MAAM;oBAAQ,OAAO;gBAAG;aAAE;YACvC,SAAS,iBAAiB,SAAS,eAC/B;gBACE;oBAAE,SAAS;wBAAC;4BAAE,MAAM;4BAAQ,OAAO;wBAAO;qBAAE;oBAAE,WAAW;gBAAM;gBAC/D;oBAAE,SAAS;wBAAC;4BAAE,MAAM;4BAAQ,OAAO;wBAAQ;qBAAE;oBAAE,WAAW;gBAAM;aACjE,GACD;gBACE;oBAAE,SAAS;wBAAC;4BAAE,MAAM;4BAAQ,OAAO;wBAAG;qBAAE;oBAAE,WAAW;gBAAM;gBAC3D;oBAAE,SAAS;wBAAC;4BAAE,MAAM;4BAAQ,OAAO;wBAAG;qBAAE;oBAAE,WAAW;gBAAM;gBAC3D;oBAAE,SAAS;wBAAC;4BAAE,MAAM;4BAAQ,OAAO;wBAAG;qBAAE;oBAAE,WAAW;gBAAM;gBAC3D;oBAAE,SAAS;wBAAC;4BAAE,MAAM;4BAAQ,OAAO;wBAAG;qBAAE;oBAAE,WAAW;gBAAM;aAC5D;YACL,aAAa;YACb,aAAa,EAAE;YACf,QAAQ;YACR,YAAY,YAAY,IAAI,EAAE,UAAU,UAAU;QACpD;QAEA,mBAAmB;QACnB,wBAAwB;IAC1B;IAEA,MAAM,eAAe,CAAC;QACpB,mBAAmB;YAAE,GAAG,QAAQ;QAAC;QACjC,wBAAwB;IAC1B;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,mBAAmB,gBAAgB,QAAQ,CAAC,MAAM,KAAK,KAAM,gBAAgB,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,UAAU,CAAC,gBAAgB,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,IAAK;YAC3J,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,YAAY,IAAI,EAAE;QAEvB,MAAM,mBAAmB;eAAI,YAAY,IAAI,CAAC,SAAS;SAAC;QACxD,MAAM,gBAAgB,iBAAiB,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB,EAAE;QAEjF,IAAI,iBAAiB,GAAG;YACtB,gBAAgB,CAAC,cAAc,GAAG;QACpC,OAAO;YACL,iBAAiB,IAAI,CAAC;QACxB;QAEA,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,GAAG;oBAAE,GAAG,KAAK,IAAI;oBAAE,WAAW;gBAAiB,IAAI;YACpE,CAAC;QAED,wBAAwB;QACxB,mBAAmB;QACnB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,YAAY,IAAI,EAAE;QAEvB,MAAM,mBAAmB,YAAY,IAAI,CAAC,SAAS,CAChD,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YACrB,GAAG,CAAC,CAAC,GAAG,QAAU,CAAC;gBAAE,GAAG,CAAC;gBAAE,YAAY;YAAM,CAAC;QAEjD,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,GAAG;oBAAE,GAAG,KAAK,IAAI;oBAAE,WAAW;gBAAiB,IAAI;YACpE,CAAC;QAED,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,sBAAsB;QAC1B,MAAM,gBAAgB,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,QAAQ,CAAC,MAAM,EAAE;QACzF,MAAM,oBAAoB,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAClD,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG,MAAM,EAAE;QAGjG,OAAO;YACL,OAAO;YACP,WAAW;YACX,YAAY,gBAAgB,IAAI,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB,OAAO;QAC1F;IACF;IAEA,MAAM,mBAAmB;IAEzB,IAAI,KAAK,OAAO,CAAC,MAAM,KAAK,GAAG;QAC7B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,8OAAC;oBAAG,WAAU;8BAA6B;;;;;;8BAC3C,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAK3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,iBAAiB,SAAS;4CAAC;4CAAI,iBAAiB,KAAK;4CAAC;;;;;;;kDAEzD,8OAAC;wCAAI,WAAU;;4CACZ,iBAAiB,UAAU;4CAAC;;;;;;;;;;;;;0CAGjC,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,2DACA,iBAAiB,UAAU,KAAK,MAC5B,gCACA;0CAEH,iBAAiB,UAAU,KAAK,oBAC/B,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMzB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAY;;;;;;;;;;;8CAEnC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAmC;;;;;;kFAClD,8OAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;4DAIhD,KAAK,SAAS,kBACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;;kEAG3B,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,KAAK,SAAS,GAAG,YAAY;wDACtC,MAAK;wDACL,WAAU;wDACV,SAAS;4DACP,IAAI,KAAK,SAAS,EAAE;gEAClB,SAAS,SAAS,KAAK,SAAS;4DAClC,OAAO;gEACL,WAAW;4DACb;wDACF;;0EAEA,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DACrB,KAAK,SAAS,GAAG,oBAAoB;;;;;;;;;;;;;;;;;;wCAM3C,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA,uBAChB,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mDACA,mBAAmB,OAAO,EAAE,GACxB,uCACA;wDAEN,SAAS;4DACP,kBAAkB,OAAO,EAAE;4DAC3B,mBAAmB;wDACrB;kEAEA,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAuB,OAAO,IAAI;;;;;;sFACjD,8OAAC;4EAAI,WAAU;;gFACZ,OAAO,QAAQ,CAAC,MAAM;gFAAC;;;;;;;;;;;;;gEAG3B,OAAO,UAAU,kBAChB,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;;sFACnC,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;oDAO9C,mBAAmB,OAAO,EAAE,kBAC3B,8OAAC;wDAAI,WAAU;;0EAEb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAS,OAAO,UAAU,GAAG,YAAY;oEACzC,MAAK;oEACL,WAAU;oEACV,SAAS;wEACP,IAAI,OAAO,UAAU,EAAE;4EACrB,SAAS,UAAU,OAAO,UAAU;wEACtC,OAAO;4EACL,WAAW;wEACb;oEACF;;sFAEA,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEACrB,OAAO,UAAU,GAAG,qBAAqB;;;;;;;;;;;;4DAK7C,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA;gEACnB,MAAM,aAAa,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG;gEAC/D,qBACE,8OAAC;oEAAqB,WAAU;;sFAC9B,8OAAC;4EACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0FACA,oBAAoB,QAAQ,EAAE,GAC1B,+BACA;4EAEN,SAAS,IAAM,mBAAmB,QAAQ,EAAE;;8FAE5C,8OAAC;8FAAM,QAAQ,IAAI;;;;;;gFAClB,4BACC,8OAAC,2NAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;;wEAK1B,oBAAoB,QAAQ,EAAE,IAAI,cAAc,QAAQ,OAAO,kBAC9D,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,8MAAA,CAAA,aAAU;4FAAC,WAAU;;;;;;sGACtB,8OAAC;sGAAK;;;;;;;;;;;;gFAEP,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;wFAEC,SAAS,IAAM,gBAAgB,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;wFAC3D,WAAU;;4FAET,mBAAmB,MAAM,IAAI;0GAC9B,8OAAC;gGAAK,WAAU;;oGACb,QAAQ;oGAAE;oGAAG,kBAAkB;;;;;;;;uFAN7B,MAAM,EAAE,IAAI;;;;;;;;;;;;mEAzBjB,QAAQ,EAAE;;;;;4DAuCxB;;;;;;;;+CA7FI,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAuG3B,8OAAC;wBAAI,WAAU;kCACZ,CAAC,gCACA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;sEAAK;;;;;;wDACL,KAAK,SAAS,kBACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAY;;;;;;;;;;;;8DAG/B,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;sDACT,KAAK,SAAS,iBACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,KAAK,SAAS,CAAC,SAAS,CAAC,MAAM;;;;;;kFAElC,8OAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;0EAEjD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EACZ,KAAK,SAAS,CAAC,YAAY;4EAAC;;;;;;;kFAE/B,8OAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;0EAEjD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,KAAK,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;;;;;;kFAE/D,8OAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;4DAEhD,KAAK,SAAS,CAAC,SAAS,kBACvB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,KAAK,SAAS,CAAC,SAAS;;;;;;kFAE3B,8OAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;;;;;;;kEAIrD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,IAAM,SAAS,SAAS,KAAK,SAAS;4DAC/C,WAAU;;8EAEV,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;qEAMvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAS,IAAM,WAAW;;0EAChC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAS3C,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA,uBAChB,8OAAC;wDAAoB,WAAU;;0EAC7B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAe,OAAO,IAAI;;;;;;oEACvC,OAAO,UAAU,kBAChB,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;;0FACb,8OAAC,kNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;0EAK7C,8OAAC;gEAAI,WAAU;;oEACZ,OAAO,QAAQ,CAAC,MAAM;oEAAC;;;;;;;0EAE1B,8OAAC;gEAAI,WAAU;0EACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA;oEACnB,MAAM,aAAa,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG;oEAC/D,qBACE,8OAAC;wEAAqB,WAAU;;0FAC9B,8OAAC;0FAAM,QAAQ,IAAI;;;;;;4EAClB,2BACC,8OAAC,2NAAA,CAAA,cAAW;gFAAC,WAAU;;;;;qGAEvB,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;;uEALX,QAAQ,EAAE;;;;;gEASxB;;;;;;;uDA1BM,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;iDAmC7B,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC;8EAAM,gBAAgB;;;;;;gEACtB,gBAAgB,gCACf,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;;sFACb,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAI1C,eAAe,4BACd,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;;sFACb,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;sEAK7C,8OAAC,gIAAA,CAAA,kBAAe;;gEAAC;gEACP,eAAe;gEACtB,eAAe,4BACd,8OAAC;oEAAK,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;8DAMlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,eAAe,CAAC;;8EAE/B,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEACd,cAAc,SAAS;;;;;;;wDAEzB,eAAe,4BACd,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,SAAS,UAAU,cAAc,UAAU;;8EAE1D,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAI1C,gBAAgB,gCACf,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;gEACP,IAAI,eAAe,WAAW,EAAE;oEAC9B,SAAS,WAAW,eAAe,WAAW;gEAChD,OAAO;oEACL,WAAW;gEACb;4DACF;;8EAEA,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEACrB,eAAe,WAAW,GAAG,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAShE,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAY;;;;;;8DACjC,8OAAC,gIAAA,CAAA,kBAAe;8DACb,cACG,mDACA;;;;;;;;;;;;sDAGR,8OAAC,gIAAA,CAAA,cAAW;sDACT,4BACC,8OAAC;gDAAI,WAAU;0DACZ,gBAAgB,WAAW,eAAe,OAAO,CAAC,MAAM,GAAG,kBAC1D;8DACG,eAAe,OAAO,CAAC,GAAG,CAAC,CAAC,OAAY,sBACvC,8OAAC;4DAEC,KAAK,CAAC;gEACJ,YAAY,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG;4DACtD;4DACA,WAAU;4DACV,IAAI,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;;8EAGhC,8OAAC;oEAAI,WAAU;;wEACZ,mBAAmB,MAAM,IAAI;sFAC9B,8OAAC;;gFAAK;gFAAS,QAAQ;;;;;;;sFACvB,8OAAC;;gFAAK;gFAAE,MAAM,IAAI;gFAAC;;;;;;;;;;;;;gEAGpB,MAAM,IAAI,KAAK,uBACd,8OAAC,wLAAA,CAAA,UAAa;oEACZ,eAAe;wEAAC,6IAAA,CAAA,UAAS;qEAAC;oEAC1B,YAAY;wEACV,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,8OAAC;gFAAG,WAAU;gFAAyC,GAAG,KAAK;;;;;;wEAEjE,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,8OAAC;gFAAG,WAAU;gFAA4C,GAAG,KAAK;;;;;;wEAEpE,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,8OAAC;gFAAG,WAAU;gFAA4C,GAAG,KAAK;;;;;;wEAEpE,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,8OAAC;gFAAG,WAAU;gFAA8C,GAAG,KAAK;;;;;;wEAEtE,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACpB,8OAAC;gFAAE,WAAU;gFAAwB,GAAG,KAAK;;;;;;wEAE/C,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,8OAAC;gFAAG,WAAU;gFAAuB,GAAG,KAAK;;;;;;wEAE/C,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,8OAAC;gFAAG,WAAU;gFAA0B,GAAG,KAAK;;;;;;wEAElD,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,8OAAC;gFAAG,WAAU;gFAAQ,GAAG,KAAK;;;;;;wEAEhC,YAAY,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBAC7B,8OAAC;gFACC,WAAU;gFACT,GAAG,KAAK;;;;;;wEAGb,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;4EAC5C,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;4EACjD,MAAM,WAAW,CAAC;4EAClB,OAAO,yBACL,8OAAC;gFACC,WAAU;gFACT,GAAG,KAAK;0FAER;;;;;uGAGH,8OAAC;gFACC,WAAU;gFACT,GAAG,KAAK;0FAER;;;;;;wEAGP;wEACA,KAAK,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACtB,8OAAC;gFAAI,WAAU;gFAAQ,GAAG,KAAK;;;;;;wEAEjC,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACxB,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFACC,WAAU;oFACT,GAAG,KAAK;;;;;;;;;;;wEAIf,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACxB,8OAAC;gFAAM,WAAU;gFAAc,GAAG,KAAK;;;;;;wEAEzC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,8OAAC;gFACC,WAAU;gFACT,GAAG,KAAK;;;;;;wEAGb,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,8OAAC;gFACC,WAAU;gFACT,GAAG,KAAK;;;;;;wEAGb,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,8OAAC;gFAAG,WAAU;gFAAwB,GAAG,KAAK;;;;;;wEAEhD,QAAQ,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACzB,8OAAC;gFACC,WAAU;gFACT,GAAG,KAAK;;;;;;wEAGb,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,8OAAC;gFAAG,WAAU;gFAAU,GAAG,KAAK;;;;;;oEAEpC;8EAEC,MAAM,KAAK;;;;;2EAEZ,MAAM,IAAI,KAAK,wBACjB,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,KAAK,MAAM,KAAK;wEAChB,KAAI;wEACJ,WAAU;;;;;;;;;;2EAGZ,MAAM,IAAI,KAAK,wBACjB,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,KAAK,MAAM,KAAK;wEAChB,QAAQ;wEACR,WAAU;;;;;;;;;;2EAGZ,MAAM,IAAI,KAAK,sBACjB,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,KAAK,MAAM,KAAK;wEAChB,WAAU;wEACV,OAAM;;;;;;;;;;yFAIV,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;;4EACV,MAAM,IAAI,KAAK,mBACZ,qBACA;0FACJ,8OAAC;gFACC,MAAM,MAAM,KAAK;gFACjB,QAAO;gFACP,KAAI;gFACJ,WAAU;0FAET,MAAM,KAAK;;;;;;;;;;;;;;;;;;2DAjJf,MAAM,EAAE,IAAI;;;;;kFA0JvB,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;qEAMhD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kJAAA,CAAA,uBAAoB;4DACnB,gBAAgB,gBAAgB,WAAW,EAAE;4DAC7C,iBAAiB;4DACjB,aAAa;;;;;;;;;;;kEAGjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEACE,gBAAgB,SAAS,UAAU;oEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAa1D,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CACT,YAAY,IAAI,EAAE,UAAU,SAAS,cAAc;;;;;;8CAEtD,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACZ,OAAO,YAAY,IAAI,EAAE,QAAQ;oDACjC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEACvC,GAAG,IAAI;gEACP,MAAM,KAAK,IAAI,GAAG;oEAAE,GAAG,KAAK,IAAI;oEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gEAAC,IAAI;4DAC7D,CAAC;;;;;;;;;;;;sDAIL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,YAAY,IAAI,EAAE,gBAAgB;oDACzC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEACvC,GAAG,IAAI;gEACP,MAAM,KAAK,IAAI,GAAG;oEAAE,GAAG,KAAK,IAAI;oEAAE,cAAc,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAE,IAAI;4DAC/E,CAAC;;;;;;;;;;;;sDAIL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACJ,OAAO,YAAY,IAAI,EAAE,aAAa;oDACtC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEACvC,GAAG,IAAI;gEACP,MAAM,KAAK,IAAI,GAAG;oEAAE,GAAG,KAAK,IAAI;oEAAE,WAAW,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;gEAAU,IAAI;4DACzG,CAAC;oDACD,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAkB;;;;;;sDACjC,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO,YAAY,IAAI,EAAE,eAAe;4CACxC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wDACvC,GAAG,IAAI;wDACP,MAAM,KAAK,IAAI,GAAG;4DAAE,GAAG,KAAK,IAAI;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC,IAAI;oDACpE,CAAC;4CACD,MAAM;;;;;;;;;;;;8CAKV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwB;;;;;;8DACtC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAS;;sEACf,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;wCAKpC,YAAY,IAAI,EAAE,UAAU,WAAW,kBACtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;iEAGzB,8OAAC;4CAAI,WAAU;sDACZ,YAAY,IAAI,EAAE,UAAU,IAAI,CAAC,UAAU,sBAC1C,8OAAC,gIAAA,CAAA,OAAI;8DACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;8FAAW,QAAQ;;;;;;8FAClC,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;8FACZ,SAAS,IAAI,KAAK,oBAAoB,kBACtC,SAAS,IAAI,KAAK,eAAe,gBAAgB;;;;;;8FAEpD,8OAAC;oFAAK,WAAU;;wFACb,SAAS,MAAM;wFAAC;;;;;;;;;;;;;sFAGrB,8OAAC;4EAAI,WAAU;sFACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC7B,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;wFACZ,MAAM,IAAI,KAAK,wBAAU,8OAAC;sGAAG,MAAM,KAAK;;;;;;wFACxC,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,kBACpC,8OAAC;4FAAI,KAAK,MAAM,KAAK;4FAAE,KAAK,CAAC,eAAe,EAAE,YAAY;4FAAE,WAAU;;;;;;;mFAHrD;;;;;;;;;;wEAQxB,SAAS,IAAI,KAAK,qBAAqB,SAAS,OAAO,kBACtD,8OAAC;4EAAI,WAAU;sFACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,yBAC7B,8OAAC;oFAAmB,WAAU;;wFAC3B,OAAO,YAAY,CAAC,KAAK;wFAAU;wFACnC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,iCAC1B,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oGACZ,MAAM,IAAI,KAAK,wBAAU,8OAAC;kHAAM,MAAM,KAAK;;;;;;oGAC3C,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,kBACpC,8OAAC;wGAAI,KAAK,MAAM,KAAK;wGAAE,KAAK,CAAC,aAAa,EAAE,kBAAkB;wGAAE,WAAU;;;;;;;+FAHzD;;;;;;mFAHf;;;;;;;;;;;;;;;;8EAelB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,aAAa;sFAE5B,cAAA,8OAAC,2MAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;sFAElB,8OAAC,2IAAA,CAAA,cAAW;;8FACV,8OAAC,2IAAA,CAAA,qBAAkB;oFAAC,OAAO;8FACzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wFAAC,SAAQ;wFAAQ,MAAK;kGAC3B,cAAA,8OAAC,0MAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;;;;;;;;;;;8FAGtB,8OAAC,2IAAA,CAAA,qBAAkB;;sGACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8GAChB,8OAAC,2IAAA,CAAA,mBAAgB;8GAAC;;;;;;8GAClB,8OAAC,2IAAA,CAAA,yBAAsB;8GAAC;;;;;;;;;;;;sGAI1B,8OAAC,2IAAA,CAAA,oBAAiB;;8GAChB,8OAAC,2IAAA,CAAA,oBAAiB;8GAAC;;;;;;8GACnB,8OAAC,2IAAA,CAAA,oBAAiB;oGAAC,SAAS,IAAM,eAAe,SAAS,EAAE;8GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDAjElE,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAiFhC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,oBAAoB;8CAAQ;;;;;;8CAGrE,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;;sDACf,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAsB,cAAc;0BAChD,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CACT,iBAAiB,WAAW,oBAAoB;;;;;;;;;;;sCAIrD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO,iBAAiB,QAAQ;oDAChC,eAAe,CAAC;wDACd,mBAAmB,CAAA;4DACjB,IAAI,CAAC,MAAM,OAAO;4DAClB,MAAM,cAAc;gEAAE,GAAG,IAAI;gEAAE,MAAM;4DAAM;4DAC3C,IAAI,UAAU,cAAc;gEAC1B,YAAY,OAAO,GAAG;oEACpB;wEAAE,SAAS;4EAAC;gFAAE,MAAM;gFAAQ,OAAO;4EAAO;yEAAE;wEAAE,WAAW;oEAAM;oEAC/D;wEAAE,SAAS;4EAAC;gFAAE,MAAM;gFAAQ,OAAO;4EAAQ;yEAAE;wEAAE,WAAW;oEAAM;iEACjE;4DACH,OAAO,IAAI,UAAU,mBAAmB;gEACtC,YAAY,OAAO,GAAG;oEACpB;wEAAE,SAAS;4EAAC;gFAAE,MAAM;gFAAQ,OAAO;4EAAG;yEAAE;wEAAE,WAAW;oEAAM;oEAC3D;wEAAE,SAAS;4EAAC;gFAAE,MAAM;gFAAQ,OAAO;4EAAG;yEAAE;wEAAE,WAAW;oEAAM;oEAC3D;wEAAE,SAAS;4EAAC;gFAAE,MAAM;gFAAQ,OAAO;4EAAG;yEAAE;wEAAE,WAAW;oEAAM;oEAC3D;wEAAE,SAAS;4EAAC;gFAAE,MAAM;gFAAQ,OAAO;4EAAG;yEAAE;wEAAE,WAAW;oEAAM;iEAC5D;4DACH,OAAO;gEACL,YAAY,OAAO,GAAG,WAAW,0BAA0B;4DAC7D;4DACA,OAAO;wDACT;oDACF;;sEAEA,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAkB;;;;;;8EACpC,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAa;;;;;;8EAC/B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAKhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACJ,OAAO,iBAAiB,UAAU;oDAClC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAClC,OAAO;gEAAE,GAAG,IAAI;gEAAE,QAAQ,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAE,IAAI;;;;;;;;;;;;;;;;;;8CAM/D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAe;;;;;;sDAC9B,8OAAC,kJAAA,CAAA,uBAAoB;4CACnB,gBAAgB,iBAAiB,YAAY,EAAE;4CAC/C,iBAAiB,CAAC,UAAY,mBAAmB,CAAA,OAC/C,OAAO;wDAAE,GAAG,IAAI;wDAAE,UAAU;oDAAQ,IAAI;4CAE1C,aAAa;;;;;;;;;;;;gCAIhB,CAAC,iBAAiB,SAAS,qBAAqB,iBAAiB,SAAS,YAAY,mBACrF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;wCACN,gBAAgB,OAAO,EAAE,IAAI,CAAC,QAAQ,sBACrC,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;4DACZ,gBAAgB,IAAI,KAAK,mCACxB,8OAAC;gEAAK,WAAU;;oEACb,OAAO,YAAY,CAAC,KAAK;oEAAO;;;;;;;4DAGpC,gBAAgB,IAAI,KAAK,kCACxB,8OAAC,kJAAA,CAAA,uBAAoB;gEACnB,gBAAgB,OAAO,OAAO,IAAI,EAAE;gEACpC,iBAAiB,CAAC;oEAChB,MAAM,aAAa;2EAAK,gBAAgB,OAAO,IAAI,EAAE;qEAAE;oEACvD,UAAU,CAAC,MAAM,GAAG;wEAAE,GAAG,UAAU,CAAC,MAAM;wEAAE,SAAS;oEAAQ;oEAC7D,mBAAmB,CAAA,OACjB,OAAO;4EAAE,GAAG,IAAI;4EAAE,SAAS;wEAAW,IAAI;gEAE9C;gEACA,aAAa;;;;;qFAGf,8OAAC;gEAAK,WAAU;0EAAyB,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;;;;;;kEAGpE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAI,CAAC,eAAe,EAAE,OAAO;gEAC7B,SAAS,OAAO,SAAS;gEACzB,iBAAiB,CAAC;oEAChB,MAAM,aAAa;2EAAK,gBAAgB,OAAO,IAAI,EAAE;qEAAE;oEACvD,UAAU,CAAC,MAAM,GAAG;wEAAE,GAAG,UAAU,CAAC,MAAM;wEAAE,WAAW;oEAAmB;oEAC1E,mBAAmB,CAAA,OACjB,OAAO;4EAAE,GAAG,IAAI;4EAAE,SAAS;wEAAW,IAAI;gEAE9C;;;;;;0EAEF,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAS,CAAC,eAAe,EAAE,OAAO;0EAAE;;;;;;;;;;;;;+CAnCrC;;;;;;;;;;;gCA2Cf,mBAAmB,gBAAgB,IAAI,KAAK,yBAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAe;;;;;;sDAC9B,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO,gBAAgB,WAAW,IAAI;4CACtC,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAClC,OAAO;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC,IAAI;4CAEpD,MAAM;;;;;;;;;;;;gCAKX,iCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,8OAAC,kJAAA,CAAA,uBAAoB;4CACnB,gBAAgB,iBAAiB,eAAe,EAAE;4CAClD,iBAAiB,CAAC;gDAChB,mBAAmB,CAAA,OACjB,OAAO;wDAAE,GAAG,IAAI;wDAAE,aAAa;oDAAQ,IAAI;4CAE/C;4CACA,aAAY;4CACZ,aAAa;;;;;;;;;;;;;;;;;;sCAMrB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,wBAAwB;8CAAQ;;;;;;8CAGzE,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;;wCACd,iBAAiB,WAAW,aAAa;wCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjE", "debugId": null}}, {"offset": {"line": 7212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst alertVariants = cva(\r\n  'relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-card text-card-foreground',\r\n        destructive:\r\n          'text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<'div'> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot='alert'\r\n      role='alert'\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='alert-title'\r\n      className={cn(\r\n        'col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='alert-description'\r\n      className={cn(\r\n        'text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription };\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 7277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/course/steps/publishing-step.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { \r\n  CheckCircle, \r\n  AlertCircle, \r\n  BookOpen, \r\n  Users, \r\n  HelpCircle,\r\n  Calendar,\r\n  Code,\r\n  Image,\r\n  FileText,\r\n  Clock,\r\n  Target,\r\n  Rocket,\r\n  Eye,\r\n  Share2\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { CourseData } from '../course-creation-wizard';\r\nimport { toast } from 'sonner';\r\n\r\ninterface PublishingStepProps {\r\n  data: CourseData;\r\n  onPublish: () => Promise<void>;\r\n  isPublishing: boolean;\r\n}\r\n\r\ninterface ValidationItem {\r\n  id: string;\r\n  label: string;\r\n  status: 'complete' | 'incomplete' | 'warning';\r\n  description: string;\r\n  required: boolean;\r\n}\r\n\r\nexport function PublishingStep({ data, onPublish, isPublishing }: PublishingStepProps) {\r\n  const [showDetails, setShowDetails] = useState(false);\r\n\r\n  const getValidationItems = (): ValidationItem[] => {\r\n    const items: ValidationItem[] = [];\r\n\r\n    // Basic course info validation\r\n    items.push({\r\n      id: 'course-name',\r\n      label: 'Nama Course',\r\n      status: data.name.trim() ? 'complete' : 'incomplete',\r\n      description: data.name.trim() ? `\"${data.name}\"` : 'Nama course harus diisi',\r\n      required: true\r\n    });\r\n\r\n    items.push({\r\n      id: 'course-description',\r\n      label: 'Deskripsi Course',\r\n      status: data.description.trim() ? 'complete' : 'incomplete',\r\n      description: data.description.trim() \r\n        ? `${data.description.length} karakter` \r\n        : 'Deskripsi course harus diisi',\r\n      required: true\r\n    });\r\n\r\n    items.push({\r\n      id: 'course-code',\r\n      label: 'Kode Course',\r\n      status: data.courseCode.trim() ? 'complete' : 'incomplete',\r\n      description: data.courseCode.trim() ? data.courseCode : 'Kode course harus diisi',\r\n      required: true\r\n    });\r\n\r\n    items.push({\r\n      id: 'cover-image',\r\n      label: 'Cover Image',\r\n      status: data.coverImage ? 'complete' : 'warning',\r\n      description: data.coverImage ? 'Cover image telah diupload' : 'Disarankan menambahkan cover image',\r\n      required: false\r\n    });\r\n\r\n    items.push({\r\n      id: 'course-dates',\r\n      label: 'Tanggal Course',\r\n      status: data.startDate && data.endDate ? 'complete' : 'warning',\r\n      description: data.startDate && data.endDate \r\n        ? `${new Date(data.startDate).toLocaleDateString()} - ${new Date(data.endDate).toLocaleDateString()}`\r\n        : 'Tanggal mulai dan selesai belum diatur',\r\n      required: false\r\n    });\r\n\r\n    // Module structure validation\r\n    const moduleCount = data.modules.length;\r\n    items.push({\r\n      id: 'modules',\r\n      label: 'Struktur Modul',\r\n      status: moduleCount > 0 ? 'complete' : 'incomplete',\r\n      description: moduleCount > 0 \r\n        ? `${moduleCount} modul telah dibuat` \r\n        : 'Minimal 1 modul harus dibuat',\r\n      required: true\r\n    });\r\n\r\n    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);\r\n    items.push({\r\n      id: 'chapters',\r\n      label: 'Chapter',\r\n      status: totalChapters > 0 ? 'complete' : 'incomplete',\r\n      description: totalChapters > 0 \r\n        ? `${totalChapters} chapter telah dibuat` \r\n        : 'Minimal 1 chapter harus dibuat',\r\n      required: true\r\n    });\r\n\r\n    // Content validation\r\n    const chaptersWithContent = data.modules.reduce((acc, module) => \r\n      acc + module.chapters.filter(chapter => chapter.content && chapter.content.length > 0).length, 0\r\n    );\r\n    \r\n    items.push({\r\n      id: 'content',\r\n      label: 'Konten Chapter',\r\n      status: chaptersWithContent === totalChapters ? 'complete' : \r\n               chaptersWithContent > 0 ? 'warning' : 'incomplete',\r\n      description: `${chaptersWithContent} dari ${totalChapters} chapter memiliki konten`,\r\n      required: true\r\n    });\r\n\r\n    // Quiz validation\r\n    const chaptersWithQuiz = data.modules.reduce((acc, module) => \r\n      acc + module.chapters.filter(chapter => chapter.hasChapterQuiz && chapter.chapterQuiz).length, 0\r\n    );\r\n    \r\n    const modulesWithQuiz = data.modules.filter(module => module.hasModuleQuiz && module.moduleQuiz).length;\r\n    \r\n    items.push({\r\n      id: 'quizzes',\r\n      label: 'Quiz',\r\n      status: (chaptersWithQuiz > 0 || modulesWithQuiz > 0) ? 'complete' : 'warning',\r\n      description: `${chaptersWithQuiz} chapter quiz, ${modulesWithQuiz} module quiz`,\r\n      required: false\r\n    });\r\n\r\n    // Final exam validation\r\n    items.push({\r\n      id: 'final-exam',\r\n      label: 'Final Exam',\r\n      status: data.finalExam ? 'complete' : 'warning',\r\n      description: data.finalExam \r\n        ? `${data.finalExam.questions.length} pertanyaan` \r\n        : 'Final exam belum dibuat',\r\n      required: false\r\n    });\r\n\r\n    return items;\r\n  };\r\n\r\n  const validationItems = getValidationItems();\r\n  const requiredItems = validationItems.filter(item => item.required);\r\n  const completedRequired = requiredItems.filter(item => item.status === 'complete').length;\r\n  const canPublish = completedRequired === requiredItems.length;\r\n  \r\n  const allCompleted = validationItems.filter(item => item.status === 'complete').length;\r\n  const completionPercentage = Math.round((allCompleted / validationItems.length) * 100);\r\n\r\n  const getCourseStats = () => {\r\n    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);\r\n    const totalQuizzes = data.modules.reduce((acc, module) => {\r\n      const chapterQuizzes = module.chapters.filter(c => c.hasChapterQuiz).length;\r\n      const moduleQuiz = module.hasModuleQuiz ? 1 : 0;\r\n      return acc + chapterQuizzes + moduleQuiz;\r\n    }, 0) + (data.finalExam ? 1 : 0);\r\n    \r\n    const estimatedDuration = data.modules.reduce((acc, module) => \r\n      acc + module.chapters.reduce((chapterAcc, chapter) =>\r\n        chapterAcc + Math.ceil((chapter.content as any[]).filter(block => block.type === 'text').reduce((textAcc, block) => textAcc + block.value.length, 0) / 1000) * 5, 0\r\n      ), 0\r\n    );\r\n\r\n    return {\r\n      modules: data.modules.length,\r\n      chapters: totalChapters,\r\n      quizzes: totalQuizzes,\r\n      estimatedDuration: Math.max(estimatedDuration, 30) // minimum 30 minutes\r\n    };\r\n  };\r\n\r\n  const stats = getCourseStats();\r\n\r\n  const handlePublish = async () => {\r\n    if (!canPublish) {\r\n      toast.error('Lengkapi semua item yang wajib diisi terlebih dahulu');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await onPublish();\r\n      toast.success('Course berhasil dipublikasi!');\r\n    } catch (error) {\r\n      toast.error('Gagal mempublikasi course');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"text-center space-y-2\">\r\n        <div className={cn(\r\n          \"w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\r\n          canPublish ? \"bg-green-100 text-green-600\" : \"bg-orange-100 text-orange-600\"\r\n        )}>\r\n          {canPublish ? (\r\n            <Rocket className=\"w-8 h-8\" />\r\n          ) : (\r\n            <AlertCircle className=\"w-8 h-8\" />\r\n          )}\r\n        </div>\r\n        <h3 className=\"text-2xl font-bold\">\r\n          {canPublish ? 'Siap untuk Dipublikasi!' : 'Hampir Selesai'}\r\n        </h3>\r\n        <p className=\"text-muted-foreground\">\r\n          {canPublish \r\n            ? 'Course Anda sudah siap untuk dipublikasi dan dapat diakses oleh siswa'\r\n            : 'Lengkapi beberapa item berikut untuk mempublikasi course'\r\n          }\r\n        </p>\r\n      </div>\r\n\r\n      {/* Progress Overview */}\r\n      <Card>\r\n        <CardHeader>\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <CardTitle className=\"flex items-center space-x-2\">\r\n                <Target className=\"w-5 h-5\" />\r\n                <span>Progress Kelengkapan</span>\r\n              </CardTitle>\r\n              <CardDescription>\r\n                {allCompleted} dari {validationItems.length} item selesai\r\n              </CardDescription>\r\n            </div>\r\n            <div className=\"text-right\">\r\n              <div className=\"text-2xl font-bold\">{completionPercentage}%</div>\r\n              <div className=\"text-sm text-muted-foreground\">Selesai</div>\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <Progress value={completionPercentage} className=\"mb-4\" />\r\n          \r\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full mx-auto mb-2\">\r\n                <BookOpen className=\"w-5 h-5\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.modules}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Modul</div>\r\n            </div>\r\n            \r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-green-100 text-green-600 rounded-full mx-auto mb-2\">\r\n                <FileText className=\"w-5 h-5\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.chapters}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Chapter</div>\r\n            </div>\r\n            \r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-purple-100 text-purple-600 rounded-full mx-auto mb-2\">\r\n                <HelpCircle className=\"w-5 h-5\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.quizzes}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Quiz</div>\r\n            </div>\r\n            \r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-orange-100 text-orange-600 rounded-full mx-auto mb-2\">\r\n                <Clock className=\"w-5 h-5\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.estimatedDuration}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Menit</div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Validation Checklist */}\r\n      <Card>\r\n        <CardHeader>\r\n          <div className=\"flex items-center justify-between\">\r\n            <CardTitle className=\"flex items-center space-x-2\">\r\n              <CheckCircle className=\"w-5 h-5\" />\r\n              <span>Checklist Publikasi</span>\r\n            </CardTitle>\r\n            <Button \r\n              variant=\"ghost\" \r\n              size=\"sm\"\r\n              onClick={() => setShowDetails(!showDetails)}\r\n            >\r\n              {showDetails ? 'Sembunyikan' : 'Lihat'} Detail\r\n            </Button>\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-3\">\r\n            {validationItems.map((item) => (\r\n              <div key={item.id} className=\"flex items-start space-x-3\">\r\n                <div className={cn(\r\n                  \"w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\",\r\n                  item.status === 'complete' ? \"bg-green-100 text-green-600\" :\r\n                  item.status === 'warning' ? \"bg-orange-100 text-orange-600\" :\r\n                  \"bg-gray-100 text-gray-400\"\r\n                )}>\r\n                  {item.status === 'complete' ? (\r\n                    <CheckCircle className=\"w-3 h-3\" />\r\n                  ) : item.status === 'warning' ? (\r\n                    <AlertCircle className=\"w-3 h-3\" />\r\n                  ) : (\r\n                    <div className=\"w-2 h-2 bg-current rounded-full\" />\r\n                  )}\r\n                </div>\r\n                \r\n                <div className=\"flex-1 min-w-0\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <span className={cn(\r\n                      \"text-sm font-medium\",\r\n                      item.status === 'complete' ? \"text-green-700\" :\r\n                      item.status === 'warning' ? \"text-orange-700\" :\r\n                      \"text-gray-500\"\r\n                    )}>\r\n                      {item.label}\r\n                    </span>\r\n                    {item.required && (\r\n                      <Badge variant=\"destructive\" className=\"text-xs px-1 py-0\">\r\n                        Wajib\r\n                      </Badge>\r\n                    )}\r\n                  </div>\r\n                  \r\n                  {showDetails && (\r\n                    <p className=\"text-xs text-muted-foreground mt-1\">\r\n                      {item.description}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Course Preview */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center space-x-2\">\r\n            <Eye className=\"w-5 h-5\" />\r\n            <span>Preview Course</span>\r\n          </CardTitle>\r\n          <CardDescription>\r\n            Begini tampilan course Anda untuk siswa\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"border rounded-lg p-4 space-y-4\">\r\n            {/* Course Header */}\r\n            <div className=\"flex items-start space-x-4\">\r\n              {data.coverImage ? (\r\n                <img \r\n                  src={typeof data.coverImage === 'string' \r\n                  ? data.coverImage \r\n                  : URL.createObjectURL(data.coverImage)}\r\n                  alt={data.name}\r\n                  className=\"w-20 h-20 object-cover rounded-lg\"\r\n                />\r\n              ) : (\r\n                <div className=\"w-20 h-20 bg-muted rounded-lg flex items-center justify-center\">\r\n                  <Image className=\"w-8 h-8 text-muted-foreground\" />\r\n                </div>\r\n              )}\r\n              \r\n              <div className=\"flex-1\">\r\n                <h4 className=\"font-semibold text-lg\">{data.name || 'Nama Course'}</h4>\r\n                <p className=\"text-sm text-muted-foreground mb-2\">\r\n                  {data.description || 'Deskripsi course'}\r\n                </p>\r\n                \r\n                <div className=\"flex items-center space-x-4 text-xs text-muted-foreground\">\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Code className=\"w-3 h-3\" />\r\n                    <span>{data.courseCode || 'COURSE-CODE'}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <BookOpen className=\"w-3 h-3\" />\r\n                    <span>{stats.modules} Modul</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Clock className=\"w-3 h-3\" />\r\n                    <span>~{stats.estimatedDuration} Menit</span>\r\n                  </div>\r\n                  {data.startDate && (\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <Calendar className=\"w-3 h-3\" />\r\n                      <span>{new Date(data.startDate).toLocaleDateString()}</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <Separator />\r\n            \r\n            {/* Module Structure Preview */}\r\n            <div className=\"space-y-2\">\r\n              <h5 className=\"font-medium text-sm\">Struktur Course:</h5>\r\n              {data.modules.length > 0 ? (\r\n                <div className=\"space-y-2\">\r\n                  {data.modules.slice(0, 3).map((module, index) => (\r\n                    <div key={module.id} className=\"text-sm\">\r\n                      <div className=\"font-medium\">\r\n                        {index + 1}. {module.name}\r\n                      </div>\r\n                      <div className=\"ml-4 text-xs text-muted-foreground\">\r\n                        {module.chapters.length} chapter\r\n                        {module.hasModuleQuiz && ' • Quiz modul'}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                  {data.modules.length > 3 && (\r\n                    <div className=\"text-xs text-muted-foreground\">\r\n                      ... dan {data.modules.length - 3} modul lainnya\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <p className=\"text-sm text-muted-foreground italic\">\r\n                  Belum ada modul\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Warnings */}\r\n      {!canPublish && (\r\n        <Alert>\r\n          <AlertCircle className=\"h-4 w-4\" />\r\n          <AlertDescription>\r\n            <strong>Perhatian:</strong> Beberapa item wajib belum lengkap. \r\n            Course tidak dapat dipublikasi sampai semua item wajib diselesaikan.\r\n          </AlertDescription>\r\n        </Alert>\r\n      )}\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex items-center justify-between pt-6\">\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          {canPublish \r\n            ? 'Course siap dipublikasi dan dapat diakses siswa'\r\n            : `${completedRequired}/${requiredItems.length} item wajib selesai`\r\n          }\r\n        </div>\r\n        \r\n        <div className=\"flex items-center space-x-3\">\r\n          <Button variant=\"outline\" disabled={isPublishing}>\r\n            <Eye className=\"w-4 h-4 mr-2\" />\r\n            Preview\r\n          </Button>\r\n          \r\n          <Button \r\n            onClick={handlePublish}\r\n            disabled={!canPublish || isPublishing}\r\n            className=\"min-w-[120px]\"\r\n          >\r\n            {isPublishing ? (\r\n              <>\r\n                <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\" />\r\n                Publishing...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Rocket className=\"w-4 h-4 mr-2\" />\r\n                Publikasi Course\r\n              </>\r\n            )}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAEA;AA3BA;;;;;;;;;;;;AA2CO,SAAS,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAuB;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB;QACzB,MAAM,QAA0B,EAAE;QAElC,+BAA+B;QAC/B,MAAM,IAAI,CAAC;YACT,IAAI;YACJ,OAAO;YACP,QAAQ,KAAK,IAAI,CAAC,IAAI,KAAK,aAAa;YACxC,aAAa,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG;YACnD,UAAU;QACZ;QAEA,MAAM,IAAI,CAAC;YACT,IAAI;YACJ,OAAO;YACP,QAAQ,KAAK,WAAW,CAAC,IAAI,KAAK,aAAa;YAC/C,aAAa,KAAK,WAAW,CAAC,IAAI,KAC9B,GAAG,KAAK,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,GACrC;YACJ,UAAU;QACZ;QAEA,MAAM,IAAI,CAAC;YACT,IAAI;YACJ,OAAO;YACP,QAAQ,KAAK,UAAU,CAAC,IAAI,KAAK,aAAa;YAC9C,aAAa,KAAK,UAAU,CAAC,IAAI,KAAK,KAAK,UAAU,GAAG;YACxD,UAAU;QACZ;QAEA,MAAM,IAAI,CAAC;YACT,IAAI;YACJ,OAAO;YACP,QAAQ,KAAK,UAAU,GAAG,aAAa;YACvC,aAAa,KAAK,UAAU,GAAG,+BAA+B;YAC9D,UAAU;QACZ;QAEA,MAAM,IAAI,CAAC;YACT,IAAI;YACJ,OAAO;YACP,QAAQ,KAAK,SAAS,IAAI,KAAK,OAAO,GAAG,aAAa;YACtD,aAAa,KAAK,SAAS,IAAI,KAAK,OAAO,GACvC,GAAG,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,GAAG,GAAG,EAAE,IAAI,KAAK,KAAK,OAAO,EAAE,kBAAkB,IAAI,GACnG;YACJ,UAAU;QACZ;QAEA,8BAA8B;QAC9B,MAAM,cAAc,KAAK,OAAO,CAAC,MAAM;QACvC,MAAM,IAAI,CAAC;YACT,IAAI;YACJ,OAAO;YACP,QAAQ,cAAc,IAAI,aAAa;YACvC,aAAa,cAAc,IACvB,GAAG,YAAY,mBAAmB,CAAC,GACnC;YACJ,UAAU;QACZ;QAEA,MAAM,gBAAgB,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,QAAQ,CAAC,MAAM,EAAE;QACzF,MAAM,IAAI,CAAC;YACT,IAAI;YACJ,OAAO;YACP,QAAQ,gBAAgB,IAAI,aAAa;YACzC,aAAa,gBAAgB,IACzB,GAAG,cAAc,qBAAqB,CAAC,GACvC;YACJ,UAAU;QACZ;QAEA,qBAAqB;QACrB,MAAM,sBAAsB,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SACpD,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG,MAAM,EAAE;QAGjG,MAAM,IAAI,CAAC;YACT,IAAI;YACJ,OAAO;YACP,QAAQ,wBAAwB,gBAAgB,aACvC,sBAAsB,IAAI,YAAY;YAC/C,aAAa,GAAG,oBAAoB,MAAM,EAAE,cAAc,wBAAwB,CAAC;YACnF,UAAU;QACZ;QAEA,kBAAkB;QAClB,MAAM,mBAAmB,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SACjD,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,cAAc,IAAI,QAAQ,WAAW,EAAE,MAAM,EAAE;QAGjG,MAAM,kBAAkB,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,aAAa,IAAI,OAAO,UAAU,EAAE,MAAM;QAEvG,MAAM,IAAI,CAAC;YACT,IAAI;YACJ,OAAO;YACP,QAAQ,AAAC,mBAAmB,KAAK,kBAAkB,IAAK,aAAa;YACrE,aAAa,GAAG,iBAAiB,eAAe,EAAE,gBAAgB,YAAY,CAAC;YAC/E,UAAU;QACZ;QAEA,wBAAwB;QACxB,MAAM,IAAI,CAAC;YACT,IAAI;YACJ,OAAO;YACP,QAAQ,KAAK,SAAS,GAAG,aAAa;YACtC,aAAa,KAAK,SAAS,GACvB,GAAG,KAAK,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,GAC/C;YACJ,UAAU;QACZ;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB;IACxB,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;IAClE,MAAM,oBAAoB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,YAAY,MAAM;IACzF,MAAM,aAAa,sBAAsB,cAAc,MAAM;IAE7D,MAAM,eAAe,gBAAgB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,YAAY,MAAM;IACtF,MAAM,uBAAuB,KAAK,KAAK,CAAC,AAAC,eAAe,gBAAgB,MAAM,GAAI;IAElF,MAAM,iBAAiB;QACrB,MAAM,gBAAgB,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,QAAQ,CAAC,MAAM,EAAE;QACzF,MAAM,eAAe,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK;YAC7C,MAAM,iBAAiB,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,EAAE,MAAM;YAC3E,MAAM,aAAa,OAAO,aAAa,GAAG,IAAI;YAC9C,OAAO,MAAM,iBAAiB;QAChC,GAAG,KAAK,CAAC,KAAK,SAAS,GAAG,IAAI,CAAC;QAE/B,MAAM,oBAAoB,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAClD,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,YAAY,UACxC,aAAa,KAAK,IAAI,CAAC,AAAC,QAAQ,OAAO,CAAW,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,QAAQ,MAAM,CAAC,CAAC,SAAS,QAAU,UAAU,MAAM,KAAK,CAAC,MAAM,EAAE,KAAK,QAAQ,GAAG,IACjK;QAGL,OAAO;YACL,SAAS,KAAK,OAAO,CAAC,MAAM;YAC5B,UAAU;YACV,SAAS;YACT,mBAAmB,KAAK,GAAG,CAAC,mBAAmB,IAAI,qBAAqB;QAC1E;IACF;IAEA,MAAM,QAAQ;IAEd,MAAM,gBAAgB;QACpB,IAAI,CAAC,YAAY;YACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM;YACN,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,wEACA,aAAa,gCAAgC;kCAE5C,2BACC,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;iDAElB,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAG3B,8OAAC;wBAAG,WAAU;kCACX,aAAa,4BAA4B;;;;;;kCAE5C,8OAAC;wBAAE,WAAU;kCACV,aACG,0EACA;;;;;;;;;;;;0BAMR,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,gIAAA,CAAA,kBAAe;;gDACb;gDAAa;gDAAO,gBAAgB,MAAM;gDAAC;;;;;;;;;;;;;8CAGhD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAAsB;gDAAqB;;;;;;;sDAC1D,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;kCAIrD,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,OAAO;gCAAsB,WAAU;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;0DAAuB,MAAM,OAAO;;;;;;0DACnD,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAGjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;0DAAuB,MAAM,QAAQ;;;;;;0DACpD,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAGjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC;gDAAI,WAAU;0DAAuB,MAAM,OAAO;;;;;;0DACnD,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAGjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;0DAAuB,MAAM,iBAAiB;;;;;;0DAC7D,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvD,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe,CAAC;;wCAE9B,cAAc,gBAAgB;wCAAQ;;;;;;;;;;;;;;;;;;kCAI7C,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,8EACA,KAAK,MAAM,KAAK,aAAa,gCAC7B,KAAK,MAAM,KAAK,YAAY,kCAC5B;sDAEC,KAAK,MAAM,KAAK,2BACf,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;uDACrB,KAAK,MAAM,KAAK,0BAClB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,8OAAC;gDAAI,WAAU;;;;;;;;;;;sDAInB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,uBACA,KAAK,MAAM,KAAK,aAAa,mBAC7B,KAAK,MAAM,KAAK,YAAY,oBAC5B;sEAEC,KAAK,KAAK;;;;;;wDAEZ,KAAK,QAAQ,kBACZ,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAc,WAAU;sEAAoB;;;;;;;;;;;;gDAM9D,6BACC,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;;;;;;;;mCAnCf,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BA8CzB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;wCACZ,KAAK,UAAU,iBACd,8OAAC;4CACC,KAAK,OAAO,KAAK,UAAU,KAAK,WAC9B,KAAK,UAAU,GACf,IAAI,eAAe,CAAC,KAAK,UAAU;4CACrC,KAAK,KAAK,IAAI;4CACd,WAAU;;;;;iEAGZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAIrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyB,KAAK,IAAI,IAAI;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW,IAAI;;;;;;8DAGvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAM,KAAK,UAAU,IAAI;;;;;;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;;wEAAM,MAAM,OAAO;wEAAC;;;;;;;;;;;;;sEAEvB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;;wEAAK;wEAAE,MAAM,iBAAiB;wEAAC;;;;;;;;;;;;;wDAEjC,KAAK,SAAS,kBACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;8EAAM,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO5D,8OAAC,qIAAA,CAAA,YAAS;;;;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsB;;;;;;wCACnC,KAAK,OAAO,CAAC,MAAM,GAAG,kBACrB,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACrC,8OAAC;wDAAoB,WAAU;;0EAC7B,8OAAC;gEAAI,WAAU;;oEACZ,QAAQ;oEAAE;oEAAG,OAAO,IAAI;;;;;;;0EAE3B,8OAAC;gEAAI,WAAU;;oEACZ,OAAO,QAAQ,CAAC,MAAM;oEAAC;oEACvB,OAAO,aAAa,IAAI;;;;;;;;uDANnB,OAAO,EAAE;;;;;gDAUpB,KAAK,OAAO,CAAC,MAAM,GAAG,mBACrB,8OAAC;oDAAI,WAAU;;wDAAgC;wDACpC,KAAK,OAAO,CAAC,MAAM,GAAG;wDAAE;;;;;;;;;;;;iEAKvC,8OAAC;4CAAE,WAAU;sDAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU7D,CAAC,4BACA,8OAAC,iIAAA,CAAA,QAAK;;kCACJ,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC,iIAAA,CAAA,mBAAgB;;0CACf,8OAAC;0CAAO;;;;;;4BAAmB;;;;;;;;;;;;;0BAOjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,aACG,oDACA,GAAG,kBAAkB,CAAC,EAAE,cAAc,MAAM,CAAC,mBAAmB,CAAC;;;;;;kCAIvE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,UAAU;;kDAClC,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIlC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,CAAC,cAAc;gCACzB,WAAU;0CAET,6BACC;;sDACE,8OAAC;4CAAI,WAAU;;;;;;wCAAsF;;iEAIvG;;sDACE,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 8335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sZACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 8388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/course/steps/admissions-step.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Calendar, ClipboardList, BookOpen, X } from 'lucide-react';\r\nimport { CourseData, AdmissionsData } from '../course-creation-wizard';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\ninterface AdmissionsStepProps {\r\n  data: CourseData;\r\n  onUpdate: (updates: Partial<CourseData>) => void;\r\n}\r\n\r\nexport function AdmissionsStep({ data, onUpdate }: AdmissionsStepProps) {\r\n  const admissions = data.admissions || { requirements: [], applicationDeadline: '', prerequisites: [] };\r\n  const [newRequirement, setNewRequirement] = useState('');\r\n  const [newPrerequisite, setNewPrerequisite] = useState('');\r\n\r\n  const handleUpdate = (field: keyof AdmissionsData, value: string | string[]) => {\r\n    onUpdate({\r\n      admissions: {\r\n        ...admissions,\r\n        [field]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  const addRequirement = () => {\r\n    if (newRequirement.trim() !== '' && !admissions.requirements.includes(newRequirement.trim())) {\r\n      handleUpdate('requirements', [...admissions.requirements, newRequirement.trim()]);\r\n      setNewRequirement('');\r\n    }\r\n  };\r\n\r\n  const removeRequirement = (index: number) => {\r\n    const updatedRequirements = admissions.requirements.filter((_, i) => i !== index);\r\n    handleUpdate('requirements', updatedRequirements);\r\n  };\r\n\r\n  const addPrerequisite = () => {\r\n    if (newPrerequisite.trim() !== '' && !admissions.prerequisites.includes(newPrerequisite.trim())) {\r\n      handleUpdate('prerequisites', [...admissions.prerequisites, newPrerequisite.trim()]);\r\n      setNewPrerequisite('');\r\n    }\r\n  };\r\n\r\n  const removePrerequisite = (index: number) => {\r\n    const updatedPrerequisites = admissions.prerequisites.filter((_, i) => i !== index);\r\n    handleUpdate('prerequisites', updatedPrerequisites);\r\n  };\r\n\r\n  return (\r\n    <Card className=\"w-full\">\r\n      <CardHeader>\r\n        <CardTitle>Informasi Pendaftaran</CardTitle>\r\n        <CardDescription>Detail terkait persyaratan pendaftaran dan prasyarat kursus.</CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <ClipboardList className=\"h-5 w-5 text-gray-500\" />\r\n            <Label htmlFor=\"newRequirement\">Persyaratan</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input\r\n              id=\"newRequirement\"\r\n              value={newRequirement}\r\n              onChange={(e) => setNewRequirement(e.target.value)}\r\n              placeholder=\"Tambahkan persyaratan baru\"\r\n              onKeyPress={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  e.preventDefault();\r\n                  addRequirement();\r\n                }\r\n              }}\r\n            />\r\n            <Button type=\"button\" onClick={addRequirement}>Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {admissions.requirements.map((req, index) => (\r\n              <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {req}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"ml-1 h-auto px-1 py-0.5\"\r\n                  onClick={() => removeRequirement(index)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Calendar className=\"h-5 w-5 text-gray-500\" />\r\n          <Label htmlFor=\"applicationDeadline\">Batas Waktu Pendaftaran</Label>\r\n        </div>\r\n        <Input\r\n          id=\"applicationDeadline\"\r\n          type=\"text\" // Could be a date picker in a real app\r\n          value={admissions.applicationDeadline}\r\n          onChange={(e) => handleUpdate('applicationDeadline', e.target.value)}\r\n          placeholder=\"Contoh: 2024-12-31\"\r\n        />\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <BookOpen className=\"h-5 w-5 text-gray-500\" />\r\n            <Label htmlFor=\"newPrerequisite\">Prasyarat</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input\r\n              id=\"newPrerequisite\"\r\n              value={newPrerequisite}\r\n              onChange={(e) => setNewPrerequisite(e.target.value)}\r\n              placeholder=\"Tambahkan prasyarat baru\"\r\n              onKeyPress={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  e.preventDefault();\r\n                  addPrerequisite();\r\n                }\r\n              }}\r\n            />\r\n            <Button type=\"button\" onClick={addPrerequisite}>Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {admissions.prerequisites.map((req, index) => (\r\n              <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {req}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"ml-1 h-auto px-1 py-0.5\"\r\n                  onClick={() => removePrerequisite(index)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;;;;;;;;;AAOO,SAAS,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAuB;IACpE,MAAM,aAAa,KAAK,UAAU,IAAI;QAAE,cAAc,EAAE;QAAE,qBAAqB;QAAI,eAAe,EAAE;IAAC;IACrG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,eAAe,CAAC,OAA6B;QACjD,SAAS;YACP,YAAY;gBACV,GAAG,UAAU;gBACb,CAAC,MAAM,EAAE;YACX;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,eAAe,IAAI,OAAO,MAAM,CAAC,WAAW,YAAY,CAAC,QAAQ,CAAC,eAAe,IAAI,KAAK;YAC5F,aAAa,gBAAgB;mBAAI,WAAW,YAAY;gBAAE,eAAe,IAAI;aAAG;YAChF,kBAAkB;QACpB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,sBAAsB,WAAW,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC3E,aAAa,gBAAgB;IAC/B;IAEA,MAAM,kBAAkB;QACtB,IAAI,gBAAgB,IAAI,OAAO,MAAM,CAAC,WAAW,aAAa,CAAC,QAAQ,CAAC,gBAAgB,IAAI,KAAK;YAC/F,aAAa,iBAAiB;mBAAI,WAAW,aAAa;gBAAE,gBAAgB,IAAI;aAAG;YACnF,mBAAmB;QACrB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,uBAAuB,WAAW,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC7E,aAAa,iBAAiB;IAChC;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAiB;;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,aAAY;wCACZ,YAAY,CAAC;4CACX,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB,EAAE,cAAc;gDAChB;4CACF;wCACF;;;;;;kDAEF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAgB;;;;;;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,sBACjC,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAY,WAAU;;4CAC9C;0DACD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;0DAEjC,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCATL;;;;;;;;;;;;;;;;kCAgBlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAsB;;;;;;;;;;;;kCAEvC,8OAAC,iIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK,OAAO,uCAAuC;;wBACnD,OAAO,WAAW,mBAAmB;wBACrC,UAAU,CAAC,IAAM,aAAa,uBAAuB,EAAE,MAAM,CAAC,KAAK;wBACnE,aAAY;;;;;;kCAGd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAkB;;;;;;;;;;;;0CAEnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,aAAY;wCACZ,YAAY,CAAC;4CACX,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB,EAAE,cAAc;gDAChB;4CACF;wCACF;;;;;;kDAEF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAiB;;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,aAAa,CAAC,GAAG,CAAC,CAAC,KAAK,sBAClC,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAY,WAAU;;4CAC9C;0DACD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,mBAAmB;0DAElC,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCATL;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB1B", "debugId": null}}, {"offset": {"line": 8741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/course/steps/academics-step.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Book, Hourglass, Award, X } from 'lucide-react';\r\nimport { CourseData, AcademicsData } from '../course-creation-wizard';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\ninterface AcademicsStepProps {\r\n  data: CourseData;\r\n  onUpdate: (updates: Partial<CourseData>) => void;\r\n}\r\n\r\nexport function AcademicsStep({ data, onUpdate }: AcademicsStepProps) {\r\n  const academics = data.academics || { credits: 0, workload: '', assessment: [] };\r\n  const [newAssessment, setNewAssessment] = useState('');\r\n\r\n  const handleUpdate = (field: keyof AcademicsData, value: string | number | string[]) => {\r\n    onUpdate({\r\n      academics: {\r\n        ...academics,\r\n        [field]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  const addAssessment = () => {\r\n    if (newAssessment.trim() !== '' && !academics.assessment.includes(newAssessment.trim())) {\r\n      handleUpdate('assessment', [...academics.assessment, newAssessment.trim()]);\r\n      setNewAssessment('');\r\n    }\r\n  };\r\n\r\n  const removeAssessment = (index: number) => {\r\n    const updatedAssessment = academics.assessment.filter((_, i) => i !== index);\r\n    handleUpdate('assessment', updatedAssessment);\r\n  };\r\n\r\n  return (\r\n    <Card className=\"w-full\">\r\n      <CardHeader>\r\n        <CardTitle>Informasi Akademik</CardTitle>\r\n        <CardDescription>Detail terkait struktur akademik dan penilaian kursus.</CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Book className=\"h-5 w-5 text-gray-500\" />\r\n          <Label htmlFor=\"credits\">Kredit</Label>\r\n        </div>\r\n        <Input\r\n          id=\"credits\"\r\n          type=\"number\"\r\n          value={academics.credits}\r\n          onChange={(e) => handleUpdate('credits', parseInt(e.target.value))}\r\n          placeholder=\"Contoh: 12\"\r\n        />\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Hourglass className=\"h-5 w-5 text-gray-500\" />\r\n          <Label htmlFor=\"workload\">Beban Kerja</Label>\r\n        </div>\r\n        <Input\r\n          id=\"workload\"\r\n          type=\"text\"\r\n          value={academics.workload}\r\n          onChange={(e) => handleUpdate('workload', e.target.value)}\r\n          placeholder=\"Contoh: 12-15 jam/minggu\"\r\n        />\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Award className=\"h-5 w-5 text-gray-500\" />\r\n            <Label htmlFor=\"newAssessment\">Penilaian</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input\r\n              id=\"newAssessment\"\r\n              value={newAssessment}\r\n              onChange={(e) => setNewAssessment(e.target.value)}\r\n              placeholder=\"Tambahkan metode penilaian baru\"\r\n              onKeyPress={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  e.preventDefault();\r\n                  addAssessment();\r\n                }\r\n              }}\r\n            />\r\n            <Button type=\"button\" onClick={addAssessment}>Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {academics.assessment.map((item, index) => (\r\n              <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {item}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"ml-1 h-auto px-1 py-0.5\"\r\n                  onClick={() => removeAssessment(index)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;;;;;;;;;AAOO,SAAS,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAsB;IAClE,MAAM,YAAY,KAAK,SAAS,IAAI;QAAE,SAAS;QAAG,UAAU;QAAI,YAAY,EAAE;IAAC;IAC/E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,eAAe,CAAC,OAA4B;QAChD,SAAS;YACP,WAAW;gBACT,GAAG,SAAS;gBACZ,CAAC,MAAM,EAAE;YACX;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,cAAc,IAAI,OAAO,MAAM,CAAC,UAAU,UAAU,CAAC,QAAQ,CAAC,cAAc,IAAI,KAAK;YACvF,aAAa,cAAc;mBAAI,UAAU,UAAU;gBAAE,cAAc,IAAI;aAAG;YAC1E,iBAAiB;QACnB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,oBAAoB,UAAU,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACtE,aAAa,cAAc;IAC7B;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;;;;;;;kCAE3B,8OAAC,iIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK;wBACL,OAAO,UAAU,OAAO;wBACxB,UAAU,CAAC,IAAM,aAAa,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;wBAChE,aAAY;;;;;;kCAGd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4MAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAW;;;;;;;;;;;;kCAE5B,8OAAC,iIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK;wBACL,OAAO,UAAU,QAAQ;wBACzB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;wBACxD,aAAY;;;;;;kCAGd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,aAAY;wCACZ,YAAY,CAAC;4CACX,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB,EAAE,cAAc;gDAChB;4CACF;wCACF;;;;;;kDAEF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAe;;;;;;;;;;;;0CAEhD,8OAAC;gCAAI,WAAU;0CACZ,UAAU,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAY,WAAU;;4CAC9C;0DACD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DAEhC,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCATL;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB1B", "debugId": null}}, {"offset": {"line": 9011, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/course/steps/tuition-financing-step.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { DollarSign, CreditCard, Gift, X } from 'lucide-react';\r\nimport { CourseData, TuitionAndFinancingData } from '../course-creation-wizard';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\ninterface TuitionFinancingStepProps {\r\n  data: CourseData;\r\n  onUpdate: (updates: Partial<CourseData>) => void;\r\n}\r\n\r\nexport function TuitionFinancingStep({ data, onUpdate }: TuitionFinancingStepProps) {\r\n  const tuitionAndFinancing = data.tuitionAndFinancing || { totalCost: 0, paymentOptions: [], scholarships: [] };\r\n  const [newPaymentOption, setNewPaymentOption] = useState('');\r\n  const [newScholarship, setNewScholarship] = useState('');\r\n\r\n  const handleUpdate = (field: keyof TuitionAndFinancingData, value: string | number | string[]) => {\r\n    onUpdate({\r\n      tuitionAndFinancing: {\r\n        ...tuitionAndFinancing,\r\n        [field]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  const addPaymentOption = () => {\r\n    if (newPaymentOption.trim() !== '' && !tuitionAndFinancing.paymentOptions.includes(newPaymentOption.trim())) {\r\n      handleUpdate('paymentOptions', [...tuitionAndFinancing.paymentOptions, newPaymentOption.trim()]);\r\n      setNewPaymentOption('');\r\n    }\r\n  };\r\n\r\n  const removePaymentOption = (index: number) => {\r\n    const updatedOptions = tuitionAndFinancing.paymentOptions.filter((_, i) => i !== index);\r\n    handleUpdate('paymentOptions', updatedOptions);\r\n  };\r\n\r\n  const addScholarship = () => {\r\n    if (newScholarship.trim() !== '' && !tuitionAndFinancing.scholarships.includes(newScholarship.trim())) {\r\n      handleUpdate('scholarships', [...tuitionAndFinancing.scholarships, newScholarship.trim()]);\r\n      setNewScholarship('');\r\n    }\r\n  };\r\n\r\n  const removeScholarship = (index: number) => {\r\n    const updatedScholarships = tuitionAndFinancing.scholarships.filter((_, i) => i !== index);\r\n    handleUpdate('scholarships', updatedScholarships);\r\n  };\r\n\r\n  return (\r\n    <Card className=\"w-full\">\r\n      <CardHeader>\r\n        <CardTitle>Biaya & Pembiayaan</CardTitle>\r\n        <CardDescription>Detail terkait biaya kursus, opsi pembayaran, dan peluang beasiswa.</CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <DollarSign className=\"h-5 w-5 text-gray-500\" />\r\n          <Label htmlFor=\"totalCost\">Total Biaya</Label>\r\n        </div>\r\n        <Input\r\n          id=\"totalCost\"\r\n          type=\"number\"\r\n          value={tuitionAndFinancing.totalCost}\r\n          onChange={(e) => handleUpdate('totalCost', parseFloat(e.target.value))}\r\n          placeholder=\"Contoh: 6000000\"\r\n        />\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <CreditCard className=\"h-5 w-5 text-gray-500\" />\r\n            <Label htmlFor=\"newPaymentOption\">Opsi Pembayaran</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input\r\n              id=\"newPaymentOption\"\r\n              value={newPaymentOption}\r\n              onChange={(e) => setNewPaymentOption(e.target.value)}\r\n              placeholder=\"Tambahkan opsi pembayaran baru\"\r\n              onKeyPress={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  e.preventDefault();\r\n                  addPaymentOption();\r\n                }\r\n              }}\r\n            />\r\n            <Button type=\"button\" onClick={addPaymentOption}>Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {tuitionAndFinancing.paymentOptions.map((option, index) => (\r\n              <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {option}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"ml-1 h-auto px-1 py-0.5\"\r\n                  onClick={() => removePaymentOption(index)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Gift className=\"h-5 w-5 text-gray-500\" />\r\n            <Label htmlFor=\"newScholarship\">Beasiswa</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input\r\n              id=\"newScholarship\"\r\n              value={newScholarship}\r\n              onChange={(e) => setNewScholarship(e.target.value)}\r\n              placeholder=\"Tambahkan beasiswa baru\"\r\n              onKeyPress={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  e.preventDefault();\r\n                  addScholarship();\r\n                }\r\n              }}\r\n            />\r\n            <Button type=\"button\" onClick={addScholarship}>Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {tuitionAndFinancing.scholarships.map((scholarship, index) => (\r\n              <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {scholarship}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"ml-1 h-auto px-1 py-0.5\"\r\n                  onClick={() => removeScholarship(index)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;;;;;;;;;AAOO,SAAS,qBAAqB,EAAE,IAAI,EAAE,QAAQ,EAA6B;IAChF,MAAM,sBAAsB,KAAK,mBAAmB,IAAI;QAAE,WAAW;QAAG,gBAAgB,EAAE;QAAE,cAAc,EAAE;IAAC;IAC7G,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,eAAe,CAAC,OAAsC;QAC1D,SAAS;YACP,qBAAqB;gBACnB,GAAG,mBAAmB;gBACtB,CAAC,MAAM,EAAE;YACX;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,iBAAiB,IAAI,OAAO,MAAM,CAAC,oBAAoB,cAAc,CAAC,QAAQ,CAAC,iBAAiB,IAAI,KAAK;YAC3G,aAAa,kBAAkB;mBAAI,oBAAoB,cAAc;gBAAE,iBAAiB,IAAI;aAAG;YAC/F,oBAAoB;QACtB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,iBAAiB,oBAAoB,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACjF,aAAa,kBAAkB;IACjC;IAEA,MAAM,iBAAiB;QACrB,IAAI,eAAe,IAAI,OAAO,MAAM,CAAC,oBAAoB,YAAY,CAAC,QAAQ,CAAC,eAAe,IAAI,KAAK;YACrG,aAAa,gBAAgB;mBAAI,oBAAoB,YAAY;gBAAE,eAAe,IAAI;aAAG;YACzF,kBAAkB;QACpB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,sBAAsB,oBAAoB,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACpF,aAAa,gBAAgB;IAC/B;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;;;;;;;kCAE7B,8OAAC,iIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK;wBACL,OAAO,oBAAoB,SAAS;wBACpC,UAAU,CAAC,IAAM,aAAa,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;wBACpE,aAAY;;;;;;kCAGd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAmB;;;;;;;;;;;;0CAEpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,aAAY;wCACZ,YAAY,CAAC;4CACX,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB,EAAE,cAAc;gDAChB;4CACF;wCACF;;;;;;kDAEF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAkB;;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;0CACZ,oBAAoB,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC/C,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAY,WAAU;;4CAC9C;0DACD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,oBAAoB;0DAEnC,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCATL;;;;;;;;;;;;;;;;kCAgBlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAiB;;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,aAAY;wCACZ,YAAY,CAAC;4CACX,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB,EAAE,cAAc;gDAChB;4CACF;wCACF;;;;;;kDAEF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAgB;;;;;;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;0CACZ,oBAAoB,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBAClD,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAY,WAAU;;4CAC9C;0DACD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;0DAEjC,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCATL;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB1B", "debugId": null}}, {"offset": {"line": 9363, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/course/steps/careers-step.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Briefcase, Building, DollarSign, X } from 'lucide-react';\r\nimport { CourseData, CareersData } from '../course-creation-wizard';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\ninterface CareersStepProps {\r\n  data: CourseData;\r\n  onUpdate: (updates: Partial<CourseData>) => void;\r\n}\r\n\r\nexport function CareersStep({ data, onUpdate }: CareersStepProps) {\r\n  const careers = data.careers || { outcomes: [], industries: [], averageSalary: '' };\r\n  const [newOutcome, setNewOutcome] = useState('');\r\n  const [newIndustry, setNewIndustry] = useState('');\r\n\r\n  const handleUpdate = (field: keyof CareersData, value: string | string[]) => {\r\n    onUpdate({\r\n      careers: {\r\n        ...careers,\r\n        [field]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  const addOutcome = () => {\r\n    if (newOutcome.trim() !== '' && !careers.outcomes.includes(newOutcome.trim())) {\r\n      handleUpdate('outcomes', [...careers.outcomes, newOutcome.trim()]);\r\n      setNewOutcome('');\r\n    }\r\n  };\r\n\r\n  const removeOutcome = (index: number) => {\r\n    const updatedOutcomes = careers.outcomes.filter((_, i) => i !== index);\r\n    handleUpdate('outcomes', updatedOutcomes);\r\n  };\r\n\r\n  const addIndustry = () => {\r\n    if (newIndustry.trim() !== '' && !careers.industries.includes(newIndustry.trim())) {\r\n      handleUpdate('industries', [...careers.industries, newIndustry.trim()]);\r\n      setNewIndustry('');\r\n    }\r\n  };\r\n\r\n  const removeIndustry = (index: number) => {\r\n    const updatedIndustries = careers.industries.filter((_, i) => i !== index);\r\n    handleUpdate('industries', updatedIndustries);\r\n  };\r\n\r\n  return (\r\n    <Card className=\"w-full\">\r\n      <CardHeader>\r\n        <CardTitle>Peluang Karir</CardTitle>\r\n        <CardDescription>Detail terkait hasil karir dan industri yang relevan setelah kursus.</CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Briefcase className=\"h-5 w-5 text-gray-500\" />\r\n            <Label htmlFor=\"newOutcome\">Hasil</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input\r\n              id=\"newOutcome\"\r\n              value={newOutcome}\r\n              onChange={(e) => setNewOutcome(e.target.value)}\r\n              placeholder=\"Tambahkan hasil karir baru\"\r\n              onKeyPress={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  e.preventDefault();\r\n                  addOutcome();\r\n                }\r\n              }}\r\n            />\r\n            <Button type=\"button\" onClick={addOutcome}>Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {careers.outcomes.map((outcome, index) => (\r\n              <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {outcome}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"ml-1 h-auto px-1 py-0.5\"\r\n                  onClick={() => removeOutcome(index)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Building className=\"h-5 w-5 text-gray-500\" />\r\n            <Label htmlFor=\"newIndustry\">Industri</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input\r\n              id=\"newIndustry\"\r\n              value={newIndustry}\r\n              onChange={(e) => setNewIndustry(e.target.value)}\r\n              placeholder=\"Tambahkan industri baru\"\r\n              onKeyPress={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  e.preventDefault();\r\n                  addIndustry();\r\n                }\r\n              }}\r\n            />\r\n            <Button type=\"button\" onClick={addIndustry}>Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {careers.industries.map((industry, index) => (\r\n              <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {industry}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"ml-1 h-auto px-1 py-0.5\"\r\n                  onClick={() => removeIndustry(index)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          <DollarSign className=\"h-5 w-5 text-gray-500\" />\r\n          <Label htmlFor=\"averageSalary\">Rata-rata Gaji</Label>\r\n        </div>\r\n        <Input\r\n          id=\"averageSalary\"\r\n          type=\"text\"\r\n          value={careers.averageSalary}\r\n          onChange={(e) => handleUpdate('averageSalary', e.target.value)}\r\n          placeholder=\"Contoh: Rp780.000.000 - Rp1.140.000.000 per tahun\"\r\n        />\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;;;;;;;;;AAOO,SAAS,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAoB;IAC9D,MAAM,UAAU,KAAK,OAAO,IAAI;QAAE,UAAU,EAAE;QAAE,YAAY,EAAE;QAAE,eAAe;IAAG;IAClF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC,OAA0B;QAC9C,SAAS;YACP,SAAS;gBACP,GAAG,OAAO;gBACV,CAAC,MAAM,EAAE;YACX;QACF;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,QAAQ,CAAC,QAAQ,CAAC,WAAW,IAAI,KAAK;YAC7E,aAAa,YAAY;mBAAI,QAAQ,QAAQ;gBAAE,WAAW,IAAI;aAAG;YACjE,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,kBAAkB,QAAQ,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAChE,aAAa,YAAY;IAC3B;IAEA,MAAM,cAAc;QAClB,IAAI,YAAY,IAAI,OAAO,MAAM,CAAC,QAAQ,UAAU,CAAC,QAAQ,CAAC,YAAY,IAAI,KAAK;YACjF,aAAa,cAAc;mBAAI,QAAQ,UAAU;gBAAE,YAAY,IAAI;aAAG;YACtE,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,oBAAoB,QAAQ,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACpE,aAAa,cAAc;IAC7B;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAa;;;;;;;;;;;;0CAE9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,YAAY,CAAC;4CACX,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB,EAAE,cAAc;gDAChB;4CACF;wCACF;;;;;;kDAEF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAY;;;;;;;;;;;;0CAE7C,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAY,WAAU;;4CAC9C;0DACD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;0DAE7B,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCATL;;;;;;;;;;;;;;;;kCAgBlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,aAAY;wCACZ,YAAY,CAAC;4CACX,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB,EAAE,cAAc;gDAChB;4CACF;wCACF;;;;;;kDAEF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAa;;;;;;;;;;;;0CAE9C,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBACjC,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAY,WAAU;;4CAC9C;0DACD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,eAAe;0DAE9B,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCATL;;;;;;;;;;;;;;;;kCAgBlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAgB;;;;;;;;;;;;kCAEjC,8OAAC,iIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK;wBACL,OAAO,QAAQ,aAAa;wBAC5B,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,KAAK;wBAC7D,aAAY;;;;;;;;;;;;;;;;;;AAKtB", "debugId": null}}, {"offset": {"line": 9715, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/course/steps/student-experience-step.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Plus, X, MessageSquare, HardHat, LifeBuoy } from 'lucide-react';\r\nimport { CourseData, StudentExperienceData } from '../course-creation-wizard';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\ninterface StudentExperienceStepProps {\r\n  data: CourseData;\r\n  onUpdate: (updates: Partial<CourseData>) => void;\r\n}\r\n\r\nexport function StudentExperienceStep({ data, onUpdate }: StudentExperienceStepProps) {\r\n  const studentExperience = data.studentExperience || { testimonials: [], facilities: [], support: [] };\r\n  const [newFacility, setNewFacility] = useState('');\r\n  const [newSupport, setNewSupport] = useState('');\r\n\r\n  const handleUpdate = (field: keyof StudentExperienceData, value: string | string[] | { name: string; feedback: string }[]) => {\r\n    onUpdate({\r\n      studentExperience: {\r\n        ...studentExperience,\r\n        [field]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  const addTestimonial = () => {\r\n    handleUpdate('testimonials', [...studentExperience.testimonials, { name: '', feedback: '' }]);\r\n  };\r\n\r\n  const updateTestimonial = (index: number, field: 'name' | 'feedback', value: string) => {\r\n    const updatedTestimonials = [...studentExperience.testimonials];\r\n    updatedTestimonials[index] = { ...updatedTestimonials[index], [field]: value };\r\n    handleUpdate('testimonials', updatedTestimonials);\r\n  };\r\n\r\n  const removeTestimonial = (index: number) => {\r\n    const updatedTestimonials = studentExperience.testimonials.filter((_, i) => i !== index);\r\n    handleUpdate('testimonials', updatedTestimonials);\r\n  };\r\n\r\n  const addFacility = () => {\r\n    if (newFacility.trim() !== '' && !studentExperience.facilities.includes(newFacility.trim())) {\r\n      handleUpdate('facilities', [...studentExperience.facilities, newFacility.trim()]);\r\n      setNewFacility('');\r\n    }\r\n  };\r\n\r\n  const removeFacility = (index: number) => {\r\n    const updatedFacilities = studentExperience.facilities.filter((_, i) => i !== index);\r\n    handleUpdate('facilities', updatedFacilities);\r\n  };\r\n\r\n  const addSupport = () => {\r\n    if (newSupport.trim() !== '' && !studentExperience.support.includes(newSupport.trim())) {\r\n      handleUpdate('support', [...studentExperience.support, newSupport.trim()]);\r\n      setNewSupport('');\r\n    }\r\n  };\r\n\r\n  const removeSupport = (index: number) => {\r\n    const updatedSupport = studentExperience.support.filter((_, i) => i !== index);\r\n    handleUpdate('support', updatedSupport);\r\n  };\r\n\r\n  return (\r\n    <Card className=\"w-full\">\r\n      <CardHeader>\r\n        <CardTitle>Pengalaman Mahasiswa</CardTitle>\r\n        <CardDescription>Detail terkait pengalaman, fasilitas, dan dukungan yang akan didapat mahasiswa.</CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        <div>\r\n          <div className=\"flex items-center space-x-2 mb-2\">\r\n            <MessageSquare className=\"h-5 w-5 text-gray-500\" />\r\n            <Label>Testimoni</Label>\r\n          </div>\r\n          {studentExperience.testimonials.map((testimonial, index) => (\r\n            <div key={index} className=\"flex items-end space-x-2 mb-4\">\r\n              <div className=\"flex-grow space-y-2\">\r\n                <Input\r\n                  placeholder=\"Nama\"\r\n                  value={testimonial.name}\r\n                  onChange={(e) => updateTestimonial(index, 'name', e.target.value)}\r\n                />\r\n                <Textarea\r\n                  placeholder=\"Umpan Balik\"\r\n                  value={testimonial.feedback}\r\n                  onChange={(e) => updateTestimonial(index, 'feedback', e.target.value)}\r\n                />\r\n              </div>\r\n              <Button variant=\"destructive\" size=\"icon\" onClick={() => removeTestimonial(index)}>\r\n                <X className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n          ))}\r\n          <Button variant=\"outline\" onClick={addTestimonial}>\r\n            <Plus className=\"h-4 w-4 mr-2\" /> Tambah Testimoni\r\n          </Button>\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <HardHat className=\"h-5 w-5 text-gray-500\" />\r\n            <Label htmlFor=\"newFacility\">Fasilitas</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input\r\n              id=\"newFacility\"\r\n              value={newFacility}\r\n              onChange={(e) => setNewFacility(e.target.value)}\r\n              placeholder=\"Tambahkan fasilitas baru\"\r\n              onKeyPress={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  e.preventDefault();\r\n                  addFacility();\r\n                }\r\n              }}\r\n            />\r\n            <Button type=\"button\" onClick={addFacility}>Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {studentExperience.facilities.map((facility, index) => (\r\n              <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {facility}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"ml-1 h-auto px-1 py-0.5\"\r\n                  onClick={() => removeFacility(index)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <LifeBuoy className=\"h-5 w-5 text-gray-500\" />\r\n            <Label htmlFor=\"newSupport\">Dukungan</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input\r\n              id=\"newSupport\"\r\n              value={newSupport}\r\n              onChange={(e) => setNewSupport(e.target.value)}\r\n              placeholder=\"Tambahkan dukungan baru\"\r\n              onKeyPress={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  e.preventDefault();\r\n                  addSupport();\r\n                }\r\n              }}\r\n            />\r\n            <Button type=\"button\" onClick={addSupport}>Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {studentExperience.support.map((supportItem, index) => (\r\n              <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {supportItem}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"ml-1 h-auto px-1 py-0.5\"\r\n                  onClick={() => removeSupport(index)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;;;;;;;;;;AAOO,SAAS,sBAAsB,EAAE,IAAI,EAAE,QAAQ,EAA8B;IAClF,MAAM,oBAAoB,KAAK,iBAAiB,IAAI;QAAE,cAAc,EAAE;QAAE,YAAY,EAAE;QAAE,SAAS,EAAE;IAAC;IACpG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe,CAAC,OAAoC;QACxD,SAAS;YACP,mBAAmB;gBACjB,GAAG,iBAAiB;gBACpB,CAAC,MAAM,EAAE;YACX;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,aAAa,gBAAgB;eAAI,kBAAkB,YAAY;YAAE;gBAAE,MAAM;gBAAI,UAAU;YAAG;SAAE;IAC9F;IAEA,MAAM,oBAAoB,CAAC,OAAe,OAA4B;QACpE,MAAM,sBAAsB;eAAI,kBAAkB,YAAY;SAAC;QAC/D,mBAAmB,CAAC,MAAM,GAAG;YAAE,GAAG,mBAAmB,CAAC,MAAM;YAAE,CAAC,MAAM,EAAE;QAAM;QAC7E,aAAa,gBAAgB;IAC/B;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,sBAAsB,kBAAkB,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAClF,aAAa,gBAAgB;IAC/B;IAEA,MAAM,cAAc;QAClB,IAAI,YAAY,IAAI,OAAO,MAAM,CAAC,kBAAkB,UAAU,CAAC,QAAQ,CAAC,YAAY,IAAI,KAAK;YAC3F,aAAa,cAAc;mBAAI,kBAAkB,UAAU;gBAAE,YAAY,IAAI;aAAG;YAChF,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,oBAAoB,kBAAkB,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC9E,aAAa,cAAc;IAC7B;IAEA,MAAM,aAAa;QACjB,IAAI,WAAW,IAAI,OAAO,MAAM,CAAC,kBAAkB,OAAO,CAAC,QAAQ,CAAC,WAAW,IAAI,KAAK;YACtF,aAAa,WAAW;mBAAI,kBAAkB,OAAO;gBAAE,WAAW,IAAI;aAAG;YACzE,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,iBAAiB,kBAAkB,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACxE,aAAa,WAAW;IAC1B;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;;;;;;;4BAER,kBAAkB,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBAChD,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO,YAAY,IAAI;oDACvB,UAAU,CAAC,IAAM,kBAAkB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;8DAElE,8OAAC,oIAAA,CAAA,WAAQ;oDACP,aAAY;oDACZ,OAAO,YAAY,QAAQ;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAGxE,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAc,MAAK;4CAAO,SAAS,IAAM,kBAAkB;sDACzE,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;mCAdP;;;;;0CAkBZ,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAIrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,aAAY;wCACZ,YAAY,CAAC;4CACX,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB,EAAE,cAAc;gDAChB;4CACF;wCACF;;;;;;kDAEF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAa;;;;;;;;;;;;0CAE9C,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC3C,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAY,WAAU;;4CAC9C;0DACD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,eAAe;0DAE9B,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCATL;;;;;;;;;;;;;;;;kCAgBlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAa;;;;;;;;;;;;0CAE9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,YAAY,CAAC;4CACX,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB,EAAE,cAAc;gDAChB;4CACF;wCACF;;;;;;kDAEF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAY;;;;;;;;;;;;0CAE7C,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,OAAO,CAAC,GAAG,CAAC,CAAC,aAAa,sBAC3C,8OAAC,iIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAY,WAAU;;4CAC9C;0DACD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;0DAE7B,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCATL;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB1B", "debugId": null}}, {"offset": {"line": 10159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/course/steps/course-details-step.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';\r\nimport { CourseData } from '../course-creation-wizard';\r\nimport { AdmissionsStep } from './admissions-step';\r\nimport { AcademicsStep } from './academics-step';\r\nimport { TuitionFinancingStep } from './tuition-financing-step';\r\nimport { CareersStep } from './careers-step';\r\nimport { StudentExperienceStep } from './student-experience-step';\r\n\r\ninterface CourseDetailsStepProps {\r\n  data: CourseData;\r\n  onUpdate: (updates: Partial<CourseData>) => void;\r\n}\r\n\r\nexport function CourseDetailsStep({ data, onUpdate }: CourseDetailsStepProps) {\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>Detail Course</CardTitle>\r\n        <CardDescription>\r\n          Kelola detail penerimaan, akademik, pembia<PERSON>an, karir, dan pengalaman siswa.\r\n        </CardDescription>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <Tabs defaultValue=\"admissions\" className=\"w-full\">\r\n          <TabsList className=\"grid w-full grid-cols-5\">\r\n            <TabsTrigger value=\"admissions\">Penerimaan</TabsTrigger>\r\n            <TabsTrigger value=\"academics\">Akademik</TabsTrigger>\r\n            <TabsTrigger value=\"tuition-financing\">Biaya & Pembiayaan</TabsTrigger>\r\n            <TabsTrigger value=\"careers\">Karir</TabsTrigger>\r\n            <TabsTrigger value=\"student-experience\">Pengalaman Siswa</TabsTrigger>\r\n          </TabsList>\r\n          <div className=\"h-[400px] overflow-y-auto pr-4\"> {/* Fixed height with scroll */}\r\n            <TabsContent value=\"admissions\">\r\n              <AdmissionsStep data={data} onUpdate={onUpdate} />\r\n            </TabsContent>\r\n            <TabsContent value=\"academics\">\r\n              <AcademicsStep data={data} onUpdate={onUpdate} />\r\n            </TabsContent>\r\n            <TabsContent value=\"tuition-financing\">\r\n              <TuitionFinancingStep data={data} onUpdate={onUpdate} />\r\n            </TabsContent>\r\n            <TabsContent value=\"careers\">\r\n              <CareersStep data={data} onUpdate={onUpdate} />\r\n            </TabsContent>\r\n            <TabsContent value=\"student-experience\">\r\n              <StudentExperienceStep data={data} onUpdate={onUpdate} />\r\n            </TabsContent>\r\n          </div>\r\n        </Tabs>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;AAOO,SAAS,kBAAkB,EAAE,IAAI,EAAE,QAAQ,EAA0B;IAC1E,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAa,WAAU;;sCACxC,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAa;;;;;;8CAChC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;8CAC/B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAoB;;;;;;8CACvC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAU;;;;;;8CAC7B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAqB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAI,WAAU;;gCAAiC;8CAC9C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CACjB,cAAA,8OAAC,2JAAA,CAAA,iBAAc;wCAAC,MAAM;wCAAM,UAAU;;;;;;;;;;;8CAExC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CACjB,cAAA,8OAAC,0JAAA,CAAA,gBAAa;wCAAC,MAAM;wCAAM,UAAU;;;;;;;;;;;8CAEvC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CACjB,cAAA,8OAAC,qKAAA,CAAA,uBAAoB;wCAAC,MAAM;wCAAM,UAAU;;;;;;;;;;;8CAE9C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CACjB,cAAA,8OAAC,wJAAA,CAAA,cAAW;wCAAC,MAAM;wCAAM,UAAU;;;;;;;;;;;8CAErC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CACjB,cAAA,8OAAC,sKAAA,CAAA,wBAAqB;wCAAC,MAAM;wCAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3D", "debugId": null}}, {"offset": {"line": 10366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/course/course-creation-wizard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { ChevronLeft, ChevronRight, Check } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\n\r\n// Step components\r\nimport { BasicInfoStep } from './steps/basic-info-step';\r\nimport { ModuleStructureStep } from './steps/module-structure-step';\r\nimport { ContentCreationStep } from './steps/content-creation-step';\r\nimport { PublishingStep } from './steps/publishing-step';\r\nimport { CourseDetailsStep } from './steps/course-details-step';\r\n\r\nexport interface CourseData {\r\n  // Basic Info\r\n  name: string;\r\n  description: string;\r\n  instructor: string;\r\n  courseCode: string;\r\n  type: 'self_paced' | 'verified';\r\n  enrollmentType: 'code' | 'invitation' | 'both' | 'purchase';\r\n  startDate?: Date | null;\r\n  endDate?: Date | null;\r\n  coverImage?: File;\r\n  coverImagePreview?: string;\r\n  isPurchasable?: boolean;\r\n  price?: number;\r\n  currency?: string;\r\n  previewMode?: boolean;\r\n  \r\n  // Module Structure\r\n  modules: ModuleData[];\r\n  \r\n  // Publishing\r\n  isPublished: boolean;\r\n  assignedClasses: number[];\r\n  finalExam?: QuizData;\r\n\r\n  // Course Details\r\n  admissions?: AdmissionsData;\r\n  academics?: AcademicsData;\r\n  tuitionAndFinancing?: TuitionAndFinancingData;\r\n  careers?: CareersData;\r\n  studentExperience?: StudentExperienceData;\r\n}\r\n\r\nexport interface ModuleData {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  orderIndex: number;\r\n  chapters: ChapterData[];\r\n  hasModuleQuiz: boolean;\r\n  moduleQuiz?: QuizData;\r\n}\r\n\r\nexport interface ChapterData {\r\n  id: string;\r\n  name: string;\r\n  content: any[]; // Changed to any[] to accommodate JSON structure\r\n  orderIndex: number;\r\n  hasChapterQuiz: boolean;\r\n  chapterQuiz?: QuizData;\r\n}\r\n\r\nexport interface QuizData {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  questions: QuestionData[];\r\n  timeLimit?: number;\r\n  minimumScore: number;\r\n}\r\n\r\nexport interface AdmissionsData {\r\n  requirements: string[];\r\n  applicationDeadline: string;\r\n  prerequisites: string[];\r\n}\r\n\r\nexport interface AcademicsData {\r\n  credits: number;\r\n  workload: string;\r\n  assessment: string[];\r\n}\r\n\r\nexport interface TuitionAndFinancingData {\r\n  totalCost: number;\r\n  paymentOptions: string[];\r\n  scholarships: string[];\r\n}\r\n\r\nexport interface CareersData {\r\n  outcomes: string[];\r\n  industries: string[];\r\n  averageSalary: string;\r\n}\r\n\r\nexport interface StudentExperienceData {\r\n  testimonials: { name: string; feedback: string }[];\r\n  facilities: string[];\r\n  support: string[];\r\n}\r\n\r\nexport interface QuestionData {\r\n  id: string;\r\n  type: 'multiple_choice' | 'true_false' | 'essay';\r\n  question: any[]; // Changed to any[] to accommodate JSON structure\r\n  options?: { content: any[]; isCorrect: boolean }[]; // Updated for new JSON structure\r\n  essayAnswer?: string | null; // Renamed from correctAnswer, can be null\r\n  explanation?: any[] | null; // New column, can be null\r\n  points: number;\r\n  orderIndex: number;\r\n}\r\n\r\nconst STEPS = [\r\n  {\r\n    id: 'basic-info',\r\n    title: 'Informasi Dasar',\r\n    description: 'Detail course dan pengaturan dasar'\r\n  },\r\n  {\r\n    id: 'module-structure',\r\n    title: 'Struktur Modul',\r\n    description: 'Buat modul dan chapter untuk course'\r\n  },\r\n  {\r\n    id: 'content-creation',\r\n    title: 'Pembuatan Konten',\r\n    description: 'Tambahkan konten dan quiz untuk setiap chapter'\r\n  },\r\n  {\r\n    id: 'course-details',\r\n    title: 'Informasi Tambahan',\r\n    description: 'Detail penerimaan, akademik, pembiayaan, karir, dan pengalaman siswa'\r\n  },\r\n  {\r\n    id: 'publishing',\r\n    title: 'Publikasi',\r\n    description: 'Review dan publikasikan course'\r\n  }\r\n];\r\n\r\ninterface CourseCreationWizardProps {\r\n  onComplete: (courseData: CourseData) => Promise<void>;\r\n  onCancel: () => void;\r\n  initialData?: Partial<CourseData>;\r\n}\r\n\r\nexport function CourseCreationWizard({ \r\n  onComplete, \r\n  onCancel, \r\n  initialData \r\n}: CourseCreationWizardProps) {\r\n  const [currentStep, setCurrentStep] = useState(0);\r\n  const [courseData, setCourseData] = useState<CourseData>({\r\n    name: initialData?.name || '',\r\n    description: initialData?.description || '',\r\n    instructor: initialData?.instructor || '',\r\n    courseCode: initialData?.courseCode || '',\r\n    type: initialData?.type || 'self_paced',\r\n    enrollmentType: initialData?.enrollmentType || 'code',\r\n    startDate: initialData?.startDate,\r\n    endDate: initialData?.endDate,\r\n    coverImage: initialData?.coverImage,\r\n    coverImagePreview: initialData?.coverImagePreview,\r\n    isPurchasable: initialData?.isPurchasable ?? false,\r\n    price: initialData?.price,\r\n    currency: initialData?.currency || '',\r\n    previewMode: initialData?.previewMode ?? false,\r\n    modules: initialData?.modules || [],\r\n    isPublished: initialData?.isPublished ?? false,\r\n    assignedClasses: initialData?.assignedClasses || [],\r\n    finalExam: initialData?.finalExam,\r\n    admissions: initialData?.admissions || { requirements: [], applicationDeadline: '', prerequisites: [] },\r\n    academics: initialData?.academics || { credits: 0, workload: '', assessment: [] },\r\n    tuitionAndFinancing: initialData?.tuitionAndFinancing || { totalCost: 0, paymentOptions: [], scholarships: [] },\r\n    careers: initialData?.careers || { outcomes: [], industries: [], averageSalary: '' },\r\n    studentExperience: initialData?.studentExperience || { testimonials: [], facilities: [], support: [] },\r\n  });\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // Load AI generated data from sessionStorage on component mount\r\n  useEffect(() => {\r\n    const loadAIGeneratedData = () => {\r\n      try {\r\n        const aiGeneratedData = sessionStorage.getItem('ai_generated_course_data');\r\n        if (aiGeneratedData) {\r\n          const parsedData = JSON.parse(aiGeneratedData);\r\n          \r\n          // Merge AI generated data with existing course data\r\n          setCourseData(prev => ({\r\n            ...prev,\r\n            name: parsedData.name || prev.name,\r\n            description: parsedData.description || prev.description,\r\n            courseCode: parsedData.courseCode || prev.courseCode,\r\n            modules: parsedData.modules || prev.modules,\r\n            finalExam: parsedData.finalExam || prev.finalExam,\r\n            ...initialData // initialData takes precedence\r\n          }));\r\n          \r\n          // Clear the session storage after loading\r\n          sessionStorage.removeItem('ai_generated_course_data');\r\n          \r\n          // If we have AI generated modules, skip to content creation step\r\n          if (parsedData.modules && parsedData.modules.length > 0) {\r\n            setCurrentStep(2); // Skip to Content Creation step\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading AI generated data:', error);\r\n      }\r\n    };\r\n\r\n    loadAIGeneratedData();\r\n  }, [initialData]);\r\n\r\n  const updateCourseData = (updates: Partial<CourseData>) => {\r\n    setCourseData(prev => ({ ...prev, ...updates }));\r\n  };\r\n\r\n  const validateStepData = (step: number): boolean => {\r\n    switch (step) {\r\n      case 0: // Basic Info\r\n        const basicValidation = !!courseData.name && !!courseData.description && !!courseData.instructor && !!courseData.courseCode;\r\n        // Additional validation for purchase type\r\n        if (courseData.enrollmentType === 'purchase') {\r\n          return basicValidation && !!courseData.price && courseData.price > 0 && !!courseData.currency;\r\n        }\r\n        return basicValidation;\r\n      case 1: // Module Structure\r\n        return courseData.modules.length > 0 &&\r\n               courseData.modules.every(module =>\r\n                 !!module.name && module.chapters.length > 0\r\n               );\r\n      case 2: // Content Creation\r\n        return courseData.modules.every(module =>\r\n          module.chapters.every(chapter => !!chapter.content)\r\n        );\r\n      case 3: // Course Details (combining Admissions, Academics, Tuition & Financing, Careers, Student Experience)\r\n        // Validation for the combined step: at least one field in any of the sub-sections should have data.\r\n        const admissionsValid = !!courseData.admissions &&\r\n                                (courseData.admissions.requirements.length > 0 ||\r\n                                 !!courseData.admissions.applicationDeadline ||\r\n                                 courseData.admissions.prerequisites.length > 0);\r\n        const academicsValid = !!courseData.academics &&\r\n                               (courseData.academics.credits > 0 ||\r\n                                !!courseData.academics.workload ||\r\n                                courseData.academics.assessment.length > 0);\r\n        const tuitionFinancingValid = !!courseData.tuitionAndFinancing &&\r\n                                      (!!courseData.tuitionAndFinancing.totalCost ||\r\n                                       courseData.tuitionAndFinancing.paymentOptions.length > 0 ||\r\n                                       courseData.tuitionAndFinancing.scholarships.length > 0);\r\n        const careersValid = !!courseData.careers &&\r\n                             (courseData.careers.outcomes.length > 0 ||\r\n                              courseData.careers.industries.length > 0 ||\r\n                              !!courseData.careers.averageSalary);\r\n        const studentExperienceValid = !!courseData.studentExperience &&\r\n                                       (courseData.studentExperience.testimonials.length > 0 ||\r\n                                        courseData.studentExperience.facilities.length > 0 ||\r\n                                        courseData.studentExperience.support.length > 0);\r\n\r\n        return admissionsValid || academicsValid || tuitionFinancingValid || careersValid || studentExperienceValid;\r\n      case 4: // Publishing\r\n        return true; // Publishing step doesn't have its own data to validate\r\n      default:\r\n        return false;\r\n    }\r\n  };\r\n\r\n  const canProceedToNext = () => {\r\n    return validateStepData(currentStep);\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentStep < STEPS.length - 1) {\r\n      setCurrentStep(currentStep + 1);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentStep > 0) {\r\n      setCurrentStep(currentStep - 1);\r\n    }\r\n  };\r\n\r\n  const handleComplete = async () => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      await onComplete(courseData);\r\n    } catch (error) {\r\n      console.error('Error creating course:', error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const renderStepContent = () => {\r\n    switch (currentStep) {\r\n      case 0:\r\n        return (\r\n          <BasicInfoStep\r\n            data={courseData}\r\n            onUpdate={updateCourseData}\r\n          />\r\n        );\r\n      case 1:\r\n        return (\r\n          <ModuleStructureStep\r\n            data={courseData}\r\n            onUpdate={updateCourseData}\r\n          />\r\n        );\r\n      case 2:\r\n        return (\r\n          <ContentCreationStep\r\n            data={courseData}\r\n            onUpdate={updateCourseData}\r\n          />\r\n        );\r\n      case 3:\r\n        return (\r\n          <CourseDetailsStep\r\n            data={courseData}\r\n            onUpdate={updateCourseData}\r\n          />\r\n        );\r\n      case 4:\r\n        return (\r\n          <PublishingStep\r\n            data={courseData}\r\n            onPublish={handleComplete}\r\n            isPublishing={isSubmitting}\r\n          />\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const progressPercentage = ((currentStep + 1) / STEPS.length) * 100;\r\n\r\n  return (\r\n    <div className=\"w-full p-6 space-y-6\">\r\n      {/* Step Indicator */}\r\n      <Card>\r\n        <CardContent className=\"pt-6\">\r\n          <div className=\"flex items-center justify-between gap-x-4 overflow-x-auto pb-4 px-4\">\r\n            {STEPS.map((step, index) => (\r\n              <div key={step.id} className=\"flex flex-col items-center flex-grow\">\r\n                <div className={cn(\r\n                  \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors duration-200\",\r\n                  index === currentStep ? \"bg-primary text-primary-foreground\" : \"bg-muted text-muted-foreground\",\r\n                  index < currentStep && \"bg-green-500 text-white\" // Completed step\r\n                )}>\r\n                  {index < currentStep ? <Check className=\"w-4 h-4\" /> : index + 1}\r\n                </div>\r\n                <span className={cn(\r\n                  \"mt-1 text-xs text-center whitespace-nowrap\",\r\n                  index === currentStep ? \"text-primary font-medium\" : \"text-muted-foreground\"\r\n                )}>\r\n                  {step.title}\r\n                </span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          <Separator className=\"my-4\" />\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex justify-between text-sm\">\r\n              <span>Langkah {currentStep + 1} dari {STEPS.length}</span>\r\n              <span>{Math.round(progressPercentage)}% selesai</span>\r\n            </div>\r\n            <Progress value={progressPercentage} className=\"h-2\" />\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Step Content */}\r\n      <Card>\r\n        <CardContent>\r\n          {renderStepContent()}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Navigation Buttons */}\r\n      <div className=\"flex justify-between\">\r\n        <Button \r\n          variant=\"outline\" \r\n          onClick={handlePrevious}\r\n          disabled={currentStep === 0}\r\n        >\r\n          <ChevronLeft className=\"w-4 h-4 mr-2\" />\r\n          Sebelumnya\r\n        </Button>\r\n        \r\n        <div className=\"flex space-x-2\">\r\n          {currentStep === STEPS.length - 1 ? (\r\n            <Button \r\n              onClick={handleComplete}\r\n              disabled={!canProceedToNext() || isSubmitting}\r\n            >\r\n              {isSubmitting ? 'Membuat Course...' : 'Selesai & Buat Course'}\r\n            </Button>\r\n          ) : (\r\n            <Button \r\n              onClick={handleNext}\r\n              disabled={!canProceedToNext()}\r\n            >\r\n              Selanjutnya\r\n              <ChevronRight className=\"w-4 h-4 ml-2\" />\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;AAuHA,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;CACD;AAQM,SAAS,qBAAqB,EACnC,UAAU,EACV,QAAQ,EACR,WAAW,EACe;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,MAAM,aAAa,QAAQ;QAC3B,aAAa,aAAa,eAAe;QACzC,YAAY,aAAa,cAAc;QACvC,YAAY,aAAa,cAAc;QACvC,MAAM,aAAa,QAAQ;QAC3B,gBAAgB,aAAa,kBAAkB;QAC/C,WAAW,aAAa;QACxB,SAAS,aAAa;QACtB,YAAY,aAAa;QACzB,mBAAmB,aAAa;QAChC,eAAe,aAAa,iBAAiB;QAC7C,OAAO,aAAa;QACpB,UAAU,aAAa,YAAY;QACnC,aAAa,aAAa,eAAe;QACzC,SAAS,aAAa,WAAW,EAAE;QACnC,aAAa,aAAa,eAAe;QACzC,iBAAiB,aAAa,mBAAmB,EAAE;QACnD,WAAW,aAAa;QACxB,YAAY,aAAa,cAAc;YAAE,cAAc,EAAE;YAAE,qBAAqB;YAAI,eAAe,EAAE;QAAC;QACtG,WAAW,aAAa,aAAa;YAAE,SAAS;YAAG,UAAU;YAAI,YAAY,EAAE;QAAC;QAChF,qBAAqB,aAAa,uBAAuB;YAAE,WAAW;YAAG,gBAAgB,EAAE;YAAE,cAAc,EAAE;QAAC;QAC9G,SAAS,aAAa,WAAW;YAAE,UAAU,EAAE;YAAE,YAAY,EAAE;YAAE,eAAe;QAAG;QACnF,mBAAmB,aAAa,qBAAqB;YAAE,cAAc,EAAE;YAAE,YAAY,EAAE;YAAE,SAAS,EAAE;QAAC;IACvG;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,IAAI;gBACF,MAAM,kBAAkB,eAAe,OAAO,CAAC;gBAC/C,IAAI,iBAAiB;oBACnB,MAAM,aAAa,KAAK,KAAK,CAAC;oBAE9B,oDAAoD;oBACpD,cAAc,CAAA,OAAQ,CAAC;4BACrB,GAAG,IAAI;4BACP,MAAM,WAAW,IAAI,IAAI,KAAK,IAAI;4BAClC,aAAa,WAAW,WAAW,IAAI,KAAK,WAAW;4BACvD,YAAY,WAAW,UAAU,IAAI,KAAK,UAAU;4BACpD,SAAS,WAAW,OAAO,IAAI,KAAK,OAAO;4BAC3C,WAAW,WAAW,SAAS,IAAI,KAAK,SAAS;4BACjD,GAAG,YAAY,+BAA+B;wBAChD,CAAC;oBAED,0CAA0C;oBAC1C,eAAe,UAAU,CAAC;oBAE1B,iEAAiE;oBACjE,IAAI,WAAW,OAAO,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,GAAG;wBACvD,eAAe,IAAI,gCAAgC;oBACrD;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,mBAAmB,CAAC;QACxB,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAChD;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,MAAM,kBAAkB,CAAC,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC,WAAW,WAAW,IAAI,CAAC,CAAC,WAAW,UAAU,IAAI,CAAC,CAAC,WAAW,UAAU;gBAC3H,0CAA0C;gBAC1C,IAAI,WAAW,cAAc,KAAK,YAAY;oBAC5C,OAAO,mBAAmB,CAAC,CAAC,WAAW,KAAK,IAAI,WAAW,KAAK,GAAG,KAAK,CAAC,CAAC,WAAW,QAAQ;gBAC/F;gBACA,OAAO;YACT,KAAK;gBACH,OAAO,WAAW,OAAO,CAAC,MAAM,GAAG,KAC5B,WAAW,OAAO,CAAC,KAAK,CAAC,CAAA,SACvB,CAAC,CAAC,OAAO,IAAI,IAAI,OAAO,QAAQ,CAAC,MAAM,GAAG;YAErD,KAAK;gBACH,OAAO,WAAW,OAAO,CAAC,KAAK,CAAC,CAAA,SAC9B,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAA,UAAW,CAAC,CAAC,QAAQ,OAAO;YAEtD,KAAK;gBACH,oGAAoG;gBACpG,MAAM,kBAAkB,CAAC,CAAC,WAAW,UAAU,IACvB,CAAC,WAAW,UAAU,CAAC,YAAY,CAAC,MAAM,GAAG,KAC5C,CAAC,CAAC,WAAW,UAAU,CAAC,mBAAmB,IAC3C,WAAW,UAAU,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC;gBACvE,MAAM,iBAAiB,CAAC,CAAC,WAAW,SAAS,IACtB,CAAC,WAAW,SAAS,CAAC,OAAO,GAAG,KAC/B,CAAC,CAAC,WAAW,SAAS,CAAC,QAAQ,IAC/B,WAAW,SAAS,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;gBAClE,MAAM,wBAAwB,CAAC,CAAC,WAAW,mBAAmB,IAChC,CAAC,CAAC,CAAC,WAAW,mBAAmB,CAAC,SAAS,IAC1C,WAAW,mBAAmB,CAAC,cAAc,CAAC,MAAM,GAAG,KACvD,WAAW,mBAAmB,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC;gBACrF,MAAM,eAAe,CAAC,CAAC,WAAW,OAAO,IACpB,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,KACrC,WAAW,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,KACvC,CAAC,CAAC,WAAW,OAAO,CAAC,aAAa;gBACxD,MAAM,yBAAyB,CAAC,CAAC,WAAW,iBAAiB,IAC9B,CAAC,WAAW,iBAAiB,CAAC,YAAY,CAAC,MAAM,GAAG,KACnD,WAAW,iBAAiB,CAAC,UAAU,CAAC,MAAM,GAAG,KACjD,WAAW,iBAAiB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;gBAE/E,OAAO,mBAAmB,kBAAkB,yBAAyB,gBAAgB;YACvF,KAAK;gBACH,OAAO,MAAM,wDAAwD;YACvE;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAO,iBAAiB;IAC1B;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,MAAM,MAAM,GAAG,GAAG;YAClC,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC,8JAAA,CAAA,gBAAa;oBACZ,MAAM;oBACN,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,8OAAC,oKAAA,CAAA,sBAAmB;oBAClB,MAAM;oBACN,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,8OAAC,oKAAA,CAAA,sBAAmB;oBAClB,MAAM;oBACN,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,8OAAC,kKAAA,CAAA,oBAAiB;oBAChB,MAAM;oBACN,UAAU;;;;;;YAGhB,KAAK;gBACH,qBACE,8OAAC,2JAAA,CAAA,iBAAc;oBACb,MAAM;oBACN,WAAW;oBACX,cAAc;;;;;;YAGpB;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,AAAC,CAAC,cAAc,CAAC,IAAI,MAAM,MAAM,GAAI;IAEhE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,8GACA,UAAU,cAAc,uCAAuC,kCAC/D,QAAQ,eAAe,0BAA0B,iBAAiB;;sDAEjE,QAAQ,4BAAc,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;uDAAe,QAAQ;;;;;;sDAEjE,8OAAC;4CAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,8CACA,UAAU,cAAc,6BAA6B;sDAEpD,KAAK,KAAK;;;;;;;mCAZL,KAAK,EAAE;;;;;;;;;;sCAiBrB,8OAAC,qIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAK;gDAAS,cAAc;gDAAE;gDAAO,MAAM,MAAM;;;;;;;sDAClD,8OAAC;;gDAAM,KAAK,KAAK,CAAC;gDAAoB;;;;;;;;;;;;;8CAExC,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,OAAO;oCAAoB,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMrD,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;8BACT;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU,gBAAgB;;0CAE1B,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAI1C,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,MAAM,MAAM,GAAG,kBAC9B,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,sBAAsB;sCAEhC,eAAe,sBAAsB;;;;;iDAGxC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC;;gCACZ;8CAEC,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 10814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/utils/course-data-transformer.ts"], "sourcesContent": ["import { CourseData, ModuleData, ChapterData, QuizData, QuestionData } from '@/components/course/course-creation-wizard';\r\n\r\n// Type definitions for API response data\r\nexport interface ApiCourse {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  instructor?: string;\r\n  courseCode: string;\r\n  type: 'self_paced' | 'verified';\r\n  enrollmentType?: 'code' | 'invitation' | 'both' | 'purchase';\r\n  startDate?: string;\r\n  endDate?: string;\r\n  coverImage?: string;\r\n  coverPicture?: string;\r\n  isPurchasable?: boolean;\r\n  price?: number;\r\n  currency?: string;\r\n  previewMode?: boolean;\r\n  teacherId: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  modules: ApiModule[];\r\n  moduleQuizzes?: ApiQuiz[];\r\n  // Related course data\r\n  admissions?: {\r\n    requirements: string[];\r\n    applicationDeadline?: string;\r\n    prerequisites: string[];\r\n  };\r\n  academics?: {\r\n    credits: number;\r\n    workload: string;\r\n    assessment: string[];\r\n  };\r\n  tuitionAndFinancing?: {\r\n    totalCost: number;\r\n    paymentOptions: string[];\r\n    scholarships: string[];\r\n  };\r\n  careers?: {\r\n    outcomes: string[];\r\n    industries: string[];\r\n    averageSalary?: string;\r\n  };\r\n  studentExperience?: {\r\n    testimonials: { name: string; feedback: string }[];\r\n    facilities: string[];\r\n    support: string[];\r\n  };\r\n}\r\n\r\nexport interface ApiModule {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  orderIndex: number;\r\n  courseId: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  chapters: ApiChapter[];\r\n}\r\n\r\nexport interface ApiChapter {\r\n  id: number;\r\n  name: string;\r\n  content: string;\r\n  orderIndex: number;\r\n  moduleId: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  quizzes?: ApiQuiz[];\r\n}\r\n\r\nexport interface ApiQuiz {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  quizType: 'chapter' | 'module' | 'final';\r\n  timeLimit?: number;\r\n  minimumScore: string;\r\n  isActive: boolean;\r\n  chapterId?: number;\r\n  moduleId?: number;\r\n  courseId?: number;\r\n  teacherId: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  questions?: ApiQuestion[];\r\n}\r\n\r\nexport interface ApiQuestion {\r\n  id: number;\r\n  type: 'multiple_choice' | 'true_false' | 'essay';\r\n  question: any[]; // JSON content\r\n  options?: { content: any[]; isCorrect: boolean }[] | null; // JSON content\r\n  essayAnswer?: string | null; // Renamed from correctAnswer\r\n  explanation?: any[] | null; // New column\r\n  points: number;\r\n  orderIndex: number;\r\n  quizId: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\n/**\r\n * Transform API question data to CourseData question format\r\n */\r\nfunction transformQuestion(apiQuestion: ApiQuestion): QuestionData {\r\n  return {\r\n    id: apiQuestion.id.toString(),\r\n    type: apiQuestion.type,\r\n    question: apiQuestion.question || [{ type: 'text', value: '' }],\r\n    options: apiQuestion.options || undefined,\r\n    essayAnswer: apiQuestion.essayAnswer,\r\n    explanation: apiQuestion.explanation || undefined,\r\n    points: apiQuestion.points,\r\n    orderIndex: apiQuestion.orderIndex\r\n  };\r\n}\r\n\r\n/**\r\n * Transform API quiz data to CourseData quiz format\r\n */\r\nfunction transformQuiz(apiQuiz: ApiQuiz): QuizData {\r\n  return {\r\n    id: apiQuiz.id.toString(),\r\n    name: apiQuiz.name,\r\n    description: apiQuiz.description,\r\n    questions: apiQuiz.questions ? apiQuiz.questions.map(transformQuestion) : [],\r\n    timeLimit: apiQuiz.timeLimit,\r\n    minimumScore: parseInt(apiQuiz.minimumScore)\r\n  };\r\n}\r\n\r\n/**\r\n * Transform API chapter data to CourseData chapter format\r\n */\r\nfunction transformChapter(apiChapter: ApiChapter): ChapterData {\r\n  const chapterQuiz = apiChapter.quizzes?.find(quiz => quiz.quizType === 'chapter');\r\n\r\n  // Handle content - it might be a JSON string or already parsed object\r\n  let content = [];\r\n  if (apiChapter.content) {\r\n    if (typeof apiChapter.content === 'string') {\r\n      try {\r\n        content = JSON.parse(apiChapter.content);\r\n      } catch (error) {\r\n        console.error('Error parsing chapter content:', error);\r\n        content = [];\r\n      }\r\n    } else {\r\n      // Content is already an object/array\r\n      content = apiChapter.content;\r\n    }\r\n  }\r\n\r\n  return {\r\n    id: apiChapter.id.toString(),\r\n    name: apiChapter.name,\r\n    content: content,\r\n    orderIndex: apiChapter.orderIndex,\r\n    hasChapterQuiz: !!chapterQuiz,\r\n    chapterQuiz: chapterQuiz ? transformQuiz(chapterQuiz) : undefined\r\n  };\r\n}\r\n\r\n/**\r\n * Transform API module data to CourseData module format\r\n * FIXED: Now properly handles module quizzes from the moduleQuizzes array\r\n */\r\nfunction transformModule(apiModule: ApiModule, allQuizzes: ApiQuiz[] = []): ModuleData {\r\n  // Find module quiz specifically for this module\r\n  const moduleQuiz = allQuizzes.find(quiz => \r\n    quiz.moduleId === apiModule.id && quiz.quizType === 'module'\r\n  );\r\n  \r\n  console.log(`Module ${apiModule.id}: Found module quiz:`, moduleQuiz); // Debug log\r\n  \r\n  return {\r\n    id: apiModule.id.toString(),\r\n    name: apiModule.name,\r\n    description: apiModule.description,\r\n    orderIndex: apiModule.orderIndex,\r\n    chapters: apiModule.chapters.map(transformChapter),\r\n    hasModuleQuiz: !!moduleQuiz,\r\n    moduleQuiz: moduleQuiz ? transformQuiz(moduleQuiz) : undefined\r\n  };\r\n}\r\n\r\n/**\r\n * Transform API course data to CourseData format for the wizard\r\n * FIXED: Better handling of quizzes and debugging\r\n */\r\nexport function transformApiCourseToWizardData(apiCourse: ApiCourse, moduleQuizzes: ApiQuiz[] = [], finalExam?: ApiQuiz): CourseData {\r\n  console.log('Transforming API course data:', {\r\n    courseId: apiCourse.id,\r\n    moduleQuizzesCount: moduleQuizzes.length,\r\n    finalExam: finalExam ? finalExam.id : 'none',\r\n    allQuizzes: moduleQuizzes.map(q => ({ id: q.id, type: q.quizType, moduleId: q.moduleId, courseId: q.courseId }))\r\n  });\r\n\r\n  // Filter quizzes properly\r\n  const actualModuleQuizzes = moduleQuizzes.filter(quiz => quiz.quizType === 'module');\r\n  const actualFinalExam = finalExam || moduleQuizzes.find(quiz => quiz.quizType === 'final');\r\n\r\n  console.log('Filtered quizzes:', {\r\n    moduleQuizzes: actualModuleQuizzes.length,\r\n    finalExam: actualFinalExam ? actualFinalExam.id : 'none'\r\n  });\r\n\r\n  return {\r\n    name: apiCourse.name,\r\n    description: apiCourse.description,\r\n    instructor: apiCourse.instructor || '', \r\n    courseCode: apiCourse.courseCode,\r\n    type: apiCourse.type,\r\n    enrollmentType: apiCourse.enrollmentType || 'code', \r\n    startDate: apiCourse.startDate ? new Date(apiCourse.startDate) : undefined,\r\n    endDate: apiCourse.endDate ? new Date(apiCourse.endDate) : undefined,\r\n    coverImagePreview: apiCourse.coverImage || apiCourse.coverPicture,\r\n    isPurchasable: apiCourse.isPurchasable,\r\n    price: apiCourse.price,\r\n    currency: apiCourse.currency,\r\n    previewMode: apiCourse.previewMode,\r\n    modules: apiCourse.modules.map(module => transformModule(module, moduleQuizzes)), // Pass ALL quizzes\r\n    isPublished: true, \r\n    assignedClasses: [], \r\n    finalExam: actualFinalExam ? transformQuiz(actualFinalExam) : undefined,\r\n    // Add related course data\r\n    admissions: apiCourse.admissions ? {\r\n      requirements: apiCourse.admissions.requirements || [],\r\n      applicationDeadline: apiCourse.admissions.applicationDeadline || '',\r\n      prerequisites: apiCourse.admissions.prerequisites || []\r\n    } : undefined,\r\n    academics: apiCourse.academics ? {\r\n      credits: apiCourse.academics.credits || 0,\r\n      workload: apiCourse.academics.workload || '',\r\n      assessment: apiCourse.academics.assessment || []\r\n    } : undefined,\r\n    tuitionAndFinancing: apiCourse.tuitionAndFinancing ? {\r\n      totalCost: apiCourse.tuitionAndFinancing.totalCost || 0,\r\n      paymentOptions: apiCourse.tuitionAndFinancing.paymentOptions || [],\r\n      scholarships: apiCourse.tuitionAndFinancing.scholarships || []\r\n    } : undefined,\r\n    careers: apiCourse.careers ? {\r\n      outcomes: apiCourse.careers.outcomes || [],\r\n      industries: apiCourse.careers.industries || [],\r\n      averageSalary: apiCourse.careers.averageSalary || ''\r\n    } : undefined,\r\n    studentExperience: apiCourse.studentExperience ? {\r\n      testimonials: apiCourse.studentExperience.testimonials || [],\r\n      facilities: apiCourse.studentExperience.facilities || [],\r\n      support: apiCourse.studentExperience.support || []\r\n    } : undefined\r\n  };\r\n}\r\n\r\n/**\r\n * Transform CourseData back to API format for updates\r\n * FIXED: Better handling of quiz transformations and ID parsing\r\n */\r\nexport function transformWizardDataToApiUpdate(courseData: CourseData, courseId: string) {\r\n  console.log('Transforming wizard data to API update:', {\r\n    courseId,\r\n    modulesCount: courseData.modules.length,\r\n    finalExamExists: !!courseData.finalExam,\r\n    moduleQuizzes: courseData.modules.filter(m => m.hasModuleQuiz).length\r\n  });\r\n\r\n  return {\r\n    courseId: parseInt(courseId),\r\n    name: courseData.name,\r\n    description: courseData.description,\r\n    courseCode: courseData.courseCode,\r\n    type: courseData.type,\r\n    enrollmentType: courseData.enrollmentType,\r\n    price: courseData.price,\r\n    currency: courseData.currency,\r\n    isPurchasable: courseData.enrollmentType === 'purchase' || courseData.enrollmentType === 'both',\r\n    startDate: courseData.startDate?.toISOString(),\r\n    endDate: courseData.endDate?.toISOString(),\r\n    modules: courseData.modules.map(module => {\r\n      console.log(`Transforming module ${module.name}:`, {\r\n        id: module.id,\r\n        hasModuleQuiz: module.hasModuleQuiz,\r\n        moduleQuizId: module.moduleQuiz?.id\r\n      });\r\n\r\n      return {\r\n        id: module.id && !isNaN(parseInt(module.id)) ? parseInt(module.id) : undefined,\r\n        name: module.name,\r\n        description: module.description,\r\n        orderIndex: module.orderIndex,\r\n        chapters: module.chapters.map(chapter => ({\r\n          id: chapter.id && !isNaN(parseInt(chapter.id)) ? parseInt(chapter.id) : undefined,\r\n          name: chapter.name,\r\n          content: chapter.content, \r\n          orderIndex: chapter.orderIndex,\r\n          quiz: chapter.hasChapterQuiz && chapter.chapterQuiz ? {\r\n            id: chapter.chapterQuiz.id && !isNaN(parseInt(chapter.chapterQuiz.id)) ? parseInt(chapter.chapterQuiz.id) : undefined,\r\n            name: chapter.chapterQuiz.name,\r\n            description: chapter.chapterQuiz.description,\r\n            quizType: 'chapter' as const,\r\n            timeLimit: chapter.chapterQuiz.timeLimit,\r\n            minimumScore: chapter.chapterQuiz.minimumScore,\r\n            questions: chapter.chapterQuiz.questions.map(question => ({\r\n              id: question.id && !isNaN(parseInt(question.id)) ? parseInt(question.id) : undefined,\r\n              type: question.type,\r\n              question: question.question, \r\n              options: question.options || null,\r\n              essayAnswer: question.type === 'essay' ? question.essayAnswer : null,\r\n              explanation: question.explanation || null,\r\n              points: question.points,\r\n              orderIndex: question.orderIndex\r\n            }))\r\n          } : undefined\r\n        })),\r\n        quiz: module.hasModuleQuiz && module.moduleQuiz ? {\r\n          id: module.moduleQuiz.id && !isNaN(parseInt(module.moduleQuiz.id)) ? parseInt(module.moduleQuiz.id) : undefined,\r\n          name: module.moduleQuiz.name,\r\n          description: module.moduleQuiz.description,\r\n          quizType: 'module' as const,\r\n          timeLimit: module.moduleQuiz.timeLimit,\r\n          minimumScore: module.moduleQuiz.minimumScore,\r\n          questions: module.moduleQuiz.questions.map(question => ({\r\n            id: question.id && !isNaN(parseInt(question.id)) ? parseInt(question.id) : undefined,\r\n            type: question.type,\r\n            question: question.question, \r\n            options: question.options || null,\r\n            essayAnswer: question.type === 'essay' ? question.essayAnswer : null,\r\n            explanation: question.explanation || null,\r\n            points: question.points,\r\n            orderIndex: question.orderIndex\r\n          }))\r\n        } : undefined\r\n      };\r\n    }),\r\n    finalExam: courseData.finalExam ? {\r\n      id: courseData.finalExam.id && !isNaN(parseInt(courseData.finalExam.id)) ? parseInt(courseData.finalExam.id) : undefined,\r\n      name: courseData.finalExam.name,\r\n      description: courseData.finalExam.description,\r\n      quizType: 'final' as const,\r\n      timeLimit: courseData.finalExam.timeLimit,\r\n      minimumScore: courseData.finalExam.minimumScore,\r\n      questions: courseData.finalExam.questions.map(question => ({\r\n        id: question.id && !isNaN(parseInt(question.id)) ? parseInt(question.id) : undefined,\r\n        type: question.type,\r\n        question: question.question, \r\n        options: question.options || null,\r\n        essayAnswer: question.type === 'essay' ? question.essayAnswer : null,\r\n        explanation: question.explanation || null,\r\n        points: question.points,\r\n        orderIndex: question.orderIndex\r\n      }))\r\n    } : undefined,\r\n    // Add related course data\r\n    admissions: courseData.admissions,\r\n    academics: courseData.academics,\r\n    tuitionAndFinancing: courseData.tuitionAndFinancing,\r\n    careers: courseData.careers,\r\n    studentExperience: courseData.studentExperience\r\n  };\r\n}"], "names": [], "mappings": ";;;;AAyGA;;CAEC,GACD,SAAS,kBAAkB,WAAwB;IACjD,OAAO;QACL,IAAI,YAAY,EAAE,CAAC,QAAQ;QAC3B,MAAM,YAAY,IAAI;QACtB,UAAU,YAAY,QAAQ,IAAI;YAAC;gBAAE,MAAM;gBAAQ,OAAO;YAAG;SAAE;QAC/D,SAAS,YAAY,OAAO,IAAI;QAChC,aAAa,YAAY,WAAW;QACpC,aAAa,YAAY,WAAW,IAAI;QACxC,QAAQ,YAAY,MAAM;QAC1B,YAAY,YAAY,UAAU;IACpC;AACF;AAEA;;CAEC,GACD,SAAS,cAAc,OAAgB;IACrC,OAAO;QACL,IAAI,QAAQ,EAAE,CAAC,QAAQ;QACvB,MAAM,QAAQ,IAAI;QAClB,aAAa,QAAQ,WAAW;QAChC,WAAW,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,GAAG,CAAC,qBAAqB,EAAE;QAC5E,WAAW,QAAQ,SAAS;QAC5B,cAAc,SAAS,QAAQ,YAAY;IAC7C;AACF;AAEA;;CAEC,GACD,SAAS,iBAAiB,UAAsB;IAC9C,MAAM,cAAc,WAAW,OAAO,EAAE,KAAK,CAAA,OAAQ,KAAK,QAAQ,KAAK;IAEvE,sEAAsE;IACtE,IAAI,UAAU,EAAE;IAChB,IAAI,WAAW,OAAO,EAAE;QACtB,IAAI,OAAO,WAAW,OAAO,KAAK,UAAU;YAC1C,IAAI;gBACF,UAAU,KAAK,KAAK,CAAC,WAAW,OAAO;YACzC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,UAAU,EAAE;YACd;QACF,OAAO;YACL,qCAAqC;YACrC,UAAU,WAAW,OAAO;QAC9B;IACF;IAEA,OAAO;QACL,IAAI,WAAW,EAAE,CAAC,QAAQ;QAC1B,MAAM,WAAW,IAAI;QACrB,SAAS;QACT,YAAY,WAAW,UAAU;QACjC,gBAAgB,CAAC,CAAC;QAClB,aAAa,cAAc,cAAc,eAAe;IAC1D;AACF;AAEA;;;CAGC,GACD,SAAS,gBAAgB,SAAoB,EAAE,aAAwB,EAAE;IACvE,gDAAgD;IAChD,MAAM,aAAa,WAAW,IAAI,CAAC,CAAA,OACjC,KAAK,QAAQ,KAAK,UAAU,EAAE,IAAI,KAAK,QAAQ,KAAK;IAGtD,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,oBAAoB,CAAC,EAAE,aAAa,YAAY;IAEnF,OAAO;QACL,IAAI,UAAU,EAAE,CAAC,QAAQ;QACzB,MAAM,UAAU,IAAI;QACpB,aAAa,UAAU,WAAW;QAClC,YAAY,UAAU,UAAU;QAChC,UAAU,UAAU,QAAQ,CAAC,GAAG,CAAC;QACjC,eAAe,CAAC,CAAC;QACjB,YAAY,aAAa,cAAc,cAAc;IACvD;AACF;AAMO,SAAS,+BAA+B,SAAoB,EAAE,gBAA2B,EAAE,EAAE,SAAmB;IACrH,QAAQ,GAAG,CAAC,iCAAiC;QAC3C,UAAU,UAAU,EAAE;QACtB,oBAAoB,cAAc,MAAM;QACxC,WAAW,YAAY,UAAU,EAAE,GAAG;QACtC,YAAY,cAAc,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,IAAI,EAAE,EAAE;gBAAE,MAAM,EAAE,QAAQ;gBAAE,UAAU,EAAE,QAAQ;gBAAE,UAAU,EAAE,QAAQ;YAAC,CAAC;IAChH;IAEA,0BAA0B;IAC1B,MAAM,sBAAsB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IAC3E,MAAM,kBAAkB,aAAa,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IAElF,QAAQ,GAAG,CAAC,qBAAqB;QAC/B,eAAe,oBAAoB,MAAM;QACzC,WAAW,kBAAkB,gBAAgB,EAAE,GAAG;IACpD;IAEA,OAAO;QACL,MAAM,UAAU,IAAI;QACpB,aAAa,UAAU,WAAW;QAClC,YAAY,UAAU,UAAU,IAAI;QACpC,YAAY,UAAU,UAAU;QAChC,MAAM,UAAU,IAAI;QACpB,gBAAgB,UAAU,cAAc,IAAI;QAC5C,WAAW,UAAU,SAAS,GAAG,IAAI,KAAK,UAAU,SAAS,IAAI;QACjE,SAAS,UAAU,OAAO,GAAG,IAAI,KAAK,UAAU,OAAO,IAAI;QAC3D,mBAAmB,UAAU,UAAU,IAAI,UAAU,YAAY;QACjE,eAAe,UAAU,aAAa;QACtC,OAAO,UAAU,KAAK;QACtB,UAAU,UAAU,QAAQ;QAC5B,aAAa,UAAU,WAAW;QAClC,SAAS,UAAU,OAAO,CAAC,GAAG,CAAC,CAAA,SAAU,gBAAgB,QAAQ;QACjE,aAAa;QACb,iBAAiB,EAAE;QACnB,WAAW,kBAAkB,cAAc,mBAAmB;QAC9D,0BAA0B;QAC1B,YAAY,UAAU,UAAU,GAAG;YACjC,cAAc,UAAU,UAAU,CAAC,YAAY,IAAI,EAAE;YACrD,qBAAqB,UAAU,UAAU,CAAC,mBAAmB,IAAI;YACjE,eAAe,UAAU,UAAU,CAAC,aAAa,IAAI,EAAE;QACzD,IAAI;QACJ,WAAW,UAAU,SAAS,GAAG;YAC/B,SAAS,UAAU,SAAS,CAAC,OAAO,IAAI;YACxC,UAAU,UAAU,SAAS,CAAC,QAAQ,IAAI;YAC1C,YAAY,UAAU,SAAS,CAAC,UAAU,IAAI,EAAE;QAClD,IAAI;QACJ,qBAAqB,UAAU,mBAAmB,GAAG;YACnD,WAAW,UAAU,mBAAmB,CAAC,SAAS,IAAI;YACtD,gBAAgB,UAAU,mBAAmB,CAAC,cAAc,IAAI,EAAE;YAClE,cAAc,UAAU,mBAAmB,CAAC,YAAY,IAAI,EAAE;QAChE,IAAI;QACJ,SAAS,UAAU,OAAO,GAAG;YAC3B,UAAU,UAAU,OAAO,CAAC,QAAQ,IAAI,EAAE;YAC1C,YAAY,UAAU,OAAO,CAAC,UAAU,IAAI,EAAE;YAC9C,eAAe,UAAU,OAAO,CAAC,aAAa,IAAI;QACpD,IAAI;QACJ,mBAAmB,UAAU,iBAAiB,GAAG;YAC/C,cAAc,UAAU,iBAAiB,CAAC,YAAY,IAAI,EAAE;YAC5D,YAAY,UAAU,iBAAiB,CAAC,UAAU,IAAI,EAAE;YACxD,SAAS,UAAU,iBAAiB,CAAC,OAAO,IAAI,EAAE;QACpD,IAAI;IACN;AACF;AAMO,SAAS,+BAA+B,UAAsB,EAAE,QAAgB;IACrF,QAAQ,GAAG,CAAC,2CAA2C;QACrD;QACA,cAAc,WAAW,OAAO,CAAC,MAAM;QACvC,iBAAiB,CAAC,CAAC,WAAW,SAAS;QACvC,eAAe,WAAW,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,EAAE,MAAM;IACvE;IAEA,OAAO;QACL,UAAU,SAAS;QACnB,MAAM,WAAW,IAAI;QACrB,aAAa,WAAW,WAAW;QACnC,YAAY,WAAW,UAAU;QACjC,MAAM,WAAW,IAAI;QACrB,gBAAgB,WAAW,cAAc;QACzC,OAAO,WAAW,KAAK;QACvB,UAAU,WAAW,QAAQ;QAC7B,eAAe,WAAW,cAAc,KAAK,cAAc,WAAW,cAAc,KAAK;QACzF,WAAW,WAAW,SAAS,EAAE;QACjC,SAAS,WAAW,OAAO,EAAE;QAC7B,SAAS,WAAW,OAAO,CAAC,GAAG,CAAC,CAAA;YAC9B,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE;gBACjD,IAAI,OAAO,EAAE;gBACb,eAAe,OAAO,aAAa;gBACnC,cAAc,OAAO,UAAU,EAAE;YACnC;YAEA,OAAO;gBACL,IAAI,OAAO,EAAE,IAAI,CAAC,MAAM,SAAS,OAAO,EAAE,KAAK,SAAS,OAAO,EAAE,IAAI;gBACrE,MAAM,OAAO,IAAI;gBACjB,aAAa,OAAO,WAAW;gBAC/B,YAAY,OAAO,UAAU;gBAC7B,UAAU,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;wBACxC,IAAI,QAAQ,EAAE,IAAI,CAAC,MAAM,SAAS,QAAQ,EAAE,KAAK,SAAS,QAAQ,EAAE,IAAI;wBACxE,MAAM,QAAQ,IAAI;wBAClB,SAAS,QAAQ,OAAO;wBACxB,YAAY,QAAQ,UAAU;wBAC9B,MAAM,QAAQ,cAAc,IAAI,QAAQ,WAAW,GAAG;4BACpD,IAAI,QAAQ,WAAW,CAAC,EAAE,IAAI,CAAC,MAAM,SAAS,QAAQ,WAAW,CAAC,EAAE,KAAK,SAAS,QAAQ,WAAW,CAAC,EAAE,IAAI;4BAC5G,MAAM,QAAQ,WAAW,CAAC,IAAI;4BAC9B,aAAa,QAAQ,WAAW,CAAC,WAAW;4BAC5C,UAAU;4BACV,WAAW,QAAQ,WAAW,CAAC,SAAS;4BACxC,cAAc,QAAQ,WAAW,CAAC,YAAY;4BAC9C,WAAW,QAAQ,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,WAAY,CAAC;oCACxD,IAAI,SAAS,EAAE,IAAI,CAAC,MAAM,SAAS,SAAS,EAAE,KAAK,SAAS,SAAS,EAAE,IAAI;oCAC3E,MAAM,SAAS,IAAI;oCACnB,UAAU,SAAS,QAAQ;oCAC3B,SAAS,SAAS,OAAO,IAAI;oCAC7B,aAAa,SAAS,IAAI,KAAK,UAAU,SAAS,WAAW,GAAG;oCAChE,aAAa,SAAS,WAAW,IAAI;oCACrC,QAAQ,SAAS,MAAM;oCACvB,YAAY,SAAS,UAAU;gCACjC,CAAC;wBACH,IAAI;oBACN,CAAC;gBACD,MAAM,OAAO,aAAa,IAAI,OAAO,UAAU,GAAG;oBAChD,IAAI,OAAO,UAAU,CAAC,EAAE,IAAI,CAAC,MAAM,SAAS,OAAO,UAAU,CAAC,EAAE,KAAK,SAAS,OAAO,UAAU,CAAC,EAAE,IAAI;oBACtG,MAAM,OAAO,UAAU,CAAC,IAAI;oBAC5B,aAAa,OAAO,UAAU,CAAC,WAAW;oBAC1C,UAAU;oBACV,WAAW,OAAO,UAAU,CAAC,SAAS;oBACtC,cAAc,OAAO,UAAU,CAAC,YAAY;oBAC5C,WAAW,OAAO,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,WAAY,CAAC;4BACtD,IAAI,SAAS,EAAE,IAAI,CAAC,MAAM,SAAS,SAAS,EAAE,KAAK,SAAS,SAAS,EAAE,IAAI;4BAC3E,MAAM,SAAS,IAAI;4BACnB,UAAU,SAAS,QAAQ;4BAC3B,SAAS,SAAS,OAAO,IAAI;4BAC7B,aAAa,SAAS,IAAI,KAAK,UAAU,SAAS,WAAW,GAAG;4BAChE,aAAa,SAAS,WAAW,IAAI;4BACrC,QAAQ,SAAS,MAAM;4BACvB,YAAY,SAAS,UAAU;wBACjC,CAAC;gBACH,IAAI;YACN;QACF;QACA,WAAW,WAAW,SAAS,GAAG;YAChC,IAAI,WAAW,SAAS,CAAC,EAAE,IAAI,CAAC,MAAM,SAAS,WAAW,SAAS,CAAC,EAAE,KAAK,SAAS,WAAW,SAAS,CAAC,EAAE,IAAI;YAC/G,MAAM,WAAW,SAAS,CAAC,IAAI;YAC/B,aAAa,WAAW,SAAS,CAAC,WAAW;YAC7C,UAAU;YACV,WAAW,WAAW,SAAS,CAAC,SAAS;YACzC,cAAc,WAAW,SAAS,CAAC,YAAY;YAC/C,WAAW,WAAW,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,WAAY,CAAC;oBACzD,IAAI,SAAS,EAAE,IAAI,CAAC,MAAM,SAAS,SAAS,EAAE,KAAK,SAAS,SAAS,EAAE,IAAI;oBAC3E,MAAM,SAAS,IAAI;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,SAAS,SAAS,OAAO,IAAI;oBAC7B,aAAa,SAAS,IAAI,KAAK,UAAU,SAAS,WAAW,GAAG;oBAChE,aAAa,SAAS,WAAW,IAAI;oBACrC,QAAQ,SAAS,MAAM;oBACvB,YAAY,SAAS,UAAU;gBACjC,CAAC;QACH,IAAI;QACJ,0BAA0B;QAC1B,YAAY,WAAW,UAAU;QACjC,WAAW,WAAW,SAAS;QAC/B,qBAAqB,WAAW,mBAAmB;QACnD,SAAS,WAAW,OAAO;QAC3B,mBAAmB,WAAW,iBAAiB;IACjD;AACF", "debugId": null}}, {"offset": {"line": 11065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/app/dashboard/teacher/courses/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, use } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { ArrowLeft, AlertCircle } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport { authStorage } from '@/lib/auth';\r\nimport { toast } from 'sonner';\r\nimport { CourseCreationWizard, CourseData } from '@/components/course/course-creation-wizard';\r\nimport { transformApiCourseToWizardData, transformWizardDataToApiUpdate, ApiCourse, ApiModule, ApiChapter, ApiQuiz, ApiQuestion } from '@/utils/course-data-transformer';\r\n\r\nexport default function CourseEditPage({\r\n  params\r\n}: {\r\n  params: Promise<{ id: string }>;\r\n}) {\r\n  const { id } = use(params);\r\n  const router = useRouter();\r\n  \r\n  const [courseData, setCourseData] = useState<CourseData | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (id) {\r\n      fetchCourseData();\r\n    }\r\n  }, [id]);\r\n\r\n  const fetchCourseData = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n      \r\n      const user = authStorage.getUser();\r\n      if (!user) {\r\n        setError('Please log in to edit course');\r\n        return;\r\n      }\r\n\r\n      // Fetch course data\r\n      const response = await fetch(`/api/courses/${id}?teacherId=${user.id}`);\r\n      const data = await response.json();\r\n\r\n      if (!response.ok) {\r\n        throw new Error(data.error || 'Failed to fetch course data');\r\n      }\r\n\r\n      if (!data.success || !data.course) {\r\n        throw new Error('Course not found');\r\n      }\r\n\r\n      const apiCourse: ApiCourse = data.course;\r\n      console.log('Fetched course data:', apiCourse); // Debug log\r\n\r\n      // Extract module quizzes and final exam from API response\r\n      const moduleQuizzes: ApiQuiz[] = apiCourse.moduleQuizzes || [];\r\n      const finalExam: ApiQuiz | undefined = moduleQuizzes.find(quiz => quiz.quizType === 'final');\r\n      const actualModuleQuizzes = moduleQuizzes.filter(quiz => quiz.quizType === 'module');\r\n\r\n      console.log('Quiz breakdown:', {\r\n        totalQuizzes: moduleQuizzes.length,\r\n        moduleQuizzes: actualModuleQuizzes.length,\r\n        finalExam: finalExam ? finalExam.id : 'none',\r\n        quizzes: moduleQuizzes.map(q => ({ id: q.id, type: q.quizType, moduleId: q.moduleId, courseId: q.courseId }))\r\n      });\r\n\r\n      // Transform API data to wizard format\r\n      const transformedData = transformApiCourseToWizardData(apiCourse, moduleQuizzes, finalExam);\r\n      console.log('Transformed data:', transformedData); // Debug log\r\n      setCourseData(transformedData);\r\n      \r\n    } catch (error) {\r\n      console.error('Error fetching course:', error);\r\n      setError(error instanceof Error ? error.message : 'Failed to fetch course data');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCourseUpdate = async (updatedCourseData: CourseData) => {\r\n    try {\r\n      setIsUpdating(true);\r\n      console.log('Starting course update with data:', updatedCourseData); // Debug log\r\n      \r\n      const user = authStorage.getUser();\r\n      if (!user) {\r\n        toast.error('Please log in to update course');\r\n        return;\r\n      }\r\n\r\n      // Transform wizard data back to API format\r\n      const updateData = transformWizardDataToApiUpdate(updatedCourseData, id);\r\n      console.log('Update data prepared:', updateData); // Debug log\r\n      \r\n      // Update course basic info\r\n      const courseResponse = await fetch(`/api/courses/${id}`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({\r\n          name: updateData.name,\r\n          description: updateData.description,\r\n          courseCode: updateData.courseCode,\r\n          type: updateData.type,\r\n          enrollmentType: updateData.enrollmentType,\r\n          price: updateData.price,\r\n          currency: updateData.currency,\r\n          isPurchasable: updateData.isPurchasable,\r\n          startDate: updateData.startDate,\r\n          endDate: updateData.endDate,\r\n          teacherId: user.id,\r\n          // Add related course data\r\n          admissions: updateData.admissions,\r\n          academics: updateData.academics,\r\n          tuitionAndFinancing: updateData.tuitionAndFinancing,\r\n          careers: updateData.careers,\r\n          studentExperience: updateData.studentExperience\r\n        })\r\n      });\r\n\r\n      if (!courseResponse.ok) {\r\n        const errorData = await courseResponse.json();\r\n        throw new Error(errorData.error || 'Failed to update course');\r\n      }\r\n\r\n      // Process modules, chapters, and quizzes\r\n      for (const moduleData of updateData.modules) {\r\n        console.log(`Processing module: ${moduleData.name}`, {\r\n          id: moduleData.id,\r\n          hasModuleQuiz: !!moduleData.quiz,\r\n          moduleQuizId: moduleData.quiz?.id\r\n        });\r\n\r\n        let currentModuleId = moduleData.id;\r\n\r\n        if (moduleData.id) {\r\n          // Update existing module\r\n          const moduleResponse = await fetch(`/api/modules/${moduleData.id}`, {\r\n            method: 'PUT',\r\n            headers: { 'Content-Type': 'application/json' },\r\n            body: JSON.stringify({\r\n              name: moduleData.name,\r\n              description: moduleData.description,\r\n              orderIndex: moduleData.orderIndex,\r\n              teacherId: user.id\r\n            })\r\n          });\r\n\r\n          if (!moduleResponse.ok) {\r\n            console.error('Failed to update module:', moduleData.id);\r\n          }\r\n        } else {\r\n          // Create new module\r\n          const newModuleResponse = await fetch('/api/modules', {\r\n            method: 'POST',\r\n            headers: { 'Content-Type': 'application/json' },\r\n            body: JSON.stringify({\r\n              name: moduleData.name,\r\n              description: moduleData.description,\r\n              orderIndex: moduleData.orderIndex,\r\n              courseId: parseInt(id),\r\n              teacherId: user.id\r\n            })\r\n          });\r\n          \r\n          if (newModuleResponse.ok) {\r\n            const newModule = await newModuleResponse.json();\r\n            currentModuleId = newModule.module.id;\r\n            console.log('Created new module with ID:', currentModuleId);\r\n          }\r\n        }\r\n\r\n        // Update chapters\r\n        for (const chapter of moduleData.chapters) {\r\n          let currentChapterId = chapter.id;\r\n\r\n          if (chapter.id) {\r\n            // Update existing chapter\r\n            const chapterResponse = await fetch(`/api/chapters/${chapter.id}`, {\r\n              method: 'PUT',\r\n              headers: { 'Content-Type': 'application/json' },\r\n              body: JSON.stringify({\r\n                name: chapter.name,\r\n                content: chapter.content,\r\n                orderIndex: chapter.orderIndex,\r\n                teacherId: user.id\r\n              })\r\n            });\r\n\r\n            if (!chapterResponse.ok) {\r\n              console.error('Failed to update chapter:', chapter.id);\r\n            }\r\n          } else {\r\n            // Create new chapter\r\n            const newChapterResponse = await fetch('/api/chapters', {\r\n              method: 'POST',\r\n              headers: { 'Content-Type': 'application/json' },\r\n              body: JSON.stringify({\r\n                name: chapter.name,\r\n                content: chapter.content,\r\n                orderIndex: chapter.orderIndex,\r\n                moduleId: currentModuleId,\r\n                teacherId: user.id\r\n              })\r\n            });\r\n            \r\n            if (newChapterResponse.ok) {\r\n              const newChapter = await newChapterResponse.json();\r\n              currentChapterId = newChapter.chapter.id;\r\n              console.log('Created new chapter with ID:', currentChapterId);\r\n            }\r\n          }\r\n\r\n          // Handle chapter quiz\r\n          if (chapter.quiz) {\r\n            console.log(`Processing chapter quiz for chapter ${currentChapterId}:`, chapter.quiz.id);\r\n\r\n            if (chapter.quiz.id) {\r\n              // Update existing chapter quiz\r\n              const quizResponse = await fetch(`/api/quizzes/${chapter.quiz.id}`, {\r\n                method: 'PUT',\r\n                headers: { 'Content-Type': 'application/json' },\r\n                body: JSON.stringify({\r\n                  name: chapter.quiz.name,\r\n                  description: chapter.quiz.description,\r\n                  quizType: 'chapter',\r\n                  timeLimit: chapter.quiz.timeLimit,\r\n                  minimumScore: chapter.quiz.minimumScore,\r\n                  teacherId: user.id,\r\n                  questions: chapter.quiz.questions\r\n                })\r\n              });\r\n\r\n              if (!quizResponse.ok) {\r\n                console.error('Failed to update chapter quiz:', chapter.quiz.id);\r\n                const errorData = await quizResponse.json();\r\n                console.error('Chapter quiz update error:', errorData);\r\n              }\r\n            } else {\r\n              // Create new chapter quiz\r\n              const quizResponse = await fetch('/api/quizzes', {\r\n                method: 'POST',\r\n                headers: { 'Content-Type': 'application/json' },\r\n                body: JSON.stringify({\r\n                  name: chapter.quiz.name,\r\n                  description: chapter.quiz.description,\r\n                  quizType: 'chapter',\r\n                  timeLimit: chapter.quiz.timeLimit,\r\n                  minimumScore: chapter.quiz.minimumScore,\r\n                  chapterId: currentChapterId,\r\n                  teacherId: user.id,\r\n                  questions: chapter.quiz.questions\r\n                })\r\n              });\r\n\r\n              if (!quizResponse.ok) {\r\n                console.error('Failed to create chapter quiz');\r\n                const errorData = await quizResponse.json();\r\n                console.error('Chapter quiz creation error:', errorData);\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // Handle module quiz - FIXED: Proper handling\r\n        if (moduleData.quiz) {\r\n          console.log(`Processing module quiz for module ${currentModuleId}:`, {\r\n            quizId: moduleData.quiz.id,\r\n            quizName: moduleData.quiz.name,\r\n            questionsCount: moduleData.quiz.questions.length\r\n          });\r\n\r\n          if (moduleData.quiz.id) {\r\n            // Update existing module quiz\r\n            const quizResponse = await fetch(`/api/quizzes/${moduleData.quiz.id}`, {\r\n              method: 'PUT',\r\n              headers: { 'Content-Type': 'application/json' },\r\n              body: JSON.stringify({\r\n                name: moduleData.quiz.name,\r\n                description: moduleData.quiz.description,\r\n                quizType: 'module',\r\n                timeLimit: moduleData.quiz.timeLimit,\r\n                minimumScore: moduleData.quiz.minimumScore,\r\n                moduleId: currentModuleId, // Ensure moduleId is set\r\n                teacherId: user.id,\r\n                questions: moduleData.quiz.questions\r\n              })\r\n            });\r\n\r\n            if (!quizResponse.ok) {\r\n              console.error('Failed to update module quiz:', moduleData.quiz.id);\r\n              const errorData = await quizResponse.json();\r\n              console.error('Module quiz update error:', errorData);\r\n            } else {\r\n              console.log('Successfully updated module quiz:', moduleData.quiz.id);\r\n            }\r\n          } else {\r\n            // Create new module quiz\r\n            const quizResponse = await fetch('/api/quizzes', {\r\n              method: 'POST',\r\n              headers: { 'Content-Type': 'application/json' },\r\n              body: JSON.stringify({\r\n                name: moduleData.quiz.name,\r\n                description: moduleData.quiz.description,\r\n                quizType: 'module',\r\n                timeLimit: moduleData.quiz.timeLimit,\r\n                minimumScore: moduleData.quiz.minimumScore,\r\n                moduleId: currentModuleId,\r\n                teacherId: user.id,\r\n                questions: moduleData.quiz.questions\r\n              })\r\n            });\r\n\r\n            if (!quizResponse.ok) {\r\n              console.error('Failed to create module quiz');\r\n              const errorData = await quizResponse.json();\r\n              console.error('Module quiz creation error:', errorData);\r\n            } else {\r\n              const newQuiz = await quizResponse.json();\r\n              console.log('Successfully created module quiz:', newQuiz.quiz?.id);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // Handle final exam - FIXED: Proper handling\r\n      if (updateData.finalExam) {\r\n        console.log('Processing final exam:', {\r\n          examId: updateData.finalExam.id,\r\n          examName: updateData.finalExam.name,\r\n          questionsCount: updateData.finalExam.questions.length\r\n        });\r\n\r\n        if (updateData.finalExam.id) {\r\n          // Update existing final exam\r\n          const examResponse = await fetch(`/api/quizzes/${updateData.finalExam.id}`, {\r\n            method: 'PUT',\r\n            headers: { 'Content-Type': 'application/json' },\r\n            body: JSON.stringify({\r\n              name: updateData.finalExam.name,\r\n              description: updateData.finalExam.description,\r\n              quizType: 'final',\r\n              timeLimit: updateData.finalExam.timeLimit,\r\n              minimumScore: updateData.finalExam.minimumScore,\r\n              courseId: parseInt(id), // Ensure courseId is set\r\n              teacherId: user.id,\r\n              questions: updateData.finalExam.questions\r\n            })\r\n          });\r\n\r\n          if (!examResponse.ok) {\r\n            console.error('Failed to update final exam:', updateData.finalExam.id);\r\n            const errorData = await examResponse.json();\r\n            console.error('Final exam update error:', errorData);\r\n          } else {\r\n            console.log('Successfully updated final exam:', updateData.finalExam.id);\r\n          }\r\n        } else {\r\n          // Create new final exam\r\n          const examResponse = await fetch('/api/quizzes', {\r\n            method: 'POST',\r\n            headers: { 'Content-Type': 'application/json' },\r\n            body: JSON.stringify({\r\n              name: updateData.finalExam.name,\r\n              description: updateData.finalExam.description,\r\n              quizType: 'final',\r\n              timeLimit: updateData.finalExam.timeLimit,\r\n              minimumScore: updateData.finalExam.minimumScore,\r\n              courseId: parseInt(id),\r\n              teacherId: user.id,\r\n              questions: updateData.finalExam.questions\r\n            })\r\n          });\r\n\r\n          if (!examResponse.ok) {\r\n            console.error('Failed to create final exam');\r\n            const errorData = await examResponse.json();\r\n            console.error('Final exam creation error:', errorData);\r\n          } else {\r\n            const newExam = await examResponse.json();\r\n            console.log('Successfully created final exam:', newExam.quiz?.id);\r\n          }\r\n        }\r\n      }\r\n\r\n      toast.success('Course updated successfully!');\r\n      router.push('/dashboard/teacher/courses');\r\n      \r\n    } catch (error) {\r\n      console.error('Error updating course:', error);\r\n      toast.error(error instanceof Error ? error.message : 'Failed to update course');\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    router.push('/dashboard/teacher/courses');\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"container mx-auto p-6\">\r\n        <div className=\"mb-6\">\r\n          <Skeleton className=\"h-8 w-48 mb-2\" />\r\n          <Skeleton className=\"h-4 w-96\" />\r\n        </div>\r\n        <Card>\r\n          <CardHeader>\r\n            <Skeleton className=\"h-6 w-32\" />\r\n            <Skeleton className=\"h-4 w-64\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"space-y-4\">\r\n              <Skeleton className=\"h-10 w-full\" />\r\n              <Skeleton className=\"h-20 w-full\" />\r\n              <Skeleton className=\"h-10 w-full\" />\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"container mx-auto p-6\">\r\n        <div className=\"mb-6\">\r\n          <Link href=\"/dashboard/teacher/courses\">\r\n            <Button variant=\"ghost\" className=\"mb-4\">\r\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n              Back to Courses\r\n            </Button>\r\n          </Link>\r\n          <h1 className=\"text-3xl font-bold\">Edit Course</h1>\r\n          <p className=\"text-muted-foreground\">Update course information and content</p>\r\n        </div>\r\n        \r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2 text-destructive\">\r\n              <AlertCircle className=\"h-5 w-5\" />\r\n              Error Loading Course\r\n            </CardTitle>\r\n            <CardDescription>{error}</CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <Button onClick={fetchCourseData} variant=\"outline\">\r\n              Try Again\r\n            </Button>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!courseData) {\r\n    return (\r\n      <div className=\"container mx-auto p-6\">\r\n        <div className=\"mb-6\">\r\n          <Link href=\"/dashboard/teacher/courses\">\r\n            <Button variant=\"ghost\" className=\"mb-4\">\r\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n              Back to Courses\r\n            </Button>\r\n          </Link>\r\n          <h1 className=\"text-3xl font-bold\">Edit Course</h1>\r\n          <p className=\"text-muted-foreground\">Update course information and content</p>\r\n        </div>\r\n        \r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Course Not Found</CardTitle>\r\n            <CardDescription>The requested course could not be found.</CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <Link href=\"/dashboard/teacher/courses\">\r\n              <Button>Back to Courses</Button>\r\n            </Link>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"container mx-auto p-6\">\r\n      <div className=\"mb-6\">\r\n        <Link href=\"/dashboard/teacher/courses\">\r\n          <Button variant=\"ghost\" className=\"mb-4\">\r\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\r\n            Back to Courses\r\n          </Button>\r\n        </Link>\r\n        <h1 className=\"text-3xl font-bold\">Edit Course</h1>\r\n        <p className=\"text-muted-foreground\">Update course information and content</p>\r\n      </div>\r\n\r\n      <CourseCreationWizard\r\n        initialData={courseData}\r\n        onComplete={handleCourseUpdate}\r\n        onCancel={handleCancel}\r\n      />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAce,SAAS,eAAe,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,MAAG,AAAD,EAAE;IACnB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,IAAI;YACN;QACF;IACF,GAAG;QAAC;KAAG;IAEP,MAAM,kBAAkB;QACtB,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,OAAO,kHAAA,CAAA,cAAW,CAAC,OAAO;YAChC,IAAI,CAAC,MAAM;gBACT,SAAS;gBACT;YACF;YAEA,oBAAoB;YACpB,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,GAAG,WAAW,EAAE,KAAK,EAAE,EAAE;YACtE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,YAAuB,KAAK,MAAM;YACxC,QAAQ,GAAG,CAAC,wBAAwB,YAAY,YAAY;YAE5D,0DAA0D;YAC1D,MAAM,gBAA2B,UAAU,aAAa,IAAI,EAAE;YAC9D,MAAM,YAAiC,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;YACpF,MAAM,sBAAsB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;YAE3E,QAAQ,GAAG,CAAC,mBAAmB;gBAC7B,cAAc,cAAc,MAAM;gBAClC,eAAe,oBAAoB,MAAM;gBACzC,WAAW,YAAY,UAAU,EAAE,GAAG;gBACtC,SAAS,cAAc,GAAG,CAAC,CAAA,IAAK,CAAC;wBAAE,IAAI,EAAE,EAAE;wBAAE,MAAM,EAAE,QAAQ;wBAAE,UAAU,EAAE,QAAQ;wBAAE,UAAU,EAAE,QAAQ;oBAAC,CAAC;YAC7G;YAEA,sCAAsC;YACtC,MAAM,kBAAkB,CAAA,GAAA,6IAAA,CAAA,iCAA8B,AAAD,EAAE,WAAW,eAAe;YACjF,QAAQ,GAAG,CAAC,qBAAqB,kBAAkB,YAAY;YAC/D,cAAc;QAEhB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,cAAc;YACd,QAAQ,GAAG,CAAC,qCAAqC,oBAAoB,YAAY;YAEjF,MAAM,OAAO,kHAAA,CAAA,cAAW,CAAC,OAAO;YAChC,IAAI,CAAC,MAAM;gBACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,2CAA2C;YAC3C,MAAM,aAAa,CAAA,GAAA,6IAAA,CAAA,iCAA8B,AAAD,EAAE,mBAAmB;YACrE,QAAQ,GAAG,CAAC,yBAAyB,aAAa,YAAY;YAE9D,2BAA2B;YAC3B,MAAM,iBAAiB,MAAM,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,WAAW,IAAI;oBACrB,aAAa,WAAW,WAAW;oBACnC,YAAY,WAAW,UAAU;oBACjC,MAAM,WAAW,IAAI;oBACrB,gBAAgB,WAAW,cAAc;oBACzC,OAAO,WAAW,KAAK;oBACvB,UAAU,WAAW,QAAQ;oBAC7B,eAAe,WAAW,aAAa;oBACvC,WAAW,WAAW,SAAS;oBAC/B,SAAS,WAAW,OAAO;oBAC3B,WAAW,KAAK,EAAE;oBAClB,0BAA0B;oBAC1B,YAAY,WAAW,UAAU;oBACjC,WAAW,WAAW,SAAS;oBAC/B,qBAAqB,WAAW,mBAAmB;oBACnD,SAAS,WAAW,OAAO;oBAC3B,mBAAmB,WAAW,iBAAiB;gBACjD;YACF;YAEA,IAAI,CAAC,eAAe,EAAE,EAAE;gBACtB,MAAM,YAAY,MAAM,eAAe,IAAI;gBAC3C,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,yCAAyC;YACzC,KAAK,MAAM,cAAc,WAAW,OAAO,CAAE;gBAC3C,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,WAAW,IAAI,EAAE,EAAE;oBACnD,IAAI,WAAW,EAAE;oBACjB,eAAe,CAAC,CAAC,WAAW,IAAI;oBAChC,cAAc,WAAW,IAAI,EAAE;gBACjC;gBAEA,IAAI,kBAAkB,WAAW,EAAE;gBAEnC,IAAI,WAAW,EAAE,EAAE;oBACjB,yBAAyB;oBACzB,MAAM,iBAAiB,MAAM,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE,EAAE;wBAClE,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;4BACnB,MAAM,WAAW,IAAI;4BACrB,aAAa,WAAW,WAAW;4BACnC,YAAY,WAAW,UAAU;4BACjC,WAAW,KAAK,EAAE;wBACpB;oBACF;oBAEA,IAAI,CAAC,eAAe,EAAE,EAAE;wBACtB,QAAQ,KAAK,CAAC,4BAA4B,WAAW,EAAE;oBACzD;gBACF,OAAO;oBACL,oBAAoB;oBACpB,MAAM,oBAAoB,MAAM,MAAM,gBAAgB;wBACpD,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;4BACnB,MAAM,WAAW,IAAI;4BACrB,aAAa,WAAW,WAAW;4BACnC,YAAY,WAAW,UAAU;4BACjC,UAAU,SAAS;4BACnB,WAAW,KAAK,EAAE;wBACpB;oBACF;oBAEA,IAAI,kBAAkB,EAAE,EAAE;wBACxB,MAAM,YAAY,MAAM,kBAAkB,IAAI;wBAC9C,kBAAkB,UAAU,MAAM,CAAC,EAAE;wBACrC,QAAQ,GAAG,CAAC,+BAA+B;oBAC7C;gBACF;gBAEA,kBAAkB;gBAClB,KAAK,MAAM,WAAW,WAAW,QAAQ,CAAE;oBACzC,IAAI,mBAAmB,QAAQ,EAAE;oBAEjC,IAAI,QAAQ,EAAE,EAAE;wBACd,0BAA0B;wBAC1B,MAAM,kBAAkB,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE,EAAE;4BACjE,QAAQ;4BACR,SAAS;gCAAE,gBAAgB;4BAAmB;4BAC9C,MAAM,KAAK,SAAS,CAAC;gCACnB,MAAM,QAAQ,IAAI;gCAClB,SAAS,QAAQ,OAAO;gCACxB,YAAY,QAAQ,UAAU;gCAC9B,WAAW,KAAK,EAAE;4BACpB;wBACF;wBAEA,IAAI,CAAC,gBAAgB,EAAE,EAAE;4BACvB,QAAQ,KAAK,CAAC,6BAA6B,QAAQ,EAAE;wBACvD;oBACF,OAAO;wBACL,qBAAqB;wBACrB,MAAM,qBAAqB,MAAM,MAAM,iBAAiB;4BACtD,QAAQ;4BACR,SAAS;gCAAE,gBAAgB;4BAAmB;4BAC9C,MAAM,KAAK,SAAS,CAAC;gCACnB,MAAM,QAAQ,IAAI;gCAClB,SAAS,QAAQ,OAAO;gCACxB,YAAY,QAAQ,UAAU;gCAC9B,UAAU;gCACV,WAAW,KAAK,EAAE;4BACpB;wBACF;wBAEA,IAAI,mBAAmB,EAAE,EAAE;4BACzB,MAAM,aAAa,MAAM,mBAAmB,IAAI;4BAChD,mBAAmB,WAAW,OAAO,CAAC,EAAE;4BACxC,QAAQ,GAAG,CAAC,gCAAgC;wBAC9C;oBACF;oBAEA,sBAAsB;oBACtB,IAAI,QAAQ,IAAI,EAAE;wBAChB,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,iBAAiB,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,EAAE;wBAEvF,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE;4BACnB,+BAA+B;4BAC/B,MAAM,eAAe,MAAM,MAAM,CAAC,aAAa,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE,EAAE;gCAClE,QAAQ;gCACR,SAAS;oCAAE,gBAAgB;gCAAmB;gCAC9C,MAAM,KAAK,SAAS,CAAC;oCACnB,MAAM,QAAQ,IAAI,CAAC,IAAI;oCACvB,aAAa,QAAQ,IAAI,CAAC,WAAW;oCACrC,UAAU;oCACV,WAAW,QAAQ,IAAI,CAAC,SAAS;oCACjC,cAAc,QAAQ,IAAI,CAAC,YAAY;oCACvC,WAAW,KAAK,EAAE;oCAClB,WAAW,QAAQ,IAAI,CAAC,SAAS;gCACnC;4BACF;4BAEA,IAAI,CAAC,aAAa,EAAE,EAAE;gCACpB,QAAQ,KAAK,CAAC,kCAAkC,QAAQ,IAAI,CAAC,EAAE;gCAC/D,MAAM,YAAY,MAAM,aAAa,IAAI;gCACzC,QAAQ,KAAK,CAAC,8BAA8B;4BAC9C;wBACF,OAAO;4BACL,0BAA0B;4BAC1B,MAAM,eAAe,MAAM,MAAM,gBAAgB;gCAC/C,QAAQ;gCACR,SAAS;oCAAE,gBAAgB;gCAAmB;gCAC9C,MAAM,KAAK,SAAS,CAAC;oCACnB,MAAM,QAAQ,IAAI,CAAC,IAAI;oCACvB,aAAa,QAAQ,IAAI,CAAC,WAAW;oCACrC,UAAU;oCACV,WAAW,QAAQ,IAAI,CAAC,SAAS;oCACjC,cAAc,QAAQ,IAAI,CAAC,YAAY;oCACvC,WAAW;oCACX,WAAW,KAAK,EAAE;oCAClB,WAAW,QAAQ,IAAI,CAAC,SAAS;gCACnC;4BACF;4BAEA,IAAI,CAAC,aAAa,EAAE,EAAE;gCACpB,QAAQ,KAAK,CAAC;gCACd,MAAM,YAAY,MAAM,aAAa,IAAI;gCACzC,QAAQ,KAAK,CAAC,gCAAgC;4BAChD;wBACF;oBACF;gBACF;gBAEA,8CAA8C;gBAC9C,IAAI,WAAW,IAAI,EAAE;oBACnB,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,gBAAgB,CAAC,CAAC,EAAE;wBACnE,QAAQ,WAAW,IAAI,CAAC,EAAE;wBAC1B,UAAU,WAAW,IAAI,CAAC,IAAI;wBAC9B,gBAAgB,WAAW,IAAI,CAAC,SAAS,CAAC,MAAM;oBAClD;oBAEA,IAAI,WAAW,IAAI,CAAC,EAAE,EAAE;wBACtB,8BAA8B;wBAC9B,MAAM,eAAe,MAAM,MAAM,CAAC,aAAa,EAAE,WAAW,IAAI,CAAC,EAAE,EAAE,EAAE;4BACrE,QAAQ;4BACR,SAAS;gCAAE,gBAAgB;4BAAmB;4BAC9C,MAAM,KAAK,SAAS,CAAC;gCACnB,MAAM,WAAW,IAAI,CAAC,IAAI;gCAC1B,aAAa,WAAW,IAAI,CAAC,WAAW;gCACxC,UAAU;gCACV,WAAW,WAAW,IAAI,CAAC,SAAS;gCACpC,cAAc,WAAW,IAAI,CAAC,YAAY;gCAC1C,UAAU;gCACV,WAAW,KAAK,EAAE;gCAClB,WAAW,WAAW,IAAI,CAAC,SAAS;4BACtC;wBACF;wBAEA,IAAI,CAAC,aAAa,EAAE,EAAE;4BACpB,QAAQ,KAAK,CAAC,iCAAiC,WAAW,IAAI,CAAC,EAAE;4BACjE,MAAM,YAAY,MAAM,aAAa,IAAI;4BACzC,QAAQ,KAAK,CAAC,6BAA6B;wBAC7C,OAAO;4BACL,QAAQ,GAAG,CAAC,qCAAqC,WAAW,IAAI,CAAC,EAAE;wBACrE;oBACF,OAAO;wBACL,yBAAyB;wBACzB,MAAM,eAAe,MAAM,MAAM,gBAAgB;4BAC/C,QAAQ;4BACR,SAAS;gCAAE,gBAAgB;4BAAmB;4BAC9C,MAAM,KAAK,SAAS,CAAC;gCACnB,MAAM,WAAW,IAAI,CAAC,IAAI;gCAC1B,aAAa,WAAW,IAAI,CAAC,WAAW;gCACxC,UAAU;gCACV,WAAW,WAAW,IAAI,CAAC,SAAS;gCACpC,cAAc,WAAW,IAAI,CAAC,YAAY;gCAC1C,UAAU;gCACV,WAAW,KAAK,EAAE;gCAClB,WAAW,WAAW,IAAI,CAAC,SAAS;4BACtC;wBACF;wBAEA,IAAI,CAAC,aAAa,EAAE,EAAE;4BACpB,QAAQ,KAAK,CAAC;4BACd,MAAM,YAAY,MAAM,aAAa,IAAI;4BACzC,QAAQ,KAAK,CAAC,+BAA+B;wBAC/C,OAAO;4BACL,MAAM,UAAU,MAAM,aAAa,IAAI;4BACvC,QAAQ,GAAG,CAAC,qCAAqC,QAAQ,IAAI,EAAE;wBACjE;oBACF;gBACF;YACF;YAEA,6CAA6C;YAC7C,IAAI,WAAW,SAAS,EAAE;gBACxB,QAAQ,GAAG,CAAC,0BAA0B;oBACpC,QAAQ,WAAW,SAAS,CAAC,EAAE;oBAC/B,UAAU,WAAW,SAAS,CAAC,IAAI;oBACnC,gBAAgB,WAAW,SAAS,CAAC,SAAS,CAAC,MAAM;gBACvD;gBAEA,IAAI,WAAW,SAAS,CAAC,EAAE,EAAE;oBAC3B,6BAA6B;oBAC7B,MAAM,eAAe,MAAM,MAAM,CAAC,aAAa,EAAE,WAAW,SAAS,CAAC,EAAE,EAAE,EAAE;wBAC1E,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;4BACnB,MAAM,WAAW,SAAS,CAAC,IAAI;4BAC/B,aAAa,WAAW,SAAS,CAAC,WAAW;4BAC7C,UAAU;4BACV,WAAW,WAAW,SAAS,CAAC,SAAS;4BACzC,cAAc,WAAW,SAAS,CAAC,YAAY;4BAC/C,UAAU,SAAS;4BACnB,WAAW,KAAK,EAAE;4BAClB,WAAW,WAAW,SAAS,CAAC,SAAS;wBAC3C;oBACF;oBAEA,IAAI,CAAC,aAAa,EAAE,EAAE;wBACpB,QAAQ,KAAK,CAAC,gCAAgC,WAAW,SAAS,CAAC,EAAE;wBACrE,MAAM,YAAY,MAAM,aAAa,IAAI;wBACzC,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C,OAAO;wBACL,QAAQ,GAAG,CAAC,oCAAoC,WAAW,SAAS,CAAC,EAAE;oBACzE;gBACF,OAAO;oBACL,wBAAwB;oBACxB,MAAM,eAAe,MAAM,MAAM,gBAAgB;wBAC/C,QAAQ;wBACR,SAAS;4BAAE,gBAAgB;wBAAmB;wBAC9C,MAAM,KAAK,SAAS,CAAC;4BACnB,MAAM,WAAW,SAAS,CAAC,IAAI;4BAC/B,aAAa,WAAW,SAAS,CAAC,WAAW;4BAC7C,UAAU;4BACV,WAAW,WAAW,SAAS,CAAC,SAAS;4BACzC,cAAc,WAAW,SAAS,CAAC,YAAY;4BAC/C,UAAU,SAAS;4BACnB,WAAW,KAAK,EAAE;4BAClB,WAAW,WAAW,SAAS,CAAC,SAAS;wBAC3C;oBACF;oBAEA,IAAI,CAAC,aAAa,EAAE,EAAE;wBACpB,QAAQ,KAAK,CAAC;wBACd,MAAM,YAAY,MAAM,aAAa,IAAI;wBACzC,QAAQ,KAAK,CAAC,8BAA8B;oBAC9C,OAAO;wBACL,MAAM,UAAU,MAAM,aAAa,IAAI;wBACvC,QAAQ,GAAG,CAAC,oCAAoC,QAAQ,IAAI,EAAE;oBAChE;gBACF;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;8BAEtB,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMhC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,WAAU;;kDAChC,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAI1C,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAGvC,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGrC,8OAAC,gIAAA,CAAA,kBAAe;8CAAE;;;;;;;;;;;;sCAEpB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAiB,SAAQ;0CAAU;;;;;;;;;;;;;;;;;;;;;;;IAO9D;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,WAAU;;kDAChC,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAI1C,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAGvC,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,WAAU;;8CAChC,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAI1C,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAGvC,8OAAC,4JAAA,CAAA,uBAAoB;gBACnB,aAAa;gBACb,YAAY;gBACZ,UAAU;;;;;;;;;;;;AAIlB", "debugId": null}}]}