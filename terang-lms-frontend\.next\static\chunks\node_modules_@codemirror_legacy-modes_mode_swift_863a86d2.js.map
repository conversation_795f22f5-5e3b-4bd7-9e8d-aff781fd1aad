{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/swift.js"], "sourcesContent": ["function wordSet(words) {\n  var set = {}\n  for (var i = 0; i < words.length; i++) set[words[i]] = true\n  return set\n}\n\nvar keywords = wordSet([\"_\",\"var\",\"let\",\"actor\",\"class\",\"enum\",\"extension\",\"import\",\"protocol\",\"struct\",\"func\",\"typealias\",\"associatedtype\",\n                        \"open\",\"public\",\"internal\",\"fileprivate\",\"private\",\"deinit\",\"init\",\"new\",\"override\",\"self\",\"subscript\",\"super\",\n                        \"convenience\",\"dynamic\",\"final\",\"indirect\",\"lazy\",\"required\",\"static\",\"unowned\",\"unowned(safe)\",\"unowned(unsafe)\",\"weak\",\"as\",\"is\",\n                        \"break\",\"case\",\"continue\",\"default\",\"else\",\"fallthrough\",\"for\",\"guard\",\"if\",\"in\",\"repeat\",\"switch\",\"where\",\"while\",\n                        \"defer\",\"return\",\"inout\",\"mutating\",\"nonmutating\",\"isolated\",\"nonisolated\",\"catch\",\"do\",\"rethrows\",\"throw\",\"throws\",\"async\",\"await\",\"try\",\"didSet\",\"get\",\"set\",\"willSet\",\n                        \"assignment\",\"associativity\",\"infix\",\"left\",\"none\",\"operator\",\"postfix\",\"precedence\",\"precedencegroup\",\"prefix\",\"right\",\n                        \"Any\",\"AnyObject\",\"Type\",\"dynamicType\",\"Self\",\"Protocol\",\"__COLUMN__\",\"__FILE__\",\"__FUNCTION__\",\"__LINE__\"])\nvar definingKeywords = wordSet([\"var\",\"let\",\"actor\",\"class\",\"enum\",\"extension\",\"import\",\"protocol\",\"struct\",\"func\",\"typealias\",\"associatedtype\",\"for\"])\nvar atoms = wordSet([\"true\",\"false\",\"nil\",\"self\",\"super\",\"_\"])\nvar types = wordSet([\"Array\",\"Bool\",\"Character\",\"Dictionary\",\"Double\",\"Float\",\"Int\",\"Int8\",\"Int16\",\"Int32\",\"Int64\",\"Never\",\"Optional\",\"Set\",\"String\",\n                     \"UInt8\",\"UInt16\",\"UInt32\",\"UInt64\",\"Void\"])\nvar operators = \"+-/*%=|&<>~^?!\"\nvar punc = \":;,.(){}[]\"\nvar binary = /^\\-?0b[01][01_]*/\nvar octal = /^\\-?0o[0-7][0-7_]*/\nvar hexadecimal = /^\\-?0x[\\dA-Fa-f][\\dA-Fa-f_]*(?:(?:\\.[\\dA-Fa-f][\\dA-Fa-f_]*)?[Pp]\\-?\\d[\\d_]*)?/\nvar decimal = /^\\-?\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[Ee]\\-?\\d[\\d_]*)?/\nvar identifier = /^\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1/\nvar property = /^\\.(?:\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1)/\nvar instruction = /^\\#[A-Za-z]+/\nvar attribute = /^@(?:\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1)/\n//var regexp = /^\\/(?!\\s)(?:\\/\\/)?(?:\\\\.|[^\\/])+\\//\n\nfunction tokenBase(stream, state, prev) {\n  if (stream.sol()) state.indented = stream.indentation()\n  if (stream.eatSpace()) return null\n\n  var ch = stream.peek()\n  if (ch == \"/\") {\n    if (stream.match(\"//\")) {\n      stream.skipToEnd()\n      return \"comment\"\n    }\n    if (stream.match(\"/*\")) {\n      state.tokenize.push(tokenComment)\n      return tokenComment(stream, state)\n    }\n  }\n  if (stream.match(instruction)) return \"builtin\"\n  if (stream.match(attribute)) return \"attribute\"\n  if (stream.match(binary)) return \"number\"\n  if (stream.match(octal)) return \"number\"\n  if (stream.match(hexadecimal)) return \"number\"\n  if (stream.match(decimal)) return \"number\"\n  if (stream.match(property)) return \"property\"\n  if (operators.indexOf(ch) > -1) {\n    stream.next()\n    return \"operator\"\n  }\n  if (punc.indexOf(ch) > -1) {\n    stream.next()\n    stream.match(\"..\")\n    return \"punctuation\"\n  }\n  var stringMatch\n  if (stringMatch = stream.match(/(\"\"\"|\"|')/)) {\n    var tokenize = tokenString.bind(null, stringMatch[0])\n    state.tokenize.push(tokenize)\n    return tokenize(stream, state)\n  }\n\n  if (stream.match(identifier)) {\n    var ident = stream.current()\n    if (types.hasOwnProperty(ident)) return \"type\"\n    if (atoms.hasOwnProperty(ident)) return \"atom\"\n    if (keywords.hasOwnProperty(ident)) {\n      if (definingKeywords.hasOwnProperty(ident))\n        state.prev = \"define\"\n      return \"keyword\"\n    }\n    if (prev == \"define\") return \"def\"\n    return \"variable\"\n  }\n\n  stream.next()\n  return null\n}\n\nfunction tokenUntilClosingParen() {\n  var depth = 0\n  return function(stream, state, prev) {\n    var inner = tokenBase(stream, state, prev)\n    if (inner == \"punctuation\") {\n      if (stream.current() == \"(\") ++depth\n      else if (stream.current() == \")\") {\n        if (depth == 0) {\n          stream.backUp(1)\n          state.tokenize.pop()\n          return state.tokenize[state.tokenize.length - 1](stream, state)\n        }\n        else --depth\n      }\n    }\n    return inner\n  }\n}\n\nfunction tokenString(openQuote, stream, state) {\n  var singleLine = openQuote.length == 1\n  var ch, escaped = false\n  while (ch = stream.peek()) {\n    if (escaped) {\n      stream.next()\n      if (ch == \"(\") {\n        state.tokenize.push(tokenUntilClosingParen())\n        return \"string\"\n      }\n      escaped = false\n    } else if (stream.match(openQuote)) {\n      state.tokenize.pop()\n      return \"string\"\n    } else {\n      stream.next()\n      escaped = ch == \"\\\\\"\n    }\n  }\n  if (singleLine) {\n    state.tokenize.pop()\n  }\n  return \"string\"\n}\n\nfunction tokenComment(stream, state) {\n  var ch\n  while (ch = stream.next()) {\n    if (ch === \"/\" && stream.eat(\"*\")) {\n      state.tokenize.push(tokenComment)\n    } else if (ch === \"*\" && stream.eat(\"/\")) {\n      state.tokenize.pop()\n      break\n    }\n  }\n  return \"comment\"\n}\n\nfunction Context(prev, align, indented) {\n  this.prev = prev\n  this.align = align\n  this.indented = indented\n}\n\nfunction pushContext(state, stream) {\n  var align = stream.match(/^\\s*($|\\/[\\/\\*]|[)}\\]])/, false) ? null : stream.column() + 1\n  state.context = new Context(state.context, align, state.indented)\n}\n\nfunction popContext(state) {\n  if (state.context) {\n    state.indented = state.context.indented\n    state.context = state.context.prev\n  }\n}\n\nexport const swift = {\n  name: \"swift\",\n  startState: function() {\n    return {\n      prev: null,\n      context: null,\n      indented: 0,\n      tokenize: []\n    }\n  },\n\n  token: function(stream, state) {\n    var prev = state.prev\n    state.prev = null\n    var tokenize = state.tokenize[state.tokenize.length - 1] || tokenBase\n    var style = tokenize(stream, state, prev)\n    if (!style || style == \"comment\") state.prev = prev\n    else if (!state.prev) state.prev = style\n\n    if (style == \"punctuation\") {\n      var bracket = /[\\(\\[\\{]|([\\]\\)\\}])/.exec(stream.current())\n      if (bracket) (bracket[1] ? popContext : pushContext)(state, stream)\n    }\n\n    return style\n  },\n\n  indent: function(state, textAfter, iCx) {\n    var cx = state.context\n    if (!cx) return 0\n    var closing = /^[\\]\\}\\)]/.test(textAfter)\n    if (cx.align != null) return cx.align - (closing ? 1 : 0)\n    return cx.indented + (closing ? 0 : iCx.unit)\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[\\)\\}\\]]$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]}\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,QAAQ,KAAK;IACpB,IAAI,MAAM,CAAC;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AAEA,IAAI,WAAW,QAAQ;IAAC;IAAI;IAAM;IAAM;IAAQ;IAAQ;IAAO;IAAY;IAAS;IAAW;IAAS;IAAO;IAAY;IACnG;IAAO;IAAS;IAAW;IAAc;IAAU;IAAS;IAAO;IAAM;IAAW;IAAO;IAAY;IACvG;IAAc;IAAU;IAAQ;IAAW;IAAO;IAAW;IAAS;IAAU;IAAgB;IAAkB;IAAO;IAAK;IAC9H;IAAQ;IAAO;IAAW;IAAU;IAAO;IAAc;IAAM;IAAQ;IAAK;IAAK;IAAS;IAAS;IAAQ;IAC3G;IAAQ;IAAS;IAAQ;IAAW;IAAc;IAAW;IAAc;IAAQ;IAAK;IAAW;IAAQ;IAAS;IAAQ;IAAQ;IAAM;IAAS;IAAM;IAAM;IAC/J;IAAa;IAAgB;IAAQ;IAAO;IAAO;IAAW;IAAU;IAAa;IAAkB;IAAS;IAChH;IAAM;IAAY;IAAO;IAAc;IAAO;IAAW;IAAa;IAAW;IAAe;CAAW;AACnI,IAAI,mBAAmB,QAAQ;IAAC;IAAM;IAAM;IAAQ;IAAQ;IAAO;IAAY;IAAS;IAAW;IAAS;IAAO;IAAY;IAAiB;CAAM;AACtJ,IAAI,QAAQ,QAAQ;IAAC;IAAO;IAAQ;IAAM;IAAO;IAAQ;CAAI;AAC7D,IAAI,QAAQ,QAAQ;IAAC;IAAQ;IAAO;IAAY;IAAa;IAAS;IAAQ;IAAM;IAAO;IAAQ;IAAQ;IAAQ;IAAQ;IAAW;IAAM;IACvH;IAAQ;IAAS;IAAS;IAAS;CAAO;AAC/D,IAAI,YAAY;AAChB,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,IAAI,UAAU;AACd,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,mDAAmD;AAEnD,SAAS,UAAU,MAAM,EAAE,KAAK,EAAE,IAAI;IACpC,IAAI,OAAO,GAAG,IAAI,MAAM,QAAQ,GAAG,OAAO,WAAW;IACrD,IAAI,OAAO,QAAQ,IAAI,OAAO;IAE9B,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,KAAK;QACb,IAAI,OAAO,KAAK,CAAC,OAAO;YACtB,OAAO,SAAS;YAChB,OAAO;QACT;QACA,IAAI,OAAO,KAAK,CAAC,OAAO;YACtB,MAAM,QAAQ,CAAC,IAAI,CAAC;YACpB,OAAO,aAAa,QAAQ;QAC9B;IACF;IACA,IAAI,OAAO,KAAK,CAAC,cAAc,OAAO;IACtC,IAAI,OAAO,KAAK,CAAC,YAAY,OAAO;IACpC,IAAI,OAAO,KAAK,CAAC,SAAS,OAAO;IACjC,IAAI,OAAO,KAAK,CAAC,QAAQ,OAAO;IAChC,IAAI,OAAO,KAAK,CAAC,cAAc,OAAO;IACtC,IAAI,OAAO,KAAK,CAAC,UAAU,OAAO;IAClC,IAAI,OAAO,KAAK,CAAC,WAAW,OAAO;IACnC,IAAI,UAAU,OAAO,CAAC,MAAM,CAAC,GAAG;QAC9B,OAAO,IAAI;QACX,OAAO;IACT;IACA,IAAI,KAAK,OAAO,CAAC,MAAM,CAAC,GAAG;QACzB,OAAO,IAAI;QACX,OAAO,KAAK,CAAC;QACb,OAAO;IACT;IACA,IAAI;IACJ,IAAI,cAAc,OAAO,KAAK,CAAC,cAAc;QAC3C,IAAI,WAAW,YAAY,IAAI,CAAC,MAAM,WAAW,CAAC,EAAE;QACpD,MAAM,QAAQ,CAAC,IAAI,CAAC;QACpB,OAAO,SAAS,QAAQ;IAC1B;IAEA,IAAI,OAAO,KAAK,CAAC,aAAa;QAC5B,IAAI,QAAQ,OAAO,OAAO;QAC1B,IAAI,MAAM,cAAc,CAAC,QAAQ,OAAO;QACxC,IAAI,MAAM,cAAc,CAAC,QAAQ,OAAO;QACxC,IAAI,SAAS,cAAc,CAAC,QAAQ;YAClC,IAAI,iBAAiB,cAAc,CAAC,QAClC,MAAM,IAAI,GAAG;YACf,OAAO;QACT;QACA,IAAI,QAAQ,UAAU,OAAO;QAC7B,OAAO;IACT;IAEA,OAAO,IAAI;IACX,OAAO;AACT;AAEA,SAAS;IACP,IAAI,QAAQ;IACZ,OAAO,SAAS,MAAM,EAAE,KAAK,EAAE,IAAI;QACjC,IAAI,QAAQ,UAAU,QAAQ,OAAO;QACrC,IAAI,SAAS,eAAe;YAC1B,IAAI,OAAO,OAAO,MAAM,KAAK,EAAE;iBAC1B,IAAI,OAAO,OAAO,MAAM,KAAK;gBAChC,IAAI,SAAS,GAAG;oBACd,OAAO,MAAM,CAAC;oBACd,MAAM,QAAQ,CAAC,GAAG;oBAClB,OAAO,MAAM,QAAQ,CAAC,MAAM,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC,QAAQ;gBAC3D,OACK,EAAE;YACT;QACF;QACA,OAAO;IACT;AACF;AAEA,SAAS,YAAY,SAAS,EAAE,MAAM,EAAE,KAAK;IAC3C,IAAI,aAAa,UAAU,MAAM,IAAI;IACrC,IAAI,IAAI,UAAU;IAClB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,SAAS;YACX,OAAO,IAAI;YACX,IAAI,MAAM,KAAK;gBACb,MAAM,QAAQ,CAAC,IAAI,CAAC;gBACpB,OAAO;YACT;YACA,UAAU;QACZ,OAAO,IAAI,OAAO,KAAK,CAAC,YAAY;YAClC,MAAM,QAAQ,CAAC,GAAG;YAClB,OAAO;QACT,OAAO;YACL,OAAO,IAAI;YACX,UAAU,MAAM;QAClB;IACF;IACA,IAAI,YAAY;QACd,MAAM,QAAQ,CAAC,GAAG;IACpB;IACA,OAAO;AACT;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI;IACJ,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,OAAO,OAAO,OAAO,GAAG,CAAC,MAAM;YACjC,MAAM,QAAQ,CAAC,IAAI,CAAC;QACtB,OAAO,IAAI,OAAO,OAAO,OAAO,GAAG,CAAC,MAAM;YACxC,MAAM,QAAQ,CAAC,GAAG;YAClB;QACF;IACF;IACA,OAAO;AACT;AAEA,SAAS,QAAQ,IAAI,EAAE,KAAK,EAAE,QAAQ;IACpC,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,QAAQ,GAAG;AAClB;AAEA,SAAS,YAAY,KAAK,EAAE,MAAM;IAChC,IAAI,QAAQ,OAAO,KAAK,CAAC,2BAA2B,SAAS,OAAO,OAAO,MAAM,KAAK;IACtF,MAAM,OAAO,GAAG,IAAI,QAAQ,MAAM,OAAO,EAAE,OAAO,MAAM,QAAQ;AAClE;AAEA,SAAS,WAAW,KAAK;IACvB,IAAI,MAAM,OAAO,EAAE;QACjB,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ;QACvC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;IACpC;AACF;AAEO,MAAM,QAAQ;IACnB,MAAM;IACN,YAAY;QACV,OAAO;YACL,MAAM;YACN,SAAS;YACT,UAAU;YACV,UAAU,EAAE;QACd;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,OAAO,MAAM,IAAI;QACrB,MAAM,IAAI,GAAG;QACb,IAAI,WAAW,MAAM,QAAQ,CAAC,MAAM,QAAQ,CAAC,MAAM,GAAG,EAAE,IAAI;QAC5D,IAAI,QAAQ,SAAS,QAAQ,OAAO;QACpC,IAAI,CAAC,SAAS,SAAS,WAAW,MAAM,IAAI,GAAG;aAC1C,IAAI,CAAC,MAAM,IAAI,EAAE,MAAM,IAAI,GAAG;QAEnC,IAAI,SAAS,eAAe;YAC1B,IAAI,UAAU,sBAAsB,IAAI,CAAC,OAAO,OAAO;YACvD,IAAI,SAAS,CAAC,OAAO,CAAC,EAAE,GAAG,aAAa,WAAW,EAAE,OAAO;QAC9D;QAEA,OAAO;IACT;IAEA,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG;QACpC,IAAI,KAAK,MAAM,OAAO;QACtB,IAAI,CAAC,IAAI,OAAO;QAChB,IAAI,UAAU,YAAY,IAAI,CAAC;QAC/B,IAAI,GAAG,KAAK,IAAI,MAAM,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,IAAI,CAAC;QACxD,OAAO,GAAG,QAAQ,GAAG,CAAC,UAAU,IAAI,IAAI,IAAI;IAC9C;IAEA,cAAc;QACZ,eAAe;QACf,eAAe;YAAC,MAAM;YAAM,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;QAC5D,eAAe;YAAC,UAAU;gBAAC;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;aAAI;QAAA;IAC1D;AACF", "ignoreList": [0], "debugId": null}}]}