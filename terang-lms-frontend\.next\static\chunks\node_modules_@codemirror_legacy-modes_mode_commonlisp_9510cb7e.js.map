{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/commonlisp.js"], "sourcesContent": ["var specialForm = /^(block|let*|return-from|catch|load-time-value|setq|eval-when|locally|symbol-macrolet|flet|macrolet|tagbody|function|multiple-value-call|the|go|multiple-value-prog1|throw|if|progn|unwind-protect|labels|progv|let|quote)$/;\nvar assumeBody = /^with|^def|^do|^prog|case$|^cond$|bind$|when$|unless$/;\nvar numLiteral = /^(?:[+\\-]?(?:\\d+|\\d*\\.\\d+)(?:[efd][+\\-]?\\d+)?|[+\\-]?\\d+(?:\\/[+\\-]?\\d+)?|#b[+\\-]?[01]+|#o[+\\-]?[0-7]+|#x[+\\-]?[\\da-f]+)/;\nvar symbol = /[^\\s'`,@()\\[\\]\";]/;\nvar type;\n\nfunction readSym(stream) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"\\\\\") stream.next();\n    else if (!symbol.test(ch)) { stream.backUp(1); break; }\n  }\n  return stream.current();\n}\n\nfunction base(stream, state) {\n  if (stream.eatSpace()) {type = \"ws\"; return null;}\n  if (stream.match(numLiteral)) return \"number\";\n  var ch = stream.next();\n  if (ch == \"\\\\\") ch = stream.next();\n\n  if (ch == '\"') return (state.tokenize = inString)(stream, state);\n  else if (ch == \"(\") { type = \"open\"; return \"bracket\"; }\n  else if (ch == \")\") { type = \"close\"; return \"bracket\"; }\n  else if (ch == \";\") { stream.skipToEnd(); type = \"ws\"; return \"comment\"; }\n  else if (/['`,@]/.test(ch)) return null;\n  else if (ch == \"|\") {\n    if (stream.skipTo(\"|\")) { stream.next(); return \"variableName\"; }\n    else { stream.skipToEnd(); return \"error\"; }\n  } else if (ch == \"#\") {\n    var ch = stream.next();\n    if (ch == \"(\") { type = \"open\"; return \"bracket\"; }\n    else if (/[+\\-=\\.']/.test(ch)) return null;\n    else if (/\\d/.test(ch) && stream.match(/^\\d*#/)) return null;\n    else if (ch == \"|\") return (state.tokenize = inComment)(stream, state);\n    else if (ch == \":\") { readSym(stream); return \"meta\"; }\n    else if (ch == \"\\\\\") { stream.next(); readSym(stream); return \"string.special\" }\n    else return \"error\";\n  } else {\n    var name = readSym(stream);\n    if (name == \".\") return null;\n    type = \"symbol\";\n    if (name == \"nil\" || name == \"t\" || name.charAt(0) == \":\") return \"atom\";\n    if (state.lastType == \"open\" && (specialForm.test(name) || assumeBody.test(name))) return \"keyword\";\n    if (name.charAt(0) == \"&\") return \"variableName.special\";\n    return \"variableName\";\n  }\n}\n\nfunction inString(stream, state) {\n  var escaped = false, next;\n  while (next = stream.next()) {\n    if (next == '\"' && !escaped) { state.tokenize = base; break; }\n    escaped = !escaped && next == \"\\\\\";\n  }\n  return \"string\";\n}\n\nfunction inComment(stream, state) {\n  var next, last;\n  while (next = stream.next()) {\n    if (next == \"#\" && last == \"|\") { state.tokenize = base; break; }\n    last = next;\n  }\n  type = \"ws\";\n  return \"comment\";\n}\n\nexport const commonLisp = {\n  name: \"commonlisp\",\n  startState: function () {\n    return {ctx: {prev: null, start: 0, indentTo: 0}, lastType: null, tokenize: base};\n  },\n\n  token: function (stream, state) {\n    if (stream.sol() && typeof state.ctx.indentTo != \"number\")\n      state.ctx.indentTo = state.ctx.start + 1;\n\n    type = null;\n    var style = state.tokenize(stream, state);\n    if (type != \"ws\") {\n      if (state.ctx.indentTo == null) {\n        if (type == \"symbol\" && assumeBody.test(stream.current()))\n          state.ctx.indentTo = state.ctx.start + stream.indentUnit;\n        else\n          state.ctx.indentTo = \"next\";\n      } else if (state.ctx.indentTo == \"next\") {\n        state.ctx.indentTo = stream.column();\n      }\n      state.lastType = type;\n    }\n    if (type == \"open\") state.ctx = {prev: state.ctx, start: stream.column(), indentTo: null};\n    else if (type == \"close\") state.ctx = state.ctx.prev || state.ctx;\n    return style;\n  },\n\n  indent: function (state) {\n    var i = state.ctx.indentTo;\n    return typeof i == \"number\" ? i : state.ctx.start + 1;\n  },\n\n  languageData: {\n    commentTokens: {line: \";;\", block: {open: \"#|\", close: \"|#\"}},\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", '\"']}\n  }\n};\n\n"], "names": [], "mappings": ";;;AAAA,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI;AAEJ,SAAS,QAAQ,MAAM;IACrB,IAAI;IACJ,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,MAAM,OAAO,IAAI;aACtB,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;YAAE,OAAO,MAAM,CAAC;YAAI;QAAO;IACxD;IACA,OAAO,OAAO,OAAO;AACvB;AAEA,SAAS,KAAK,MAAM,EAAE,KAAK;IACzB,IAAI,OAAO,QAAQ,IAAI;QAAC,OAAO;QAAM,OAAO;IAAK;IACjD,IAAI,OAAO,KAAK,CAAC,aAAa,OAAO;IACrC,IAAI,KAAK,OAAO,IAAI;IACpB,IAAI,MAAM,MAAM,KAAK,OAAO,IAAI;IAEhC,IAAI,MAAM,KAAK,OAAO,CAAC,MAAM,QAAQ,GAAG,QAAQ,EAAE,QAAQ;SACrD,IAAI,MAAM,KAAK;QAAE,OAAO;QAAQ,OAAO;IAAW,OAClD,IAAI,MAAM,KAAK;QAAE,OAAO;QAAS,OAAO;IAAW,OACnD,IAAI,MAAM,KAAK;QAAE,OAAO,SAAS;QAAI,OAAO;QAAM,OAAO;IAAW,OACpE,IAAI,SAAS,IAAI,CAAC,KAAK,OAAO;SAC9B,IAAI,MAAM,KAAK;QAClB,IAAI,OAAO,MAAM,CAAC,MAAM;YAAE,OAAO,IAAI;YAAI,OAAO;QAAgB,OAC3D;YAAE,OAAO,SAAS;YAAI,OAAO;QAAS;IAC7C,OAAO,IAAI,MAAM,KAAK;QACpB,IAAI,KAAK,OAAO,IAAI;QACpB,IAAI,MAAM,KAAK;YAAE,OAAO;YAAQ,OAAO;QAAW,OAC7C,IAAI,YAAY,IAAI,CAAC,KAAK,OAAO;aACjC,IAAI,KAAK,IAAI,CAAC,OAAO,OAAO,KAAK,CAAC,UAAU,OAAO;aACnD,IAAI,MAAM,KAAK,OAAO,CAAC,MAAM,QAAQ,GAAG,SAAS,EAAE,QAAQ;aAC3D,IAAI,MAAM,KAAK;YAAE,QAAQ;YAAS,OAAO;QAAQ,OACjD,IAAI,MAAM,MAAM;YAAE,OAAO,IAAI;YAAI,QAAQ;YAAS,OAAO;QAAiB,OAC1E,OAAO;IACd,OAAO;QACL,IAAI,OAAO,QAAQ;QACnB,IAAI,QAAQ,KAAK,OAAO;QACxB,OAAO;QACP,IAAI,QAAQ,SAAS,QAAQ,OAAO,KAAK,MAAM,CAAC,MAAM,KAAK,OAAO;QAClE,IAAI,MAAM,QAAQ,IAAI,UAAU,CAAC,YAAY,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,KAAK,GAAG,OAAO;QAC1F,IAAI,KAAK,MAAM,CAAC,MAAM,KAAK,OAAO;QAClC,OAAO;IACT;AACF;AAEA,SAAS,SAAS,MAAM,EAAE,KAAK;IAC7B,IAAI,UAAU,OAAO;IACrB,MAAO,OAAO,OAAO,IAAI,GAAI;QAC3B,IAAI,QAAQ,OAAO,CAAC,SAAS;YAAE,MAAM,QAAQ,GAAG;YAAM;QAAO;QAC7D,UAAU,CAAC,WAAW,QAAQ;IAChC;IACA,OAAO;AACT;AAEA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,MAAM;IACV,MAAO,OAAO,OAAO,IAAI,GAAI;QAC3B,IAAI,QAAQ,OAAO,QAAQ,KAAK;YAAE,MAAM,QAAQ,GAAG;YAAM;QAAO;QAChE,OAAO;IACT;IACA,OAAO;IACP,OAAO;AACT;AAEO,MAAM,aAAa;IACxB,MAAM;IACN,YAAY;QACV,OAAO;YAAC,KAAK;gBAAC,MAAM;gBAAM,OAAO;gBAAG,UAAU;YAAC;YAAG,UAAU;YAAM,UAAU;QAAI;IAClF;IAEA,OAAO,SAAU,MAAM,EAAE,KAAK;QAC5B,IAAI,OAAO,GAAG,MAAM,OAAO,MAAM,GAAG,CAAC,QAAQ,IAAI,UAC/C,MAAM,GAAG,CAAC,QAAQ,GAAG,MAAM,GAAG,CAAC,KAAK,GAAG;QAEzC,OAAO;QACP,IAAI,QAAQ,MAAM,QAAQ,CAAC,QAAQ;QACnC,IAAI,QAAQ,MAAM;YAChB,IAAI,MAAM,GAAG,CAAC,QAAQ,IAAI,MAAM;gBAC9B,IAAI,QAAQ,YAAY,WAAW,IAAI,CAAC,OAAO,OAAO,KACpD,MAAM,GAAG,CAAC,QAAQ,GAAG,MAAM,GAAG,CAAC,KAAK,GAAG,OAAO,UAAU;qBAExD,MAAM,GAAG,CAAC,QAAQ,GAAG;YACzB,OAAO,IAAI,MAAM,GAAG,CAAC,QAAQ,IAAI,QAAQ;gBACvC,MAAM,GAAG,CAAC,QAAQ,GAAG,OAAO,MAAM;YACpC;YACA,MAAM,QAAQ,GAAG;QACnB;QACA,IAAI,QAAQ,QAAQ,MAAM,GAAG,GAAG;YAAC,MAAM,MAAM,GAAG;YAAE,OAAO,OAAO,MAAM;YAAI,UAAU;QAAI;aACnF,IAAI,QAAQ,SAAS,MAAM,GAAG,GAAG,MAAM,GAAG,CAAC,IAAI,IAAI,MAAM,GAAG;QACjE,OAAO;IACT;IAEA,QAAQ,SAAU,KAAK;QACrB,IAAI,IAAI,MAAM,GAAG,CAAC,QAAQ;QAC1B,OAAO,OAAO,KAAK,WAAW,IAAI,MAAM,GAAG,CAAC,KAAK,GAAG;IACtD;IAEA,cAAc;QACZ,eAAe;YAAC,MAAM;YAAM,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;QAC5D,eAAe;YAAC,UAAU;gBAAC;gBAAK;gBAAK;gBAAK;aAAI;QAAA;IAChD;AACF", "ignoreList": [0], "debugId": null}}]}