{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/ntriples.js"], "sourcesContent": ["var Location = {\n  PRE_SUBJECT         : 0,\n  WRITING_SUB_URI     : 1,\n  WRITING_BNODE_URI   : 2,\n  PRE_PRED            : 3,\n  WRITING_PRED_URI    : 4,\n  PRE_OBJ             : 5,\n  WRITING_OBJ_URI     : 6,\n  WRITING_OBJ_BNODE   : 7,\n  WRITING_OBJ_LITERAL : 8,\n  WRITING_LIT_LANG    : 9,\n  WRITING_LIT_TYPE    : 10,\n  POST_OBJ            : 11,\n  ERROR               : 12\n};\nfunction transitState(currState, c) {\n  var currLocation = currState.location;\n  var ret;\n\n  // Opening.\n  if     (currLocation == Location.PRE_SUBJECT && c == '<') ret = Location.WRITING_SUB_URI;\n  else if(currLocation == Location.PRE_SUBJECT && c == '_') ret = Location.WRITING_BNODE_URI;\n  else if(currLocation == Location.PRE_PRED    && c == '<') ret = Location.WRITING_PRED_URI;\n  else if(currLocation == Location.PRE_OBJ     && c == '<') ret = Location.WRITING_OBJ_URI;\n  else if(currLocation == Location.PRE_OBJ     && c == '_') ret = Location.WRITING_OBJ_BNODE;\n  else if(currLocation == Location.PRE_OBJ     && c == '\"') ret = Location.WRITING_OBJ_LITERAL;\n\n  // Closing.\n  else if(currLocation == Location.WRITING_SUB_URI     && c == '>') ret = Location.PRE_PRED;\n  else if(currLocation == Location.WRITING_BNODE_URI   && c == ' ') ret = Location.PRE_PRED;\n  else if(currLocation == Location.WRITING_PRED_URI    && c == '>') ret = Location.PRE_OBJ;\n  else if(currLocation == Location.WRITING_OBJ_URI     && c == '>') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_OBJ_BNODE   && c == ' ') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_OBJ_LITERAL && c == '\"') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_LIT_LANG && c == ' ') ret = Location.POST_OBJ;\n  else if(currLocation == Location.WRITING_LIT_TYPE && c == '>') ret = Location.POST_OBJ;\n\n  // Closing typed and language literal.\n  else if(currLocation == Location.WRITING_OBJ_LITERAL && c == '@') ret = Location.WRITING_LIT_LANG;\n  else if(currLocation == Location.WRITING_OBJ_LITERAL && c == '^') ret = Location.WRITING_LIT_TYPE;\n\n  // Spaces.\n  else if( c == ' ' &&\n           (\n             currLocation == Location.PRE_SUBJECT ||\n               currLocation == Location.PRE_PRED    ||\n               currLocation == Location.PRE_OBJ     ||\n               currLocation == Location.POST_OBJ\n           )\n         ) ret = currLocation;\n\n  // Reset.\n  else if(currLocation == Location.POST_OBJ && c == '.') ret = Location.PRE_SUBJECT;\n\n  // Error\n  else ret = Location.ERROR;\n\n  currState.location=ret;\n}\n\nexport const ntriples = {\n  name: \"ntriples\",\n  startState: function() {\n    return {\n      location : Location.PRE_SUBJECT,\n      uris     : [],\n      anchors  : [],\n      bnodes   : [],\n      langs    : [],\n      types    : []\n    };\n  },\n  token: function(stream, state) {\n    var ch = stream.next();\n    if(ch == '<') {\n      transitState(state, ch);\n      var parsedURI = '';\n      stream.eatWhile( function(c) { if( c != '#' && c != '>' ) { parsedURI += c; return true; } return false;} );\n      state.uris.push(parsedURI);\n      if( stream.match('#', false) ) return 'variable';\n      stream.next();\n      transitState(state, '>');\n      return 'variable';\n    }\n    if(ch == '#') {\n      var parsedAnchor = '';\n      stream.eatWhile(function(c) { if(c != '>' && c != ' ') { parsedAnchor+= c; return true; } return false;});\n      state.anchors.push(parsedAnchor);\n      return 'url';\n    }\n    if(ch == '>') {\n      transitState(state, '>');\n      return 'variable';\n    }\n    if(ch == '_') {\n      transitState(state, ch);\n      var parsedBNode = '';\n      stream.eatWhile(function(c) { if( c != ' ' ) { parsedBNode += c; return true; } return false;});\n      state.bnodes.push(parsedBNode);\n      stream.next();\n      transitState(state, ' ');\n      return 'builtin';\n    }\n    if(ch == '\"') {\n      transitState(state, ch);\n      stream.eatWhile( function(c) { return c != '\"'; } );\n      stream.next();\n      if( stream.peek() != '@' && stream.peek() != '^' ) {\n        transitState(state, '\"');\n      }\n      return 'string';\n    }\n    if( ch == '@' ) {\n      transitState(state, '@');\n      var parsedLang = '';\n      stream.eatWhile(function(c) { if( c != ' ' ) { parsedLang += c; return true; } return false;});\n      state.langs.push(parsedLang);\n      stream.next();\n      transitState(state, ' ');\n      return 'string.special';\n    }\n    if( ch == '^' ) {\n      stream.next();\n      transitState(state, '^');\n      var parsedType = '';\n      stream.eatWhile(function(c) { if( c != '>' ) { parsedType += c; return true; } return false;} );\n      state.types.push(parsedType);\n      stream.next();\n      transitState(state, '>');\n      return 'variable';\n    }\n    if( ch == ' ' ) {\n      transitState(state, ch);\n    }\n    if( ch == '.' ) {\n      transitState(state, ch);\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,WAAW;IACb,aAAsB;IACtB,iBAAsB;IACtB,mBAAsB;IACtB,UAAsB;IACtB,kBAAsB;IACtB,SAAsB;IACtB,iBAAsB;IACtB,mBAAsB;IACtB,qBAAsB;IACtB,kBAAsB;IACtB,kBAAsB;IACtB,UAAsB;IACtB,OAAsB;AACxB;AACA,SAAS,aAAa,SAAS,EAAE,CAAC;IAChC,IAAI,eAAe,UAAU,QAAQ;IACrC,IAAI;IAEJ,WAAW;IACX,IAAQ,gBAAgB,SAAS,WAAW,IAAI,KAAK,KAAK,MAAM,SAAS,eAAe;SACnF,IAAG,gBAAgB,SAAS,WAAW,IAAI,KAAK,KAAK,MAAM,SAAS,iBAAiB;SACrF,IAAG,gBAAgB,SAAS,QAAQ,IAAO,KAAK,KAAK,MAAM,SAAS,gBAAgB;SACpF,IAAG,gBAAgB,SAAS,OAAO,IAAQ,KAAK,KAAK,MAAM,SAAS,eAAe;SACnF,IAAG,gBAAgB,SAAS,OAAO,IAAQ,KAAK,KAAK,MAAM,SAAS,iBAAiB;SACrF,IAAG,gBAAgB,SAAS,OAAO,IAAQ,KAAK,KAAK,MAAM,SAAS,mBAAmB;SAGvF,IAAG,gBAAgB,SAAS,eAAe,IAAQ,KAAK,KAAK,MAAM,SAAS,QAAQ;SACpF,IAAG,gBAAgB,SAAS,iBAAiB,IAAM,KAAK,KAAK,MAAM,SAAS,QAAQ;SACpF,IAAG,gBAAgB,SAAS,gBAAgB,IAAO,KAAK,KAAK,MAAM,SAAS,OAAO;SACnF,IAAG,gBAAgB,SAAS,eAAe,IAAQ,KAAK,KAAK,MAAM,SAAS,QAAQ;SACpF,IAAG,gBAAgB,SAAS,iBAAiB,IAAM,KAAK,KAAK,MAAM,SAAS,QAAQ;SACpF,IAAG,gBAAgB,SAAS,mBAAmB,IAAI,KAAK,KAAK,MAAM,SAAS,QAAQ;SACpF,IAAG,gBAAgB,SAAS,gBAAgB,IAAI,KAAK,KAAK,MAAM,SAAS,QAAQ;SACjF,IAAG,gBAAgB,SAAS,gBAAgB,IAAI,KAAK,KAAK,MAAM,SAAS,QAAQ;SAGjF,IAAG,gBAAgB,SAAS,mBAAmB,IAAI,KAAK,KAAK,MAAM,SAAS,gBAAgB;SAC5F,IAAG,gBAAgB,SAAS,mBAAmB,IAAI,KAAK,KAAK,MAAM,SAAS,gBAAgB;SAG5F,IAAI,KAAK,OACL,CACE,gBAAgB,SAAS,WAAW,IAClC,gBAAgB,SAAS,QAAQ,IACjC,gBAAgB,SAAS,OAAO,IAChC,gBAAgB,SAAS,QAAQ,AACrC,GACA,MAAM;SAGV,IAAG,gBAAgB,SAAS,QAAQ,IAAI,KAAK,KAAK,MAAM,SAAS,WAAW;SAG5E,MAAM,SAAS,KAAK;IAEzB,UAAU,QAAQ,GAAC;AACrB;AAEO,MAAM,WAAW;IACtB,MAAM;IACN,YAAY;QACV,OAAO;YACL,UAAW,SAAS,WAAW;YAC/B,MAAW,EAAE;YACb,SAAW,EAAE;YACb,QAAW,EAAE;YACb,OAAW,EAAE;YACb,OAAW,EAAE;QACf;IACF;IACA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,KAAK,OAAO,IAAI;QACpB,IAAG,MAAM,KAAK;YACZ,aAAa,OAAO;YACpB,IAAI,YAAY;YAChB,OAAO,QAAQ,CAAE,SAAS,CAAC;gBAAI,IAAI,KAAK,OAAO,KAAK,KAAM;oBAAE,aAAa;oBAAG,OAAO;gBAAM;gBAAE,OAAO;YAAM;YACxG,MAAM,IAAI,CAAC,IAAI,CAAC;YAChB,IAAI,OAAO,KAAK,CAAC,KAAK,QAAS,OAAO;YACtC,OAAO,IAAI;YACX,aAAa,OAAO;YACpB,OAAO;QACT;QACA,IAAG,MAAM,KAAK;YACZ,IAAI,eAAe;YACnB,OAAO,QAAQ,CAAC,SAAS,CAAC;gBAAI,IAAG,KAAK,OAAO,KAAK,KAAK;oBAAE,gBAAe;oBAAG,OAAO;gBAAM;gBAAE,OAAO;YAAM;YACvG,MAAM,OAAO,CAAC,IAAI,CAAC;YACnB,OAAO;QACT;QACA,IAAG,MAAM,KAAK;YACZ,aAAa,OAAO;YACpB,OAAO;QACT;QACA,IAAG,MAAM,KAAK;YACZ,aAAa,OAAO;YACpB,IAAI,cAAc;YAClB,OAAO,QAAQ,CAAC,SAAS,CAAC;gBAAI,IAAI,KAAK,KAAM;oBAAE,eAAe;oBAAG,OAAO;gBAAM;gBAAE,OAAO;YAAM;YAC7F,MAAM,MAAM,CAAC,IAAI,CAAC;YAClB,OAAO,IAAI;YACX,aAAa,OAAO;YACpB,OAAO;QACT;QACA,IAAG,MAAM,KAAK;YACZ,aAAa,OAAO;YACpB,OAAO,QAAQ,CAAE,SAAS,CAAC;gBAAI,OAAO,KAAK;YAAK;YAChD,OAAO,IAAI;YACX,IAAI,OAAO,IAAI,MAAM,OAAO,OAAO,IAAI,MAAM,KAAM;gBACjD,aAAa,OAAO;YACtB;YACA,OAAO;QACT;QACA,IAAI,MAAM,KAAM;YACd,aAAa,OAAO;YACpB,IAAI,aAAa;YACjB,OAAO,QAAQ,CAAC,SAAS,CAAC;gBAAI,IAAI,KAAK,KAAM;oBAAE,cAAc;oBAAG,OAAO;gBAAM;gBAAE,OAAO;YAAM;YAC5F,MAAM,KAAK,CAAC,IAAI,CAAC;YACjB,OAAO,IAAI;YACX,aAAa,OAAO;YACpB,OAAO;QACT;QACA,IAAI,MAAM,KAAM;YACd,OAAO,IAAI;YACX,aAAa,OAAO;YACpB,IAAI,aAAa;YACjB,OAAO,QAAQ,CAAC,SAAS,CAAC;gBAAI,IAAI,KAAK,KAAM;oBAAE,cAAc;oBAAG,OAAO;gBAAM;gBAAE,OAAO;YAAM;YAC5F,MAAM,KAAK,CAAC,IAAI,CAAC;YACjB,OAAO,IAAI;YACX,aAAa,OAAO;YACpB,OAAO;QACT;QACA,IAAI,MAAM,KAAM;YACd,aAAa,OAAO;QACtB;QACA,IAAI,MAAM,KAAM;YACd,aAAa,OAAO;QACtB;IACF;AACF", "ignoreList": [0], "debugId": null}}]}