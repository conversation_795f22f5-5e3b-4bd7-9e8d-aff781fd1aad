{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40codemirror/legacy-modes/mode/ttcn.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nconst parserConfig = {\n  name: \"ttcn\",\n  keywords: words(\"activate address alive all alt altstep and and4b any\" +\n                  \" break case component const continue control deactivate\" +\n                  \" display do else encode enumerated except exception\" +\n                  \" execute extends extension external for from function\" +\n                  \" goto group if import in infinity inout interleave\" +\n                  \" label language length log match message mixed mod\" +\n                  \" modifies module modulepar mtc noblock not not4b nowait\" +\n                  \" of on optional or or4b out override param pattern port\" +\n                  \" procedure record recursive rem repeat return runs select\" +\n                  \" self sender set signature system template testcase to\" +\n                  \" type union value valueof var variant while with xor xor4b\"),\n  builtin: words(\"bit2hex bit2int bit2oct bit2str char2int char2oct encvalue\" +\n                 \" decomp decvalue float2int float2str hex2bit hex2int\" +\n                 \" hex2oct hex2str int2bit int2char int2float int2hex\" +\n                 \" int2oct int2str int2unichar isbound ischosen ispresent\" +\n                 \" isvalue lengthof log2str oct2bit oct2char oct2hex oct2int\" +\n                 \" oct2str regexp replace rnd sizeof str2bit str2float\" +\n                 \" str2hex str2int str2oct substr unichar2int unichar2char\" +\n                 \" enum2int\"),\n  types: words(\"anytype bitstring boolean char charstring default float\" +\n               \" hexstring integer objid octetstring universal verdicttype timer\"),\n  timerOps: words(\"read running start stop timeout\"),\n  portOps: words(\"call catch check clear getcall getreply halt raise receive\" +\n                 \" reply send trigger\"),\n  configOps: words(\"create connect disconnect done kill killed map unmap\"),\n  verdictOps: words(\"getverdict setverdict\"),\n  sutOps: words(\"action\"),\n  functionOps: words(\"apply derefers refers\"),\n\n  verdictConsts: words(\"error fail inconc none pass\"),\n  booleanConsts: words(\"true false\"),\n  otherConsts: words(\"null NULL omit\"),\n\n  visibilityModifiers: words(\"private public friend\"),\n  templateMatch: words(\"complement ifpresent subset superset permutation\"),\n  multiLineStrings: true\n}\n\nvar wordList = []\nfunction add(obj) {\n  if (obj) for (var prop in obj) if (obj.hasOwnProperty(prop))\n    wordList.push(prop);\n}\nadd(parserConfig.keywords);\nadd(parserConfig.builtin);\nadd(parserConfig.timerOps);\nadd(parserConfig.portOps);\n\nvar keywords = parserConfig.keywords || {},\n    builtin = parserConfig.builtin || {},\n    timerOps = parserConfig.timerOps || {},\n    portOps  = parserConfig.portOps || {},\n    configOps = parserConfig.configOps || {},\n    verdictOps = parserConfig.verdictOps || {},\n    sutOps = parserConfig.sutOps || {},\n    functionOps = parserConfig.functionOps || {},\n\n    verdictConsts = parserConfig.verdictConsts || {},\n    booleanConsts = parserConfig.booleanConsts || {},\n    otherConsts   = parserConfig.otherConsts || {},\n\n    types = parserConfig.types || {},\n    visibilityModifiers = parserConfig.visibilityModifiers || {},\n    templateMatch = parserConfig.templateMatch || {},\n    multiLineStrings = parserConfig.multiLineStrings,\n    indentStatements = parserConfig.indentStatements !== false;\nvar isOperatorChar = /[+\\-*&@=<>!\\/]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[\\[\\]{}\\(\\),;\\\\:\\?\\.]/.test(ch)) {\n    curPunc = ch;\n    return \"punctuation\";\n  }\n  if (ch == \"#\"){\n    stream.skipToEnd();\n    return \"atom\";\n  }\n  if (ch == \"%\"){\n    stream.eatWhile(/\\b/);\n    return \"atom\";\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    if(ch == \"@\"){\n      if(stream.match(\"try\") || stream.match(\"catch\")\n         || stream.match(\"lazy\")){\n        return \"keyword\";\n      }\n    }\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n  var cur = stream.current();\n\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  if (builtin.propertyIsEnumerable(cur)) return \"builtin\";\n\n  if (timerOps.propertyIsEnumerable(cur)) return \"def\";\n  if (configOps.propertyIsEnumerable(cur)) return \"def\";\n  if (verdictOps.propertyIsEnumerable(cur)) return \"def\";\n  if (portOps.propertyIsEnumerable(cur)) return \"def\";\n  if (sutOps.propertyIsEnumerable(cur)) return \"def\";\n  if (functionOps.propertyIsEnumerable(cur)) return \"def\";\n\n  if (verdictConsts.propertyIsEnumerable(cur)) return \"string\";\n  if (booleanConsts.propertyIsEnumerable(cur)) return \"string\";\n  if (otherConsts.propertyIsEnumerable(cur)) return \"string\";\n\n  if (types.propertyIsEnumerable(cur)) return \"typeName.standard\";\n  if (visibilityModifiers.propertyIsEnumerable(cur))\n    return \"modifier\";\n  if (templateMatch.propertyIsEnumerable(cur)) return \"atom\";\n\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped){\n        var afterQuote = stream.peek();\n        //look if the character after the quote is like the B in '10100010'B\n        if (afterQuote){\n          afterQuote = afterQuote.toLowerCase();\n          if(afterQuote == \"b\" || afterQuote == \"h\" || afterQuote == \"o\")\n            stream.next();\n        }\n        end = true; break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !(escaped || multiLineStrings))\n      state.tokenize = null;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\n\nfunction pushContext(state, col, type) {\n  var indent = state.indented;\n  if (state.context && state.context.type == \"statement\")\n    indent = state.context.indented;\n  return state.context = new Context(indent, col, type, null, state.context);\n}\n\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n//Interface\nexport const ttcn = {\n  name: \"ttcn\",\n  startState: function() {\n    return {\n      tokenize: null,\n      context: new Context(0, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\" || curPunc == \",\")\n        && ctx.type == \"statement\"){\n      popContext(state);\n    }\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (indentStatements &&\n             (((ctx.type == \"}\" || ctx.type == \"top\") && curPunc != ';') ||\n              (ctx.type == \"statement\" && curPunc == \"newstatement\")))\n      pushContext(state, stream.column(), \"statement\");\n\n    state.startOfLine = false;\n\n    return style;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    autocomplete: wordList\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,MAAM,GAAG;IAChB,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;IACvD,OAAO;AACT;AAEA,MAAM,eAAe;IACnB,MAAM;IACN,UAAU,MAAM,yDACA,4DACA,wDACA,0DACA,uDACA,uDACA,4DACA,4DACA,8DACA,2DACA;IAChB,SAAS,MAAM,+DACA,yDACA,wDACA,4DACA,+DACA,yDACA,6DACA;IACf,OAAO,MAAM,4DACA;IACb,UAAU,MAAM;IAChB,SAAS,MAAM,+DACA;IACf,WAAW,MAAM;IACjB,YAAY,MAAM;IAClB,QAAQ,MAAM;IACd,aAAa,MAAM;IAEnB,eAAe,MAAM;IACrB,eAAe,MAAM;IACrB,aAAa,MAAM;IAEnB,qBAAqB,MAAM;IAC3B,eAAe,MAAM;IACrB,kBAAkB;AACpB;AAEA,IAAI,WAAW,EAAE;AACjB,SAAS,IAAI,GAAG;IACd,IAAI,KAAK;QAAA,IAAK,IAAI,QAAQ,IAAK,IAAI,IAAI,cAAc,CAAC,OACpD,SAAS,IAAI,CAAC;IAAK;AACvB;AACA,IAAI,aAAa,QAAQ;AACzB,IAAI,aAAa,OAAO;AACxB,IAAI,aAAa,QAAQ;AACzB,IAAI,aAAa,OAAO;AAExB,IAAI,WAAW,aAAa,QAAQ,IAAI,CAAC,GACrC,UAAU,aAAa,OAAO,IAAI,CAAC,GACnC,WAAW,aAAa,QAAQ,IAAI,CAAC,GACrC,UAAW,aAAa,OAAO,IAAI,CAAC,GACpC,YAAY,aAAa,SAAS,IAAI,CAAC,GACvC,aAAa,aAAa,UAAU,IAAI,CAAC,GACzC,SAAS,aAAa,MAAM,IAAI,CAAC,GACjC,cAAc,aAAa,WAAW,IAAI,CAAC,GAE3C,gBAAgB,aAAa,aAAa,IAAI,CAAC,GAC/C,gBAAgB,aAAa,aAAa,IAAI,CAAC,GAC/C,cAAgB,aAAa,WAAW,IAAI,CAAC,GAE7C,QAAQ,aAAa,KAAK,IAAI,CAAC,GAC/B,sBAAsB,aAAa,mBAAmB,IAAI,CAAC,GAC3D,gBAAgB,aAAa,aAAa,IAAI,CAAC,GAC/C,mBAAmB,aAAa,gBAAgB,EAChD,mBAAmB,aAAa,gBAAgB,KAAK;AACzD,IAAI,iBAAiB;AACrB,IAAI;AAEJ,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,KAAK,OAAO,IAAI;IAEpB,IAAI,MAAM,OAAO,MAAM,KAAK;QAC1B,MAAM,QAAQ,GAAG,YAAY;QAC7B,OAAO,MAAM,QAAQ,CAAC,QAAQ;IAChC;IACA,IAAI,wBAAwB,IAAI,CAAC,KAAK;QACpC,UAAU;QACV,OAAO;IACT;IACA,IAAI,MAAM,KAAI;QACZ,OAAO,SAAS;QAChB,OAAO;IACT;IACA,IAAI,MAAM,KAAI;QACZ,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,IAAI,KAAK,IAAI,CAAC,KAAK;QACjB,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,IAAI,MAAM,KAAK;QACb,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,MAAM,QAAQ,GAAG;YACjB,OAAO,aAAa,QAAQ;QAC9B;QACA,IAAI,OAAO,GAAG,CAAC,MAAM;YACnB,OAAO,SAAS;YAChB,OAAO;QACT;IACF;IACA,IAAI,eAAe,IAAI,CAAC,KAAK;QAC3B,IAAG,MAAM,KAAI;YACX,IAAG,OAAO,KAAK,CAAC,UAAU,OAAO,KAAK,CAAC,YACjC,OAAO,KAAK,CAAC,SAAQ;gBACzB,OAAO;YACT;QACF;QACA,OAAO,QAAQ,CAAC;QAChB,OAAO;IACT;IACA,OAAO,QAAQ,CAAC;IAChB,IAAI,MAAM,OAAO,OAAO;IAExB,IAAI,SAAS,oBAAoB,CAAC,MAAM,OAAO;IAC/C,IAAI,QAAQ,oBAAoB,CAAC,MAAM,OAAO;IAE9C,IAAI,SAAS,oBAAoB,CAAC,MAAM,OAAO;IAC/C,IAAI,UAAU,oBAAoB,CAAC,MAAM,OAAO;IAChD,IAAI,WAAW,oBAAoB,CAAC,MAAM,OAAO;IACjD,IAAI,QAAQ,oBAAoB,CAAC,MAAM,OAAO;IAC9C,IAAI,OAAO,oBAAoB,CAAC,MAAM,OAAO;IAC7C,IAAI,YAAY,oBAAoB,CAAC,MAAM,OAAO;IAElD,IAAI,cAAc,oBAAoB,CAAC,MAAM,OAAO;IACpD,IAAI,cAAc,oBAAoB,CAAC,MAAM,OAAO;IACpD,IAAI,YAAY,oBAAoB,CAAC,MAAM,OAAO;IAElD,IAAI,MAAM,oBAAoB,CAAC,MAAM,OAAO;IAC5C,IAAI,oBAAoB,oBAAoB,CAAC,MAC3C,OAAO;IACT,IAAI,cAAc,oBAAoB,CAAC,MAAM,OAAO;IAEpD,OAAO;AACT;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,UAAU,OAAO,MAAM,MAAM;QACjC,MAAO,CAAC,OAAO,OAAO,IAAI,EAAE,KAAK,KAAM;YACrC,IAAI,QAAQ,SAAS,CAAC,SAAQ;gBAC5B,IAAI,aAAa,OAAO,IAAI;gBAC5B,oEAAoE;gBACpE,IAAI,YAAW;oBACb,aAAa,WAAW,WAAW;oBACnC,IAAG,cAAc,OAAO,cAAc,OAAO,cAAc,KACzD,OAAO,IAAI;gBACf;gBACA,MAAM;gBAAM;YACd;YACA,UAAU,CAAC,WAAW,QAAQ;QAChC;QACA,IAAI,OAAO,CAAC,CAAC,WAAW,gBAAgB,GACtC,MAAM,QAAQ,GAAG;QACnB,OAAO;IACT;AACF;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,WAAW,OAAO;IACtB,MAAO,KAAK,OAAO,IAAI,GAAI;QACzB,IAAI,MAAM,OAAO,UAAU;YACzB,MAAM,QAAQ,GAAG;YACjB;QACF;QACA,WAAY,MAAM;IACpB;IACA,OAAO;AACT;AAEA,SAAS,QAAQ,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;IAClD,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,SAAS,YAAY,KAAK,EAAE,GAAG,EAAE,IAAI;IACnC,IAAI,SAAS,MAAM,QAAQ;IAC3B,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,aACzC,SAAS,MAAM,OAAO,CAAC,QAAQ;IACjC,OAAO,MAAM,OAAO,GAAG,IAAI,QAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,OAAO;AAC3E;AAEA,SAAS,WAAW,KAAK;IACvB,IAAI,IAAI,MAAM,OAAO,CAAC,IAAI;IAC1B,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAC/B,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ;IACzC,OAAO,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI;AAC3C;AAGO,MAAM,OAAO;IAClB,MAAM;IACN,YAAY;QACV,OAAO;YACL,UAAU;YACV,SAAS,IAAI,QAAQ,GAAG,GAAG,OAAO;YAClC,UAAU;YACV,aAAa;QACf;IACF;IAEA,OAAO,SAAS,MAAM,EAAE,KAAK;QAC3B,IAAI,MAAM,MAAM,OAAO;QACvB,IAAI,OAAO,GAAG,IAAI;YAChB,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;YACnC,MAAM,QAAQ,GAAG,OAAO,WAAW;YACnC,MAAM,WAAW,GAAG;QACtB;QACA,IAAI,OAAO,QAAQ,IAAI,OAAO;QAC9B,UAAU;QACV,IAAI,QAAQ,CAAC,MAAM,QAAQ,IAAI,SAAS,EAAE,QAAQ;QAClD,IAAI,SAAS,WAAW,OAAO;QAC/B,IAAI,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG;QAEnC,IAAI,CAAC,WAAW,OAAO,WAAW,OAAO,WAAW,GAAG,KAChD,IAAI,IAAI,IAAI,aAAY;YAC7B,WAAW;QACb,OACK,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK,YAAY,OAAO,OAAO,MAAM,IAAI;aACxD,IAAI,WAAW,KAAK;YACvB,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;YACjD,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM,WAAW;YACtC,MAAO,IAAI,IAAI,IAAI,YAAa,MAAM,WAAW;QACnD,OACK,IAAI,WAAW,IAAI,IAAI,EAAE,WAAW;aACpC,IAAI,oBACA,CAAC,AAAC,CAAC,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,KAAK,WAAW,OACrD,IAAI,IAAI,IAAI,eAAe,WAAW,cAAe,GAC9D,YAAY,OAAO,OAAO,MAAM,IAAI;QAEtC,MAAM,WAAW,GAAG;QAEpB,OAAO;IACT;IAEA,cAAc;QACZ,eAAe;QACf,eAAe;YAAC,MAAM;YAAM,OAAO;gBAAC,MAAM;gBAAM,OAAO;YAAI;QAAC;QAC5D,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}]}